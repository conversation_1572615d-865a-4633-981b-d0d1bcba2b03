<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ include file="/common/taglibs.jsp"%>
<%@ include file="/common/app_common.jsp" %>
<!DOCTYPE html>
<html>
<head>
	<title>成绩管理</title>
	<meta charset="UTF-8">
	<meta content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" name="viewport" id="viewport"/>
	<meta name = "format-detection" content = "telephone=no">
	<meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
	<meta http-equiv="keywords" content="好医生">
	<link rel="stylesheet" href="${ctx}/css/mobile_new/css/reset.css"/>
	<link rel="stylesheet" href="${ctx}/css/mobile_new/css/common.css"/>
	<link rel="stylesheet" href="${ctx}/css/mobile_new/css/main.css" />
	<link rel="stylesheet" href="${ctx}/css/mobile_new/css/alertify.core.css" />
	<link rel="stylesheet" href="${ctx}/css/mobile_new/css/alertify.default.css" />
	<link rel="stylesheet" href="${ctx}/css/mobile_new/css/new_zyy.css" />
	<script src="${ctx}/css/mobile_new/js/init.js"></script>
	<script src="${ctx}/js/jquery-1.10.2.min.js"></script>
	<script src="${ctx}/css/mobile_new/js/app.js"></script>
	<script src="${ctx}/css/mobile_new/js/alertify.min.js"></script>
	<script src="${ctx}/css/mobile_new/js/mobiscroll_002.js" type="text/javascript"></script>
    <script src="${ctx}/css/mobile_new/js/mobiscroll.js" type="text/javascript"></script>
	<style>
		/* 年月选择器样式 */
		.date-selector {
			display: flex;
			justify-content: center;
			align-items: center;
			padding: 0.5rem 0.8rem;
			background: #fff;
			border-bottom: 1px solid #f0f0f0;
		}
		
		.date-item {
			display: flex;
			align-items: center;
			margin: 0 1rem;
			font-size: 0.7rem;
			color: #333;
		}
		
		.date-value {
			margin: 0 0.3rem;
			font-weight: bold;
			color: #10a86c;
		}
		
		.date-arrow {
			width: 0;
			height: 0;
			border-left: 0.25rem solid transparent;
			border-right: 0.25rem solid transparent;
			border-top: 0.3rem solid #999;
			cursor: pointer;
		}
		
		/* 考试卡片样式 */
		.exam-card {
			background: #fff;
			margin: 0.5rem 0.6rem;
			border-radius: 0.4rem;
			box-shadow: 0 2px 8px rgba(0,0,0,0.1);
			overflow: hidden;
		}
		
		.exam-header {
			padding: 0.8rem;
			border-bottom: 1px solid #f5f5f5;
		}
		
		.exam-title {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 0.5rem;
		}
		
		.exam-name {
			font-size: 0.75rem;
			font-weight: bold;
			color: #333;
		}
		
		.exam-type {
			background: #f0f8ff;
			color: #4a90e2;
			padding: 0.15rem 0.4rem;
			border-radius: 0.8rem;
			font-size: 0.55rem;
		}
		
		.exam-subject {
			font-size: 0.65rem;
			color: #666;
			margin-bottom: 0.3rem;
		}
		
		.exam-time {
			font-size: 0.6rem;
			color: #999;
		}
		
		.exam-duration {
			float: right;
			color: #666;
		}
		
		.exam-actions {
			display: flex;
			border-top: 1px solid #f5f5f5;
		}
		
		.action-btn {
			flex: 1;
			padding: 0.6rem;
			text-align: center;
			font-size: 0.65rem;
			color: #4a90e2;
			text-decoration: none;
			border-right: 1px solid #f5f5f5;
		}
		
		.action-btn:last-child {
			border-right: none;
		}
		
		.action-btn.published {
			color: #999;
		}
		
		/* 发布成功提示 */
		.success-toast {
			position: fixed;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
			background: rgba(0,0,0,0.8);
			color: #fff;
			padding: 0.8rem 1.2rem;
			border-radius: 0.3rem;
			font-size: 0.65rem;
			z-index: 9999;
			display: none;
		}
		
		/* 空状态样式 */
		.empty-state {
			text-align: center;
			padding: 2rem 0;
			color: #999;
		}
		
		.empty-state img {
			width: 4rem;
			height: 4rem;
			margin-bottom: 0.5rem;
		}
	</style>
</head>
<body class="pdb250">
<!-- 头部start -->
<header class="qbd_header clearfix">
	<div class="fl qbd_return"><a href="javascript:history.go(-1);"></a></div> 
    <h1 class="fs32">成绩管理</h1>
    <div class="fr qbd_more"><a href="javascript:void(0);"></a></div>
</header>
<!-- 头部end -->

<!-- 年月选择器 -->
<div class="date-selector">
	<div class="date-item">
		<span>年度</span>
		<span class="date-arrow" onclick="showYearPicker()"></span>
		<span class="date-value" id="currentYear">2025</span>
	</div>
	<div class="date-item">
		<span>月</span>
		<span class="date-arrow" onclick="showMonthPicker()"></span>
		<span class="date-value" id="currentMonth">6</span>
	</div>
</div>

<!-- 考试列表 -->
<div class="exam-list" id="examList">
	<c:choose>
		<c:when test="${not empty records}">
			<c:forEach items="${records}" var="item" varStatus="vs">
				<div class="exam-card">
					<div class="exam-header">
						<div class="exam-title">
							<span class="exam-name">${item.name}</span>
							<span class="exam-type">出科考试</span>
						</div>
						<div class="exam-subject">考试科目：${item.courseName}</div>
						<div class="exam-time">
							${item.startDate}&nbsp;${item.courseStartTime}&nbsp;-&nbsp;${item.endDate}&nbsp;${item.courseEndTime}
							<span class="exam-duration">${item.examDuration}分钟</span>
						</div>
					</div>
					<div class="exam-actions">
						<a href="javascript:void(0);" class="action-btn" onclick="manageScore(1)">管理成绩</a>
						<c:choose>
							<c:when test="${item.scorePublishStatus eq 1}">
								<a href="javascript:void(0);" class="action-btn published">成绩已发布</a>
							</c:when>
							<c:otherwise>
								<a href="javascript:void(0);" class="action-btn" onclick="publishScore(1)">发布成绩</a>
							</c:otherwise>
						</c:choose>
					</div>
				</div>
			</c:forEach>
		</c:when>
		<c:otherwise>
			<div class="empty-state">
				<img src="${ctx}/css/mobile_new/img/daishenhe.png" alt="">
				<p>暂无出科考试数据</p>
			</div>
		</c:otherwise>
	</c:choose>



	<!-- 11月份出科考核 -->
	<%--<div class="exam-card">
		<div class="exam-header">
			<div class="exam-title">
				<span class="exam-name">11月份出科考核</span>
				<span class="exam-type">出科考试</span>
			</div>
			<div class="exam-subject">考试科目：测试</div>
			<div class="exam-time">
				2022/11/19 20:21-2022/11/20 06:33
				<span class="exam-duration">180分钟</span>
			</div>
		</div>
		<div class="exam-actions">
			<a href="javascript:void(0);" class="action-btn" onclick="manageScore(1)">管理成绩</a>
			<a href="javascript:void(0);" class="action-btn" onclick="publishScore(1)">发布成绩</a>
		</div>
	</div>--%>
	
	<!-- 1月份出科考核 -->
	<%--<div class="exam-card">
		<div class="exam-header">
			<div class="exam-title">
				<span class="exam-name">1月份出科考核</span>
				<span class="exam-type">出科考试</span>
			</div>
			<div class="exam-subject">考试科目：测试</div>
			<div class="exam-time">
				2022/12/19 20:21-2022/12/20 06:33
				<span class="exam-duration">180分钟</span>
			</div>
		</div>
		<div class="exam-actions">
			<a href="javascript:void(0);" class="action-btn" onclick="manageScore(1)">管理成绩</a>
			<a href="javascript:void(0);" class="action-btn published">成绩已发布</a>
		</div>
	</div>--%>
</div>

<!-- 发布成功提示 -->
<div class="success-toast" id="successToast">发布成功</div>

<!-- 小尾巴start -->
<div class="foot">
  <ul class="foot_nav">
    <li class="fr1"><a href="${ctx}/mainapp/index" >首页</a></li>
 	<li class="fr4"><a href="#" class="active">带教管理</a></li>
    <li class="fr5"><a href="${ctx}/mainapp/toEditActivityView" >教学活动</a></li>
    <li class="fr3"><a href="${ctx}/mainapp/me" >我的</a></li>
  </ul>
</div>  

<script>
$(document).ready(function() {
	// 初始化页面
	initPage();
});

// 初始化页面
function initPage() {
	var currentDate = new Date();
	var year = currentDate.getFullYear();
	var month = currentDate.getMonth() + 1;
	
	$("#currentYear").text(year);
	$("#currentMonth").text(month);
	
	// 加载考试数据
	loadExamData(year, month);
}

// 显示年份选择器
function showYearPicker() {
	var years = [];
	var currentYear = new Date().getFullYear();
	for(var i = currentYear - 5; i <= currentYear + 5; i++) {
		years.push(i);
	}
	
	// 这里可以集成mobiscroll或其他选择器
	var selectedYear = prompt("请选择年份", $("#currentYear").text());
	if(selectedYear && !isNaN(selectedYear)) {
		$("#currentYear").text(selectedYear);
		loadExamData(selectedYear, $("#currentMonth").text());
	}
}

// 显示月份选择器
function showMonthPicker() {
	var months = [];
	for(var i = 1; i <= 12; i++) {
		months.push(i);
	}
	
	// 这里可以集成mobiscroll或其他选择器
	var selectedMonth = prompt("请选择月份", $("#currentMonth").text());
	if(selectedMonth && !isNaN(selectedMonth) && selectedMonth >= 1 && selectedMonth <= 12) {
		$("#currentMonth").text(selectedMonth);
		loadExamData($("#currentYear").text(), selectedMonth);
	}
}

// 加载考试数据
function loadExamData(year, month) {
	// 这里应该是AJAX请求获取数据
	// $.ajax({
	//     url: '${ctx}/deptApp/exam/getExamList',
	//     type: 'POST',
	//     data: {
	//         year: year,
	//         month: month
	//     },
	//     success: function(data) {
	//         renderExamList(data);
	//     }
	// });
	
	// 模拟数据加载
	console.log("加载" + year + "年" + month + "月的考试数据");
}

// 管理成绩
function manageScore(examId) {
	// 跳转到成绩管理页面
	window.location.href = "${ctx}/deptApp/exam/manageScore?examId=" + examId;
}

// 发布成绩
function publishScore(examId) {
	if(confirm("确定要发布成绩吗？发布后学员将能看到考试成绩。")) {
		// 发送发布请求
		$.ajax({
			url: '${ctx}/deptApp/exam/publishScore',
			type: 'POST',
			data: {
				examId: examId
			},
			success: function(data) {
				if(data.success) {
					showSuccessToast();
					// 更新按钮状态
					updatePublishButton(examId);
				} else {
					alert(data.message || "发布失败");
				}
			},
			error: function() {
				alert("网络错误，请重试");
			}
		});
	}
}

// 显示成功提示
function showSuccessToast() {
	$("#successToast").fadeIn(300);
	setTimeout(function() {
		$("#successToast").fadeOut(300);
	}, 2000);
}

// 更新发布按钮状态
function updatePublishButton(examId) {
	// 找到对应的按钮并更新状态
	$(".exam-card").each(function() {
		var card = $(this);
		// 这里需要根据实际的examId来匹配
		var publishBtn = card.find(".action-btn").last();
		publishBtn.text("成绩已发布").addClass("published");
	});
}

// 渲染考试列表
function renderExamList(examData) {
	var html = "";
	if(examData && examData.length > 0) {
		$.each(examData, function(index, exam) {
			html += '<div class="exam-card">';
			html += '  <div class="exam-header">';
			html += '    <div class="exam-title">';
			html += '      <span class="exam-name">' + exam.examName + '</span>';
			html += '      <span class="exam-type">' + exam.examType + '</span>';
			html += '    </div>';
			html += '    <div class="exam-subject">考试科目：' + exam.subject + '</div>';
			html += '    <div class="exam-time">';
			html += '      ' + exam.startTime + '-' + exam.endTime;
			html += '      <span class="exam-duration">' + exam.duration + '分钟</span>';
			html += '    </div>';
			html += '  </div>';
			html += '  <div class="exam-actions">';
			html += '    <a href="javascript:void(0);" class="action-btn" onclick="manageScore(' + exam.id + ')">管理成绩</a>';
			if(exam.published) {
				html += '    <a href="javascript:void(0);" class="action-btn published">成绩已发布</a>';
			} else {
				html += '    <a href="javascript:void(0);" class="action-btn" onclick="publishScore(' + exam.id + ')">发布成绩</a>';
			}
			html += '  </div>';
			html += '</div>';
		});
	} else {
		html = '<div class="empty-state">';
		html += '  <img src="${ctx}/css/mobile_new/img/daishenhe.png" alt="">';
		html += '  <p>暂无考试数据</p>';
		html += '</div>';
	}
	
	$("#examList").html(html);
}
</script>

</body>
</html>
