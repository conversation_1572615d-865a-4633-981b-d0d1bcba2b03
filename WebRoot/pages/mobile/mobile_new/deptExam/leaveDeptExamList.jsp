<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ include file="/common/taglibs.jsp"%>
<%@ include file="/common/app_common.jsp" %>
<!DOCTYPE html>
<html>
<head>
	<title>成绩管理</title>
	<meta charset="UTF-8">
	<meta content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" name="viewport" id="viewport"/>
	<meta name = "format-detection" content = "telephone=no">
	<meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
	<meta http-equiv="keywords" content="好医生">
	<link rel="stylesheet" href="${ctx}/css/mobile_new/css/reset.css"/>
	<link rel="stylesheet" href="${ctx}/css/mobile_new/css/common.css"/>
	<link rel="stylesheet" href="${ctx}/css/mobile_new/css/main.css" />
	<link rel="stylesheet" href="${ctx}/css/mobile_new/css/alertify.core.css" />
	<link rel="stylesheet" href="${ctx}/css/mobile_new/css/alertify.default.css" />
	<link rel="stylesheet" href="${ctx}/css/mobile_new/css/new_zyy.css" />
	<script src="${ctx}/css/mobile_new/js/init.js"></script>
	<script src="${ctx}/js/jquery-1.10.2.min.js"></script>
	<script src="${ctx}/css/mobile_new/js/app.js"></script>
	<script src="${ctx}/css/mobile_new/js/alertify.min.js"></script>
	<script src="${ctx}/css/mobile_new/js/mobiscroll_002.js" type="text/javascript"></script>
    <script src="${ctx}/css/mobile_new/js/mobiscroll.js" type="text/javascript"></script>
	<style>
		/* 年月选择器样式 */
		.date-selector {
			position: fixed;
			top: 0;
			left: 0;
			right: 0;
			z-index: 1000;
			display: flex;
			justify-content: center;
			align-items: center;
			padding: 0.5rem 0.8rem;
			background: #fff;
			border-bottom: 1px solid #f0f0f0;
			box-shadow: 0 2px 4px rgba(0,0,0,0.1);
		}

		.date-item {
			display: flex;
			align-items: center;
			margin: 0 1rem;
			font-size: 0.7rem;
			color: #333;
		}

		.date-value {
			margin: 0 0.3rem;
			font-weight: bold;
			color: #10a86c;
		}

		.date-arrow {
			width: 0;
			height: 0;
			border-left: 0.25rem solid transparent;
			border-right: 0.25rem solid transparent;
			border-top: 0.3rem solid #999;
			cursor: pointer;
		}
		
		/* 考试卡片样式 */
		.exam-card {
			background: #fff;
			margin: 0.5rem 0.6rem;
			border-radius: 0.4rem;
			box-shadow: 0 2px 8px rgba(0,0,0,0.1);
			overflow: hidden;
		}
		
		.exam-header {
			padding: 0.8rem;
			border-bottom: 1px solid #f5f5f5;
		}
		
		.exam-title {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 0.5rem;
		}
		
		.exam-name {
			font-size: 0.75rem;
			font-weight: bold;
			color: #333;
		}
		
		.exam-type {
			background: #f0f8ff;
			color: #4a90e2;
			padding: 0.15rem 0.4rem;
			border-radius: 0.8rem;
			font-size: 0.55rem;
		}
		
		.exam-subject {
			font-size: 0.65rem;
			color: #666;
			margin-bottom: 0.3rem;
		}
		
		.exam-time {
			font-size: 0.6rem;
			color: #999;
		}
		
		.exam-duration {
			float: right;
			color: #666;
		}
		
		.exam-actions {
			display: flex;
			border-top: 1px solid #f5f5f5;
		}
		
		.action-btn {
			flex: 1;
			padding: 0.6rem;
			text-align: center;
			font-size: 0.65rem;
			color: #4a90e2;
			text-decoration: none;
			border-right: 1px solid #f5f5f5;
		}
		
		.action-btn:last-child {
			border-right: none;
		}
		
		.action-btn.published {
			color: #999;
		}
		
		/* 发布成功提示 */
		.success-toast {
			position: fixed;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
			background: rgba(0,0,0,0.8);
			color: #fff;
			padding: 0.8rem 1.2rem;
			border-radius: 0.3rem;
			font-size: 0.65rem;
			z-index: 9999;
			display: none;
		}
		
		/* 空状态样式 */
		.empty-state {
			text-align: center;
			padding: 2rem 0;
			color: #999;
		}
		
		.empty-state img {
			width: 4rem;
			height: 4rem;
			margin-bottom: 0.5rem;
		}

		/* 为固定选择器添加页面内容间距 */
		.page-content {
			margin-top: 3.5rem; /* 为固定的选择器留出空间 */
		}

		.yearMonthSelect{
			appearance: none;
			-webkit-appearance: none;
			-moz-appearance: none;
			background: transparent;
			border: none;
			padding: 0;
			margin: 0;
			font-family: inherit;
			font-size: inherit;
			color: #10a86c;
			cursor: pointer;
			text-align: center;
		}
	</style>
</head>
<body class="pdb250">
<!-- 头部start -->
<header class="qbd_header clearfix">
	<div class="fl qbd_return"><a href="javascript:history.go(-1);"></a></div>
    <h1 class="fs32">成绩管理</h1>
    <div class="fr qbd_more"><a href="javascript:void(0);"></a></div>
</header>
<!-- 头部end -->

<!-- 年月选择器 -->
<div class="date-selector">
	<div class="date-item">
		<form id="searchForm" action="${ctx}/appDeptExam/leaveDeptExamList">
			<input readonly id="examMonth" name="examMonth" value="${query.examMonth}" class="yearMonthSelect" style="display:inline-block;width:7.6rem;" type="text" placeholder="请选择考试月份" />
		</form>
	</div>
</div>

<!-- 考试列表 -->
<div class="exam-list page-content" id="examList">
	<c:choose>
		<c:when test="${not empty records}">
			<c:forEach items="${records}" var="item" varStatus="vs">
				<div class="exam-card">
					<div class="exam-header">
						<div class="exam-title">
							<span class="exam-name">${item.name}</span>
							<span class="exam-type">出科考试</span>
						</div>
						<div class="exam-subject">考试科目：${item.courseName}</div>
						<div class="exam-time">
							${item.startDate}&nbsp;${item.courseStartTime}&nbsp;-&nbsp;${item.endDate}&nbsp;${item.courseEndTime}
							<span class="exam-duration">${item.examDuration}分钟</span>
						</div>
					</div>
					<div class="exam-actions">
						<a href="javascript:void(0);" class="action-btn" onclick="manageScore(1)">管理成绩</a>
						<c:choose>
							<c:when test="${item.scorePublishStatus eq 1}">
								<a href="javascript:void(0);" class="action-btn published">成绩已发布</a>
							</c:when>
							<c:otherwise>
								<a href="javascript:void(0);" class="action-btn" onclick="publishScore(1)">发布成绩</a>
							</c:otherwise>
						</c:choose>
					</div>
				</div>
			</c:forEach>
		</c:when>
		<c:otherwise>
			<div class="empty-state">
				<img src="${ctx}/css/mobile_new/img/daishenhe.png" alt="">
				<p>暂无出科考试数据</p>
			</div>
		</c:otherwise>
	</c:choose>
</div>

<!-- 发布成功提示 -->
<div class="success-toast" id="successToast">发布成功</div>
</body>
<script>
	$(document).ready(function() {
		/*
		 * 日历
		 */
		var currYear = (new Date()).getFullYear();
		var opt={};
		opt.date = {preset : 'date'};
		opt.datetime = {preset : 'datetime'};
		opt.time = {preset : 'time'};
		opt.default = {
			theme: 'android-ics light', //皮肤样式
			display: 'top', //显示方式
			mode: 'scroller', //日期选择模式
			rtl:true,
			dateFormat: 'yyyy-mm',
			dateOrder: 'yyyymm', // 只显示年月
			lang: 'zh',
			showNow: true,
			nowText: '当月',
			startYear: currYear - 10, //开始年份
			endYear: currYear + 10 ,//结束年份
			onSelect:function(valueText, inst){
				$('#searchForm').submit();
			},
			onBeforeShow: function (event, inst) {
				var validity = $('#examMonth').val().split('-');
				if(validity != null && validity != '')
					$('#examMonth').mobiscroll('setDate', new Date(validity[0], validity[1]-1));
			}
		};
		var optDate = $.extend(opt['date'], opt['default']);
		$('#examMonth').mobiscroll(optDate).date(optDate);
	});

	// 初始化页面
	function initPage() {
		var currentDate = new Date();
		var year = currentDate.getFullYear();
		var month = currentDate.getMonth() + 1;

		$("#currentYear").text(year);
		$("#currentMonth").text(month);
	}

	// 显示年份选择器
	function showYearPicker() {
		var years = [];
		var currentYear = new Date().getFullYear();
		for(var i = currentYear - 5; i <= currentYear + 5; i++) {
			years.push(i);
		}

		// 这里可以集成mobiscroll或其他选择器
		var selectedYear = prompt("请选择年份", $("#currentYear").text());
		if(selectedYear && !isNaN(selectedYear)) {
			$("#currentYear").text(selectedYear);
		}
	}

	// 显示月份选择器
	function showMonthPicker() {
		var months = [];
		for(var i = 1; i <= 12; i++) {
			months.push(i);
		}

		// 这里可以集成mobiscroll或其他选择器
		var selectedMonth = prompt("请选择月份", $("#currentMonth").text());
		if(selectedMonth && !isNaN(selectedMonth) && selectedMonth >= 1 && selectedMonth <= 12) {
			$("#currentMonth").text(selectedMonth);
		}
	}

	// 管理成绩
	function manageScore(examId) {
		// 跳转到成绩管理页面
		window.location.href = "${ctx}/deptApp/exam/manageScore?examId=" + examId;
	}

	// 发布成绩
	function publishScore(examId) {
		if(confirm("确定要发布成绩吗？发布后学员将能看到考试成绩。")) {
			// 发送发布请求
			$.ajax({
				url: '${ctx}/deptApp/exam/publishScore',
				type: 'POST',
				data: {
					examId: examId
				},
				success: function(data) {
					if(data.success) {
						showSuccessToast();
						// 更新按钮状态
						updatePublishButton(examId);
					} else {
						alert(data.message || "发布失败");
					}
				},
				error: function() {
					alert("网络错误，请重试");
				}
			});
		}
	}

	// 显示成功提示
	function showSuccessToast() {
		$("#successToast").fadeIn(300);
		setTimeout(function() {
			$("#successToast").fadeOut(300);
		}, 2000);
	}

	// 更新发布按钮状态
	function updatePublishButton(examId) {
		// 找到对应的按钮并更新状态
		$(".exam-card").each(function() {
			var card = $(this);
			// 这里需要根据实际的examId来匹配
			var publishBtn = card.find(".action-btn").last();
			publishBtn.text("成绩已发布").addClass("published");
		});
	}
</script>
</html>