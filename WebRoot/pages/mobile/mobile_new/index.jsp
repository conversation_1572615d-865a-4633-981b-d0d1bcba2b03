<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ include file="/common/taglibs.jsp"%>
<!DOCTYPE html>
<html lang="en">
<head>
	<title>住院医</title>
	<meta charset="UTF-8">
	<meta content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" name="viewport" id="viewport"/>
	<meta name = "format-detection" content = "telephone=no">
	<meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
	<meta http-equiv="keywords" content="好医生">
	<link rel="stylesheet" href="${ctx}/css/mobile_new/css/reset.css"/>
	<link rel="stylesheet" href="${ctx}/css/mobile_new/css/common.css"/>
	<link rel="stylesheet" href="${ctx}/css/mobile_new/css/main.css" />
	<link rel="stylesheet" href="${ctx}/css/mobile_new/css/alertify.core.css" />
	<link rel="stylesheet" href="${ctx}/css/mobile_new/css/alertify.default.css" />
	<!-- https://codepen.io/vkjgr/pen/VYMeXp css select source -->
	<link rel="stylesheet" href="${ctx}/css/mobile_new/css/select_option.css"  />
	<script src="${ctx}/css/mobile_new/js/init.js"></script>
	<script src="${ctx}/js/jquery-1.10.2.min.js"></script>
	<script src="${ctx}/css/mobile_new/js/alertify.min.js"></script>
	<script type="text/javascript" src="${ctx}/css/mobile_new/js/resetFontSize.js"></script>
	<script type="text/javascript" src="${ctx}/js/jquery-easyui/smartUtil.js" charset="utf-8"></script>
</head>
<body>
	<!-- 头部start -->
	
	<!-- <header class="qbd_header clearfix">
		<h1 class="fs32">登录</h1>
	</header> -->
	
	<!-- 头部end -->
    <div class="logo">
    	<div class="logo_img" style="width: 100%"><img src="${ctx}/css/mobile_new/img/logonew.png" alt=""/></div>
    </div> 
    <form id="form" name="form" method="post" data-ajax='false'>
    	<input type="hidden" name="type" value="1"/> 
		<div class="qbd_register_bigcon qbd_bind_bigcon">
			<!--绑定start-->
			<div class="qbd_register_item_con">
				<ul class="qbd_register_item_ul"> 
					<li> 
						<div class="leave_item_after" style="position: relative;"  >
						<img src="${ctx}/css/mobile_new/img/location_map.png" style="width:.8rem;height:.8rem;position:absolute; left:0;top:50%; margin-top:-.4rem" >
						<select id="provinceType" name="provinceType" class="minimal" onchange="changeProvinceType();" >
							<option value="" >请选择你的规培省份</option>
							<option value="29" <c:if test="${provinceType == '29' }">selected="selected"</c:if> >北京市</option>
							<option value="2" <c:if test="${provinceType == '2' }">selected="selected"</c:if> >重庆市</option>
							<option value="3" <c:if test="${provinceType == '3' }">selected="selected"</c:if> >福建省</option>
							<option value="4" <c:if test="${provinceType == '4' }">selected="selected"</c:if> >广东省</option>
							<option value="5" <c:if test="${provinceType == '5' }">selected="selected"</c:if> >广西壮族自治区</option>
							<option value="6" <c:if test="${provinceType == '6' }">selected="selected"</c:if> >贵州省</option>
							<option value="7" <c:if test="${provinceType == '7' }">selected="selected"</c:if> >甘肃省</option>
							<option value="8" <c:if test="${provinceType == '8' }">selected="selected"</c:if> >河北省</option>
							<option value="9" <c:if test="${provinceType == '9' }">selected="selected"</c:if> >河南省</option>
							<option value="10" <c:if test="${provinceType == '10' }">selected="selected"</c:if> >湖北省</option>
							<option value="11" <c:if test="${provinceType == '11' }">selected="selected"</c:if> >湖南省</option>
							<option value="12" <c:if test="${provinceType == '12' }">selected="selected"</c:if> >海南省</option>
							<option value="13" <c:if test="${provinceType == '13' }">selected="selected"</c:if> >黑龙江省</option>
							<option value="14" <c:if test="${provinceType == '14' }">selected="selected"</c:if> >吉林省</option>
							<option value="15" <c:if test="${provinceType == '15' }">selected="selected"</c:if> >江苏省</option>
							<option value="16" <c:if test="${provinceType == '16' }">selected="selected"</c:if> >江西省</option>
							<option value="17" <c:if test="${provinceType == '17' }">selected="selected"</c:if> >辽宁省</option>
							<option value="18" <c:if test="${provinceType == '18' }">selected="selected"</c:if> >内蒙古自治区</option>
							<option value="19" <c:if test="${provinceType == '19' }">selected="selected"</c:if> >宁夏回族自治区</option>
							<option value="20" <c:if test="${provinceType == '20' }">selected="selected"</c:if> >青海省</option>
							<option value="21" <c:if test="${provinceType == '21' }">selected="selected"</c:if> >上海市</option>
							<option value="22" <c:if test="${provinceType == '22' }">selected="selected"</c:if> >山东省</option>
							<option value="23" <c:if test="${provinceType == '23' }">selected="selected"</c:if> >四川省</option>
							<option value="24" <c:if test="${provinceType == '24' }">selected="selected"</c:if> >山西省</option>
							<option value="25" <c:if test="${provinceType == '25' }">selected="selected"</c:if> >陕西省</option>
							<option value="26" <c:if test="${provinceType == '26' }">selected="selected"</c:if> >天津市</option>
							<option value="27" <c:if test="${provinceType == '27' }">selected="selected"</c:if> >台湾省</option>
							<option value="28" <c:if test="${provinceType == '28' }">selected="selected"</c:if> >西藏自治区</option>
							<option value="34" <c:if test="${provinceType == '34' }">selected="selected"</c:if> >新疆公共卫生疾控中心</option>
							<option value="1" <c:if test="${provinceType == '1' }">selected="selected"</c:if> >新疆维吾尔自治区</option>
							<option value="32" <c:if test="${provinceType == '32' }">selected="selected"</c:if> >新疆生产建设兵团</option>
							<option value="30" <c:if test="${provinceType == '30' }">selected="selected"</c:if> >云南省</option>
							<option value="31" <c:if test="${provinceType == '31' }">selected="selected"</c:if> >浙江省</option>
							<option value="33" <c:if test="${provinceType == '33' }">selected="selected"</c:if> >其他</option>
						</select> 
						</div>
					</li>
					<li>
						<input type="text" id="accountName" name="accountName" class="mobile" onchange="trimInputValue(this)"
							   style="position: relative;z-index:2;background-color: white;left:3px;top:3px;"
							   placeholder="请输入您的用户名" maxlength="100" />
						<select id="accountList" class="minimal" onchange="hysSelectUserPassword(this)" style="position: relative;z-index:1;left:3px;top:-50px;">
						</select>
						<input type="hidden" id="openid" value="${openid }" name="openid" />
					</li>
					<li>
						<input type="password" class="password" id="passWord" name="accountPassword" placeholder="请输入您的密码"  maxlength="100">
					</li>
	                <li>
						<input type="text" class="password" placeholder="请输入验证码" id="validateCode" name="validateCode" maxlength="4">
	                    <div class="yzm"><img id="validateCodeImg" title="点击更换" onclick="javascript:refresh(this);" /></div>
					</li>
				</ul>
	            <div class="wjmm"> 
	            	<input style="position: absolute;left:40px;" id="remeberPwd" type="checkbox" checked="checked" />
	            	<span style="position: absolute;left: 60px;"> 记住密码</span>
	            	<a href="${ctx }/app/toResetPwd" class="">忘记密码</a>
	            </div>
				<a href="javascript:submtForm();" class="qbd_next_button register_next_but">登录</a>
				<c:if test="${sso:isActiveH5()}">
					<%--<a href="javascript:ssoAuth();" class="qbd_next_button register_next_but">学员SSO登录</a>--%>
				</c:if>
			</div>
			<!--绑定end-->
		</div>
		</form>
		
		<div class="mark"></div>   
		
		<!-- 版权信息 -->
		<div style="text-align: center;width: 100%;color: #666;margin-top: 2rem;">
			Copyright © 2000-2018 好医生网站 版权所有
		</div>
		<br/>
		<%@include file="/common/umengCommon.jsp"%>
</body>
</html>
<script>
	function refresh(img) {
	    img.src = "${ctx}/validatecode?" + Math.random();
	}
	function trimInputValue(inputObj){
		$(inputObj).val($.trim($(inputObj).val()));
	}
	function submtForm() {
		var provinceType = $("#provinceType").val();
		if (provinceType == null || provinceType == "" || provinceType == undefined){
			$(".mark").show();
	        alertify.alert("请选择规培省份",function (e) {
	            $(".mark").hide();
	            $("#provinceType").focus();
	        });
			return false;
		}
		 var accountName = $("#accountName").val();
		 if(accountName==null||accountName==undefined||accountName==""){
			$(".mark").show();
	        alertify.alert("请输入用户名",function (e) {
	            $(".mark").hide();
	            $("#accountName").focus();
	        });
			return false;
		 }
		 var accountPassword = $("#passWord").val();
		 if(accountPassword==null||accountPassword==undefined||accountPassword==""){
			$(".mark").show();
	        alertify.alert("请输入密码",function (e) {
	            $(".mark").hide();
	            $("#passWord").focus();
	        });
			return false;
		 }		 		 
		 var validateCode = $("#validateCode").val();
		 if(validateCode==null||validateCode==undefined||validateCode==""){
			$(".mark").show();
	        alertify.alert("请输入验证码",function (e) {
	            $(".mark").hide();
	            $("#validateCode").focus();
	        });			
			return false;
		 }			 

		 var data = $("#form").serialize();
		 loginApp(data);
	}
	
	function loginApp(data) {
		//alert(data);
		 $.ajax({
				url:'${ctx}/app/login',
				type:'post',
				data: data,
				dataType:'text',
				success:function(message){
					if(message == "errorValidateCode"){
						var imgCode = document.getElementById("validateCodeImg");  
						refresh(imgCode);
						$(".mark").show();
				        alertify.alert("验证码输入有误",function (e) {
				            $(".mark").hide();
				            $("#validateCode").val("");
				            $("#validateCode").focus();
				        });		
					}else if (message == "errorAccount"){
						$(".mark").show();
				        alertify.alert("用户名或密码错误",function (e) {
				            $(".mark").hide();
				        });			
					}else if (message == "error"){
						$(".mark").show();
				        alertify.alert("系统异常,请稍后重试",function (e) {
				            $(".mark").hide();
				        });			
					}else if (message == "errorArea"){//未开放的地区
						$(".mark").show();
				        alertify.alert("该地区暂未开放!",function (e) {
				            $(".mark").hide();
				        });			
					}else if (message == "xinjiang"){
						$(".mark").show();
				        alertify.alert("您的账户和规培省份不一致，请重新选择！",function (e) {
				            $(".mark").hide();
				        });				
					}else if (message == "shixisheng"){
						$(".mark").show();
				        alertify.alert("您的账户和规培省份不一致，请重新选择！",function (e) {
				            $(".mark").hide();
				        });	
					} else {
						// localStorage.setItem("n",$("#accountName").val());
						// localStorage.setItem("p",$("#passWord").val());
						hysSetUserPassword();
						window.location.href = "${ctx}"+message;
					}
				}
		 });
	}
	
	 $(function(){
		 //localStorage.clear();
		 var imgCode = document.getElementById("validateCodeImg");  
		 refresh(imgCode);
		 //showStorage();

		 hysInitUserPassword();
	 });	
	 function showStorage(){
	 	  var storage = window.localStorage;
	 	  var dataJson="type=2";
		//alert(storage.length);
		  if(storage.length>0) {
//			  dataJson += "\"accountName\":\""+storage.getItem(storage.key(0))+"\"";
//			  dataJson += ",\"accountPassword\":\""+storage.getItem(storage.key(1))+"\"";
			  dataJson += "&accountName="+storage.getItem("n");
			  dataJson += "&accountPassword="+storage.getItem("p");

			  //alert(dataJson);
			  //loginApp(dataJson);
		  }
	 }

	/**
	 * 记住密码功能
	 * hys_set_user_passwords
	 */
	function hysSetUserPassword(){

		//查看库是否存在
		var hysDatasNewArr=[];

		var hysDatasArr=hysGetLocalUserPassword();
		if(hysDatasArr==null||hysDatasArr==undefined||hysDatasArr.toString()===hysGetLocalUserPasswordError.toString()){
			return;
		}

		var remeberVal=$("#remeberPwd:checked").val();
		var userName=$("#accountName").val();
		var userPwd=$("#passWord").val();

		var isRemeber=false;
		if(remeberVal!=null&&remeberVal!=undefined){
			isRemeber=true;
		}

		if(userName!=null&&userName!=undefined&&userName.toString().trim()!=''){
			userPwd=userPwd!=null&&userPwd!=undefined?userPwd:'';

			if(isRemeber){//不记就清空
				var curUserInfo={};
				curUserInfo.userName=userName;
				curUserInfo.pwd=userPwd;
				hysDatasNewArr[hysDatasNewArr.length]=curUserInfo;//向顶部记忆密码
			}

			//重新拉取数据并排除本次的账号 然后追加到新数据
			for (let i = 0; i < hysDatasArr.length; i++) {
				let hisObj=hysDatasArr[i];
				if(hisObj.userName==null||hisObj.userName==undefined||hisObj.userName!=userName){
					hysDatasNewArr[hysDatasNewArr.length]=hisObj;
				}
			}

			try{
				localStorage.setItem(hysGetLocalUserPasswordKey,encodeURIComponent(JSON.stringify(hysDatasNewArr)));
			}catch (e) {
				alert('密码记忆失败!');
			}
		}
	}

	var hysGetLocalUserPasswordError='error';
	var hysGetLocalUserPasswordKey='hys_set_user_passwords';
	/**
	 * 获取本地密码数据
	 */
	function hysGetLocalUserPassword(){
		var hysDatasArr=null;
		if(typeof(localStorage) == "undefined" || localStorage == null||localStorage==undefined){
			alert('浏览器内核不支持记住密码功能');
			return hysGetLocalUserPasswordError;
		}else{
			var hysDatas=localStorage.getItem(hysGetLocalUserPasswordKey);
			if(hysDatas!=null&&hysDatas!=undefined){
				try{
					hysDatasArr=JSON.parse(decodeURIComponent(hysDatas));
				}catch (e) {
					alert('密码记忆数据被破坏，已重置记忆数据');
					hysDatasArr=[];
				}
			}else{
				hysDatasArr=[];
			}
		}
		return hysDatasArr;
	}

	/**
	 * 选择密码到筛选框
	 */
	function hysSelectUserPassword(selEl){

		var userNameVal=$(selEl).val();
		if(!userNameVal){
			return;
		}

		var hysDatasArr=hysGetLocalUserPassword();
		if(hysDatasArr==null||hysDatasArr==undefined||hysDatasArr.toString()===hysGetLocalUserPasswordError.toString()){
			return;
		}

		for (let i = 0; i < hysDatasArr.length; i++) {
			let hisObj=hysDatasArr[i];
			if(hisObj.userName!=null&&hisObj.userName!=undefined&&hisObj.userName.toString()===userNameVal.toString()){

				console.log("opelok-sel-"+userNameVal+','+hisObj.pwd);

				$("#accountName").val(userNameVal);
				$("#passWord").val(hisObj.pwd);
				break;
			}
		}
	}

	/**
	 * 默认加载记住密码下拉框
	 */
	function hysInitUserPassword(){
		var hysDatasArr=hysGetLocalUserPassword();
		if(hysDatasArr==null||hysDatasArr==undefined||hysDatasArr.toString()===hysGetLocalUserPasswordError.toString()){
			return;
		}
		let selHtml='';
		for (let i = 0; i < hysDatasArr.length; i++) {
			let hisObj=hysDatasArr[i];
			let userName=hisObj.userName;
			if(userName!=null&&userName!=undefined){
				selHtml+='<option value="'+userName+'">'+userName+'</option>';
			}
		}
		if(selHtml.length>0){
			selHtml='<option value="" selected="selected">请选择登录账号</option>'+selHtml;
			$("#accountList").html(selHtml);
		}else{
			$("#accountList").css('display','none');
		}
	}
	 
	 
	 //监听浏览器的返回事件
    $(function(){
    	pushHistory();
    	window.addEventListener("popstate", function(e) {
    		//alert("我监听到了浏览器的返回按钮事件啦");//根据自己的需求实现自己的功能
    		var isWinxin = is_weixn();
    		if (isWinxin){ 
    			//在微信中可以直接调用，不需要引入任何的js
    			WeixinJSBridge.call('closeWindow');
    		}else{
    			//window.nativeFinish.finishWeb();
    		}
    	 	
    	}, false);
    	function pushHistory() {
		    	var state = {
		    	title: "title",
		    	url: "#"
	    	};
	    	window.history.pushState(state, "title", "#");
    	}
	});
	
	//判断是否是微信
	function is_weixn(){  
	    var ua = navigator.userAgent.toLowerCase();  
	    if(ua.match(/MicroMessenger/i)=="micromessenger") {  
	        return true;  
	    } else {  
	        return false;  
	    }  
	} 
	
	//改变了规培省份，更换域名
	function changeProvinceType(){
		var provinceType = $("#provinceType").val();
		if(${nr:isDev() }){
    		window.location.href = hys.bp() + '/app/index?provinceType=' + provinceType;
    		return;
    	}
		if(${nr:isTest() }){
			if (provinceType == 1){
				//新疆
				window.location.href = "http://192.168.191.31:8091/xjrct/app/index?provinceType="+provinceType;
			} else {
				window.location.href = "http://192.168.191.31:8091/rct/app/index?provinceType="+provinceType;
			}
			return;
		}
		//域名和项目名称都需要确认
		if (provinceType == 1){
			//新疆
			window.location.href = "http://xjrct.xjyxonline.com/newrct/app/index?provinceType="+provinceType;
		} else {
			window.location.href = "http://zyy.haoyisheng.com/newrct/app/index?provinceType="+provinceType;
		}
	}
	
	/**
	 * 获得项目根路径
	 */
	function bp() {
		var curWwwPath = window.document.location.href;
		var pathName = window.document.location.pathname;
		var pos = curWwwPath.indexOf(pathName);
		var localhostPaht = curWwwPath.substring(0, pos);
		var projectName = pathName.substring(0, pathName.substr(1).indexOf('/') + 1);
		return (localhostPaht + projectName);
	};
	
	function ssoAuth() {
		/*var provinceType = $("#provinceType").val();
		if (provinceType == null || provinceType == "" || provinceType == undefined){
			$(".mark").show();
	        alertify.alert("请选择规培省份",function (e) {
	            $(".mark").hide();
	            $("#provinceType").focus();
	        });
			return false;
		}*/
		window.location.href = '${sso:getH5LoginUrl()}?redirect=' + bp() + '/app/sso/autoLogin&source=13';
	}
</script>

















