<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ include file="/common/taglibs.jsp"%>
<%@ include file="/common/app_common.jsp"%>
<!DOCTYPE html>
<html lang="en">
<head>
	<title>首页</title>
	<meta charset="UTF-8">
	<meta content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" name="viewport" id="viewport"/>
	<meta name = "format-detection" content = "telephone=no">
	<meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
	<meta http-equiv="keywords" content="好医生">
	<link rel="stylesheet" href="${ctx}/css/mobile_new/css/reset.css"/>
	<link rel="stylesheet" href="${ctx}/css/mobile_new/css/common.css"/>
	<link rel="stylesheet" href="${ctx}/css/mobile_new/css/main.css" />
	<link rel="stylesheet" href="${ctx}/css/mobile_new/css/alertify.core.css" />
	<link rel="stylesheet" href="${ctx}/css/mobile_new/css/alertify.default.css" />
	<link rel="stylesheet" href="${ctx }/css/attendance/css/dropload.css"/>
	<script src="${ctx}/css/mobile_new/js/init.js"></script>
	<script src="${ctx}/js/jquery-1.10.2.min.js"></script>
	<script src="${ctx}/css/mobile_new/js/app.js"></script>
	<script src="${ctx}/css/mobile_new/js/alertify.min.js"></script>
	<script type="text/javascript" src="${ctx}/js/attendance/dropload.js"></script>
	<style type="text/css">
		.go-home{display:none;}
	</style>
</head>
<body class="bodybg pdb250">
<!-- 头部start -->
<header class="qbd_header clearfix">
    <h1 class="fs32">首页</h1>
</header>
<!-- 头部end -->
<!-- 学员信息start -->
<div class="user_box">
    	<div class="user_pic">
    	<c:choose>
		    <c:when test="${not empty User.photoPath}">
		       <img src="${ctx}${User.photoPath}" alt=""/>
		    </c:when>
		    <c:otherwise>
		       <img src="${ctx}/css/mobile_new/img/pic.png" alt=""/>
		    </c:otherwise>
		</c:choose>

    	</div>
        <div class="user_text" style="margin-left:2rem">
        	<h3></h3>
            <p>${userBasicInfo.deptName}</p>
            <p>${teacherCount}个带教 &nbsp; ${studentCount} 个学员</p>

            <c:if test="${not empty user.relVoList }">
	            <div style="float: right;margin-top:-1.2rem;">
		            <select style="width: 75px;background-image: url('');color:#999;" id="changeAccountName" onchange="changeRelatedAccount();">
		            	<option  value="">切换账号</option>
		            	<c:forEach items="${user.relVoList }" var="accountVo">
							<option style="color:#1a1a1a;"  value="${accountVo.accountName }">${accountVo.showUnitName }</option>
						</c:forEach>
		            	<option style="color:#1a1a1a;" value="logoutApp">退出账号</option>
		            </select>
	            </div>
            </c:if>
            <c:if test="${empty user.relVoList }">
               <a href="javascript:logoutApp();" >退出账号</a>
            </c:if>
        </div>
</div>
<!-- 学员信息end -->
<table style="width: 100%;display: none">
	<tr>
		<td> <a href="javascript:pointTeacher();" class="qbd_next_button register_next_but">指定带教 </a> </td>
		<!--
			<td> <a href="javascript:myStudent();" class="qbd_next_button register_next_but">我的学员</a></td>
		-->
		<td> <a href="javascript:activity();" class="qbd_next_button register_next_but">发布活动</a></td>
	</tr>
</table>

<!-- 学员信息end -->
<ul class="zyy_nav">
    <li><a href="javascript:pointTeacher();"><img src="${ctx}/css/mobile_new/img/txsc.png"   alt=""/><span>轮转管理</span></a></li>
    <li><a href="javascript:enterTeacher();" ><img src="${ctx}/css/mobile_new/img/work.png"   alt=""/><span>入科教育</span></a></li>
    <li><a href="javascript:activity();" ><img src="${ctx}/css/mobile_new/img/fbhd.png"   alt=""/><span>教学活动</span></a></li>
    <c:if test="${userBasicInfo.zyyUserProvinceId != '60000000' && userBasicInfo.zyyUserProvinceId != '90010007' }">
    <li><a href="javascript:toEvalList();"  ><img src="${ctx}/css/mobile_new/img/pjgl.png"   alt=""/><span>评价管理</span></a></li>
    </c:if>
    <li><a href="javascript:toAuditAttendance();"><img src="${ctx}/css/mobile_new/img/kqgl.png" alt=""/><span>考勤管理</span></a></li>
    <c:if test="${user.zyyUserProvinceId == '90010004' }">
    <li><a href="javascript:toScheduleCourse();"><img src="${ctx}/css/mobile_new/img/kcgl.png" alt=""/><span>课程管理</span></a></li>
    </c:if>
    <li><a href="javascript:toHandBookAudit();"><img src="${ctx}/css/mobile_new/img/scsh.png"   alt=""/><span>手册审查</span></a></li>
    <!-- <li><a href="javascript:connectServiceApp();"><img src="${ctx}/css/mobile_new/img/lxkf.png"   alt=""/><span>客服</span></a></li> -->
    <li><a href="javascript:toStatPage();"><img src="${ctx}/css/mobile_new/img/icon-stat.png" alt=""/><span>统计</span></a></li>
	<li><a href="javascript:toDeptExam();"><img src="${ctx}/css/mobile_new/img/drafts_002.png" alt=""/><span>出科考试</span></a></li>
</ul>
<!-- 消息start -->
<div class="user_box divPFont" style="border-bottom:1px solid #f0f0f0;">
	<p style="float: left;">我的消息</p>
</div>
<div id="myMessage">
	<ul class="news_text"></ul>
</div>
<!-- 消息end -->
<!-- 小尾巴start -->
<div class="foot">
  <ul class="foot_nav">
    <li class="fr1"><a href="${ctx}/mainapp/index" class="active">首页</a></li>
 	<li class="fr4"><a href="javascript:pointTeacher();" >带教管理</a></li>
    <li class="fr5"><a href="javascript:activity();" >教学活动</a></li>
    <li class="fr3"><a href="${ctx}/mainapp/me" >我的</a></li>
  </ul>
</div>
</body>
</html>
<script>
	var dropload;
	var loadEnd = false;
	var page = 0;

	function refresh() {
		window.localStorage.clear();
		window.location.href = "${ctx}/app/index";
	}

$(function(){
	var a=$(".titleClass a");
	for(var i=0;i<a.length;i++){
	    a[i].addEventListener('touchstar',function(){},false);
	}
	 //localStorage.clear();
	 pushHistory();
    	window.addEventListener("popstate", function(e) {
    		//alert("我监听到了浏览器的返回按钮事件啦");//根据自己的需求实现自己的功能
    		var isWinxin = is_weixn();
    		if (isWinxin){
    			//在微信中可以直接调用，不需要引入任何的js
    			//WeixinJSBridge.call('closeWindow');
    			var test = window.location.pathname;
    			if(test.search("main") != -1){
    			   window.history.go(-1)
    			}
    		}else{
    		     window.history.go(-1)
    			//window.nativeFinish.finishWeb();
    		}

    	}, false);
    	function pushHistory() {
		    	var state = {
		    	title: "title",
		    	url: "#"
	    	};
	    	window.history.pushState(state, "title", "#");
    	}

    	function is_weixn(){
		    var ua = navigator.userAgent.toLowerCase();
		    if(ua.match(/MicroMessenger/i)=="micromessenger") {
		        return true;
		    } else {
		        return false;
		    }
	    }
	
   	dropload = $('#myMessage').dropload({
        scrollArea: window,
        domDown: {
            domClass: 'dropload-down',
            domRefresh: '<div class="dropload-refresh">上拉加载更多</div>',
            domLoad: '<div class="dropload-load"><span class="loading"></span>加载中...</div>',
            domNoData: '<div class="dropload-noData">已无数据</div>'
        },
        loadDownFn: function (me) {
            setTimeout(function () {
                if (loadEnd) {
                	//不在加载数据
                	me.resetload();
			        me.lock();
			        me.noData();
			        me.resetload();
                    return;
                }
				loadMyMessage(me);
            }, 500);
        }
    });
	
});

//跳转到指定带教
function pointTeacher() {
	window.location.href = "${ctx}/deptApp/deptManager/listByDept";
}
//请假管理
function toAuditAttendance() {
	window.location.href = "${ctx}/appAttendance/queryAudit";
}
//跳转到发布活动
function activity() {
	window.location.href = "${ctx}/mainapp/activityPlan";
}
// 跳转到入科教育页面
function enterTeacher() {
	window.location.href = "${ctx}/appEenterTeach/viewlist";
}

function toEvalList(){
	window.location.href = "${ctx}/evalApp/evaluate/toListView";
}
function logoutApp(){
	window.location.href = "${ctx}/app/logout";
}

function connectServiceApp(){
	window.location.href = "https://static.meiqia.com/dist/standalone.html?_=t&eid=94076";
}

function toEvalListApp(){
	window.location.href = "${ctx}/evalApp/evaluate/toListView";
}
function toHandBookAudit(){
	window.location.href = "${ctx}/appHandbook/manualverify";
}

function toScheduleCourse() {
	window.location.href = "${ctx}/appSchedule/scheduleCourseList";
}

function toStatPage() {
	window.location.href = "${ctx}/appWorkQualityMonthReport/statPage";
}

function toDeptExam() {
	window.location.href = "${ctx}/appDeptExam/leaveDeptExamList";
}

//切换账号
function changeRelatedAccount(){
	var accountName = $("#changeAccountName").val();
	if (accountName ==  null || accountName == ''){
		return ;
	}
	if ('logoutApp' == accountName ){
		logoutApp();
	}else{
		window.location.href = "${ctx}/app/changeRelatedAccount?accountName="+accountName;
	}
}

function dateFormat_1(longTypeDate){
    var dateType = "";
    var date = new Date();
    date.setTime(longTypeDate);
    dateType += date.getFullYear();   //年
    dateType += "-" + (date.getMonth()+1); //月
    dateType += "-" + date.getDate();   //日
    dateType+= "&nbsp;&nbsp;" + date.getHours();   //时
    dateType+= ":" + date.getMinutes();      //分
    return dateType;
}

function toDetail(id, messageType){
	if(messageType == 'jx')
		window.location.href = "${ctx}/mainapp/toEditActivityView?id="+id+"&look=true";
	else if (messageType == 'tz')
		window.location.href = "${ctx}/mainapp/viewMessage?msId=" + id;
}

	function loadMyMessage(me){
		var pageOffset = page;
		pageOffset += Number(1);
		page = pageOffset;
		$.ajax({
			url: '${ctx}/mainapp/loadMyMessage',
			data: {"pageOffset":pageOffset},
			dataType: 'json',
			success:function(data){
				var code = data.code;
				if (code == "200"){
					var isLast = data.result.isLast;//是否最后一页
					var pager = data.result.pager;
					var result = '';
					if (pager != "" && pager != null){
						var list = pager.list;
						var len = list.length;
						for (var i = 0; i < len; i++)
							result += genHtml(list[i]);
					}
					$('#myMessage').find('ul').append(result);
					if (isLast == 1)
						loadEnd = true;
					//这个需要在ajax成功之后调用，否则会多次请求数据
					me.resetload();
				}else{
					loadEnd = true;
					alert("后台加载数据失败！");
				}
				
			},
			error:function(){
				loadEnd = true;
				alert("加载数据失败");
			}
		});
	}
	
	function genHtml(node){
	   var id = node.id;
	   var messageType = node.messageType;
	   var messageIcon = messageType == 'jx' ? '教学' : '通知';
	   var messageName = node.messageName;
	   var timeSection = node.timeSection;
	   var resiCount = node.resiCount;
	   var createrName = node.createrName;
	   var html = ('<li class="titleClass" onclick="toDetail(' + id + ',\'' + messageType + '\');">');
	   html += ('<span class="' + messageType + '">' + messageIcon + '</span>');
	   html += ('<h3><a href="javascript:;">' + messageName + '</a></h3>');
	   html += '<div class="form">';
	   html += ('<span class="fl">' + timeSection + '</span>');
	   if(messageType == 'jx')
	       html += ('<span style="position:absolute;right:.8rem;top:.4rem">' + resiCount + '&nbsp;人</span>');
	   html += ('<span class="fr">' + createrName + '</span>');
	   html += '</div></li>';
	   return html;
   }
</script>
