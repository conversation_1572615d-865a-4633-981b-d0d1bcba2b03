package com.hys.zyy.manage.collection;

import java.util.ArrayDeque;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import java.util.ListIterator;
import java.util.Queue;

/**
 * 循环队列
 * peek 取出队列第一个元素，没有为null
 * poll 取出队列第一个元素，并将该元素放到队列尾部
 * <AUTHOR>
 *
 * @param <E>
 */
public class CycleQueue<E> implements List<E>, Queue<E> {


	private ArrayDeque<E> delegate;

	public CycleQueue() {

	this.delegate = new ArrayDeque<E>();

	}


	public CycleQueue(Collection<E> list) {

	if(list != null || !list.isEmpty()) {

	this.delegate = new ArrayDeque<E>(list.size());

	this.delegate.addAll(list);

	}

	else

	this.delegate = new ArrayDeque<E>();

	}


	@Override

	public boolean add(E e) {

	return delegate.add(e);

	}




	@Override

	public boolean addAll(Collection<? extends E> c) {

	return delegate.addAll(c);

	}




	@Override

	public void clear() {

	delegate.clear();

	}




	@Override

	public boolean contains(Object o) {

	return delegate.contains(o);

	}




	@Override

	public boolean containsAll(Collection<?> c) {

	return delegate.containsAll(c);

	}




	@Override

	public boolean isEmpty() {

	return delegate.isEmpty();

	}




	@Override

	public Iterator<E> iterator() {

	return delegate.iterator();

	}




	@Override

	public boolean remove(Object o) {

	return delegate.remove(o);

	}




	@Override

	public boolean removeAll(Collection<?> c) {

	return delegate.removeAll(c);

	}




	@Override

	public boolean retainAll(Collection<?> c) {

	return delegate.retainAll(c);

	}




	@Override

	public int size() {

	return delegate.size();

	}




	@Override

	public Object[] toArray() {

	return delegate.toArray();

	}




	@Override

	public <T> T[] toArray(T[] a) {

	return delegate.toArray(a);

	}


	@Override

	@Deprecated

	public void add(int index, E element) {

	throw new UnsupportedOperationException(); 

	}




	@Override

	@Deprecated

	public boolean addAll(int index, Collection<? extends E> c) {

	throw new UnsupportedOperationException();

	}




	@Override

	@Deprecated

	public E get(int index) {

	throw new UnsupportedOperationException();

	}




	@Override

	@Deprecated

	public int indexOf(Object o) {

	throw new UnsupportedOperationException();

	}




	@Override

	@Deprecated

	public int lastIndexOf(Object o) {

	throw new UnsupportedOperationException();

	}




	@Override

	@Deprecated

	public ListIterator<E> listIterator() {

	throw new UnsupportedOperationException();

	}




	@Override

	@Deprecated

	public ListIterator<E> listIterator(int index) {

	throw new UnsupportedOperationException();

	}




	@Override

	@Deprecated

	public E remove(int index) {

	throw new UnsupportedOperationException();

	}




	@Override

	@Deprecated

	public E set(int index, E element) {

	throw new UnsupportedOperationException();

	}




	@Override

	@Deprecated

	public List<E> subList(int fromIndex, int toIndex) {

	throw new UnsupportedOperationException();

	}


	/****************************************************
	* 
	* Queue实现
	* 
	****************************************************/




	@Override

	public E element() {

	return delegate.element();

	}




	@Override

	public boolean offer(E e) {

	return delegate.offer(e);

	}




	@Override

	public E peek() {

	return delegate.peek();

	}




	@Override

	public E poll() {
		E node = delegate.poll();
		if(node != null)
			delegate.offer(node);
		return node;
	}




	@Override

	public E remove() {

	return delegate.remove();

	}


}

