package com.hys.zyy.manage.constants;

/**
 * 常用常量类
 * <AUTHOR>
 * 2018-4-27 下午1:50:09
 */
public class CommonConstants {
	// 注册，短信验证码
	public final static String MSG_REG_CODE = "msgRegCode_";

	//万能短信验证码
	public final static String SUPER_MSG_CODE_V2 ="20210301";

	//老用户注册的id
	public final static String OLD_USER_ID ="oldUserId";

	//结业再培注册的id
	public final static String GRADUATION_USER_ID ="graduationUserId";

	//发短信接口  model ： 4 - 成功 （args：一个参数） 5 - 失败 （args：一个参数：用户名，另一个失败原因，有顺序）
	public final static String MSG_MODEL_SUCCESS ="4";
	public final static String MSG_MODEL_FAIL ="5";

	//1 = 老用户
	public final static Integer OLD_USER_YES = 1;

	//自动登录需要的数据 accountName accountPassword zyyOrgId
	public final static String AUTO_LOGIN_ACCOUNT_NAME ="auto_login_account_name";
	public final static String AUTO_LOGIN_ACCOUNT_PASSWORD ="auto_login_account_password";

	//默认查询页数的大小
	public final static Integer PAGE_SIZE_DEFAULT = 99999;

	//定时任务的页数大小
	public final static Integer PAGE_SIZE_TIMER = 100;

	//定时任务相关的常量类
	public final static String SAVE_NEED_VAL_REC_BY_STUDENT = "SAVE_NEED_VAL_REC_BY_STUDENT";
	public final static String SAVE_NEED_VAL_REC_BY_TEACHER = "SAVE_NEED_VAL_REC_BY_TEACHER";
	public final static String SAVE_NEED_VAL_REC_BY_TUTOR = "SAVE_NEED_VAL_REC_BY_TUTOR";
	public final static String SAVE_NEED_VAL_REC_BY_DEPART = "SAVE_NEED_VAL_REC_BY_DEPART";
	public final static String SAVE_NEED_VAL_REC_BY_BASE = "SAVE_NEED_VAL_REC_BY_BASE";
	public final static String SAVE_NEED_VAL_REC_BY_HOSPITAL = "SAVE_NEED_VAL_REC_BY_HOSPITAL";
	public final static String ON = "on";
	public final static String ZERO = "0";
	public final static int EVAL_JOB_EXPIRE = 300;

	// 学员管理 导出excle的名称
	public final static String USER_INFO = "user_info";
	public final static String RECRUIT_USER_INFO = "recruit_user_info";
	public final static String USER_INFO_GRADUATE = "user_info_graduate";
	public final static String USER_INFO_TARGET = "user_info_target";
	public final static String ADMIN = "admin";
	public final static Long ADMIN_ORG= Long.valueOf(-100);

	//频率 1 ： 仅一次  2 ：每年发布
	public final static Integer PUBLISH_FREQUENCY_ONE = 1;
	public final static Integer PUBLISH_FREQUENCY_YEAR = 2;

	//导出人员模板名称
	public final static String EXPORT_STUINFO_YN = "export_stuinfo_yn.xlsx";
	public final static String EXPORT_EXTEND_INFO = "exportExtendInfo";
	public final static String EXPORT_STUDENT_STATUS = "exportStudentStatus";

	//默认导出的大小 100万
	public final static Integer EXPORT_SIZE_DEFAULT = 1000000;

	//是否仅查询总数 1 - 是 ， 空 - 否  app需要优化速度
	public final static Integer ONLY_TOTAL_COUNT_YES = null;
	//PROVINCE - 省行政区划
	public final static String  PROVINCE = "PROVINCE";
	//PROVINCE_DOMAIN -- 省份域名
	public final static String PROVINCE_DOMAIN = "PROVINCE_DOMAIN";

	public final static String PERSENT_ZERO = "0%";
	public final static String PERSENT_HUNDRED = "100%";

	//赣南医学院第一附属医院
	public static final Long JXGN_ORG_ID = Long.valueOf(9120);//测试 434  ，正式  9120
	//赣州市人民医院
	public static final Long JXGZ_ORG_ID = Long.valueOf(9121);
	// 德阳市人民医院
	public static final Long DYSRMYY_ORG_ID = Long.valueOf(9242);

	//数字对应汉字  按下标取值
	public static final String[] NUMBER_CHINESE_ARR= new String[]{"零","一","二","三","四","五","六","七","八","九","十"};

	public static final String[] WEEK_NUMBER_CHINESE= new String[]{"日","一","二","三","四","五","六","日"};

	public static final String[] NUMBER_CHINESE_ARR_EXT= new String[]{"零","一","二","三","四","五","六","七","八","九",
		"十","十一","十二","十三","十四","十五","十六","十七","十八","十九",
		"二十","二十一","二十二","二十三","二十四","二十五","二十六","二十七","二十八","二十九",
		"三十","三十一","三十二","三十三","三十四","三十五","三十六","三十七","三十八","三十九",
		"四十","四十一","四十二","四十三","四十四","四十五","四十六","四十七","四十八","四十九",
		"五十","五十一","五十二","五十三","五十四","五十五"};
	public static final String[] NUMBER_CHINESE_WEEK= new String[]{"日","一","二","三","四","五","六","日"};

	//克拉玛依中心医院  的特殊活动类型
	public static final Long KLMY_HOSPITAL_ACTIVITY_TYPE = Long.valueOf(-200) ;

	public static final String UNIQUE_SIGN = "ZYY-";

	public static final String PC = "PC";

	public static final String H5 = "H5";

	public static final String APP = "APP";

	public static final String ZYY_EXCLUDE_VALIDATE_PC = "zyyExcludeValidatePC_";

	public static final String YES = "yes";

	public static final String NO = "no";

	public static final String ZYY_PREPARE_WILL_SET = "zyyPrepareWillSet";
	/**
	 * 	对接云课堂用户保存成功
 	 */
	public static final Integer  SUCCESS=1;
	/**
	 * 	对接云课堂用户保存失败
	 */
	public static final Integer  FAILD=0;
	
	public static final String QINIU_FILE_DOMAIN = "qiniu_file_domain";

	public static final String BDP_PLAT = "bdpPlat2023";
	public static final Integer ONE = 1;
	public static final Integer TWO = 2;
	public static final Integer THREE = 3;
	public static final Integer TYPE_1 = 1;
	public static final Integer TYPE_2 = 2;
	public static final Integer TYPE_3 = 3;
	public static final Integer TYPE_4 = 4;
	public static final Integer SUCCESS_CODE = 200;

	// bdp的字典分类
	//	SEX	性别
	public static final String SEX = "SEX";
	//	DEPT	科室
	public static final String DEPT = "DEPT";
	//	MAJOR	专业
	public static final String MAJOR = "MAJOR";
	//	CERTIFICATE	身份证类型
	public static final String CERTIFICATE = "CERTIFICATE";
	//	TITLE	职称
	public static final String TITLE = "TITLE";
	//	HOSPITAL	医院分类
	public static final String HOSPITAL = "HOSPITAL";
	//	HIGHSCHOOL	最高学历
	public static final String HIGHSCHOOL = "HIGHSCHOOL";
	//	HIGHESTDEGREE	最高学位
	public static final String HIGHESTDEGREE = "HIGHESTDEGREE";

	//AES key haoyisheng2022
	public static final String AES256_KEY = "haoyisheng2022";

	// 住院医的字典
	// HIGHEST_RECORD_SCHOOL
	public static final String HIGHEST_RECORD_SCHOOL = "HIGHEST_RECORD_SCHOOL";
	//HIGHEST_DEGREE
	public static final String HIGHEST_DEGREE = "HIGHEST_DEGREE";

}
