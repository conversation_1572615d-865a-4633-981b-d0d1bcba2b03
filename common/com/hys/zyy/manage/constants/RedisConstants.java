package com.hys.zyy.manage.constants;
/**
 * Redis相关的常量类
 * <AUTHOR>
 * @date 2017年8月11日 下午3:38:58
 */
public interface RedisConstants {
	//app_token,调用接口时需要用到的
	String RCT_APP_TOKEN_2020 = "RCT_APP_TOKEN_2020";
	//用户登录sessionId的系统前缀,redis中放userId
	String RCT_LOGIN_SESSEION_ID = "RCT_LOGIN_SESSEION_ID";
	//手机短信验证码
	String RCT_MOBILE_MSG_CHECK_CODE = "RCT_MOBILE_MSG_CHECK_CODE";
	String RCT_MOBILE_ACCOUNT_CHECK_CODE = "RCT_MOBILE_ACCOUNT_CHECK_CODE";

	//公共的appKey 
	String APP_KEY = "API_APP_KEY";
	//获取token的url
	String APP_TOKEN_URL = "API_APP_TOKEN_URL";
	//发送短信验证码 （不校验手机号是否存在）
	String SEND_CHECK_CODE_URL = "API_SEND_CHECK_CODE_URL";
	//发送短信类通知，通用
	String SEND_MSG_URL = "API_SEND_MSG_URL";
	
	//放入redis的key：
	//云南省订单定向生 报名开始时间   和 结束时间
	String RCT_GRADUATE_TARGET_START_TIME_2018 = "RCT_GRADUATE_TARGET_START_TIME_2018";
	String RCT_GRADUATE_TARGET_END_TIME_2018 = "RCT_GRADUATE_TARGET_END_TIME_2018";
	// 云南专硕生注册时间
	String YN_ZS_REGISTER_START_TIME = "ynZsRegisterStartTime";
	String YN_ZS_REGISTER_END_TIME = "ynZsRegisterEndTime";
	
	//手机端公众号登录校验验证码
	String RCT_RANDOMCODE_20180801 = "RCT_RANDOMCODE_20180801";
	
	//账户信息
	String RCT_SECURITY_ACCOUNT_20181227 = "RCT_SECURITY_ACCOUNT_20181227";
	//用户详细信息
	String RCT_USER_EXTEND_20181227 = "RCT_USER_EXTEND_20181227";
	
	//试卷总分rediskey
	String PAPER_SCORE_20190118 = "PAPER_SCORE_20190118";
	
	// 本年度报名招录统计更新时间
	String ZYY_RECRUIT_STAT_UPDATE_TIME = "zyyRecruitStatUpdateTime";
	
	// 老学员注册专业基地限制开关
	String ZYY_OLD_USER_BASE_SET = "zyy_old_user_base_set_";
	
	String HIGHEST_BENKEYS = "_highestBenkeYs_";
	// 河北西医最高学历本科以上
	String HIGHEST_BENKEYS_1000000 = "1000000_highestBenkeYs_";
	// 云南住院医最高学历本科以上
	String HIGHEST_BENKEYS_69000000 = "69000000_highestBenkeYs_";
	
	// 形成性评价开关设置
	String APP_FORM_SWITCH_SETTING = "xjzyy_app_form_switch_setting_";
	
	String OFF = "off";
	
	// BDP用户ID前缀
	String BDP_USER_ID = "bdp_user_id_";
	
	// 机构域名对应关系
	String ZYY_ORG_DOMAINNAME_MAP = "zyy_org_domainname_map";
}
