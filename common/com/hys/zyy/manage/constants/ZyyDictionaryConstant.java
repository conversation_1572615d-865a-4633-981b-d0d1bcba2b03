package com.hys.zyy.manage.constants;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import com.hys.zyy.manage.util.StringUtils;

/**
 * 字典常量
 * <AUTHOR>
 */
public class ZyyDictionaryConstant {
	
	public static final Map<Integer, String> SEX_CONSTANT;
	public static final Map<Integer, String> FIRST_RECORD_SCHOOL_CONSTANT;
	public static final Map<Integer, String> FIRST_DEGREE_CONSTANT;
	public static final Map<Integer, String> HIGHEST_RECORD_SCHOOL_RESIDENCY_CONSTANT;
	public static final Map<Integer, String> HIGHEST_RECORD_SCHOOL_TEACHER_CONSTANT;
	public static final Map<Integer, String> HIGHEST_DEGREE_CONSTANT;

	static {
		/*
		 * 性别
		 */
		SEX_CONSTANT = new HashMap<Integer, String>();
		SEX_CONSTANT.put(null, "");
		SEX_CONSTANT.put(0, "未知");
		SEX_CONSTANT.put(1, "男");
		SEX_CONSTANT.put(2, "女");
		/*
		 * 第一学历
		 */
		FIRST_RECORD_SCHOOL_CONSTANT = new HashMap<Integer, String>();
		FIRST_RECORD_SCHOOL_CONSTANT.put(null, "");
		FIRST_RECORD_SCHOOL_CONSTANT.put(1, "大学专科教育");
		FIRST_RECORD_SCHOOL_CONSTANT.put(2, "大学本科教育");
		FIRST_RECORD_SCHOOL_CONSTANT.put(3, "硕士研究生");
		FIRST_RECORD_SCHOOL_CONSTANT.put(4, "博士研究生");
		FIRST_RECORD_SCHOOL_CONSTANT.put(6, "普通高级中学教育");
		FIRST_RECORD_SCHOOL_CONSTANT.put(7, "高中以下");
		FIRST_RECORD_SCHOOL_CONSTANT.put(8, "其他");
		FIRST_RECORD_SCHOOL_CONSTANT.put(9, "中等职业教育");
		/*
		 * 第一学位
		 */
		FIRST_DEGREE_CONSTANT = new HashMap<Integer, String>();
		FIRST_DEGREE_CONSTANT.put(null, "");
		FIRST_DEGREE_CONSTANT.put(-1, "无");
		FIRST_DEGREE_CONSTANT.put(1, "学士");
		FIRST_DEGREE_CONSTANT.put(2, "硕士");
		FIRST_DEGREE_CONSTANT.put(3, "博士");
		/*
		 * 最高学历-学员
		 */
		HIGHEST_RECORD_SCHOOL_RESIDENCY_CONSTANT = new HashMap<Integer, String>();
		HIGHEST_RECORD_SCHOOL_RESIDENCY_CONSTANT.put(null, "");
		HIGHEST_RECORD_SCHOOL_RESIDENCY_CONSTANT.put(1, "大学专科教育");
		HIGHEST_RECORD_SCHOOL_RESIDENCY_CONSTANT.put(2, "大学本科教育");
		HIGHEST_RECORD_SCHOOL_RESIDENCY_CONSTANT.put(3, "硕士研究生");
		HIGHEST_RECORD_SCHOOL_RESIDENCY_CONSTANT.put(4, "博士研究生");
		HIGHEST_RECORD_SCHOOL_RESIDENCY_CONSTANT.put(6, "普通高级中学教育");
		HIGHEST_RECORD_SCHOOL_RESIDENCY_CONSTANT.put(7, "高中以下");
		HIGHEST_RECORD_SCHOOL_RESIDENCY_CONSTANT.put(8, "其他");
		HIGHEST_RECORD_SCHOOL_RESIDENCY_CONSTANT.put(9, "中等职业教育");
		/*
		 * 最高学历-带教
		 */
		HIGHEST_RECORD_SCHOOL_TEACHER_CONSTANT = new HashMap<Integer, String>();
		HIGHEST_RECORD_SCHOOL_TEACHER_CONSTANT.put(null, "");
		HIGHEST_RECORD_SCHOOL_TEACHER_CONSTANT.put(1, "大学专科");
		HIGHEST_RECORD_SCHOOL_TEACHER_CONSTANT.put(2, "大学本科");
		HIGHEST_RECORD_SCHOOL_TEACHER_CONSTANT.put(3, "硕士研究生");
		HIGHEST_RECORD_SCHOOL_TEACHER_CONSTANT.put(4, "博士研究生");
		HIGHEST_RECORD_SCHOOL_TEACHER_CONSTANT.put(5, "博士后");
		HIGHEST_RECORD_SCHOOL_TEACHER_CONSTANT.put(6, "普通高级中学教育");
		HIGHEST_RECORD_SCHOOL_TEACHER_CONSTANT.put(7, "高中以下");
		HIGHEST_RECORD_SCHOOL_TEACHER_CONSTANT.put(8, "其他");
		HIGHEST_RECORD_SCHOOL_TEACHER_CONSTANT.put(9, "中等职业教育");
		/*
		 * 最高学位
		 */
		HIGHEST_DEGREE_CONSTANT = new HashMap<Integer, String>();
		HIGHEST_DEGREE_CONSTANT.put(null, "");
		HIGHEST_DEGREE_CONSTANT.put(-1, "无");
		HIGHEST_DEGREE_CONSTANT.put(1, "学士");
		HIGHEST_DEGREE_CONSTANT.put(2, "硕士");
		HIGHEST_DEGREE_CONSTANT.put(3, "博士");
	}
	
	public static Integer getSexValue(String value) {
		Integer key = null;
		if (StringUtils.isBlank(value))
			return key;
		Set<Integer> keys = SEX_CONSTANT.keySet();
		for (Integer k : keys) {
			if (value.equals(SEX_CONSTANT.get(k))) {
				key = k;
				break;
			}
		}
		return key;
	}
	
	public static void main(String[] args) {
		// System.out.println(ZyyDictionaryConstant.SEX_CONSTANT.get(null));
		System.out.println(ZyyDictionaryConstant.getSexValue("男"));
	}

}




















