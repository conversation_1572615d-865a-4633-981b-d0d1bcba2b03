package com.hys.zyy.manage.constants;

import java.util.Map;

import com.alibaba.fastjson.JSON;

public class ZyyGraduationConstants {
	
	
	private static String REGIONCODE="{\"北京市\":\"11\",	\"天津市\":\"12\"," +
			"\"1000000\":\"13\",\"山西省\":\"14\",\"内蒙古自治区\":\"15\",\"辽宁省\":\"21\"," +
			"\"2\":\"22\",\"黑龙江省\":\"23\",\"上海市\":\"31\",\"江苏省\":\"32\"," +
			"\"浙江省\":\"33\",\"安徽省\":\"34\",\"福建省\"	:\"35\",\"江西省\"	:\"36\"," +
			"\"山东省\":\"37\",\"河南省\":\"41\",\"湖北省\"	:\"42\"," +
			"\"湖南省\":\"43\",\"广东省\":\"44\",\"广西壮族自治区\":\"45\",\"11000000\":\"46\"," +
			"\"重庆市\":\"50\",\"四川省\":\"51\",\"贵州省\"	:\"52\",\"69000000\":\"53\"," +
			"\"西藏自治区\":\"54\",\"陕西省\":\"61\",\"甘肃省\":\"62\",\"青海省\":\"63\"," +
			"\"宁夏回族自治区\":\"64\",\"60000000\":\"65\"}";
	
	public static final Map<String, String> REGIONMAP = (Map<String, String>)JSON.parse(REGIONCODE);
	
	private static String MAJORCODE="{\"内科\":\"0100\",\"儿科\":\"0200\",\"急诊科\":\"0300\",\"皮肤科\":\"0400\"," +
			"\"精神科\":\"0500\",\"神经内科\":\"0600\",\"全科\":\"0700\",\"康复医学科\":\"0800\"," +
			"\"外科\":\"0900\",\"外科（神经外科方向）\":\"1000\",\"外科（胸心外科方向）\":\"1100\",\"外科（泌尿外科方向）\":\"1200\"," +
			"\"外科（整形外科方向）\":\"1300\",\"骨科\":\"1400\",\"儿外科\":\"1500\",\"妇产科\":\"1600\"," +
			"\"眼科\":\"1700\",\"耳鼻咽喉科\":\"1800\",\"麻醉科\":\"1900\",\"临床病理科\":\"2000\"," +
			"\"检验医学科\":\"2100\",\"放射科\":\"2200\",\"超声医学科\":\"2300\",\"核医学科\":\"2400\"," +
			"\"放射肿瘤科\":\"2500\",\"医学遗传科\":\"2600\",\"预防医学科\":\"2700\",\"口腔全科\":\"2800\"," +
			"\"口腔内科\":\"2900\",\"口腔颌面外科\":\"3000\",\"口腔修复科\":\"3100\",\"口腔正畸科\":\"3200\"," +
			"\"口腔病理科\":\"3300\",\"口腔颌面影像科\":\"3400\",\"中医\":\"3500\",\"中医全科\":\"3600\",\"助理全科\":\"6100\"}";
	
	public static final Map<String, String> MAJORMAP = (Map<String, String>)JSON.parse(MAJORCODE);
	
	public static void main(String[] args){
				
		String mm = "1000000";
		System.out.println(REGIONMAP.get(mm));
	}
	
}
