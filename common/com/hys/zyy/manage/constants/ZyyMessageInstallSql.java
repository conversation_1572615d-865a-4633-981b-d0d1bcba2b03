package com.hys.zyy.manage.constants;

import com.hys.zyy.manage.model.ZyyUser;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：张伟清 2012-07-29
 * 
 * 描述：消息 查询用户 组装SQL
 * 
 * 说明:
 */
public class ZyyMessageInstallSql implements java.io.Serializable {

	private static final long serialVersionUID = -8730289481488617902L;
	
	final static private String getUserExtendVoList_SQL = "select distinct t.id, t.real_name, t.zyy_user_type, t1.email,t3.org_name,t3.id orgId,t3.alias_name orgAliasName "
		+ "from zyy_user t "
		+ "join zyy_user_extend t1 on t.id = t1.zyy_user_id "
		+ "join zyy_org t3 on t.zyy_user_org_id = t3.id " ;
	
	//查询报名学员
	final static private String getRecruitResidency_SQL = "select distinct t.id, t.real_name, t.zyy_user_type, t1.email,decode(t3.org_name,null,null) org_name,to_number(decode(t3.id,null,null)) orgId,decode(t3.alias_name,null,null) orgAliasName "
		+ "from zyy_user t "
		+ "join zyy_user_extend t1 on t.id = t1.zyy_user_id "
		+ "join zyy_recruit_residency_will t2 on t.id = t2.residency_id "
		+ "join zyy_org t3 on t2.hospital_id = t3.id "
		+ "join zyy_recruit_base_extend t4 on t4.id = t2.recruit_base_id "
		+ "join ZYY_RESIDENT_QUALIFICATION t5 on t5.RECRUIT_RESIDENCY_WILL = t2.id " //2013-1-29 xsq 添加 and t5.FINAL_ADMISSION_QUALIFICATION != 1"
		+ "where t2.status <> -1 and t.zyy_user_type=20  and t.zyy_user_status = 1 and t4.year_id = ? ";//t.zyy_user_type = 20 and 2013-1-28 去掉此查询条件 xsq

	//查询正式学员
	final static private String getBaseResidency_SQL = "select distinct t.id, t.real_name, t.zyy_user_type, t1.email,t3.org_name,t3.id orgId,t3.alias_name orgAliasName "
		+ "from zyy_user t "
		+ "join zyy_user_extend t1 on t.id = t1.zyy_user_id "
		+ "join zyy_base_residency t2 on t1.zyy_user_id = t2.residency_id "
		+ "join zyy_org t3 on t2.hospital_id = t3.id "
		+ "where t.zyy_user_status = 1 "; //and t2.recruit_year_id <> ? 
	
	final static private String getDeptSameLevel_SQL = "select distinct t.id, t.real_name, t.zyy_user_type, t1.email,t3.org_name,t3.id orgId,t3.alias_name orgAliasName "
		+ "from zyy_user t "
		+ "join zyy_user_extend t1 on t.id = t1.zyy_user_id "
		+ "join zyy_dept_user t2 on t2.zyy_user_id = t1.zyy_user_id "
		+ "join zyy_org t3 on t3.id = t.zyy_user_org_id "
		+ "join zyy_dept t4 on t4.id = t2.zyy_dept_id "
		+ "where t.zyy_user_status = 1 and t4.status = 1 and t.zyy_user_org_id = ? " ;
	
	final static private String getDeptResidencyUser_SQL = "select distinct t.id, t.real_name, t.zyy_user_type, t1.email,t3.org_name,t3.id orgId,t3.alias_name orgAliasName "
		+ "from zyy_user t "
		+ "join zyy_user_extend t1 on t.id = t1.zyy_user_id "
		+ "join zyy_base_residency t2 on t2.residency_id = t1.zyy_user_id "
		+ "join zyy_org t3 on t3.id = t2.hospital_id "
		+ "join zyy_cycle_table_resi_cycle t4 on t4.residency_id = t2.residency_id "
		+ "where t.zyy_user_status = 1 and t4.status = 1 "
		+ "and t.zyy_user_org_id = ? ";
	
	//查询组织机构
	final static private String getOrg_SQL = "select x.id from zyy_org x where x.org_type_flag = ? and x.status = 1 start with x.id = ? connect by prior x.id = x.parent_org_id" ;
	
	//查询区县机构
	final static private String getAdminOrg_SQL = "select x.id from zyy_org x where x.org_type_flag = ? and x.status = 1 start with x.id = ? connect by prior x.id = x.admin_parent_org_id" ;
	
	/**
	 * 取得卫生厅查询全员用户信息
	 * @param length 用户类别数量
	 * @param type	 1.省厅组织机构 2.医院组织机构 3.大学用户查询
	 * @param orgSQL 查询大学用户下医院用户信息
	 * @return
	 */
	public static String getDepartMentUserSelect(int length, int type, String orgSQL){
		StringBuilder sqlStr = new StringBuilder() ;
		sqlStr.append(getUserExtendVoList_SQL) ;
		sqlStr.append(" where t.zyy_user_status = 1 ") ;
		if(type == 1){
			sqlStr.append(" and t.zyy_user_province_id = ? ") ;
		}else if(type == 2){
			sqlStr.append(" and t.zyy_user_org_id = ? ") ;
		}else if(type == 3){
			sqlStr.append(" and t.zyy_user_org_id in (").append(orgSQL).append(")") ;
		}
		sqlStr.append(" and t.zyy_user_type in (").append(setSelectPlaceholder(length)).append(")") ;
		return sqlStr.toString() ;
	}
	
	/**
	 * 取得同级别用户信息
	 * @return
	 */
	public static String getSameLevelUserSelect(String orgSQL){
		StringBuilder sqlStr = new StringBuilder() ;
		sqlStr.append(getUserExtendVoList_SQL) ;
		sqlStr.append(" where t.zyy_user_status = 1 and t.zyy_user_type = ? ") ;
		sqlStr.append(" and t.zyy_user_org_id in (").append(orgSQL).append(")") ;
		return sqlStr.toString() ;
	}
	
	/**
	 * 查询报名学员
	 * @param length  状态类型数量
	 * @param hospSQL 查询医院SQL
	 * @param baseId  基地ID
	 * @return
	 */
	public static String getRecruitResidencyUserSelect(int length, String hospSQL, Long baseId){
		StringBuilder sqlStr = new StringBuilder() ;
		sqlStr.append(getRecruitResidency_SQL) ;
		sqlStr.append(" and t2.status in (").append(setSelectPlaceholder(length)).append(")") ;
		sqlStr.append(" and t2.hospital_id in (").append(hospSQL).append(")") ;
		if(baseId != null && baseId > 0){
			sqlStr.append(" and t2.base_id = ? ") ;
		}
		return sqlStr.toString() ;   
	}

	//取得下级机构医院信息
	public static String getZyyOrgVoList(ZyyUser zyyUser) {
		if(zyyUser.getZyyUserType() == Constants.USER_TYPE_DEPART_3.intValue()){
			return getAdminOrg_SQL ;
		}else{
			return getOrg_SQL ;
		}
	}
	
	/**
	 * 查询正式学员
	 * @param baseId 基地ID
	 * @return
	 */
	public static String getBaseResidencyUserSelect(String hospSQL, Long baseId){
		StringBuilder sqlStr = new StringBuilder() ;
		sqlStr.append(getBaseResidency_SQL) ;
		sqlStr.append(" and t2.hospital_id in (").append(hospSQL).append(")") ;
		if(baseId != null && baseId > 0){
			sqlStr.append(" and t2.base_id = ? ") ;
		}
		return sqlStr.toString() ;   
	}
	
	/**
	 * 科室同级别用户
	 * @return
	 */
	public static String getDeptSameLevel(int deptLength, int typeLength){
		StringBuilder sqlStr = new StringBuilder() ;
		sqlStr.append(getDeptSameLevel_SQL) ;
		if(deptLength > 0){
			sqlStr.append(" and t4.id in (").append(setSelectPlaceholder(deptLength)).append(")") ;
		}
		sqlStr.append(" and t.zyy_user_type in (").append(setSelectPlaceholder(typeLength)).append(")") ;
		return sqlStr.toString() ;
	}
	
	/**
	 * 取得科室下的正式学员信息
	 * @return
	 */
	public static String getDeptResidencyUser(int deptLength){
		StringBuilder sqlStr = new StringBuilder() ;
		sqlStr.append(getDeptResidencyUser_SQL) ;
		sqlStr.append(" and t4.DEPT_ID in (").append(setSelectPlaceholder(deptLength)).append(")") ;
		return sqlStr.toString() ; 
	}
	
	//设置查询占位符
	public static String setSelectPlaceholder(int length) {
		StringBuilder holderStr = new StringBuilder() ;
		for (int i = 0; i < length; i++) {
			holderStr.append("?") ;
			if(i < length - 1){
				holderStr.append(",") ;
			}
		}
		return holderStr.toString() ;
	}
	
	public static boolean getUnionInfo(boolean flag, StringBuilder sqlStr) {
		if(flag){
			sqlStr.append(" union ") ;
			return flag ;
		}
		flag = true ;
		return flag ;
	}
	
}
