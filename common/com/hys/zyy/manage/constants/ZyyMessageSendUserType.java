package com.hys.zyy.manage.constants;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：陈明凯 2012-4-26
 * 
 * 描述：消息发送用户类别
 * 
 * 说明:
 */
public class ZyyMessageSendUserType implements java.io.Serializable {

	public static Map<Integer, List<ZyyMessageSendUserType>> ZyyMessageSendUserTypeMap = new HashMap<Integer, List<ZyyMessageSendUserType>>();

	static {
		setZyyMessageSendUserTypeMap();
		setZyyMessageSendUserTypeMap2();
		setZyyMessageSendUserTypeMap3();
		setZyyMessageSendUserTypeMap4();
		setZyyMessageSendUserTypeMap5();
		setZyyMessageSendUserTypeMap6();
		setZyyMessageSendUserTypeMap7();

	}

	// 设置卫生厅用户
	private static void setZyyMessageSendUserTypeMap() {

		List<ZyyMessageSendUserType> list = new ArrayList<ZyyMessageSendUserType>();

		ZyyMessageSendUserType z = new ZyyMessageSendUserType();
		z.setUserTypeId(13);
		z.setUserTypeName("卫生局其他用户");
		list.add(z);

		z = new ZyyMessageSendUserType();
		z.setUserTypeId(14);
		z.setUserTypeName("医学院校联系人");
		list.add(z);

		z = new ZyyMessageSendUserType();
		z.setUserTypeId(5);
		z.setUserTypeName("医院联系人");
		list.add(z);

		z = new ZyyMessageSendUserType();
		z.setUserTypeId(7);
		z.setUserTypeName("基地联系人(学科负责人)");
		list.add(z);

		z = new ZyyMessageSendUserType();
		z.setUserTypeId(9);
		z.setUserTypeName("科室联系人");
		list.add(z);

		z = new ZyyMessageSendUserType();
		z.setUserTypeId(11);
		z.setUserTypeName("带教老师");
		list.add(z);

		z = new ZyyMessageSendUserType();
		z.setUserTypeId(20);
		z.setUserTypeName("住院医师");

		List<ZyyResidencyWillStatus> wList = new ArrayList<ZyyResidencyWillStatus>();
		ZyyResidencyWillStatus c = new ZyyResidencyWillStatus();
		c.setWillStatus(1);
		c.setWillStatusName("已报名");
		wList.add(c);
       /**
		c = new ZyyResidencyWillStatus();
		c.setWillStatus(10);
		c.setWillStatusName("已通知笔试");
		wList.add(c);

		c = new ZyyResidencyWillStatus();
		c.setWillStatus(11);
		c.setWillStatusName("已笔试");
		wList.add(c);

		c = new ZyyResidencyWillStatus();
		c.setWillStatus(12);
		c.setWillStatusName("已通知面试");
		wList.add(c);

		c = new ZyyResidencyWillStatus();
		c.setWillStatus(13);
		c.setWillStatusName("已面试");
		wList.add(c);
       **/
		/*c = new ZyyResidencyWillStatus();
		c.setWillStatus(15);
		c.setWillStatusName("拟录取");
		wList.add(c);*/
		
		
		c = new ZyyResidencyWillStatus();
		c.setWillStatus(14);
		c.setWillStatusName("已录取");
		wList.add(c);
		
		
		
		/*c = new ZyyResidencyWillStatus();
		c.setWillStatus(20);
		c.setWillStatusName("未录取");
		wList.add(c);

		c = new ZyyResidencyWillStatus();
		c.setWillStatus(30);
		c.setWillStatusName("已通知签约");
		wList.add(c);

		c = new ZyyResidencyWillStatus();
		c.setWillStatus(31);
		c.setWillStatusName("已签约");
		wList.add(c);

		c = new ZyyResidencyWillStatus();
		c.setWillStatus(32);
		c.setWillStatusName("已毁约");
		wList.add(c);

		c = new ZyyResidencyWillStatus();
		c.setWillStatus(40);
		c.setWillStatusName("已通知报到");
		wList.add(c);

		c = new ZyyResidencyWillStatus();
		c.setWillStatus(41);
		c.setWillStatusName("已报到");
		wList.add(c);*///2013-1-28 注释掉

		c = new ZyyResidencyWillStatus();
		c.setWillStatus(99);
		c.setWillStatusName("正式住院医师");
		wList.add(c);

		z.setWillStatusList(wList);
		list.add(z);

		ZyyMessageSendUserTypeMap.put(2, list);

		ZyyMessageSendUserTypeMap.put(3, list);

		ZyyMessageSendUserTypeMap.put(13, list);

	}

	// 设置大学院校用户
	private static void setZyyMessageSendUserTypeMap2() {

		List<ZyyMessageSendUserType> list = new ArrayList<ZyyMessageSendUserType>();

		ZyyMessageSendUserType z = new ZyyMessageSendUserType();
		z.setUserTypeId(14);
		z.setUserTypeName("医学院校联系人");
		list.add(z);

		z = new ZyyMessageSendUserType();
		z.setUserTypeId(5);
		z.setUserTypeName("医院联系人");
		list.add(z);

		z = new ZyyMessageSendUserType();
		z.setUserTypeId(7);
		z.setUserTypeName("基地联系人(学科负责人)");
		list.add(z);

		z = new ZyyMessageSendUserType();
		z.setUserTypeId(9);
		z.setUserTypeName("科室联系人");
		list.add(z);

		z = new ZyyMessageSendUserType();
		z.setUserTypeId(11);
		z.setUserTypeName("带教老师");
		list.add(z);

		z = new ZyyMessageSendUserType();
		z.setUserTypeId(20);
		z.setUserTypeName("住院医师");

		List<ZyyResidencyWillStatus> wList = new ArrayList<ZyyResidencyWillStatus>();
		ZyyResidencyWillStatus c = new ZyyResidencyWillStatus();
		c.setWillStatus(1);
		c.setWillStatusName("已报名");
		wList.add(c);
		//2013-1-28 根据需求 注释掉下面的 又新添加了 已录取
		c = new ZyyResidencyWillStatus();
		c.setWillStatus(14);
		c.setWillStatusName("已录取");
		wList.add(c);
		/*c = new ZyyResidencyWillStatus();
		c.setWillStatus(10);
		c.setWillStatusName("已通知笔试");
		wList.add(c);

		c = new ZyyResidencyWillStatus();
		c.setWillStatus(11);
		c.setWillStatusName("已笔试");
		wList.add(c);

		c = new ZyyResidencyWillStatus();
		c.setWillStatus(12);
		c.setWillStatusName("已通知面试");
		wList.add(c);

		c = new ZyyResidencyWillStatus();
		c.setWillStatus(13);
		c.setWillStatusName("已面试");
		wList.add(c);
		c = new ZyyResidencyWillStatus();
		c.setWillStatus(20);
		c.setWillStatusName("已淘汰");
		wList.add(c);

		c = new ZyyResidencyWillStatus();
		c.setWillStatus(30);
		c.setWillStatusName("已通知签约");
		wList.add(c);

		c = new ZyyResidencyWillStatus();
		c.setWillStatus(31);
		c.setWillStatusName("已签约");
		wList.add(c);

		c = new ZyyResidencyWillStatus();
		c.setWillStatus(32);
		c.setWillStatusName("已毁约");
		wList.add(c);

		c = new ZyyResidencyWillStatus();
		c.setWillStatus(40);
		c.setWillStatusName("已通知报到");
		wList.add(c);

		c = new ZyyResidencyWillStatus();
		c.setWillStatus(41);
		c.setWillStatusName("已报到");
		wList.add(c);*/

		c = new ZyyResidencyWillStatus();
		c.setWillStatus(99);
		c.setWillStatusName("正式住院医师");
		wList.add(c);

		z.setWillStatusList(wList);
		list.add(z);

		ZyyMessageSendUserTypeMap.put(14, list);

	}

	// 设置医院用户
	private static void setZyyMessageSendUserTypeMap3() {

		List<ZyyMessageSendUserType> list = new ArrayList<ZyyMessageSendUserType>();

		ZyyMessageSendUserType z = null;
		//去掉医院用户给同级别用户发送
//				z.setUserTypeId(5);
//				z.setUserTypeName(source.getMessage("search.label.hospitalcontact", null, null));
//				list.add(z);

		z = new ZyyMessageSendUserType();
		z.setUserTypeId(15);
		z.setUserTypeName("医院管理用户");
		list.add(z);

		z = new ZyyMessageSendUserType();
		z.setUserTypeId(7);
		z.setUserTypeName("基地联系人(学科负责人)");
		list.add(z);

		z = new ZyyMessageSendUserType();
		z.setUserTypeId(9);
		z.setUserTypeName("科室联系人");
		list.add(z);

		z = new ZyyMessageSendUserType();
		z.setUserTypeId(11);
		z.setUserTypeName("带教老师");
		list.add(z);

		z = new ZyyMessageSendUserType();
		z.setUserTypeId(20);
		z.setUserTypeName("住院医师");

		List<ZyyResidencyWillStatus> wList = new ArrayList<ZyyResidencyWillStatus>();
		ZyyResidencyWillStatus c = new ZyyResidencyWillStatus();
		c.setWillStatus(1);
		c.setWillStatusName("已报名");
		wList.add(c);
		/**
		c = new ZyyResidencyWillStatus();
		c.setWillStatus(10);
		c.setWillStatusName("已通知笔试");
		wList.add(c);

		c = new ZyyResidencyWillStatus();
		c.setWillStatus(11);
		c.setWillStatusName("已笔试");
		wList.add(c);

		c = new ZyyResidencyWillStatus();
		c.setWillStatus(12);
		c.setWillStatusName("已通知面试");
		wList.add(c);

		c = new ZyyResidencyWillStatus();
		c.setWillStatus(13);
		c.setWillStatusName("已面试");
		wList.add(c);
		
		c = new ZyyResidencyWillStatus();
		c.setWillStatus(15);
		c.setWillStatusName("拟录取");
		wList.add(c);
		**/
		c = new ZyyResidencyWillStatus();
		c.setWillStatus(14);
		c.setWillStatusName("已录取");
		wList.add(c);

		/*c = new ZyyResidencyWillStatus();
		c.setWillStatus(20);
		c.setWillStatusName("未录取");
		wList.add(c);

		c = new ZyyResidencyWillStatus();
		c.setWillStatus(30);
		c.setWillStatusName("已通知签约");
		wList.add(c);

		c = new ZyyResidencyWillStatus();
		c.setWillStatus(31);
		c.setWillStatusName("已签约");
		wList.add(c);

		c = new ZyyResidencyWillStatus();
		c.setWillStatus(32);
		c.setWillStatusName("已毁约");
		wList.add(c);

		c = new ZyyResidencyWillStatus();
		c.setWillStatus(40);
		c.setWillStatusName("已通知报到");
		wList.add(c);

		c = new ZyyResidencyWillStatus();
		c.setWillStatus(41);
		c.setWillStatusName("已报到");
		wList.add(c);*/

		c = new ZyyResidencyWillStatus();
		c.setWillStatus(99);
		c.setWillStatusName("正式住院医师");
		wList.add(c);

		z.setWillStatusList(wList);
		list.add(z);

		ZyyMessageSendUserTypeMap.put(5, list);
	}

	// 设置医院管理用户
	private static void setZyyMessageSendUserTypeMap6() {

		List<ZyyMessageSendUserType> list = new ArrayList<ZyyMessageSendUserType>();

		ZyyMessageSendUserType z = new ZyyMessageSendUserType();
		z.setUserTypeId(5);
		z.setUserTypeName("医院联系人");
		list.add(z);

		z = new ZyyMessageSendUserType();
		z.setUserTypeId(15);
		z.setUserTypeName("医院管理用户");
		list.add(z);

		z = new ZyyMessageSendUserType();
		z.setUserTypeId(7);
		z.setUserTypeName("基地联系人(学科负责人)");
		list.add(z);

		z = new ZyyMessageSendUserType();
		z.setUserTypeId(9);
		z.setUserTypeName("科室联系人");
		list.add(z);

		z = new ZyyMessageSendUserType();
		z.setUserTypeId(11);
		z.setUserTypeName("带教老师");
		list.add(z);

		z = new ZyyMessageSendUserType();
		z.setUserTypeId(20);
		z.setUserTypeName("住院医师");

		List<ZyyResidencyWillStatus> wList = new ArrayList<ZyyResidencyWillStatus>();
		ZyyResidencyWillStatus c = new ZyyResidencyWillStatus();
		c.setWillStatus(1);
		c.setWillStatusName("已报名");
		wList.add(c);
		
		c = new ZyyResidencyWillStatus();
		c.setWillStatus(14);
		c.setWillStatusName("已录取");
		wList.add(c);
		/*c = new ZyyResidencyWillStatus();
		c.setWillStatus(10);
		c.setWillStatusName("已通知笔试");
		wList.add(c);

		c = new ZyyResidencyWillStatus();
		c.setWillStatus(11);
		c.setWillStatusName("已笔试");
		wList.add(c);

		c = new ZyyResidencyWillStatus();
		c.setWillStatus(12);
		c.setWillStatusName("已通知面试");
		wList.add(c);

		c = new ZyyResidencyWillStatus();
		c.setWillStatus(13);
		c.setWillStatusName("已面试");
		wList.add(c);

		c = new ZyyResidencyWillStatus();
		c.setWillStatus(20);
		c.setWillStatusName("已淘汰");
		wList.add(c);

		c = new ZyyResidencyWillStatus();
		c.setWillStatus(30);
		c.setWillStatusName("已通知签约");
		wList.add(c);

		c = new ZyyResidencyWillStatus();
		c.setWillStatus(31);
		c.setWillStatusName("已签约");
		wList.add(c);

		c = new ZyyResidencyWillStatus();
		c.setWillStatus(32);
		c.setWillStatusName("已毁约");
		wList.add(c);

		c = new ZyyResidencyWillStatus();
		c.setWillStatus(40);
		c.setWillStatusName("已通知报到");
		wList.add(c);

		c = new ZyyResidencyWillStatus();
		c.setWillStatus(41);
		c.setWillStatusName("已报到");
		wList.add(c);*/

		c = new ZyyResidencyWillStatus();
		c.setWillStatus(99);
		c.setWillStatusName("正式住院医师");
		wList.add(c);

		z.setWillStatusList(wList);
		list.add(z);

		ZyyMessageSendUserTypeMap.put(15, list);
	}

	// 设置基地用户
	private static void setZyyMessageSendUserTypeMap4() {

		List<ZyyMessageSendUserType> list = new ArrayList<ZyyMessageSendUserType>();

		ZyyMessageSendUserType z = new ZyyMessageSendUserType();
		z.setUserTypeId(7);
		z.setUserTypeName("基地联系人(学科负责人)");
		list.add(z);

		z = new ZyyMessageSendUserType();
		z.setUserTypeId(9);
		z.setUserTypeName("科室联系人");
		list.add(z);

		z = new ZyyMessageSendUserType();
		z.setUserTypeId(11);
		z.setUserTypeName("带教老师");
		list.add(z);

		z = new ZyyMessageSendUserType();
		z.setUserTypeId(20);
		z.setUserTypeName("住院医师");

		List<ZyyResidencyWillStatus> wList = new ArrayList<ZyyResidencyWillStatus>();
		ZyyResidencyWillStatus c = new ZyyResidencyWillStatus();
		c.setWillStatus(1);
		c.setWillStatusName("已报名");
		wList.add(c);

		c = new ZyyResidencyWillStatus();
		c.setWillStatus(14);
		c.setWillStatusName("已录取");
		wList.add(c);
		
		/*c = new ZyyResidencyWillStatus();
		c.setWillStatus(10);
		c.setWillStatusName("已通知笔试");
		wList.add(c);

		c = new ZyyResidencyWillStatus();
		c.setWillStatus(11);
		c.setWillStatusName("已笔试");
		wList.add(c);

		c = new ZyyResidencyWillStatus();
		c.setWillStatus(12);
		c.setWillStatusName("已通知面试");
		wList.add(c);

		c = new ZyyResidencyWillStatus();
		c.setWillStatus(13);
		c.setWillStatusName("已面试");
		wList.add(c);

		c = new ZyyResidencyWillStatus();
		c.setWillStatus(20);
		c.setWillStatusName("已淘汰");
		wList.add(c);

		c = new ZyyResidencyWillStatus();
		c.setWillStatus(30);
		c.setWillStatusName("已通知签约");
		wList.add(c);

		c = new ZyyResidencyWillStatus();
		c.setWillStatus(31);
		c.setWillStatusName("已签约");
		wList.add(c);

		c = new ZyyResidencyWillStatus();
		c.setWillStatus(32);
		c.setWillStatusName("已毁约");
		wList.add(c);

		c = new ZyyResidencyWillStatus();
		c.setWillStatus(40);
		c.setWillStatusName("已通知报到");
		wList.add(c);

		c = new ZyyResidencyWillStatus();
		c.setWillStatus(41);
		c.setWillStatusName("已报到");
		wList.add(c);*/

		c = new ZyyResidencyWillStatus();
		c.setWillStatus(99);
		c.setWillStatusName("正式住院医师");
		wList.add(c);

		z.setWillStatusList(wList);
		list.add(z);

		ZyyMessageSendUserTypeMap.put(7, list);

	}

	// 设置科室用户
	private static void setZyyMessageSendUserTypeMap5() {

		List<ZyyMessageSendUserType> list = new ArrayList<ZyyMessageSendUserType>();

		ZyyMessageSendUserType z = new ZyyMessageSendUserType();
		z.setUserTypeId(9);
		z.setUserTypeName("科室联系人");
		list.add(z);

		z = new ZyyMessageSendUserType();
		z.setUserTypeId(11);
		z.setUserTypeName("带教老师");
		list.add(z);

		z = new ZyyMessageSendUserType();
		z.setUserTypeId(20);
		z.setUserTypeName("住院医师");

		List<ZyyResidencyWillStatus> wList = new ArrayList<ZyyResidencyWillStatus>();
		ZyyResidencyWillStatus c = new ZyyResidencyWillStatus();
		
		//2013-4-7 需求调整 查询条件变化 xusq
		/*c.setWillStatus(1);
		c.setWillStatusName("已报名");
		wList.add(c);
		
		c = new ZyyResidencyWillStatus();
		c.setWillStatus(14);
		c.setWillStatusName("已录取");
		wList.add(c);
		
		c.setWillStatus(99);
		c.setWillStatusName("正式住院医师");
		wList.add(c);*/
		
		c.setWillStatus(100);
		c.setWillStatusName("轮转结束");
		wList.add(c);
		
		c = new ZyyResidencyWillStatus();
		c.setWillStatus(200);
		c.setWillStatusName("正在轮转");
		wList.add(c);
		
		c = new ZyyResidencyWillStatus();
		c.setWillStatus(300);
		c.setWillStatusName("即将轮转");
		wList.add(c);

		z.setWillStatusList(wList);
		list.add(z);

		ZyyMessageSendUserTypeMap.put(9, list);
	}
	
	// 设置带教用户
	private static void setZyyMessageSendUserTypeMap7() {

		List<ZyyMessageSendUserType> list = new ArrayList<ZyyMessageSendUserType>();

		ZyyMessageSendUserType z = new ZyyMessageSendUserType();
		z.setUserTypeId(20);
		z.setUserTypeName("住院医师");

		List<ZyyResidencyWillStatus> wList = new ArrayList<ZyyResidencyWillStatus>();
		ZyyResidencyWillStatus c = new ZyyResidencyWillStatus();
		
		c.setWillStatus(100);
		c.setWillStatusName("带教结束");
		wList.add(c);
		
		c = new ZyyResidencyWillStatus();
		c.setWillStatus(200);
		c.setWillStatusName("正在带教");
		wList.add(c);
		
		c = new ZyyResidencyWillStatus();
		c.setWillStatus(300);
		c.setWillStatusName("即将带教");
		wList.add(c);

		z.setWillStatusList(wList);
		list.add(z);

		ZyyMessageSendUserTypeMap.put(11, list);
	}

	/**
	 * 
	 */
	private static final long serialVersionUID = 1988146866358678037L;

	/**
	 * 用户类别id
	 */
	private Integer userTypeId;

	/**
	 * 用户类别名称
	 */
	private String userTypeName;

	/**
	 * 住院医师状态列表
	 */
	private List<ZyyResidencyWillStatus> willStatusList;

	public List<ZyyResidencyWillStatus> getWillStatusList() {
		return willStatusList;
	}

	public void setWillStatusList(List<ZyyResidencyWillStatus> willStatusList) {
		this.willStatusList = willStatusList;
	}

	public Integer getUserTypeId() {
		return userTypeId;
	}

	public void setUserTypeId(Integer userTypeId) {
		this.userTypeId = userTypeId;
	}

	public String getUserTypeName() {
		return userTypeName;
	}

	public void setUserTypeName(String userTypeName) {
		this.userTypeName = userTypeName;
	}
}
