package com.hys.zyy.manage.jdbc;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.sql.DataSource;

import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;

/**
 * 数据源工具类
 * <AUTHOR>
 *
 */
public class DataSourceUtils {

	/**
	 * 返回系统中数据源的名字
	 * @return
	 */
	public static String[] getDataSourceNames() {
		WebApplicationContext wac = ContextLoader.getCurrentWebApplicationContext();
		if(wac == null)
			return new String[0];
		
		List<String> dataSourceNames = new ArrayList<String>();
		Map<String, DataSource> map = wac.getBeansOfType(DataSource.class);
		for(Map.Entry<String, DataSource> entry : map.entrySet()) {
			if(entry.getValue() instanceof HGroupDataSource)
				continue;
			dataSourceNames.add(entry.getKey());
		}
		
		return dataSourceNames.toArray(new String[0]);
	}
	
}
