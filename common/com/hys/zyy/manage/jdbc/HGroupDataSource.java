package com.hys.zyy.manage.jdbc;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource;

import com.google.common.collect.Lists;
import com.hys.zyy.manage.service.ApplicationConfigKeys;
import com.hys.zyy.manage.service.impl.ApplicationConfigurationManager;

/**
 * 数据源选择器
 * <AUTHOR>
 *
 */
public class HGroupDataSource extends AbstractRoutingDataSource {
	
	private final static Logger log = LoggerFactory.getLogger(HGroupDataSource.class);
	
	public static List<String> DATASOURCE_NAMES = Collections.EMPTY_LIST;
	
	private static ThreadLocal<String> threadDataSourceName = new ThreadLocal<String>();
	
	private Object lock = new Object();
	
	@Override
	public synchronized void setTargetDataSources(Map<Object, Object> targetDataSources) {
		super.setTargetDataSources(targetDataSources);
		DATASOURCE_NAMES = Lists.newArrayList();
		for(Object dsName : targetDataSources.keySet())
			DATASOURCE_NAMES.add(dsName.toString());
	}
	
	@Override
	protected Object determineCurrentLookupKey() {
		String dsKey = null;
		// 如果当前线程没有预设数据源，则使用域名来确定
		synchronized (lock) {
			dsKey = threadDataSourceName.get();
			if(dsKey == null) {
				if(log.isDebugEnabled())
					log.debug("thread#{} datasource key is null, use domain name!", Thread.currentThread());
				dsKey = ApplicationConfigurationManager.getString(ApplicationConfigKeys.DATASOURCE_KEY);
			}
		}
		
		if(log.isDebugEnabled())
			log.debug("load datasource key is : {}", dsKey);
		
		return dsKey;
		
	}

	public static String setDataSourceName(String dsName) {
		if(log.isDebugEnabled())
			log.debug("set thread#{} datasource key is : {}", Thread.currentThread(), dsName);
		threadDataSourceName.set(dsName);
		return dsName;
	}

	public static void clearDataSourceName() {
		if(log.isDebugEnabled())
			log.debug("clear thread#{} datasource key.", Thread.currentThread());
		threadDataSourceName.remove();
	}

}
