package com.hys.zyy.manage.json;

import com.hys.zyy.manage.model.BdpDictValueVO;

import java.io.Serializable;

/**
 * BDP字典信息返回类封装
 * <AUTHOR>
 */
public class BdpDictInfoResult implements Serializable {

	private static final long serialVersionUID = 1L;
	private boolean success;
	private Integer code;
	private String msg;
	private BdpDictValueVO data;

	public BdpDictInfoResult() {
		super();
	}

	public boolean isSuccess() {
		return success;
	}

	public void setSuccess(boolean success) {
		this.success = success;
	}

	public Integer getCode() {
		return code;
	}

	public void setCode(Integer code) {
		this.code = code;
	}

	public String getMsg() {
		return msg;
	}

	public void setMsg(String msg) {
		this.msg = msg == null ? null : msg.trim();
	}

	public BdpDictValueVO getData() {
		return data;
	}

	public void setData(BdpDictValueVO data) {
		this.data = data;
	}

	public static BdpDictInfoResult success(String msg) {
		BdpDictInfoResult result = new BdpDictInfoResult();
		result.setSuccess(true);
		result.setMsg(msg);
		return result;
	}
	
	public static BdpDictInfoResult failure(String msg) {
		BdpDictInfoResult result = new BdpDictInfoResult();
		result.setSuccess(false);
		result.setMsg(msg);
		return result;
	}

	@Override
	public String toString() {
		return "BdpUserInfoResult [success=" + success + ", code=" + code + ", msg=" + msg + ", data=" + data + "]";
	}

}