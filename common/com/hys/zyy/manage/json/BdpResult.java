package com.hys.zyy.manage.json;

import java.io.Serializable;

/**
 * BDP接口返回类封装
 * <AUTHOR>
 */
public class BdpResult implements Serializable {
	
	private static final long serialVersionUID = 1L;
	private boolean success;
	private Integer code;
	private String msg;
	private Object data;

	public BdpResult() {
		super();
	}

	public boolean isSuccess() {
		return success;
	}

	public void setSuccess(boolean success) {
		this.success = success;
	}

	public Integer getCode() {
		return code;
	}

	public void setCode(Integer code) {
		this.code = code;
	}

	public String getMsg() {
		return msg;
	}

	public void setMsg(String msg) {
		this.msg = msg == null ? null : msg.trim();
	}

	public Object getData() {
		return data;
	}

	public void setData(Object data) {
		this.data = data;
	}
	
	public static BdpResult success(String msg) {
		BdpResult result = new BdpResult();
		result.setSuccess(true);
		result.setMsg(msg);
		return result;
	}
	
	public static BdpResult failure(String msg) {
		BdpResult result = new BdpResult();
		result.setSuccess(false);
		result.setMsg(msg);
		return result;
	}

	@Override
	public String toString() {
		return "BdpResult [success=" + success + ", code=" + code + ", msg=" + msg + ", data=" + data + "]";
	}

}





















