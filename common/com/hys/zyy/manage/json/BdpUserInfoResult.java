package com.hys.zyy.manage.json;

import java.io.Serializable;

import com.hys.zyy.manage.model.BdpUserInfoVO;

/**
 * BDP用户信息返回类封装
 * <AUTHOR>
 */
public class BdpUserInfoResult implements Serializable {

	private static final long serialVersionUID = 1L;
	private boolean success;
	private Integer code;
	private String msg;
	private BdpUserInfoVO data;
	private String dataFailMessage;

	public BdpUserInfoResult() {
		super();
	}

	public boolean isSuccess() {
		return success;
	}

	public void setSuccess(boolean success) {
		this.success = success;
	}

	public Integer getCode() {
		return code;
	}

	public void setCode(Integer code) {
		this.code = code;
	}

	public String getMsg() {
		return msg;
	}

	public void setMsg(String msg) {
		this.msg = msg == null ? null : msg.trim();
	}
	
	public BdpUserInfoVO getData() {
		return data;
	}

	public void setData(BdpUserInfoVO data) {
		this.data = data;
	}

	public String getDataFailMessage() {
		return dataFailMessage;
	}

	public void setDataFailMessage(String dataFailMessage) {
		this.dataFailMessage = dataFailMessage == null ? null : dataFailMessage.trim();
	}

	public static BdpUserInfoResult success(String msg) {
		BdpUserInfoResult result = new BdpUserInfoResult();
		result.setSuccess(true);
		result.setMsg(msg);
		return result;
	}
	
	public static BdpUserInfoResult failure(String msg) {
		BdpUserInfoResult result = new BdpUserInfoResult();
		result.setSuccess(false);
		result.setMsg(msg);
		return result;
	}

	@Override
	public String toString() {
		return "BdpUserInfoResult [success=" + success + ", code=" + code + ", msg=" + msg + ", data=" + data
				+ ", dataFailMessage=" + dataFailMessage + "]";
	}
	
}