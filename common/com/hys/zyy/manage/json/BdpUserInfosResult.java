package com.hys.zyy.manage.json;

import java.io.Serializable;
import java.util.List;

import com.hys.zyy.manage.model.BdpUserInfoVO;

/**
 * BDP用户信息集合返回封装
 * <AUTHOR>
 */
public class BdpUserInfosResult implements Serializable {

	private static final long serialVersionUID = 1L;
	private boolean success;
	private Integer code;
	private String msg;
	private List<BdpUserInfoVO> data;
	private String dataFailMessage;

	public BdpUserInfosResult() {
		super();
	}

	public boolean isSuccess() {
		return success;
	}

	public void setSuccess(boolean success) {
		this.success = success;
	}

	public Integer getCode() {
		return code;
	}

	public void setCode(Integer code) {
		this.code = code;
	}

	public String getMsg() {
		return msg;
	}

	public void setMsg(String msg) {
		this.msg = msg == null ? null : msg.trim();
	}
	
	public List<BdpUserInfoVO> getData() {
		return data;
	}

	public void setData(List<BdpUserInfoVO> data) {
		this.data = data;
	}
	
	public String getDataFailMessage() {
		return dataFailMessage;
	}

	public void setDataFailMessage(String dataFailMessage) {
		this.dataFailMessage = dataFailMessage == null ? null : dataFailMessage.trim();
	}

	public static BdpUserInfosResult success(String msg) {
		BdpUserInfosResult result = new BdpUserInfosResult();
		result.setSuccess(true);
		result.setMsg(msg);
		return result;
	}
	
	public static BdpUserInfosResult failure(String msg) {
		BdpUserInfosResult result = new BdpUserInfosResult();
		result.setSuccess(false);
		result.setMsg(msg);
		return result;
	}

	@Override
	public String toString() {
		return "BdpUserInfosResult [success=" + success + ", code=" + code + ", msg=" + msg + ", data=" + data + "]";
	}
	
}