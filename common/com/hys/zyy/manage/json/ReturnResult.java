package com.hys.zyy.manage.json;

import java.io.Serializable;
/**
 * 封装返回类
 * <AUTHOR>
 * 2018-4-24 下午3:16:42
 */
public class ReturnResult implements Serializable {
	/**
	 *
	 */
	private static final long serialVersionUID = 1L;

	private Integer code;
	private String msg;
	private Object data;

	private static Integer FAIL_CODE = 100;
	private static Integer SUCCESS_CODE = 200;
	private static String FAIL_MSG = "失败";
	private static String SUCCESS_MSG = "成功";


	public static ReturnResult failure(String failMsg) {
		ReturnResult result = new ReturnResult();
		result.setCode(FAIL_CODE);
		result.setMsg(failMsg);
		return result;
	}

	public static ReturnResult failure() {
		ReturnResult result = new ReturnResult();
		result.setCode(FAIL_CODE);
		result.setMsg(FAIL_MSG);
		return result;
	}

	public static ReturnResult success() {
		ReturnResult result = new ReturnResult();
		result.setCode(SUCCESS_CODE);
		result.setMsg(SUCCESS_MSG);
		return result;

	}
	public static ReturnResult success(String msg,Object data) {
		ReturnResult result = new ReturnResult();
		result.setCode(SUCCESS_CODE);
		result.setMsg(msg);
		result.setData(data);
		return result;
	}
	public static ReturnResult success(Object data) {
		ReturnResult result = new ReturnResult();
		result.setCode(SUCCESS_CODE);
		result.setMsg(SUCCESS_MSG);
		result.setData(data);
		return result;
	}

	public static ReturnResult customer(Integer code,String msg,Object data) {
		ReturnResult result = new ReturnResult();
		result.setCode(code);
		result.setMsg(msg);
		result.setData(data);
		return result;

	}

	public Integer getCode() {
		return code;
	}

	public void setCode(Integer code) {
		this.code = code;
	}

	public String getMsg() {
		return msg;
	}
	public void setMsg(String msg) {
		this.msg = msg;
	}
	public Object getData() {
		return data;
	}
	public void setData(Object data) {
		this.data = data;
	}
}
