package com.hys.zyy.manage.json;

import java.io.Serializable;
import java.util.ResourceBundle;
import com.hys.framework.util.StringUtils;
import com.hys.zyy.manage.exception.ErrorCode;

/**
 * 非HTTP状态码的返回结果 
 */
public class ReturnResultModel<T extends Object> implements Serializable {
	
	private static final long serialVersionUID = -7973091610509603958L;

	private static ResourceBundle defaultResourceBundle =ResourceBundle.getBundle("messages");
	
	private static String SUCCESS_CODE = "ok";
	
	private String code;
	
	private String msg;
	
	private T data;
	
	private ReturnResultModel(){}
	
	/**
	 * message异常类定义
	 * @param code=ErrorCode中定义的值
	 */
	public ReturnResultModel(String code){
		this.setCode(code);
		this.setMsg(defaultResourceBundle.getString(code));
	}
	
	/**
	 * 不需要统一的可 自定义错误消息
	 * @param code=ErrorCode中定义的值
	 * @param mes=自定义的错误消息
	 */
	public ReturnResultModel(String code,String mes){
		this.setCode(code);
		this.setMsg(mes);
	}
	public ReturnResultModel(String code,String mes,T data){
		this.setCode(code);
		this.setMsg(mes);
		this.setData(data);
	}
	
	/**
	 * 是否为正确结果
	 * @return
	 */
	public boolean isSuccess(){
		return SUCCESS_CODE.equals(this.getCode());
	}
	
	public static ReturnResultModel<String> failure() {
		return new ReturnResultModel<String>(ErrorCode.E0000);
	}
	public static ReturnResultModel<String> failure(String mes) {
		return new ReturnResultModel<String>(ErrorCode.E0000,mes);
	}
	public static ReturnResultModel<String> failure(Object data) {
		ReturnResultModel resultModel=new ReturnResultModel<String>(ErrorCode.E0000);
		resultModel.setData(data);
		return resultModel;
	}
	public static ReturnResultModel<String> failureOfArgs(Object data) {
		ReturnResultModel model= new ReturnResultModel<String>(ErrorCode.REQUEST_ARGS_ERROR);
		model.setData(data);
		return model;
	}
	
	public static ReturnResultModel<String> success() {
		ReturnResultModel<String> result = new ReturnResultModel<String>();
		result.setCode(SUCCESS_CODE);
		result.setMsg(StringUtils.EMPTY);
		return result;

	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getMsg() {
		return msg;
	}

	public void setMsg(String msg) {
		this.msg = msg;
	}

	public T getData() {
		return data;
	}

	public void setData(T data) {
		this.data = data;
	}
	
}
