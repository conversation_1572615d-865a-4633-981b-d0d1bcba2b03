package com.hys.zyy.manage.json;

import java.io.IOException;
import java.util.Date;

import org.codehaus.jackson.JsonGenerator;
import org.codehaus.jackson.JsonProcessingException;
import org.codehaus.jackson.map.JsonSerializer;
import org.codehaus.jackson.map.SerializerProvider;

import com.hys.zyy.manage.util.DateUtil;

public class SimpleDateSerializer extends JsonSerializer<Date> {

	@Override
	public void serialize(Date input, JsonGenerator gen, SerializerProvider provider)
			throws IOException, JsonProcessingException {
		String formattedDate = DateUtil.format(input, DateUtil.FORMAT_SHORT);
        gen.writeString(formattedDate); 
	}

}
