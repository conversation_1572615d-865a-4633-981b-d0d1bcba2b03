package com.hys.zyy.manage.json;

import java.io.Serializable;

/**
 * 封装返回类
 * <AUTHOR>
 * 2018-4-24 下午3:16:42
 */
public class YunKeTangResult implements Serializable {

	private Integer status;
	private String msg;
	private Object data;

	private static Integer FAIL_CODE = 500;
	private static Integer SUCCESS_CODE = 200;

	public boolean issuccess(){
		return this.status.equals(SUCCESS_CODE);
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public String getMsg() {
		return msg;
	}
	public void setMsg(String msg) {
		this.msg = msg;
	}
	public Object getData() {
		return data;
	}
	public void setData(Object data) {
		this.data = data;
	}
	
	public static YunKeTangResult success(String msg) {
		YunKeTangResult result = new YunKeTangResult();
		result.setStatus(SUCCESS_CODE);
		result.setMsg(msg);
		return result;
	}
	
	public static YunKeTangResult success(String msg, Object data) {
		YunKeTangResult result = new YunKeTangResult();
		result.setData(data);
		result.setStatus(SUCCESS_CODE);
		result.setMsg(msg);
		return result;
	}

	public static YunKeTangResult failure(String failMsg) {
		YunKeTangResult result = new YunKeTangResult();
		result.setStatus(FAIL_CODE);
		result.setMsg(failMsg);
		return result;
	}
	
	public static YunKeTangResult failure(String failMsg, Object data) {
		YunKeTangResult result = new YunKeTangResult();
		result.setData(data);
		result.setStatus(FAIL_CODE);
		result.setMsg(failMsg);
		return result;
	}
}
