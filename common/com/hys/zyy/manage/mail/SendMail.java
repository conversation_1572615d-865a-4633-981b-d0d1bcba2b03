/* ========================================================================
 * 好医生住院医系统: a commercial library for the Java(tm) platform
 * ========================================================================
 *
 * (C) Copyright 2008-2010, by hys Interactive LTD.
 *
 * 产品信息:
 *
 * [Java is a trademark or registered trademark of Sun Microsystems, Inc.
 * in the United States and other countries.]
 *
 * ----------------------------
 * TestMail.java
 * ----------------------------
 * 创建者: Fish
 *
 * ID:TestMail.java v0.1 Jan 17, 2011
 *
 * 变更:
 */

package com.hys.zyy.manage.mail;

import java.io.InputStream;
import java.util.Properties;

import org.apache.log4j.Logger;

import com.hys.zyy.manage.model.zyyBaseEmail;
import com.hys.zyy.manage.service.ZyyBaseEmailService;


/**
 * 类文件说明
 * 
 */
public class SendMail {
	
	Logger log = Logger.getLogger(SendMail.class);
	
	
	private ZyyBaseEmailService zyyBaseEmailService;
	

	public SendMail(ZyyBaseEmailService zyyBaseEmailService) {
		super();
		this.zyyBaseEmailService = zyyBaseEmailService;
	}

	/**
	 * 发送邮件
	 * 
	 * @param toUser
	 *            接收邮件者（多人时，邮件地址以,分隔）
	 * @param subject
	 *            邮件标题
	 * @param content
	 *            邮件内容
	 */
	public void send(String toUser, String subject, String content) {
		try {
			
			/*InputStream inputStream = this.getClass().getClassLoader().getResourceAsStream("mail.properties");
			Properties p = new Properties();
			p.load(inputStream);
			// 这个类主要是设置邮件
			MailSenderInfo mailInfo = new MailSenderInfo();
			mailInfo.setMailServerHost(p.getProperty("MailServerHost"));
			mailInfo.setMailServerPort(p.getProperty("MailServerPort"));
			mailInfo.setUserName(p.getProperty("UserName"));
			mailInfo.setPassword(p.getProperty("Password"));
			mailInfo.setValidate(true);
			mailInfo.setFromAddress(p.getProperty("FromAddress"));*/
			
			//在这里改成通过查询数据库来获取参数
			
			zyyBaseEmail baseEmail = zyyBaseEmailService.getBaseEmail();
			if(baseEmail==null) {
				return ;
			}
			
			MailSenderInfo mailInfo = new MailSenderInfo();
			mailInfo.setMailServerHost(baseEmail.getMailserverhost());
			mailInfo.setMailServerPort(baseEmail.getMailserverport());
			mailInfo.setUserName(baseEmail.getUsername());
			mailInfo.setPassword(baseEmail.getPassword());
			mailInfo.setValidate(true);
			mailInfo.setFromAddress(baseEmail.getFromaddress());
			
			if(null != toUser && !"".equals(toUser)){
				String [] tos = toUser.split(",");
				if(tos.length>1){
					mailInfo.setReceivers(tos);
				}else {
					mailInfo.setToAddress(toUser);
				}
			}
			mailInfo.setSubject(subject);
			mailInfo.setContent(content);
			// 这个类主要来发送邮件
			com.hys.zyy.manage.mail.SimpleMailSender sms = new com.hys.zyy.manage.mail.SimpleMailSender();
			sms.sendHtmlMail(mailInfo);// 发送文体格式
			log.error(String.format("send mail %s",mailInfo.getToAddress()));
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public static void main(String[] args) {
		//SendMail mail = new SendMail();
		//mail.send("<EMAIL>", "邮件测试", "测试是时尚！！！");
	}

}
