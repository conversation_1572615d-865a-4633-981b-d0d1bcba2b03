package com.hys.zyy.manage.mail;

import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.util.Properties;

import javax.mail.Address;
import javax.mail.BodyPart;
import javax.mail.Message;
import javax.mail.MessagingException;
import javax.mail.Multipart;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;
import javax.mail.internet.MimeUtility;



public class SimpleMailSender {
	public boolean sendTextMail(MailSenderInfo mailInfo) {
		MyAuthenticator authenticator = null;
		Properties pro = mailInfo.getProperties();
		if (mailInfo.isValidate()) {
			authenticator = new MyAuthenticator(mailInfo.getUserName(),
					mailInfo.getPassword());
		}

		//Session sendMailSession = Session.getDefaultInstance(pro, authenticator);
		Session sendMailSession = Session.getInstance(pro, authenticator);
		
		try {
			MimeMessage mailMessage = new MimeMessage(sendMailSession);

			Address from = new InternetAddress(mailInfo.getFromAddress());

			mailMessage.setFrom(from);

			mailMessage.setRecipients(Message.RecipientType.TO, InternetAddress
					.parse(mailInfo.getToAddress()));

			mailMessage.setSubject(mailInfo.getSubject(),"GB2312");

			mailMessage.setSentDate(new Date());

			String mailContent = mailInfo.getContent();
			mailMessage.setText(mailContent, "GB2312");

			Transport.send(mailMessage);
			return true;
		} catch (MessagingException ex) {
			ex.printStackTrace();
		}
		return false;
	}

	public static boolean sendHtmlMail(MailSenderInfo mailInfo) {
		MyAuthenticator authenticator = null;
		Properties pro = mailInfo.getProperties();

		if (mailInfo.isValidate()) {
			authenticator = new MyAuthenticator(mailInfo.getUserName(),
					mailInfo.getPassword());
		}

		//Session sendMailSession = Session.getDefaultInstance(pro, authenticator);
		Session sendMailSession = Session.getInstance(pro, authenticator);
		
		try {
			Message mailMessage = new MimeMessage(sendMailSession);
			try {
				
				Address from = new InternetAddress(MimeUtility.encodeText("住院医规范化培训系统","gb2312","B") + "<" + mailInfo.getFromAddress() + ">");

				mailMessage.setFrom(from);
	
				Address to = new InternetAddress(mailInfo.getToAddress());
	
				mailMessage.setRecipient(Message.RecipientType.TO, to);

				mailMessage.setSubject(MimeUtility.encodeText(mailInfo.getSubject().toString(),"gb2312","B"));
			} catch (UnsupportedEncodingException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}

			mailMessage.setSentDate(new Date());

			Multipart mainPart = new MimeMultipart();

			BodyPart html = new MimeBodyPart();

			html.setContent(mailInfo.getContent(), "text/html; charset=utf-8");
			mainPart.addBodyPart(html);

			mailMessage.setContent(mainPart);

			Transport.send(mailMessage);
			/*Transport transport=sendMailSession.getTransport("smtp");
			transport.connect(mailInfo.getUserName(),mailInfo.getPassword());
			Address[] address={to};
			transport.sendMessage(mailMessage,address);*/
			
			return true;
		} catch (MessagingException ex) {
			ex.printStackTrace();
		}
		return false;
	}
}