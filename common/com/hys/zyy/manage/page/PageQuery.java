package com.hys.zyy.manage.page;


/**
 * 分页查询对象
 */
public class PageQuery implements java.io.Serializable {
	private static final long serialVersionUID = -8000900575354501298L;

	public static final int DEFAULT_PAGE_SIZE = 10;
	/** 页数  */
	private Integer pageNumber = 1;
	/** 分页大小 */
	private Integer pageSize = DEFAULT_PAGE_SIZE;

	public PageQuery() {
	}

	public PageQuery(Integer pageSize) {
		this.pageSize = pageSize;
	}

	public PageQuery(PageQuery query) {
		this.pageNumber = query.pageNumber;
		this.pageSize = query.pageSize;
	}

	public PageQuery(int page, int pageSize) {
		this.pageNumber = page;
		this.pageSize = pageSize;
	}

	public int getPageSize() {
		return pageSize;
	}

	public void setPageSize(Integer pageSize) {
		this.pageSize = pageSize;
	}

	public String toString() {
		return "page:" + pageNumber + ",pageSize:" + pageSize;
	}

	public Integer getPageNumber() {
		return pageNumber;
	}

	public void setPageNumber(Integer pageIndex) {
		this.pageNumber = pageIndex;
	}

}
