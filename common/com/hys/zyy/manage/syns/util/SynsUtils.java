package com.hys.zyy.manage.syns.util;

import java.awt.Image;
import java.awt.image.BufferedImage;
import java.io.BufferedReader;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.UUID;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import javax.imageio.ImageIO;

import org.apache.commons.codec.binary.Base64;

import com.hys.zyy.manage.model.Message;
import com.sun.image.codec.jpeg.JPEGCodec;
import com.sun.image.codec.jpeg.JPEGImageEncoder;

import net.sf.json.JSONObject;
import sun.misc.BASE64Encoder;

public class SynsUtils {
	
	private static String url = "https://www.ynrct.haoyisheng.com/rct";

	public static Message verifyToken(String token){
		String urlString="https://www.ynrct.haoyisheng.com/authenticate/Auth/AuthToken?token=%s";
		Message result = null;
		try {
			String resultString="";
			URL url = new URL(String.format(urlString, token));
			HttpURLConnection connection =(HttpURLConnection) url.openConnection();
	    	connection.connect();
		   	BufferedReader reader =new BufferedReader(new InputStreamReader(connection.getInputStream(),"utf-8"));
		   	String lines;
		   	while((lines= reader.readLine())!=null){
		   		resultString += lines;  
		   	}
		   	reader.close();
		   	connection.disconnect(); 
		   	
		   	JSONObject jsonObject=JSONObject.fromObject(resultString);
		   	result = (Message)JSONObject.toBean(jsonObject, Message.class);
		   	return result;
		} catch (Exception e) {
			e.printStackTrace();
			result = new Message();
			result.setErrcode("-1");
			result.setErrmsg("系统繁忙");
			return result;
		} 
	}
	
	public static String aesEncrypt(String str, String key)
		    throws Exception{
	    if ((str == null) || (key == null) || (key.length() != 16)) {
	      return null;
	    }
	    Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
	    cipher.init(1, new SecretKeySpec(key.getBytes("utf-8"), "AES"));
	    byte[] bytes = cipher.doFinal(str.getBytes("utf-8"));
	    return new Base64().encodeToString(bytes);
	}
	
	
	//1性别转换	
	public static String getSex(String code){
		String c="9";
		if(code.equals("1")){
			c= "1";
		}else if(code.equals("2")){
			c= "2";
		}else{
			c= "9";
		}
		return c;
	}
	
	//2民族转换
	public static String getNation(String nation){
		String c="97";
		if(nation.indexOf("汉")>0){
			c="1";
		}
		else if(nation.indexOf("蒙")>0){
			c="2";
		}
		else if(nation.indexOf("回")>0){
			c="3";
		}
		else if(nation.indexOf("藏")>0){
			c="4";
		}
		else if(nation.indexOf("维")>0){
			c="5";
		}
		else if(nation.indexOf("苗")>0){
			c="6";
		} 
		else if(nation.indexOf("彝")>0){
			c="7";
		} 
		else if(nation.indexOf("壮")>0){
			c="8";
		}
		else if(nation.indexOf("布依")>0){
			c="9";
		}
		else if(nation.indexOf("朝")>0){
			c="10";
		}
		else if(nation.indexOf("满")>0){
			c="11";
		}
		else if(nation.indexOf("侗")>0){
			c="12";
		}
		else if(nation.indexOf("瑶")>0){
			c="13";
		}
		else if(nation.indexOf("白")>0){
			c="14";
		}
		else if(nation.indexOf("土家")>0){
			c="15";
		}
		else if(nation.indexOf("哈尼")>0){
			c="16";
		}
		else if(nation.indexOf("哈萨")>0){
			c="17";
		}
		else if(nation.indexOf("傣")>0){
			c="18";
		}
		else if(nation.indexOf("黎")>0){
			c="19";
		}
		else if(nation.indexOf("傈")>0){
			c="20";
		}
		else if(nation.indexOf("佤")>0){
			c="21";
		}
		else if(nation.indexOf("畲")>0){
			c="22";
		}
		else if(nation.indexOf("高山")>0){
			c="23";
		}
		else if(nation.indexOf("拉祜")>0){
			c="24";
		}
		else if(nation.indexOf("水")>0){
			c="25";
		}
		else if(nation.indexOf("东乡")>0){
			c="26";
		}
		else if(nation.indexOf("纳西")>0){
			c="27";
		}
		else if(nation.indexOf("景颇")>0){
			c="28";
		}
		else if(nation.indexOf("柯尔克孜")>0){
			c="29";
		}
		else if(nation.indexOf("土")>0){
			c="30";
		}
		else if(nation.indexOf("达斡尔")>0){
			c="31";
		}
		else if(nation.indexOf("仫佬")>0){
			c="32";
		}
		else if(nation.indexOf("羌")>0){
			c="33";
		}
		else if(nation.indexOf("布朗")>0){
			c="34";
		}
		else if(nation.indexOf("撒拉")>0){
			c="35";
		}
		else if(nation.indexOf("毛难")>0){
			c="36";
		}
		else if(nation.indexOf("仡佬")>0){
			c="37";
		}
		else if(nation.indexOf("锡伯")>0){
			c="38";
		}
		else if(nation.indexOf("阿昌")>0){
			c="39";
		}
		else if(nation.indexOf("普米")>0){
			c="40";
		}
		else if(nation.indexOf("塔吉克")>0){
			c="41";
		}
		else if(nation.indexOf("怒")>0){
			c="42";
		}
		else if(nation.indexOf("乌孜别克")>0){
			c="43";
		}
		else if(nation.indexOf("俄")>0){
			c="44";
		}
		else if(nation.indexOf("鄂温克")>0){
			c="45";
		}
		else if(nation.indexOf("德昂")>0){
			c="46";
		}
		else if(nation.indexOf("保安")>0){
			c="47";
		}
		else if(nation.indexOf("裕固")>0){
			c="48";
		}
		else if(nation.indexOf("京")>0){
			c="49";
		}
		else if(nation.indexOf("塔塔尔")>0){
			c="50";
		}
		else if(nation.indexOf("独龙")>0){
			c="51";
		}
		else if(nation.indexOf("鄂伦春")>0){
			c="52";
		}
		else if(nation.indexOf("赫哲")>0){
			c="53";
		}
		else if(nation.indexOf("门巴")>0){
			c="54";
		}
		else if(nation.indexOf("珞巴")>0){
			c="55";
		}
		else if(nation.indexOf("基诺")>0){
			c="56";
		}
		else if(nation.indexOf("穿青人")>0){
			c="81";
		}
		else if(nation.indexOf("外国")>0){
			c="98";
		}
		else{
			c="97";
		}
		return c;
	}
	
	//3证件类型转换
	public static String getCertificateType(String code){
		String c="99";
		if(code.equals("1")){
			c= "01";
		}else if(code.equals("2")){
			c= "04";
		}else if(code.equals("3")){
			c= "03";
		}else if(code.equals("4")){
			c="06";;
		}else if(code.equals("5")){
			c= "07";
		}else{
			c= "99";
		}
		return c;
	}
    
	//4 英语等级转换
	public static String getForeignLanguage(String language){
		  String c="1";
		  //优先匹配专业英语
		  if( (language.indexOf("专业")>=0 || language.indexOf("TEM")>=0 || language.indexOf("tem")>=0) && ( language.indexOf("8")>=0 || language.indexOf("八")>=0 || language.indexOf("4")>=0 || language.indexOf("四")>=0) ){
			  if( language.indexOf("8")>=0 || language.indexOf("八")>=0){
				  c="5";
			  }
			  else{
				  c="4";
			  }
		  }
		  //再匹配公共英语
		  else if( (language.indexOf("公共")>=0 || language.indexOf("PETS")>=0 || language.indexOf("pets")>=0) 
				  && ( language.indexOf("1")>=0 || language.indexOf("一")>=0 || language.indexOf("2")>=0 || language.indexOf("二")>=0 ||
					   language.indexOf("3")>=0 || language.indexOf("三")>=0 || language.indexOf("4")>=0 || language.indexOf("四")>=0 ||
						  language.indexOf("5")>=0 || language.indexOf("五")>=0 ) ){
			  if( language.indexOf("5")>=0 || language.indexOf("五")>=0){
				  c="10";
			  }
			  else if( language.indexOf("4")>=0 || language.indexOf("四")>=0){
				  c="9";
			  }
			  else if( language.indexOf("3")>=0 || language.indexOf("三")>=0){
				  c="8";
			  }
			  else if( language.indexOf("2")>=0 || language.indexOf("二")>=0){
				  c="7";
			  }
			  else{
				  c="6";
			  }
		  }
		  
		  
		  if( c.equals(1) || c.equals(6) ||  c.equals(7) ||  c.equals(8) ){
			  if( (language.indexOf("CET")>0 || language.indexOf("cet")>0) && (language.indexOf("4")>=0 || language.indexOf("四")>=0 || language.indexOf("6")>=0 || language.indexOf("六")>=0)) {
				  if( language.indexOf("6")>=0 || language.indexOf("6")>=0){
					  c="3";
				  }
				  else{
					  c="2";
				  }
			  }
		  }
		  return c;
	  }

	
	//将图片转为BASE64字符串
	/*public static String getImageBase64Str(String imagePath){
		if(imagePath == null || "".equals(imagePath)){
			return "";
		}
		
		InputStream in = null;
		byte[] data = null;
		// 读取图片字节数组
		try {
			in = new FileInputStream(imagePath);
			data = new byte[in.available()];
			in.read(data);
			in.close();
		} catch (IOException e) {
			e.printStackTrace();
			return "";
		}
		
		BASE64Encoder encoder = new BASE64Encoder();
		return encoder.encode(data);
	}*/
	
	/**
	 * 
	 * @param imagePath 原图路径
	 * @param width 宽度
	 * @param height 高度
	 * @return 图片转为BASE64字符串
	 */
	public static String getImageBase64Str(String imagePath, int width, int height){
		if(null == imagePath || imagePath.length() < 1)	return "";		
		if(width <= 0 && height <= 0) return "";
		File srcFile =  new File(imagePath);
		if(!srcFile.exists()) return "";
		
		try {
			Image src = ImageIO.read(srcFile);  
            BufferedImage _image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);  
            _image.getGraphics().drawImage(src.getScaledInstance(width, height, Image.SCALE_SMOOTH), 0, 0, null);
            //String imageDist = imagePath.substring(0, imagePath.lastIndexOf(File.separator) +1) + UUID.randomUUID().toString().replaceAll("-", "").toUpperCase() + ".jpeg";
			//FileOutputStream out = new FileOutputStream(imageDist);
			ByteArrayOutputStream out = new ByteArrayOutputStream(); 
	        //JPEGImageEncoder encoder = JPEGCodec.createJPEGEncoder(out);  
	        //encoder.encode(_image);  
			
			ImageIO.write(_image, "jpg", out);
			byte[] data = out.toByteArray();
			// 读取图片字节数组
			out.write(data);
			BASE64Encoder base64Encoder = new BASE64Encoder(); 
	        out.close();  
	        return base64Encoder.encode(data);
		} catch (IOException e) {
			e.printStackTrace();
			return "";
		}
	}
	
	public static int getForeignLanguageTestType(int languageTestType){
		if(languageTestType == 4){
			return 5;
		}else{
			return languageTestType;
		}
	}
	
	public static String getDegreeType(Integer degreeType){
		if(degreeType == null || degreeType == -1){
			return "3";
		}
		if(degreeType == 1 || degreeType == 2){
			return degreeType.toString();
		}else{
			return "3";
		}
	}
	
	public static String getHasCertificate(Integer hasCertificate){
		if(hasCertificate == null){
			return "2";
		}
		if(hasCertificate == 0){
			return "2";
		}else if (hasCertificate == 1){
			return "1";
		}else {
			return "2";
		}
	}
	
	public static String getDegree(Integer degree){
		if(degree == null){
			return "0";
		}
		if(degree == 1){
			return "4";
		}else if(degree == 2){
			return "3";
		}else if(degree == 3){
			return "2";
		}else {
			return "0";
		}
	}
	
	public static String isReading(Integer residencySource, Integer degree_convergence){
	
		//修改判断逻辑，单位人residencySource=1   residencySource =2 社会人  residencySource = 3 学位衔接
		if(residencySource == 1 ){
			return "1";
		}if(residencySource == 3 ){// 学位衔接
			return "1";
		}else{
			return "2";
		}
		
	}
	
	
	public static String getEducation(Integer degree){
		if(degree == null ){
			return "2";
		}
		if(degree == 1){
			return "3";
		}else if(degree == 2){
			return "2";
		}else{
			return "1";
		}
	}
	
	
	public static String getPhysiciansQualificationLevel(Integer level){
		if(level == null){
			return "2";
		}
		if(level == 2){
			return "1";
		}else if(level == 3){
			return "2";
		}else{
			return "2";
		}
	}
	
	public static String getExamResult(Integer result) {
		if(result == null){
			return "";
		}else if(result == 1){
			return "1";
		}else if(result == 2){
			return "2";
		}else{
			return "";
		}
	}
	
	public static void main(String[] args) {
		//System.out.println(resizeImage("D:\\1.jpeg", 295, 413));
	}
	
}
