package com.hys.zyy.manage.util;

import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;

import org.springframework.util.StopWatch;

import com.hys.zyy.manage.model.ZyyRecruitStageVO;
import com.hys.zyy.manage.util.annotation.Column;
import com.hys.zyy.manage.util.annotation.Id;

public class AnnotationUtils {
	
	// 性能不好可以针对性缓存
	public static List<Field> findFields(Class<?> clazz, Class<? extends Annotation>... annotationTypes) {
		List<Field> list = findFields(clazz);
		if(list != null)	{
			for(Iterator<Field> it = list.iterator(); it.hasNext();) {
				boolean mark = true;
				Field field = it.next();
				if(annotationTypes != null)
					for(Class<? extends Annotation> annotation : annotationTypes)
						mark &= field.isAnnotationPresent(annotation);
				if(!mark)
					it.remove();
			}
		}
		return list;
	}
	
	public static List<Field> findFields(Class<?> clazz) {
		List<Field> fields = new ArrayList<Field>(Arrays.asList(clazz.getDeclaredFields()));
		
		while(!(clazz = clazz.getSuperclass()).getName().equals("java.lang.Object"))
			fields.addAll(Arrays.asList(clazz.getDeclaredFields()));
			
		return fields;
	}
	
	public static String[] findFieldName(Class<?> clazz, Class<? extends Annotation> annotationTypes) {
		List<Field> fields = findFields(clazz, annotationTypes);
		String[] values = new String[fields.size()];
		for(int i = 0; i < fields.size(); i++) {
			Field field = fields.get(i);
			values[i] = field.getName();
		}
		return values;
	}
	
	public static Field findUniqueField(Class<?> clazz, Class<? extends Annotation> annotationTypes) {
		List<Field> list = findFields(clazz, annotationTypes);
		if(list.isEmpty())
			return null;
		return list.get(0);
	}
	
	public static String findUniqueFieldName(Class<?> clazz, Class<? extends Annotation> annotationTypes) {
		String[] fieldNames = findFieldName(clazz, annotationTypes);
		if(fieldNames.length > 0)
			return fieldNames[0];
		return null;
	}
	
	public static Object getValue(Class<?> clazz, Class<? extends Annotation> annotationType, String attributeName) {
		Annotation annotation = findAnnotation(clazz, annotationType);
		if(annotation != null)
			if(attributeName == null)
				return org.springframework.core.annotation.AnnotationUtils.getValue(annotation);
			else
				return org.springframework.core.annotation.AnnotationUtils.getValue(annotation, attributeName);
		return null;
	}
	
	public static Object getValue(Field field, Class<? extends Annotation> annotationType, String attributeName) {
		if(field == null)
			return null;
		Annotation annotation = field.getAnnotation(annotationType);
		if(annotation == null)
			return null;
		if(attributeName == null)
			attributeName = "value";
		
		return org.springframework.core.annotation.AnnotationUtils.getValue(annotation, attributeName);
	}
	
	public static <A extends Annotation> A findAnnotation(Class<?> clazz, Class<A> annotationType) {
		return org.springframework.core.annotation.AnnotationUtils.findAnnotation(clazz, annotationType);
	}
	
	public static void main(String[] args) {
		StopWatch sw = new StopWatch();
		sw.start();
		for(int i = 0; i < 10000; i++) {
			AnnotationUtils.findFields(ZyyRecruitStageVO.class, Id.class, Column.class);
		}
		sw.stop();
		System.out.println(sw.getTotalTimeMillis());
	}
	
}
