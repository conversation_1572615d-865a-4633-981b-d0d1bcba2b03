package com.hys.zyy.manage.util;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.hys.framework.utils.SpringContextUtils;
import com.hys.framework.utils.cache.redis.springTemplate.RedisTemplateUtil;
import com.hys.framework.utils.httpclient.HttpClientUtil;
import com.hys.zyy.manage.constants.Constants;
import com.hys.zyy.manage.constants.RedisConstants;
import com.hys.zyy.manage.facade.ZyyUserExtendEnlargeFacade;
import com.hys.zyy.manage.util.redis.RedisClientTemplate;

public class AppTokenUtil {
	
	private static RedisClientTemplate redisClientTemplate;
	//60分钟
	private static int EXPIRE_TIME = 60*60;
	
	private static String getToken() {
    	try {
			Map<String, String> params = new HashMap<String, String>();//请求参数集合
			Map<String, String> map = ConfigPropertiesUtil.getInstance().getPropMap();
			String appKey = map.get(RedisConstants.APP_KEY);
			String appTokenUrl = map.get(RedisConstants.APP_TOKEN_URL);
			params.put("appKey", appKey);
			String result = HttpClientUtil.sendHttpRequestForMsg(appTokenUrl, HttpClientUtil.httpRequestMethod.POST, params);
			if (StringUtils.isNotBlank(result)){
				JSONObject resultJson = JSON.parseObject(result);
				if (resultJson != null){
					String code = resultJson.getString("code");
					if (StringUtils.isNotBlank(code) && Constants.SUCCESS_CODE.equals(code)){
						String  token = resultJson.getString("result");
						return token;
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}
    
	/**
	 * 获取token
	 * 根据各个业务平台在网关注册所的到的appKey获取token。Token两个小时过期失效，每一次业务系统的主动获取都将刷新该业务系统对应的token的值。
	 * @return
	 * <AUTHOR>
	 * @date 2017年8月11日 下午1:55:08
	 */
    public static String getAppToken() {
        String accessToken = null;
        try {
        	Map<String, String> map = ConfigPropertiesUtil.getInstance().getPropMap();
			String appKey = map.get(RedisConstants.APP_KEY);
        	String redisKey = RedisConstants.RCT_APP_TOKEN_2020+appKey;
        	if (redisClientTemplate == null){
				WebApplicationContext wac = ContextLoader.getCurrentWebApplicationContext();
				redisClientTemplate = wac.getBean(RedisClientTemplate.class);
			}
        	accessToken = redisClientTemplate.get(redisKey);
            if (null == accessToken) {
            	accessToken = getToken();
            	redisClientTemplate.set(redisKey, accessToken);
            	redisClientTemplate.expire(redisKey, EXPIRE_TIME);
            }
        } catch (Exception e) {
        	e.printStackTrace();
        }
        return accessToken;
    }
   
}
