package com.hys.zyy.manage.util;

import java.util.List;

public class ArrayUtils extends org.apache.commons.lang.ArrayUtils {
	
    public static <T extends Object> boolean isNullOrEmpty(T[] array) {
        return (array == null || array.length == 0);
    }

    public static String[] trim(String[] array) {
        if (array == null) {
            return null;
        }
        for (int i = 0; i < array.length; i++) {
            array[i] = array[i].trim();
        }
        return array;
    }

    public static String[] asArray(List<String> list) {
        if (list == null) return null;
        return list.toArray(new String[0]);
    }
    
	public static Integer[] toInteger(String[] array) {
		if (array == null || array.length == 0)
			return new Integer[0];
		Integer[] value = new Integer[array.length];
		for (int i = 0; i < array.length; i++)
			value[i] = StringUtils.getIntegerParam(array[i], 0);
		return value;
	}
    
    public static Long[] toLong(String[] array) {
		if(array == null || array.length == 0)
			return new Long[0];
		Long[] value = new Long[array.length];
		for(int i = 0; i < array.length; i++)
			value[i] = StringUtils.getLongParam(array[i], 0l);
		return value;
	}
    
    public static boolean isSameLength(Object[]... arrays) {
    	
    	if(arrays == null || arrays.length <= 1)
    		return false;
    	
    	if(contains(arrays, null))
    		return false;
    	
    	for(int i = 0; i < arrays.length - 1; i++) {
    		for(int j = 1; j < arrays.length; j++) {
    			if(arrays[i].length != arrays[j].length)
    				return false;
    		}
    	}
    	return true;
    }
    
    //判断数组，是否包含指定元素
    public static boolean useLoop(String[] arr, String targetValue) {
    	if (arr == null || StringUtils.isBlank(targetValue)){
    		return false;
    	}
        for(String s: arr){
            if(targetValue.equals(s)){
            	return true;
            }
        }
        return false;
    }
    
}
