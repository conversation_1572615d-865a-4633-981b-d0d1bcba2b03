package com.hys.zyy.manage.util;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.apache.log4j.LogManager;
import org.apache.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.hys.zyy.manage.constants.Constants;
import com.hys.zyy.manage.json.BdpDictInfoResult;
import com.hys.zyy.manage.json.BdpResult;
import com.hys.zyy.manage.json.BdpUserInfoResult;
import com.hys.zyy.manage.json.BdpUserInfosResult;
import com.hys.zyy.manage.model.BdpUserInfoVO;
import com.hys.zyy.manage.model.ZyyResidencyImportVO;
import com.hys.zyy.manage.model.ZyyUserExtendVO;
import com.hys.zyy.manage.service.ZyyBaseManage;
import com.hys.zyy.manage.service.ZyyDictionaryDetailManage;
import com.hys.zyy.manage.service.ZyyOrgManage;
import com.hys.zyy.manage.service.ZyyTeacherTitleManage;

/**
 * 基础数据平台工具类
 */
@Component
public class BDPUtil {

	private static Logger logger = LogManager.getLogger(BDPUtil.class.getName());
	
	private static final String EXISTS = "exists", FAIL = "fail", SUCCESS = "success";

	@Autowired
	private ZyyOrgManage zyyOrgManage;
	@Autowired
	private ZyyBaseManage zyyBaseManage;
	@Autowired
	private ZyyTeacherTitleManage zyyTeacherTitleManage;
	@Autowired
	private ZyyDictionaryDetailManage zyyDictionaryDetailManage;

	/**
	 * 住院医用户转换为BDP用户
	 */
	public BdpUserInfoVO transferZyyUserExtend(ZyyUserExtendVO record, Integer zyyUserType) {
		BdpUserInfoVO bdpUser = new BdpUserInfoVO();
		if (record == null)
			return bdpUser;
		zyyUserType = zyyUserType == null ? -1 : zyyUserType;
		/*
		 * 通用字段处理
		 */
		// BDP用户ID
		String bdpUserId = record.getBdpUserId();
		if (StringUtils.isNotBlank(bdpUserId))
			bdpUser.setUserId(bdpUserId);
		// 注册来源
		bdpUser.setRegistOrigin(Constants.REGIST_ORIGIN_ZYY);
		// 用户类型：0普通用户1实名认证用户2专家
		bdpUser.setUserType(0);
		// 用户状态1正常；0锁定；-1删除
		bdpUser.setUserStatus(1);
		// 用户名
		bdpUser.setUserAccount(record.getAccountName());
		// 密码
		bdpUser.setUserPassword(record.getAccountPassword());
		// 姓名
		bdpUser.setRealName(record.getRealName());
		// 性别（1=男；2=女）
		bdpUser.setSex(record.getSex());
		// 出生日期
		Date birthday = record.getBirthday();
		if (birthday != null)
			bdpUser.setBirthday(DateUtil.format(birthday, DateUtil.FORMAT_SHORT));
		// 证件类型
		Integer certificateType = record.getCertificateType();
		if (certificateType != null)
			bdpUser.setCertificateType(zyyDictionaryDetailManage.find(2L, certificateType.toString()));
		// 证件号码
		if (StringUtils.isNotEmpty(record.getCertificateNo())){
			bdpUser.setCertificateNo(AESUtil256.encrypt(record.getCertificateNo(), Constants.AES256_KEY));
		}
		// 手机号码
		if (StringUtils.isNotEmpty(record.getMobilNumber()))
			bdpUser.setMobileNumber(AESUtil256.encrypt(record.getMobilNumber(), Constants.AES256_KEY));
		// 邮箱
		bdpUser.setEmail(record.getEmail());
		// 户口所在地省市县
		/*Long regLocProvinceId = record.getRegLocProvinceId();
		Long regLocCityId = record.getRegLocCityId();
		Long regLocCountyId = record.getRegLocCountyId();
		bdpUser.setWorkProvince(regLocProvinceId == null ? null : regLocProvinceId.toString());
		bdpUser.setWorkCity(regLocCityId == null ? null : regLocCityId.toString());
		bdpUser.setWorkCounties(regLocCountyId == null ? null : regLocCountyId.toString());*/
		// 工作单位通讯地址
		bdpUser.setAddress(record.getAddress());
		// 邮编
		bdpUser.setPostCode(record.getAddressPostCode());
		// 最高学历
		Integer highestRecordSchool = record.getHighestRecordSchool();
		if (highestRecordSchool != null)
			bdpUser.setHighestRecordSchool(zyyDictionaryDetailManage.find(3L, highestRecordSchool.toString()));
		// 最高学位
		Integer highestDegree = record.getHighestDegree();
		if (highestDegree != null)
			bdpUser.setHighestDegree(zyyDictionaryDetailManage.find(4L, highestDegree.toString()));
		// 所属机构
		Long zyyUserOrgId = record.getZyyUserOrgId();
		if (zyyUserOrgId != null)
			bdpUser.setHospital(zyyOrgManage.getBdpOrgIdById(zyyUserOrgId));
		/*
		 * 个性化字段处理
		 */
		switch (zyyUserType) {
			case 11: // 带教老师
			case 17: // 责任导师
				// 职称
				Integer userPostTitle = record.getUserPostTitle();
				if (userPostTitle != null)
					bdpUser.setTitle(zyyTeacherTitleManage.getBdpDicCodeById(Long.valueOf(userPostTitle)));
				break;
			case 21: // 正式学员用户
				// 专业基地
				/*Long baseId = record.getBaseId();
				if (baseId != null)
					bdpUser.setSpecialtyId(zyyBaseManage.getBdpDicCodeByBaseId(baseId));*/
				break;
		}
		return bdpUser;
	}

	public List<BdpUserInfoVO> transferZyyUserExtend(List<ZyyUserExtendVO> records, Integer zyyUserType) {
		List<BdpUserInfoVO> results = new ArrayList<BdpUserInfoVO>();
		if (CollectionUtils.isEmpty(records))
			return results;
		for (ZyyUserExtendVO record : records)
			results.add(transferZyyUserExtend(record, zyyUserType));
		return results;
	}
	
	private static String change(Map<String, ZyyUserExtendVO> zyyUserMap, BdpUserInfoVO d) {
		if (d == null)
			return StringPool.BLANK;
		String status = d.getStatus();
		if (StringUtils.isBlank(status))
			return StringPool.BLANK;
		if (SUCCESS.equals(status)) {
			ZyyUserExtendVO zyyUser = null;
			String accountName = d.getUserAccount();
			if (StringUtils.isNotBlank(accountName))
				zyyUser = zyyUserMap.get(accountName);
			if (zyyUser != null)
				zyyUser.setBdpUserId(d.getBdpUserId());
			return StringPool.BLANK;
		} else
			return String.format("[bdpUserId=%s，userAccount=%s，msg=%s];", d.getBdpUserId(), d.getUserAccount(), d.getMsg());
	}

	private static String change(List<ZyyUserExtendVO> zyyUsers, List<BdpUserInfoVO> bdpUsers){
		if(CollectionUtils.isEmpty(zyyUsers) || CollectionUtils.isEmpty(bdpUsers))
			return StringPool.BLANK;
		Map<String, ZyyUserExtendVO> zyyUserMap = new HashMap<>();
		for (ZyyUserExtendVO zyyUser : zyyUsers) {
			if(zyyUser == null)
				continue;
			zyyUserMap.put(zyyUser.getAccountName(), zyyUser);
		}
		StringBuilder dataFailMsg = new StringBuilder("");
		for (BdpUserInfoVO bdpUser : bdpUsers)
			dataFailMsg.append(change(zyyUserMap, bdpUser));
		return dataFailMsg.toString();
	}
	
	/**
	 * 校验用户是否在一体化授权
	 */
	public Boolean checkUserIsReg(String bdpUserId) {
		Boolean result = false;
		String dataStr = JSONObject.toJSONString(new BdpUserInfoVO(bdpUserId, Constants.REGIST_ORIGIN_ZYY));
		System.out.println("---------------checkUserIsReg.dataStr=" + dataStr);
		String resStr = HttpUtil.doPostHttpRequest(SSOUtil.getDomain() + "/user/checkUserIsReg", dataStr);
		System.out.println("---------------checkUserIsReg.resStr=" + resStr);
		if (StringUtils.isNotBlank(resStr))
			result = Boolean.parseBoolean(resStr);
		return result;
	}
	
	/**
	 * 业务系统自动注册后到一体化授权
	 */
	public BdpUserInfoResult regAuth(String bdpUserId) {
		String dataStr = JSONObject.toJSONString(new BdpUserInfoVO(bdpUserId, Constants.REGIST_ORIGIN_ZYY));
		System.out.println("---------------regAuth.dataStr=" + dataStr);
		String resStr = HttpUtil.doPostHttpRequest(SSOUtil.getDomain() + "/dr/user/suUserInfo", dataStr);
		System.out.println("---------------regAuth.resStr=" + resStr);
		return JSONObject.parseObject(resStr, BdpUserInfoResult.class);
	}

	/**
	 * 单个导入/更新BDP用户
	 */
	public BdpUserInfoResult saveOrUpdate(ZyyUserExtendVO record, Integer zyyUserType) {
		BdpUserInfoVO bdpUser = this.transferZyyUserExtend(record, zyyUserType);
		String dataStr = JSONObject.toJSONString(bdpUser);
		System.out.println("---------------saveOrUpdate.dataStr=" + dataStr);
		String resStr = HttpUtil.doPostHttpRequest(SSOUtil.getDomain() + "/dr/user/suUserInfo", dataStr);
		System.out.println("---------------saveOrUpdate.resStr=" + resStr);
		BdpUserInfoResult br = JSONObject.parseObject(resStr, BdpUserInfoResult.class);
		if (br.isSuccess()) {
			Map<String, ZyyUserExtendVO> zyyUserMap = new HashMap<>();
			zyyUserMap.put(record.getAccountName(), record);
			br.setDataFailMessage(change(zyyUserMap, br.getData()));
		}
		return br;
	}

	/**
	 * 批量导入/更新BDP用户
	 */
	public BdpUserInfosResult saveOrUpdate(List<ZyyUserExtendVO> records, Integer zyyUserType) {
		List<BdpUserInfoVO> bdpUsers = this.transferZyyUserExtend(records, zyyUserType);
		String url = SSOUtil.getDomain() + "/dr/user/drUser";
		String dataStr = JSONObject.toJSONString(bdpUsers);
		System.out.println("---------------url=" + url);
		System.out.println("---------------dataStr=" + dataStr);
		String resStr = HttpUtil.doPostHttpRequest(url, dataStr);
		System.out.println("---------------resStr=" + resStr);
		BdpUserInfosResult br = JSONObject.parseObject(resStr, BdpUserInfosResult.class);
		if (br.isSuccess())
			br.setDataFailMessage(change(records, br.getData()));
		return br;
	}
	
	/**
	 * 根据bdpUserId校验用户密码是否正确
	 */
	public BdpResult validatePassword(String bdpUserId, String password) {
		if (StringUtils.isBlank(bdpUserId))
			return BdpResult.failure("bdpUserId为空！");
		if (StringUtils.isBlank(password))
			return BdpResult.failure("password为空！");
		String passwordEncrypt = BDPMD5Util.encryptSalt(password);
		String url = SSOUtil.getDomain() + "/user/checkPwd/" + bdpUserId + "?pwd=" + passwordEncrypt;
		String resStr = HttpUtil.doGet(url);
		System.out.println("---------------url=" + url);
		System.out.println("---------------resStr=" + resStr);
		BdpResult br = JSONObject.parseObject(resStr, BdpResult.class);
		return br;
	}
	
	/**
	 * 根据bdpUserId修改用户密码
	 */
	public BdpResult modifyPassword(String bdpUserId, String password) {
		if (StringUtils.isBlank(bdpUserId))
			return BdpResult.failure("bdpUserId为空！");
		if (StringUtils.isBlank(password))
			return BdpResult.failure("password为空！");
		String passwordEncrypt = BDPMD5Util.encryptSalt(password);
		String url = SSOUtil.getDomain() + "/user/editPwd/" + bdpUserId + "?pwd=" + passwordEncrypt;
		String resStr = HttpUtil.doPostHttpRequestOnlyUrl(url);
		System.out.println("---------------url=" + url);
		System.out.println("---------------resStr=" + resStr);
		BdpResult br = JSONObject.parseObject(resStr, BdpResult.class);
		return br;
	}

	public BdpDictInfoResult getBdpDictValueByCode(String dictCode) {
		if (StringUtils.isBlank(dictCode))
			return null;
		StringBuffer buffer = new StringBuffer();
		buffer.append(SSOUtil.getDomain());
		buffer.append("/dict/value/");
		buffer.append(dictCode);
		String resStr = HttpUtil.doGet(buffer.toString());
		System.out.println("---------------getBdpDictValueByCode=" + resStr);
		BdpDictInfoResult br = JSONObject.parseObject(resStr, BdpDictInfoResult.class);
		return br;
	}
	
	public JSONObject saveOrUpdateBdpUserInfoVO(BdpUserInfoVO bdpUserInfoVO) {
		if (bdpUserInfoVO == null){
			return null;
		}
		String dataStr = JSONObject.toJSONString(bdpUserInfoVO);
		String resStr = HttpUtil.doPostHttpRequest(SSOUtil.getDomain() + "/dr/user/suUserInfo", dataStr);
		JSONObject jsonObject = JSONObject.parseObject(resStr);
		return jsonObject;

	}

	public JSONObject getUserInfoByUserName(String userName) {
		try {
			StringBuffer buffer = new StringBuffer();
			buffer.append(SSOUtil.getDomain());
			buffer.append("/user/management/getUserByAccount");
			List<NameValuePair> params = new ArrayList<NameValuePair>();
			params.add(new BasicNameValuePair("userAccount", userName));
			params.add(new BasicNameValuePair("registOrigin", Constants.REGIST_ORIGIN_ZYY));
			System.out.println("---------------url=" + buffer.toString());
			System.out.println("---------------dataStr=" + params);
			String record = HttpUtil.doPostHttpRequestFormUrlencoded(buffer.toString(),params);
			System.out.println("---------------resStr=" + record);
			JSONObject jo = JSONObject.parseObject(record);
			return jo;
		}catch (Exception e){
			logger.error("getUserInfoByUserName",e);
		}
		return null;
	}
	
	private static List<ZyyUserExtendVO> change(List<ZyyResidencyImportVO> tolist) {
		List<ZyyUserExtendVO> olist = new ArrayList<ZyyUserExtendVO>();
		if (CollectionUtils.isEmpty(tolist))
			return olist;
		for (ZyyResidencyImportVO to : tolist) {
			if (to == null)
				continue;
			ZyyUserExtendVO o = new ZyyUserExtendVO();
			BeanUtils.copyProperties(to, o);
			o.setZyyUserOrgId(to.getOrgId());
			olist.add(o);
		}
		return olist;
	}
	
	/**
	 * 数据推送至BDP
	 */
	public Json transfer(List<ZyyResidencyImportVO> datas) {
		Json j = new Json();
		List<ZyyUserExtendVO> zyyUsers = change(datas);
		BdpUserInfosResult br = this.saveOrUpdate(zyyUsers, Constants.USER_TYPE_STUDENT_2);
		if (!br.isSuccess()) {
			j.setSuccess(false);
			j.setMsg(br.getMsg());
			return j;
		} else {
			String dataFailMessage = br.getDataFailMessage();
			if (StringUtils.isNotBlank(dataFailMessage)) {
				j.setSuccess(false);
				j.setMsg(dataFailMessage);
				return j;
			}
		}
		if (CollectionUtils.isNotEmpty(datas) && CollectionUtils.isNotEmpty(zyyUsers)) {
			Map<String, ZyyResidencyImportVO> dataMap = new HashMap<String, ZyyResidencyImportVO>();
			for (ZyyResidencyImportVO data : datas) {
				if (data == null)
					continue;
				dataMap.put(data.getAccountName(), data);
			}
			String accountName = null;
			ZyyResidencyImportVO data = null;
			for (ZyyUserExtendVO user : zyyUsers) {
				if (user == null)
					continue;
				accountName = user.getAccountName();
				if (StringUtils.isBlank(accountName))
					continue;
				data = dataMap.get(accountName);
				if (data != null)
					data.setBdpUserId(user.getBdpUserId());
			}
		}
		j.setSuccess(true);
		j.setMsg("操作成功！");
		return j;
	}

}





















