package com.hys.zyy.manage.util;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;

import ch.lambdaj.Lambda;

/**
 * Collection工具�?
 * <AUTHOR>
 */
public class CollectionUtils extends org.apache.commons.collections.CollectionUtils {
	
	/**
	 * 向列表中填充指定个数的对�?
	 * @param list
	 * @param size
	 */
	public static void fillList(List list, int size)
    {
        fillList(list, size, null);
    }

	public static void fillList(List list, int size, Object object)
    {
        for(int ii = list.size(); ii <  size; ii++)
        {
            list.add(object);
        }
    }
	
	/**
	 * 返回指定个数的列�?
	 * @param list
	 * @param len
	 * @return
	 */
	public static List truncateList(List list, int len)
    {
        if(len >= list.size()) return list;
        ArrayList newList = new ArrayList(len);
        for(int ii = 0; ii < len; ii++)
        {
            newList.add(list.get(ii));
        }
        return newList;
    }
	
	/**
	 * 判断c1是否包括c2的全�?
	 * @param c1
	 * @param c2
	 * @return
	 */
	public static boolean containsAll(Collection c1, Collection c2) {
		if(c1 == null && c2 == null)
			return true;
		else if(c1 == null && c2 != null)
			return false;
		else if(c1 != null && c2 == null)
			return true;
		else if(c1 != null && c2 != null)
			return c1.containsAll(c2);
		return false;
	}
	
	public static boolean isEmpty(Collection list) {
		if(list == null || list.size() == 0)
			return true;
		return false;
	}
	
	public static boolean isNotEmpty(Collection list) {
		return !isEmpty(list);
	}
	
	/**
	 * 如果列表中存放的是Bean,将返回bean属�?�名为attrName的属性�?�集�?
	 * @param list
	 * @param attrName
	 * @return
	 */
	public static List extractProperty(Collection list, String attrName) {
		if(list == null)
			return Collections.EMPTY_LIST;
		if(StringUtils.isBlank(attrName))
			return new LinkedList(list);
		return Lambda.extractProperty(list, attrName);
	}
	
	public static Collection extractDistinctProperty(Collection list, String attrName) {
		list = extractProperty(list, attrName);
		return Lambda.selectDistinct(list);
	}
	
	public static <T> List<T> extractDistinctProperty2(Collection list, String attrName) {
		Collection<T> collection = CollectionUtils.extractDistinctProperty(list, attrName);
		List<T> resultList = new ArrayList<T>(collection);
		return resultList;
	}
	
	public static <T> T safeGet(Collection<T> collection, int index) {
		try {
			return (T) get(collection, index);
		}
		catch(RuntimeException e) {
		}
		return null;
	}
	
	public static Field[] getBeanFields(Class cls, Field[] fs) {
		fs = (Field[]) ArrayUtils.addAll(fs, cls.getDeclaredFields());
		if (cls.getSuperclass() != null)
			fs = getBeanFields(cls.getSuperclass(), fs);
		return fs;
	}
	
	/**
	 * @desc 批量设置实体类集合里面某一个属性的值
	 * @param entitys 目标实体类集合
	 * @param key 目标属性
	 * @param value 值
	 * <AUTHOR>
	 * @date 2021-7-1  下午2:10:27
	 */
	public static <E> void setValue(List<E> entitys, String key, Object value) {
		try {
			for (Object entity : entitys) {
				// 获取实体类的所有属性
				Field[] fs = new Field[]{};
				fs = CollectionUtils.getBeanFields(entity.getClass(), fs);
				for (Field f : fs) {
					if (f.getName().equals(key)) {
						f.setAccessible(true);// 设置访问权限
						f.set(entity, value);// 赋值
					}
				}
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		}
	}
	
	/**
	 * 将源List按照指定元素数量拆分为多个List
	 * @param source 源List
	 * @param splitItemNum 每个List中元素数量
	 */
	public static <T> List<List<T>> averageAssign(List<T> source, int splitItemNum) {
		List<List<T>> result = new ArrayList<List<T>>();
		if (source != null && source.size() > 0 && splitItemNum > 0) {
			if (source.size() <= splitItemNum) {
				// 源List元素数量小于等于目标分组数量
				result.add(source);
			} else {
				// 计算拆分后list数量
				int splitNum = (source.size() % splitItemNum == 0) ? (source.size() / splitItemNum) : (source.size() / splitItemNum + 1);
				List<T> value = null;
				for (int i = 0; i < splitNum; i++) {
					if (i < splitNum - 1) {
						value = source.subList(i * splitItemNum, (i + 1) * splitItemNum);
					} else {
						// 最后一组
						value = source.subList(i * splitItemNum, source.size());
					}
					result.add(value);
				}
			}
		}
		return result;
	}
	
}






















