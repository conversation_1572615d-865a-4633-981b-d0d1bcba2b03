package com.hys.zyy.manage.util;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.UnsupportedEncodingException;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.KeyGenerator;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;


import sun.misc.BASE64Encoder;

public class Crypto {
	
	/**
     * 密钥算法
    */
    private static final String KEY_ALGORITHM = "AES";
       
    private static final String DEFAULT_CIPHER_ALGORITHM = "AES/ECB/PKCS5Padding";
    

	/** 
     * 进行MD5加密 
     *  
     * @param info 
     *            要加密的信息 
     * @return String 加密后的字符串 
     */  
    public static String encryptToMD5(String info) {  
        byte[] digesta = null;  
        try {  
            // 得到一个md5的消息摘要  
            MessageDigest alga = MessageDigest.getInstance("MD5");  
            // 添加要进行计算摘要的信息  
            alga.update(info.getBytes());  
            // 得到该摘要  
            digesta = alga.digest();  
        } catch (NoSuchAlgorithmException e) {  
            e.printStackTrace();  
        }  
        // 将摘要转为字符串  
        String rs = parseByte2HexStr(digesta);  
        return rs;  
    }  
    /** 
     * 进行SHA加密 
     *  
     * @param info  要加密的信息 
     * @return String 加密后的字符串 
     */  
    public static String encryptToSHA(String info) {  
        byte[] digesta = null;  
        try {  
            // 得到一个SHA-1的消息摘要  
            MessageDigest alga = MessageDigest.getInstance("SHA-1");  
            // 添加要进行计算摘要的信息  
            alga.update(info.getBytes());  
            // 得到该摘要  
            digesta = alga.digest();  
        } catch (NoSuchAlgorithmException e) {  
            e.printStackTrace();  
        }  
        // 将摘要转为字符串  
        String rs = parseByte2HexStr(digesta);  
        return rs;  
    }  
   
       
     /** 
     * 加密 
     *  
     * @param content 需要加密的内容 
     * @param key  密钥
     * @return 
     */  
    public static String encrypt(String content, String key) {  
            try {             
                    KeyGenerator kgen = KeyGenerator.getInstance("AES");  
                    SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG" );  
                    secureRandom.setSeed(key.getBytes());  
                    kgen.init(128,secureRandom);  
                    //kgen.init(128, new SecureRandom(key.getBytes("utf-8")));  
                    SecretKey secretKey = kgen.generateKey();  
                    byte[] enCodeFormat = secretKey.getEncoded(); 
                    BASE64Encoder encode=new BASE64Encoder(); 
                    String keys=encode.encode(enCodeFormat);
                    SecretKeySpec k = new SecretKeySpec(enCodeFormat, "AES"); 
                    Cipher cipher = Cipher.getInstance("AES");// 创建密码器  
                    byte[] byteContent = content.getBytes("utf-8");  
                    cipher.init(Cipher.ENCRYPT_MODE, k);// 初始化  
                    byte[] result = cipher.doFinal(byteContent);  
                    return parseByte2HexStr(result); // <strong>加密</strong>  
            } catch (NoSuchAlgorithmException e) {  
                    e.printStackTrace();  
            } catch (NoSuchPaddingException e) {  
                    e.printStackTrace();  
            } catch (InvalidKeyException e) {  
                    e.printStackTrace();  
            } catch (UnsupportedEncodingException e) {  
                    e.printStackTrace();  
            } catch (IllegalBlockSizeException e) {  
                    e.printStackTrace();  
            } catch (BadPaddingException e) {  
                    e.printStackTrace();  
            }  
            return null;  
    }  
    /**解密
     * @param content  待解密内容 
     * @param password 解密密钥 
     * @return 
     */  
    public static String decrypt(String content, String key) {  
            try {  
                     KeyGenerator kgen = KeyGenerator.getInstance("AES");  
                     SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG" );  
                     secureRandom.setSeed(key.getBytes());  
                     kgen.init(128,secureRandom);  
                     //kgen.init(128, new SecureRandom(key.getBytes("utf-8")));  
                     SecretKey secretKey = kgen.generateKey();  
                     byte[] enCodeFormat = secretKey.getEncoded();  
                     SecretKeySpec k = new SecretKeySpec(enCodeFormat, "AES");              
                     Cipher cipher = Cipher.getInstance("AES");// 创建密码器  
                    cipher.init(Cipher.DECRYPT_MODE, k);// 初始化  
                    byte[] result = cipher.doFinal(parseHexStr2Byte(content));  
                    return new String(result); // <strong>加密</strong>  
            } catch (NoSuchAlgorithmException e) {  
                    e.printStackTrace();  
            } catch (NoSuchPaddingException e) {  
                    e.printStackTrace();  
            } catch (InvalidKeyException e) {  
                    e.printStackTrace();  
            } catch (IllegalBlockSizeException e) {  
                    e.printStackTrace();  
            } catch (BadPaddingException e) {  
                    e.printStackTrace();  
            }  
            return null;  
    }  
   
    /**将二进制转换成16进制 
     * @param buf 
     * @return 
     */  
    public static String parseByte2HexStr(byte buf[]) {  
            StringBuffer sb = new StringBuffer();  
            for (int i = 0; i < buf.length; i++) {  
                    String hex = Integer.toHexString(buf[i] & 0xFF);  
                    if (hex.length() == 1) {  
                            hex = '0' + hex;  
                    }  
                    sb.append(hex.toUpperCase());  
            }  
            return sb.toString();  
    }  
    
    /**将16进制转换为二进制 
     * @param hexStr 
     * @return 
     */  
    public static byte[] parseHexStr2Byte(String hexStr) {  
            if (hexStr.length() < 1)  
                    return null;  
            byte[] result = new byte[hexStr.length()/2];  
            for (int i = 0;i< hexStr.length()/2; i++) {  
                    int high = Integer.parseInt(hexStr.substring(i*2, i*2+1), 16);  
                    int low = Integer.parseInt(hexStr.substring(i*2+1, i*2+2), 16);  
                    result[i] = (byte) (high * 16 + low);  
            }  
            return result;  
    }  
   
    public Object getObjFromFile(String file, int i) {  
        ObjectInputStream ois = null;  
        Object obj = null;  
        try {  
            FileInputStream fis = new FileInputStream(file);  
            ois = new ObjectInputStream(fis);  
            for (int j = 0; j < i; j++) {  
                obj = ois.readObject();  
            }  
        } catch (Exception e) {  
            e.printStackTrace();  
        } finally {  
            try {  
                ois.close();  
            } catch (IOException e) {  
                e.printStackTrace();  
            }  
        }  
        return obj;  
    }  
    /** 
     * 测试 
     *  
     * @param args 
     */  
    public static void main(String[] args) throws Exception {  
        String data ="123456";
        System.out.println("加密前数据: string:"+data);
        System.out.println();
        String encryptData = encrypt(data, "HYSZYY");
        System.out.println("加密后数据: hexStr:"+encryptData);
        System.out.println();
        String decryptData = decrypt(encryptData, "HYSZYY");
        System.out.println("解密后数据: string:"+decryptData);
       
    } 
}
