package com.hys.zyy.manage.util;

import java.io.IOException;
import java.util.Date;
import java.util.List;

import javax.servlet.jsp.JspException;
import javax.servlet.jsp.JspWriter;
import javax.servlet.jsp.tagext.TagSupport;

import com.hys.zyy.manage.model.ZyyCycleTableResiCycleVO;

public class DateToHalfMonthTag extends TagSupport{
	private  List<ZyyCycleTableResiCycleVO> deptList;
	
	public int doEndTag() throws JspException { 
		if(null == deptList || deptList.size()==0){
			return TagSupport.EVAL_PAGE;
		}
		JspWriter writer = pageContext.getOut();
		Date startDate = deptList.get(0).getStartDate();
		Date endDate = deptList.get(deptList.size()-1).getEndDate();
		//判断 一个人在科室里的时间是否是断开的，如果是断开的 那就写两次
		for(int i = 0;i < deptList.size(); i++){
			ZyyCycleTableResiCycleVO vo = deptList.get(i);
			//判断 日期是否连续
			if(i + 1 != deptList.size() && !dateIsLianXu(vo.getEndDate(),deptList.get(i+1).getStartDate())){
				this.writeToPage(startDate, vo.getEndDate(), writer);
				startDate = deptList.get(i+1).getStartDate();
			}
		}
		this.writeToPage(startDate, endDate, writer);
		return TagSupport.EVAL_PAGE;
	}
	private void writeToPage(Date startDate,Date endDate,JspWriter writer){
		try {
			int star = checkHalfMonth(startDate);
			int end = checkHalfMonth(endDate);
			String startTime = DateUtil.format(startDate, "yyyy-MM") ;
			String startTimeEnd = DateUtil.format(startDate, "yy/MM/dd") ;
			if(star==0) startTime += "月"; else startTime += "月(下)";
			String endTime = DateUtil.format(endDate, "yyyy-MM") ;
			String endTimeEnd = DateUtil.format(endDate, "yy/MM/dd") ;
			if(end==0) endTime += "月(上)"; else endTime += "月";
			
			if(startDate.getMonth() == endDate.getMonth() && startDate.getYear() ==  endDate.getYear()){
				int startDay = LogicUtils.getDayInCurrentTime(startDate);
				int endDay = LogicUtils.getDayInCurrentTime(endDate);
				if(endDay == 15 && startTime.indexOf("(上)") == -1){
					startTime += "(上)";
				}else if(startDay == 16 && startTime.indexOf("(下)") == -1){
					startTime += "(下)";
				}
				String str = "</br>"+startTime +"</br><span class='fontgray'>(" + startTimeEnd +"&nbsp;&nbsp;至 " + endTimeEnd+")</span>";
				if (NewRecruitUtil.unCheckCycleStartDate())
					str = str.replace("(上)", StringPool.BLANK).replace("(下)", StringPool.BLANK);
				writer.write(str);
			}else{
				String str = "</br>"+startTime+" 至 "+endTime +"</br><span class='fontgray'>(" + startTimeEnd +"&nbsp;&nbsp;至 " + endTimeEnd+")</span>";
				if (NewRecruitUtil.unCheckCycleStartDate())
					str = str.replace("(上)", StringPool.BLANK).replace("(下)", StringPool.BLANK);
				writer.write(str);
			}
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
	//判断两个日期是否连续
	private Boolean dateIsLianXu(Date date1Min,Date date2Max){
		return DateUtil.getStartAndEndDays(date1Min, date2Max)==1 ? true : false;
	}
	public static int checkHalfMonth(Date startTime){
		//取得当前时间的天
		int day = LogicUtils.getDayInCurrentTime(startTime) ;
		//1.表示是上半月 1->15号, >1或(16)表示下半月 也就是16-月末
		if(day == 1 || day == 15){
			return 0;
		}else{
			return 1;
		}
	}

	public List<ZyyCycleTableResiCycleVO> getDeptList() {
		return deptList;
	}

	public void setDeptList(List<ZyyCycleTableResiCycleVO> deptList) {
		this.deptList = deptList;
	}
}
