package com.hys.zyy.manage.util;

import java.io.IOException;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import javax.servlet.jsp.JspException;
import javax.servlet.jsp.JspWriter;
import javax.servlet.jsp.tagext.TagSupport;

import com.hys.zyy.manage.model.ZyyCycleTableResiCycleVO;

public class DateToWeekTag extends TagSupport{
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	private  List<ZyyCycleTableResiCycleVO> deptList;
	
	public int doEndTag() throws JspException { 
		if(null == deptList || deptList.size()==0){
			return TagSupport.EVAL_PAGE;
		}
		JspWriter writer = pageContext.getOut();
		Date startDate = deptList.get(0).getStartDate();
		Date endDate = deptList.get(deptList.size()-1).getEndDate();
		//判断 一个人在科室里的时间是否是断开的，如果是断开的 那就写多次
		for(int i = 0;i < deptList.size(); i++){
			ZyyCycleTableResiCycleVO vo = deptList.get(i);
			//判断 日期是否连续
			if(i + 1 != deptList.size() && !dateIsLianXu(vo.getEndDate(),deptList.get(i+1).getStartDate())){
				this.writeToPage(startDate, vo.getEndDate(), writer);
				startDate = deptList.get(i+1).getStartDate();
			}
		}
		this.writeToPage(startDate, endDate, writer);
		return TagSupport.EVAL_PAGE;
	}
	
	@SuppressWarnings("deprecation")
	private void writeToPage(Date startDate,Date endDate,JspWriter writer){
		try {
			String startTime = DateUtil.format(startDate, "yyyy-MM-dd") ;
			String startTimeEnd = DateUtil.format(startDate, "yy/MM/dd") ;
			String endTime = DateUtil.format(endDate, "yyyy-MM-dd") ;
			String endTimeEnd = DateUtil.format(endDate, "yy/MM/dd") ;
			
			Date temp = DateUtil.parse(LogicUtils.addTime(endDate, -6),"yyyy-MM-dd");
			int startYearWeek = DateUtil.calculateDateInWeekByDate(Calendar.MONDAY, Calendar.WEEK_OF_YEAR, startDate) ;
			int endYearWeek = DateUtil.calculateDateInWeekByDate(Calendar.MONDAY, Calendar.WEEK_OF_YEAR, temp) ;
			String str1 = "";
			String str2 = "";
			if(startYearWeek < 10) str1 = "0";
			if(endYearWeek < 10) str2 = "0";
				
			if(startDate.getTime() == temp.getTime()){
				writer.write("</br>" + startTime.substring(0,4) + "-" + str1 + startYearWeek + "周" 
						+ "</br>(" + startTimeEnd +"&nbsp;&nbsp;至 " + endTimeEnd+")");
			}else{
				writer.write("</br>" + startTime.substring(0,4) + "-" + str1 + startYearWeek + "周" 
						+ " 至 "+ endTime.substring(0,4) + "-" + str2 + endYearWeek + "周"
						+ "</br>(" + startTimeEnd +"&nbsp;&nbsp;至 " + endTimeEnd+")");
			}
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
	
	//判断两个日期是否连续
	private Boolean dateIsLianXu(Date date1Min,Date date2Max){
		return DateUtil.getStartAndEndDays(date1Min, date2Max)==1 ? true : false;
	}

	public List<ZyyCycleTableResiCycleVO> getDeptList() {
		return deptList;
	}

	public void setDeptList(List<ZyyCycleTableResiCycleVO> deptList) {
		this.deptList = deptList;
	}
	
}
