package com.hys.zyy.manage.util;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.lang.time.DateUtils;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：张伟清 2011-07-02
 * 
 * 描述：日期工具类
 * 
 * 说明:
 */
public class DateUtil {

	/**
	 * 英文简写（默认）如：2010-12
	 */	
	public static String FORMAT_SHORTER = "yyyy-MM";
	/**
	 * 英文简写（默认）如：2010-12-01
	 */
	public static String FORMAT_SHORT = "yyyy-MM-dd";
	
	public static String FORMAT_DAY = "dd";
	
	public static String FORMAT_SHORT_POINT = "yyyy.MM.dd";
	
	public static String FORMAT_SHORT_SLASH = "yy/MM/dd";
	
	public static String FORMAT_SHORT_BLANK = "yyyyMMdd";

	/**
	 * 英文全称如：2010-12-01 23:15:06
	 */
	public static final String FORMAT_LONG = "yyyy-MM-dd HH:mm:ss";

	/**
	 * Oracle数据库格式 英文全称如：2010-12-01 23:15:06
	 */
	public static final String FORMAT_LONG_OF_ORACLE = "yyyy-mm-dd hh24:mi:ss";

	/**
	 * 精确到毫秒的完整时间 如：yyyy-MM-dd HH:mm:ss.S
	 */
	public static String FORMAT_FULL = "yyyy-MM-dd HH:mm:ss.S";
	
	/**
	 * 精确到分钟
	 */
	public static final String FORMAT_MINUTES = "yyyy-MM-dd HH:mm";
	
	public static final String FORMAT_SECOND = "HHmmss";

	/**
	 * 中文简写如：2010年12月01日
	 */
	public static String FORMAT_SHORT_CN = "yyyy年MM月dd日";

	/**
	 * 中文全称如：2010年12月01日23时15分06秒
	 */

	public static String FORMAT_LONG_CN = "yyyy年MM月dd日HH时mm分ss秒";
	
	/**
	 * 中文全称如：2010年12月01日23时15分
	 */
	public static String FORMAT_MINUTES_CN = "yyyy年MM月dd日HH时mm分";

	/**
	 * 精确到毫秒的完整中文时间
	 */
	public static String FORMAT_FULL_CN = "yyyy年MM月dd日HH时mm分ss秒SSS毫秒";
	
	/**
	 * 正则匹配yyyy-MM格式日期
	 */
	public static String REGEX_FORMAT_SHORTER = "((19|20)\\d{2})[-](0[1-9]|1[012])";

	/**
	 * 获得默认的 date pattern
	 */
	public static String getDatePattern() {
		return FORMAT_LONG;
	}

	/**
	 * 根据预设格式返回当前日期
	 * @return
	 */
	public static String getNow() {
		return format(new Date());
	}

	/**
	 * 根据用户格式返回当前日期
	 * @param format
	 * @return
	 */
	public static String getNow(String format) {
		return format(new Date(), format);
	}

	/**
	 * 使用预设格式格式化日期
	 * @param date
	 * @return
	 */
	public static String format(Date date) {
		return format(date, getDatePattern());
	}

	/**
	 * 使用用户格式格式化日期
	 * @param date 日期
	 * @param pattern 日期格式
	 * @return
	 */
	public static String format(Date date, String pattern) {
		String returnValue = "";
		if (date != null) {
			TimeZone.setDefault(TimeZone.getTimeZone("GMT+08"));
			SimpleDateFormat df = new SimpleDateFormat(pattern);
			returnValue = df.format(date);
		}
		return returnValue;
	}

	/**
	 * 使用预设格式提取字符串日期
	 * @param strDate 日期字符串
	 * @return
	 */
	public static Date parse(String strDate) {
		return parse(strDate, getDatePattern());
	}

	/**
	 * 使用用户格式提取字符串日期
	 * @param strDate 日期字符串
	 * @param pattern 日期格式
	 * @return
	 */
	public static Date parse(String strDate, String pattern) {
		SimpleDateFormat df = new SimpleDateFormat(pattern);
		try {
			return df.parse(strDate);
		} catch (Exception e) {
			//e.printStackTrace();
			return null;
		}
	}
	
	public static List<Date> parse(List<String> strDates, String pattern) {
		List<Date> results = new ArrayList<Date>();
		if (CollectionUtils.isNotEmpty(strDates)) {
			for (String strDate : strDates)
				results.add(parse(strDate, pattern));
		}
		return results;
	}
	
	/**
	 * 使用用户格式提取字符串日期
	 * @param strDate 日期字符串
	 * @param pattern 日期格式
	 * @return
	 */
	public static Date parse(Date strDate, String pattern) {
		SimpleDateFormat df = new SimpleDateFormat(pattern);
		try {
			String tempDate = df.format(strDate) ;
			return df.parse(tempDate);
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	/**
	 * 在日期上增加数个整月
	 * @param date 日期
	 * @param n 要增加的月数
	 * @return
	 */
	public static Date addMonth(Date date, int n) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		cal.add(Calendar.MONTH, n);
		return cal.getTime();
	}

	/**
	 * 在日期上增加天数
	 * @param date 日期
	 * @param n 要增加的天数
	 * @return
	 */
	public static Date addDay(Date date, int n) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		cal.add(Calendar.DATE, n);
		return cal.getTime();
	}
	
	/**
	 * 分钟上添加
	 * @param date
	 * @param n
	 * @return
	 */
	public static Date addMinute(Date date, int n) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		cal.add(Calendar.MINUTE, n);
		return cal.getTime();
	}
	
	/**
	 * 获取时间戳
	 */

	public static String getTimeString() {
		SimpleDateFormat df = new SimpleDateFormat(FORMAT_FULL);
		Calendar calendar = Calendar.getInstance();
		return df.format(calendar.getTime());
	}

	/**
	 * 获取日期年份
	 * @param date 日期
	 * @return
	 */
	public static String getYear(Date date) {
		return format(date).substring(0, 4);
	}

	/**
	 * 按默认格式的字符串距离今天的天数
	 * @param date 日期字符串
	 * @return
	 */
	public static int countDays(String date) {
		long t = Calendar.getInstance().getTime().getTime();
		Calendar c = Calendar.getInstance();
		c.setTime(parse(date));
		long t1 = c.getTime().getTime();
		return (int) (t / 1000 - t1 / 1000) / 3600 / 24;
	}
	
	public static int countMonths(Date d1, Date d2) {
		return Math.abs(calculateIntervalMonths(d1, d2));
	}

	/**
	 * 按用户格式字符串距离今天的天数
	 * @param date 日期字符串
	 * @param format 日期格式
	 * @return
	 */
	public static int countDays(String date, String format) {
		long t = Calendar.getInstance().getTime().getTime();
		Calendar c = Calendar.getInstance();
		c.setTime(parse(date, format));
		long t1 = c.getTime().getTime();
		return (int) (t / 1000 - t1 / 1000) / 3600 / 24;
	}
	
	/**
	 * 按用户格式字符串距离今天的天数
	 * @param date 日期
	 * @return
	 */
	public static int countDays(Date date) {
		long t = Calendar.getInstance().getTime().getTime();
		Calendar c = Calendar.getInstance();
		c.setTime(date);
		long t1 = c.getTime().getTime();
		return (int) (t / 1000 - t1 / 1000) / 3600 / 24;
	}

	/**
	 * 计算结束时间距离开始时间天数
	 */
	public static int getStartAndEndDays(Date startTime, Date endTime) {
		Calendar start = Calendar.getInstance(); start.setTime(startTime);
		Calendar end = Calendar.getInstance(); end.setTime(endTime);
		long _start = start.getTime().getTime() ;
		long _end 	= end.getTime().getTime() ;
		return Math.abs((int) (_end / 1000 - _start / 1000) / 3600 / 24);
	}
	
	
	/**
	 * 计算结束时间距离开始时间天数  不带绝对值
	 */
	public static int getStartAndEndDaysNotABS(Date startTime, Date endTime) {
		Calendar start = Calendar.getInstance(); start.setTime(startTime);
		Calendar end = Calendar.getInstance(); end.setTime(endTime);
		long _start = start.getTime().getTime() ;
		long _end 	= end.getTime().getTime() ;
		return ((int) (_end / 1000 - _start / 1000) / 3600 / 24);
	}
	
	
	/**
	 * 取得当前月最大天数
	 * @return
	 */
	public static int getMonthMaxDay(){
		Calendar calendar = Calendar.getInstance() ;
		calendar.setTime(new Date()) ;
		return calendar.getActualMaximum(Calendar.DAY_OF_MONTH) ;
	}

	/**
	 * 取得当前月最小天数
	 * @return
	 */
	public static int getMonthMinDay(){
		Calendar calendar = Calendar.getInstance() ;
		calendar.setTime(new Date()) ;
		return calendar.getActualMaximum(Calendar.DAY_OF_MONTH) ;
	}
	
	/**
	 * 取得每月最大天数
	 * @param date
	 * @return
	 */
	public static int getMonthMaxDay(String date){
		Calendar calendar = Calendar.getInstance() ;
		calendar.setTime(parse(date)) ;
		return calendar.getActualMaximum(Calendar.DAY_OF_MONTH) ;
	}

	/**
	 * 取得每月最小天数
	 * @param date
	 * @return
	 */
	public static int getMonthMinDay(String date){
		Calendar calendar = Calendar.getInstance() ;
		calendar.setTime(parse(date)) ;
		return calendar.getActualMinimum(Calendar.DAY_OF_MONTH) ;
	}
	
	/**
	 * 得到本月的第一天
	 */
	public static Date getMonthFirstDay(Date date) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMinimum(Calendar.DAY_OF_MONTH));

		return calendar.getTime();
	}

	/**
	 * 得到本月的最后一天
	 */
	public static Date getMonthLastDay(Date date) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
		return calendar.getTime();
	}

	/**
	 * 按用户格式字符串取得每月最大天数
	 * @param date
	 * @return
	 */
	public static int getMonthMaxDay(String date, String format){
		Calendar calendar = Calendar.getInstance() ;
		calendar.setTime(parse(date, format)) ;
		return calendar.getActualMaximum(Calendar.DAY_OF_MONTH) ;
	}
	
	/**
	 * 根据开始与结束时间取得间隔月份
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	public static int calculateIntervalMonths(Date startTime, Date endTime){
		Calendar start = Calendar.getInstance(); start.setTime(startTime);
		Calendar end = Calendar.getInstance(); end.setTime(endTime);
		
		int year = end.get(Calendar.YEAR) - start.get(Calendar.YEAR) ;
		int mont = end.get(Calendar.MONTH) - start.get(Calendar.MONTH) ;
		return year * 12 + mont ;
	}
	
	
	/**
	 * 根据开始与结束时间取得间隔年份
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	public static int calculateIntervalYears(Date startTime, Date endTime){
		Calendar start = Calendar.getInstance(); start.setTime(startTime);
		Calendar end = Calendar.getInstance(); end.setTime(endTime);
		int year = end.get(Calendar.YEAR) - start.get(Calendar.YEAR) ;
		return year  ;
	}
	
	/**
	 * 根据时间，获取间隔整年份信息
	 * @param nowDate 当前日期
	 * @param intervalYear  间隔年份
	 * @return
	 */
	public static String getBeforeYear(Date nowDate, int intervalYear){
		Calendar c = Calendar.getInstance();
		c.setTime(nowDate);
        c.add(Calendar.YEAR, intervalYear);
        c.add(Calendar.DATE, 1);
        Date y = c.getTime();
        SimpleDateFormat format = new SimpleDateFormat(FORMAT_SHORT);
        String year = format.format(y);
		return year  ;
	}
	
	/**
	 * 计算月份有多少周
	 * @param filed		Calendar.MONDAY 中国计算方式 已周一为一周开始
	 * @param strDate	月份日期
	 * @param format	格式化方式
	 * @return
	 */
	public static int calculateMonthWeeks(int filed, String strDate, String format){
		Calendar c = Calendar.getInstance();
		c.setTime(DateUtil.parse(strDate, format)) ;
		c.setFirstDayOfWeek(filed);
		c.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY) ;//每周从周一开始
		return c.getActualMaximum(Calendar.WEEK_OF_MONTH);
	}

	/**
	 * 计算月份有多少周
	 * @param filed		Calendar.MONDAY 中国周一为开始
	 * @param strDate	月份日期
	 * @param format	格式化方式
	 * @return
	 */
	public static int calculateMonthWeeksByDate(int filed, Date strDate, String format){
		return calculateMonthWeeks(filed, format(strDate, format), format) ;
	}
	
	/**
	 * 计算当前日期为本年度第几周
	 * @param startDay	Calendar.MONDAY 中国计算方式 已周一为一周开始
	 * @param weekFiled	Calendar.WEEK_OF_MONTH 本月第几周, Calendar.WEEK_OF_YEAR 本年度第几周 
	 * @param strDate	日期
	 * @return
	 */
	public static int calculateDateInWeek(int startDay, int weekFiled, String strDate){
		Calendar c = Calendar.getInstance();
		c.setTime(DateUtil.parse(strDate, "yyyy-MM-dd")) ;
		c.setFirstDayOfWeek(startDay);
		//每周从周一开始
		c.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY) ;
		return c.get(weekFiled);
	}

	/**
	 * 计算当前日期为本年度第几周
	 * @param filed	Calendar.MONDAY 中国计算方式 已周一为一周开始
	 * @param weekFiled	Calendar.WEEK_OF_MONTH 本月第几周, Calendar.WEEK_OF_YEAR 本年度第几周 
	 * @param strDate	日期
	 * @return
	 */
	public static int calculateDateInWeekByDate(int filed, int weekFiled, Date strDate){
		return calculateDateInWeek(filed, weekFiled, format(strDate, "yyyy-MM-dd"));
	}
	
	/**
	 * 计算本月 第一个 星期一 是几号
	 * @param strDate
	 * @return
	 */
	public static Date calculateMonthFirstWeek(Date strDate){
		Date retDate = null ;
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(parse(strDate, "yyyy-MM"));
		
		//取得本月最大天数
		int maxDate = getMonthMaxDay(format(calendar.getTime())) ;
		for (int i = 0; i <= maxDate; i++) {
			calendar.setTime(addDay(calendar.getTime(), (i == 0 ? 0 : 1))) ;
			if(calendar.get(Calendar.DAY_OF_WEEK) == Calendar.MONDAY){
				retDate = calendar.getTime() ; 
				break ;
			}
		}
		
		return retDate ;
	}
	
	/**
	 * 计算当前日期 第一个 星期一 是几号
	 * @param strDate
	 * @return
	 */
	public static Date calculateMonthFirstWeek(Date strDate, String format){
		Date retDate = null ;
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(parse(strDate, format));
		
		//取得本月最大天数
		int maxDate = getMonthMaxDay(format(calendar.getTime())) ;
		for (int i = 0; i <= maxDate; i++) {
			calendar.setTime(addDay(calendar.getTime(), (i == 0 ? 0 : 1))) ;
			if(calendar.get(Calendar.DAY_OF_WEEK) == Calendar.MONDAY){
				retDate = calendar.getTime() ; 
				break ;
			}
		}
		
		return retDate ;
	}
	
	/**
	 * @desc 获取日期是星期几（1=星期一；2=星期二；3=星期三；4=星期四；5=星期五；6=星期六；7=星期日）
	 * <AUTHOR>
	 * @date 2022-12-3 下午8:29:05
	 */
	public static Integer getWeekOfDate(Date date) {
		if (date == null)
			return null;
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		int w = cal.get(Calendar.DAY_OF_WEEK) - 1;
		if (w < 0)
			w = 0;
		if (w == 0)
			w = 7;
		return w;
	}
	
	public static void set(Date date, int value, int field) {
		if(date == null)
			return;
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.set(field, value);
		date.setTime(calendar.getTime().getTime());
	}

	/**
	 * 设置到本月的最后一天
	 * @param date
	 */
	public static void setLastDayOfMonth(Date date) {
		if(date == null)
			return;
		Calendar calendar = Calendar.getInstance() ;
		calendar.setTime(date);
		int lastDay = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
		calendar.set(Calendar.DAY_OF_MONTH, lastDay);
		date.setTime(calendar.getTime().getTime());
	}
	
	/**
	 * 传入时间是否在两个时间之间
	 * @param input
	 * @param start
	 * @param end
	 * @return
	 */
	public static boolean between(Date input, Date start, Date end) {
		 return compareTo(input, start) >= 0 && compareTo(input, end) <= 0;
	}
	
	public static Date now(String format) {
		return parse(new Date(), format);
	}
	/**
	 * yyyy-MM-dd 日期
	 * @return
	 * <AUTHOR>
	 * @date 2018-9-7上午10:26:13
	 */
	public static Date getShortNowDate() {
		return parse(new Date(), FORMAT_SHORT);
	}
	
	public static int compareTo(Date date1, Date date2) {
        if(date1 == null && date2 == null) return 0;
        if(date1 == null && date2 != null) return -1;
        if(date1 != null && date2 == null) return 1;
        return (int)(date1.compareTo(date2));
	}
	
	public static boolean isSameDay(Date date1, Date date2) {
		return DateUtils.isSameDay(date1, date2);
	}
	/**
	 * 校验日期 可识别的格式
	 * 2009-01-01
	 * 2009/01/01
	 * @param date
	 * @return true 正确 false 错误
	 */
	public static boolean checkDate(String date) {
		  String eL= "^((\\d{2}(([02468][048])|([13579][26]))[\\-\\/\\s]?((((0?[13578])|(1[02]))[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(30)))|(0?2[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])))))|(\\d{2}(([02468][1235679])|([13579][01345789]))[\\-\\/\\s]?((((0?[13578])|(1[02]))[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(30)))|(0?2[\\-\\/\\s]?((0?[1-9])|(1[0-9])|(2[0-8]))))))";
		  Pattern p = Pattern.compile(eL);
		  Matcher m = p.matcher(date);
		  boolean b = m.matches();
		  return b;
	}
	
	/**
	 * @desc 当前时间差值获取时间
	 * @param nowTime  当前时间
	 * @param offsetNum 差值（天）
	 * @param offsetType  相差类型  1 减  ; 2 加
	 * @return
	 */
	public static String getDateByOffset(Date nowTime, Integer offsetNum, Integer offsetType) {
		String result = "";
		if (offsetType == 1){
			if (offsetNum != null){
				Date date = DateUtil.addDay(nowTime,-offsetNum);
				result = DateUtil.format(date, DateUtil.FORMAT_SHORT);
			}
		} else if (offsetType == 2){
			if (offsetNum != null){
				Date date = DateUtil.addDay(nowTime,offsetNum);
				result = DateUtil.format(date, DateUtil.FORMAT_SHORT);
			}
		}
		return result;
	}
	
	/**
	 * 计算两个日期的相差天数(d1-d2)
	 */
	public static Long getDayDifference(Date d1, Date d2) {
		Long days = null;
		try {
			Long num = (d1.getTime() - d2.getTime()) / 1000;
			days = num / (3600 * 24);
			return days >= 0 ? (days + 1) : null;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}
      
	/** 
     *这里共有2个时间段（b1-----e1）【b2-----e2】，4个时间点； 
     *相当于两条线段(b代表起点，e代表端点，b<=e)，4个端点。 
     *可分3种情况： 
     *1.不相交。（b1-----e1）【b2-----e2】（b1-----e1）。if(e1<b2||b1>e2)此时，重合天数为零。 
     *2.相交。 
     *情况一：（b1---【b2---e1）----e2】          if(b1<b2&&e1<e2&&e1>b2) 
     *情况二：【b2---(b1---e2】----e1)       if(b1>b2&&b1<e2&&e2<e1) 
     *3.包含：计算较短的时间段日期长度。 
     *（b1---【b2-----e2】--e1）               if(b1<b2&&e1>e2) 
     *【b2---（b1-----e1）--e2】               if(b1>b2&&e1<e2) 
     * @param begindate1 开始日期 
     * @param enddate1      结束日期
     * @param enddate2     结束日期 
     * @return 
     */  
	public static Long getDayCoincidence(Date begindate1, Date enddate1, Date begindate2, Date enddate2) {
		long b1 = begindate1.getTime();
		long e1 = enddate1.getTime();
		long b2 = begindate2.getTime();
		long e2 = enddate2.getTime();
		assert (b1 <= e1 && b2 <= e2);
		Long coincidenceday = null;
		if (b1 <= b2 && e1 >= e2) {
			coincidenceday = getDayDifference(enddate2, begindate2);
		} else if (b1 >= b2 && e1 <= e2) {
			coincidenceday = getDayDifference(enddate1, begindate1);
		} else if (b1 >= b2 && b1 <= e2 && e2 <= e1) {
			coincidenceday = getDayDifference(enddate2, begindate1);
		} else if (b1 <= b2 && e1 <= e2 && e1 >= b2) {
			coincidenceday = getDayDifference(enddate1, begindate2);
		} else if (e1 <= b2 || b1 >= e2) {
			coincidenceday = 0L;
		}
		return coincidenceday;
	}
	
	/**
	 * @desc 判断2个时间段是否有交集
	 * <AUTHOR>
	 * @date 2021-6-7 上午10:37:08
	 */
	public static boolean hasCoincidence(Date beginDate1, Date endDate1, Date beginDate2, Date endDate2) {
		Long dayCoincidence = getDayCoincidence(beginDate1, endDate1, beginDate2, endDate2);
		return (dayCoincidence == null || dayCoincidence == 0L) ? false : true;
	}
	
	/**
	 * @desc 给出一个时间，判断是上半月还是下半月
	 * <AUTHOR>
	 * @date 2019-11-11 下午1:29:28
	 */
	public static Boolean isFirstHalfOfTheMonth(Date date) {
		Boolean result = null;
		if (date == null) {
			return result;
		}
		String dayStr = DateUtil.format(date, "dd");
		if (StringUtils.isBlank(dayStr)) {
			return result;
		}
		Integer day = Integer.valueOf(dayStr);
		if (day == null) {
			return result;
		}
		return day >= 16 ? false : true;
	}
	
	/**
	 * @desc 判断2个时间是相同年月
	 * <AUTHOR>
	 * @date 2019-11-11 下午1:47:43
	 */
	public static Boolean isSameYearAndMonth(Date date1, Date date2) {
		Boolean result = null;
		if (date1 == null || date2 == null) {
			return result;
		}
		String dateStr1 = DateUtil.format(date1, FORMAT_SHORTER);
		String dateStr2 = DateUtil.format(date2, FORMAT_SHORTER);
		if (StringUtils.isBlank(dateStr1) || StringUtils.isBlank(dateStr2)) {
			return result;
		}
		return dateStr1.equals(dateStr2) ? true : false;
	}
	
	/**
     * 获取时间路径-上传文件使用
     * @param date 时间
     * @param level 时间级别  1 年  2 年月  3  年月日
     * @return
     */
    public static String getTimePath(Date date,Integer level){
        if(date == null){
            date = new Date();
        }
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        StringBuilder sbl = new StringBuilder("");
        int year = cal.get(Calendar.YEAR);
        sbl.append(year).append("/");
        if(level > 1){
            int month = cal.get(Calendar.MONTH)+1;
            sbl.append(month).append("/");
        }
        if(level > 2){
            int day = cal.get(Calendar.DATE);
            sbl.append(day).append("/");
        }
        return sbl.toString();
    }

    public static String getTimePath(Date date){
        return getTimePath(date,3);
    }

    public static String getTimePath(Integer level){
        return getTimePath(new Date(),level);
    }
    public static String getTimePath(){
        return getTimePath(new Date(),3);
    }
	
	/**
	 * @desc 判断日期字符串格式是否符合指定的格式
	 * <AUTHOR>
	 * @date 2019-12-24  上午10:59:32
	 */
	public static boolean isValidDate(String datePattern, String dateStr) {
		boolean convertSuccess = true;
		SimpleDateFormat format = new SimpleDateFormat(datePattern);
		try {
			// 严格解析日期
			format.setLenient(false);
			format.parse(dateStr);
		} catch (ParseException ex) {
			convertSuccess = false;
		}
		return convertSuccess;
	}
	
	public static boolean validDate(String dateRegex, String dateStr) {
		Pattern pattern = Pattern.compile(dateRegex);
		Matcher matcher = pattern.matcher(dateStr);
		return matcher.matches();
	}
	
	/**
     * @desc 获取时间差值
     * @param startTime  格式HH:mm
     * @param endTime  格式HH:mm
     * @return time2 > time1
     */
    public static String getTimeGap(String startTime, String endTime){
        DateFormat df = new SimpleDateFormat("HH:mm");
        //UTC ：Universal Time Coordinated,世界协调时间，又称世界标准时间、世界统一时间。UTC 提供了一种与时区无关（或非特定于时区）的时间。
        //我为了计算小时之间的时间差，不得不用世界标准时
        String tz = "UTC";
        df.setTimeZone(TimeZone.getTimeZone(tz));
        try {
            Date dta = df.parse(startTime);
            Date dtb = df.parse(endTime);
            if(dtb.getTime()<dta.getTime()){
                return "0";
            }
            Long times = (dtb.getTime()-dta.getTime())/1000/60;
            return times.toString();
        }catch(Exception e){
            return "0";
        }
    }

	/**
	 * 拆分日期
	 * @param startDate 开始时间段
	 * @param endDate 结束时间段
	 * @param splitType 分隔时间段类型 Calendar的常量类型，例如：Calendar.DAY_OF_MONTH
	 * @param amount 累加间隔数量
	 * @return
	 */
	public static List<Date> splitDate(Date startDate,Date endDate,int splitType,int amount) {

		List<Date> list = new ArrayList<Date>();

		list.add(startDate);
		if(splitType==Calendar.DAY_OF_MONTH) {
			Date tmpDate=startDate;
			while (tmpDate.getTime() < endDate.getTime()) {
				Calendar calendar = Calendar.getInstance();
				calendar.setTime(tmpDate);
				calendar.add(splitType,amount);
				tmpDate=calendar.getTime();
				if(tmpDate.getTime() < endDate.getTime()){
					list.add(tmpDate);
				}
			}
		}
		list.add(endDate);

		return list;
	}
	
	public static String genXQiniuDate() {
		Date now = new Date();
		return new StringBuilder(DateUtil.format(now, FORMAT_SHORT_BLANK))
				.append(StringPool.T)
				.append(DateUtil.format(now, FORMAT_SECOND))
				.append(StringPool.Z)
				.toString();
	}
	
}

















