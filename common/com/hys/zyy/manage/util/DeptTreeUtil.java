package com.hys.zyy.manage.util;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

import com.hys.zyy.manage.model.dto.InitGpDictValueParam;

public class DeptTreeUtil {
	
	public static List<InitGpDictValueParam> generateTree(List<InitGpDictValueParam> list){
		return generate(list,null,null);
	}
	public static List<InitGpDictValueParam> generateTree(List<InitGpDictValueParam> list,Long parentID){
		return generate(list,parentID,null);
	}
	public static List<InitGpDictValueParam> generateTree(List<InitGpDictValueParam> list,Comparator<InitGpDictValueParam> comparator){
		return generate(list,null,comparator);
	}
	public static List<InitGpDictValueParam> generateTree(List<InitGpDictValueParam> list,Long parentID,Comparator<InitGpDictValueParam> comparator){
		return generate(list,parentID,comparator);
	}
	/**
	 * 生成树
	 * @param list
	 * @return
	 */
	private static List<InitGpDictValueParam> generate(List<InitGpDictValueParam> list,Long parentID,Comparator<InitGpDictValueParam> comparator){
        // 根节点
        List<InitGpDictValueParam> root = new ArrayList();
        for (InitGpDictValueParam node:list){
        	if(parentID==null){
	            if(node.getParentDeptId() == null){
	            	root.add(node);
	            } else {
	            	for (InitGpDictValueParam tempNode:list){
	            		if(node.getParentDeptId().equals(tempNode.getDeptId())){
	            			tempNode.getDeptChildList().add(node);
	            		}
	            	}
	            }
        	}else{
        		 if(parentID.equals(node.getParentDeptId())) {
        			 root.add(node);
 	            } else {
 	            	for (InitGpDictValueParam tempNode:list){
	            		if(node.getParentDeptId().equals(tempNode.getDeptId())){
	            			tempNode.getDeptChildList().add(node);
	            		}
	            	}
 	            }
        	}
        }
        if(comparator!=null){
        	Collections.sort(root, comparator);
        	for(InitGpDictValueParam node:root){
        		node.sortDeptChildList(comparator);
        	}
        }
        return root;
	}
	
	/**
	 * 过时的
	 * 递归生成树  效率较慢 不适合层级过多
	 * @param listNode
	 * @param childNodeList
	 * @param parentId
	 * @return
	 */
	public static List<InitGpDictValueParam> generateTree(List<InitGpDictValueParam> listNode,List<InitGpDictValueParam> childNodeList,Long parentId){
		if(childNodeList==null)
			childNodeList = new ArrayList();
		for(InitGpDictValueParam node : listNode){
			if(node.getParentDeptId().equals(parentId)){
				List<InitGpDictValueParam> childChildNodeList = DeptTreeUtil.generateTree(listNode,node.getDeptChildList(),node.getDeptId());
				node.setDeptChildList(childChildNodeList);
				childNodeList.add(node);
			}
		}
		return childNodeList;
	}
	
}
