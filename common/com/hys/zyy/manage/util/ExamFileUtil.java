package com.hys.zyy.manage.util;

import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.util.Date;
import java.util.Iterator;
import java.util.UUID;

import javax.imageio.ImageIO;
import javax.imageio.ImageReader;
import javax.imageio.stream.ImageInputStream;
import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;

import org.springframework.web.multipart.commons.CommonsMultipartFile;

import com.hys.zyy.manage.constants.ExamQuestionConstants;
import com.hys.zyy.manage.exception.ErrorCode;
import com.hys.zyy.manage.exception.FrameworkRuntimeException;
import com.hys.zyy.manage.model.vo.ZyyExamFileVO;

/**
 * 考试文件工具类
 * 
 * <AUTHOR>
 * @date 2020-4-21下午3:14:21
 */
public class ExamFileUtil {
	/**
	 * 图片后缀的格式检验
	 * 
	 * @param file
	 *            文件
	 * @param imageType
	 *            后缀格式，如"JPEG,png.."
	 * @return true:符合imageType格式; false:不符合
	 * @throws IOException
	 */
	public static boolean checkImageType(File file, String imageType)
			throws IOException {
		if (!file.exists()) {
			return false;
		}
		boolean result = false;
		ImageInputStream iis = ImageIO.createImageInputStream(file);
		Iterator<ImageReader> readers = ImageIO.getImageReaders(iis);
		ImageReader reader = null;
		if (readers.hasNext()) {
			reader = readers.next();
		}
		if (reader.getFormatName().equalsIgnoreCase(imageType)) {
			result = true;
		}
		return result;
	}

	/**
	 * 图片的像素判断
	 * 
	 * @param file
	 *            文件
	 * @param imageWidth
	 *            图片宽度
	 * @param imageHeight
	 *            图片高度
	 * @return true:上传图片宽度和高度都小于等于规定最大值
	 * @throws IOException
	 */
	public static boolean checkImageElement(File file, int imageWidth,
			int imageHeight) throws IOException {
		boolean result = false;
		if (!file.exists()) {
			return false;
		}
		BufferedImage bufferedImage = ImageIO.read(file);
		if (bufferedImage != null && bufferedImage.getHeight() < imageHeight
				&& bufferedImage.getWidth() < imageWidth) {
			result = true;
		}
		return result;
	}

	/**
	 * 检测图片的大小
	 * 
	 * @param file
	 *            文件
	 * @param imageSize
	 *            图片最大值(KB)
	 * @return true:上传图片小于图片的最大值
	 */
	public static boolean checkImageSize(File file, int imageSize) {
		boolean result = false;
		if (!file.exists()) {
			return false;
		}
		if ((file.length() / 1024) > imageSize) {
			result = true;
		}

		return result;
	}

	/**
	 * @desc 获取图片大小（K）
	 * <AUTHOR>
	 * @date 2018-8-2 下午5:43:52
	 */
	public static Long getImageSize(File imageFile) {
		Long imageSize = null;
		if (imageFile.exists()) {
			imageSize = (imageFile.length() / 1024);
		}
		return imageSize;
	}
	
	/**
	 * ajax上传
	 * @param request
	 * @param uploadFile
	 * @param bussinessName  细分业务模块
	 * @return
	 * <AUTHOR>
	 * @date 2020-4-21下午3:45:11
	 */
	public static ZyyExamFileVO uploadExamFile(HttpServletRequest request,CommonsMultipartFile uploadFile,String bussinessName) {
		ZyyExamFileVO result = new ZyyExamFileVO();
		try {
			String fileName = uploadFile.getOriginalFilename();
			// 得到文件名称
			String fname = UUID.randomUUID().toString() + "." + LogicUtils.getExtention(fileName);

			// 得到文件路径
			StringBuilder path = new StringBuilder();
			path.append(ExamQuestionConstants.UPLOAD_FILE_PATH);
			path.append(bussinessName + "/");// 加一个业务的名称 区分不同文件归属的业务
			path.append(DateUtil.format(new Date(), "yyyyMMdd"));
			path.append("/");

			String newPath = path.toString() + fname;

			// 取得服务器绝对路径
			ServletContext context = request.getSession().getServletContext();
			String realPath = context.getRealPath("/").substring(0,
					context.getRealPath("/").length() - 1);

			// 写入绝对路径
			path.insert(0, realPath);
			// 创建本地目录
			LogicUtils.checkFileExist(path.toString());
			String photoPathNew = path.toString() + fname;

			try {
				uploadFile.transferTo(new File(photoPathNew));
			} catch (Exception e) {
				throw new FrameworkRuntimeException(ErrorCode.E0008);
			}
			result.setFileName(fileName);
			result.setFilePath(newPath);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return result;
	}

}
