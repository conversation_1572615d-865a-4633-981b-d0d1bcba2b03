package com.hys.zyy.manage.util;

import com.hys.zyy.manage.constants.ExamQuestionConstants;
import com.hys.zyy.manage.query.Pager;

/**
 * 考试工具类
 * <AUTHOR>
 * @date 2019-12-30下午3:20:25
 */
public class ExamUtil {
	//分页信息
	public static String createPageInfo(String url,
			Pager pager) {
		StringBuffer buffer = new StringBuffer();
		buffer.append(ExamQuestionConstants.EXAM_QUESTION_DOMAIN);
		buffer.append(url);
		buffer.append("?pageNum=");
		buffer.append(pager.getPageOffset());
		buffer.append("&pageSize=");
		buffer.append(pager.getPageSize());
		return buffer.toString();
	}
	//分页信息 pageNo 
	public static String createPageInfoPageNo(String url,
			Pager pager) {
		StringBuffer buffer = new StringBuffer();
		buffer.append(ExamQuestionConstants.EXAM_QUESTION_DOMAIN);
		buffer.append(url);
		buffer.append("?pageNo=");
		buffer.append(pager.getPageOffset());
		buffer.append("&pageSize=");
		buffer.append(pager.getPageSize());
		return buffer.toString();
	}
}
