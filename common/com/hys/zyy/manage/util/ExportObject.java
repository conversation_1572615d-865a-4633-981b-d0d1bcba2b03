package com.hys.zyy.manage.util;

import java.util.List;

/**
 * 导出对像类
 * <AUTHOR>
 *
 * @param <T>
 */
public class ExportObject<T> {
	
	
	/**
	 * 导出属性集合
	 */
	private List<Property> propertyList;
	
	
	/**
	 * 导出数据集合
	 */
	private List<T> objectList;
	
	/**
	 * 取得标题行数
	 * @param property
	 * @return
	 */
	public  int getTitleRowNumber(){
		int i=1;
		if(this.propertyList!=null && this.propertyList.size()>0){
			for(Property property:this.propertyList){
				int number = getTitleRowNumber(property);
				i= i < number ? number : i ;
			}
		}
		return i;
	}
	/**
	 * 取得标题行数
	 * @param property
	 * @return
	 */
	public  int getTitleRowNumber(Property property){
		int i=1;
		if(property.getChildProperty() !=null && property.getChildProperty().size()>0 && property.getIsShowMergeTitle()==1 ){
			for(Property p:property.getChildProperty()){
				int number= getTitleRowNumber(p);
				i= i < (number+1) ? (number+1) : i ;
			}
		}
		else{
			i=1;
		}
		return i;
	}
	
	
	/**
	 * 最得所有子节点最大级别
	 * @return
	 */
	public  int getPropertyMaxLevel(){
		int i=0;
		if(this.propertyList!=null && this.propertyList.size()>0){
			for(Property property:this.propertyList){
				int level = getPropertyMaxLevel(property);
				i= i < level ? level : i ;
			}
		}
		return i;
	}
	
	/**
	 * 最得节点最大级别
	 * @param property
	 * @return
	 */
	public  int getPropertyMaxLevel(Property property){
		int i=1;
		if(property.getChildProperty() !=null && property.getChildProperty().size()>0 && property.getIsShowMergeTitle()==1 ){
			for(Property p:property.getChildProperty()){
				int level= getPropertyMaxLevel(property);
				i= i < level ? level : i ;
			}
		}
		else{
			i=1;
		}
		return i;
	}
	
	
	
	/**
	 * 最得有子节点最大级别
	 * @return
	 */
	public int getPropertyAllChildSize(Property property){
		int i=0;
		return i;
	}
	
	
	
	public List<Property> getPropertyList() {
		return propertyList;
	}

	public void setPropertyList(List<Property> propertyList) {
		this.propertyList = propertyList;
	}


	public List<T> getObjectList() {
		return objectList;
	}

	public void setObjectList(List<T> objectList) {
		this.objectList = objectList;
	}

}
