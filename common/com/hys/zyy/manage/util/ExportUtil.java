package com.hys.zyy.manage.util;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFRichTextString;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.util.CellRangeAddress;

public class ExportUtil<T> {
	
	private HSSFWorkbook workbook=null;
	
	private HSSFSheet sheet=null;
	
	private HSSFCellStyle headStyle ;
	
	private HSSFCellStyle dataStyle ;
	
	/**
	 * 
	 * @param exportEntity
	 * @return
	 */
	public  HSSFWorkbook ObjectToWorkbook(ExportObject<T> exportObject){
		if(exportObject.getPropertyList().size()>0){
			workbook=new HSSFWorkbook();
		    sheet = workbook.createSheet();
		    

			headStyle= workbook.createCellStyle();  
		    headStyle.setAlignment(HSSFCellStyle.ALIGN_CENTER);// 左右居中  
		    headStyle.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);// 上下居中  
		    
		    dataStyle= workbook.createCellStyle();  
		    dataStyle.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);// 上下居中  

		    /* 生成表格标题 */
		    int rowIndex=addTitle(exportObject);
			
			/* 添加数据行*/
			for(T t:exportObject.getObjectList()){
				rowIndex=addDateRow(t,rowIndex,0,exportObject.getPropertyList());
			}
		}
		return workbook;
	}
	
    /**
     * 添加标题行
     * @param exportObject
     * @return
     */
	private int addTitle(ExportObject<T> exportObject){
		int rowNumber=exportObject.getTitleRowNumber();
		int rowIndex=0;
		//标题行集合
		Map<Integer,HSSFRow> titleRow=new HashMap<Integer,HSSFRow>();
		//标题行添加列索引集合
		Map<Integer,Integer> cellIndex=new HashMap<Integer,Integer>();
		for(;rowIndex<rowNumber;rowIndex++){
			HSSFRow row     = sheet.createRow(rowIndex);
			titleRow.put(rowIndex, row);
			cellIndex.put(rowIndex, 0);
		}
		for(Property property:exportObject.getPropertyList()){
			addCellTitle(titleRow,cellIndex,0,property);
			
		}
		return rowIndex;
	}
	
	/**
	 * 添加标题列
	 * @param titleRow
	 * @param cellIndex
	 * @param rowIndex
	 * @param property
	 * @return
	 */
	private Integer addCellTitle(Map<Integer,HSSFRow> titleRow,Map<Integer,Integer> cellIndex,Integer rowIndex,Property property){
		HSSFCell cell=null;
		int cellSize=0;
		//添加没有子节点标题列
		if(property.getChildProperty()==null||property.getChildProperty().size()<1){
			//添加列
			cell=titleRow.get(rowIndex).createCell(cellIndex.get(rowIndex));
			//添标题样式
			cell.setCellStyle(headStyle);
			cell.setCellValue(new HSSFRichTextString(property.getPropertyTitle()));
			//跨行合并
			if(rowIndex<(titleRow.size()-1)){
				sheet.addMergedRegion(new CellRangeAddress(rowIndex, (titleRow.size()-1), cellIndex.get(rowIndex), cellIndex.get(rowIndex)));
				
			}

			//补齐跨行列索引
			for(int i=rowIndex;i<titleRow.size();i++){
				cellIndex.put(i, cell.getColumnIndex()+1);
			}
			cellSize=1;
		}
		//添加有子节点并要求显示合并标题的列
		else if(property.getIsShowMergeTitle()==1){
			//添加列
			cell=titleRow.get(rowIndex).createCell(cellIndex.get(rowIndex));
			//添标题样式
			cell.setCellStyle(headStyle);
			cell.setCellValue(new HSSFRichTextString(property.getPropertyTitle()));
			//添加下级列
			for(Property p:property.getChildProperty()){
				cellSize=cellSize+addCellTitle(titleRow,cellIndex,rowIndex+1,p);
			}
			cellIndex.put(rowIndex, cell.getColumnIndex()+cellSize);
			if(cellSize>1){
				sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, cell.getColumnIndex(), cell.getColumnIndex()+(cellSize-1)));
			}
			
		}
		//添加有子节点并不显示合并标题的列
		else{
			for(Property p:property.getChildProperty()){
				cellSize=cellSize+addCellTitle(titleRow,cellIndex,rowIndex,p);
			}
		}
		return cellSize;
		
	}
	
	/**
	 * 添加数据行
	 * @param object
	 * @param rowIndex
	 * @param colunmIndex
	 * @param propertyList
	 * @return
	 */
	private int addDateRow(Object object,Integer rowIndex,Integer colunmIndex,List<Property> propertyList){
		HSSFRow row  =sheet.getRow(rowIndex)!=null?sheet.getRow(rowIndex++): sheet.createRow(rowIndex++);
		int startRowIndex=row.getRowNum();
		HSSFCell cell=null;
		Map<String,Class<?>> fields=getClassFields(object.getClass(),true);
		Object v=null;
		List<HSSFCell> cells=new ArrayList<HSSFCell>();
		for(Property property:propertyList){
			try{
				//if(fields.get(property.getPropertyName())!=null){
				//	Field field=fields.get(property.getPropertyName()).getDeclaredField(property.getPropertyName());
				//	field.setAccessible(true);
					if(property.getPropertyType()==1){
						if(property.getChildProperty()==null || property.getChildProperty().size()<1){
							cell=row.createCell(colunmIndex++);
							cell.setCellStyle(dataStyle);
							cell.setCellValue(getPropertyValues(object,fields,property));
							cells.add(cell);
						}
						else{
							rowIndex=addDateRow(object,rowIndex-1,colunmIndex,property.getChildProperty());
							colunmIndex=colunmIndex+property.getChildProperty().size();
						}
					}
					else if(property.getPropertyType()==2){
						if(fields.get(property.getPropertyName())!=null){
							Field field=fields.get(property.getPropertyName()).getDeclaredField(property.getPropertyName());
							field.setAccessible(true);
							v=field.get(object);
							if(v!=null){
								List<?> listValue=(List<?>)v;
								for(int i=0;i<listValue.size();i++){
									rowIndex=addDateRow(listValue.get(i),i==0?rowIndex-1:rowIndex,colunmIndex,property.getChildProperty());
								}
							}
							colunmIndex=colunmIndex+property.getColumnSize(property);
						}
					}
					else if(property.getPropertyType()==3){
						cell=row.createCell(colunmIndex++);
						cell.setCellStyle(dataStyle);
						if(fields.get(property.getPropertyName())!=null){
							Field field=fields.get(property.getPropertyName()).getDeclaredField(property.getPropertyName());
							field.setAccessible(true);
							Map<?,?> mapValue=(Map<?,?>)field.get(object);
							cell.setCellValue(getMapValue(mapValue,property));
						}
						cells.add(cell);
					}
				//}
				
			}
			catch(Exception e){
				e.printStackTrace();
			}
		}
		if(startRowIndex<rowIndex-1){
			for(HSSFCell c:cells){
				sheet.addMergedRegion(new CellRangeAddress(startRowIndex, rowIndex-1, c.getColumnIndex(), c.getColumnIndex()));
			}
		}
		return rowIndex;
	}
	
	/**
	 * 从Map对象中反射出相应属性
	 * @param object  对象
	 * @param objectClass 对象类
	 * @param property  属性名
	 * @return
	 */
	private String getMapValue(Map<?,?> mapValue,Property property){
		List<Object> valueList=new ArrayList<Object>();
		if(mapValue!=null){
			Object object=mapValue.get(property.getKey());
			if(object!=null){
				Map<String,Class<?>> fields=getClassFields(object.getClass(),true);
				String [] ps=property.getValueProperty().split(",");
				for(String p:ps){
					if(fields.get(p)!=null){
						try{
							Field field=fields.get(p).getDeclaredField(p);
							field.setAccessible(true);
							valueList.add(field.get(object));
						}
						catch(Exception e){
							
						}
					}
				}
			}
		}
		else{
			valueList.add(null);
		}
		return property.getValue(valueList);
	}
	
	/**
	 * 取得类中所有属性 （包括父类） 
	 * @param clazz 类
	 * @param includeParentClass 是否包括父类
	 * @return 属性与属性所属类  map key  属性 value  属性所属类
	 */
	private  Map<String,Class<?>> getClassFields(Class<?> clazz, boolean includeParentClass )
	{
		Map<String,Class<?>> map = new HashMap<String,Class<?>>();
		Field[] fields = clazz.getDeclaredFields();
		for (Field field : fields )
		{
			map.put(field.getName(),clazz);
		}
		if (includeParentClass)
			getParentClassFields(map,clazz.getSuperclass());
		return map;
	}
	
	/**
	 * 取得父类属性
	 * @param map
	 * @param clazz
	 * @return
	 */
	private  Map<String,Class<?>> getParentClassFields(Map<String,Class<?>> map, Class<?> clazz )
	{
		Field[] fields=clazz.getDeclaredFields();
		for (Field field : fields)
		{
			if(map.get(field.getName())==null){
				map.put(field.getName(),clazz);
			}
		}
		if(clazz.getSuperclass()==null)
		{
			return map;
		}
		getParentClassFields(map,clazz.getSuperclass());
		return map;
	}
	
	/**
	 * 取得值
	 * @param object
	 * @param fields
	 * @param property
	 * @return
	 */
	private String getPropertyValues(Object object,Map<String,Class<?>> fields,Property property){
		String [] ps=property.getPropertyName().split(",");
		List<Object> valueList=new ArrayList<Object>();
		for(String p:ps){
			if(fields.get(p)!=null){
				try{
					Field field=fields.get(p).getDeclaredField(p);
					field.setAccessible(true);
					valueList.add(field.get(object));
				}
				catch(Exception e){
					
				}
			}
		}
		return property.getValue(valueList);
		
	}
	
	
}
