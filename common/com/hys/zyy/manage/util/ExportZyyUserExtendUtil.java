package com.hys.zyy.manage.util;

import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;

import com.hys.zyy.manage.constants.CommonConstants;
import com.hys.zyy.manage.constants.Constants;
import com.hys.zyy.manage.constants.ZyyDictionaryConstant;
import com.hys.zyy.manage.model.StudentReport;
import com.hys.zyy.manage.model.ZyyUserExtendVO;
/**
 * 专供云南 和 云南助理全科 使用
 * <AUTHOR>
 * @date 2018-10-24下午4:53:35
 */
public class ExportZyyUserExtendUtil {

	private static String getZyyUserStatus(Integer zyyUserStatus) {
		String str = "";
		if (zyyUserStatus != null){
			if (zyyUserStatus == 1){
				str = "正常";
			} else if (zyyUserStatus == 7){
				str = "延长毕业";
			} else if (zyyUserStatus == -7){
				str = "结业";
			} else if (zyyUserStatus == -5){
				str = "退培";
			} else if (zyyUserStatus == 11) {
				str = "延期3个月";
			} else if (zyyUserStatus == 12) {
				str = "延期6个月";
			} else if (zyyUserStatus == 13) {
				str = "延期12个月";
			}
		}
		return str;
	}


	//base_id 不是从user_extend表取的，所以不能用*
	public static String getUserExtendSql() {
		StringBuilder builder = new StringBuilder() ;
		builder.append(" t1.sex,t1.birthday,t1.email,t1.job_number,t1.certificate_type,t1.certificate_no,t1.user_post,t1.mobil_number, ");
		builder.append(" t1.family_register,t1.nation,t1.birthplace,t1.political_landscape,t1.is_married,t1.is_fresh,t1.home_place,t1.join_party_date, ");
		builder.append(" t1.highest_record_school,t1.highest_graduate_school,t1.highest_degree,t1.highest_record_prof,t1.highest_school_system,t1.graduation_date, ");
		builder.append(" t1.hobbies,t1.post_during_study,t1.has_certificate,t1.certificate_number,t1.get_date,t1.practice_certificate_number,  ");
		builder.append(" t1.physicians_practicing_cert,t1.work_unit,t1.work_date,t1.get_title_date,t1.get_title,t1.address,t1.address_post_code,  ");
		builder.append(" t1.account_address,t1.account_address_post_code,t1.home_address,t1.home_address_post_code,t1.telphone,t1.emergency_contacter, ");
		builder.append(" t1.emergency_contacter_phone,t1.emergency_contacter_post_code,t1.health_state,t1.height,t1.weight,t1.shoe_size,t1.foreign_language, ");
		builder.append(" t1.computer_skills,t1.other_skills,t1.awards,t1.research_papers,t1.reward_punishment,t1.former_name,t1.repudiations, ");
		builder.append(" t1.graduate_student_status,t1.category_certificate,t1.scope_certificate,t1.undergraduate_type,t1.school_place,  ");
		builder.append(" t1.graduate_type,t1.candidates_type,t1.pre_specialty,t1.recruit_year_id,  ");
		builder.append(" t1.hospital_id,t1.is_old_reg,t1.residency_source,t1.entrust_type,t1.entrust_unit,t1.work_years,  ");
		builder.append(" t1.first_degree,t1.first_graduate_school,t1.first_record_prof,t1.training_period,t1.first_school_system,t1.first_record_school,  ");
		builder.append(" t1.first_graduate_type,t1.student_type,t1.before_for_checkrecord,t1.has_first_record_certificate,t1.first_record_certifi_num,  ");
		builder.append(" t1.has_first_degree_certificate,t1.first_degree_certifi_num,t1.first_degree_type,  ");
		builder.append(" t1.has_highest_degree_certificate,t1.highest_degree_type,t1.has_highest_record_certificate,t1.highest_degree_certifi_num,  ");
		builder.append(" t1.physicians_qualification_level,t1.physicians_qualification_type,t1.computer_level,t1.foreign_language_test_type,  ");
		builder.append(" t1.work_unit_level,t1.work_unit_nature,t1.qq_number,t1.train_school,t1.user_Post_Title,t1.self_evaluation, t1.hosp_type,  ");
		builder.append(" t1.wechat_number,t1.teac_qual_cer_num,t1.teaching_title,t1.DUTY,t1.TARGET_PLACE,t1.get_practice_date, ");
		builder.append(" t1.DOCTOR_CERTIFICATE,t1.CARD_FRONT,t1.CARD_BEHIND,t1.PHOTO_PATH, ");
		builder.append(" t1.graduate_target_type,t1.is_rural_target_free_stu,t1.LIVE_PROVINCE_ID,t1.LIVE_CITY_ID,t1.USER_GROUP,t1.MAJOR,t1.school_type,t1.graduation_code, ");
		builder.append("  (SELECT ja.city_name FROM jc_area ja WHERE ja.id = t1.live_province_id) AS liveProvinceName,  ");
		builder.append("  (SELECT ja.city_name FROM jc_area ja WHERE ja.id = t1.live_city_id) AS liveCityName,  ");
		builder.append("  (SELECT ja.city_name FROM jc_area ja WHERE ja.id = t1.home_province_id) AS homeProvinceName,  ");
		builder.append("  (SELECT ja.city_name FROM jc_area ja WHERE ja.id = t1.home_city_id) AS homeCityName,  ");
		builder.append(" CASE T1.RESIDENCY_SOURCE WHEN 1 THEN '单位人' WHEN 2 THEN '社会人' WHEN 3 THEN '学位衔接' ELSE '未知人员类型' END AS residencySourceStr, ");
		builder.append(" t1.is_pass_qualification_exam ,t1.pass_qualification_exam_time, ");
		builder.append(" t1.educational_type ");

		return builder.toString();
	}

	private static String getForeignLanguageTestType(Integer foreignLanguageTestType) {

		if (Integer.valueOf(1).equals(foreignLanguageTestType)){
			return "全国英语等级考试";
		} else if (Integer.valueOf(2).equals(foreignLanguageTestType)){
			return "大学英语四六级考试";
		} else if (Integer.valueOf(3).equals(foreignLanguageTestType)){
			return "英语专业等级考试";
		} else if (Integer.valueOf(4).equals(foreignLanguageTestType)){
			return "未参加前三项等级考试";
		}
		return "无";
	}


	private static String getComputerLevel(Integer computerLevel) {
		if (Integer.valueOf(1).equals(computerLevel)){
			return "一级";
		} else if (Integer.valueOf(2).equals(computerLevel)){
			return "二级";
		} else if (Integer.valueOf(3).equals(computerLevel)){
			return "三级";
		} else if (Integer.valueOf(4).equals(computerLevel)){
			return "四级";
		} else if (Integer.valueOf(5).equals(computerLevel)){
			return "其他";
		} else if (Integer.valueOf(6).equals(computerLevel)){
			return "未参加考试";
		}
		return "无";
	}


	private static String getPhysiciansQualificationType(
			Integer physiciansQualificationType) {
		if (Integer.valueOf(1).equals(physiciansQualificationType)){
			return "临床";
		} else if (Integer.valueOf(2).equals(physiciansQualificationType)){
			return "口腔";
		} else if (Integer.valueOf(3).equals(physiciansQualificationType)){
			return "公共卫生";
		} else if (Integer.valueOf(4).equals(physiciansQualificationType)){
			return "中医";
		}
		return "无";
	}


	private static String getPhysiciansQualificationLevel(
			Integer physiciansQualificationLevel) {
		if (Integer.valueOf(2).equals(physiciansQualificationLevel)){
			return "执业医师";
		} else if (Integer.valueOf(3).equals(physiciansQualificationLevel)){
			return "助理执业医师";
		}
		return "无";
	}


	private static String toStr(String str){
		return str==null?"":str ;
	}
	private static String toBig(Integer i){
		return i==1?"一":(i==2?"二":"三");
	}
	private static String getSchoolType(Integer schoolType){
		if (schoolType == null){
			schoolType = 0;
		}
		String schoolTypeStr  = "" ;
		switch (schoolType) {
		case 1:
			schoolTypeStr = ( "211工程院校");
			break;
		case 2:
			schoolTypeStr = ( "985工程院校");
			break;
		case 3:
			schoolTypeStr = ( "211工程院校,985工程院校");
			break;
		case 4:
			schoolTypeStr = ( "其他");
			break;
		default:
			schoolTypeStr = ( "无");
			break;
		}
		return schoolTypeStr;
	}

	/**
	 * 获取学历   1 -大学专科 2 -大学本科 3 -硕士研究生 4 -博士研究生 5 -博士后 6 小学 7- 初中 8 高中 9 中专 10 师承,11 其他
	 * @param recordSchool
	 * @return
	 */
	private static String getRecordSchool(int recordSchool){
		switch (recordSchool) {
		case Constants.HIGHEST_RECORD_SCHOOL_1:
			return "大学专科教育";
		case Constants.HIGHEST_RECORD_SCHOOL_2:
			return "大学本科教育";
		case Constants.HIGHEST_RECORD_SCHOOL_3:
			return "硕士研究生";
		case Constants.HIGHEST_RECORD_SCHOOL_4:
			return "博士研究生";
		case Constants.HIGHEST_RECORD_SCHOOL_5:
			return "博士后";
		case 6:
			return "普通高级中学教育";
		case 7:
			return "高中以下";
		case 8:
			return "其他";
		case 9:
			return "中等职业教育";
		}
		return "无";
	}

	//助理全科的 学历
    private static String getRecordSchool2(int recordSchool){
		switch (recordSchool) {
		case 1:
			return "大学专科";
		case 2:
			return "大学本科";
		case 3:
			return "硕士研究生";
		case 4:
			return "博士研究生";
		case 9:
			return "中专";
		}
		return "无";
	}



	/**
	 * 获取学位
	 * @param degree
	 * @return
	 */
	private static String getDegree(int degree){
		switch (degree) {
		case 1:
			return "学士";
		case 2:
			return "硕士";
		case 3:
			return "博士";
		}
		return "无";
	}

	/**
	 * 返回是或否
	 * @param flag
	 * @return
	 */
	private static String getYesOrNo(int flag){
		switch (flag) {
		case 0:
			return "否";
		case 1:
			return "是";
		}
		return "无";
	}

	private static String getHighestRecordProf(String prof){
		String value="";
		if(prof.equals("1")){value="内科学";}
		else if(prof.equals("2")){value="儿科学";}
		else if(prof.equals("3")){value="急诊医学";}
		else if(prof.equals("4")){value="皮肤病与性病学";}
		else if(prof.equals("5")){value="精神病与精神卫生学";}
		else if(prof.equals("6")){value="神经病学";}
		else if(prof.equals("7")){value="全科医学";}
		else if(prof.equals("8")){value="康复医学与理疗学";}
		else if(prof.equals("9")){value="外科学";}
		else if(prof.equals("10")){value="妇产科学";}
		else if(prof.equals("11")){value="眼科学";}
		else if(prof.equals("12")){value="耳鼻咽喉科学";}
		else if(prof.equals("13")){value="麻醉学";}
		else if(prof.equals("14")){value="临床检验诊断学";}
		else if(prof.equals("15")){value="影像医学与核医学";}
		else if(prof.equals("16")){value="口腔临床医学";}
		else if(prof.equals("17")){value="肿瘤学";}
		else if(prof.equals("18")){value="老年医学";}
		else if(prof.equals("19")){value="运动医学";}
		else if(prof.equals("20")){value="中医诊断学";}
		else if(prof.equals("21")){value="中医内科学";}
		else if(prof.equals("22")){value="中医外科学";}
		else if(prof.equals("23")){value="中医骨伤科学";}
		else if(prof.equals("24")){value="中医妇科学";}
		else if(prof.equals("25")){value="中医儿科学";}
		else if(prof.equals("26")){value="中医五官科学";}
		else if(prof.equals("27")){value="针灸推拿学";}
		else if(prof.equals("28")){value="民族医学（含：藏医学、蒙医学等）";}
		else if(prof.equals("29")){value="中西医结合临床";}
		else if(prof.equals("30")){value="其他";}
		else if(prof.equals("31")){value="预防医学";}
		else if(prof.equals("32")){value="医学技术";}
		else if(prof.equals("33")){value="人体解剖和组织胚胎学";}
		else if(prof.equals("34")){value="免疫学";}
		else if(prof.equals("35")){value="病原生物学";}
		else if(prof.equals("36")){value="病理学与病理生理学";}
		else if(prof.equals("37")){value="法医学";}
		else if(prof.equals("38")){value="放射医学";}
		else if(prof.equals("39")){value="航空、航天与航海医学";}
		else if(prof.equals("40")){value="护理学";}
		else if(prof.equals("41")){value="口腔基础医学";}
		else if(prof.equals("42")){value="流行病与卫生统计学";}
		else if(prof.equals("43")){value="劳动卫生与环境卫生学";}
		else if(prof.equals("44")){value="营养与食品卫生学";}
		else if(prof.equals("45")){value="儿少卫生与妇幼保健学";}
		else if(prof.equals("46")){value="卫生毒理学";}
		else if(prof.equals("47")){value="军事预防医学";}
		else if(prof.equals("48")){value="中医基础理论";}
		else if(prof.equals("49")){value="中医临床基础";}
		else if(prof.equals("50")){value="中医医史文献";}
		else if(prof.equals("51")){value="方剂学";}
		else if(prof.equals("52")){value="民族医学（含：藏医学、蒙医学等）";}
		else if(prof.equals("53")){value="中西医结合基础";}
		else if(prof.equals("54")){value="药物化学";}
		else if(prof.equals("55")){value="药剂学";}
		else if(prof.equals("56")){value="生药学";}
		else if(prof.equals("57")){value="药物分析学";}
		else if(prof.equals("58")){value="微生物与生化药学";}
		else if(prof.equals("59")){value="药理学";}
		else if(prof.equals("60")){value="临床病理";}
       return value;
	}

	/**
	 * 云南和河北地区的最高学历为研究生时，存储的最高学历专业是数字，这里进行转换
	 * @param user
	 * @param flag true = zlqk , false = 非zlqk
	 * @return
	 */
	private static String setHigestRecordSchool(ZyyUserExtendVO user,boolean flag){
		String highestRecordProf = user.getHighestRecordProf();
		if(StringUtils.isBlank(highestRecordProf)) {
			return "";
		}

		Integer highestRecordSchool = user.getHighestRecordSchool();
		if(highestRecordSchool != null && (highestRecordSchool.intValue() == 3 || highestRecordSchool.intValue() == 4)) {
			// String value = getHighestRecordProf(highestRecordProf);
			String value = ZyyProfessionUtil.getHighestRecordProf(user);
			//有就返回结果 lzq
			if (StringUtils.isNotBlank(value)){
				highestRecordProf = value;
			}
		}

		return highestRecordProf;
	}

	/**
	 * 云南导出数据构造  -- 人员管理导出
	 * @param userList
	 * @return
	 * <AUTHOR>
	 * @date 2018-11-8上午11:00:19
	 */
	public static List<Map<String,String>> createExportDataYunnan(List<ZyyUserExtendVO> userList,boolean flag,String excelName) {
		List<Map<String,String>> exportData = new ArrayList<Map<String,String>>();
		if (CollectionUtils.isEmpty(userList)){
			return exportData;
		}
	    Map<String,String> row = null;
	    StringBuilder bd = null;
	    int size = userList.size();
	    for (int i = 0; i < size; i++) {
	    	row = new LinkedHashMap<String, String>();
	    	ZyyUserExtendVO user = userList.get(i);
	    	createRowDataByYunan(row, user, flag, excelName,bd);
		    exportData.add(row);
	    }
		return exportData;
	}

	/**
	 * 其他省份导出数据 -- 人员管理导出
	 * @param userList
	 * @return
	 * <AUTHOR>
	 * @param zyyUserP
	 * @date 2018-11-12上午11:44:29
	 */
	public static List<Map<String,String>> createExportDataByOther(List<ZyyUserExtendVO> userList, Long zyyUserProvinceId,Long orgId) {
		List<Map<String,String>> exportData = new ArrayList<Map<String,String>>();
		if (CollectionUtils.isEmpty(userList)){
			return exportData;
		}
	    Map<String,String> row = null;
	    StringBuilder bd = null;
	    int size = userList.size();
	    for (int i = 0; i < size; i++) {
	    	row = new LinkedHashMap<String, String>();
	    	ZyyUserExtendVO user = userList.get(i);
	    	createRowDataByOther(row, user,bd,zyyUserProvinceId,orgId);
		    exportData.add(row);
	    }
		return exportData;
	}

	private static void createRowDataByOther(Map<String, String> row, ZyyUserExtendVO user, StringBuilder bd,Long zyyUserProvinceId,Long orgId) {
		//用户名
		row.put("2", toStr(user.getRealName()));
		//所属机构名
		row.put("3", toStr(user.getOrgAliasName()));
		//是否上传头像
		row.put("3.5", StringUtils.isNotBlank(user.getPhotoPath())?"是":"否");
		//账户名
		row.put("4", toAppendTab(bd, user.getAccountName()));
		//基地/专科
		row.put("5", toStr(user.getBaseAliasName()));
		//手机号
		row.put("6", toAppendTab(bd, user.getMobilNumber()));
		//证件类型
		row.put("7", getCertificateType(user.getCertificateType()));
		// 证件号
		row.put("8", toAppendTab(bd, user.getCertificateNo()));
		// 身份证人身面是否上传
		row.put("9", StringUtils.isNotBlank(user.getCardBehind())?"是":"否");
		// 身份证国徽面是否上传
		row.put("10", StringUtils.isNotBlank(user.getCardFront())?"是":"否");
		//性别
		row.put("11", getSexStr(user.getSex()));
		// 民族
		row.put("12", toStr(user.getNation()));
		//年级
		row.put("13", user.getYear());
		//邮箱
		row.put("14", toStr(user.getEmail()));
		//医师资格证书
		row.put("15", getHasCertificate(user.getHasCertificate()));

		//医师资格证书编码
		row.put("16", toAppendTab(bd,user.getCertificateNumber()));

		//医师执业证书
		row.put("16.5", getPhysiciansPracticingCert(user.getPhysiciansPracticingCert()));
		// 医师执业证书是否上传
		row.put("16.7", StringUtils.isNotBlank(user.getDoctorCertificate())?"是":"否");
		//工作单位
		row.put("17", toStr(user.getWorkUnit()));
		Integer educationalType = user.getEducationalType();
		String educationalTypeStr = "";
		if (educationalType != null && 1 == educationalType){
			educationalTypeStr = "国家计划培养";
		} else if (educationalType != null && 2 == educationalType){
			educationalTypeStr = "省内自主培养";
		}
		row.put("17.5", educationalTypeStr);
		// 医师执业地点
		row.put("17.7", toStr(user.getDoctorPractisePlace()));
		
		// 第一学历
		row.put("17.71", ZyyDictionaryConstant.FIRST_RECORD_SCHOOL_CONSTANT.get(user.getFirstRecordSchool()));
		// 第一学位
		row.put("17.72", ZyyDictionaryConstant.FIRST_DEGREE_CONSTANT.get(user.getFirstDegree()));
		// 最高学历
		row.put("17.73", ZyyDictionaryConstant.HIGHEST_RECORD_SCHOOL_RESIDENCY_CONSTANT.get(user.getHighestRecordSchool()));
		// 最高学位
		row.put("17.74", ZyyDictionaryConstant.HIGHEST_DEGREE_CONSTANT.get(user.getHighestDegree()));
		// 最高学历毕业学校
		row.put("17.75", toStr(user.getHighestGraduateSchool()));
		// 最高学历毕业时间
		row.put("17.77", toAppendTabDate(bd, user.getGraduationDate()));
		// 学员状态
		row.put("17.8", toStr(user.getZyyUserStatusStr()));
		if (!ResidencySourceUtil.isHide()) {
			// 住院医师类别
			row.put("18", getResidencySource(user.getResidencySource()));
			// 订单定向生
			row.put("18.5", getDirectStuStr(user.getDirectStu()));
		}
		//执业医师证书编号
		row.put("19", toAppendTab(bd, user.getPracticeCertificateNumber()));
		// 培训年限
		row.put("20", user.getSchoolSystem() == null ? "" : user.getSchoolSystem() + "年制");

		if(zyyUserProvinceId == 1000000){
			//籍贯
			if(user.getFamilyRegister() == null || user.getFamilyRegister() == ""){
				row.put("21", "");
			}else{
				row.put("21", user.getFamilyRegister());
			}
			// 签约单位
			if(user.getEntrustUnit() == null || user.getEntrustUnit() == ""){
				row.put("22", "");
			}else{
				row.put("22", user.getEntrustUnit());
			}
			// 人员细分
			if(user.getPersonDetail() == null) {
				row.put("22.5", "");
			} else {
				String personDetailStr = "";
				switch (user.getPersonDetail()) {
					case 1: personDetailStr = "全日制硕士专业学位研究生"; break;
					case 2: personDetailStr = "外单位委托培养住院医师"; break;
					case 3: personDetailStr = "本单位住院医师"; break;
					case 4: personDetailStr = "面向社会招收住院医师"; break;
				}
				row.put("22.5", personDetailStr);
			}
		}
		if (CommonConstants.JXGN_ORG_ID.equals(orgId)){
			row.put("23", user.getJobNumber());//工号
		}
	}

	private static String getResidencySource(Integer residencySource) {
		if (residencySource != null && residencySource == 2) {
			return "社会人";
		} else if (residencySource != null && residencySource == 3) {
			return "学位衔接";
		} else if (residencySource != null && residencySource == 1) {
			return "单位人";
		}
		return "";
	}
	
	private static String getDirectStuStr(Integer directStu) {
		String directStuStr = StringPool.BLANK;
		if (directStu == null)
			return directStuStr;
		switch (directStu) {
			case 0: directStuStr = "否"; break;
			case 1: directStuStr = "是"; break;
		}
		return directStuStr;
	}

	private static String getPhysiciansPracticingCert(
			Integer physiciansPracticingCert) {
		if(physiciansPracticingCert == null){
			return "";
		}else if(physiciansPracticingCert == 1){
			return "有";
		}else if(physiciansPracticingCert == 0 || physiciansPracticingCert == 2){
			return "无";
		}
		return "";
	}

	private static String getHasCertificate(Integer hasCertificate) {
		if(hasCertificate == null){
			return "未知";
		}else if(hasCertificate == 1){
			return "有";
		}else if(hasCertificate == 0 || hasCertificate == 2){
			return "无";
		}
		return "";
	}

	private static String getSexStr(Integer sex) {
		if(sex == null || sex == 0 ){
			return "未知";
		}else if (sex == 1){
			return "男";
		}else if (sex == 2){
			return "女";
		}
		return "";
	}

	private static String getCertificateType(Integer certificateType) {
		if(certificateType == null){
			return "其他";
		}else if(certificateType == 1){
			return "身份证";
		}else if(certificateType == 2){
			return "干部证";
		}else if(certificateType == 3){
			return "军官证";
		}else if(certificateType == 4){
			return "军校学员证";
		}else if(certificateType == 5){
			return "护照";
		}else if(certificateType == 6){
			return "回乡证";
		}else if(certificateType == 7){
			return "台胞证";
		}else if(certificateType == 0){
			return "其他";
		}
		return "";
	}

	private static String getCertificateType2(Integer certificateType) {
		if (certificateType == null){
			return "未知";
		}else if(certificateType == 1){
			return "居民身份证";
		}else if(certificateType == 2){
		   return "军官证";
		}else if(certificateType == 3){
			return "护照";
		}else if(certificateType == 4){
			return "港澳通行证";
		}else if(certificateType == 5){
			return "台胞证";
		}
		return "";
	}

	/**
	 * 日期添加制表符
	 * @param bd
	 * @param date
	 * @return
	 * <AUTHOR>
	 * @date 2018-11-8下午6:17:51
	 */
	private static String toAppendTabDate(StringBuilder bd,Date date){
		if (date == null){
			return "";
		}
		bd = new StringBuilder();
		bd.append("\t");
		bd.append(DateUtil.format(date, DateUtil.FORMAT_SHORT));
		return bd.toString();
	}
	private static String toAppendTabDate2(StringBuilder bd,Date date){
		if (date == null){
			return "无";
		}
		bd = new StringBuilder();
		bd.append("\t");
		bd.append(DateUtil.format(date, DateUtil.FORMAT_SHORT));
		return bd.toString();
	}
	/**
	 * 特殊字符串数字添加制表符
	 * @param bd
	 * @param str
	 * @return
	 * <AUTHOR>
	 * @date 2018-11-8下午6:19:17
	 */
	private static String toAppendTab(StringBuilder bd,String str){
		if (str == null){
			return "";
		}
		bd = new StringBuilder();
		bd.append("\t");
		bd.append(str);
		return bd.toString();
	}

	private static void createRowDataByYunan(Map<String,String> row,ZyyUserExtendVO user,boolean flag,String excelName,StringBuilder bd) {
		//一个方法太多了,拆分了
		createRowData1(row,user,bd);
		createRowData2(row,user,flag,bd);
		createRowData3(row,user,flag,bd);
		createRowData4(row,user,flag,bd);
		createRowData5(row,user,flag,bd);
		createRowData6(row,user,flag,bd);

		//紧急联系人姓名	紧急联系人联系电话	健康状况（既往病史）	工作单位所在州（市）	工作单位所在县（区）	报到情况
    	row.put("79", toStr(user.getEmergencyContacter()));
    	row.put("80",toAppendTab(bd, user.getEmergencyContacterPhone()));
    	row.put("81", toStr(user.getHealthState()));
    	Integer residencySource = user.getResidencySource();
		if (residencySource != null && Constants.RESIDENCY_SOURCE_1 == residencySource){
			row.put("82", toStr(user.getLiveProvinceName()));
	    	row.put("83", toStr(user.getLiveCityName()));
		}else{
			row.put("82", "");
	    	row.put("83", "");
		}
		Integer residencySource2 = user.getResidencySource2();
		if (residencySource2 != null && residencySource2.intValue() == 2){
			row.put("84", "无");
		} else {
			row.put("84", Integer.valueOf(21).equals(user.getZyyUserType()) ?"已报到":"未报到");
		}
		if (CommonConstants.USER_INFO.equals(excelName)){
			row.put("85", getZyyUserStatus(user.getZyyUserStatus()));
		}

	}
	//2016年度、2017年度、2018年度、2019年度、 尚未报名
	private static String getFirstApplyLicensedDoctorStr(
			Integer firstApplyLicensedDoctor) {
		String str = "";
		if (firstApplyLicensedDoctor != null){
			if (firstApplyLicensedDoctor == 0){
				str = "尚未报名";
			}else if (firstApplyLicensedDoctor == 1){
				str = "2016年度";
			} else if (firstApplyLicensedDoctor == 2){
				str = "2017年度";
			} else if (firstApplyLicensedDoctor == 2){
				str = "2018年度";
			} else if (firstApplyLicensedDoctor == 2){
				str = "2019年度";
			}
		}
		return str;
	}


	private static void createRowData6(Map<String, String> row,
			ZyyUserExtendVO user, boolean flag, StringBuilder bd) {
		//有无助理医师资格证书（有/无）	助理医师资格证书取得时间 	助理医师资格证书编码	有无助理医师执业医师证书（有/无）	助理医师执业医师证书取得时间	助理医师执业医师证书编码	助理医师执业医师证书执业范围	助理医师执业医师证书执业类别
		if (flag){
			row.put("65", "");
	    	row.put("66", "");
	    	row.put("67", "");
	    	row.put("68", "");
	    	row.put("69", "");
	    	row.put("70", "");
	    	row.put("71", "");
	    	row.put("72", "");
		}else{
			//zlqk
			row.put("65",  user.getHasCertificate() != null && user.getHasCertificate() == 1? "有" : "无");
	    	row.put("66", toAppendTabDate2(bd, user.getGetDate()));
	    	row.put("67", toAppendTab(bd, user.getCertificateNumber()));
	    	row.put("68",  user.getPhysiciansPracticingCert() != null && user.getPhysiciansPracticingCert() == 1 ? "有" : "无");
	    	row.put("69", toAppendTabDate2(bd, user.getGetPracticeDate()));
	    	row.put("70",  toAppendTab(bd, user.getPracticeCertificateNumber()));
	    	row.put("71",  toStr(user.getScopeCertificate()));
	    	row.put("72",  toStr(user.getCategoryCertificate()));
		}
		//计算机能力	其他	外语等级考试类型	外语能力	技术职称	工作单位通讯地址/通讯地址
		// 重复
		if (flag){
			row.put("73", getComputerLevel(user.getComputerLevel())); //计算机能力
		}else{
			//zlqk
			row.put("73", toStr(user.getComputerSkills())); //计算机能力
		}
		row.put("74", toStr(user.getOtherSkills()));//其他特长爱好

		// 重复
		if(flag){
			row.put("75", getForeignLanguageTestType(user.getForeignLanguageTestType()));
		}else{
			row.put("75", "");
		}
		row.put("76", toStr(user.getForeignLanguage()));
		row.put("77", toStr(user.getGetTitle()));
    	row.put("78", toStr(user.getAddress()));

	}

	private static void createRowData5(Map<String, String> row,
			ZyyUserExtendVO user, boolean flag, StringBuilder bd) {
		//最高学位培养学制	最高学位类型	最高学位是否获得证书（是/否）	最高学位证书编号	是否应届（是/否）	是否有研究生学籍（是/否）
    	row.put("49", user.getHighestDegree() == null|| user.getHighestDegree().equals(-1)||user.getHighestSchoolSystem() == null|| user.getHighestSchoolSystem().equals(-1) ? "无" : user.getHighestSchoolSystem().toString()+"年");
		if (flag){
			row.put("50", user.getHighestDegreeType()==null?"":(user.getHighestDegreeType()==1?"专业型":"科学型"));
	    	row.put("51", Integer.valueOf(1).equals(user.getHasHighestDegreeCertificate()) ?"是":"否");
	    	row.put("52", toAppendTab(bd, user.getHighestDegreeCertifiNum()));
		} else {
			row.put("50", "");
	    	row.put("51", "");
	    	row.put("52", "");
		}
		row.put("53", user.getIsFresh()==null?"否":(user.getIsFresh()==1?"是":"否"));
    	row.put("54", user.getGraduateStudentStatus() == null ? "无" : getYesOrNo(user.getGraduateStudentStatus()));
		//是否获得医师资格证书（是/否）	医师资格级别	医师资格类别    “ 首次报名执业医师考试时间” 	取得医师资格证书时间	医师资格证书编码   “是否通过医师资格考试” “通过医师资格考试时间” 	有无医师执业证书   执业证书取得时间	执业证书编码	证书执业范围	证书执业类别
		// 重复
		if (flag){
			row.put("55", user.getHasCertificate() != null && user.getHasCertificate() == 1? "是" : "否");
			row.put("55.5", StringUtils.isNotEmpty(user.getDoctorCertificate())?"是":"否");
			row.put("56", getPhysiciansQualificationLevel( user.getPhysiciansQualificationLevel()));

			row.put("57", getPhysiciansQualificationType( user.getPhysiciansQualificationType()));
			//新增的一列值 -- lzq
	    	row.put("86", getFirstApplyLicensedDoctorStr(user.getFirstApplyLicensedDoctor()));
	    	row.put("58",  toAppendTabDate2(bd, user.getGetDate()));
	    	row.put("59",  toAppendTab(bd, user.getCertificateNumber()));
	    	//新增两列 值  -- lzq
	    	row.put("87", user.getIsPassQualificationExam()== null ? "" : getYesOrNo(user.getIsPassQualificationExam()));
	    	row.put("88", toAppendTabDate2(bd,user.getPassQualificationExamTime()));
	    	row.put("60",  user.getPhysiciansPracticingCert() == null ? "" : getYesOrNo(user.getPhysiciansPracticingCert()));
	    	row.put("61", toAppendTabDate2(bd, user.getGetPracticeDate()));
	    	row.put("62", toAppendTab(bd, user.getPracticeCertificateNumber()));
	    	row.put("63",  toStr(user.getScopeCertificate()));
			//证书执业类别
	    	row.put("64",  toStr(user.getCategoryCertificate()));
		}else{
			row.put("55", "");
	    	row.put("56", "");
	    	row.put("57", "");
	    	row.put("58", "");
	    	row.put("59", "");
	    	row.put("60", "");
	    	row.put("61", "");
	    	row.put("62", "");
	    	row.put("63", "");
	    	row.put("64", "");
		}

	}

	private static void createRowData4(Map<String, String> row,
			ZyyUserExtendVO user, boolean flag, StringBuilder bd) {
		//第一学位类型		第一学位是否获得证书（是/否）		第一学位证书编号		最高学历    	最高学历毕业学校		最高学历专业
		if (flag){
			row.put("37", user.getFirstDegreeType()==null?"":(user.getFirstDegreeType()==1?"专业型":"科学型") );
	    	row.put("38", user.getHasFirstDegreeCertificate()==null?"否":(user.getHasFirstDegreeCertificate()==1?"是":"否"));
	    	row.put("39", toAppendTab(bd, user.getFirstDegreeCertifiNum()));
	    	row.put("40", user.getHighestRecordSchool()==null?"":getRecordSchool(user.getHighestRecordSchool()));
		} else {
			row.put("37", "");
	    	row.put("38", "");
	    	row.put("39", "");
	    	row.put("40", user.getHighestRecordSchool()==null?"":getRecordSchool2(user.getHighestRecordSchool()));
		}
		row.put("41", toStr(user.getHighestGraduateSchool()));
    	row.put("42", setHigestRecordSchool(user,flag));
		//最高学历毕业时间	最高学历毕业院校（非本地高校/本地高校）	最高学历是否获得证书（是/否）	最高学历毕业院校毕业分类	最高学历毕业证书编号	最高学位
    	row.put("43", toAppendTabDate(bd, user.getGraduationDate()));
    	row.put("44", user.getSchoolPlace()==null?"非本地高校":(user.getSchoolPlace()==1?"本地高校":"非本地高校"));
		if (flag){
			row.put("45", user.getHasHighestRecordCertificate()==null?"否":(user.getHasHighestRecordCertificate()==1?"是":"否"));
		} else{
			row.put("45", "");
		}
		row.put("46", getSchoolType(user.getSchoolType()));
    	row.put("47", toAppendTab(bd, user.getGraduationCode()));
    	row.put("48", user.getHighestDegree() == null ? "" : getDegree(user.getHighestDegree()));

	}

	private static void createRowData3(Map<String, String> row, ZyyUserExtendVO user, boolean flag, StringBuilder bd) {
		//培训年限	培训年级	是否订单定向（是/否）	第一学历	第一学历毕业学校	第一学历专业
    	row.put("25", user.getSchoolSystem()==null?"":user.getSchoolSystem().toString()+"年制");
    	row.put("26", toStr(user.getYear()));
    	// row.put("27", Constants.STUDENT_TYPE_TARGET.equals(user.getGraduateTargetType())?"是":"否");
		String lable = "";
		Integer isRuralTargetFreeStu = user.getIsRuralTargetFreeStu();
		if (isRuralTargetFreeStu != null) {
			if (isRuralTargetFreeStu == 1)
				lable = "是";
			else if (isRuralTargetFreeStu == 0)
				lable = "否";
		}
		row.put("27", lable);
		if (flag){
			row.put("28", user.getFirstRecordSchool()==null?"":getRecordSchool(user.getFirstRecordSchool()));
		} else {
			//zlqk
			row.put("28", user.getFirstRecordSchool()==null?"":getRecordSchool2(user.getFirstRecordSchool()));
		}
		row.put("29", toStr(user.getFirstGraduateSchool()));
    	row.put("30", toStr(user.getFirstRecordProf()));
		//第一学历毕业时间	第一学历是否获得证书（是/否）	第一学历毕业证书编号	第一学位	第一学位培养学制	第一学历培养学制
		if (flag){
			row.put("31", toAppendTabDate(bd, user.getTrainingPeriod()));
	    	row.put("32", user.getHasFirstRecordCertificate()==null?"否":(user.getHasFirstRecordCertificate()==1?"是":"否"));
	    	row.put("33", toAppendTab(bd, user.getFirstRecordCertifiNum()));
	    	row.put("34", user.getFirstDegree() == null ? "无" : getDegree(user.getFirstDegree()) );
	    	row.put("35", user.getFirstSchoolSystem() == null|| user.getFirstSchoolSystem().equals(-1)? "无" : user.getFirstSchoolSystem().toString()+"年" );
	    	row.put("36", "");
		} else{
			//zlqk
			row.put("31", "");
	    	row.put("32", "");
	    	row.put("33", "");
	    	row.put("34", "");
	    	row.put("35", "");
	    	row.put("36", user.getFirstSchoolSystem() == null|| user.getFirstSchoolSystem().equals(-1)? "无" : user.getFirstSchoolSystem().toString()+"年" );
		}
	}

	private static void createRowData2(Map<String, String> row,
			ZyyUserExtendVO user, boolean flag, StringBuilder bd) {
		//定向地	证件类型	证件号码	移动电话	固定电话	Email
    	row.put("13", toStr(user.getTargetPlace()));
    	row.put("14", user.getCertificateType().equals(1)?"身份证  ":(user.getCertificateType().equals(1)?"军官证":(user.getCertificateType().equals(1)?"护照":"其他")));
		row.put("14.5", toAppendTab(bd, user.getCertificateNo()));
		row.put("15",StringUtils.isNotBlank(user.getCardBehind())?"是":"否");
		row.put("15.5",StringUtils.isNotBlank(user.getCardFront())?"是":"否");
    	row.put("16", toAppendTab(bd, user.getMobilNumber()));
    	row.put("17", toAppendTab(bd, user.getTelphone()));
    	row.put("18", toStr(user.getEmail()));
		//QQ号码	人员类型/助理医师来源      工作单位	培养学校	培训基地	培训专业
		if (flag){
			row.put("19", toAppendTab(bd, user.getQqNumber()));
		} else{
			row.put("19", "");
		}
		Integer educationalType = user.getEducationalType();
		String educationalTypeStr = "";
		if (educationalType != null && 1 == educationalType){
			educationalTypeStr = "国家计划培养";
		} else if (educationalType != null && 2 == educationalType){
			educationalTypeStr = "省内自主培养";
		}
		row.put("19.5", educationalTypeStr);
		row.put("20", user.getResidencySource() == null ?"" : (user.getResidencySource() == Constants.RESIDENCY_SOURCE_1 ?"单位人" : (user.getResidencySource() == Constants.RESIDENCY_SOURCE_3 ? "学位衔接" :"社会人" )));
		Integer residencySource = user.getResidencySource();
		if (residencySource != null && Constants.RESIDENCY_SOURCE_1 == residencySource){
			row.put("21", toStr(user.getWorkUnit()));
		} else {
			row.put("21", "");
		}
		row.put("22", toStr(user.getTrainSchool()));
    	row.put("23", toStr(user.getOrgName()));
    	row.put("24", toStr(user.getBaseAliasName()));
	}

	private static void createRowData1(Map<String, String> row,
			ZyyUserExtendVO user, StringBuilder bd) {
		// 姓名	性别	出生日期	曾用名	民族	婚姻 -（已婚/未婚）
		row.put("1", toStr(user.getRealName()));
		row.put("2", user.getSex()==null?"女":(user.getSex()==1?"男":"女"));
    	row.put("3", toAppendTabDate(bd, user.getBirthday()));
    	row.put("4", toStr(user.getFormerName()));
		// todo
		row.put("5", StringUtils.isNotEmpty(user.getPhotoPath())?"是":"否");
		row.put("5.5", toStr(user.getNation()));
    	row.put("6", user.getIsMarried()==null?"未婚":(user.getIsMarried()==1?"已婚":"未婚"));
		//籍贯	政治面貌	入党（团）时间	生源地 -（本地/非本地）	生源地-市	生源地-县
    	row.put("7", toStr(user.getFamilyRegister()));
    	row.put("8", toStr(user.getPoliticalLandscape()));
    	row.put("9", toAppendTabDate(bd, user.getJoinPartyDate()));
    	row.put("10",user.getHomePlace() != null && user.getHomePlace() == 1 ? "本地" : "非本地" );
    	row.put("11",toStr(user.getHomeProvinceName()));
    	row.put("12",toStr(user.getHomeCityName()));
	}

	//人员管理导出excel表头
	public static String createExportDataYunnanHead(String excelName){
		StringBuilder bd = new StringBuilder();
		bd.append("姓名,性别,出生日期,曾用名,个人照片是否上传,民族,婚姻（已婚/未婚）,籍贯,政治面貌,入党（团）时间,生源地（本地/非本地）,生源地-市,生源地-县,定向地,");
		bd.append("证件类型,证件号码,身份证人像页是否上传,身份证国徽页是否上传,移动电话,固定电话,Email,QQ号码,培养类型,人员类型/助理医师来源,工作单位,培养学校,培训基地,专业基地,培训年限,培训年级,");
		bd.append("是否为农村订单定向免费医学毕业生,第一学历,第一学历毕业学校,第一学历专业,第一学历毕业时间,第一学历是否获得证书（是/否）,第一学历毕业证书编号,");
		bd.append("第一学位,第一学位培养学制,第一学历培养学制,第一学位类型,第一学位是否获得证书（是/否）,第一学位证书编号,最高学历,最高学历毕业学校,");
		bd.append("最高学历专业,最高学历毕业时间,最高学历毕业院校（非本地高校/本地高校）,最高学历是否获得证书（是/否）,最高学历毕业院校毕业分类,");
		bd.append("最高学历毕业证书编号,最高学位,最高学位培养学制,最高学位类型,最高学位是否获得证书（是/否）,最高学位证书编号,是否应届（是/否）,");
		bd.append("是否有研究生学籍（是/否）,是否获得医师资格证书（是/否）,医师资格证扫描件是否上传,医师资格级别,医师资格类别,取得医师资格证书时间,医师资格证书编码,");
		bd.append("有无医师执业证书,执业证书取得时间,执业证书编码,证书执业范围,证书执业类别,有无助理医师资格证书（有/无）,助理医师资格证书取得时间 ,");
		bd.append("助理医师资格证书编码,有无助理医师执业医师证书（有/无）,助理医师执业医师证书取得时间,助理医师执业医师证书编码,助理医师执业医师证书执业范围,");
		bd.append("助理医师执业医师证书执业类别,计算机能力,其他,外语等级考试类型,外语能力,技术职称,工作单位通讯地址/通讯地址,紧急联系人姓名,紧急联系人联系电话,");
		bd.append("健康状况（既往病史）,工作单位所在州（市）,工作单位所在县（区）,报到情况");
		if (CommonConstants.USER_INFO.equals(excelName)){
			bd.append(",状态");
		}
		return bd.toString();
	}
	//人员管理导出excel表头
	public static String createExportDataYunnanHead2(String excelName){
		StringBuilder bd = new StringBuilder();
		bd.append("姓名,性别,出生日期,曾用名,个人照片是否上传,民族,婚姻（已婚/未婚）,籍贯,政治面貌,入党（团）时间,生源地（本地/非本地）,生源地-市,生源地-县,定向地,");
		bd.append("证件类型,证件号码,身份证人像页是否上传,身份证国徽页是否上传,移动电话,固定电话,Email,QQ号码,培养类型,人员类型/助理医师来源,工作单位,培养学校,培训基地,专业基地,培训年限,培训年级,");
		bd.append("是否为农村订单定向免费医学毕业生,第一学历,第一学历毕业学校,第一学历专业,第一学历毕业时间,第一学历是否获得证书（是/否）,第一学历毕业证书编号,");
		bd.append("第一学位,第一学位培养学制,第一学历培养学制,第一学位类型,第一学位是否获得证书（是/否）,第一学位证书编号,最高学历,最高学历毕业学校,");
		bd.append("最高学历专业,最高学历毕业时间,最高学历毕业院校（非本地高校/本地高校）,最高学历是否获得证书（是/否）,最高学历毕业院校毕业分类,");
		bd.append("最高学历毕业证书编号,最高学位,最高学位培养学制,最高学位类型,最高学位是否获得证书（是/否）,最高学位证书编号,是否应届（是/否）,");
		bd.append("是否有研究生学籍（是/否）,是否获得医师资格证书（是/否）,医师资格证扫描件是否上传,医师资格级别,医师资格类别,首次报名执业医师考试时间,取得医师资格证书时间,医师资格证书编码,");
		bd.append("是否通过医师资格考试,通过医师资格考试时间,");//新增两个字段导出 lzq
		bd.append("有无医师执业证书,执业证书取得时间,执业证书编码,证书执业范围,证书执业类别,有无助理医师资格证书（有/无）,助理医师资格证书取得时间 ,");
		bd.append("助理医师资格证书编码,有无助理医师执业医师证书（有/无）,助理医师执业医师证书取得时间,助理医师执业医师证书编码,助理医师执业医师证书执业范围,");
		bd.append("助理医师执业医师证书执业类别,计算机能力,其他,外语等级考试类型,外语能力,技术职称,工作单位通讯地址/通讯地址,紧急联系人姓名,紧急联系人联系电话,");
		bd.append("健康状况（既往病史）,工作单位所在州（市）,工作单位所在县（区）,报到情况");
		if (CommonConstants.USER_INFO.equals(excelName)){
			bd.append(",状态");
		}
		return bd.toString();
	}
	//人员管理导出excel表头
	public static String createExportDataOtherHead(Long zyyUserProvinceId,Long orgId){
		StringBuilder bd = new StringBuilder();
		bd.append("用户名,所属机构名,个人照片是否上传,账户名,基地/专科,手机号,证件类型,证件号码,身份证人像页是否上传,身份证国徽页是否上传,性别,民族,年级,邮箱,医师资格证书,医师资格证书编码,医师执业证书,医师资格证扫描件是否上传,工作单位,培养类型,医师执业地点,第一学历,第一学位,最高学历,最高学位,最高学历毕业学校,最高学历毕业时间,状态");
		if(!ResidencySourceUtil.isHide()){
			bd.append(",住院医师类别,订单定向生");
		}
		bd.append(",执业医师证书编号,培训年限");
		if(zyyUserProvinceId == 1000000){
			bd.append(",籍贯,签约单位,人员细分");
		}
		if (CommonConstants.JXGN_ORG_ID.equals(orgId)){
			bd.append(",工号");
		}
		return bd.toString();
	}

	/**
	 * @desc 考试平台人员管理导出Excel表头
	 * <AUTHOR>
	 * @date 2019-1-15  下午5:05:00
	 */
	public static String createExportExcelHeader() {
		return new StringBuilder("姓名,性别,证件类型,证件号码,培训年级,专业基地,培训年限,电子邮箱,移动电话").toString();
	}

	/**
	 * 学员报到导出
	 * @param list
	 * @return
	 * <AUTHOR>
	 * @date 2018-11-12下午2:38:07
	 */
	public static List<Map<String, String>> createExportDataByReport(
			List<StudentReport> userList) {
		List<Map<String,String>> exportData = new ArrayList<Map<String,String>>();
		if (CollectionUtils.isEmpty(userList)){
			return exportData;
		}
	    Map<String,String> row = null;
	    StringBuilder bd = null;
	    int size = userList.size();
	    for (int i = 0; i < size; i++) {
	    	row = new LinkedHashMap<String, String>();
	    	StudentReport user = userList.get(i);
	    	createRowDataByReport(row, user,bd,i);
		    exportData.add(row);
	    }
		return exportData;
	}

	private static void createRowDataByReport(Map<String, String> row,
			StudentReport user, StringBuilder bd,int num) {
		bd = new StringBuilder();
		bd.append(num+1);
		row.put("1", toAppendTab(bd, bd.toString()));
		if (NewRecruitUtil.validate() || HbzlqkRecruitUtil.validate()) {
			Integer status = user.getStatus();
			Integer state = user.getState();
			String reportMsg = "";
			if (status == Constants.ZYY_RECRUIT_WILL_STATUS_YBD)
				reportMsg = "已报到";
			else if (status == Constants.ZYY_RECRUIT_WILL_STATUS_YLQ)
				reportMsg = state == null ? "未操作" : "未报到";
			row.put("2", reportMsg);
		} else
			row.put("2", Constants.ZYY_RECRUIT_WILL_STATUS_YLQ == user.getStatus() ? "未报到":"已报到");
		row.put("3", toStr(user.getRealName()));
		row.put("4", getSexStr(user.getSex()));
		row.put("5", getCertificateType2(user.getCertificateType()));
		row.put("6", toAppendTab(bd, user.getCertificateNo()));
		row.put("7", getResidencySource(user.getResidencySource()));
		row.put("8", toStr(user.getHospName()));
		row.put("9", toStr(user.getBaseName()));
		row.put("10", getHasCertificate(user.getHasCertificate()));
		row.put("11", toAppendTab(bd, user.getCertificateNumber()));
		row.put("12", toAppendTab(bd, user.getMobilNumber()));
	}

	//学员报到excel表头
	public static String createExportDataByReport(){
		StringBuilder bd = new StringBuilder();
		bd.append("序号,报到状态,姓名,性别,证件类型,证件号码,人员类型,录取基地,录取专业,有无医师资格证书,医师资格证书编码,移动电话");
		return bd.toString();
	}

}
