package com.hys.zyy.manage.util;

import java.util.List;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;

/**
 * alibaba fastjson的工具类
 * <AUTHOR>
 * @date 2019-8-15下午3:26:11
 */
public class FastJsonUtil {

	/**
	 * list 转为为对应的类
	 * @param list
	 * @param beanClass
	 * @return
	 * <AUTHOR>
	 * @date 2019-8-15下午3:28:00
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public static <T> List<T> toListBean(List list,TypeReference beanClass) {
		if (CollectionUtils.isEmpty(list) || beanClass == null){
			return null;
		}
		String jsonstr = JSON.toJSONString(list); // List转json
		return (List<T>) JSON.parseObject(jsonstr,beanClass); // Json 转List
	}

}
