package com.hys.zyy.manage.util;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import org.apache.log4j.LogManager;
import org.apache.log4j.Logger;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.hys.security.util.SecurityUtils;
import com.hys.zyy.manage.facade.ZyyRecruitStageFacade;
import com.hys.zyy.manage.model.ZyyUser;
import com.hys.zyy.manage.model.ZyyUserExtendVO;
import com.hys.zyy.manage.service.ZyyRecruitYearManage;

/**
 * 河北助理全科招录工具类
 * <AUTHOR>
 */
public class HbzlqkRecruitUtil {
	
	private static Logger logger = LogManager.getLogger(HbzlqkRecruitUtil.class.getName());
	
	private static final String RESIDENCY_REPORT_DATE_MAP = "residencyReportDateMap";
	
	// 省ID
	public static final Long[] PROVINCE_IDS = new Long[] { 1300000L };
	
	// 限制操作学员报到情况的用户类型
	public static final Integer[] OPESTUREP_USER_TYPE = new Integer[] { 
		5 // 医院用户
	};
	
	/**
	 * @desc 验证当前用户所在省是否为河北助理全科
	 * <AUTHOR>
	 * @date 2021-8-19 10:20:06
	 */
	public static boolean validate(Long provinceId) {
		if (provinceId == null)
			return false;
		return Arrays.asList(PROVINCE_IDS).contains(provinceId);
	}
	
	public static boolean validate(ZyyUser zyyUser) {
		if (zyyUser == null)
			return false;
		return validate(zyyUser.getZyyUserProvinceId());
	}
	
	public static boolean validate(ZyyUserExtendVO zyyUser) {
		return validate((ZyyUser) zyyUser);
	}

	public static boolean validate() {
		ZyyUserExtendVO zyyUser = SecurityUtils.getCurrentUser();
		return validate(zyyUser);
	}
	
	/**
	 * 给定几个时间段，判断当前时间是否在这几个时间段之间
	 */
	public static boolean nowBetweenDates(List<Map<String, Date>> dates) {
		boolean result = false;
		if (CollectionUtils.isEmpty(dates))
			return result;
		Date now = new Date();
		for (Map<String, Date> dateMap : dates) {
			if (DateUtil.between(now, dateMap.get("startTime"), dateMap.get("endTime"))) {
				result = true;
				break;
			}
		}
		return result;
	}
	
	/**
	 * @desc 获取某省历年学员报到时间 
	 * <AUTHOR>
	 * @date 2020-6-23  下午1:59:36
	 */
	@SuppressWarnings({ "unchecked" })
	public static Map<String, List<Map<String, Date>>> getResiReportDateMap() {
		Map<String, List<Map<String, Date>>> result = null;
		HttpServletRequest req = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
		ZyyUserExtendVO zyyUser = SecurityUtils.getCurrentUser();
		HttpSession session = req.getSession();
		String key = zyyUser.getZyyUserProvinceId() + "_" + RESIDENCY_REPORT_DATE_MAP;
		Object obj = session.getAttribute(key);
		if (obj != null)
			result = (Map<String, List<Map<String, Date>>>) obj;
		else {
			result = SpringUtils.getSingleBeanOfType(ZyyRecruitStageFacade.class).getResiReportDateMap();
			session.setAttribute(key, result);
		}
		return result;
	}
	
	/**
	 * @desc 判断当前登录用户是否可以操作学员报到情况：
	 *  1、该省为河北助理全科；
	 * 	2、非医院用户不予限制；
	 *  3、当前时间在招录年度的学员报到时间范围内；
	 *  4、往年度学员不能进行操作；
	 * <AUTHOR>
	 * @date 2020-6-22  上午11:04:25
	 */
	public static boolean opeStuRep(Long zyyRecruitYearId){
		boolean result = true;
		// 判断是否使用了新招录
		if (!HbzlqkRecruitUtil.validate())
			return result;
		ZyyUserExtendVO zyyUser = SecurityUtils.getCurrentUser();
		if(zyyUser == null)
			return result;
		Integer zyyUserType = zyyUser.getZyyUserType();
		Long zyyUserProvinceId = zyyUser.getZyyUserProvinceId();
		// 判断用户类型
		if (Arrays.asList(OPESTUREP_USER_TYPE).contains(zyyUserType)) {
			// 当前开启年度ID
			Long yearId = SpringUtils.getSingleBeanOfType(ZyyRecruitYearManage.class).getCurrentOpenRecruitYear(zyyUserProvinceId).getId();
			if (!yearId.equals(zyyRecruitYearId))
				result = false;
			else {
				Map<String, List<Map<String, Date>>> resiReportDateMap = HbzlqkRecruitUtil.getResiReportDateMap();
				String key = zyyRecruitYearId + "_" + RecruitContextHolder.getHospitalType();
				List<Map<String, Date>> dateList = resiReportDateMap.get(key);
				result = HbzlqkRecruitUtil.nowBetweenDates(dateList);
			}
		}
		return result;
	}
	
}



















