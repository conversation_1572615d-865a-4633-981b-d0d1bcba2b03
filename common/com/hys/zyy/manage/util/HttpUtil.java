package com.hys.zyy.manage.util;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.URL;
import java.net.URLConnection;
import java.util.List;
import java.util.Map;

import org.apache.http.HttpEntity;
import org.apache.http.HttpHeaders;
import org.apache.http.NameValuePair;
import org.apache.http.ParseException;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import com.hys.zyy.manage.constants.Constants;
import com.hys.zyy.manage.constants.ExamQuestionConstants;

/**
 * Created by lich on 2017/8/10. http 请求
 */
public class HttpUtil {

	private static CloseableHttpClient httpClient = HttpClients.createDefault();

	/**
	 * 发送post请求
	 *
	 * @param url
	 * @param requestBody
	 * @return
	 * <AUTHOR>
	 * @date 2019-12-17下午5:44:37
	 */
	public static String doPostHttpRequest(String url, String requestBody) {
		String entityStr = null;
		CloseableHttpResponse response = null;
		try {
			HttpPost post = new HttpPost(url);
			// 添加头部信息
			post.addHeader("content-type", "application/json;charset=UTF-8");
			post.addHeader("token", ExamQuestionConstants.WXMINI_TOKEN_ZYY);
			post.addHeader(HttpHeaders.USER_AGENT, Constants.ZYY_USER_AGENT);
			HttpEntity entity =  new StringEntity(requestBody, "UTF-8");
			post.setEntity(entity);
			response = httpClient.execute(post);
			// 获得响应的实体对象
			HttpEntity httpEntity = response.getEntity();
			// 使用Apache提供的工具类进行转换成字符串
			entityStr = EntityUtils.toString(httpEntity, "UTF-8");
		} catch (ClientProtocolException e) {
			e.printStackTrace();
		} catch (ParseException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}
		return entityStr;
	}

	/**
	 * url 中带参数
	 * @param url
	 * @return
	 */
	public static String doPostHttpRequestOnlyUrl(String url) {
		String entityStr = null;
		CloseableHttpResponse response = null;
		try {
			HttpPost post = new HttpPost(url);
			// 添加头部信息
			post.addHeader("content-type", "application/json;charset=UTF-8");
			post.addHeader("token", ExamQuestionConstants.WXMINI_TOKEN_ZYY);
			post.addHeader(HttpHeaders.USER_AGENT, Constants.ZYY_USER_AGENT);
			response = httpClient.execute(post);
			// 获得响应的实体对象
			HttpEntity httpEntity = response.getEntity();
			// 使用Apache提供的工具类进行转换成字符串
			entityStr = EntityUtils.toString(httpEntity, "UTF-8");
		} catch (ClientProtocolException e) {
			e.printStackTrace();
		} catch (ParseException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}
		return entityStr;
	}

	public static String doPostHttpRequestFormUrlencoded(String url,
			List<NameValuePair> params) {
		String entityStr = null;
		CloseableHttpResponse response = null;
		try {
			HttpPost post = new HttpPost(url);
			// 添加头部信息
			post.addHeader("content-type", "application/x-www-form-urlencoded");
			post.addHeader("token", ExamQuestionConstants.WXMINI_TOKEN_ZYY);
			post.addHeader(HttpHeaders.USER_AGENT, Constants.ZYY_USER_AGENT);
			// HttpEntity entity = new StringEntity(requestBody, "Utf-8");
			post.setEntity(new UrlEncodedFormEntity(params, "UTF-8"));
			response = httpClient.execute(post);
			// 获得响应的实体对象
			HttpEntity httpEntity = response.getEntity();
			// 使用Apache提供的工具类进行转换成字符串
			entityStr = EntityUtils.toString(httpEntity, "UTF-8");
		} catch (ClientProtocolException e) {
			e.printStackTrace();
		} catch (ParseException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}
		return entityStr;
	}
	
	public static String doGet(String url) {
		String result = "";
		BufferedReader in = null;
		try {
			URL realUrl = new URL(url);
			URLConnection connection = realUrl.openConnection();
			connection.setRequestProperty("accept", "*/*");
			connection.setRequestProperty("connection", "Keep-Alive");
			connection.setRequestProperty(HttpHeaders.USER_AGENT, "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
			connection.connect();
			in = new BufferedReader(new InputStreamReader(connection.getInputStream(), "Utf-8"));
			String line;
			while ((line = in.readLine()) != null)
				result += line;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			try {
				if (in != null)
					in.close();
			} catch (Exception ex) {
				ex.printStackTrace();
			}
		}
		return result;
	}

	/**
	 * 发送GET请求
	 *
	 * @param url
	 * @param param
	 * @return
	 */
	public static String sendGet(String url, String param) {
		String result = "";
		BufferedReader in = null;
		try {
			String urlNameString = "";
			if (StringUtils.isEmpty(param)){
				urlNameString = url + "?token="+ExamQuestionConstants.WXMINI_TOKEN_ZYY;
			} else {
				urlNameString = url + "?" + param+ "&token="+ExamQuestionConstants.WXMINI_TOKEN_ZYY;;
			}
			URL realUrl = new URL(urlNameString);
			// 打开和URL之间的连接
			URLConnection connection = realUrl.openConnection();
			// 设置通用的请求属性
			connection.setRequestProperty("accept", "*/*");
			connection.setRequestProperty("connection", "Keep-Alive");
			connection.setRequestProperty(HttpHeaders.USER_AGENT,
					"Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");

			// 建立实际的连接
			connection.connect();
			// 获取所有响应头字段
			Map<String, List<String>> map = connection.getHeaderFields();
			// 遍历所有的响应头字段
			// for (String key : map.keySet()) {
			// System.out.println(key + "--->" + map.get(key));
			// }
			// 定义 BufferedReader输入流来读取URL的响应
			in = new BufferedReader(new InputStreamReader(
					connection.getInputStream(), "Utf-8"));
			String line;
			while ((line = in.readLine()) != null) {
				result += line;
			}
		} catch (Exception e) {
			System.out.println("发送GET请求出现异常！" + e);
			e.printStackTrace();
		}
		// 使用finally块来关闭输入流
		finally {
			try {
				if (in != null) {
					in.close();
				}
			} catch (Exception e2) {
				e2.printStackTrace();
			}
		}
		return result;
	}

}
