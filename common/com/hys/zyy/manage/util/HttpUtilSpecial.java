package com.hys.zyy.manage.util;

import java.io.IOException;

import org.apache.http.HttpEntity;
import org.apache.http.HttpHeaders;
import org.apache.http.ParseException;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import com.hys.zyy.manage.constants.Constants;

/**
 * Created by lich on 2017/8/10. http 请求
 */
public class HttpUtilSpecial {

	private static CloseableHttpClient httpClient = HttpClients.createDefault();

	/**
	 * 发送post请求
	 * 
	 * @param url
	 * @param requestBody
	 * @return
	 * <AUTHOR>
	 * @date 2019-12-17下午5:44:37
	 */
	public static String doPostHttpRequest(String url, String requestBody) {
		String entityStr = null;
		CloseableHttpResponse response = null;
		try {
			HttpPost post = new HttpPost(url);
			// 添加头部信息
			post.addHeader("content-type", "application/json;charset=UTF-8");
			post.addHeader(HttpHeaders.USER_AGENT, Constants.ZYY_USER_AGENT);
			HttpEntity entity =  new StringEntity(requestBody, "UTF-8");
			post.setEntity(entity);
			response = httpClient.execute(post);
			// 获得响应的实体对象
			HttpEntity httpEntity = response.getEntity();
			// 使用Apache提供的工具类进行转换成字符串
			entityStr = EntityUtils.toString(httpEntity, "UTF-8");
		} catch (ClientProtocolException e) {
			e.printStackTrace();
		} catch (ParseException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}
		return entityStr;
	}
}
