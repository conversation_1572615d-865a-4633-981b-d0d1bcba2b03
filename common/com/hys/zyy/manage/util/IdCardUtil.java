package com.hys.zyy.manage.util;

import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class IdCardUtil {

	/**
	 * 通过身份证号码获取出生日期、性别、年龄
	 * 返回的出生日期格式：Date
	 * 性别格式：1：男，2：女
	 */
	public static Map<String, Object> getBirthdayAgeSex(String certificateNo) {
		Map<String, Object> result = new HashMap<String, Object>();
		String birthday = null;
		Integer age = null, sex = null;
		int year = Calendar.getInstance().get(Calendar.YEAR);
		char[] number = certificateNo.toCharArray();
		boolean flag = true;
		if (number.length == 15) {
			for (int x = 0; x < number.length; x++) {
				if (!flag)
					return result;
				flag = Character.isDigit(number[x]);
			}
		} else if (number.length == 18) {
			for (int x = 0; x < number.length - 1; x++) {
				if (!flag)
					return result;
				flag = Character.isDigit(number[x]);
			}
		}
		if (flag && certificateNo.length() == 15) {
			birthday = "19" + certificateNo.substring(6, 8) + "-" + certificateNo.substring(8, 10) + "-" + certificateNo.substring(10, 12);
			sex = Integer.parseInt(certificateNo.substring(certificateNo.length() - 3, certificateNo.length())) % 2 == 0 ? 2 : 1;
			age = (year - Integer.parseInt("19" + certificateNo.substring(6, 8)));
		} else if (flag && certificateNo.length() == 18) {
			birthday = certificateNo.substring(6, 10) + "-" + certificateNo.substring(10, 12) + "-" + certificateNo.substring(12, 14);
			sex = Integer.parseInt(certificateNo.substring(certificateNo.length() - 4, certificateNo.length() - 1)) % 2 == 0 ? 2 : 1;
			age = (year - Integer.parseInt(certificateNo.substring(6, 10)));
		}
		result.put("birthday", DateUtil.parse(birthday, "yyyy-MM-dd"));
		result.put("age", age);
		result.put("sex", sex);
		return result;
	}

	public static void main(String[] args) {
		Map<String, Object> result = getBirthdayAgeSex("330302198711162412");
		System.out.println(DateUtil.format((Date)result.get("birthday")) + ", " + result.get("sex"));
	}

}
