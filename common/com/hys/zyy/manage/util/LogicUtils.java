package com.hys.zyy.manage.util;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Random;

import javax.servlet.ServletContext;

import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.LogManager;
import org.apache.log4j.Logger;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;

import com.hys.zyy.manage.constants.Constants;
import com.hys.zyy.manage.facade.ZyyCycleMonthSettingFacade;
import com.hys.zyy.manage.facade.ZyyOrgFacade;
import com.hys.zyy.manage.model.ZyyCycleMonthSetting;
import com.hys.zyy.manage.model.ZyyCycleMonthSettingVO;
import com.hys.zyy.manage.model.ZyyCycleTableResiCycleVO;
import com.hys.zyy.manage.model.ZyyCycleTableResiTime;
import com.hys.zyy.manage.model.ZyyDate;
import com.hys.zyy.manage.model.ZyyOrgVO;
import com.hys.zyy.manage.model.ZyyRecruitStageVO;
import com.hys.zyy.manage.model.ZyyRecruitYear;
import com.hys.zyy.manage.model.ZyyUser;
import com.hys.zyy.manage.model.ZyyUserExtendVO;
import com.hys.zyy.manage.query.ZyyCycleTableQuery;

/**
 *
 * 标题：住院医师
 *
 * 作者：张伟清 2011-10-21
 *
 * 描述：逻辑工具类
 *
 * 说明:
 */
public class LogicUtils {

	private static Logger logger = LogManager.getLogger(LogicUtils.class.getName());

	public static final String PATTERN_DEV = "dev";//开发环境  本地开发人员使用

	public static final String PATTERN_PROD = "prod";//线上环境  生产环境运维使用

	public static final String PATTERN_TEST = "test";//测试环境  测试人员使用

	private static ZyyOrgFacade zyyOrgFacade;

	private static ZyyCycleMonthSettingFacade zyyCycleMonthSettingFacade ;

	public void setZyyOrgFacade(ZyyOrgFacade zyyOrgFacade) {
		LogicUtils.zyyOrgFacade = zyyOrgFacade;
	}

	public static void setZyyCycleMonthSettingFacade(ZyyCycleMonthSettingFacade zyyCycleMonthSettingFacade) {
		LogicUtils.zyyCycleMonthSettingFacade = zyyCycleMonthSettingFacade;
	}

	//判断上传文件类型
	public static boolean isValidateFile(final String fileName, String[] allowFile){
		if(fileName == null || fileName.length() < 1){
			return false;
		}
		return ArrayUtils.contains(allowFile, FilenameUtils.getExtension(fileName).toLowerCase());
	}

	/**
	 * 取得文件扩展名
	 */
	public static String getExtention(String fileName) {
		int pos = fileName.lastIndexOf(".") + 1;
		return fileName.substring(pos).toLowerCase();
	}

	/**
	 * 检查目录是否存在 没有存在 建立目录
	 */
	public static void checkFileExist(String path) {
		try {
			File file = new File(path);

			if (!file.exists()) {
				file.mkdirs();
			}
		} catch (Exception e) {
		}
	}

	/**
	 * 写文件
	 */
	public static void copyFile(byte[] content, File dst) {
		OutputStream out = null;
		try {
			out = new BufferedOutputStream(new FileOutputStream(dst), 1024);
			out.write(content);
			out.flush();
		} catch (Exception e) {
		} finally {
			if (out != null) {
				try {
					out.close();
				} catch (IOException e) {
				}
			}
		}
	}

	/**
	 * 计算结束时间
	 */
	public static Date addTimeByType(Date startTime, int realTime, int type){
		Date time = null ;
		Calendar c = Calendar.getInstance();
		c.setTime(startTime) ;

		if(type == 3){//按月轮转
			c.add(Calendar.MONTH, realTime) ;
			time = addTime(c.getTime(), -1) ;
		}

		if(type == 2 || type == 1){//按天或周 轮转
			c.add(Calendar.DAY_OF_MONTH, type == 1 ? realTime : realTime * 7) ;
			time = addTime(c.getTime(), -1) ;
		}

		if(type == 4){//按半月轮转
			//取得当前时间的月中
			Date halfMonth = DateUtil.parse(DateUtil.format(startTime, "yyyy-MM") + "-15", "yyyy-MM-dd") ;
			if(startTime.getTime() <= halfMonth.getTime()){
				time = halfMonth ;
//				time = addTime(c.getTime(), 14) ;
			}else{
				//取得当前时间最后一天
				time = Utils.getMonthLastDay(startTime) ;
			}
		}

		return time ;
	}

	/**
	 * 判断半月时间是上半月 还是下半月
	 * @param startTime
	 * @return flag 0 上半月 1 下半月
	 */
	public static Integer checkHalfMonth2(Date startTime){
		Calendar c = Calendar.getInstance();
		c.setTime(startTime) ;

		Date halfMonth = DateUtil.parse(DateUtil.format(startTime, "yyyy-MM") + "-15", "yyyy-MM-dd") ;
		//上半月
		if(startTime.getTime() <= halfMonth.getTime()){
			return 0 ;
		}else{
			// 下半月为当前时间最后一天
			return 1 ;
		}
	}
	
	/**
	 * 获取上下半月文字描述 
	 * null or 0=上半月， 1=下半月，2=特殊轮转周期
	 */
	public static String getHalfMonthStr(Integer halfMonth) {
		String str = "";
		if (halfMonth == null)
			str = "(上)";
		else if (halfMonth == 0)
			str = "(上)";
		else if (halfMonth == 1)
			str = "(下)";
		return str;
	}

	/**
	 * 判断半月时间是上半月 还是下半月
	 * @param startTime
	 * @return
	 */
	public static int checkHalfMonth(Date startTime){
		//取得当前时间的天
		int day = getDayInCurrentTime(startTime) ;
		//1.表示是上半月 1->15号, >1或(16)表示下半月 也就是16-月末
		// return day == 1 ? 0 : 1 ;
		
		if (day == 1)
			return 0; // 上半月
		else if (day == 16)
			return 1; // 下半月
		else
			return 2; // 特殊轮转周期（轮转开始时间既不是1号也不是16号）
	}

	//判断时间处于上半月 还是下半月
	public static Integer checkStartAndEnd(Date startDate, Date endDate){
		if(startDate == null || endDate == null){
			return null ;
		}
		//判断上、下半月
		int days = DateUtil.getStartAndEndDays(startDate, endDate) ;
		if(days < 27){
			return checkHalfMonth(startDate) ;
		}
		return null ;
	}

	/**
	 * 计算时间
	 */
	public static Date addTime(Date time, int value){
		Calendar c = Calendar.getInstance();
		c.setTime(time) ;
		c.add(Calendar.DAY_OF_MONTH, value) ;
		return c.getTime() ;
	}

	/**
	 * 增加月份
	 *
	 * @param time
	 * @param value
	 * @return
	 */
	public static Date addMonth(Date time, int value){
		Calendar c = Calendar.getInstance();
		c.setTime(time) ;
		c.add(Calendar.MONTH, value) ;
		return c.getTime() ;
	}

	//得到下月1号
	public static Date getNextMonthFirstDay(Date time){
		Calendar c = Calendar.getInstance();
		c.setTime(time) ;
		c.add(Calendar.MONTH, 1) ;
		c.set(Calendar.DATE, 1);
		c.set(Calendar.HOUR, 0);
		c.set(Calendar.MINUTE, 0);
		c.set(Calendar.SECOND, 0);
		return c.getTime() ;
	}

	//取得当前时间是几号
	public static int getDayInCurrentTime(Date date){
		Calendar calendar = Calendar.getInstance() ;
		calendar.setTime(date) ;
		return calendar.get(Calendar.DAY_OF_MONTH) ;
	}

	/**
	 * 判断学员轮转时间是否重合
	 * @param list
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	public static int checkStudentCycleVO(List<ZyyCycleTableResiCycleVO> list, Date startTime, Date endTime) {
		int count = 0 ;

		if(Utils.isListEmpty(list)){
			return count ;
		}

		for (ZyyCycleTableResiCycleVO cycle : list) {
			if((cycle.getStartDate().getTime() <= startTime.getTime() &&
					cycle.getEndDate().getTime() >= startTime.getTime())
					||
					(cycle.getStartDate().getTime() <= endTime.getTime() &&
							cycle.getEndDate().getTime() >= endTime.getTime())
							||
							(cycle.getStartDate().getTime() >= startTime.getTime() &&
									cycle.getEndDate().getTime() <= endTime.getTime())){

				count++ ;
			}
		}

		return count ;
	}

	//随机取科室信息
	public static ZyyCycleTableResiTime randomDept(List<ZyyCycleTableResiTime> depts){
		if(depts.size() == 1)
			return depts.get(0) ;

		//根据比较器排序轮转信息
		Collections.sort(depts, MyComparator.deptAsc) ;

		List<ZyyCycleTableResiTime> timeList = new ArrayList<ZyyCycleTableResiTime>() ;
		ZyyCycleTableResiTime time = depts.get(0) ;
		timeList.add(time) ;
		for (int i = 1; i < depts.size(); i++) {
			ZyyCycleTableResiTime temp = depts.get(i) ;
			if(time.getDeptNumber().equals(temp.getDeptNumber())){
				timeList.add(temp) ;
			}
		}

		if(timeList.size() == 1)
			return timeList.get(0) ;

		//首先随机排序List
		Collections.shuffle(timeList) ;

		//2次随机一个新数据
		Random random = new Random() ;
		int value = random.nextInt(timeList.size() - 1) ;

		return timeList.get(value) ;
	}

	/**
	 * 根据轮转每月占比 取得轮转科室
	 * @param tableTime	【轮转表】起始时间
	 * @param startDate	【轮转】起始时间
	 * @param depts		需要随机的科室列表
	 * @param cycleMap	全局的各科室轮转信息
	 * @return
	 */
	public static ZyyCycleTableResiTime randomDept2(Date tableTime, Date startDate, List<ZyyCycleTableResiTime> depts,
			Map<Long, List<ZyyCycleTableResiCycleVO>> cycleMap){

		if(depts.size() == 1)
			return depts.get(0) ;

		//根据比较器排序轮转信息
		Collections.sort(depts, MyComparator.deptAsc) ;

		List<ZyyCycleTableResiTime> timeList = new ArrayList<ZyyCycleTableResiTime>() ;
		ZyyCycleTableResiTime resiTime = depts.get(0) ;
		timeList.add(resiTime) ;
		for (int i = 1; i < depts.size(); i++) {
			ZyyCycleTableResiTime temp = depts.get(i) ;
			if(resiTime.getDeptNumber().equals(temp.getDeptNumber())){
				timeList.add(temp) ;
			}
		}

		if(timeList.size() == 1)
			return timeList.get(0) ;

		int firstVal = 0, endval = 0 ;
		for (ZyyCycleTableResiTime time : timeList) {
			Date beginDate = LogicUtils.addMonth(startDate, -1) ;
			List<ZyyCycleTableResiCycleVO> cycleList = cycleMap.get(time.getDeptId()) ;
			if(beginDate.compareTo(tableTime) >= 0){
				//计算起始月份人数总量
				Date endTime = addTimeByType(beginDate, 1, 3) ;
				//起始月份人员数量
				int coincide = checkStudentCycleVO(cycleList, beginDate, endTime) ;
				if(coincide - time.getDeptNumber() > 0){
					firstVal = coincide - time.getDeptNumber() ;
				}else{
					firstVal = 0 ;
				}
			}

			//取得当前科室轮转结束时间
			Date currDeptDate = addTime(addTimeByType2(startDate, time, time.getCycleType()), 1) ;
			Date endDate = addTimeByType(currDeptDate, 1, 3) ;
			//结束月份人员数量
			int coincide = checkStudentCycleVO(cycleList, currDeptDate, endDate) ;
			if(coincide - time.getDeptNumber() > 0){
				endval = coincide - time.getDeptNumber() ;
			}else{
				endval = 0 ;
			}

			double deptRatio = (endval + firstVal) / 2 ;
			time.setDeptRatio(deptRatio) ;
		}

		//创建比较器
		Comparator<ZyyCycleTableResiTime> myComp1 = new Comparator<ZyyCycleTableResiTime>(){
			@Override
			public int compare(ZyyCycleTableResiTime time1, ZyyCycleTableResiTime time2) {
				return time1.getDeptRatio().intValue() - time2.getDeptRatio().intValue();
			}
		};


		//根据比较器排序轮转信息
		Collections.sort(timeList, myComp1) ;

		return timeList.get(0) ;
	}

	/**
	 * 根据轮转每月占比 取得轮转科室
	 * @param startDate	【轮转】起始时间
	 * @param depts		需要随机的科室列表
	 * @param cycleMap	全局的各科室轮转信息
	 * @return
	 */
	public static ZyyCycleTableResiCycleVO randomDept3(Date startDate, List<ZyyCycleTableResiCycleVO> depts,
			Map<Long, List<ZyyCycleTableResiCycleVO>> cycleMap, Map<Long, Integer> avgMap){

		if(depts.size() == 1)
			return depts.get(0) ;

		for (ZyyCycleTableResiCycleVO cycle : depts) {
			List<ZyyCycleTableResiCycleVO> cycleList = cycleMap.get(cycle.getDeptId()) ;
			Date endDate = addMonth(startDate, 1) ;
			int val = checkStudentCycleVO(cycleList, startDate, endDate) ;
			cycle.setDeptRatio(val) ;
		}

		Collections.sort(depts, MyComparator.cycleRatAsc) ;

		List<ZyyCycleTableResiCycleVO> timeList = new ArrayList<ZyyCycleTableResiCycleVO>() ;
		ZyyCycleTableResiCycleVO time = depts.get(0) ;
		timeList.add(time) ;
		for (int i = 1; i < depts.size(); i++) {
			ZyyCycleTableResiCycleVO temp = depts.get(i) ;
			if(time.getDeptRatio().equals(temp.getDeptRatio())){
				timeList.add(temp) ;
			}
		}

		if(timeList.size() == 1)
			return timeList.get(0) ;

		//首先随机排序List
		Collections.shuffle(timeList) ;

		//2次随机一个新数据
		Random random = new Random() ;
		int value = random.nextInt(timeList.size() - 1) ;

		return depts.get(value) ;
	}

	//设置轮转信息
	public static ZyyCycleTableResiCycleVO setStudentCycle(Date start, Date end, ZyyCycleTableResiTime resi, ZyyCycleTableQuery query){
		ZyyCycleTableResiCycleVO zyyStu = new ZyyCycleTableResiCycleVO() ;
		zyyStu.setCycleTableId(resi.getCycleTableId()) ;
		zyyStu.setResidencyId(resi.getResidencyId()) ;
		zyyStu.setDeptId(resi.getDeptId()) ;
		zyyStu.setCycleType(resi.getCycleType()) ;
		zyyStu.setCycleTime(resi.getNewCycleTime()) ;
		zyyStu.setHalfMonthFlag(resi.isHalfMoonFlag()) ;
		zyyStu.setStartDate(start) ;
		zyyStu.setEndDate(end) ;
		zyyStu.setIsContDept(resi.getIsContDept()) ;
		zyyStu.setIsAuto(Constants.IS_AUTO) ;
		zyyStu.setStatus(Constants.ZYY_CYCLE_TABLE_STATUS_1) ;
		zyyStu.setCycleStatus(Constants.STUDENT_CYCLE_TIME_ZERO) ;
		zyyStu.setScheduler(query.getZyyUser().getId()) ;
		zyyStu.setLastScheduler(query.getZyyUser().getId()) ;

		return zyyStu ;
	}

	//移除科室信息
	public static void removeDeptList(ZyyCycleTableResiCycleVO cycle, List<ZyyCycleTableResiTime> resiList){
		for (Iterator<ZyyCycleTableResiTime> iterator = resiList.iterator(); iterator.hasNext();) {
			ZyyCycleTableResiTime resi = iterator.next();

			if(resi.getDeptId().longValue() == cycle.getDeptId().longValue()){
				iterator.remove() ;
				break ;
			}
		}
	}

	//移除住院医师信息
	public static void removeResiDencyList(ZyyUserExtendVO extend, List<ZyyUserExtendVO> resiList){
		for (Iterator<ZyyUserExtendVO> iterator = resiList.iterator(); iterator.hasNext();) {
			ZyyUserExtendVO resi = iterator.next();
			if(resi.getId().longValue() == extend.getId()){
				iterator.remove() ;
				break ;
			}
		}
	}

	//取出轮转时间的最大结束时间
	public static Date checkAllCycleTime(List<ZyyCycleTableResiCycleVO> list){
		if(Utils.isListEmpty(list))
			return null ;

		//根据比较器排序轮转信息
		Collections.sort(list, MyComparator.cycleVoDesc) ;

		return list.get(0).getEndDate() ;
	}

	//取得轮转时间最短的住院医师
	public static ZyyUserExtendVO getMinCycletimeResidency(List<ZyyUserExtendVO> userList){
		//创建比较器
		Comparator<ZyyUserExtendVO> compare = new Comparator<ZyyUserExtendVO>(){
			@Override
			public int compare(ZyyUserExtendVO user1, ZyyUserExtendVO user2) {
				return user1.getStartDate().compareTo(user2.getStartDate());
	    	}
		};

		//根据比较器排序轮转信息
		Collections.sort(userList, compare) ;

		return userList.get(0) ;
	}

	/**
	 * 取出适合轮转的科室信息
	 * @param startDate	轮转起始时间
	 * @param cycleMap	科室轮转信息
	 * @param timeList	住院医师轮转科室时间列表
	 * @return
	 */
	public static List<ZyyCycleTableResiTime> getZyyCycleDept(Date startDate, List<ZyyCycleTableResiTime> timeList,
			Map<Long, List<ZyyCycleTableResiCycleVO>> cycleMap){

		List<ZyyCycleTableResiTime> retList = new ArrayList<ZyyCycleTableResiTime>() ;

		//取得优先级科室
		List<ZyyCycleTableResiTime> deptList = getPriorCycleDept(timeList) ;
		if(!Utils.isListEmpty(deptList)){
//			retList = getDeptInfo(deptList, cycleMap, startDate) ;
			retList = getDeptRatioInfo(deptList, cycleMap, startDate) ;
			if(!Utils.isListEmpty(retList)){
				return retList ;
			}
		}

//		//取得满足最小人数的轮转科室
//		retList = getDeptInfo(timeList, cycleMap, startDate) ;
//		if(!Utils.isListEmpty(retList)){
//			return retList ;
//		}

		//取得满足科室占比的轮转科室
		return getDeptRatioInfo(timeList, cycleMap, startDate) ;
	}

	//取得满足最小人数的轮转科室
	public static List<ZyyCycleTableResiTime> getDeptInfo(List<ZyyCycleTableResiTime> deptList, Map<Long, List<ZyyCycleTableResiCycleVO>> cycleMap, Date startDate){
		List<ZyyCycleTableResiTime> retList = new ArrayList<ZyyCycleTableResiTime>() ;

		Date endDate = null ;
		List<ZyyCycleTableResiCycleVO> cycleList = null ;
		for (ZyyCycleTableResiTime time : deptList) {
			//取得科室轮转信息
			cycleList = cycleMap.get(time.getDeptId()) ;
			//计算科室轮转结束时间
			endDate = addTimeByType2(startDate, time, time.getCycleType()) ;

			int deptNumber = checkStudentCycleVO(cycleList, startDate, endDate) ;
			if(time.getDeptMinNumber() - deptNumber > 0){
				time.setDeptNumber(deptNumber) ;
				retList.add(time) ;
			}
		}

		return retList ;
	}

	//取得满足科室占比的轮转科室
	public static List<ZyyCycleTableResiTime> getDeptRatioInfo(List<ZyyCycleTableResiTime> deptList, Map<Long, List<ZyyCycleTableResiCycleVO>> cycleMap, Date startDate){
		if(deptList.size() == 1){
			return deptList ;
		}

		Date endDate = null ;
		List<ZyyCycleTableResiCycleVO> cycleList = null ;
		List<ZyyCycleTableResiTime> retList = new ArrayList<ZyyCycleTableResiTime>() ;
		for (ZyyCycleTableResiTime time : deptList) {
			//取得科室轮转信息
			cycleList = cycleMap.get(time.getDeptId()) ;
			//计算科室轮转结束时间
			endDate = addTimeByType2(startDate, time, time.getCycleType()) ;

			//科室容纳人数
			int deptNumber = checkStudentCycleVO(cycleList, startDate, endDate) ;
			//科室占比
			Double deptRatio = Double.valueOf(deptNumber) / time.getDeptMaxNumber() ;
			time.setDeptRatio(deptRatio) ;
		}

		//创建比较器
		Comparator<ZyyCycleTableResiTime> compare = new Comparator<ZyyCycleTableResiTime>(){
			@Override
			public int compare(ZyyCycleTableResiTime time1, ZyyCycleTableResiTime time2) {
				if(time1.getDeptRatio().doubleValue() > time2.getDeptRatio()){
					return 1 ;
				}else if(time1.getDeptRatio().doubleValue() == time2.getDeptRatio()){
					return 0 ;
				}else{
					return -1 ;
				}
	    	}
		};

		Collections.sort(deptList, compare) ;

		retList.add(deptList.get(0)) ;

		return retList ;
	}

	//取出优先轮转科室信息
	public static List<ZyyCycleTableResiTime> getPriorCycleDept(List<ZyyCycleTableResiTime> timeList){
		try {
			//得到新拷贝轮转科室信息
			List<ZyyCycleTableResiTime> retList = Utils.copyArrayList(timeList) ;
			for (Iterator<ZyyCycleTableResiTime> iterator = retList.iterator(); iterator.hasNext();) {
				ZyyCycleTableResiTime time = iterator.next();

				if(time.getDeptPriorLevel() <= 0){
					//移除非优先级科室信息
					iterator.remove() ;
				}
			}
			return retList ;
		} catch (Exception e) {
			return null ;
		}
	}

	//设置页面初始化 时间
	public static List<ZyyCycleTableResiCycleVO> checkDateList(Date startTime, Date endTime){
		//返回结果集
		List<ZyyCycleTableResiCycleVO> dateList = new ArrayList<ZyyCycleTableResiCycleVO>() ;

		ZyyCycleTableResiCycleVO cycle = null ;
		Date endDate = DateUtil.parse(endTime, "yyyy-MM") ;
		do{
			Calendar calendar = Calendar.getInstance() ;
			calendar.setTime(startTime) ;

			cycle = new ZyyCycleTableResiCycleVO() ;
			cycle.setStartDate(Utils.getMonthFirstDay(startTime)) ;
			cycle.setEndDate(Utils.getMonthLastDay(startTime)) ;
			cycle.setMonth(calendar.get(Calendar.MONTH) + 1) ;
			cycle.setYear(String.valueOf(calendar.get(Calendar.YEAR))) ;
			cycle.setMonthWeeks(DateUtil.calculateMonthWeeksByDate(Calendar.MONDAY, cycle.getStartDate(), "yyyy-MM-dd")) ;
			dateList.add(cycle) ;

			startTime = addMonth(calendar.getTime(), 1) ;
		}while(startTime.compareTo(endDate) <= 0) ;

		return dateList ;
	}

	//设置页面初始化时间,按周
	public static List<ZyyCycleTableResiCycleVO> checkDateListByWeek(Date startTime, Date endTime){
		List<ZyyCycleTableResiCycleVO> dateList = new ArrayList<ZyyCycleTableResiCycleVO>();
		ZyyCycleTableResiCycleVO cycle = null;
		Date endDate = DateUtil.parse(endTime, "yyyy-MM-dd") ;
		do{
			Calendar calendar = Calendar.getInstance();
			calendar.setTime(startTime);

			cycle = new ZyyCycleTableResiCycleVO();
			cycle.setStartDate(startTime);
			cycle.setEndDate(addTime(startTime, 6));
			cycle.setMonth(calendar.get(Calendar.MONTH) + 1);
			cycle.setYear(DateUtil.format(cycle.getEndDate(), "yyyy"));

			cycle.setYearWeek(String.valueOf(DateUtil.calculateDateInWeekByDate(Calendar.MONDAY, Calendar.WEEK_OF_YEAR, cycle.getStartDate()))) ;
			cycle.setMonthWeeks(DateUtil.calculateMonthWeeksByDate(Calendar.MONDAY, cycle.getStartDate(), "yyyy-MM-dd"));
			dateList.add(cycle);

			startTime = addTime(calendar.getTime(), 7);

		}while(startTime.compareTo(endDate) <= 0);

		return dateList;
	}

	//设置页面初始化 时间
	public static List<ZyyDate> initPageDateListByDate(Date startTime, Date endTime){
		//返回结果集
		List<ZyyDate> dateList = new ArrayList<ZyyDate>() ;
		ZyyDate zyyDate = null ;
		Date endDate = DateUtil.parse(endTime, "yyyy-MM");
		int i = 0;
		do{
			Calendar calendar = Calendar.getInstance() ;
			calendar.setTime(startTime) ;
			zyyDate = new ZyyDate() ;
			if (i==0 && NewRecruitUtil.unCheckCycleStartDate())
				zyyDate.setStartDate(startTime);
			else
				zyyDate.setStartDate(Utils.getMonthFirstDay(startTime));
			zyyDate.setEndDate(Utils.getMonthLastDay(startTime)) ;
			zyyDate.setMonth(String.valueOf(calendar.get(Calendar.MONTH) + 1)) ;
			zyyDate.setYear(String.valueOf(calendar.get(Calendar.YEAR))) ;
			zyyDate.setMonthWeeks(String.valueOf(DateUtil.calculateMonthWeeksByDate(Calendar.MONDAY, zyyDate.getStartDate(), "yyyy-MM-dd"))) ;
			dateList.add(zyyDate) ;
			startTime = addMonth(calendar.getTime(), 1);
			i++;
		} while (NewRecruitUtil.unCheckCycleStartDate() ? startTime.compareTo(endTime) <= 0 : startTime.compareTo(endDate) <= 0);
		return dateList;
	}

	//根据周 设置页面初始化时间
	public static List<ZyyDate> initPageDateListByWeek(Date startTime, Date endTime){
		//返回结果集
		List<ZyyDate> dateList = new ArrayList<ZyyDate>() ;
		Date tempStartTime = startTime;
		if (NewRecruitUtil.unCheckCycleStartDate()) {
			Integer week = DateUtil.getWeekOfDate(startTime);
			week--;
			startTime = DateUtil.addDay(startTime, -week);
		} else {
			startTime = DateUtil.calculateMonthFirstWeek(startTime, "yyyy-MM-dd");
		}
		ZyyDate zyyDate = null ;
		Date endDate = DateUtil.parse(endTime, "yyyy-MM-dd");
		int i = 0;
		do{
			Calendar calendar = Calendar.getInstance() ;
			calendar.setTime(startTime) ;

			zyyDate = new ZyyDate() ;
			if (i==0 && NewRecruitUtil.unCheckCycleStartDate())
				zyyDate.setStartDate(tempStartTime) ;
			else
				zyyDate.setStartDate(calendar.getTime());
			
			zyyDate.setEndDate(addTime(startTime, 6)) ;
			zyyDate.setBeginMonth(DateUtil.format(zyyDate.getStartDate(), "yy/MM/dd")) ;
			zyyDate.setEndMonth(DateUtil.format(zyyDate.getEndDate(), "yy/MM/dd")) ;
			zyyDate.setMonth(String.valueOf(calendar.get(Calendar.MONTH) + 1)) ;
			zyyDate.setYear(DateUtil.format(zyyDate.getEndDate(), "yyyy")) ;

			zyyDate.setYearWeek(String.valueOf(DateUtil.calculateDateInWeekByDate(Calendar.MONDAY, Calendar.WEEK_OF_YEAR, zyyDate.getStartDate()))) ;
			zyyDate.setMonthWeek(String.valueOf(DateUtil.calculateDateInWeekByDate(Calendar.MONDAY, Calendar.WEEK_OF_MONTH, zyyDate.getStartDate()))) ;
			zyyDate.setMonthWeeks(String.valueOf(DateUtil.calculateMonthWeeksByDate(Calendar.MONDAY, zyyDate.getStartDate(), "yyyy-MM-dd"))) ;
			dateList.add(zyyDate) ;

			startTime = addTime(calendar.getTime(), 7) ;
			i++;
		}while(startTime.compareTo(endDate) <= 0) ;

		return dateList ;
	}

	//根据周 设置Excel表头初始化时间
	public static List<ZyyDate> initExcelDateListByWeek(Date startTime, Date endTime){
		//返回结果集
		List<ZyyDate> dateList = new ArrayList<ZyyDate>() ;

		startTime = DateUtil.calculateMonthFirstWeek(startTime, "yyyy-MM-dd") ;

		ZyyDate zyyDate = null ;
		Date endDate = DateUtil.parse(endTime, "yyyy-MM-dd") ;
		do{
			Calendar calendar = Calendar.getInstance() ;
			calendar.setTime(startTime) ;

			zyyDate = new ZyyDate() ;
			zyyDate.setStartDate(calendar.getTime()) ;
			zyyDate.setEndDate(addTime(startTime, 6)) ;
			zyyDate.setBeginMonth(DateUtil.format(zyyDate.getStartDate(), "yy/MM/dd")) ;
			zyyDate.setEndMonth(DateUtil.format(zyyDate.getEndDate(), "yy/MM/dd")) ;
			zyyDate.setMonth(String.valueOf(calendar.get(Calendar.MONTH) + 1)) ;
			zyyDate.setYear(DateUtil.format(zyyDate.getStartDate(), "yyyy")) ;

			zyyDate.setYearWeek(String.valueOf(DateUtil.calculateDateInWeekByDate(Calendar.MONDAY, Calendar.WEEK_OF_YEAR, zyyDate.getStartDate()))) ;
			zyyDate.setMonthWeek(String.valueOf(DateUtil.calculateDateInWeekByDate(Calendar.MONDAY, Calendar.WEEK_OF_MONTH, zyyDate.getStartDate()))) ;
			zyyDate.setMonthWeeks(String.valueOf(DateUtil.calculateMonthWeeksByDate(Calendar.MONDAY, zyyDate.getStartDate(), "yyyy-MM-dd"))) ;
			dateList.add(zyyDate) ;

			startTime = addTime(calendar.getTime(), 7) ;
		}while(startTime.compareTo(endDate) <= 0) ;

		return dateList ;
	}

	//设置页面初始化 时间
	public static List<Date> checkDateList2(Date startTime, Date endTime){
		//返回结果集
		List<Date> dateList = new ArrayList<Date>() ;

		Date endDate = DateUtil.parse(endTime, "yyyy-MM") ;
		do{
			dateList.add(startTime) ;
			startTime = addMonth(startTime, 1) ;
		}while(startTime.compareTo(endDate) <= 0) ;

		return dateList ;
	}

	//设置医院轮转月份
	public static void setZyyCycleMonthToMap(String startDate,String endDate, Long zyyOrgId, List<ZyyCycleTableResiCycleVO> dateList){
		//返回结果集
		Map<String, ZyyCycleMonthSetting> monthMap = new HashMap<String, ZyyCycleMonthSetting>() ;

		String key = null ;
		String year = "";
		if(!"".equals(startDate)){
			String s1 = startDate.substring(0, 4);
			String s2 = endDate.substring(0, 4);
			if(!s1.equals(s2)){
				year = s1+","+s2;
			}else {
				year = s1;
			}
		}
		List<ZyyCycleMonthSettingVO> monthList = zyyCycleMonthSettingFacade.getZyyCycleMonthSettingList(year, zyyOrgId) ;
		for (ZyyCycleMonthSetting month : monthList) {
			key = month.getCycleYear() +"-"+ month.getCycleMonth() ;
			monthMap.put(key, month) ;
		}

		Calendar calendar = null ;
		for (ZyyCycleTableResiCycleVO resiCycle : dateList) {
			key = resiCycle.getYear() +"-"+ resiCycle.getMonth() ;
			ZyyCycleMonthSetting month = monthMap.get(key) ;
			if(month != null){
				resiCycle.setBeginMonth(DateUtil.format(month.getCycleStartDate(), "MM-dd")) ;
				resiCycle.setEndMonth(DateUtil.format(month.getCycleEndDate(), "MM-dd")) ;
			}else{
				calendar = Calendar.getInstance() ;
				calendar.setTime(DateUtil.parse(key, "yyyy-MM")) ;
				resiCycle.setBeginMonth(DateUtil.format(calendar.getTime(), "MM-dd")) ;

				calendar.add(Calendar.MONTH, 1) ;
				calendar.add(Calendar.DAY_OF_MONTH, -1) ;
				resiCycle.setEndMonth(DateUtil.format(calendar.getTime(), "MM-dd")) ;
			}
		}
	}

	//设置医院轮转 --按周 chenlb,11-09
	public static void setZyyCycleWeekToMap(Date startDate,Date endDate, Long zyyOrgId, List<ZyyCycleTableResiCycleVO> dateList){
		//返回结果集
		Map<String, ZyyDate> weekMap = new HashMap<String, ZyyDate>();

		String key = null ;
		List<ZyyDate> weekList = LogicUtils.initPageDateListByWeek(startDate, endDate) ;
		for (ZyyDate week : weekList) {
			String yearWeek = week.getYearWeek();
			key = week.getYear() +"-"+ yearWeek;		//exp:2012-03,03表示第三周
			weekMap.put(key, week) ;
		}

		for (ZyyCycleTableResiCycleVO resiCycle : dateList) {
			key = resiCycle.getYear() +"-"+ resiCycle.getYearWeek() ;
			ZyyDate week = weekMap.get(key) ;
			if(week != null){
				resiCycle.setBeginWeek(DateUtil.format(week.getStartDate(), "MM-dd")) ;
				resiCycle.setEndWeek(DateUtil.format(week.getEndDate(), "MM-dd")) ;
			}else{
				//System.out.println("week is null in line 944");
			}
		}
	}

	/**
	 * 根据用户类别取得组织机构ID
	 * @param zyyUser
	 * @return
	 */
	public static Long checkZyyUserOrgId(ZyyUser zyyUser){
		//默认当前用户组织机构ID
		Long orgId = zyyUser.getZyyUserOrgId() ;

		//【省厅管理】用户
		if(Constants.USER_TYPE_MANAGER.equals(zyyUser.getZyyUserType())){
			orgId = zyyUser.getZyyUserProvinceId() ;
		}else if(Constants.USER_TYPE_HOS_MANAGER.equals(zyyUser.getZyyUserType())){
			//【医院管理】用户
			ZyyOrgVO org = zyyOrgFacade.getZyyOrgById(zyyUser.getZyyUserOrgId()) ;
			if(org != null){
				orgId = org.getParentOrgId() ;
			}
		}else if(Constants.USER_TYPE_STUDENT_1.equals(zyyUser.getZyyUserType()) ||
				 Constants.USER_TYPE_STUDENT_2.equals(zyyUser.getZyyUserType())){
			//学员用户
			orgId = zyyUser.getZyyUserProvinceId() ;
		}

		return orgId ;
	}

	//根据开始与结束时间去的年度列表
	public static List<ZyyRecruitYear> getRecruitYearByTime(String starTime, String endTime) {
		if(starTime == null || "".equals(starTime)){
			return Collections.emptyList() ;
		}

		if(endTime == null || "".equals(endTime)){
			return Collections.emptyList() ;
		}

		Date star = DateUtil.parse(starTime, "yyyy") ;
		Date end  = DateUtil.parse(endTime, "yyyy") ;
		return getRecruitYearByTime(star, end) ;
	}

	//根据开始与结束时间去的年度列表
	public static List<ZyyRecruitYear> getRecruitYearByTime(Date starTime, Date endTime){
		List<ZyyRecruitYear> retList = new ArrayList<ZyyRecruitYear>() ;

		if(starTime == null || endTime == null){
			return retList ;
		}

		//取得开始于结束年份信息
		Date star = DateUtil.parse(starTime, "yyyy") ;
		Date end  = DateUtil.parse(endTime, "yyyy") ;

		//计算年度列表信息
		Calendar calendar = Calendar.getInstance() ;
		calendar.setTime(star) ;
		ZyyRecruitYear recYear = null ;
		do {
			//保存年度信息到返回列表
			recYear = new ZyyRecruitYear() ;
			recYear.setYear(String.valueOf(calendar.get(Calendar.YEAR))) ;
			retList.add(recYear) ;

			calendar.add(Calendar.YEAR, 1) ;//年份增加 1
		} while (calendar.getTime().compareTo(end) <= 0) ;

		return retList ;
	}

	/**
	 * 根据周 计算轮转起始时间
	 * @param time	开始时间
	 * @param week	起始周
	 * @return
	 */
	public static ZyyDate getCycleTimeByWeek(Date time, String week) {
		Map<Object, ZyyDate> dateMap = new HashMap<Object, ZyyDate>() ;
		List<ZyyDate> dateList = calculateMonthWeeksList(time) ;
		for (ZyyDate zyyDate : dateList) {
			dateMap.put(zyyDate.getMonthWeek(), zyyDate) ;
		}
		return dateMap.get(week) ;
	}

	//计算每个月 的 周时间列表
	public static List<ZyyDate> calculateMonthWeeksList(Date time){
		ZyyDate zyyDate = null ;
		List<ZyyDate> weekList = new ArrayList<ZyyDate>() ;

		Date start = DateUtil.getMonthFirstDay(time) ;
		Date end = DateUtil.getMonthLastDay(time) ;

		Calendar cal = Calendar.getInstance();
		cal.setFirstDayOfWeek(Calendar.MONDAY);
		cal.setTime(start);

		//计算本月有多少周
		int value = DateUtil.calculateMonthWeeksByDate(Calendar.MONDAY, time, "yyyy-MM-dd") ;

		for (int i = 0; i < value; i++) {
			zyyDate = new ZyyDate() ;
			zyyDate.setMonthWeek(String.valueOf(i+1)) ;

			int dayWeek = cal.get(Calendar.DAY_OF_WEEK);
			cal.add(Calendar.DAY_OF_MONTH, dayWeek == 1 ? -1 : 0);

			if(i == 0){
				int tempWeek = dayWeek == 1 ? 1 : (8 - dayWeek) ;
				zyyDate.setStartDate(start) ;
				zyyDate.setEndDate(DateUtil.addDay(cal.getTime(), tempWeek)) ;
				cal.add(Calendar.DATE, tempWeek + 1);
				weekList.add(zyyDate) ;
			}else{
				zyyDate.setStartDate(cal.getTime()) ;
				cal.add(Calendar.DATE, 6);
				zyyDate.setEndDate(cal.getTime().compareTo(end) > 0 ? end : cal.getTime()) ;
				cal.add(Calendar.DATE, 1);
				weekList.add(zyyDate) ;
			}
		}

		return weekList ;
	}

	/**
	 * 判断轮转时间在本月处于第几周
	 * @param cycleDate
	 * @return
	 */
	public static String checkDeptCycleTime(Date startDate, Date endDate){
		StringBuilder str = new StringBuilder() ;
		List<ZyyDate> dateList = calculateMonthWeeksList(startDate) ;
		for (ZyyDate zyyDate : dateList) {
			if((zyyDate.getStartDate().getTime() <= startDate.getTime() &&
				zyyDate.getEndDate().getTime() >= startDate.getTime())
				||
			   (zyyDate.getStartDate().getTime() <= endDate.getTime() &&
				zyyDate.getEndDate().getTime() >= endDate.getTime())
				||
			   (zyyDate.getStartDate().getTime() >= startDate.getTime() &&
				zyyDate.getEndDate().getTime() <= endDate.getTime())){

				str.append(zyyDate.getMonthWeek()).append(",") ;
			}
		}

		return str.substring(0, str.length() - 1) ;
	}

	public static int selectIsAdjust(Integer isAdjust){
		int tempAdjust = Constants.ZYY_RECRUIT_WILL_IS_ADJUST_All ;//全部
		if(isAdjust == null){
			return tempAdjust ;
		}

		if(isAdjust == 1){
			//服从
			tempAdjust = Constants.ZYY_RECRUIT_WILL_IS_ADJUST_YES ;
		}else if(isAdjust == 0){
			//不服从
			tempAdjust = Constants.ZYY_RECRUIT_WILL_IS_ADJUST_NO ;
		}

		return tempAdjust ;
	}

	/**
	 * 判断轮转表轮转类别 1-日 2-周 3-月 4-半月
	 * @param cycleType
	 * @return
	 */
	public static String chooseCycleTableTypeName(Integer cycleType){
		if(cycleType == 1){
			return "日" ;
		}else if(cycleType == 2){
			return "周" ;
		}else if(cycleType == 3){
			return "月" ;
		}else{
			return "半月" ;
		}
	}

	/**
	 * 判断阶段名称
	 * @param stageId
	 * @return
	 */
	public static String chooseStageName(Long stageId){
		String stageName = "第一阶段" ;
		if(stageId == 2){
			return "第二阶段" ;
		}else if(stageId == 3){
			return "第三阶段" ;
		}else if(stageId == 4){
			return "第四阶段" ;
		}else if(stageId == 5){
			return "第五阶段" ;
		}else{
			return stageName ;
		}
	}
	//分离阶段信息
	public static Map<Long, List<ZyyRecruitStageVO>> getZyyRecruitStageList_JL(List<ZyyRecruitStageVO> stageList){
		ZyyRecruitStageVO stage = null ;
		List<ZyyRecruitStageVO> staList = null ;
		Map<Long, List<ZyyRecruitStageVO>> stageMap = new HashMap<Long, List<ZyyRecruitStageVO>>() ;

		for (ZyyRecruitStageVO recStage : stageList) {
			staList = new ArrayList<ZyyRecruitStageVO>() ;

			//第一志愿招生时间
			if(recStage.getRecruitStartDate() != null && recStage.getRecruitEndDate() != null){
				stage = new ZyyRecruitStageVO() ;
				stage.setSignStartDate(recStage.getSignStartDate()) ;
				stage.setSignEndDate(recStage.getSignEndDate()) ;
				stage.setRecruitStartDate(recStage.getRecruitStartDate()) ;
				stage.setRecruitEndDate(recStage.getRecruitEndDate()) ;
				staList.add(stage) ;
			}
			//第二志愿招生时间
			if(recStage.getRecruitStartDateTwo() != null && recStage.getRecruitEndDateTwo() != null){
				stage = new ZyyRecruitStageVO() ;
				stage.setRecruitStartDate(recStage.getRecruitStartDateTwo()) ;
				stage.setRecruitEndDate(recStage.getRecruitEndDateTwo()) ;
				staList.add(stage) ;
			}
			//第三志愿招生时间
			if(recStage.getRecruitStartDateThree() != null && recStage.getRecruitEndDateThree() != null){
				stage = new ZyyRecruitStageVO() ;
				stage.setRecruitStartDate(recStage.getRecruitStartDateThree()) ;
				stage.setRecruitEndDate(recStage.getRecruitEndDateThree()) ;
				staList.add(stage) ;
			}
			//第四志愿招生时间
			if(recStage.getRecruitStartDateFour() != null && recStage.getRecruitEndDateFour() != null){
				stage = new ZyyRecruitStageVO() ;
				stage.setRecruitStartDate(recStage.getRecruitStartDateFour()) ;
				stage.setRecruitEndDate(recStage.getRecruitEndDateFour()) ;
				staList.add(stage) ;
			}
			//第五志愿招生时间
			if(recStage.getRecruitStartDateFive() != null && recStage.getRecruitEndDateFive() != null){
				stage = new ZyyRecruitStageVO() ;
				stage.setRecruitStartDate(recStage.getRecruitStartDateFive()) ;
				stage.setRecruitEndDate(recStage.getRecruitEndDateFive()) ;
				staList.add(stage) ;
			}

			stageMap.put(recStage.getStageId(), staList) ;
		}

		return stageMap ;
	}

	//根据当前时间得到下周第一天(包括跨月)
	public static Date getNextWeekFirstDay(Date date){
		List<ZyyDate> list = calculateMonthWeeksList(date);
		Date t = null;
		for(int i=0; i<list.size(); i++){
			ZyyDate zd = (ZyyDate)list.get(i);
			if(date.getTime() < zd.getStartDate().getTime()){
				t = zd.getStartDate();
				break;
			}
		}
		if(t == null){	//如果为空则表示跨月
			date = getNextMonthFirstDay(date);
			t = getNextWeekFirstDay(date);
		}
		return t;
	}

	public static Date addTimeByType2(Date startTime, ZyyCycleTableResiTime time, int type){
		Date temp = null ;
		if(time.getNewCycleTime() > 0){
			temp = (temp == null) ? startTime : temp ;
			temp = addTimeByType(temp, time.getNewCycleTime(), type) ;
		}

		if(time.isHalfMoonFlag()){
			temp = (temp == null) ? startTime : temp ;
			temp = addTime(temp, 1) ;
			temp = addTimeByType(temp, 0, 4) ;
		}

		return temp ;
	}

	public static String splitWillStatusToString(Integer[] status){
		if(status == null || status.length == 0){
			return "" ;
		}else{
			StringBuilder builder = new StringBuilder() ;
			for (int i = 0; i < status.length; i++) {
				builder.append(status[i]) ;
				if(i < status.length - 1){
					builder.append(",") ;
				}
			}
			return builder.toString() ;
		}
	}

	/**
	 * 获取开发模式还是产品模式
	 */
	public static String getPattern() {
		WebApplicationContext webApplicationContext = ContextLoader.getCurrentWebApplicationContext();
		ServletContext servletContext = webApplicationContext.getServletContext();
		String pattern = servletContext.getInitParameter("spring.profiles.active");
		logger.info("pattern=" + pattern);
		return pattern;
	}

	/**
	 * 是否开发模式
	 */
	public static boolean isDev() {
		return PATTERN_DEV.equals(getPattern()) ? true : false;
	}

	/**
	 * 是否产品模式
	 */
	public static boolean isProd() {
		return PATTERN_PROD.equals(getPattern()) ? true : false;
	}

	/**
	 * 是否测试环境
	 * @return
	 */
	public static boolean isTest() {
		return PATTERN_TEST.equals(getPattern()) ? true : false;
	}

	public static void main(String[] args) {
		Date start = DateUtil.parse("2012-08-05", "yyyy-MM-dd") ;
		Date end   = DateUtil.parse("2013-07-27", "yyyy-MM-dd") ;

		List<ZyyDate> dateList = LogicUtils.initPageDateListByWeek(start, end) ;
		for (ZyyDate zyyDate : dateList) {
			System.out.println(zyyDate.getZyyDateInfo());
		}
	}

	/**
	 * 校验用户是否已经完善个人信息，如果咩有完善需要进行跳转到信息完善页面
	 */
	public static int checkUserInfo2(ZyyUserExtendVO info) {
//		if (isDev()){
//			return true;
//		}
		if (info != null) {
			// 校验个人头像和身份证是否上传
			if (!checkUserBaseInfo(info)){
				return 1;
			}
			// 校验学历是否填写
			if (!checkCertificateInfo(info)) {
				return 2;
			}
			// 校验医生资格信息页面是否填写
			if (!checkasCertificate(info)) {
				return 3;
			}
			// 校验其他信息页面是否填写
			if (!checkOtherUserInfo(info)) {
				return 4;
			}
		}
		return 0;
	}
	/**
	 * 校验用户是否已经完善个人信息，如果咩有完善需要进行跳转到信息完善页面
	 */
	public static boolean checkUserInfo(ZyyUserExtendVO info, Long ZyyUserProvinceId) {
//		if (isDev()){
//			return true;
//		}
		if (info != null) {
			// 校验个人头像和身份证是否上传
			if (!checkUserBaseInfo(info)){
				return false;
			}
			// 校验学历是否填写
			if (!checkCertificateInfo(info)) {
				return false;
			}
			// 2024-4-23新疆建设生产兵团改为报名学员只要完善基本信息和学历信息，就可以填写报名表
			if (ZyyUserProvinceId == null || ZyyUserProvinceId != 62000000) {
				// 校验医生资格信息页面是否填写
				if (!checkasCertificate(info)) {
					return false;
				}
				// 校验其他信息页面是否填写
				if (!checkOtherUserInfo(info)) {
					return false;
				}
			}
		}
		return true;
	}


	private static boolean checkasCertificate(ZyyUserExtendVO info) {
		// 校验医生资格证书是否填写
		if (info.getHasCertificate() ==null || info.getHasCertificate() == -1) {
			return false;
		}
		// 如果有需要校验获取时间和编号
		if (info.getHasCertificate()==null || info.getHasCertificate() == 1) {
			if (info.getGetDate() == null || info.getCertificateNumber() == null) {
				return false;
			}
		}
		// 校验英语能力是否填写
		if (info.getForeignLanguageTestType() !=null){
			if (info.getForeignLanguageTestType() > -1 && info.getForeignLanguageTestType() <= 3) {
				if (info.getEnglishLevel() == null) {
					return false;
				}
			}
		}
		return true;
	}

	public static boolean checkCertificateInfo(ZyyUserExtendVO info) {
		if (NewRecruitUtil.isXjgw())
			return true;
		if (info.getFirstRecordSchool() == null
				|| info.getTrainingPeriod() == null
				|| StringUtils.isEmpty(info.getFirstGraduateSchool())
				|| info.getFullTimeFrecord() == null
				|| info.getIsReadingFirstRecord() == null
				|| info.getFirstRecordProf() == null
		) {
			return false;

		}
		if (info.getIsReadingFirstRecord()==2){
			if (info.getFirstRecordCertificateTime() == null && info.getFirstRecordCertifiNum()==null) {
				return false;
			}
		}
		// 校验第一学位如果有的话需要校验获取时间和证书编号
		if (info.getFirstDegree() != null && info.getFirstDegree() != -1) {
			if (info.getFirstDegreeCertificateTime() == null
					|| info.getFirstDegreeCertifiNum() == null) {
				return false;
			}
		}
		/// 校验最高学历是否填写
		if (info.getHighestRecordSchool() == null
				|| info.getGraduationDate() == null
				|| info.getHighestGraduateSchool() == null
				|| info.getHighestRecordProf() == null
				|| info.getFullTimeGraduation() == null
				|| info.getIsReadingGraduation() == null

		) {
			return false;
		}
		if (info.getIsReadingGraduation()==2){
			if (info.getGraduationCertificateTime() == null && info.getGraduationCode()==null) {
				return false;
			}
		}
		// 校验最高学位如果有的话需要校验获取时间和证书编号
		if (info.getHighestDegree() != null && info.getHighestDegree() > -1) {
			if (info.getHdegreeCertificateTime() == null
					|| info.getHighestDegreeCertifiNum() == null) {
				return false;
			}
		}
		return true;
	}

	private static boolean checkUserBaseInfo(ZyyUserExtendVO info) {
		if (StringUtils.isEmpty(info.getCardBehind())
				|| StringUtils.isEmpty(info.getCardFront())
				|| StringUtils.isEmpty(info.getPhotoPath())
		) {
			return false;
		}
		
		Integer residencySource = info.getResidencySource();
		if (NewRecruitUtil.isHbxy() && (residencySource != null && residencySource == 3) 
			&& (StringUtils.isBlank(info.getMentorName()) || StringUtils.isBlank(info.getMentorUnit())))
			return false;
		
		return true;
	}

	private static boolean checkOtherUserInfo(ZyyUserExtendVO info) {
		if (info.getIsSupport()!=null && info.getIsSupport()==1) {
			if(info.getSendAddress()==null
					|| StringUtils.isBlank(info.getSendCreditCode())
					|| info.getReceiveAddress()==null
					|| StringUtils.isBlank(info.getReceiveCreditCode())
			)
				return false;
		}
		return true;
	}


}




















