package com.hys.zyy.manage.util;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.hys.zyy.manage.model.vo.MapKeyValueVO;

/**
 * 构造map
 * <AUTHOR>
 * @date 2019-7-10上午9:58:54
 */
public class MapKeyValueUtil {
	//构造map
	public static Map<Long, String> createMap(List<MapKeyValueVO> list) {
		Map<Long, String> map = new HashMap<Long, String>();
		if (CollectionUtils.isNotEmpty(list)){
			int size = list.size();
			for (int i = 0; i < size; i++) {
				MapKeyValueVO item = list.get(i);
				map.put(item.getId(), item.getVal());
			}
		}
		return map;
	}
	
	//构造map
	public static Map<Long, Integer> createMapInteger(List<MapKeyValueVO> list) {
		Map<Long, Integer> map = new HashMap<Long, Integer>();
		if (CollectionUtils.isNotEmpty(list)){
			int size = list.size();
			for (int i = 0; i < size; i++) {
				MapKeyValueVO item = list.get(i);
				if (StringUtils.isNotEmpty(item.getVal())){
					map.put(item.getId(), Integer.valueOf(item.getVal()));
				}
			}
		}
		return map;
	}
}
