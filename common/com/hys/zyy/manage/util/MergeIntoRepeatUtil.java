package com.hys.zyy.manage.util;

/**
 * meger into 执行的时候，出现了重复的记录
 * 报：unable to get a stable set of rows in the source tables 错误
 * <AUTHOR>
 * @date 2018-9-19下午8:22:34
 */
public class MergeIntoRepeatUtil {
	
	/**
	 * 根据uniqueKey 去除重复
	 * @param sql
	 * @return
	 * <AUTHOR>
	 * @date 2018-9-19下午8:29:20
	 */
	public static String removeRepeatRecord(String sql){
		StringBuffer buffer = new StringBuffer();
		buffer.append(" select * from ( ");
		buffer.append(" select temp100.*, row_number() OVER(PARTITION BY temp100.uniqueKey ORDER BY temp100.uniqueKey  DESC ) AS RW  from ( ");
		buffer.append(sql);
		buffer.append(" ) temp100 ");
		buffer.append(" ) temp200 where RW =1 ");
		return buffer.toString();
	}
}
