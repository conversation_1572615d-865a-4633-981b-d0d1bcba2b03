package com.hys.zyy.manage.util;

import java.util.Comparator;

import com.hys.zyy.manage.model.ZyyCycleTableResiCycle;
import com.hys.zyy.manage.model.ZyyCycleTableResiCycleVO;
import com.hys.zyy.manage.model.ZyyCycleTableResiTime;
import com.hys.zyy.manage.model.ZyyOrgVO;
import com.hys.zyy.manage.model.ZyyProcessDetail;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：张伟清 2012-07-06
 * 
 * 描述：比较器类
 * 
 * 说明:
 */
public class MyComparator {

	// 轮转升序比较器
	public static Comparator<ZyyCycleTableResiCycleVO> cycleVoAsc = new Comparator<ZyyCycleTableResiCycleVO>() {
		@Override
		public int compare(ZyyCycleTableResiCycleVO cycle1, ZyyCycleTableResiCycleVO cycle2) {
			return cycle1.getStartDate().compareTo(cycle2.getStartDate());
		}
	};

	// 轮转将序比较器 
	public static Comparator<ZyyCycleTableResiCycle> cycleAsc = new Comparator<ZyyCycleTableResiCycle>() {
		@Override
		public int compare(ZyyCycleTableResiCycle cycle1, ZyyCycleTableResiCycle cycle2) {
			return cycle1.getStartDate().compareTo(cycle2.getStartDate());
		}
	};
	
	// 轮转将序比较器 vo
	public static Comparator<ZyyCycleTableResiCycleVO> cycleVoDesc = new Comparator<ZyyCycleTableResiCycleVO>() {
		@Override
		public int compare(ZyyCycleTableResiCycleVO cycle1, ZyyCycleTableResiCycleVO cycle2) {
			return cycle2.getStartDate().compareTo(cycle1.getStartDate());
		}
	};

	// 轮转将序比较器 
	public static Comparator<ZyyCycleTableResiCycle> cycleDesc = new Comparator<ZyyCycleTableResiCycle>() {
		@Override
		public int compare(ZyyCycleTableResiCycle cycle1, ZyyCycleTableResiCycle cycle2) {
			return cycle2.getStartDate().compareTo(cycle1.getStartDate());
		}
	};
	
	// 轮转将序比较器 vo
	public static Comparator<ZyyCycleTableResiCycleVO> cycleRatAsc = new Comparator<ZyyCycleTableResiCycleVO>() {
		@Override
		public int compare(ZyyCycleTableResiCycleVO cycle1, ZyyCycleTableResiCycleVO cycle2) {
			return cycle1.getDeptRatio() - cycle2.getDeptRatio();
		}
	};
	
	//科室当前轮转容纳人数
	public static Comparator<ZyyCycleTableResiTime> deptAsc = new Comparator<ZyyCycleTableResiTime>(){
		@Override
		public int compare(ZyyCycleTableResiTime time1, ZyyCycleTableResiTime time2) {
			return time1.getDeptNumber().intValue() - time2.getDeptNumber().intValue();
		}
	};

	//住院医师流程
	public static Comparator<ZyyProcessDetail> proDesc = new Comparator<ZyyProcessDetail>(){
		@Override
		public int compare(ZyyProcessDetail detail1, ZyyProcessDetail detail2) {
			return detail2.getProcessLevel().intValue() - detail1.getProcessLevel().intValue();
		}
	};

	//住院医师流程
	public static Comparator<ZyyProcessDetail> proAsc = new Comparator<ZyyProcessDetail>(){
		@Override
		public int compare(ZyyProcessDetail detail1, ZyyProcessDetail detail2) {
			return detail1.getProcessLevel().intValue() - detail2.getProcessLevel().intValue();
		}
	};

	//组织机构排序
	public static Comparator<ZyyOrgVO> orgAsc = new Comparator<ZyyOrgVO>(){
		@Override
		public int compare(ZyyOrgVO org1, ZyyOrgVO org2) {
			return org1.getOrgSeq2().intValue() - org2.getOrgSeq2().intValue();
		}
	};
	
	
	
	
}
