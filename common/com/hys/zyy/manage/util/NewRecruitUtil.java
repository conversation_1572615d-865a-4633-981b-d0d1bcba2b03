package com.hys.zyy.manage.util;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import org.apache.log4j.LogManager;
import org.apache.log4j.Logger;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.hys.security.model.Account;
import com.hys.security.model.Resource;
import com.hys.security.util.SecurityUtils;
import com.hys.zyy.manage.constants.CommonConstants;
import com.hys.zyy.manage.constants.Constants;
import com.hys.zyy.manage.dao.YktStatDAO;
import com.hys.zyy.manage.facade.ZyyRecruitStageFacade;
import com.hys.zyy.manage.facade.ZyyUserCategoryFacade;
import com.hys.zyy.manage.model.YktStatQuery;
import com.hys.zyy.manage.model.ZyyRecruitYear;
import com.hys.zyy.manage.model.ZyyUser;
import com.hys.zyy.manage.model.ZyyUserExtendVO;
import com.hys.zyy.manage.service.ZyyRecruitYearManage;

import ch.lambdaj.Lambda;

/**
 * 新招录工具类
 * <AUTHOR>
 */
public class NewRecruitUtil {
	
	private static Logger logger = LogManager.getLogger(NewRecruitUtil.class.getName());
	
	private static final String RESIDENCY_REPORT_DATE_MAP = "residencyReportDateMap";
	
	private static final String RESIDENCY_DESCRIBE_1 = "住院医师";
	private static final String RESIDENCY_DESCRIBE_2 = "在培" + RESIDENCY_DESCRIBE_1;
	private static final String RESIDENCY_DESCRIBE_3 = "学员";
	private static final String RESIDENCY_DESCRIBE_4 = "助理全科医生";
	private static final String RESIDENCY_DESCRIBE_5 = "在培" + RESIDENCY_DESCRIBE_4;
	private static final String GWGPXY = "公卫规培学员";
	private static final Long LIAN_JIANG_ORG_ID = 9540L; // 廉江市人民医院org_id：9540
	private static final Long NEI_MENG_PROVINCE_ID = 89000000L; // 内蒙古province_id：89000000L
	private static final Long HZSRMYY_ORG_ID = 10021L; // 化州市人民医院
	private static final Long WCSRMYY_ORG_ID = 9900L; // 吴川市人民医院
	private static final Long HHSDYRMYY_ORG_ID = 10060L; // 怀化市第一人民医院
	private static final Long TSSGRYY_ORG_ID = 430L; // 唐山市工人医院
	private static final Long HBSSY_ORG_ID = 449L; // 河北省四院
	private static final Long BDSDEYY_ORG_ID = 3825L; // 保定市第二医院
	private static final Long WLMQZYYY_ORG_ID = 10082L; // 乌鲁木齐中医医院
	private static final Long LPSFYBJY_ORG_ID = 9780L; // 六盘水市妇幼保健院
	private static final Long XJGW_PROVINCE_ID = 79000000L; // 新疆公卫省级
	private static final Long HBXY_PROVINCE_ID = 1000000L; // 河北西医省级
	
	// 使用新版出科审核的医院ID
	public static final Long[] USE_NEW_LEAVE_DEPT_AUDIT_ORG_IDS = new Long[] { 
		9780L // 六盘水市妇幼保健院
	};
	
	// 使用新招录的省ID
	public static final Long[] PROVINCE_IDS = new Long[] { 
		1000000L, // 河北西医
		69000000L // 云南省
	};
	
	// 限制操作学员报到情况的用户类型
	public static final Integer[] OPESTUREP_USER_TYPE = new Integer[] { 
		5 // 医院用户
	};
	
	public static final Long[] UNCHECK_CYCLE_START_DATE_ORG = new Long[] { 
		10021L, // 化州市人民医院
		9900L // 吴川市人民医院
	};
	
	// 开启协同基地[科室、带教]评价学员的医院
	public static final Long[] COMBINED_BASE_COMMENT_ORG_IDS = new Long[] { 
		3830L // 河北省第六人民医院
	};
	
	public static final Long[] TEACH_ACT_ATT_DOWNLOAD_PRIVILEGE_ORG = new Long[] {
		451L, // 衡水市人民医院
		440L // 沧州市人民医院
	};
	
	/**
	 * @desc 验证当前用户所在省是否使用了新招录
	 * <AUTHOR>
	 * @date 2020-6-17  下午4:35:29
	 */
	public static boolean validate(Long provinceId) {
		if (provinceId == null)
			return false;
		return Arrays.asList(PROVINCE_IDS).contains(provinceId);
	}
	
	public static boolean validate(ZyyUser zyyUser) {
		if (zyyUser == null)
			return false;
		return validate(zyyUser.getZyyUserProvinceId());
	}
	
	public static boolean validate(ZyyUserExtendVO zyyUser) {
		return validate((ZyyUser) zyyUser);
	}

	public static boolean validate() {
		ZyyUserExtendVO zyyUser = SecurityUtils.getCurrentUser();
		return validate(zyyUser);
	}
	
	/**
	 * 给定几个时间段，判断当前时间是否在这几个时间段之间
	 */
	public static boolean nowBetweenDates(List<Map<String, Date>> dates) {
		boolean result = false;
		if (CollectionUtils.isEmpty(dates))
			return result;
		Date now = new Date();
		for (Map<String, Date> dateMap : dates) {
			if (DateUtil.between(now, dateMap.get("startTime"), dateMap.get("endTime"))) {
				result = true;
				break;
			}
		}
		return result;
	}
	
	/**
	 * @desc 获取某省历年学员报到时间 
	 * <AUTHOR>
	 * @date 2020-6-23  下午1:59:36
	 */
	@SuppressWarnings({ "unchecked" })
	public static Map<String, List<Map<String, Date>>> getResiReportDateMap() {
		Map<String, List<Map<String, Date>>> result = null;
		HttpServletRequest req = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
		ZyyUserExtendVO zyyUser = SecurityUtils.getCurrentUser();
		HttpSession session = req.getSession();
		String key = zyyUser.getZyyUserProvinceId() + "_" + RESIDENCY_REPORT_DATE_MAP;
		Object obj = session.getAttribute(key);
		if (obj != null)
			result = (Map<String, List<Map<String, Date>>>) obj;
		else {
			result = SpringUtils.getSingleBeanOfType(ZyyRecruitStageFacade.class).getResiReportDateMap();
			session.setAttribute(key, result);
		}
		return result;
	}
	
	/**
	 * @desc 判断当前登录用户是否可以操作学员报到情况：
	 *  1、该省使用了新招录；
	 * 	2、非医院用户不予限制；
	 *  3、当前时间在招录年度的学员报到时间范围内；
	 *  4、往年度学员不能进行操作；
	 * <AUTHOR>
	 * @date 2020-6-22  上午11:04:25
	 */
	public static boolean opeStuRep(Long zyyRecruitYearId){
		boolean result = true;
		// 判断是否使用了新招录
		if (!NewRecruitUtil.validate())
			return result;
		ZyyUserExtendVO zyyUser = SecurityUtils.getCurrentUser();
		if(zyyUser == null)
			return result;
		Integer zyyUserType = zyyUser.getZyyUserType();
		Long zyyUserProvinceId = zyyUser.getZyyUserProvinceId();
		// 判断用户类型
		if (Arrays.asList(OPESTUREP_USER_TYPE).contains(zyyUserType)) {
			// 当前开启年度ID
			Long yearId = SpringUtils.getSingleBeanOfType(ZyyRecruitYearManage.class).getCurrentOpenRecruitYear(zyyUserProvinceId).getId();
			if (!yearId.equals(zyyRecruitYearId))
				result = false;
			else {
				Map<String, List<Map<String, Date>>> resiReportDateMap = NewRecruitUtil.getResiReportDateMap();
				String key = zyyRecruitYearId + "_" + RecruitContextHolder.getHospitalType();
				List<Map<String, Date>> dateList = resiReportDateMap.get(key);
				result = NewRecruitUtil.nowBetweenDates(dateList);
			}
		}
		return result;
	}
	
	/**
	 * 获取当前学年
	 */
	public static ZyyRecruitYear getCurrentOpenRecruitYear() {
		return SpringUtils.getSingleBeanOfType(ZyyRecruitYearManage.class).getCurrentOpenRecruitYear(SecurityUtils.getCurrentUser().getZyyUserProvinceId());
	}
	
	public static Long getCurrentOpenRecruitYearId() {
		ZyyRecruitYear currentOpenRecruitYear = getCurrentOpenRecruitYear();
		return currentOpenRecruitYear == null ? 0L : currentOpenRecruitYear.getId();
	}
	
	public static String getCurrentOpenRecruitYearName() {
		ZyyRecruitYear currentOpenRecruitYear = getCurrentOpenRecruitYear();
		return currentOpenRecruitYear == null ? "" : currentOpenRecruitYear.getYear();
	}
	
	public static ZyyRecruitYear getYearByProvinceId(Long provinceId) {
		return SpringUtils.getSingleBeanOfType(ZyyRecruitYearManage.class).getCurrentOpenRecruitYear(provinceId);
	}
	
	public static Long getYearIdByProvinceId(Long provinceId) {
		ZyyRecruitYear zyyRecruitYear = getYearByProvinceId(provinceId);
		return zyyRecruitYear == null ? 0L : zyyRecruitYear.getId();
	}
	
	/**
	 * 获取时间戳
	 */
	public static long timestmp() {
		return System.currentTimeMillis();
	}
	
	/**
	 * 数组是否包含指定元素
	 */
	public static boolean intArrContains(Integer[] array, Integer valueToFind){
		return ArrayUtils.contains(array, valueToFind);
	}
	
	public static String resiDesc() {
		String result = RESIDENCY_DESCRIBE_1;
		ZyyUserExtendVO zyyUser = SecurityUtils.getCurrentUser();
		if (zyyUser == null)
			return result;
		Long zyyUserProvinceId = zyyUser.getZyyUserProvinceId();
		Long zyyUserOrgId = zyyUser.getZyyUserOrgId();
		if (zyyUserProvinceId == null || zyyUserOrgId == null)
			return result;
		if (NEI_MENG_PROVINCE_ID.toString().equals(zyyUserProvinceId.toString())
			|| LIAN_JIANG_ORG_ID.toString().equals(zyyUserOrgId.toString())
			|| WCSRMYY_ORG_ID.toString().equals(zyyUserOrgId.toString())
			|| HHSDYRMYY_ORG_ID.toString().equals(zyyUserOrgId.toString())
		)
			result = RESIDENCY_DESCRIBE_3;
		else if (HZSRMYY_ORG_ID.toString().equals(zyyUserOrgId.toString()))
			result = RESIDENCY_DESCRIBE_4;
		else if (XJGW_PROVINCE_ID.toString().equals(zyyUserProvinceId.toString()))
			result = GWGPXY;
		return result;
	}
	
	public static String zpResiDesc() {
		String result = RESIDENCY_DESCRIBE_2;
		ZyyUserExtendVO zyyUser = SecurityUtils.getCurrentUser();
		if (zyyUser == null)
			return result;
		Long zyyUserProvinceId = zyyUser.getZyyUserProvinceId();
		Long zyyUserOrgId = zyyUser.getZyyUserOrgId();
		if (zyyUserProvinceId == null || zyyUserOrgId == null)
			return result;
		if (NEI_MENG_PROVINCE_ID.toString().equals(zyyUserProvinceId.toString())
			|| LIAN_JIANG_ORG_ID.toString().equals(zyyUserOrgId.toString())
			|| WCSRMYY_ORG_ID.toString().equals(zyyUserOrgId.toString())
			|| HHSDYRMYY_ORG_ID.toString().equals(zyyUserOrgId.toString())
		)
			result = RESIDENCY_DESCRIBE_3;
		else if (HZSRMYY_ORG_ID.toString().equals(zyyUserOrgId.toString()))
			result = RESIDENCY_DESCRIBE_5;
		else if (XJGW_PROVINCE_ID.toString().equals(zyyUserProvinceId.toString()))
			result = GWGPXY;
		return result;
	}
	
	/**
	 * @desc 是否护士规培平台
	 * <AUTHOR>
	 * @date 2020-12-9 下午3:26:10
	 */
	public static boolean isNursePlatform() {
		boolean result = false;
		ZyyUserExtendVO zyyUser = SecurityUtils.getCurrentUser();
		if (zyyUser == null)
			return result;
		Long zyyUserProvinceId = zyyUser.getZyyUserProvinceId();
		if (zyyUserProvinceId == null)
			return result;
		if (NEI_MENG_PROVINCE_ID.toString().equals(zyyUserProvinceId.toString()))
			result = true;
		return result;
	}
	
	public static String getCtx(Boolean isLocalFile) {
		String result = null;
		ServletRequestAttributes sra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
		HttpServletRequest req = sra.getRequest();
		if (isLocalFile != null && isLocalFile)
			result = req.getContextPath();
		else
			result = req.getSession().getServletContext().getAttribute(CommonConstants.QINIU_FILE_DOMAIN).toString();
		return result;
	}
	
	/**
	 * @desc 验证当前机构是否使用了新版出科审核
	 * <AUTHOR>
	 * @date 2022-10-28 下午3:41:56
	 */
	public static boolean useNewLeaveDeptAudit(Long hospitalId) {
		if (hospitalId == null)
			return false;
		return Arrays.asList(USE_NEW_LEAVE_DEPT_AUDIT_ORG_IDS).contains(hospitalId);
	}
	
	public static boolean useNewLeaveDeptAudit(ZyyUser zyyUser) {
		if (zyyUser == null)
			return false;
		return useNewLeaveDeptAudit(zyyUser.getZyyUserOrgId());
	}
	
	public static boolean useNewLeaveDeptAudit(ZyyUserExtendVO zyyUser) {
		return useNewLeaveDeptAudit((ZyyUser) zyyUser);
	}
	
	public static boolean useNewLeaveDeptAudit() {
		return useNewLeaveDeptAudit(SecurityUtils.getCurrentUser());
	}
	
	/**
	 * @desc 不限制排轮转开始时间的医院
	 * <AUTHOR>
	 * @date 2022-11-25 下午2:33:38
	 */
	public static boolean unCheckCycleStartDate(Long zyyUserOrgId) {
		if (zyyUserOrgId == null)
			return false;
		return Arrays.asList(UNCHECK_CYCLE_START_DATE_ORG).contains(zyyUserOrgId);
	}
	
	public static boolean unCheckCycleStartDate(ZyyUser zyyUser) {
		if (zyyUser == null)
			return false;
		return unCheckCycleStartDate(zyyUser.getZyyUserOrgId());
	}
	
	public static boolean unCheckCycleStartDate(ZyyUserExtendVO zyyUser) {
		return unCheckCycleStartDate((ZyyUser) zyyUser);
	}
	
	public static boolean unCheckCycleStartDate() {
		return unCheckCycleStartDate(SecurityUtils.getCurrentUser());
	}
	
	/**
	 * @desc 医院是否开启协同基地评价
	 * <AUTHOR>
	 * @date 2023-3-3  下午2:53:47
	 */
	public static boolean combinedBaseComment(Long hospitalId) {
		if (hospitalId == null)
			return false;
		return Arrays.asList(COMBINED_BASE_COMMENT_ORG_IDS).contains(hospitalId);
	}
	
	public static boolean combinedBaseComment(ZyyUser zyyUser) {
		if (zyyUser == null)
			return false;
		return combinedBaseComment(zyyUser.getZyyUserOrgId());
	}
	
	public static boolean combinedBaseComment(ZyyUserExtendVO zyyUser) {
		return combinedBaseComment((ZyyUser) zyyUser);
	}
	
	public static boolean combinedBaseComment() {
		ZyyUserExtendVO zyyUser = SecurityUtils.getCurrentUser();
		return combinedBaseComment(zyyUser);
	}
	
	/**
	 * 是否是唐山市工人医院
	 */
	public static boolean isTssgryy() {
		boolean result = false;
		ZyyUserExtendVO zyyUser = SecurityUtils.getCurrentUser();
		if (zyyUser == null)
			return result;
		Long zyyUserOrgId = zyyUser.getZyyUserOrgId();
		if (zyyUserOrgId == null)
			return result;
		if (TSSGRYY_ORG_ID.toString().equals(zyyUserOrgId.toString()))
			result = true;
		return result;
	}
	
	/**
	 * 是否是河北省四院
	 */
	public static boolean isHbssy() {
		boolean result = false;
		ZyyUserExtendVO zyyUser = SecurityUtils.getCurrentUser();
		if (zyyUser == null)
			return result;
		Long zyyUserOrgId = zyyUser.getZyyUserOrgId();
		if (zyyUserOrgId == null)
			return result;
		if (HBSSY_ORG_ID.toString().equals(zyyUserOrgId.toString()))
			result = true;
		return result;
	}
	
	/**
	 * 是否是保定市第二医院
	 */
	public static boolean isBdsdeyy() {
		boolean result = false;
		ZyyUserExtendVO zyyUser = SecurityUtils.getCurrentUser();
		if (zyyUser == null)
			return result;
		Long zyyUserOrgId = zyyUser.getZyyUserOrgId();
		if (zyyUserOrgId == null)
			return result;
		if (BDSDEYY_ORG_ID.toString().equals(zyyUserOrgId.toString()))
			result = true;
		return result;
	}
	
	/**
	 * 是否是乌鲁木齐中医医院
	 */
	public static boolean isWlmqzyyy() {
		boolean result = false;
		ZyyUserExtendVO zyyUser = SecurityUtils.getCurrentUser();
		if (zyyUser == null)
			return result;
		Long zyyUserOrgId = zyyUser.getZyyUserOrgId();
		if (zyyUserOrgId == null)
			return result;
		if (WLMQZYYY_ORG_ID.toString().equals(zyyUserOrgId.toString()))
			result = true;
		return result;
	}
	
	/**
	 * 是否是六盘水市妇幼保健院
	 */
	public static boolean isLpsfybjy() {
		boolean result = false;
		ZyyUserExtendVO zyyUser = SecurityUtils.getZyyUser();
		if (zyyUser == null)
			return result;
		Long zyyUserOrgId = zyyUser.getZyyUserOrgId();
		if (zyyUserOrgId == null)
			return result;
		if (LPSFYBJY_ORG_ID.toString().equals(zyyUserOrgId.toString()))
			result = true;
		return result;
	}
	
	/**
	 * 是否属于河北西医平台
	 */
	public static boolean isHbxy() {
		boolean result = false;
		ZyyUserExtendVO zyyUser = SecurityUtils.getZyyUser();
		if (zyyUser == null)
			return result;
		Long zyyUserProvinceId = zyyUser.getZyyUserProvinceId();
		if (zyyUserProvinceId == null)
			return result;
		if (HBXY_PROVINCE_ID.toString().equals(zyyUserProvinceId.toString()))
			result = true;
		return result;
	}
	
	/**
	 * 是否属于新疆公卫省级
	 */
	public static boolean isXjgw(ZyyUserExtendVO zyyUser) {
		boolean result = false;
		if (zyyUser == null)
			return result;
		Long zyyUserProvinceId = zyyUser.getZyyUserProvinceId();
		if (zyyUserProvinceId == null)
			return result;
		if (XJGW_PROVINCE_ID.toString().equals(zyyUserProvinceId.toString()))
			result = true;
		return result;
	}
	
	public static boolean isXjgw() {
		return isXjgw(SecurityUtils.getZyyUser());
	}
	
	public static String ckxjDesc() {
		return isXjgw() ? Constants.XJGW_CKXJ_DESC : Constants.OTHER_CKXJ_DESC;
	}
	
	public static String zyysDesc() {
		return isXjgw() ? Constants.GWYS : Constants.ZYYS;
	}
	
	/**
	 * 是否有教学活动附件批量下载权限
	 */
	public static boolean hasTeachActAttDownloadPrivilege() {
		boolean result = false;
		ZyyUserExtendVO zyyUser = SecurityUtils.getZyyUser();
		if (zyyUser == null)
			return result;
		Long zyyUserOrgId = zyyUser.getZyyUserOrgId();
		if (zyyUserOrgId == null)
			return result;
		if (Arrays.asList(TEACH_ACT_ATT_DOWNLOAD_PRIVILEGE_ORG).contains(zyyUserOrgId))
			result = true;
		return result;
	}
	
	/**
	 * 获取机构配置的人员分类
	 */
	public static List<Resource> queryOrgUserCategory() {
		ZyyUserExtendVO zyyUser = SecurityUtils.getZyyUser();
		return SpringUtils.getSingleBeanOfType(YktStatDAO.class).queryUserCategory(new YktStatQuery(zyyUser.getZyyUserOrgId()));
	}
	
	/**
	 * 获取人员分类集合
	 */
	public static List<Resource> getUserCategoryList() {
		List<Resource> resiManageSubMenus = new ArrayList<>();
		Account account = SecurityUtils.getZyyAccount();
		if (account == null)
			return resiManageSubMenus;
		resiManageSubMenus = account.getResiManageSubMenus();
		return resiManageSubMenus;
	}
	
	public static List<String> getUserCategoryStrList() {
		return Lambda.extract(getUserCategoryList(), Lambda.on(Resource.class).getResourceName());
	}
	
	public static Map<Integer, String> getUserCategoryMap() {
		Map<Integer, String> resultMap = new HashMap<>();
		List<Resource> resources = getUserCategoryList();
		if (CollectionUtils.isEmpty(resources))
			return resultMap;
		for (Resource resource : resources)
			resultMap.put(resource.getResourceProp(), resource.getResourceName());
		return resultMap;
	}
	
	/**
	 * 根据人员分类名称获取人员分类编码
	 */
	public static Integer getUserCategoryKeyByValue(String value) {
		Integer key = null;
		if (StringUtils.isBlank(value))
			return key;
		Map<Integer, String> map = getUserCategoryMap();
		if (map.size() <= 0)
			return key;
		Set<Integer> keys = map.keySet();
		for (Integer k : keys) {
			if (value.equals(map.get(k))) {
				key = k;
				break;
			}
		}
		return key;
	}
	
	public static Integer[] getUserCategoryKeyByValue(String[] valueArr) {
		List<Integer> keys = new ArrayList<>();
		if (!ArrayUtils.isNullOrEmpty(valueArr)) {
			Integer key = null;
			for (String value : valueArr) {
				key = getUserCategoryKeyByValue(value);
				if (key != null)
					keys.add(key);
			}
		}
		return keys.toArray(new Integer[0]);
	}
	
	/**
	 * 是否显示人员分类
	 */
	public static boolean showUserCategory() {
		return CollectionUtils.isNotEmpty(getUserCategoryList()) ? true : false;
	}
	
	/**
	 * 获取人员分类名称
	 */
	public static String getCategoryStr(Integer userCategory) {
		return SpringUtils.getSingleBeanOfType(ZyyUserCategoryFacade.class).getCategoryStr(userCategory);
	}
	
}



















