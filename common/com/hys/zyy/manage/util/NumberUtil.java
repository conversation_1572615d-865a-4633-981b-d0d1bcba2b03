package com.hys.zyy.manage.util;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.regex.Pattern;

/**
 * 
 * 标题：学习
 * 
 * 时间: 2010 1 29
 * 
 * 描述：
 * 
 * 说明:
 */
public class NumberUtil {

	final static private Integer HOURS = 60 * 60;

	final static private String PERSENT_ZERO = "0%";

	final static private String PERSENT_ONE = "1%";

	final static private String PERSENT_HUNDRED = "100%";
	
    private static final int DEF_DIV_SCALE = 10;

	/**
	 * 把秒转换为小时
	 * 
	 * @param second
	 * @return
	 */
	public static String toHours(Long second) {

		// 为0的话 返回 0小时
		if (second == null || second.longValue() == 0) {
			return 0 + "小时";
		}

		// 小于60 返回时间加秒
		if (second < 60) {
			return second + "秒";
		}

		NumberFormat nf = NumberFormat.getInstance();
		nf.setMaximumFractionDigits(2);

		// 大于秒 小于小时 返回分钟
		if (second > 60 && second < HOURS) {
			return nf.format((second / 60)) + "分钟";
		}

		double d1 = second;

		// 返回小时
		return nf.format(d1 / HOURS) + "小时";
	}

	/**
	 * 计算百分比
	 * 
	 * @param sum
	 * @param value
	 * @param reservedWords 保留字
	 * @return
	 */
	public static String toPercent(Long sum, Long value, int reservedWords) {

		if (sum == null || sum.longValue() == 0) {
			return PERSENT_ZERO;
		}

		if (value == null || value.longValue() == 0) {
			return PERSENT_ZERO;
		}

		if (value > sum) {
			return PERSENT_HUNDRED;
		}

		if (value < (sum / 100)) {
			return PERSENT_ONE;
		}

		double d1 = sum;
		double d2 = value;
		NumberFormat nf = NumberFormat.getPercentInstance();
		nf.setMinimumFractionDigits(reservedWords);

		return nf.format(d2 / d1);
	}

	public static String toPercentOld(int sum, int value, int reservedWords) {

		if (sum == 0) {
			return PERSENT_ZERO;
		}

		if (value == 0) {
			return PERSENT_ZERO;
		}

		if (value > sum) {
			return PERSENT_HUNDRED;
		}

		if (value < (sum / 100)) {
			return PERSENT_ONE;
		}

		double d1 = sum;
		double d2 = value;
		NumberFormat nf = NumberFormat.getPercentInstance();
		nf.setMinimumFractionDigits(reservedWords);

		return nf.format(d2 / d1);
	}
	
	/**
	 * 计算百分比  有特殊处理
	 * 
	 * @param sum
	 * @param value
	 * @param reservedWords 保留字
	 * @return
	 */
	public static String toPercent(int sum, int value, int reservedWords) {
		/**
		 * JUST TO IT
		 * 数据为“100.00%”,显示为“100%”；
		 * 若数据为“0.00%”,显示为“0%”；
		 * 若数据为“79.00%”,显示为“79%”；
		 * 若数据为“69.10%”,显示为“69.10%”
		 */
		if (sum == 0) {
			return PERSENT_ZERO;
		}

		if (value == 0) {
			return PERSENT_ZERO;
		}

		if (value > sum) {
			return PERSENT_HUNDRED;
		}

		if (value < (sum / 100)) {
			return PERSENT_ONE;
		}

		double d1 = sum;
		double d2 = value;
		double d = mul(div(d2, d1), 100) ;
		
		return formatZeroTwo(String.valueOf(d))+"%";
	}
	
	/**
	 * 格式化字符串为整型
	 * @param name 名称
	 * @return
	 */
	public static int parseInteger(String name) {
		if (name == null || "".equals(name)) {
			return 0;
		}
		
		try {
			return Integer.valueOf(name);
		} catch (NumberFormatException ne) {
			return 0;
		}
	}
	
	/**
	 * 格式化字符串为整型
	 * @param name		 名称
	 * @param defaultNum 缺省值
	 * @return
	 */
	public static int parseInteger(String name, int defaultNum) {
		if (name != null && !name.equals("")) {
			int num = defaultNum;
			try {
				num = Integer.valueOf(name);
			} catch (Exception ignored) {
			}
			return num;
		} else {
			return defaultNum;
		}
	}
	
	/**
	 * 格式化字符串为长整型
	 * @param name 名称
	 * @return
	 */
	public static long parseLong(String name) {
		if (name == null || "".equals(name)) {
			return 0;
		}
		
		try {
			return Long.valueOf(name);
		} catch (NumberFormatException ne) {
			return 0;
		}
	}
	
	/**
	 * 格式化字符串为长整型
	 * @param name		 名称
	 * @param defaultNum 缺省值
	 * @return
	 */
	public static long parseLong(String name, long defaultNum) {
		if (name != null && !name.equals("")) {
			long num = defaultNum;
			try {
				num = Long.valueOf(name);
			} catch (Exception ignored) {
			}
			return num;
		} else {
			return defaultNum;
		}
	}
	
	/**
	 * 格式化字符串为长整型
	 * @param name 名称
	 * @return
	 */
	public static double parseDouble(String name) {
		if (name == null || "".equals(name)) {
			return 0;
		}

		try {
			return Double.valueOf(name);
		} catch (NumberFormatException ne) {
			return 0;
		}
	}

	/**
	 * 格式化字符串为Double
	 * @param name		 名称
	 * @param defaultNum 缺省值
	 * @return
	 */
	public static double parseDouble(String name, int defaultNum) {
		if (name != null && !name.equals("")) {
			double num = defaultNum;
			try {
				num = Double.valueOf(name);
			} catch (Exception ignored) {
			}
			return num;
		} else {
			return defaultNum;
		}
	}
	
	/**
	 * 取得保留数字的数值 无小数 不会填0补位
	 * @param number		数值
	 * @param reservedWords	保留字
	 * @return
	 */
	public static String toNumber(double number, int reservedWords) {
		if (number == 0) {
			return "0";
		}

		NumberFormat format = NumberFormat.getNumberInstance();
		format.setMaximumFractionDigits(reservedWords) ;
		return format.format(number);
	}
	
	/**
	 * double 类型相加
	 * @param v1
	 * @param v2
	 * @return
	 */
	public static double add(double v1, double v2){
        BigDecimal b1 = new BigDecimal(Double.toString(v1));
        BigDecimal b2 = new BigDecimal(Double.toString(v2));
        return b1.add(b2).doubleValue();
    }
	/**
	 * 加的计算
	 * @param v1
	 * @param v2
	 * @return
	 */
	public static BigDecimal addBig(BigDecimal v1, BigDecimal v2){
		if (v1 == null){
			v1 = BigDecimal.ZERO;
		}
		if (v2 == null){
			v2 = BigDecimal.ZERO;
		}
        return v1.add(v2);
    }

	/**
     * 提供（相对）精确的除法运算，当发生除不尽的情况时，精确到 小数点以后10位，以后的数字四舍五入。
     * @param v1  被除数
     * @param v2  除数
     * @return 两个参数的商
     */
    public static double div(double v1, double v2){
        return div(v1, v2, DEF_DIV_SCALE);
    }
    
    /**
     * 提供精确的乘法运算。
     * 
     * @param v1
     *            被乘数
     * @param v2
     *            乘数
     * @return 两个参数的积
     */
 
    public static double mul(double v1, double v2){
        BigDecimal b1 = new BigDecimal(Double.toString(v1));
        BigDecimal b2 = new BigDecimal(Double.toString(v2));
        return b1.multiply(b2).doubleValue();
    }
 
    
    /**
     * 提供（相对）精确的除法运算。当发生除不尽的情况时，由scale参数指 定精度，以后的数字四舍五入。
     * 
     * @param v1
     *            被除数
     * @param v2
     *            除数
     * @param scale
     *            表示表示需要精确到小数点以后几位。
     * @return 两个参数的商
     */
 
    public static double div(double v1, double v2, int scale) {
        if (scale < 0){
            throw new IllegalArgumentException("The   scale   must   be   a   positive   integer   or   zero");
        }
        BigDecimal b1 = new BigDecimal(Double.toString(v1));
        BigDecimal b2 = new BigDecimal(Double.toString(v2));
        return b1.divide(b2, scale, BigDecimal.ROUND_HALF_UP).doubleValue();
    }
    
    /**
     * 提供精确的减法运算。
     * @param value1 被减数
     * @param value2 减数
     * @return 两个参数的差
     */
    public static double sub(Double value1, Double value2) {
    	BigDecimal b1 = new BigDecimal(Double.toString(value1));
        BigDecimal b2 = new BigDecimal(Double.toString(value2));
        return b1.subtract(b2).doubleValue();
    }
    
    /**
     * 这个方法挺简单的。
     * DecimalFormat is a concrete subclass of NumberFormat that formats decimal numbers. 
     * @param d
     * @return
     */
    public static String formatDouble4(double d) {
        DecimalFormat df = new DecimalFormat("#.00");
        return df.format(d);
    }
    
    public static String formatDouble2(Double val){
    	DecimalFormat df = new DecimalFormat("0.00");
        return df.format(val);
    }
    /**
     * 仅保留一位，如果小数位是0，直接省略；
     * @param str
     * @return
     * <AUTHOR>
     * @date 2019-3-27下午2:20:20
     */
    public static String formatZeroOne(String str){
    	if (StringUtils.isBlank(str)){
    		return "0";
    	}
    	BigDecimal b = new BigDecimal(str);
    	DecimalFormat df = new DecimalFormat("0.0");
    	String result = df.format(b);
    	String[] array = result.split("\\.");//java中需要转义一下
    	if ("0".equals(array[1])){
    		return array[0];
    	}
    	return result;
    }
    /**
     * 保留两位，如果小数位都是0，直接省略
     * @param str
     * @return
     * <AUTHOR>
     * @date 2019-3-27下午2:37:29
     */
    public static String formatZeroTwo(String str){
    	if (StringUtils.isBlank(str)){
    		return "0";
    	}
    	BigDecimal b = new BigDecimal(str);
    	DecimalFormat df = new DecimalFormat("0.00");
    	String result = df.format(b);
    	String[] array = result.split("\\.");//java中需要转义一下
    	if ("00".equals(array[1])){
    		return array[0];
    	}
    	return result;
    }
    
	/*
	 * 判断是否为整数
	 * @param str 传入的字符串
	 * @return 是整数返回true,否则返回false
	 */
	public static boolean isInteger(String str) {
		Pattern pattern = Pattern.compile("^[-\\+]?[\\d]*$");
		return pattern.matcher(str).matches();
	}

	public static void main(String[] arg) {
		System.out.println(NumberUtil.isInteger("1"));
	}
}
