package com.hys.zyy.manage.util;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.validator.GenericValidator;

import com.hys.zyy.manage.exception.FrameworkException;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：陈明凯 2011-1-5
 * 
 * 描述：分页
 * 
 * 说明:
 */
public class PageUtil {

	/**
	 * 分页-sql
	 * 
	 * @param sql
	 * @param pageSize
	 * @param currentPage
	 * @return
	 */
	// public static String getPageSql(String sql, int pageSize, int
	// currentPage) {
	// StringBuffer pagingSelect = new StringBuffer(100);
	// pagingSelect
	// .append("select * from(select row_.*, rownum rownum_ from(");
	// pagingSelect.append(sql);
	// pagingSelect.append(") row_ where rownum<=");
	// pagingSelect.append(pageSize * (currentPage + 1));
	// pagingSelect.append(") where rownum_ >");
	// pagingSelect.append(pageSize * currentPage);
	//
	// return pagingSelect.toString();
	// }
	public static String getPageSql(String sql, int pageSize, int currentPage) {
		StringBuffer sb = new StringBuffer();
		sb.append("select * from(select row_.*, rownum rownum_ from(");
		sb.append(sql);
		sb.append(") row_ where rownum<=");
		sb.append(pageSize * currentPage);
		sb.append(") where rownum_>");
		sb.append(pageSize * (currentPage - 1));
		return sb.toString();
	}
	
	public static String getPageSqlWithOrderBy(String sql, int pageSize, int currentPage) {
		StringBuffer sb = new StringBuffer();
		sb.append("select * from(select row_.*, rownum rownum_ from(");
		sb.append(sql);
		sb.append(") row_ ");
		sb.append(") where rownum_>");
		sb.append(pageSize * (currentPage - 1));
		sb.append(" and rownum<=");
		sb.append(pageSize);
		return sb.toString();
	}
	
	public static String getPageSqlByRowNumber(String sql, long pageSize, long rowNumber) {
		StringBuffer sb = new StringBuffer();
		sb.append("select * from(select row_.*, rownum rownum_ from(");
		sb.append(sql);
		sb.append(") row_ where rownum <= ");
		sb.append(pageSize + rowNumber);
		sb.append(") where rownum_ > ");
		sb.append(rowNumber);
		return sb.toString();
	}

	/**
	 * 取page index
	 * 
	 * @param request
	 * @return
	 * @throws FrameworkException
	 */
	public static Integer getPageIndex(HttpServletRequest request)
			throws FrameworkException {
		try {
			return GenericValidator.isBlankOrNull(request.getParameter("page")) ? 1
					: (Integer.parseInt(request.getParameter("page")));
		} catch (NumberFormatException ne) {
			return 1;
		}
	}
}
