package com.hys.zyy.manage.util;

import java.util.regex.Pattern;

/**
 * 校验工具类
 * <AUTHOR> 2018-4-24 下午5:55:22
 */
public class PatternUtil {
	// 正则表达式：验证手机号
	public static final String REGEX_MOBILE = "^(((13[0-9]{1})|(14[0-9]{1})|(15[0-9]{1})|(16[0-9]{1})|(17[0-9]{1})|(18[0-9]{1})|(19[0-9]{1}))+\\d{8})$";
	// 正则表达式：验证身份证
	public static final String REGEX_ID_CARD = "(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)";
	// 正数（正整数 + 0）
	public static final String POSITIVE_NUMBER = "^[1-9]\\d*|0$";;
	
	/**
	 * 校验手机号
	 * @param mobile
	 * @return 校验通过返回true，否则返回false
	 */
	public static boolean isMobile(String mobile) {
		return Pattern.matches(REGEX_MOBILE, mobile);
	}
	
	/**
	 * 校验身份证
	 * @param idCard
	 * @return 校验通过返回true，否则返回false
	 */
	public static boolean isIDCard(String idCard) {
		return Pattern.matches(REGEX_ID_CARD, idCard);
	}
	
	/**
	 * 正数（正整数 + 0）
	 */
	public static boolean isPositiveNumber(String input) {
		if (StringUtils.isBlank(input))
			return false;
		return Pattern.matches(POSITIVE_NUMBER, StringUtils.trim(input));
	}
	
}











