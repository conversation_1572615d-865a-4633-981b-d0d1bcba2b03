package com.hys.zyy.manage.util;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;



public class Property {
	
	public Property(){
		
	}
	
	public Property(String propertyName,String propertyTitle){
		this.propertyName=propertyName;
		this.propertyTitle=propertyTitle;
	}
	
	public Property(Integer propertyType,String propertyName,String propertyTitle){
		this.propertyType=propertyType!=null?propertyType:1;
		this.propertyName=propertyName;
		this.propertyTitle=propertyTitle;
	}
	
	public Property(Integer propertyType,String propertyName,String propertyTitle,Object key){
		this.propertyType=propertyType!=null?propertyType:1;
		this.propertyName=propertyName;
		this.propertyTitle=propertyTitle;
		this.key=key;
	}
	
	public Property(String propertyName,String propertyTitle,List<Property> childProperty){
		this.propertyName=propertyName;
		this.propertyTitle=propertyTitle;
		this.childProperty=childProperty;
	}
	
	/**
	 * 属性类型  1 基础类弄数据   2. List数据  3 Map数据
	 */
	private int propertyType=1;
	
	/**
	 * 属性名称
	 */
	private String propertyName;
	
	/**
	 * 属性标题
	 */
	private String propertyTitle;
	
	/**
	 * 存在子记录时是否显示合并标题
	 */
	private int isShowMergeTitle=1;
	
	/**
	 * 下级属性(有子纪录）（propertyType为1 从当前对像反射数据， propertyType为2 从集合对像反射数据）
	 */
	private List<Property> childProperty;
	
	/**
	 * 只对MAP数据有效 map key 
	 */
	private Object key;
	
	/**
	 * 只对MAP数据有效 map值的属性  如果为空，值类型，直接接取，如果不为空，反射些属性的值
	 */
	private String valueProperty;
	
	/**
	 * 字典值
	 */
	private Map<Object,String> dictValue;
	
	/**
	 * 格式化字符串
	 */
	private String formatString;
	
	/**
	 * 取得值
	 * @param value
	 * @return
	 */
	public String getValue(Object value){
		if(formatString!=null && !formatString.isEmpty() ){
			return String.format(formatString, value);
		}
		if(dictValue!=null){
			return dictValue.get(value);
		}
		else{
			return value.toString();
		}
	}
	
	
	public String getValue(List<Object> values){
		if(formatString!=null && !formatString.isEmpty() ){
			return values.size()>0?String.format(formatString, values.get(0)):"";
		}
		if(dictValue!=null){
			return values.size()>0 && dictValue.get(values.get(0))!=null ?dictValue.get(values.get(0)):dictValue.get(99);
		}
		else{
			StringBuilder rs=new StringBuilder();
			for(int i=0;i<values.size();i++){
				Object v=values.get(i);
				if(v!=null){
					if(v!=null){
						if(v instanceof Date || v instanceof Timestamp){
							SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
							rs.append(formatter.format(v));
						}
						else{
							rs.append(values.get(i).toString());
						}
						if(i!=values.size()-1){
							rs.append("-");
						}
					}
				}
			}
			return rs.toString();
		}
	}
	
	/**
	 * 取得列数
	 * @return
	 */
	public Integer getColumnSize(Property property){
		int i=0;
		if(property.getChildProperty()==null || property.getChildProperty().size()<1){
			i=1;
		}
		else{
			for(Property p :property.getChildProperty()){
				i=i+getColumnSize(p);
			}
		}
		
		return i;
	}
	

	public String getPropertyName() {
		return propertyName;
	}

	public void setPropertyName(String propertyName) {
		this.propertyName = propertyName;
	}

	public String getPropertyTitle() {
		return propertyTitle;
	}

	public void setPropertyTitle(String propertyTitle) {
		this.propertyTitle = propertyTitle;
	}

	public List<Property> getChildProperty() {
		return childProperty;
	}

	public void setChildProperty(List<Property> childProperty) {
		this.childProperty = childProperty;
	}

	public int getPropertyType() {
		return propertyType;
	}

	public void setPropertyType(int propertyType) {
		this.propertyType = propertyType;
	}

	public Object getKey() {
		return key;
	}

	public void setKey(Object key) {
		this.key = key;
	}

	public String getFormatString() {
		return formatString;
	}

	public void setFormatString(String formatString) {
		this.formatString = formatString;
	}

	public Map<Object,String> getDictValue() {
		return dictValue;
	}

	public void setDictValue(Map<Object,String> dictValue) {
		this.dictValue = dictValue;
	}


	public int getIsShowMergeTitle() {
		return isShowMergeTitle;
	}

	public void setIsShowMergeTitle(int isShowMergeTitle) {
		this.isShowMergeTitle = isShowMergeTitle;
	}

	public String getValueProperty() {
		return valueProperty;
	}

	public void setValueProperty(String valueProperty) {
		this.valueProperty = valueProperty;
	}

}
