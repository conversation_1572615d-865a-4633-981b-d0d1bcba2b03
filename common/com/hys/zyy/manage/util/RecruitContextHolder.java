package com.hys.zyy.manage.util;

import javax.servlet.http.HttpServletRequest;

import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.util.WebUtils;


/**
 * 招录信息 Holder
 * <AUTHOR>
 *
 */
public class RecruitContextHolder {
	
	public static final String ATTRIBUTE_NAME_HOSPITAL_TYPE = "__htp__";
	
	public static final String ATTRIBUTE_NAME_MODE_TYPE = "__model__";

	/**
	 * 返回当前所选的医院类型
	 * 1、当前请求里面有__htp__参数，使用该参数判断
	 * 2、当前请求里没有__htp__参数，使用会话中的参数
	 * 3、如果请求和会话都没有该参数，默认医院类型为西医
	 * 4、保存最终医院类型到当前会话中。
	 * @return
	 */
	public static Integer getHospitalType() {
		HttpServletRequest request = ((ServletRequestAttributes)RequestContextHolder.getRequestAttributes()).getRequest();
		return (Integer)WebUtils.getSessionAttribute(request, ATTRIBUTE_NAME_HOSPITAL_TYPE);
	}

	/**
	 * 释放掉当前选择的医院类型
	 */
	public static Integer releaseHospitalType() {
		HttpServletRequest request = ((ServletRequestAttributes)RequestContextHolder.getRequestAttributes()).getRequest();
		Integer value = (Integer)WebUtils.getSessionAttribute(request, ATTRIBUTE_NAME_HOSPITAL_TYPE);
		request.getSession(true).removeAttribute(ATTRIBUTE_NAME_HOSPITAL_TYPE);
		request.getSession(true).removeAttribute(ATTRIBUTE_NAME_MODE_TYPE);
		return value;
	}
	
	/**
	 * 当前的使用模式，复杂模式采用位比对，简单模式采用正常比对
	 * @return
	 */
	public static boolean ifSimpleMode() {
		HttpServletRequest request = ((ServletRequestAttributes)RequestContextHolder.getRequestAttributes()).getRequest();
		Boolean bool = (Boolean)WebUtils.getSessionAttribute(request, ATTRIBUTE_NAME_MODE_TYPE);
		return bool != null ? bool.booleanValue() : false;
	}
	
	/**
	 * 设置当前选择医院类型
	 */
	public static void setHospitalType(Integer type) {
		setHospitalType(type, false);
	}

	public static void setHospitalType(Integer type, boolean simple) {
		if(type != null) {
			HttpServletRequest request = ((ServletRequestAttributes)RequestContextHolder.getRequestAttributes()).getRequest();
			WebUtils.setSessionAttribute(request, ATTRIBUTE_NAME_HOSPITAL_TYPE, type);
			WebUtils.setSessionAttribute(request, ATTRIBUTE_NAME_MODE_TYPE, simple);
		}
	}
	
}
