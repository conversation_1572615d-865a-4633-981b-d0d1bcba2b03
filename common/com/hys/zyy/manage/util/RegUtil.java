package com.hys.zyy.manage.util;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.lang.StringUtils;
/**
 * 校验类
 * <AUTHOR>
 * 2018-3-23 上午10:33:23
 */
public class RegUtil {
	/**
	 * 根据后缀校验是否是图片或者pdf文件
	 * @param suffix
	 * @return
	 * 2018-3-23上午10:32:42
	 * <AUTHOR>
	 */
	public static boolean checkFileSuffix(String suffix) {
		if (StringUtils.isEmpty(suffix)){
			return false;
		}
		Pattern p = Pattern.compile("(.jpg|.png|.gif|.ps|.jpeg|.pdf)");
		Matcher m = p.matcher(suffix);
		return m.find();
	}
	
	public static boolean checkExcelFileSuffix(String suffix) {
		if (StringUtils.isEmpty(suffix)){
			return false;
		}
		Pattern p = Pattern.compile("(.xls)");
		Matcher m = p.matcher(suffix);
		return m.find();
	}
	
	public static boolean checkExcelFileSuffix2(String suffix) {
		if (StringUtils.isEmpty(suffix)){
			return false;
		}
		if (".csv".equals(suffix) || ".xls".equals(suffix) || ".xlsx".equals(suffix)){
			return true;
		}
		 
		return false;
	}
}
