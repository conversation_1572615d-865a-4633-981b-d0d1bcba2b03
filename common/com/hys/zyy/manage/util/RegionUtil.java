package com.hys.zyy.manage.util;

import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import com.hys.zyy.manage.model.ZyyRegion;

/**
 * 站点工具类
 * <AUTHOR>
 * 2018-4-24 上午9:48:03
 */
public class RegionUtil {
	private static String ZYY_REGION_MAP = "ZYY_REGION_MAP";
	//克拉玛依中心医院
	public static final Long KLMY_HOSPITAL_ID = Long.valueOf(1167) ;

	@SuppressWarnings("unchecked")
	public static ZyyRegion getZyyRegion(HttpServletRequest request){
		Map<String, ZyyRegion> zyyRegionMap = (Map<String, ZyyRegion>)request.getSession().getServletContext().getAttribute(ZYY_REGION_MAP);
		ZyyRegion z = zyyRegionMap.get(request.getServerName().toLowerCase());
		return z ;
	}


	/**
	 * 判断是否是实习生系统
	 *
	 * gcsxsrct.haoyisheng.com/newrct/indexgo    -- 90010004
	 *  shixiadmin 登录 【权限】--【账号分配】下面的就是所有的实习系统
	 * @param zyyProvinceId  省份id
	 * @return true : 是实习生系统  false：不是
	 * <AUTHOR>
	 * @date 2019-4-29下午4:05:31
	 */
	public static boolean checkInternSystem(String zyyProvinceId) {
		boolean flag = false;
		String[] internSystem = {"90010004"};
		int len = internSystem.length;
		for (int i = 0; i < len; i++) {
			String item = internSystem[i];
			if (item.equals(zyyProvinceId)){
				flag = true;
				break;
			}
		}
		return flag;
	}

	/**
	 * 判断是否是云南系统
	 * @param zyyProvinceId
	 * @return true : 是云南系统  false：不是
	 * <AUTHOR>
	 * @date 2019-6-18下午2:52:49
	 */
	public static boolean checkYunnanSystem(String zyyProvinceId) {
		boolean flag = false;
		String[] internSystem = {"69000000","69100000"};
		int len = internSystem.length;
		for (int i = 0; i < len; i++) {
			String item = internSystem[i];
			if (item.equals(zyyProvinceId)){
				flag = true;
				break;
			}
		}
		return flag;
	}

	/**
	 * 判断是否是唐山工人医院
	 * @param zyyProvinceId
	 * @return true : 唐山工人医院
	 * <AUTHOR>
	 * @date 2019-6-18下午2:52:49
	 */
	public static boolean checkTangShanSystem(Long zyyProvinceId) {
		String idStr = ConfigPropertiesUtil.getInstance().getPropMap().get("REPORT_HOSPITAL");
		String[] ids = idStr.split(",");
		boolean flg = false;
		for (int i = 0; i <ids.length; i++) {
			if(ids[i].equals(zyyProvinceId+"")){
				flg = true;
				break;
			}
		}
		return flg;
	}



	/**
	 * 判断是否是新疆系统
	 * @param zyyProvinceId
	 * @return true : 是新疆系统  false：不是
	 * <AUTHOR>
	 * @date 2019-6-20下午1:24:44
	 */
	public static boolean checkXinjiangSystem(String zyyProvinceId) {
		boolean flag = false;
		String[] internSystem = {"60000000"};
		int len = internSystem.length;
		for (int i = 0; i < len; i++) {
			String item = internSystem[i];
			if (item.equals(zyyProvinceId)){
				flag = true;
				break;
			}
		}
		return flag;
	}

	/**
	 * 判断是否是 德阳市人民医院
	 * @param zyyOrgId
	 * @return
	 * <AUTHOR>
	 * @date 2019-8-20下午4:08:54
	 */
	public static boolean checkDeyangCityHospital(String zyyOrgId) {
		boolean flag = false;
		String[] systemArray = {"9242"}; // 测试 1167  正式  9242
		int len = systemArray.length;
		for (int i = 0; i < len; i++) {
			String item = systemArray[i];
			if (item.equals(zyyOrgId)){
				flag = true;
				break;
			}
		}
		return flag;
	}

}
