package com.hys.zyy.manage.util;

import java.util.Arrays;

import com.hys.security.util.SecurityUtils;
import com.hys.zyy.manage.model.ZyyUserExtendVO;

/**
 * 查询条件是否添加人员类型工具类
 * <AUTHOR>
 */
public class ResidencySourceUtil {

	// 需要隐藏人员类型的省ID
	public static final Long[] HIDE_PROVINCE_IDS = new Long[] { 
		90010004L // 实习生系统
		//, 60000000L // 新疆
		//, 69000000L // 云南
	};

	public static Long[] getHideProvinceIds() {
		return HIDE_PROVINCE_IDS;
	}

	public static boolean isHide() {
		ZyyUserExtendVO zyyUser = SecurityUtils.getZyyUser();
		Long userProvinceId = zyyUser.getZyyUserProvinceId();
		boolean isHide = Arrays.asList(HIDE_PROVINCE_IDS).contains(userProvinceId);
		return isHide;
	}
	
	public static boolean isHideNew(ZyyUserExtendVO zyyUser) {
		Long userProvinceId = zyyUser.getZyyUserProvinceId();
		boolean isHide = Arrays.asList(HIDE_PROVINCE_IDS).contains(userProvinceId);
		return isHide;
	}

}
