package com.hys.zyy.manage.util;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.apache.log4j.LogManager;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;

import com.alibaba.fastjson.JSONObject;
import com.hys.zyy.manage.constants.RedisConstants;
import com.hys.zyy.manage.json.BdpResult;
import com.hys.zyy.manage.json.BdpUserInfoResult;
import com.hys.zyy.manage.util.redis.RedisClientTemplate;
import com.hys.zyy.manage.web.RequestUtils;

/**
 * 单点登录工具类
 */
@Component
public class SSOUtil {

	private static Logger logger = LogManager.getLogger(SSOUtil.class.getName());
	
	public static final String ACTIVE_ON = "on", ACTIVE_OFF = "off";
	public static final Map<String, String> CONFIG_PROPERTIE_MAP;
	
	@Autowired
	private RedisClientTemplate redisClientTemplate;
	
	static {
		CONFIG_PROPERTIE_MAP = ConfigPropertiesUtil.getInstance().getPropMap();
	}

	public static String getActivePC() {
		WebApplicationContext webApplicationContext = ContextLoader.getCurrentWebApplicationContext();
		ServletContext servletContext = webApplicationContext.getServletContext();
		String active = servletContext.getInitParameter("pc.sso.active");
		logger.info("pc.sso.active=" + active);
		return active;
	}
	
	public static String getActiveH5() {
		WebApplicationContext webApplicationContext = ContextLoader.getCurrentWebApplicationContext();
		ServletContext servletContext = webApplicationContext.getServletContext();
		String active = servletContext.getInitParameter("h5.sso.active");
		logger.info("h5.sso.active=" + active);
		return active;
	}
	
	/**
	 * PC是否开启SSO登录
	 */
	public static boolean isActivePC() {
		return ACTIVE_ON.equals(getActivePC()) ? true : false;
	}
	
	/**
	 * H5是否开启SSO登录
	 */
	public static boolean isActiveH5() {
		return ACTIVE_ON.equals(getActiveH5()) ? true : false;
	}
	
	public static String getDomain() {
		return CONFIG_PROPERTIE_MAP.get(LogicUtils.getPattern() + ".sso.domain");
	}

	public static String getSecretkey() {
		return CONFIG_PROPERTIE_MAP.get(LogicUtils.getPattern() + ".sso.secretkey");
	}
	
	public static String getPcLoginUrl() {
		return CONFIG_PROPERTIE_MAP.get(LogicUtils.getPattern() + ".pc.login.url");
	}
	
	public static String getH5LoginUrl() {
		return CONFIG_PROPERTIE_MAP.get(LogicUtils.getPattern() + ".h5.login.url");
	}
	
	public static String buildSign(String loginId, String timestamp, String nonce) {
		StringBuffer buffer = new StringBuffer();
		buffer.append("loginId=");
		buffer.append(loginId);
		buffer.append("&nonce=");
		buffer.append(nonce);
		buffer.append("&timestamp=");
		buffer.append(timestamp);
		buffer.append("&key=");
		buffer.append(getSecretkey());
		String sign = MD5NewUtil.encrypByMd5(buffer.toString());
		return sign;
	}
	
	/**
	 * PC授权
	 */
	public static void auth(HttpServletRequest req, HttpServletResponse res) {
		PrintWriter out = null;
		try {
			out = res.getWriter();
			out.flush();// 清空缓存
			out.println("<SCRIPT LANGUAGE='JavaScript'>");
			out.println("alert('用户信息已失效，请重新登录')");
			out.println("parent.parent.parent.location = '" + SSOUtil.getPcLoginUrl() + "?redirect=" + RequestUtils.getDomainNameAndProjectName(req) + "/sso/autoLogin" + "'");
			out.println("</SCRIPT>");
		} catch (IOException ex) {
			ex.printStackTrace();
		} finally {
			if (out != null)
				out.close();
		}
	}
	
	/**
	 * H5授权
	 */
	public static void h5Auth(HttpServletRequest req, HttpServletResponse res) {
		PrintWriter out = null;
		try {
			out = res.getWriter();
			out.flush();// 清空缓存
			out.println("<SCRIPT LANGUAGE='JavaScript'>");
			out.println("alert('用户信息已失效，请重新登录')");
			out.println("location = '" + SSOUtil.getH5LoginUrl() + "?redirect=" + RequestUtils.getDomainNameAndProjectName(req) + "/app/sso/autoLogin" + "'");
			out.println("</SCRIPT>");
		} catch (IOException ex) {
			ex.printStackTrace();
		} finally {
			if (out != null)
				out.close();
		}
	}
	
	/**
	 * Ticket校验
	 */
	public static BdpResult checkTicket(String logoutUrl, String ticket) {
		if (StringUtils.isBlank(ticket))
			return BdpResult.failure("ticket为空！");
		String ssoLogoutCall = logoutUrl + "/sso/logoutCall";
		String url = getDomain() + "/sso/checkTicket";
		System.out.println("---------------url=" + url + ", ssoLogoutCall=" + ssoLogoutCall + ", ticket=" + ticket);
		List<NameValuePair> params = new ArrayList<NameValuePair>();
		params.add(new BasicNameValuePair("ssoLogoutCall", ssoLogoutCall));
		params.add(new BasicNameValuePair("ticket", ticket));
		String resStr = HttpUtil.doPostHttpRequestFormUrlencoded(url, params);
		System.out.println("---------------checkTicketResult=" + resStr);
		BdpResult br = JSONObject.parseObject(resStr, BdpResult.class);
		return br;
	}
	
	/**
	 * 单点主动注销
	 */
	public static BdpResult signout(String bdpUserId) {
		if (StringUtils.isBlank(bdpUserId))
			return BdpResult.failure("bdpUserId为空！");
		String timestamp = String.valueOf(System.currentTimeMillis());
		String nonce = UuidUtil.getUuid();
		String sign = buildSign(bdpUserId, timestamp, nonce);
		String url = getDomain() + "/sso/signout";
		System.out.println("---------------url=" + url + ", bdpUserId=" + bdpUserId + ", timestamp=" + timestamp + ", nonce=" + nonce + ", sign=" + sign);
		List<NameValuePair> params = new ArrayList<NameValuePair>();
		params.add(new BasicNameValuePair("loginId", bdpUserId));
		params.add(new BasicNameValuePair("timestamp", timestamp));
		params.add(new BasicNameValuePair("nonce", nonce));
		params.add(new BasicNameValuePair("sign", sign));
		String resStr = HttpUtil.doPostHttpRequestFormUrlencoded(url, params);
		System.out.println("---------------signout=" + resStr);
		BdpResult br = JSONObject.parseObject(resStr, BdpResult.class);
		return br;
	}
	
	/**
	 * 注销回调
	 */
	public BdpResult logoutCall(String bdpUserId, String timestamp, String nonce, String sign) {
		System.out.println("---------------bdpUserId=" + bdpUserId + ", timestamp=" + timestamp + ", nonce=" + nonce + ", sign=" + sign);
		if(StringUtils.isBlank(bdpUserId))
			return BdpResult.failure("bdpUserId为空！");
		if(StringUtils.isBlank(timestamp))
			return BdpResult.failure("timestamp为空！");
		if(StringUtils.isBlank(nonce))
			return BdpResult.failure("nonce为空！");
		if(StringUtils.isBlank(sign))
			return BdpResult.failure("sign为空！");
		String safeSign = buildSign(bdpUserId, timestamp, nonce);
		if(!safeSign.equals(sign))
			return BdpResult.failure("sign无效！");
		redisClientTemplate.del(RedisConstants.BDP_USER_ID + bdpUserId);
		return BdpResult.success("操作成功!");
	}
	
	/**
	 * 获取BDP用户信息
	 */
	public static void getUserInfoById(String bdpUserId) {
		if (StringUtils.isBlank(bdpUserId))
			return;
		String timestamp = String.valueOf(System.currentTimeMillis());
		String nonce = UuidUtil.getUuid();
		String sign = buildSign(bdpUserId, timestamp, nonce);
		String url = getDomain() + "/sso/getUserInfoById";
		System.out.println("---------------url=" + url + ", bdpUserId=" + bdpUserId + ", timestamp=" + timestamp + ", nonce=" + nonce + ", sign=" + sign);
		List<NameValuePair> params = new ArrayList<NameValuePair>();
		params.add(new BasicNameValuePair("loginId", bdpUserId));
		params.add(new BasicNameValuePair("timestamp", timestamp));
		params.add(new BasicNameValuePair("nonce", nonce));
		params.add(new BasicNameValuePair("sign", sign));
		String resStr = HttpUtil.doPostHttpRequestFormUrlencoded(url, params);
		System.out.println("---------------getUserInfoById=" + resStr);
		BdpResult br = JSONObject.parseObject(resStr, BdpResult.class);
		System.out.println(br);
	}
	
	/**
	 * 获取BDP用户信息（返回的信息更全）
	 */
	public static BdpUserInfoResult getUserInfoByUserId(String bdpUserId) {
		if (StringUtils.isBlank(bdpUserId))
			return BdpUserInfoResult.failure("bdpUserId为空！");
		String url = getDomain() + "/user/getUserInfoByUserId/";
		System.out.println("---------------url=" + url + ", bdpUserId=" + bdpUserId);
		String resStr = HttpUtil.doGet(url + bdpUserId);
		System.out.println("---------------getUserInfoByUserId=" + resStr);
		BdpUserInfoResult br = JSONObject.parseObject(resStr, BdpUserInfoResult.class);
		return br;
	}
	
	public static void main(String[] args) {
		BdpUserInfoResult br = getUserInfoByUserId("4072608");
		System.out.println(br);
	}
	
}

















