package com.hys.zyy.manage.util;

/**
 * 字符串加密
 * <AUTHOR>
 */
public class SecurityTextUtil {
    /**
     * 手机号码加密，不验证手机号码是否有效
     * eg：13618435012 --> 137****5047
     */
    public static String phoneSecurityText(String phone) {
        return phone.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
    }

    /**
     * 身份证号码加密，不验证身份证号码是否有效
     * eg：152122199512123432 --> 1521****3432
     */
    public static String idCardSecurityText(String idCard) {
        return idCard.replaceAll("(\\d{4})\\d{10}(\\w{4})", "$1****$2");
    }

}
