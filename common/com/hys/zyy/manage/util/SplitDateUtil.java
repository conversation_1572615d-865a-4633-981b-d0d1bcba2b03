package com.hys.zyy.manage.util;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang.StringUtils;

import com.hys.zyy.manage.model.vo.KeyValueForDate;

public class SplitDateUtil {
	
	public static void main(String[] args) {
		String str = "核医学科主任兼市核医学检测中心主任";
		System.out.println(str.length());
		
		 List<KeyValueForDate> list = SplitDateUtil.getKeyValueForDate("2015-06-15","2015-06-15");
	        System.out.println("开始日期--------------结束日期");
	        for(KeyValueForDate date : list){
	            System.out.println(date.getStartDate()+"-----"+date.getEndDate());
	        }
	}
	
	 /**
     * 根据一段时间区间，按月份拆分成多个时间段
     * @param startDate 开始日期
     * @param endDate  结束日期
     * @return
     */
    public static List<KeyValueForDate> getKeyValueForDate(String startDate,String endDate) {
        List<KeyValueForDate> list = null;
        try {
            list = new ArrayList<KeyValueForDate>();
            if (StringUtils.isBlank(startDate) || StringUtils.isBlank(startDate)){
            	return list;
            }

            String firstDay = "";
            String lastDay = "";
            String lastDayYm = "";
            Date d1 = new SimpleDateFormat("yyyy-MM-dd").parse(startDate);// 定义起始日期

            Date d2 = new SimpleDateFormat("yyyy-MM-dd").parse(endDate);// 定义结束日期
            
            String endDateYm = new SimpleDateFormat("yyyy-MM").format(d2);// 定义结束日期

            Calendar dd = Calendar.getInstance();// 定义日期实例
            dd.setTime(d1);// 设置日期起始时间
            Calendar cale = Calendar.getInstance();

            Calendar c = Calendar.getInstance();
            c.setTime(d2);

            int startDay = dd.get(Calendar.DATE);
            int endDay = c.get(Calendar.DATE);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            SimpleDateFormat sdfYm = new SimpleDateFormat("yyyy-MM");

            KeyValueForDate keyValueForDate = null;
            if (dd.getTime().after(d2)){
            	return list;
            }

            while (dd.getTime().before(d2)) {// 判断是否到结束日期
                keyValueForDate = new KeyValueForDate();
                cale.setTime(dd.getTime());

                if(dd.getTime().equals(d1)){
                    cale.set(Calendar.DAY_OF_MONTH, dd
                            .getActualMaximum(Calendar.DAY_OF_MONTH));
                    lastDay = sdf.format(cale.getTime());
                    lastDayYm = sdfYm.format(cale.getTime());
                    keyValueForDate.setStartDate(sdf.format(d1));
                    if (endDateYm.equals(lastDayYm)){
                    	keyValueForDate.setEndDate(endDate);
                    } else {
                    	keyValueForDate.setEndDate(lastDay);
                    }

                }else if(dd.get(Calendar.MONTH) == c.get(Calendar.MONTH) && dd.get(Calendar.YEAR) == c.get(Calendar.YEAR)){
                    cale.set(Calendar.DAY_OF_MONTH,1);//取第一天
                    firstDay = sdf.format(cale.getTime());

                    keyValueForDate.setStartDate(firstDay);
                    keyValueForDate.setEndDate(sdf.format(d2));

                }else {
                    cale.set(Calendar.DAY_OF_MONTH,1);//取第一天
                    firstDay = sdf.format(cale.getTime());

                    cale.set(Calendar.DAY_OF_MONTH, dd
                            .getActualMaximum(Calendar.DAY_OF_MONTH));
                    lastDay = sdf.format(cale.getTime());
                    lastDayYm = sdfYm.format(cale.getTime());
                    keyValueForDate.setStartDate(firstDay);
                    if (endDateYm.equals(lastDayYm)){
                    	keyValueForDate.setEndDate(endDate);
                    } else {
                    	keyValueForDate.setEndDate(lastDay);
                    }

                }
                list.add(keyValueForDate);
                dd.add(Calendar.MONTH, 1);// 进行当前日期月份加1

            }

            if(endDay<startDay){
                keyValueForDate = new KeyValueForDate();

                cale.setTime(d2);
                cale.set(Calendar.DAY_OF_MONTH,1);//取第一天
                firstDay = sdf.format(cale.getTime());

                keyValueForDate.setStartDate(firstDay);
                keyValueForDate.setEndDate(sdf.format(d2));
                list.add(keyValueForDate);
            } 
            //相等
            if (startDate.equals(endDate)){
            	keyValueForDate = new KeyValueForDate();
                keyValueForDate.setStartDate(firstDay);
                keyValueForDate.setEndDate(endDate);
                list.add(keyValueForDate);
            }
        } catch (ParseException e) {
            return null;
        }

        return list;
    }
 
}