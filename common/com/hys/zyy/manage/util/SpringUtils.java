package com.hys.zyy.manage.util;

import java.util.Collection;
import java.util.Collections;
import java.util.Map;

import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;

public class SpringUtils {
	
	public static <T> Collection<T> getBeansOfType(Class<T> clz) {
		WebApplicationContext wac = ContextLoader.getCurrentWebApplicationContext();
		if(wac == null)
			return Collections.EMPTY_LIST;
		Map result = wac.getBeansOfType(clz);
		if(result == null)
			return Collections.EMPTY_LIST;
		return result.values();
	}
	
	public static <T> T getSingleBeanOfType(Class<T> clz) {
		Collection<T> list = SpringUtils.getBeansOfType(clz);
		if(list == null || list.isEmpty())
			return null;
		return list.iterator().next();
	}
	
	//根据name
    public static Object getBean(String name) {
    	WebApplicationContext wac = ContextLoader.getCurrentWebApplicationContext();
        return wac.getBean(name);
    }
	
}
