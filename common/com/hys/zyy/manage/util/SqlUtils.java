package com.hys.zyy.manage.util;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.hys.zyy.manage.constants.SymbleConstants;


/**
 * 与SQL语句有关的帮助类
 * <AUTHOR>
 *
 */
public class SqlUtils {
	
	private static Pattern PATTERN_WHERE_PARAM = Pattern.compile("\\w+\\s*(=|>|<|!=|like)\\s*\\$\\{\\w+\\}");
	
	public static final String COMMA = StringPool.COMMA;
	
	public static final String AND = StringPool.SPACE + "and" + StringPool.SPACE;
	
	public static final String OR = StringPool.SPACE + "or" + StringPool.SPACE;
	/**
	 * 组装SQL IN语句,如果str为空返回""
	 * 	SqlUtil.in("name","'sdfsdf','sdfsdf','sdfsdf'")  ====> " name in('sdfsdf','sdfsdf','sdfsdf')"
	 * @param propertyName					// 属�?�名�?
	 * @param str							// 属�?��?�以,分割
	 * @return
	 */
	public static String andIn(String propertyName, String str){
		if(StringUtils.isBlank(propertyName) || StringUtils.isBlank(str))
			return StringUtils.EMPTY;
		return AND + in(propertyName, str);
	}
	
	public static String andIn(String propertyName, String[] str){
		if(StringUtils.isBlank(propertyName) || str == null)
			return StringUtils.EMPTY;
		return andIn(propertyName, StringUtils.join(str, null, null));
	}
	
	public static String andIn(String propertyName, Integer[] array){
		if(StringUtils.isBlank(propertyName) || array == null)
			return StringUtils.EMPTY;
		return andIn(propertyName, StringUtils.join(array, StringPool.COMMA, StringPool.BLANK));
	}
	
	public static String andIn(String propertyName, Collection list, boolean wrap) {
		if(StringUtils.isBlank(propertyName) || list == null)
			return StringUtils.EMPTY;
		if(wrap)
			return andIn(propertyName, StringUtils.join(list, null, null));
		else
			return andIn(propertyName, StringUtils.join(list, StringPool.COMMA, null));
	}
	
	public static String andIn(String propertyName, Collection list) {
		return andIn(propertyName, list, false);
	}
	
	/**
	 * 组装SQL语句,如果str为空返回"" 
	 * SqlUtil.addIs("model.id", {"123","456"}) ========> " and model.id = '123' and model.id = '456'"
	 * @param propertyName					// 属�?�名�?
	 * @param str							// 属�?��?�以,分割
	 * @return
	 */
	public static String andIs(String propertyName, String str){
		if(StringUtils.isBlank(propertyName) || StringUtils.isBlank(str))
			return StringUtils.EMPTY;
		return AND + is(propertyName, str);
	}
	
	public static String andIsAnd(String propertyName, String[] str){
		if(StringUtils.isBlank(propertyName) || str == null)
			return StringUtils.EMPTY;
		return AND + isAnd(propertyName, str);
	}
	
	public static String andIsAnd(String propertyName, Collection<String> list){
		if(StringUtils.isBlank(propertyName) || CollectionUtils.isEmpty(list))
			return StringUtils.EMPTY;
		return AND + isAnd(propertyName, list.toArray(new String[0]));
	}
	
	public static String andIsOr(String propertyName, Object[] str){
		if(StringUtils.isBlank(propertyName) || str == null)
			return StringUtils.EMPTY;
		return AND + isOr(propertyName, str);
	}
	
	public static String andIsOr(String propertyName, Collection<? extends Object> list){
		if(StringUtils.isBlank(propertyName) || CollectionUtils.isEmpty(list))
			return StringUtils.EMPTY;
		return AND + isOr(propertyName, list.toArray());
	}
	
	public static String andLikeOr(String propertyName, String[] str){
		if(StringUtils.isBlank(propertyName) || str == null)
			return StringUtils.EMPTY;
		return AND + likeOr(propertyName, str, StringPool.PERCENT, StringPool.PERCENT);
	}
	
	public static String andLikeOr(String propertyName, Collection<String> list) {
		if(StringUtils.isBlank(propertyName) || CollectionUtils.isEmpty(list))
			return StringUtils.EMPTY;
		return AND + likeOr(propertyName, list.toArray(new String[0]), StringPool.PERCENT, StringPool.PERCENT);
	}
	
	public static String andLikeAnd(String propertyName, Collection<String> list) {
		if(StringUtils.isBlank(propertyName) || CollectionUtils.isEmpty(list))
			return StringUtils.EMPTY;
		return AND + likeAnd(propertyName, list.toArray(new String[0]), StringPool.PERCENT, StringPool.PERCENT);
	}
	
	public static String andLikeAnd(String propertyName, String[] list) {
		if(StringUtils.isBlank(propertyName) || ArrayUtils.isEmpty(list))
			return StringUtils.EMPTY;
		return AND + likeAnd(propertyName, list, StringPool.PERCENT, StringPool.PERCENT);
	}
	
	public static String andLikeOr(String propertyName, Collection<String> list, String prefix, String suffix){
		if(StringUtils.isBlank(propertyName) || CollectionUtils.isEmpty(list))
			return StringUtils.EMPTY;
		return AND + likeOr(propertyName, list.toArray(new String[0]), prefix, suffix);
	}
	
	public static String in(String propertyName, String str){
		if(StringUtils.isBlank(propertyName) || StringUtils.isBlank(str))
			return StringUtils.EMPTY;
		return propertyName + " in(" + str + ") ";
	}
	
	public static String in(String propertyName, String[] str){
		if(StringUtils.isBlank(propertyName) || str == null)
			return StringUtils.EMPTY;
		return in(propertyName, StringUtils.join(str, null, null));
	}
	
	public static String in(String propertyName, Collection<String> list){
		if(StringUtils.isBlank(propertyName) || list == null)
			return StringUtils.EMPTY;
		return in(propertyName, StringUtils.join(list, null, null));
	}

	public static String is(String propertyName, Object str) {
		String value = str.toString();
		if(StringUtils.isBlank(propertyName) || StringUtils.isBlank(value))
			return StringUtils.EMPTY;
		if(String.class.isAssignableFrom(str.getClass()))
			return propertyName + StringPool.SPACE + StringPool.EQUAL + StringPool.SPACE + StringPool.APOSTROPHE + str + StringPool.APOSTROPHE;
		else
			return propertyName + StringPool.SPACE + StringPool.EQUAL + StringPool.SPACE + str;
	}
	
	public static String like(String propertyName, String str, String prefix, String suffix) {
		if(StringUtils.isBlank(propertyName) || StringUtils.isBlank(str))
			return StringUtils.EMPTY;
		if(StringUtils.isBlank(prefix))
			prefix = StringUtils.EMPTY;
		if(StringUtils.isBlank(suffix))
			suffix = StringUtils.EMPTY;
		
		return propertyName + StringPool.SPACE + StringPool.LIKE + StringPool.SPACE + StringPool.APOSTROPHE + prefix + str + suffix + StringPool.APOSTROPHE;
	}
	
	public static String isOr(String propertyName, Object[] str){
		if(StringUtils.isBlank(propertyName) || str == null)
			return StringUtils.EMPTY;
		List<String> values = new ArrayList<String>();
		for(Object s : str)	
		{
			if(s != null)
				values.add(is(propertyName, s));
		}
		return StringPool.OPEN_PARENTHESIS + StringUtils.join(values, OR, StringPool.BLANK) + StringPool.CLOSE_PARENTHESIS;
	}
	
	public static String likeOr(String propertyName, String[] str, String prefix, String suffix){
		if(StringUtils.isBlank(propertyName) || str == null)
			return StringUtils.EMPTY;
		List<String> values = new ArrayList<String>();
		for(String s : str)	
		{
			if(StringUtils.isNotBlank(s))
				values.add(like(propertyName, s, prefix, suffix));
		}
		return StringPool.OPEN_PARENTHESIS + StringUtils.join(values, OR, StringPool.BLANK) + StringPool.CLOSE_PARENTHESIS;
	}
	
	public static String likeAnd(String propertyName, String[] str, String prefix, String suffix){
		if(StringUtils.isBlank(propertyName) || str == null)
			return StringUtils.EMPTY;
		List<String> values = new ArrayList<String>();
		for(String s : str)	
		{
			if(StringUtils.isNotBlank(s))
				values.add(like(propertyName, s, prefix, suffix));
		}
		return StringPool.OPEN_PARENTHESIS + StringUtils.join(values, AND, StringPool.BLANK) + StringPool.CLOSE_PARENTHESIS;
	}
	
	public static String isAnd(String propertyName, String[] str){
		if(StringUtils.isBlank(propertyName) || str == null)
			return StringUtils.EMPTY;
		List<String> values = new ArrayList<String>();
		for(String s : str)	
		{
			if(StringUtils.isNotBlank(s))
				values.add(is(propertyName, s));
		}
		return StringPool.OPEN_PARENTHESIS + StringUtils.join(values, AND, StringPool.BLANK) + StringPool.CLOSE_PARENTHESIS;
	}
	
	/**
	 * 去除sql条件中无法赋值的参数
	 * @param sql
	 * @return
	 */
	public static String stripUnResolverMacro(String sql) {
		if(sql == null)
			return "";
		Matcher m = PATTERN_WHERE_PARAM.matcher(sql);
		sql = m.replaceAll(" 1=1 ");
		return sql;
	}
	
	/**
	 * <b>function:</b> 处理oracle sql 语句in子句中（where id in (1, 2, ..., 1000, 1001)），
	 * 如果子句中超过1000项就会报错。
	 * 这主要是oracle考虑性能问题做的限制。
	 * 如果要解决次问题，可以用 where id (1, 2, ..., 1000) or id (1001, ...)
	 * <AUTHOR>
	 * @createDate 2012-8-31 下午02:36:03
	 * @param ids in语句中的集合对象
	 * @param count in语句中出现的条件个数
	 * @param field in语句对应的数据库查询字段
	 * @return 返回 field in (...) or field in (...) 字符串
	 */
	public static String getOracleSQLIn(List<Long> ids, int count, String field) {
	    count = Math.min(count, 1000);
	    int len = ids.size();
	    int size = len % count;
	    if (size == 0) {
	        size = len / count;
	    } else {
	        size = (len / count) + 1;
	    }
	    StringBuilder builder = new StringBuilder();
	    for (int i = 0; i < size; i++) {
	        int fromIndex = i * count;
	        int toIndex = Math.min(fromIndex + count, len);
	        //System.out.println(ids.subList(fromIndex, toIndex));
	        String productId =  StringUtils.join(ids.subList(fromIndex, toIndex), null, null);
	        if (i != 0) {
	            builder.append(" or ");
	        }
	        builder.append(field).append(" in (").append(productId).append(")");
	    }
	    
	    return StringUtils.defaultIfEmpty(builder.toString(), field + " in ('')");
	}
	
	/**
	 * @desc 功能同getOracleSQLIn， 区别是需要传入运算符（IN 或 NOT IN）
	 * <AUTHOR>
	 * @date 2018-9-14  上午11:26:18
	 */
	public static String getOracleSQLIn(List<Long> ids, int count, String field, String operator) {
		count = Math.min(count, 1000);
		int len = ids.size();
		int size = len % count;
		if (size == 0) {
			size = len / count;
		} else {
			size = (len / count) + 1;
		}
		StringBuilder builder = new StringBuilder();
		for (int i = 0; i < size; i++) {
			int fromIndex = i * count;
			int toIndex = Math.min(fromIndex + count, len);
			String productId = StringUtils.join(ids.subList(fromIndex, toIndex), ",", "");
			if (i != 0) {
				builder.append(" or ");
			}
			builder.append(field).append(" " + operator + " (").append(productId).append(")");
		}
		return StringUtils.defaultIfEmpty(builder.toString(), field + " " + operator + " ('')");
	}
	
	/**
	 * 字符串类型的
	* <p>Description: </p>
	* @param ids
	* @param count
	* @param field
	* @return
	* <AUTHOR> 
	* @date 2018-6-1下午6:04:43
	 */
	public static String getOracleSQLInStr(List<String> ids, int count, String field) {
	    count = Math.min(count, 1000);
	    int len = ids.size();
	    int size = len % count;
	    if (size == 0) {
	        size = len / count;
	    } else {
	        size = (len / count) + 1;
	    }
	    StringBuilder builder = new StringBuilder();
	    for (int i = 0; i < size; i++) {
	        int fromIndex = i * count;
	        int toIndex = Math.min(fromIndex + count, len);
	        String productId =  StringUtils.join(ids.subList(fromIndex, toIndex), null, null);
	        if (i != 0) {
	            builder.append(" or ");
	        }
	        builder.append(field).append(" in (").append(productId).append(")");
	    }
	    
	    return StringUtils.defaultIfEmpty(builder.toString(), field + " in ('')");
	}
	
	public static String getOracleSQLInStr2(List<Long> ids, int count, String field) {
	    count = Math.min(count, 1000);
	    int len = ids.size();
	    int size = len % count;
	    if (size == 0) {
	        size = len / count;
	    } else {
	        size = (len / count) + 1;
	    }
	    StringBuilder builder = new StringBuilder();
	    for (int i = 0; i < size; i++) {
	        int fromIndex = i * count;
	        int toIndex = Math.min(fromIndex + count, len);
	        String productId =  StringUtils.join(ids.subList(fromIndex, toIndex), null, null);
	        if (i != 0) {
	            builder.append(" or ");
	        }
	        builder.append(field).append(" in (").append(productId).append(")");
	    }
	    
	    return StringUtils.defaultIfEmpty(builder.toString(), field + " in ('')");
	}
	
	public static String createInSql(List<Long> list){
		if (CollectionUtils.isEmpty(list)){
			return null;
		}
		StringBuffer buffer = new StringBuffer();
		int size = list.size();
		for (int i = 0; i < size; i++) {
			Long id = list.get(i);
			if (id != null){
				buffer.append(id);
				buffer.append(SymbleConstants.COMMA);
			}
		}
		int len = buffer.length();
		if (len > 0){
			return buffer.substring(0, len-1);
		}
		return null;
	}
	
}