package com.hys.zyy.manage.util;

public class StringPool {

	public static final String AMPERSAND = "&";

	public static final String APOSTROPHE = "'";

	public static final String AT = "@";

	public static final String BACK_SLASH = "\\";

	public static final String BLANK = "";

	public static final String CDATA_OPEN = "<![CDATA[";

	public static final String CDATA_CLOSE = "]]>";

	public static final String CLOSE_BRACKET = "]";

	public static final String CLOSE_CURLY_BRACE = "}";

	public static final String CLOSE_PARENTHESIS = ")";
	
	public static final String CLOSE_PARENTHESIS_ZH = "）";

	public static final String COLON = ":";

	public static final String COMMA = ",";

	public static final String DASH = "-";

	public static final String DOUBLE_SLASH = "//";

	public static final String EQUAL = "=";

	public static final String GREATER_THAN = ">";

	public static final String FORWARD_SLASH = "/";

	public static final String LESS_THAN = "<";

	public static final String LIKE = "LIKE";

	public static final String MINUS = "-";

	public static final String NBSP = "&nbsp;";

	public static final String NEW_LINE = "\n";

	public static final String NOT_EQUAL = "!=";

	public static final String NULL = "null";

	public static final String OPEN_BRACKET = "[";

	public static final String OPEN_CURLY_BRACE = "{";

	public static final String OPEN_PARENTHESIS = "(";
	
	public static final String OPEN_PARENTHESIS_ZH = "（";

	public static final String PERCENT = "%";

	public static final String PERIOD = ".";

	public static final String PIPE = "|";

	public static final String PLUS = "+";

	public static final String POUND = "#";

	public static final String QUESTION = "?";

	public static final String QUOTE = "\"";

	public static final String RETURN = "\r";

	public static final String RETURN_NEW_LINE = "\r\n";

	public static final String SEMICOLON = ";";

	public static final String SLASH = FORWARD_SLASH;

	public static final String SPACE = " ";

	public static final String STAR = "*";

	public static final String TILDE = "~";

	public static final String UNDERLINE = "_";

	public static final String UTF8 = "UTF-8";
	
	public static final String ZERO = "0";
	
	public static final String TO = "至";
	
	public static final String UPPER = "(上)";
	
	public static final String LOWER = "(下)";
	
	public static final String TXT = ".txt";

	public static final String ZIP = ".zip";
	
	public static final String PDF = ".pdf";
	
	public static final String T = "T", Z = "Z";
	
}