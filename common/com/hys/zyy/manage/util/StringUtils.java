package com.hys.zyy.manage.util;

import java.io.UnsupportedEncodingException;
import java.util.Collection;

import org.apache.commons.lang.BooleanUtils;

/**
 * 
 * 标题：住院医师
 * 
 * 时间: 2011-7-15
 * 
 * 描述：格式化字符
 * 
 * 说明:
 */
public class StringUtils extends org.apache.commons.lang.StringUtils {
	
	/** 7位ASCII字符，也叫作ISO646-US、Unicode字符集的基本拉丁块 */
	public static final String US_ASCII = "US-ASCII";
	
    /** ISO 拉丁字母表 No.1，也叫作 ISO-LATIN-1 */
    public static final String ISO_8859_1 = "ISO-8859-1";
    
    /** 8 位 UCS 转换格式 */
    public static final String UTF_8 = "UTF-8";
    
    /** 16 位 UCS 转换格式，Big Endian（最低地址存放高位字节）字节顺序 */
    public static final String UTF_16BE = "UTF-16BE";
    
    /** 16 位 UCS 转换格式，Little-endian（最高地址存放低位字节）字节顺序 */
    public static final String UTF_16LE = "UTF-16LE";
    
    /** 16 位 UCS 转换格式，字节顺序由可选的字节顺序标记来标识 */
    public static final String UTF_16 = "UTF-16";
    
    /** 中文超大字符集 */
    public static final String GBK = "GBK";


	public static long getLongParam(String param, long defaultNum) {
		if (param != null && !param.equals("")) {
			try {
				return Long.parseLong(param);
			} catch (Exception e) {
				return defaultNum;
			}
		} else {
			return defaultNum;
		}
	}
	
	public static boolean getBooleanParam(String param, boolean defaultValue) {
		if(isBlank(param))
			return defaultValue;
		if(StringUtils.equals("1", param))
			return true;
		if(StringUtils.equals("0", param))
			return false;
		
		Boolean result = BooleanUtils.toBooleanObject(param);
		if(result == null)
			return defaultValue;
		return result.booleanValue();
	}

	public static int getIntegerParam(String param, int defaultNum) {
		if (param != null && !param.equals("")) {
			try {
				return Integer.parseInt(param);
			} catch (Exception e) {
				return defaultNum;
			}
		} else {
			return defaultNum;
		}
	}

	public static boolean isEmpty(String string) {
		if (string == null || "".equals(string.trim())) {
			return true;
		}
		return false;
	}
	
	public static boolean isNotEmpty(String string) {
		return !isEmpty(string);
	}
	
	/**
	 * 数组拼串
	 * 如：
	 * StringUtils.join(new String['a','b','c'], '+', '&')     ======>         &a&+&b&+&c&
	 * @param source
	 * @param delim
	 * @param wrapper
	 * @return
	 */
	public static String join(Object[] source, String delim, String wrapper)
	{
		return join(source, delim, wrapper, wrapper);
	}
	
	public static String join(Object[] source, String delim, String prefix, String suffix) {
		
		if(source == null || source.length == 0)
			return EMPTY;
		
		if(delim == null)
			delim = StringPool.COMMA;
		if(prefix == null)
			prefix = StringPool.APOSTROPHE;
		if(suffix == null)
			suffix = StringPool.APOSTROPHE;
		
		StringBuilder sb = new StringBuilder();
		
		for(Object str : source)
		{
			if(str != null)
				sb.append(prefix).append(str.toString()).append(suffix).append(delim);
		}
		
		return chomp(sb.toString(), delim);
	}
	
	public static String join(Collection source, String delim, String wrapper)
	{
		if(source == null || source.isEmpty())
			return EMPTY;
		return join(source.toArray(), delim, wrapper);
	}
	
	public static String decodeTo(String str, String oldCharset, String newCharset) {
		try {
		    if (str != null) {
		    	// 用旧的字符编码解码字符串。解码可能会出现异常。
		    	byte[] bs = str.getBytes(oldCharset);
		    	// 用新的字符编码生成字符串
		    	return new String(bs, newCharset);
		    }
		}
		catch(Exception e) {
		} 
		return StringPool.BLANK;
	}
	
	public static boolean startsWithIgnoreCase(String str, String prefix) {
		if (str == null || prefix == null) {
			return false;
		}
		if (str.startsWith(prefix)) {
			return true;
		}
		if (str.length() < prefix.length()) {
			return false;
		}
		String lcStr = str.substring(0, prefix.length()).toLowerCase();
		String lcPrefix = prefix.toLowerCase();
		return lcStr.equals(lcPrefix);
	}

}