package com.hys.zyy.manage.util;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * easyui使用的tree模型
 * <AUTHOR>
 */
public class TreeNode implements java.io.Serializable {
	private String id;
	private String parentId;
	private String isLeaf;
	private String text;// 树节点名称
	private String iconCls;// 前面的小图标样式
	private Boolean checked = false;// 是否勾选状态
	private Map<String, Object> attributes;// 其他参数
	private List<TreeNode> children = new ArrayList();// 子节点
	private String state = "open";// 是否展开(open,closed)
	private int totalRows = 0;//每个节点的叶子几点个数
	private String attribute;//定量指标值或定性指标选项
	private String score;//指标得分
	private String weightScore; //
	private Integer level;
	
	public TreeNode() {
		super();
	}

	public TreeNode(String id, String parentId, String text) {
		super();
		this.id = id;
		this.parentId = parentId;
		this.text = text;
	}

	public int getTotalRows() {
		totalRows  = 0;
		if(children!=null&&children.size()!=0){//如果有孩子节点
			for(TreeNode treeNode:children){
				totalRows+=treeNode.getTotalRows();
			}
		}else{//如果没有孩子节点
			totalRows = 1;
			return totalRows;
		}
		return totalRows;
	}

	public void setTotalRows(int totalRows) {
		this.totalRows = totalRows;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getText() {
		return text;
	}

	public void setText(String text) {
		this.text = text;
	}

	public Boolean getChecked() {
		return checked;
	}

	public void setChecked(Boolean checked) {
		this.checked = checked;
	}

	public Map<String, Object> getAttributes() {
		return attributes;
	}

	public void setAttributes(Map<String, Object> attributes) {
		this.attributes = attributes;
	}

	public List<TreeNode> getChildren() {
		return children;
	}

	public void setChildren(List<TreeNode> children) {
		this.children = children;
	}

	public String getState() {
		return state;
	}

	public void setState(String state) {
		this.state = state;
	}

	public String getIconCls() {
		return iconCls;
	}

	public void setIconCls(String iconCls) {
		this.iconCls = iconCls;
	}

	public String getParentId() {
		return parentId;
	}

	public void setParentId(String parentId) {
		this.parentId = parentId;
	}

	public String getIsLeaf() {
		return isLeaf;
	}

	public void setIsLeaf(String isLeaf) {
		this.isLeaf = isLeaf;
	}
	
	public String getScore() {
		return score;
	}

	public void setScore(String score) {
		this.score = score;
	}
	public String getWeightScore() {
		return weightScore;
	}
	public void setWeightScore(String weightScore) {
		this.weightScore = weightScore;
	}
	
	public Integer getLevel() {
		return level;
	}

	public void setLevel(Integer level) {
		this.level = level;
	}

	public static void main(String[] args) {
		TreeNode treenode = new TreeNode();
		TreeNode treenode1 = new TreeNode();
		TreeNode treenode2 = new TreeNode();
		TreeNode treenode3 = new TreeNode();
		TreeNode treenode4 = new TreeNode();
		TreeNode treenode5 = new TreeNode();
		List<TreeNode> list1 = new ArrayList();
		treenode.setChildren(list1);
		list1.add(treenode1);
		list1.add(treenode2);
		
		List<TreeNode> list2 = new ArrayList();
		list2.add(treenode3);
		list2.add(treenode4);
		treenode2.setChildren(list2);
		
		list1.add(treenode5);
		
		
		
	}
	public String getAttribute() {
		return attribute;
	}
	public void setAttribute(String attribute) {
		this.attribute = attribute;
	}
	
    //节点横向排序
    public void sortChildren(Comparator<TreeNode> comparator) {
    	if (children.size() != 0) {
    		// 对本层节点进行排序（可根据不同的排序属性，传入不同的比较器）
    		Collections.sort(children, comparator);
    		// 对每个节点的下一层节点进行排序
    		for (Iterator<TreeNode> it = children.iterator(); it.hasNext();) {
    			it.next().sortChildren(comparator);
    		}
    	}
    }


    
	@Override
	public String toString() {
		return "TreeNode [id=" + id + ", parentId=" + parentId + ", isLeaf="
				+ isLeaf + ", text=" + text + ", iconCls=" + iconCls
				+ ", checked=" + checked + ", attributes=" + attributes
				+ ", children=" + children + ", state=" + state + "]";
	}
}
