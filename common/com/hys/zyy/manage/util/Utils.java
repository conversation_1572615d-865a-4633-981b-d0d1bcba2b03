package com.hys.zyy.manage.util;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.ResourceBundle;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.jsp.JspException;

import jxl.Workbook;
import jxl.write.Label;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;

import com.hys.zyy.manage.constants.Constants;
import com.hys.zyy.manage.exception.ErrorCode;
import com.hys.zyy.manage.exception.FrameworkRuntimeException;
import com.hys.zyy.manage.model.ZyyCycleTableResiCycleVO;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：张伟清 2011-1-5
 * 
 * 描述：常用工具类
 * 
 * 说明:
 */
public class Utils {

	private static void render(final HttpServletResponse response,
			final String contentType, final String content) {
		try {
			String encoding = Constants.ENCODING_DEFAULT ;
			boolean noCache = Constants.NOCACHE_DEFAULT ;

			String fullContentType = contentType + ";charset=" + encoding;
			response.setContentType(fullContentType);
			if (noCache) {
				response.setHeader("Pragma", "No-cache");
				response.setHeader("Cache-Control", "no-cache");
				response.setDateHeader("Expires", 0);
			}

			response.getWriter().write(content);
			response.getWriter().flush();
		} catch (IOException e) {
			throw new RuntimeException("");
		}
	}

	public static void renderText(final HttpServletResponse response, final String text) {
		render(response, "text/plain", text);
	}

	public static void renderXml(final HttpServletResponse response, final String text) {
		render(response, "text/xml", text);
	}
	
	/**
	 * 导出成行的Excel表格
	 * 
	 * @param response
	 * @param tableList
	 * @param fileName
	 * @throws Exception
	 */
	public static void downLoadExcel(final HttpServletResponse response, List<List<String>> tableList, String fileName) throws Exception {
		response.setHeader("Content-disposition", "attachment;inline;filename=" + new String(fileName.getBytes("gbk"), "iso-8859-1") + ".xls");
		response.setContentType("APPLICATION ND.MS-EXCEL;charset=GBK");
		ServletOutputStream outstream = response.getOutputStream();

		WritableWorkbook workbook = Workbook.createWorkbook(outstream);
		WritableSheet sheet = workbook.createSheet(fileName, 0);
		
		for (int i = 0; i < tableList.size(); i++) {
			List<String> row = tableList.get(i);
			
			for (int j = 0; j < row.size(); j++) {
				Label text = new Label(j, i, row.get(j));
				sheet.addCell(text);
			}
		}
		
		workbook.write();
		workbook.close();
	}

	// 判断List结果集是否为空!
	public static <T> boolean isListEmpty(List<T> list) {
		if (null == list || list.isEmpty()) {
			return true;
		} else {
			return false;
		}
	}

	// 判断Map结果集是否为空!
	public static <K, V> boolean isMapEmpty(Map<K, V> map) {
		if (null == map || map.isEmpty()) {
			return true;
		} else {
			return false;
		}
	}

	//判断当前值是否为空
	public static String isStringEmpty(String tempVal, String defaultVal) {
		if (null == tempVal || "".equals(tempVal)) {
			return defaultVal ;
		} else {
			return tempVal ;
		}
	}
	
	/**
	 * 将list重新复制
	 */
	@SuppressWarnings("unchecked")
	public static <T> List<T> copyArrayList(List<T> list) {
		ByteArrayOutputStream byteOut = null;
		ObjectOutputStream out = null;
		ByteArrayInputStream byteIn = null;
		ObjectInputStream in = null;

		try {
			byteOut = new ByteArrayOutputStream();
			out = new ObjectOutputStream(byteOut);
			out.writeObject(list);

			byteIn = new ByteArrayInputStream(byteOut.toByteArray());
			in = new ObjectInputStream(byteIn);
			List<T> newList = (List<T>) in.readObject();

			return newList;
		} catch (Exception e) {
			throw new FrameworkRuntimeException(ErrorCode.E0001, e);
		} finally {
			try {
				if (byteOut != null) {
					byteOut.close();
				}
			} catch (IOException e) {
			}
			try {
				if (out != null) {
					out.close();
				}
			} catch (IOException e) {
			}
			try {
				if (byteIn != null) {
					byteIn.close();
				}
			} catch (IOException e) {
			}
			try {
				if (in != null) {
					in.close();
				}
			} catch (IOException e) {
			}
		}
    }
	
	/**
	 * 得到本月的第一天
	 */
	public static Date getMonthFirstDay(Date date) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMinimum(Calendar.DAY_OF_MONTH));

		return calendar.getTime();
	}

	/**
	 * 得到本月的最后一天
	 */
	public static Date getMonthLastDay(Date date) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
		return calendar.getTime();
	}
	
	/**
	 * 得到随机
	 * @param charCount
	 * @return
	 */
	public static String getRandNum(int charCount) {
		String charValue = "";
	    for (int i = 0; i < charCount; i++){
		    char c = (char) (randomInt(0,10)+'0');
		    charValue += String.valueOf(c);
	    }
		return charValue;
	}
	
	 public static int randomInt(int from, int to){
		  Random r = new Random();
		  return from + r.nextInt(to - from);
	}

	// 读取配置文件信息
	final static private ResourceBundle resource = ResourceBundle.getBundle("resources_zh_CN");

	//各个组织机构角色列表
	final static private Map<Long, List<Long>> roleMap = new HashMap<Long, List<Long>>() ;
	static {
		String resiRoleAll = resource.getString("residency_role");
		String[] resiRoles = resiRoleAll.split("\\|") ;
		List<Long> roleList = null ;
		for (String resiRole : resiRoles) {
			roleList = new ArrayList<Long>() ;
			String[] roles = resiRole.split(",") ;
			Long bm  = NumberUtil.parseLong(roles[0], 0) ;//报名学员用户
			Long zs  = NumberUtil.parseLong(roles[1], 0) ;//正式学员用户
			Long org = NumberUtil.parseLong(roles[2], 0) ;//省厅组织机构
			
			roleList.add(bm) ;
			roleList.add(zs) ;
			roleMap.put(org, roleList) ;
		}
	}

	/**
	 * 根据组织机构ID 取得角色列表
	 * @param orgId	省厅组织机构
	 */
	public static List<Long> getRoleType(Long orgId){
		return roleMap.get(orgId) ;
	}
	
	//判断当前字符串是否存在小数
	public static boolean checkNumeral(String digital) {
		return digital.matches("^[+-]?\\d+\\.\\d+$");
	}
	
	
	 private final static byte[] val = { 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F,
	      0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F,
	      0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x00, 0x01, 0x02,
	      0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E,
	      0x0F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F,
	      0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F,
	      0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F,
	      0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F,
	      0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F,
	      0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F,
	      0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F,
	      0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F,
	      0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F,
	      0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F };
	  /**
	   * JavaScript unescape
	   * 
	   * @param s
	   * @return
	   */
	  public static String unescape(String s) {
	    StringBuffer sbuf = new StringBuffer();
	    int i = 0;
	    int len = s.length();
	    while (i < len) {
	      int ch = s.charAt(i);
	      if (ch == '+') { // + : map to ' '
	        sbuf.append(' ');
	      } else if ('A' <= ch && ch <= 'Z') { // 'A'..'Z' : as it was
	        sbuf.append((char) ch);
	      } else if ('a' <= ch && ch <= 'z') { // 'a'..'z' : as it was
	        sbuf.append((char) ch);
	      } else if ('0' <= ch && ch <= '9') { // '0'..'9' : as it was
	        sbuf.append((char) ch);
	      } else if (ch == '-' || ch == '_' // unreserved : as it was
	          || ch == '.' || ch == '!' || ch == '~' || ch == '*' || ch == '\'' || ch == '(' || ch == ')') {
	        sbuf.append((char) ch);
	      } else if (ch == '%') {
	        int cint = 0;
	        if ('u' != s.charAt(i + 1)) { // %XX : map to ascii(XX)
	          cint = (cint << 4) | val[s.charAt(i + 1)];
	          cint = (cint << 4) | val[s.charAt(i + 2)];
	          i += 2;
	        } else { // %uXXXX : map to unicode(XXXX)
	          cint = (cint << 4) | val[s.charAt(i + 2)];
	          cint = (cint << 4) | val[s.charAt(i + 3)];
	          cint = (cint << 4) | val[s.charAt(i + 4)];
	          cint = (cint << 4) | val[s.charAt(i + 5)];
	          i += 5;
	        }
	        sbuf.append((char) cint);
	      }
	      i++;
	    }
	    return sbuf.toString();
	  }
	  
	  
	//判断两个日期是否连续
	public static Boolean dateIsLianXu(Date date1Min,Date date2Max){
		return DateUtil.getStartAndEndDays(date1Min, date2Max)==1 ? true : false;
	}
	public static int checkHalfMonth(Date startTime){
		//取得当前时间的天
		int day = LogicUtils.getDayInCurrentTime(startTime) ;
		//1.表示是上半月 1->15号, >1或(16)表示下半月 也就是16-月末
		if(day == 1 || day == 15){
			return 0;
		}else{
			return 1;
		}
	}
	
	//轮转第五步,按照学员科室,月,导出
	public static String cycleStuDeptMonth4export(List<ZyyCycleTableResiCycleVO> deptList) throws JspException { 
		if(null == deptList || deptList.size()==0){
			return "";
		}
		String str = "";
		Date startDate = deptList.get(0).getStartDate();
		Date endDate = deptList.get(deptList.size()-1).getEndDate();
		//判断 一个人在科室里的时间是否是断开的，如果是断开的 那就写两次
		for(int i = 0;i < deptList.size(); i++){
			ZyyCycleTableResiCycleVO vo = deptList.get(i);
			//判断 日期是否连续
			if(i + 1 != deptList.size() && !dateIsLianXu(vo.getEndDate(),deptList.get(i+1).getStartDate())){
				str += writeToPage(startDate, vo.getEndDate()) + "  ";
				startDate = deptList.get(i+1).getStartDate();
			}
		}
		str += writeToPage(startDate, endDate) + "  ";
		return str;
	}
	@SuppressWarnings("deprecation")
	private static String writeToPage(Date startDate,Date endDate){
		int star = checkHalfMonth(startDate);
		int end = checkHalfMonth(endDate);
		String startTime = DateUtil.format(startDate, "yyyy-MM") ;
		String startTimeEnd = DateUtil.format(startDate, "yy/MM/dd") ;
		if(star==0) startTime += "月"; else startTime += "月(下)";
		String endTime = DateUtil.format(endDate, "yyyy-MM") ;
		String endTimeEnd = DateUtil.format(endDate, "yy/MM/dd") ;
		if(end==0) endTime += "月(上)"; else endTime += "月";
		
		if(startDate.getMonth() == endDate.getMonth() && startDate.getYear() ==  endDate.getYear()){
			int startDay = LogicUtils.getDayInCurrentTime(startDate);
			int endDay = LogicUtils.getDayInCurrentTime(endDate);
			if(endDay == 15 && startTime.indexOf("(上)") == -1){
				startTime += "(上)";
			}else if(startDay == 16 && startTime.indexOf("(下)") == -1){
				startTime += "(下)";
			}
			String str = (startTime +"(" + startTimeEnd +"至 " + endTimeEnd+")");
			if(NewRecruitUtil.unCheckCycleStartDate())
				str = str.replace("(上)", StringPool.BLANK).replace("(下)", StringPool.BLANK);
			return str;
		}else{
			String str = (startTime+" 至 "+endTime +"(" + startTimeEnd +";至 " + endTimeEnd+")");
			if(NewRecruitUtil.unCheckCycleStartDate())
				str = str.replace("(上)", StringPool.BLANK).replace("(下)", StringPool.BLANK);
			return str;
		}
	}
	
	//轮转第五步,按照学员科室,周,导出
	public static String cycleStuDeptWeek4export(List<ZyyCycleTableResiCycleVO> deptList) throws JspException { 
		if(null == deptList || deptList.size()==0){
			return "";
		}
		String str = "";
		Date startDate = deptList.get(0).getStartDate();
		Date endDate = deptList.get(deptList.size()-1).getEndDate();
		//判断 一个人在科室里的时间是否是断开的，如果是断开的 那就写多次
		for(int i = 0;i < deptList.size(); i++){
			ZyyCycleTableResiCycleVO vo = deptList.get(i);
			//判断 日期是否连续
			if(i + 1 != deptList.size() && !dateIsLianXu(vo.getEndDate(),deptList.get(i+1).getStartDate())){
				str = writeToPageWeek(startDate, vo.getEndDate());
				startDate = deptList.get(i+1).getStartDate();
			}
		}
		str += writeToPageWeek(startDate, endDate);
		return str;
	}
	
	@SuppressWarnings("deprecation")
	private static String writeToPageWeek(Date startDate,Date endDate){
		String startTime = DateUtil.format(startDate, "yyyy-MM-dd") ;
		String startTimeEnd = DateUtil.format(startDate, "yy/MM/dd") ;
		String endTime = DateUtil.format(endDate, "yyyy-MM-dd") ;
		String endTimeEnd = DateUtil.format(endDate, "yy/MM/dd") ;
		
		Date temp = DateUtil.parse(LogicUtils.addTime(endDate, -6),"yyyy-MM-dd");
		int startYearWeek = DateUtil.calculateDateInWeekByDate(Calendar.MONDAY, Calendar.WEEK_OF_YEAR, startDate) ;
		int endYearWeek = DateUtil.calculateDateInWeekByDate(Calendar.MONDAY, Calendar.WEEK_OF_YEAR, temp) ;
		String str1 = "";
		String str2 = "";
		if(startYearWeek < 10) str1 = "0";
		if(endYearWeek < 10) str2 = "0";
			
		if(startDate.getTime() == temp.getTime()){
			return (startTime.substring(0,4) + "-" + str1 + startYearWeek + "周" 
					+ "(" + startTimeEnd +"至 " + endTimeEnd+")");
		}else{
			return (startTime.substring(0,4) + "-" + str1 + startYearWeek + "周" 
					+ " 至 "+ endTime.substring(0,4) + "-" + str2 + endYearWeek + "周"
					+ "(" + startTimeEnd +"至 " + endTimeEnd+")");
		}
	}
	public static Long[] converToLong(String[] values){
		  Long[] longs  = null;
		  if(values != null && values.length > 0){
			  longs  = new Long[values.length];
			  for(int i = 0;i<values.length;i++) {
			    String str = values[i];      
			    long lg = Long.valueOf(str);
			    longs[i] = lg ;
			  }
		  }
		  return longs;  
	}


	
}