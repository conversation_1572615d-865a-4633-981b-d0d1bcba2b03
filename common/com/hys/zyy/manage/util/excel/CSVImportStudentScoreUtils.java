package com.hys.zyy.manage.util.excel;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang.StringUtils;

import com.hys.beq.entities.vo.exam.Examinee;
import com.hys.framework.util.DateUtil;

public class CSVImportStudentScoreUtils {
	
	public static List<Examinee> readCsv(InputStream inStream)
			throws IOException {
		List<Examinee> list = new ArrayList<Examinee>();
		Examinee examinee = null;

		try {
			// BufferedReader reader=new BufferedReader(new
			// InputStreamReader(new FileInputStream(path),"GBK"));

			BufferedReader reader = new BufferedReader(new InputStreamReader(
					inStream, "GBK"));
			// 换成你的文件名

			reader.readLine();// 第一行信息，为标题信息，不用,如果需要，注释掉
			String line = null;
			int num = 0;
			while ((line = reader.readLine()) != null) {
				num++;
				String item[] = line.split(",");// CSV格式文件为逗号分隔符文件，这里根据逗号切分

				examinee = new Examinee();

				// 考试名称,考试科目，考试时间,姓名,性别，证件号 - 5,是否进入考试,是否提交试卷,考试用时,主观成绩
				// -9,客观成绩-10

				// 证件号
				String certificateNo = getValue(item, 5);
				// 主观成绩
				String subjectiveScore = getValue(item, 9);
				// 客观成绩
				String objectiveScore = getValue(item, 10);

				if (StringUtils.isNotBlank(certificateNo)){
					certificateNo = certificateNo.replaceAll("\"", "").trim();
					examinee.setCertificateNo(certificateNo.replaceAll("\t", ""));
				}
				examinee.setSubjectiveScore(subjectiveScore);
				examinee.setObjectiveScore(objectiveScore);

				list.add(examinee);

			}

		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}

	public static String getValue(String[] item, int index) {

		if (item.length > index) {
			String value = item[index];
			return value;
		}
		return "";
	}

	public static Date getCsvDate(String item) throws Exception {

		if (item.indexOf("/") > 0) {
			item = item.replaceAll("/", "-");
		} else if (item.indexOf("年") > 0) {
			item = item.replaceAll("年", "-").replaceAll("月", "-")
					.replaceAll("日", "");
		}

		Date birth = DateUtil.parse(item);
		Date defaultDate = DateUtil.parse("1900-01-01");

		if (birth.getTime() <= defaultDate.getTime()) {
			return defaultDate;
		}
		return birth;
	}
}
