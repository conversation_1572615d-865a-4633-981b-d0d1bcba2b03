package com.hys.zyy.manage.util.excel;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.net.URLEncoder;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import javax.servlet.http.HttpServletResponse;

import org.apache.commons.beanutils.BeanUtils;

import com.hys.zyy.manage.util.CollectionUtils;
 
/**
 * 文件操作 ,导出excel大数据量的时候，改为生成cvs，提高导出速度
 */
public class CSVUtils {
	
	private static String ENCODE_TYPE = "GBK"; //UTF-8
 
  /**
   * 生成为CVS文件
   * @param exportData
   *       源数据List
   * @param map
   *       csv文件的列表头map
   * @param outPutPath
   *       文件路径
   * @param fileName
   *       文件名称
   * @return
   */
  @SuppressWarnings("rawtypes")
  public static File createCSVFile(List exportData, LinkedHashMap map, String outPutPath,
                   String fileName) {
    File csvFile = null;
    BufferedWriter csvFileOutputStream = null;
    try {
      File file = new File(outPutPath);
      if (!file.exists()) {
        file.mkdir();
      }
      //定义文件名格式并创建
      csvFile = File.createTempFile(fileName, ".csv", new File(outPutPath));
      System.out.println("csvFile：" + csvFile);
      // UTF-8使正确读取分隔符","
      csvFileOutputStream = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(csvFile), ENCODE_TYPE), 1024);
      System.out.println("csvFileOutputStream：" + csvFileOutputStream);
      // 写入文件头部
      for (Iterator propertyIterator = map.entrySet().iterator(); propertyIterator.hasNext();) {
        java.util.Map.Entry propertyEntry = (java.util.Map.Entry) propertyIterator.next();
        csvFileOutputStream.write("\"" + (String) propertyEntry.getValue() != null ? (String) propertyEntry.getValue() : "" + "\"");
        if (propertyIterator.hasNext()) {
          csvFileOutputStream.write(",");
        }
      }
      csvFileOutputStream.newLine();
      // 写入文件内容
      for (Iterator iterator = exportData.iterator(); iterator.hasNext();) {
        Object row = (Object) iterator.next();
        for (Iterator propertyIterator = map.entrySet().iterator(); propertyIterator
          .hasNext();) {
          java.util.Map.Entry propertyEntry = (java.util.Map.Entry) propertyIterator
            .next();
          csvFileOutputStream.write((String) BeanUtils.getProperty(row,
            (String) propertyEntry.getKey()));
          if (propertyIterator.hasNext()) {
            csvFileOutputStream.write(",");
          }
        }
        if (iterator.hasNext()) {
          csvFileOutputStream.newLine();
        }
      }
      csvFileOutputStream.flush();
    } catch (Exception e) {
      e.printStackTrace();
    } finally {
      try {
        csvFileOutputStream.close();
      } catch (IOException e) {
        e.printStackTrace();
      }
    }
    return csvFile;
  }
  
  /**
   * 新的导出csv文件
   * @param exportData
   * @param headTitle
   * @param outPutPath
   * @param fileName
   * @return
   * <AUTHOR>
   * @date 2018-11-8下午2:50:16
   */
  public static File createCSVFile2(List<Map<String,String>> exportData, String headTitle, String outPutPath, String fileName) {
    File csvFile = null;
    BufferedWriter csvFileOutputStream = null;
    FileOutputStream fos = null;
    try {
      File file = new File(outPutPath);
      System.out.println("开始创建文件.........."+outPutPath);
      if (!file.exists()) {
    	System.out.println("执行创建文件的命令..........");
    	file.setWritable(true, false);
        file.mkdirs();
      }
      //定义文件名格式并创建
      csvFile = File.createTempFile(fileName, ".csv", new File(outPutPath));
      System.out.println("csvFile：" + csvFile);
      fos = new FileOutputStream(csvFile);
      // UTF-8使正确读取分隔符","
      csvFileOutputStream = new BufferedWriter(new OutputStreamWriter(fos, ENCODE_TYPE), 1024);
      System.out.println("csvFileOutputStream：" + csvFileOutputStream);
      // 写入文件头部
      csvFileOutputStream.write(headTitle);
      csvFileOutputStream.write(",");
      csvFileOutputStream.newLine();
      // 写入文件内容
      if (!CollectionUtils.isEmpty(exportData)){
    	  int size = exportData.size();
    	  StringBuilder bd = null;
    	  for (int i = 0; i < size; i++) {
    		  Map<String,String> map = exportData.get(i);
    		  for(Entry<String, String> entry:map.entrySet()){
    			  bd = new StringBuilder();
    			  bd.append("\"");
    			  bd.append(entry.getValue());
    			  bd.append("\"");
    			  csvFileOutputStream.write(bd.toString());
    			  csvFileOutputStream.write(",");
    		  }
    		  csvFileOutputStream.newLine();
    	  }
      }
      csvFileOutputStream.flush();
    } catch (Exception e) {
      e.printStackTrace();
    } finally {
      try {
        // csvFileOutputStream.close();
        if (csvFileOutputStream != null){
        	csvFileOutputStream.close();
        }
        if (fos != null){
        	fos.close();
        }
      } catch (IOException e) {
        e.printStackTrace();
      }
    }
    return csvFile;
  }
  
  /**
   * 考试平台导出csv文件
   * @param exportData
   * @param headTitle
   * @param outPutPath
   * @param fileName
   * @return
   * <AUTHOR>
   * @date 2018-11-8下午2:50:16
   */
  public static File createCSVFile3(List<Map<String,String>> exportData, String headTitle, String outPutPath, String fileName) {
    File csvFile = null;
    BufferedWriter csvFileOutputStream = null;
    FileOutputStream fos = null;
    try {
      File file = new File(outPutPath);
      System.out.println("开始创建文件.........."+outPutPath);
      if (!file.exists()) {
    	System.out.println("执行创建文件的命令..........");
    	file.setWritable(true, false);
        file.mkdirs();
      }
      //定义文件名格式并创建
      csvFile = File.createTempFile(fileName, ".csv", new File(outPutPath));
      System.out.println("csvFile：" + csvFile);
      fos = new FileOutputStream(csvFile);
      // UTF-8使正确读取分隔符","
      csvFileOutputStream = new BufferedWriter(new OutputStreamWriter(fos, ENCODE_TYPE), 1024);
      System.out.println("csvFileOutputStream：" + csvFileOutputStream);
      // 写入文件头部
      csvFileOutputStream.write(headTitle);
      csvFileOutputStream.write(",");
      csvFileOutputStream.newLine();
      // 写入文件内容
      if (!CollectionUtils.isEmpty(exportData)){
    	  int size = exportData.size();
    	  StringBuilder bd = null;
				for (int i = 0; i < size; i++) {
					Map<String, String> map = exportData.get(i);
					
					/*bd = new StringBuilder();
					bd.append(map.get("4")); // 用户名
					csvFileOutputStream.write(bd.toString() + ",");

					bd = new StringBuilder();
					bd.append(map.get("25")); // 密码
					csvFileOutputStream.write(bd.toString() + ",");*/

					bd = new StringBuilder();
					bd.append(map.get("2")); // 姓名
					csvFileOutputStream.write(bd.toString() + ",");

					bd = new StringBuilder();
					bd.append(map.get("9")); // 性别
					csvFileOutputStream.write(bd.toString() + ",");

					bd = new StringBuilder();
					bd.append(map.get("7")); // 证件类型
					csvFileOutputStream.write(bd.toString() + ",");

					bd = new StringBuilder();
					bd.append(map.get("8")); // 证件号码
					csvFileOutputStream.write(bd.toString() + ",");
					
					bd = new StringBuilder();
					bd.append(map.get("11")); // 培训年级
					csvFileOutputStream.write(bd.toString() + ",");
					
					bd = new StringBuilder();
					bd.append(map.get("5")); // 专业基地
					csvFileOutputStream.write(bd.toString() + ",");
					
					bd = new StringBuilder();
					bd.append(map.get("18")); // 培训年限
					csvFileOutputStream.write(bd.toString() + ",");

					bd = new StringBuilder();
					bd.append(map.get("12")); // 电子邮箱
					csvFileOutputStream.write(bd.toString() + ",");

					bd = new StringBuilder();
					bd.append(map.get("6")); // 移动电话
					csvFileOutputStream.write(bd.toString() + ",");
					csvFileOutputStream.newLine();
				}
      }
      csvFileOutputStream.flush();
    } catch (Exception e) {
      e.printStackTrace();
    } finally {
      try {
        csvFileOutputStream.close();
        if (csvFileOutputStream != null){
        	csvFileOutputStream.close();
        }
        if (fos != null){
        	fos.close();
        }
      } catch (IOException e) {
        e.printStackTrace();
      }
    }
    return csvFile;
  }
 
  /**
   * 下载文件
   * @param response
   * @param csvFilePath
   *       文件路径
   * @param fileName
   *       文件名称
   * @throws IOException
   */
  public static void exportFile(HttpServletResponse response, String csvFilePath, String fileName)
                                                  throws IOException {
    response.setContentType("application/csv;charset=UTF-8");
    response.setHeader("Content-Disposition",
      "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));
 
    InputStream in = null;
    try {
      in = new FileInputStream(csvFilePath);
      int len = 0;
      byte[] buffer = new byte[1024];
      response.setCharacterEncoding("UTF-8");
      OutputStream out = response.getOutputStream();
      while ((len = in.read(buffer)) > 0) {
        out.write(buffer, 0, len);
      }
    } catch (FileNotFoundException e) {
      System.out.println(e);
    } finally {
      if (in != null) {
        try {
          in.close();
        } catch (Exception e) {
          throw new RuntimeException(e);
        }
      }
    }
  }
 
  /**
   * 删除该目录filePath下的所有文件
   * @param filePath
   *      文件目录路径
   */
  public static void deleteFiles(String filePath) {
    File file = new File(filePath);
    if (file.exists()) {
      File[] files = file.listFiles();
      for (int i = 0; i < files.length; i++) {
        if (files[i].isFile()) {
          files[i].delete();
        }
      }
    }
  }
 
  /**
   * 删除单个文件
   * @param filePath
   *     文件目录路径
   * @param fileName
   *     文件名称
   */
  public static void deleteFile(String filePath, String fileName) {
    File file = new File(filePath);
    if (file.exists()) {
      File[] files = file.listFiles();
      for (int i = 0; i < files.length; i++) {
        if (files[i].isFile()) {
          if (files[i].getName().equals(fileName)) {
            files[i].delete();
            return;
          }
        }
      }
    }
  }
}

