package com.hys.zyy.manage.util.excel;
/**
 * 导出excel公用的方法
 * <AUTHOR>
 * @date 2020-4-26下午5:13:02
 */
public class CommonExcelExportUtil {
	public static String toStr(String str){
		return str==null?"":str ;
	}
	
	/**
	 * 特殊字符串数字添加制表符
	 * @param bd
	 * @param str
	 * @return
	 * <AUTHOR>
	 * @date 2018-11-8下午6:19:17
	 */
	public static String toAppendTab(StringBuilder bd,String str){
		if (str == null){
			return "";
		}
		bd = new StringBuilder();
		bd.append("\t");
		bd.append(str);
		return bd.toString();
	}
	/**
	 * 证件类型
	 * @param certificateType
	 * @return
	 * <AUTHOR>
	 * @date 2020-4-27上午11:19:36
	 */
	public static String getCertificateType(Integer certificateType) {
		if (certificateType == null){
			return "未知";
		}else if(certificateType == 1){
			return "居民身份证";
		}else if(certificateType == 2){
		   return "军官证";
		}else if(certificateType == 3){
			return "护照";
		}else if(certificateType == 4){
			return "港澳通行证";
		}else if(certificateType == 5){
			return "台胞证";
		}
		return "";
	}
	/**
	 * 用户类型
	 * @param residencySource
	 * @return
	 * <AUTHOR>
	 * @date 2020-4-27上午11:22:32
	 */
	public static String getResidencySource(Integer residencySource) {
		if (residencySource != null && residencySource == 2) {
			return "社会人";
		} else if (residencySource != null && residencySource == 3) {
			return "学位衔接";
		} else if (residencySource != null && residencySource == 1) {
			return "单位人";
		}
		return "";
	}
	/**
	 * 1 本单位住院医师 2 外单位委托培养住院医师 3 面向社会招收住院医师 4全日制硕士专业研究生
	 * @param doctorUserType
	 * @return
	 * <AUTHOR>
	 * @date 2020-4-27上午11:35:39
	 */
	public static String getDoctorUserType(Integer doctorUserType) {
		if (doctorUserType == null){
			return "";
		}
		if (doctorUserType == 1) {
			return "本单位住院医师";
		} else if (doctorUserType == 2) {
			return "外单位委托培养住院医师";
		} else if (doctorUserType == 3) {
			return "面向社会招收住院医师";
		} else if (doctorUserType == 4) {
			return "全日制硕士专业研究生";
		}
		return "";
	}
	/**
	 * 是否为军队人员  1 现役军人  2 否  3 军队文职人员
	 */
	public static String getArmyType(Integer armyType) {
		if (armyType == null){
			return "";
		}
		if (armyType == 1) {
			return "现役军人";
		} else if (armyType == 2) {
			return "否";
		} else if (armyType == 3) {
			return "军队文职人员";
		}  
		return "";
	}
	/**
	 * 是否
	 * @param type
	 * @return
	 * <AUTHOR>
	 * @date 2020-4-27下午1:58:19
	 */
	public static String getYesNoStr(Integer type) {
		if (type == null){
			return "";
		}
		if (type == 1) {
			return "是";
		} else if (type == 0) {
			return "否";
		}   
		return "";
	}
	/**
	 * 证书类别
	 * @param physiciansQualificationType
	 * @return
	 * <AUTHOR>
	 * @date 2020-4-27下午1:58:43
	 */
	public static String getPhysiciansQualificationType(
			Integer physiciansQualificationType) {
		if (physiciansQualificationType == null){
			return "";
		}
		if (Integer.valueOf(1).equals(physiciansQualificationType)){
			return "临床";
		} else if (Integer.valueOf(2).equals(physiciansQualificationType)){
			return "口腔";
		} else if (Integer.valueOf(3).equals(physiciansQualificationType)){
			return "公共卫生";
		} else if (Integer.valueOf(4).equals(physiciansQualificationType)){
			return "中医";
		}
		return "无";
	}
	
	
}
