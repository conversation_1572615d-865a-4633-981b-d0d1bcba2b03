package com.hys.zyy.manage.util.excel;

import java.io.InputStream;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFDateUtil;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.ss.util.NumberToTextConverter;
import org.springframework.web.multipart.MultipartFile;

import com.hys.beq.entities.vo.exam.Examinee;
import com.hys.zyy.manage.util.CollectionUtils;

/**
 * 导入学生成绩
 * <AUTHOR>
 *
 */
public class ExcelImportCourseStudentScoreUtil {
	/**
	 * 读取excel中的数据
	 * @param file
	 * @return
	 * 2018-6-1下午1:39:58
	 * <AUTHOR>
	 */
	public static List<Examinee> getExamineeFromExcelCSV(MultipartFile file) {
		// IO流读取文件  
        InputStream input = null; 
        List<Examinee> list = new ArrayList<Examinee>();
		try {
			if (file != null){
				input = file.getInputStream();
				
				list = CSVImportStudentScoreUtils.readCsv(input);
			}
		} catch (Exception e) {
			e.printStackTrace();
		} finally{
			if (input != null){
				try {
					input.close();
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
		}
		return list;
	}
	
	/**
	 * 读取excel中的数据
	 * @param file
	 * @return
	 * 2018-6-1下午1:39:58
	 * <AUTHOR>
	 */
	public static List<Examinee> getExamineeFromExcel(MultipartFile file) {
		// IO流读取文件  
        InputStream input = null; 
        List<Examinee> list = new ArrayList<Examinee>();
		try {
			if (file != null){
				input = file.getInputStream();
				
				HSSFWorkbook workbook = new HSSFWorkbook(input);
				HSSFSheet sheet = workbook.getSheetAt(0);
				
				if (sheet != null){
					int title = 11;
					boolean flag = checkExcelFormat(title,sheet);
					if (flag){
						return list;
					}
					
					int totalRows = sheet.getLastRowNum();
					Examinee domain = null;
					//读取Row,从第2行开始
					for (int i = 1; i <= totalRows; i++) {
						domain = new Examinee();
						HSSFRow hSSFRow  = sheet.getRow(i);
						String lineNo = getCellValue(hSSFRow.getCell(0)); 
						if (StringUtils.isNotBlank(lineNo)){
							//获取每一行的值
							setData(domain,hSSFRow);
							list.add(domain);
						}
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		} finally{
			if (input != null){
				try {
					input.close();
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
		}
		return list;
	}
	
	/**
	 * 校验一下excel的格式是否正确
	 * @param title
	 * @return
	 */
	private static boolean checkExcelFormat(int title,HSSFSheet sheet) {
		HSSFRow  row = sheet.getRow(1);
		boolean flag = false;
		for (int i = 0; i < title; i++) {
			String value = getCellValue(row.getCell(i)); 
			if (StringUtils.isBlank(value)){
				flag = true;
				break;
			}
		}
		return flag;
	}
	/**
	 * 获取每一行的值   
	 * @param domain
	 * @param hSSFRow
	 * 2018-3-23下午3:21:37
	 * <AUTHOR>
	 */
	private static void setData(Examinee domain,HSSFRow hSSFRow) {
		//考试名称,考试科目，考试时间,姓名,性别，证件号 - 5,是否进入考试,是否提交试卷,考试用时,主观成绩 -9,客观成绩-10

		 //证件号
        String certificateNo = getCellValue(hSSFRow.getCell(5)); 
        //主观成绩
        String subjectiveScore = getCellValue(hSSFRow.getCell(6)); 
        //客观成绩
        String objectiveScore = getCellValue(hSSFRow.getCell(7)); 

        domain.setCertificateNo(certificateNo);
        domain.setSubjectiveScore(subjectiveScore);
        domain.setObjectiveScore(objectiveScore);
	}
	
	/** 
     * 根据excel单元格类型获取excel单元格值 
     * @param cell 
     * @return 
     */  
    private static String getCellValue(Cell cell) {  
        String cellvalue = "";  
        if (cell != null) {  
            // 判断当前Cell的Type  
            switch (cell.getCellType()) {  
            // 如果当前Cell的Type为NUMERIC  
            case HSSFCell.CELL_TYPE_NUMERIC: {  
                short format = cell.getCellStyle().getDataFormat();  
                if(format == 14 || format == 31 || format == 57 || format == 58){   //excel中的时间格式  
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");    
                    double value = cell.getNumericCellValue();    
                    Date date = DateUtil.getJavaDate(value);    
                    cellvalue = sdf.format(date);    
                }  
                // 判断当前的cell是否为Date  
                else if (HSSFDateUtil.isCellDateFormatted(cell)) {  //先注释日期类型的转换，在实际测试中发现HSSFDateUtil.isCellDateFormatted(cell)只识别2014/02/02这种格式。  
                    // 如果是Date类型则，取得该Cell的Date值           // 对2014-02-02格式识别不出是日期格式  
                    Date date = cell.getDateCellValue();  
                    DateFormat formater = new SimpleDateFormat("yyyy-MM-dd");  
                    cellvalue= formater.format(date);  
                } else { // 如果是纯数字  
                    // 取得当前Cell的数值  
                    cellvalue = NumberToTextConverter.toText(cell.getNumericCellValue());   
                      
                }  
                break;  
            }  
            // 如果当前Cell的Type为STRIN  
            case HSSFCell.CELL_TYPE_STRING:  
                // 取得当前的Cell字符串  
                cellvalue = cell.getStringCellValue().replaceAll("'", "''");  
                break;  
            case  HSSFCell.CELL_TYPE_BLANK:  
                cellvalue = null;  
                break;  
            // 默认的Cell值  
            default:{  
                cellvalue = " ";  
            }  
            }  
        } else {  
            cellvalue = "";  
        }  
        return cellvalue;  
    }
    
 /**
  * 校验数据是否合法
  * @param list
  * @return
  */
	public static String checkImportExcelDataValide(List<Examinee> list) {
		
		String result = "";
		if (CollectionUtils.isNotEmpty(list)){
			int size = list.size();
			for (int i = 0; i < size; i++) {
				Examinee item = list.get(i);
				String certificateNo = item.getCertificateNo();
				String subjectiveScore = item.getSubjectiveScore();
				String objectiveScore = item.getObjectiveScore();
				 
				if (StringUtils.isBlank(certificateNo)){
					result =  "第"+(i+2)+"行：行,证件号为空！";
					break;
				}
				if (StringUtils.isBlank(subjectiveScore)){
					result =  "第"+(i+2)+"行：行,主观成绩为空！";
					break;
				} else {
					subjectiveScore = subjectiveScore.replaceAll("\"", "").trim();
					item.setSubjectiveScore(subjectiveScore.replaceAll("\t", ""));
				}
				if (StringUtils.isBlank(objectiveScore)){
					result =  "第"+(i+2)+"行：行,客观成绩为空！";
					break;
				} else {
					objectiveScore = objectiveScore.replaceAll("\"", "").trim();
					item.setObjectiveScore(objectiveScore.replaceAll("\t", ""));
				}
			}
		}
		
		return result;
	}  
}
