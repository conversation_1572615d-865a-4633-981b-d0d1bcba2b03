package com.hys.zyy.manage.util.excel;

import java.io.InputStream;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFDateUtil;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.ss.util.NumberToTextConverter;
import org.springframework.web.multipart.MultipartFile;

import com.hys.beq.entities.vo.HysQuestionVO;
import com.hys.beq.entities.vo.QuesAttrRelVO;
import com.hys.zyy.manage.util.CollectionUtils;

/**
 * excel工具类  导入试题
 * <AUTHOR>
 * @date 2020-1-7上午10:17:12
 */
public class ExcelImportExamQuestionUtil {
	/**
	 * 读取excel中的数据
	 * @param file
	 * @return
	 * 2018-6-1下午1:39:58
	 * <AUTHOR>
	 */
	public static List<HysQuestionVO> getDataFromExcel(MultipartFile file) {
		// IO流读取文件  
        InputStream input = null; 
        List<HysQuestionVO> list = new ArrayList<HysQuestionVO>();
		try {
			if (file != null){
				input = file.getInputStream();
				
				HSSFWorkbook workbook = new HSSFWorkbook(input);
				HSSFSheet sheet = workbook.getSheetAt(0);
				
				if (sheet != null){
					int title = 17;
					boolean flag = checkExcelFormat(title,sheet);
					if (flag){
						return list;
					}
					
					int totalRows = sheet.getLastRowNum();
					HysQuestionVO domain = null;
					//读取Row,从第2行开始
					for (int i = 1; i <= totalRows; i++) {
						domain = new HysQuestionVO();
						HSSFRow hSSFRow  = sheet.getRow(i);
						String seqNo = getCellValue(hSSFRow.getCell(0));//试题编号
				        String pSeqNo = getCellValue(hSSFRow.getCell(1));//父试题编号
				        String labelName = getCellValue(hSSFRow.getCell(2));//题型
						if(StringUtils.isBlank(seqNo) && StringUtils.isBlank(pSeqNo) && StringUtils.isBlank(labelName)){
					            //试题编号和父试题编号,类型名称都不存在，则为空行，跳出循环
					            break;
					    }
						//获取每一行的值
						setData(domain,hSSFRow);
						list.add(domain);
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		} finally{
			if (input != null){
				try {
					input.close();
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
		}
		return list;
	}
	
	/**
	 * 校验一下excel的格式是否正确
	 * @param title
	 * @return
	 */
	private static boolean checkExcelFormat(int title,HSSFSheet sheet) {
		HSSFRow  row = sheet.getRow(0);
		boolean flag = false;
		for (int i = 0; i < title; i++) {
			String value = getCellValue(row.getCell(i)); 
			if (StringUtils.isBlank(value)){
				flag = true;
				break;
			}
		}
		return flag;
	}
	/**
	 * 获取每一行的值   
	 * @param domain
	 * @param hSSFRow
	 * 2018-3-23下午3:21:37
	 * <AUTHOR>
	 */
	private static void setData(HysQuestionVO domain,HSSFRow hSSFRow) {
		//试题编号(不能重复)	父试题编号	题型	难度系数	是否带图	题库属性	
		//题干内容	正确答案	答案解析	选项A	选项B	选项C	选项D	选项E	选项F	选项G	选项H

		String seqNo = getCellValue(hSSFRow.getCell(0));//试题编号
        String pSeqNo = getCellValue(hSSFRow.getCell(1));//父试题编号
        String labelName = getCellValue(hSSFRow.getCell(2)).replace("（","(").replace("）",")");//题型
        String qDifficultyGrade = getCellValue(hSSFRow.getCell(3));//难度系数
        String isnotMultimediaDesc = getCellValue(hSSFRow.getCell(4));//是否带图
        String storageAttries = getCellValue(hSSFRow.getCell(5));//题库属性路径
        String content = getCellValue(hSSFRow.getCell(6));//题干内容
        String rightKey = getCellValue(hSSFRow.getCell(7));//正确答案
        String analyse = getCellValue(hSSFRow.getCell(8));//答案解析
        String quesKeyA = getCellValue(hSSFRow.getCell(9));//选项A
        String quesKeyB = getCellValue(hSSFRow.getCell(10));//选项B
        String quesKeyC = getCellValue(hSSFRow.getCell(11));//选项C
        String quesKeyD = getCellValue(hSSFRow.getCell(12));//选项D
        String quesKeyE = getCellValue(hSSFRow.getCell(13));//选项E
        String quesKeyF = getCellValue(hSSFRow.getCell(14));//选项F
        String quesKeyG = getCellValue(hSSFRow.getCell(15));//选项G
        String quesKeyH = getCellValue(hSSFRow.getCell(16));//选项H

        /* 调用数据库方法，题库属性 */
        String[] attrsPathArr = storageAttries.split(",");
        List<QuesAttrRelVO> quesAttrRelList = new ArrayList<QuesAttrRelVO>();
        for(String attrsPath : attrsPathArr){
            //验证属性是否正确
            QuesAttrRelVO relVO = new QuesAttrRelVO();
            relVO.setAttrName(attrsPath);
            quesAttrRelList.add(relVO);
        }
        Integer isnotMultimedia = null;//多媒体试题
        if ("是".equals(isnotMultimediaDesc)){
        	isnotMultimedia = 1;
        } else if ("否".equals(isnotMultimediaDesc)){
        	isnotMultimedia = 0;
        } else {
        	isnotMultimedia = -1;
        }
        //赋值
        domain.setSeqNo(seqNo);
        domain.setpSeqNo(pSeqNo);
        domain.setLabelName(labelName);
        domain.setContent(content);
        domain.setAnalyse(analyse);
        domain.setIsnotMultimedia(isnotMultimedia);
        //试题难度
        if ("简单".equals(qDifficultyGrade)){
        	domain.setqDifficultyGrade(1);
        } else if ("中等".equals(qDifficultyGrade)){
        	domain.setqDifficultyGrade(2);
        } else if ("较难".equals(qDifficultyGrade)){
        	domain.setqDifficultyGrade(3);
        } else {
        	domain.setqDifficultyGrade(-1);//不合法
        }
        domain.setRightKey(rightKey);
        domain.setQuesAttrRelList(quesAttrRelList);
        domain.setQuesKeyA(quesKeyA);
        domain.setQuesKeyB(quesKeyB);
        domain.setQuesKeyC(quesKeyC);
        domain.setQuesKeyD(quesKeyD);
        domain.setQuesKeyE(quesKeyE);
        domain.setQuesKeyF(quesKeyF);
        domain.setQuesKeyG(quesKeyG);
        domain.setQuesKeyH(quesKeyH);
        domain.setStorageAttries(storageAttries);
	}
	
	/** 
     * 根据excel单元格类型获取excel单元格值 
     * @param cell 
     * @return 
     */  
    private static String getCellValue(Cell cell) {  
        String cellvalue = "";  
        if (cell != null) {  
            // 判断当前Cell的Type  
            switch (cell.getCellType()) {  
            // 如果当前Cell的Type为NUMERIC  
            case HSSFCell.CELL_TYPE_NUMERIC: {  
                short format = cell.getCellStyle().getDataFormat();  
                if(format == 14 || format == 31 || format == 57 || format == 58){   //excel中的时间格式  
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");    
                    double value = cell.getNumericCellValue();    
                    Date date = DateUtil.getJavaDate(value);    
                    cellvalue = sdf.format(date);    
                }  
                // 判断当前的cell是否为Date  
                else if (HSSFDateUtil.isCellDateFormatted(cell)) {  //先注释日期类型的转换，在实际测试中发现HSSFDateUtil.isCellDateFormatted(cell)只识别2014/02/02这种格式。  
                    // 如果是Date类型则，取得该Cell的Date值           // 对2014-02-02格式识别不出是日期格式  
                    Date date = cell.getDateCellValue();  
                    DateFormat formater = new SimpleDateFormat("yyyy-MM-dd");  
                    cellvalue= formater.format(date);  
                } else { // 如果是纯数字  
                    // 取得当前Cell的数值  
                    cellvalue = NumberToTextConverter.toText(cell.getNumericCellValue());   
                      
                }  
                break;  
            }  
            // 如果当前Cell的Type为STRIN  
            case HSSFCell.CELL_TYPE_STRING:  
                // 取得当前的Cell字符串  
                cellvalue = cell.getStringCellValue().replaceAll("'", "''");  
                break;  
            case  HSSFCell.CELL_TYPE_BLANK:  
                cellvalue = null;  
                break;  
            // 默认的Cell值  
            default:{  
                cellvalue = "";  
            }  
            }  
        } else {  
            cellvalue = "";  
        }  
        return cellvalue == null?null:cellvalue.trim();  
    }
    
  /**
   * 校验数据是否合法
   * @param hggMap
   * @param list
   * @return
   * <AUTHOR>
   * @date 2019-6-26下午3:30:55
   */
	public static String checkImportExcelDataValide(
			Map<String, String> labelMap,
			List<HysQuestionVO> list,Long orgId,String accountName,Map<String,Long> attrMap) {
		String[] pLabelCodes = {"A3","A4","B1","C","NLFX"};//父子试题类型
		String[] keyTypes ={"A1","A2","A3","A4","B1","C","NLFX","X","PD","TK"};
		String result = "";
		if (CollectionUtils.isNotEmpty(list)){
			int count = 0;
			int size = list.size();
			 //遍历行--检查数据质量
            StringBuffer errorMsg = new StringBuffer("导入失败，错误原因： ");
			for (int j = 0; j < size; j++) {
				HysQuestionVO item = list.get(j);
				//全部校验通过了，赋值
                item.setOrgId(orgId);
                item.setSourceOrgId(orgId);
                item.setCreator(accountName);
                
				String seqNo = item.getSeqNo();//试题编号
                String pSeqNo = item.getpSeqNo();//父试题编号
                String labelName = item.getLabelName();//题型
                Integer qDifficultyGrade = item.getqDifficultyGrade();//难度系数
                Integer isnotMultimedia = item.getIsnotMultimedia();//是否带图
                String storageAttries = item.getStorageAttries();//题库属性路径
                String content = item.getContent();//题干内容
                String rightKey = item.getRightKey();//正确答案
                String analyse = item.getAnalyse();//答案解析
                String quesKeyA = item.getQuesKeyA();//选项A
                String quesKeyB = item.getQuesKeyB();//选项B
                String quesKeyC = item.getQuesKeyC();//选项C
                String quesKeyD = item.getQuesKeyD();//选项D
                String quesKeyE = item.getQuesKeyE();//选项E

                if(StringUtils.isBlank(seqNo) && StringUtils.isBlank(pSeqNo) && StringUtils.isBlank(labelName)){
                    //试题编号和父试题编号,类型名称都不存在，则为空行，跳出循环
                    break;
                }
                if(StringUtils.isBlank(seqNo)){
                    errorMsg.append("\r\n        第"+ (j + 2) +"行,试题编号数据丢失 ");
                    count ++;
                }
                if(StringUtils.isBlank(pSeqNo)){
                    errorMsg.append("\r\n        第"+ (j + 2) +"行,父试题编号数据丢失 ");
                    count ++;
                }
                //题型
                if(StringUtils.isBlank(labelName) || labelMap.get(labelName) == null){
                    errorMsg.append("\r\n        第"+ (j + 2) +"行,试题类型信息不合法 ");
                    count ++;
                }
                String labelCode = labelMap.get(labelName);//试题类型
                item.setLabelCode(labelCode);
                //是否为父子试题,不是父子试题，直接进入下一题
                if(!Arrays.asList(pLabelCodes).contains(labelCode) && (StringUtils.isNotBlank(pSeqNo) && !"0".equals(pSeqNo))){
                    errorMsg.append("\r\n        第"+ (j + 2) +"行,题型异常，该题型不能有父试题 ");
                    count ++;
                }
                if(labelCode.equals("TK")){
                    quesKeyA = rightKey;//将正确答案赋值给A选项
                }
                //判断题
                if(labelCode.equals("PD")){
                    quesKeyA="正确";
                    quesKeyB="错误";
                }
                item.setQuesKeyA(quesKeyA);
                item.setQuesKeyB(quesKeyB);
                //试题难度
                if(qDifficultyGrade == null){
                    errorMsg.append("\r\n        第"+ (j + 2) +"行,试题难度数据丢失 ");
                    count ++;
                }
                if(qDifficultyGrade != null && qDifficultyGrade.intValue() == -1){
                    errorMsg.append("\r\n        第"+ (j + 2) +"行,试题难度数据不合法 ");
                    count ++;
                }
                //是否带图
                if(isnotMultimedia == null || isnotMultimedia == -1 ){
                    errorMsg.append("\r\n        第"+ (j + 2) +"行,试题编号数据不合法 ");
                    count ++;
                }
                //题干
                if(StringUtils.isBlank(content)){
                    errorMsg.append("\r\n        第"+ (j + 2) +"行,题干信息丢失 ");
                    count ++;
                }
                //题库属性
                if(StringUtils.isBlank(storageAttries)){
                    errorMsg.append("\r\n        第"+ (j + 2) +"行,试题题库属性丢失 ");
                    count ++;
                }
                /* 调用数据库方法，验证题库属性合法，Start */
                List<QuesAttrRelVO> quesAttrRelList = item.getQuesAttrRelList();
                if (CollectionUtils.isNotEmpty(quesAttrRelList)){
                	for(QuesAttrRelVO relVO : quesAttrRelList){
                		String attrsPath = relVO.getAttrName();
                		if (StringUtils.isNotBlank(attrsPath)){
                			//验证属性是否正确
                			Long attrId = attrMap.get(attrsPath);
                			if(attrId == null){
                				errorMsg.append("\r\n        第"+ (j + 2) +"行,试题题库属性[ "+attrsPath+" ]不合法！ ");
                				count ++;
                			}
                		}
                	}
                }
                /* 调用数据库方法，验证题库属性合法，End */
                /* 根据试题类型，判断答案的数量及条件 */
                //不包含答案的试题直接下一题。包含答案，进行答案判断
                if(!Arrays.asList(keyTypes).contains(labelCode)){
                    continue;
                }
                //父子试题，父试题无答案选项和试题解析
                if(Arrays.asList(pLabelCodes).contains(labelCode) && "0".equals(pSeqNo)){
                    continue;
                }
                if(StringUtils.isBlank(rightKey)){
                    errorMsg.append("\r\n        第"+ (j + 2) +"行,试题正确答案信息为空 ");
                    count ++;
                }
                //填空
                if(labelCode.equals("TK")){
                    continue;
                }
                //B型C型，无答案选项，继续下次
                if(labelCode.equals("C")||labelCode.equals("B1")){
                    //四个选项，选项题
                    continue;
                }
                //判断题
                if(labelCode.equals("PD")){
                    if(!rightKey.equals("正确") && !rightKey.equals("错误")){
                        errorMsg.append("\r\n        第"+ (j + 2) +"行,试题正确答案信息不合法 ");
                        count ++;
                    }
                    continue;
                }
                if(StringUtils.isBlank(quesKeyA)){
                    errorMsg.append("\r\n        第"+ (j + 2) +"行,试题第一个选项为空 ");
                    count ++;
                }
                if(StringUtils.isBlank(quesKeyB)){
                    errorMsg.append("\r\n        第"+ (j + 2) +"行,试题第二个选项为空 ");
                    count ++;
                }
                if(StringUtils.isBlank(quesKeyC)){
                    errorMsg.append("\r\n        第"+ (j + 2) +"行,试题第三个选项为空 ");
                    count ++;
                }
                if(StringUtils.isBlank(quesKeyD)){
                    errorMsg.append("\r\n        第"+ (j + 2) +"行,试题第四个选项为空 ");
                    count ++;
                }
                if(StringUtils.isBlank(quesKeyE)){
                    errorMsg.append("\r\n        第"+ (j + 2) +"行,试题第五个选项为空 ");
                    count ++;
                }
               
			}
			if (count > 0){
				return errorMsg.toString() ;
			}
		}
		
		return result;
	}  
	
}
