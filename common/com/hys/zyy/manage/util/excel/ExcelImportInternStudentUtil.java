package com.hys.zyy.manage.util.excel;

import java.io.InputStream;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFDateUtil;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.ss.util.NumberToTextConverter;
import org.springframework.web.multipart.MultipartFile;

import com.hys.zyy.manage.constants.SymbleConstants;
import com.hys.zyy.manage.model.ZyyHospitalGradeGroup;
import com.hys.zyy.manage.model.vo.ZyyUserExtendInternVO;
import com.hys.zyy.manage.util.CollectionUtils;

/**
 * excel工具类  实习生 批量修改
* <p>Description: </p>
* <AUTHOR> 
* @date 2018-6-1下午1:39:58
 */
public class ExcelImportInternStudentUtil {
	/**
	 * 读取excel中的数据
	 * @param file
	 * @return
	 * 2018-6-1下午1:39:58
	 * <AUTHOR>
	 */
	public static List<ZyyUserExtendInternVO> getUserExtendInternFromExcel(MultipartFile file) {
		// IO流读取文件  
        InputStream input = null; 
        List<ZyyUserExtendInternVO> list = new ArrayList<ZyyUserExtendInternVO>();
		try {
			if (file != null){
				input = file.getInputStream();
				
				HSSFWorkbook workbook = new HSSFWorkbook(input);
				HSSFSheet sheet = workbook.getSheetAt(0);
				
				if (sheet != null){
					int title = 9;
					boolean flag = checkExcelFormat(title,sheet);
					if (flag){
						return list;
					}
					
					int totalRows = sheet.getLastRowNum();
					ZyyUserExtendInternVO domain = null;
					//读取Row,从第2行开始
					for (int i = 1; i <= totalRows; i++) {
						domain = new ZyyUserExtendInternVO();
						HSSFRow hSSFRow  = sheet.getRow(i);
						String lineNo = getCellValue(hSSFRow.getCell(0)); 
						if (StringUtils.isNotBlank(lineNo)){
							//获取每一行的值
							setData(domain,hSSFRow);
							list.add(domain);
						}
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		} finally{
			if (input != null){
				try {
					input.close();
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
		}
		return list;
	}
	
	/**
	 * 校验一下excel的格式是否正确
	 * @param title
	 * @return
	 */
	private static boolean checkExcelFormat(int title,HSSFSheet sheet) {
		HSSFRow  row = sheet.getRow(0);//用excel的第1行判断
		boolean flag = false;
		for (int i = 0; i < title; i++) {
			String value = getCellValue(row.getCell(i)); 
			if (StringUtils.isBlank(value)){
				flag = true;
				break;
			}
		}
		return flag;
	}
	/**
	 * 获取每一行的值   
	 * @param domain
	 * @param hSSFRow
	 * 2018-3-23下午3:21:37
	 * <AUTHOR>
	 */
	private static void setData(ZyyUserExtendInternVO domain,HSSFRow hSSFRow) {
		//导出的为准
		//"姓名 0"	"性别	1"	"证件号码	2"	"专业3	"	"年级4	"	"电话	5"	"分班	6"	"分组	7"	"学校	8"
 

		String certificateNo = getCellValue(hSSFRow.getCell(2)); 
		String year = getCellValue(hSSFRow.getCell(4)); 
		String major = getCellValue(hSSFRow.getCell(3)); 
		String userGrade = getCellValue(hSSFRow.getCell(6)); 
		String userGroup = getCellValue(hSSFRow.getCell(7)); 
		String highestGraduateSchool = getCellValue(hSSFRow.getCell(8)); 
		
		domain.setCertificateNo(certificateNo);
		domain.setYear(year);
		domain.setMajor(major);
		domain.setUserGrade(userGrade);
		domain.setUserGroup(userGroup);
		domain.setHighestGraduateSchool(highestGraduateSchool);
	}
	
	/** 
     * 根据excel单元格类型获取excel单元格值 
     * @param cell 
     * @return 
     */  
    private static String getCellValue(Cell cell) {  
        String cellvalue = "";  
        if (cell != null) {  
            // 判断当前Cell的Type  
            switch (cell.getCellType()) {  
            // 如果当前Cell的Type为NUMERIC  
            case HSSFCell.CELL_TYPE_NUMERIC: {  
                short format = cell.getCellStyle().getDataFormat();  
                if(format == 14 || format == 31 || format == 57 || format == 58){   //excel中的时间格式  
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");    
                    double value = cell.getNumericCellValue();    
                    Date date = DateUtil.getJavaDate(value);    
                    cellvalue = sdf.format(date);    
                }  
                // 判断当前的cell是否为Date  
                else if (HSSFDateUtil.isCellDateFormatted(cell)) {  //先注释日期类型的转换，在实际测试中发现HSSFDateUtil.isCellDateFormatted(cell)只识别2014/02/02这种格式。  
                    // 如果是Date类型则，取得该Cell的Date值           // 对2014-02-02格式识别不出是日期格式  
                    Date date = cell.getDateCellValue();  
                    DateFormat formater = new SimpleDateFormat("yyyy-MM-dd");  
                    cellvalue= formater.format(date);  
                } else { // 如果是纯数字  
                    // 取得当前Cell的数值  
                    cellvalue = NumberToTextConverter.toText(cell.getNumericCellValue());   
                      
                }  
                break;  
            }  
            // 如果当前Cell的Type为STRIN  
            case HSSFCell.CELL_TYPE_STRING:  
                // 取得当前的Cell字符串  
                cellvalue = cell.getStringCellValue().replaceAll("'", "''");  
                break;  
            case  HSSFCell.CELL_TYPE_BLANK:  
                cellvalue = null;  
                break;  
            // 默认的Cell值  
            default:{  
                cellvalue = " ";  
            }  
            }  
        } else {  
            cellvalue = "";  
        }
        if (StringUtils.isNotBlank(cellvalue)){
        	cellvalue = cellvalue.trim();
        }
        return cellvalue;  
    }
    
  /**
   * 校验数据是否合法
   * @param hggMap
   * @param list
   * @return
   * <AUTHOR>
   * @date 2019-6-26下午3:30:55
   */
	public static String checkImportExcelDataValide(
			Map<String, ZyyHospitalGradeGroup> hggMap,
			List<ZyyUserExtendInternVO> list) {
		
		String result = "";
		if (CollectionUtils.isNotEmpty(list)){
			int size = list.size();
			StringBuilder  builder = null;
			for (int i = 0; i < size; i++) {
				ZyyUserExtendInternVO item = list.get(i);
				String year = item.getYear();
				String major = item.getMajor();
				if (StringUtils.isBlank(year) || StringUtils.isBlank(major)){
					continue;
				}
				String grade = item.getUserGrade();
				String group = item.getUserGroup();
				builder = new StringBuilder();
				builder.append(year.trim());
				builder.append(SymbleConstants.SUB);
				builder.append(major.trim());
				
				ZyyHospitalGradeGroup hospitalGradeGroup = hggMap.get(builder.toString());
				if (hospitalGradeGroup == null){
					result =  "第"+(i+2)+"行：表格中年级和专业没有设置分班分组！";
					break;
				}
				if (StringUtils.isNotBlank(grade)){
					Integer gradeMax = hospitalGradeGroup.getGradeMax();
					if (gradeMax == null){
						result =  "第"+(i+2)+"行：表格中年级和专业没有设置分班！";
						break;
					}
					if (Integer.valueOf(grade) < 0){
						result =  "第"+(i+2)+"行：表格中分班不合法！";
						break;
					}
					if (Integer.valueOf(grade) > gradeMax){
						result =  "第"+(i+2)+"行：表格中分班超过了最大值限制！";
						break;
					}
				}
				if (StringUtils.isNotBlank(group)){
					Integer groupMax = hospitalGradeGroup.getGroupMax();
					if (groupMax == null){
						result =  "第"+(i+2)+"行：表格中年级和专业没有设置分组！";
						break;
					}
					if (Integer.valueOf(group) < 0){
						result =  "第"+(i+2)+"行：表格中分组不合法！";
						break;
					}
					if (Integer.valueOf(group) > groupMax){
						result =  "第"+(i+2)+"行：表格中分组超过了最大值限制！";
						break;
					}
				}
			}
		}
		
		return result;
	}  
}
