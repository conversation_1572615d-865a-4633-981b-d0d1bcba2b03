package com.hys.zyy.manage.util.excel;

import java.io.FileInputStream;
import java.io.InputStream;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFDateUtil;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.NumberToTextConverter;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.web.multipart.MultipartFile;

import com.hys.zyy.manage.constants.Constants;
import com.hys.zyy.manage.model.ZyyGraduateTargetStudent;

/**
 * excel工具类
* <p>Description: </p>
* <AUTHOR> 
* @date 2018-6-1下午1:39:58
 */
public class ExcelImportStudentUtil {
	/**
	 * 读取专硕研究生excel中的数据
	 * @param file
	 * @return
	 * 2018-6-1下午1:39:58
	 * <AUTHOR>
	 */
	public static List<ZyyGraduateTargetStudent> getGraduateFromExcel(MultipartFile file) {
		// IO流读取文件  
        InputStream input = null; 
        List<ZyyGraduateTargetStudent> list = new ArrayList<ZyyGraduateTargetStudent>();
		try {
			if (file != null){
				input = file.getInputStream();
				
				XSSFWorkbook workbook = new XSSFWorkbook(input);
				XSSFSheet sheet = workbook.getSheetAt(0);
				XSSFRow row =sheet.getRow(0);
				XSSFCell cell= row.getCell(0);
				
				if (sheet != null){
					int title = 10;
					boolean flag = checkExcelFormat(title,sheet);
					if (flag){
						return list;
					}
					
					int totalRows = sheet.getLastRowNum();
					ZyyGraduateTargetStudent domain = null;
					//读取Row,从第3行开始
					for (int i = 2; i <= totalRows; i++) {
						domain = new ZyyGraduateTargetStudent();
						XSSFRow hSSFRow  = sheet.getRow(i);
						String lineNo = getCellValue(hSSFRow.getCell(0)); 
						if (StringUtils.isNotBlank(lineNo)){
							//获取每一行的值
							setGraduateData(domain,hSSFRow);
							list.add(domain);
						}
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		} finally{
			if (input != null){
				try {
					input.close();
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
		}
		return list;
	}
	
	/**
	 * 校验一下excel的格式是否正确
	 * @param title
	 * @return
	 */
	private static boolean checkExcelFormat(int title,XSSFSheet sheet) {
		XSSFRow  row = sheet.getRow(1);
		boolean flag = false;
		for (int i = 0; i < title; i++) {
			String value = getCellValue(row.getCell(i)); 
			if (StringUtils.isBlank(value)){
				flag = true;
				break;
			}
		}
		return flag;
	}
	/**
	 * 获取每一行的值  -- 专硕研究生
	 * @param domain
	 * @param hSSFRow
	 * 2018-3-23下午3:21:37
	 * <AUTHOR>
	 */
	private static void setGraduateData(ZyyGraduateTargetStudent domain,XSSFRow hSSFRow) {
		//序号	姓名	性别	身份证号码	手机号码	培养学校	住培基地	住培专业	培训年级 培训年限

		String lineNo = getCellValue(hSSFRow.getCell(0)); 
		String name = getCellValue(hSSFRow.getCell(1)); 
		String sex = getCellValue(hSSFRow.getCell(2)); 
		String certificateNo = getCellValue(hSSFRow.getCell(3)); 
		String mobileNumber = getCellValue(hSSFRow.getCell(4)); 
		String trainSchool = getCellValue(hSSFRow.getCell(5)); 
		String hospitalName = getCellValue(hSSFRow.getCell(6)); 
		String baseName = getCellValue(hSSFRow.getCell(7)); 
		String year = getCellValue(hSSFRow.getCell(8)); 
		String schoolSystem = getCellValue(hSSFRow.getCell(9)); 
		
		
		domain.setLineNo(lineNo);
		domain.setName(name);
		Integer sexInt = 0;
		if (StringUtils.isNotBlank(sex)){
			if (Constants.ZYY_SEX_MALE.equals(sex)){
				sexInt = Constants.SEX_MAN;
			}else if(Constants.ZYY_SEX_FEMALE.equals(sex)){
				sexInt = Constants.SEX_WOMAN;
			}
		}
		domain.setSex(sexInt);
		domain.setCertificateNo(certificateNo);
		domain.setMobileNumber(mobileNumber);
		domain.setTrainSchool(trainSchool);
		domain.setHospitalName(hospitalName);
		domain.setBaseName(baseName);
		domain.setStudentType(Constants.STUDENT_TYPE_GRADUATE);
		domain.setYear(year);
		Integer schooleSys = 0;
		if (StringUtils.isNotBlank(schoolSystem)){
			schooleSys = Integer.parseInt(schoolSystem);
		}
		domain.setSchoolSystem(schooleSys);
	}
	
	/**
	 * 读取订单定向免费医学生excel中的数据
	 * @param file
	 * @return
	 * 2018-6-1下午1:39:58
	 * <AUTHOR>
	 */
	public static List<ZyyGraduateTargetStudent> getTargetFromExcel(MultipartFile file) {
		// IO流读取文件  
        InputStream input = null; 
        List<ZyyGraduateTargetStudent> list = new ArrayList<ZyyGraduateTargetStudent>();
		try {
			if (file != null){
				input = file.getInputStream();
				
				XSSFWorkbook workbook = new XSSFWorkbook(input);
				XSSFSheet sheet = workbook.getSheetAt(0);
				XSSFRow row =sheet.getRow(0);
				XSSFCell cell= row.getCell(0);
				
				if (sheet != null){
					int title = 14;
					boolean flag = checkExcelFormat(title,sheet);
					if (flag){
						return list;
					}
					int totalRows = sheet.getLastRowNum();
					ZyyGraduateTargetStudent domain = null;
					//读取Row,从第3行开始
					for (int i = 2; i <= totalRows; i++) {
						domain = new ZyyGraduateTargetStudent();
						XSSFRow hSSFRow  = sheet.getRow(i);
						String lineNo = getCellValue(hSSFRow.getCell(0)); 
						if (StringUtils.isNotBlank(lineNo)){
							//获取每一行的值
							setTargetData(domain,hSSFRow);
							list.add(domain);
						}
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		} finally{
			if (input != null){
				try {
					input.close();
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
		}
		return list;
	}
	
	/**
	 * 获取每一行的值   -- 订单定向免费医学生
	 * @param domain
	 * @param hSSFRow
	 * 2018-3-23下午3:21:37
	 * <AUTHOR>
	 */
	private static void setTargetData(ZyyGraduateTargetStudent domain, XSSFRow  hSSFRow) {
		
		//序号	姓名	性别	身份证号码	手机号码	住培/助理基地	住培/助理专业	生源地-市	生源地-县	定向地-市	定向地-县	培养学校	培训年级   培训年限
		
		String lineNo = getCellValue(hSSFRow.getCell(0));
		String name = getCellValue(hSSFRow.getCell(1)); 
		String sex = getCellValue(hSSFRow.getCell(2)); 
		String certificateNo = getCellValue(hSSFRow.getCell(3)); 
		String mobileNumber = getCellValue(hSSFRow.getCell(4)); 
		
		String hospitalName = getCellValue(hSSFRow.getCell(5)); 
		String baseName = getCellValue(hSSFRow.getCell(6)); 
		String homeProvince = getCellValue(hSSFRow.getCell(7));
		String homeCity = getCellValue(hSSFRow.getCell(8));
		String targetPlaceProvince = getCellValue(hSSFRow.getCell(9));
		String targetPlaceCity = getCellValue(hSSFRow.getCell(10));
		
		String trainSchool = getCellValue(hSSFRow.getCell(11)); 
		String year = getCellValue(hSSFRow.getCell(12)); 
		String schoolSystem = getCellValue(hSSFRow.getCell(13)); 
		
		if (StringUtils.isBlank(targetPlaceProvince)){
			targetPlaceProvince = "";
		}
		if (StringUtils.isBlank(targetPlaceCity)){
			targetPlaceCity = "";
		}
		
		domain.setLineNo(lineNo);
		domain.setName(name);
		Integer sexInt = 0;
		if (StringUtils.isNotBlank(sex)){
			if (Constants.ZYY_SEX_MALE.equals(sex)){
				sexInt = Constants.SEX_MAN;
			}else if(Constants.ZYY_SEX_FEMALE.equals(sex)){
				sexInt = Constants.SEX_WOMAN;
			}
		}
		domain.setSex(sexInt);
		domain.setCertificateNo(certificateNo);
		domain.setMobileNumber(mobileNumber);
		domain.setTrainSchool(trainSchool);
		domain.setHospitalName(hospitalName);
		domain.setBaseName(baseName);
		domain.setHomeProvince(homeProvince);
		domain.setHomeCity(homeCity);
		domain.setTargetPlace(targetPlaceProvince+targetPlaceCity);
		domain.setTrainSchool(trainSchool);
		domain.setStudentType(Constants.STUDENT_TYPE_TARGET);
		domain.setYear(year);
		Integer schooleSys = 0;
		if (StringUtils.isNotBlank(schoolSystem)){
			schooleSys = Integer.parseInt(schoolSystem);
		}
		domain.setSchoolSystem(schooleSys);
	}
	
	/** 
     * 根据excel单元格类型获取excel单元格值 
     * @param cell 
     * @return 
     */  
    private static String getCellValue(Cell cell) {  
        String cellvalue = "";  
        if (cell != null) {  
            // 判断当前Cell的Type  
            switch (cell.getCellType()) {  
            // 如果当前Cell的Type为NUMERIC  
            case HSSFCell.CELL_TYPE_NUMERIC: {  
                short format = cell.getCellStyle().getDataFormat();  
                if(format == 14 || format == 31 || format == 57 || format == 58){   //excel中的时间格式  
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");    
                    double value = cell.getNumericCellValue();    
                    Date date = DateUtil.getJavaDate(value);    
                    cellvalue = sdf.format(date);    
                }  
                // 判断当前的cell是否为Date  
                else if (HSSFDateUtil.isCellDateFormatted(cell)) {  //先注释日期类型的转换，在实际测试中发现HSSFDateUtil.isCellDateFormatted(cell)只识别2014/02/02这种格式。  
                    // 如果是Date类型则，取得该Cell的Date值           // 对2014-02-02格式识别不出是日期格式  
                    Date date = cell.getDateCellValue();  
                    DateFormat formater = new SimpleDateFormat("yyyy-MM-dd");  
                    cellvalue= formater.format(date);  
                } else { // 如果是纯数字  
                    // 取得当前Cell的数值  
                    cellvalue = NumberToTextConverter.toText(cell.getNumericCellValue());   
                      
                }  
                break;  
            }  
            // 如果当前Cell的Type为STRIN  
            case HSSFCell.CELL_TYPE_STRING:  
                // 取得当前的Cell字符串  
                cellvalue = cell.getStringCellValue().replaceAll("'", "''");  
                break;  
            case  HSSFCell.CELL_TYPE_BLANK:  
                cellvalue = null;  
                break;  
            // 默认的Cell值  
            default:{  
                cellvalue = " ";  
            }  
            }  
        } else {  
            cellvalue = "";  
        }  
        return cellvalue;  
    }  
}
