package com.hys.zyy.manage.util.excel;

import java.io.InputStream;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFDateUtil;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.ss.util.NumberToTextConverter;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.web.multipart.MultipartFile;

import com.hys.zyy.manage.model.ZyyGraduateTargetStudent;

/**
 * excel工具类
* <p>Description: </p>
* <AUTHOR> 
* @date 2018-6-1下午1:39:58
 */
public class ExcelImportUserInfoUtil {
	/**
	 * 读取专硕研究生excel中的数据
	 * @param file
	 * @return
	 * 2018-6-1下午1:39:58
	 * <AUTHOR>
	 */
	public static List<ZyyGraduateTargetStudent> getDataFromExcel(MultipartFile file) {
		// IO流读取文件  
        InputStream input = null; 
        List<ZyyGraduateTargetStudent> list = new ArrayList<ZyyGraduateTargetStudent>();
		try {
			if (file != null){
				input = file.getInputStream();
				
				XSSFWorkbook workbook = new XSSFWorkbook(input);
				XSSFSheet sheet = workbook.getSheetAt(0);
				
				if (sheet != null){
					int title = 6;
					boolean flag = checkExcelFormat(title,sheet);
					if (flag){
						return list;
					}
					
					int totalRows = sheet.getLastRowNum();
					ZyyGraduateTargetStudent domain = null;
					//读取Row,从第2行开始
					for (int i = 1; i <= totalRows; i++) {
						domain = new ZyyGraduateTargetStudent();
						XSSFRow hSSFRow  = sheet.getRow(i);
						String lineNo = getCellValue(hSSFRow.getCell(0)); 
						if (StringUtils.isNotBlank(lineNo)){
							//获取每一行的值
							setDomainData(domain,hSSFRow);
							list.add(domain);
						}
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		} finally{
			if (input != null){
				try {
					input.close();
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
		}
		return list;
	}
	
	/**
	 * 校验一下excel的格式是否正确
	 * @param title
	 * @return
	 */
	private static boolean checkExcelFormat(int title,XSSFSheet sheet) {
		XSSFRow  row = sheet.getRow(0);
		boolean flag = false;
		for (int i = 0; i < title; i++) {
			String value = getCellValue(row.getCell(i)); 
			if (StringUtils.isBlank(value)){
				flag = true;
				break;
			}
		}
		return flag;
	}
	/**
	 * 获取每一行的值  -- 专硕研究生
	 * @param domain
	 * @param hSSFRow
	 * 2018-3-23下午3:21:37
	 * <AUTHOR>
	 */
	private static void setDomainData(ZyyGraduateTargetStudent domain,XSSFRow hSSFRow) {
		//序号	用户ID	姓名	医院ID	专业名称   医院类型
		String lineNo = getCellValue(hSSFRow.getCell(0)); 
		String id = getCellValue(hSSFRow.getCell(1)); 
		String name = getCellValue(hSSFRow.getCell(2)); 
		String hospitalId = getCellValue(hSSFRow.getCell(3)); 
		String baseName = getCellValue(hSSFRow.getCell(4)); 
		String hospitalType = getCellValue(hSSFRow.getCell(5)); 
		
		domain.setLineNo(lineNo);
		domain.setId(Long.valueOf(id));
		domain.setName(name);
		domain.setHospitalId(Long.valueOf(hospitalId));
		domain.setBaseName(baseName);
		domain.setHospitalType(Integer.valueOf(hospitalType));
	}
	
	/** 
     * 根据excel单元格类型获取excel单元格值 
     * @param cell 
     * @return 
     */  
    private static String getCellValue(Cell cell) {  
        String cellvalue = "";  
        if (cell != null) {  
            // 判断当前Cell的Type  
            switch (cell.getCellType()) {  
            // 如果当前Cell的Type为NUMERIC  
            case HSSFCell.CELL_TYPE_NUMERIC: {  
                short format = cell.getCellStyle().getDataFormat();  
                if(format == 14 || format == 31 || format == 57 || format == 58){   //excel中的时间格式  
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");    
                    double value = cell.getNumericCellValue();    
                    Date date = DateUtil.getJavaDate(value);    
                    cellvalue = sdf.format(date);    
                }  
                // 判断当前的cell是否为Date  
                else if (HSSFDateUtil.isCellDateFormatted(cell)) {  //先注释日期类型的转换，在实际测试中发现HSSFDateUtil.isCellDateFormatted(cell)只识别2014/02/02这种格式。  
                    // 如果是Date类型则，取得该Cell的Date值           // 对2014-02-02格式识别不出是日期格式  
                    Date date = cell.getDateCellValue();  
                    DateFormat formater = new SimpleDateFormat("yyyy-MM-dd");  
                    cellvalue= formater.format(date);  
                } else { // 如果是纯数字  
                    // 取得当前Cell的数值  
                    cellvalue = NumberToTextConverter.toText(cell.getNumericCellValue());   
                      
                }  
                break;  
            }  
            // 如果当前Cell的Type为STRIN  
            case HSSFCell.CELL_TYPE_STRING:  
                // 取得当前的Cell字符串  
                cellvalue = cell.getStringCellValue().replaceAll("'", "''");  
                break;  
            case  HSSFCell.CELL_TYPE_BLANK:  
                cellvalue = null;  
                break;  
            // 默认的Cell值  
            default:{  
                cellvalue = " ";  
            }  
            }  
        } else {  
            cellvalue = "";  
        }  
        return cellvalue;  
    }  
}
