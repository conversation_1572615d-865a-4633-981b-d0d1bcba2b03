package com.hys.zyy.manage.util.excel;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.springframework.util.CollectionUtils;

import com.hys.zyy.manage.constants.SymbleConstants;
import com.hys.zyy.manage.model.vo.ZyyStudentExamApplyUserVO;
import com.hys.zyy.manage.util.DateUtil;

/**
 * 考试资格审核 导出学员信息
 * <AUTHOR>
 * @date 2020-4-26下午5:11:10
 */
public class ExportCheckExamStudentInfoUtil {
	/**
	 * 构造导出数据
	 * @param list
	 * @return
	 */
	public static List<Map<String,String>> createExportData(List<ZyyStudentExamApplyUserVO> list,Map<String, String> exportColMap) {
		List<Map<String,String>> exportData = new ArrayList<Map<String,String>>();
		if (CollectionUtils.isEmpty(list)){
			return exportData;
		}
		if (exportColMap == null){
			return exportData;
		}
		Map<String,String> row = null;
		StringBuilder bd = null;
		int size = list.size();
		for (int i = 0; i < size; i++) {
			row = new LinkedHashMap<String, String>();
			ZyyStudentExamApplyUserVO item = list.get(i);
			createRowData(row, item,bd,exportColMap);
			exportData.add(row);
		}
		return exportData;
	}
	
	public static Map<String, String> createExportColMap(String exportExtendCol) {
		if (StringUtils.isBlank(exportExtendCol)){
			return null;
		}
		Map<String,String> exportColMap = new HashMap<String, String>();
		String[] exportExtendColArray = exportExtendCol.split(SymbleConstants.COMMA);
		int len = exportExtendColArray.length;
		for (int i = 0; i < len; i++) {
			String exportItem = exportExtendColArray[i];
			// 姓名,性别,证件类型,证件号码,手机号,培训年级,学员类型,培训年限,出生日期,民族
			if ("realName".equals(exportItem)){
				exportColMap.put(exportItem, "姓名");
			} else if ("sex".equals(exportItem)){
				exportColMap.put(exportItem, "性别");
			} else if ("certificateType".equals(exportItem)){
				exportColMap.put(exportItem, "证件类型");
			} else if ("certificateNo".equals(exportItem)){
				exportColMap.put(exportItem, "证件号码");
			} else if ("mobile".equals(exportItem)){
				exportColMap.put(exportItem, "手机号");
			} else if ("year".equals(exportItem)){
				exportColMap.put(exportItem, "培训年级");
			} else if ("residencySource".equals(exportItem)){
				exportColMap.put(exportItem, "人员类型");
			} else if ("schoolSystem".equals(exportItem)){
				exportColMap.put(exportItem, "培训年限");
			} else if ("birthday".equals(exportItem)){
				exportColMap.put(exportItem, "出生日期");
			} else if ("nation".equals(exportItem)){
				exportColMap.put(exportItem, "民族");
			} else if ("address".equals(exportItem)){
				//联系地址,邮政编码,人员类型,所在住培基地,是否为军队人员,本次考试是否为初次报考,医师资格取得时间,医师资格证书编号,证书类别,最高学历,
				exportColMap.put(exportItem, "联系地址");
			} else if ("addressPostCode".equals(exportItem)){
				exportColMap.put(exportItem, "邮政编码");
			} else if ("doctorUserType".equals(exportItem)){
				exportColMap.put(exportItem, "人员类型");
			} else if ("orgName".equals(exportItem)){
				exportColMap.put(exportItem, "所在住培基地");
			} else if ("armyType".equals(exportItem)){
				exportColMap.put(exportItem, "是否为军队人员");
			} else if ("firstApplyExam".equals(exportItem)){
				exportColMap.put(exportItem, "本次考试是否为初次报考");
			} else if ("getPracticeDate".equals(exportItem)){
				exportColMap.put(exportItem, "医师资格取得时间");
			} else if ("practiceCertificateNumber".equals(exportItem)){
				exportColMap.put(exportItem, "医师资格证书编号");
			} else if ("physiciansQualificationType".equals(exportItem)){
				exportColMap.put(exportItem, "证书类别");
			} else if ("highestRecordSchool".equals(exportItem)){
				exportColMap.put(exportItem, "最高学历");
			} else if ("highestDegree".equals(exportItem)){
				//最高学位,学位类型,毕业学校,毕业时间,毕业专业,毕业专业备注,毕业证书编号,是否为西部支援住院医师,单位名称（全称）,单位级别
				exportColMap.put(exportItem, "最高学位");
			}  else if ("highestDegreeType".equals(exportItem)){
				exportColMap.put(exportItem, "学位类型");
			}  else if ("highestGraduateSchool".equals(exportItem)){
				exportColMap.put(exportItem, "毕业学校");
			}  else if ("highestRecordSchool".equals(exportItem)){
				exportColMap.put(exportItem, "最高学历");
			}  else if ("graduationDate".equals(exportItem)){
				exportColMap.put(exportItem, "毕业时间");
			}  else if ("highestRecordProf".equals(exportItem)){
				exportColMap.put(exportItem, "毕业专业");
			}  else if ("graduateProfRemark".equals(exportItem)){
				exportColMap.put(exportItem, "毕业专业备注");
			}  else if ("graduationCode".equals(exportItem)){
				exportColMap.put(exportItem, "毕业证书编号");
			}  else if ("westSupportDoctor".equals(exportItem)){
				exportColMap.put(exportItem, "是否为西部支援住院医师");
			}  else if ("unitName".equals(exportItem)){
				exportColMap.put(exportItem, "单位名称（全称）");
			}  else if ("unitLevel".equals(exportItem)){
				exportColMap.put(exportItem, "单位级别");
			}  else if ("workDate".equals(exportItem)){
				//参加工作时间,工作年限,进入培训基地时间,培训专业,培训专业备注,是否在协同单位培训,所在协同单位,执业地点,执业范围,是否为补考学员
				exportColMap.put(exportItem, "参加工作时间");
			}  else if ("workYear".equals(exportItem)){
				exportColMap.put(exportItem, "工作年限");
			}  else if ("workStartDate".equals(exportItem)){
				exportColMap.put(exportItem, "进入培训基地时间");
			}  else if ("baseName".equals(exportItem)){
				exportColMap.put(exportItem, "培训专业");
			}  else if ("baseRemark".equals(exportItem)){
				exportColMap.put(exportItem, "培训专业备注");
			}  else if ("cooperatUnit".equals(exportItem)){
				exportColMap.put(exportItem, "是否在协同单位培训");
			}  else if ("cooperatUnitName".equals(exportItem)){
				exportColMap.put(exportItem, "所在协同单位");
			}  else if ("practicePlace".equals(exportItem)){
				exportColMap.put(exportItem, "执业地点");
			}  else if ("scopeCertificate".equals(exportItem)){
				exportColMap.put(exportItem, "执业范围");
			}  else if ("makeupExam".equals(exportItem)){
				exportColMap.put(exportItem, "是否为补考学员");
			}                  
		}
		return exportColMap;
	}

	private static void createRowData(Map<String, String> row,
			ZyyStudentExamApplyUserVO item, StringBuilder bd,Map<String, String> exportColMap) {
		
		// 姓名,性别,证件类型,证件号码,手机号,培训年级,学员类型,培训年限,出生日期,民族
		int i = 0;
		if (exportColMap.containsKey("realName")){
			i++;
			row.put(""+i, CommonExcelExportUtil.toStr(item.getRealName()));
		} 
		if (exportColMap.containsKey("sex")){
			i++;
			Integer sex = item.getSex();
			String sexStr = "";
			if (sex != null){
				if (sex == 1){
					sexStr = "男";
				} else {
					sexStr = "女";
				}
			}
			row.put(""+i, sexStr);
		}
		if (exportColMap.containsKey("certificateType")){
			i++;
			row.put(""+i, CommonExcelExportUtil.getCertificateType(item.getCertificateType()));
		}
		if (exportColMap.containsKey("certificateNo")){
			i++;
			row.put(""+i, CommonExcelExportUtil.toAppendTab(bd, item.getCertificateNo()));
		}
		if (exportColMap.containsKey("mobile")){
			i++;
			row.put(""+i, CommonExcelExportUtil.toAppendTab(bd, item.getMobile()));
		}
		if (exportColMap.containsKey("year")){
			i++;
			row.put(""+i, CommonExcelExportUtil.toStr(item.getYear()));
		}
		if (exportColMap.containsKey("residencySource")){
			i++;
			row.put(""+i, CommonExcelExportUtil.getResidencySource(item.getResidencySource()));
		}
		if (exportColMap.containsKey("schoolSystem")){
			i++;
			String schoolSystemStr = "";
			Integer schoolSystem = item.getSchoolSystem();
			if (schoolSystem != null){
				if (schoolSystem == 1){
					schoolSystemStr = "一年制";
				} else if (schoolSystem == 2){
					schoolSystemStr = "两年制";
				} else if (schoolSystem == 3){
					schoolSystemStr = "三年制";
				}
			}
			row.put(""+i, schoolSystemStr);
		}
		if (exportColMap.containsKey("birthday")){
			i++;
			String birthdayStr = "";
			Date birthday = item.getBirthday();
			if (birthday != null){
				birthdayStr = DateUtil.format(birthday, DateUtil.FORMAT_SHORT);
			}
			row.put(""+i, birthdayStr);
		}
		if (exportColMap.containsKey("nation")){
			i++;
			row.put(""+i, CommonExcelExportUtil.toStr(item.getNation()));
		}
		//联系地址,邮政编码,人员类型,所在住培基地,是否为军队人员,本次考试是否为初次报考,医师资格取得时间,医师资格证书编号,证书类别,最高学历,
		if (exportColMap.containsKey("address")){
			i++;
			row.put(""+i, CommonExcelExportUtil.toStr(item.getAddress()));
		}
		if (exportColMap.containsKey("addressPostCode")){
			i++;
			row.put(""+i, CommonExcelExportUtil.toStr(item.getAddressPostCode()));
		}
		if (exportColMap.containsKey("doctorUserType")){
			i++;
			row.put(""+i, CommonExcelExportUtil.getDoctorUserType(item.getDoctorUserType()));
		}
		if (exportColMap.containsKey("orgName")){
			i++;
			row.put(""+i, CommonExcelExportUtil.toStr(item.getOrgName()));
		}
		if (exportColMap.containsKey("armyType")){
			i++;
			row.put(""+i, CommonExcelExportUtil.getArmyType(item.getArmyType()));
		}
		if (exportColMap.containsKey("firstApplyExam")){
			i++;
			row.put(""+i, CommonExcelExportUtil.getYesNoStr(item.getFirstApplyExam()));
		}
		if (exportColMap.containsKey("getPracticeDate")){
			i++;
			String getPracticeDateStr = "";
			Date getPracticeDate = item.getGetPracticeDate();
			if (getPracticeDate != null){
				getPracticeDateStr = DateUtil.format(getPracticeDate, DateUtil.FORMAT_SHORT);
			}
			row.put(""+i, getPracticeDateStr);
		}
		if (exportColMap.containsKey("practiceCertificateNumber")){
			i++;
			row.put(""+i, CommonExcelExportUtil.toStr(item.getPracticeCertificateNumber()));
		}
		if (exportColMap.containsKey("physiciansQualificationType")){
			i++;
			row.put(""+i, CommonExcelExportUtil.getPhysiciansQualificationType(item.getPhysiciansQualificationType()));
		}
		if (exportColMap.containsKey("highestRecordSchool")){
			i++;
			row.put(""+i, CommonExcelExportUtil.toStr(item.getHighestRecordSchool()));
		}
		//最高学位,学位类型,毕业学校,毕业时间,毕业专业,毕业专业备注,毕业证书编号,是否为西部支援住院医师,单位名称（全称）,单位级别
		if (exportColMap.containsKey("highestDegree")){
			i++;
			row.put(""+i, CommonExcelExportUtil.toStr(item.getHighestDegree()));
		}
		if (exportColMap.containsKey("highestDegreeType")){
			i++;
			row.put(""+i, item.getHighestDegreeType()==null?"":(item.getHighestDegreeType()==1?"专业型":"科学型"));
		}
		if (exportColMap.containsKey("highestGraduateSchool")){
			i++;
			row.put(""+i, CommonExcelExportUtil.toStr(item.getHighestGraduateSchool()));
		}
		if (exportColMap.containsKey("graduationDate")){
			i++;
			String graduationDateStr = "";
			Date graduationDate = item.getGraduationDate();
			if (graduationDate != null){
				graduationDateStr = DateUtil.format(graduationDate, DateUtil.FORMAT_SHORT);
			}
			row.put(""+i, graduationDateStr);
		}
		if (exportColMap.containsKey("highestRecordProf")){
			i++;
			row.put(""+i, CommonExcelExportUtil.toStr(item.getHighestRecordProf()));
		}
		if (exportColMap.containsKey("graduateProfRemark")){
			i++;
			row.put(""+i, CommonExcelExportUtil.toStr(item.getGraduateProfRemark()));
		}
		if (exportColMap.containsKey("graduationCode")){
			i++;
			row.put(""+i, CommonExcelExportUtil.toStr(item.getGraduationCode()));
		}
		if (exportColMap.containsKey("westSupportDoctor")){
			i++;
			row.put(""+i, CommonExcelExportUtil.getYesNoStr(item.getWestSupportDoctor()));
		}
		if (exportColMap.containsKey("unitName")){
			i++;
			row.put(""+i, "");
		}
		if (exportColMap.containsKey("unitLevel")){
			i++;
			row.put(""+i, "");
		}
		//参加工作时间,工作年限,进入培训基地时间,培训专业,培训专业备注,是否在协同单位培训,所在协同单位,执业地点,执业范围,是否为补考学员
		if (exportColMap.containsKey("workDate")){
			i++;
			row.put(""+i, CommonExcelExportUtil.toStr(item.getWorkDate()));
		}
		if (exportColMap.containsKey("workYear")){
			i++;
			row.put(""+i, CommonExcelExportUtil.toStr(item.getWorkYear()));
		}
		if (exportColMap.containsKey("workStartDate")){
			i++;
			String workStartDateStr = "";
			Date workStartDate = item.getWorkStartDate();
			if (workStartDate != null){
				workStartDateStr = DateUtil.format(workStartDate, DateUtil.FORMAT_SHORT);
			}
			row.put(""+i, workStartDateStr);
		}
		if (exportColMap.containsKey("baseName")){
			i++;
			row.put(""+i, CommonExcelExportUtil.toStr(item.getBaseName()));
		}
		if (exportColMap.containsKey("baseRemark")){
			i++;
			row.put(""+i, CommonExcelExportUtil.toStr(item.getBaseRemark()));
		}
		
		if (exportColMap.containsKey("cooperatUnit")){
			i++;
			row.put(""+i, CommonExcelExportUtil.getYesNoStr(item.getCooperatUnit()));
		}
		if (exportColMap.containsKey("cooperatUnitName")){
			i++;
			row.put(""+i, CommonExcelExportUtil.toStr(item.getCooperatUnitName()));
		}
		if (exportColMap.containsKey("practicePlace")){
			i++;
			row.put(""+i, CommonExcelExportUtil.toStr(item.getPracticePlace()));
		}
		if (exportColMap.containsKey("scopeCertificate")){
			i++;
			row.put(""+i, CommonExcelExportUtil.toStr(item.getScopeCertificate()));
		}
		if (exportColMap.containsKey("makeupExam")){
			i++;
			row.put(""+i, CommonExcelExportUtil.getYesNoStr(item.getMakeupExam()));
		}
		
	}
	
	//导出excel表头
	public static String createExportDataHead(String exportColmns,Map<String,String> nameMap){
		StringBuilder bd = new StringBuilder();
		// 姓名,性别,证件类型,证件号码,手机号,培训年级,学员类型,培训年限,出生日期,民族
		if (StringUtils.isBlank(exportColmns)){
			return "";
		}
		String[] exportArray = exportColmns.split(SymbleConstants.COMMA);
		int len = exportArray.length;
		for (int i = 0; i < len; i++) {
			String item = exportArray[i];
			if (StringUtils.isNotBlank(item) && nameMap.containsKey(item)){
				bd.append(nameMap.get(item));
				bd.append(SymbleConstants.COMMA);
			}
		}
		String result = "";
		int len2 = bd.length();
		if (len2 > 0){
			result = bd.substring(0, len2 - 1);
		}
		
		return result;
	}
	
}
