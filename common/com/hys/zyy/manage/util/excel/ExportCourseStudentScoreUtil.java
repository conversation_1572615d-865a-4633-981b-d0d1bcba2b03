package com.hys.zyy.manage.util.excel;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.springframework.util.CollectionUtils;

import com.hys.beq.entities.vo.exam.Examinee;
/**
 * 导出考试科目学生的成绩
 * <AUTHOR>
 *
 */
public class ExportCourseStudentScoreUtil {
	/**
	 * 构造导出数据
	 * @param list
	 * @return
	 */
	public static List<Map<String,String>> createExportData(List<Examinee> list) {
		List<Map<String,String>> exportData = new ArrayList<Map<String,String>>();
		if (CollectionUtils.isEmpty(list)){
			return exportData;
		}
	    Map<String,String> row = null;
	    StringBuilder bd = null;
	    int size = list.size();
	    for (int i = 0; i < size; i++) {
	    	row = new LinkedHashMap<String, String>();
	    	Examinee item = list.get(i);
	    	createRowData(row, item,bd);
		    exportData.add(row);
	    }
		return exportData;
	}
	
	/**
	 * 构造导出数据   -- 查看成绩
	 * @param list
	 * @return
	 */
	public static List<Map<String,String>> createExportDataForView(List<Examinee> list) {
		List<Map<String,String>> exportData = new ArrayList<Map<String,String>>();
		if (CollectionUtils.isEmpty(list)){
			return exportData;
		}
	    Map<String,String> row = null;
	    StringBuilder bd = null;
	    int size = list.size();
	    for (int i = 0; i < size; i++) {
	    	row = new LinkedHashMap<String, String>();
	    	Examinee item = list.get(i);
	    	createRowDataForView(row, item,bd);
		    exportData.add(row);
	    }
		return exportData;
	}
	
	private static String toStr(String str){
		return str==null?"":str ;
	}
	
	private static void createRowData(Map<String, String> row,
			Examinee examinee, StringBuilder bd) {
		//{"考试名称", "考试科目", "考试时间", "姓名","性别", "证件号", "是否进入考试","是否提交试卷","考试用时","主观成绩","客观成绩"}
		row.put("1", toStr(examinee.getExamName()));
		row.put("2", toStr(examinee.getExamCourseName()));
		row.put("3", toStr(examinee.getExamStartDateStr()));
		row.put("4", toStr(examinee.getStudentName()));
		row.put("5", toStr(examinee.getSex()));
		
		row.put("6", toAppendTab(bd, examinee.getCertificateNo()));
		row.put("7", examinee.getStatus() != null && examinee.getStatus() >= 1 ? "是" : "否");
		row.put("8", examinee.getStatus() != null && examinee.getStatus() == 2 ? "是" : "否");
		row.put("9", examinee.getExamUseTime()+"分");
		row.put("10", toStr(examinee.getSubjectiveScore()));
		
		row.put("11", toStr(examinee.getObjectiveScore()));
	}
	
	// 构造成绩  -- 查看成绩
	private static void createRowDataForView(Map<String, String> row,
			Examinee examinee, StringBuilder bd) {
		//{"考试名称", "考试科目", "考试时间", "姓名","性别", "证件号", "是否进入考试","是否提交试卷","考试用时","主观成绩","客观成绩","总成绩"}
		row.put("1", toStr(examinee.getExamName()));
		row.put("2", toStr(examinee.getExamCourseName()));
		row.put("3", toStr(examinee.getExamStartDateStr()));
		row.put("4", toStr(examinee.getStudentName()));
		row.put("5", toStr(examinee.getSex()));
		
		row.put("6", toAppendTab(bd, examinee.getCertificateNo()));
		row.put("7", examinee.getStatus() != null && examinee.getStatus() >= 1 ? "是" : "否");
		row.put("8", examinee.getStatus() != null && examinee.getStatus() == 2 ? "是" : "否");
		row.put("9", examinee.getExamUseTime()+"分");
		row.put("10", toStr(examinee.getSubjectiveScore()));
		
		row.put("11", toStr(examinee.getObjectiveScore()));
		row.put("12", toStr(examinee.getAnswerEndTimeStr()));
	}
	
	/**
	 * 特殊字符串数字添加制表符
	 * @param bd
	 * @param str
	 * @return
	 * <AUTHOR>
	 * @date 2018-11-8下午6:19:17
	 */
	private static String toAppendTab(StringBuilder bd,String str){
		if (str == null){
			return "";
		}
		bd = new StringBuilder();
		bd.append("\t");
		bd.append(str);
		return bd.toString();
	}
	
	//导出excel表头
	public static String createExportDataHead(){
		StringBuilder bd = new StringBuilder();
		bd.append("考试名称,考试科目,考试时间,姓名,性别,证件号,是否进入考试,是否提交试卷,考试用时,主观成绩,客观成绩");
		return bd.toString();
	}
	
	//导出excel表头
	public static String createExportDataHeadForView(){
		StringBuilder bd = new StringBuilder();
		bd.append("考试名称,考试科目,考试时间,姓名,性别,证件号,是否进入考试,是否提交试卷,考试用时,主观成绩,客观成绩,总成绩");
		return bd.toString();
	}
}
