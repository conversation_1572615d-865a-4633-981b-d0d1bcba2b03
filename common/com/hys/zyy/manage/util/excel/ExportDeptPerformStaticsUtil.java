package com.hys.zyy.manage.util.excel;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.util.CollectionUtils;

import com.hys.zyy.manage.model.ZyyActivityTypeVO;
import com.hys.zyy.manage.model.vo.ZyyTeacherPerformanceVO;
import com.hys.zyy.manage.query.ZyyTeacherPerformanceQuery;
import com.hys.zyy.manage.util.RegionUtil;
import com.hys.zyy.manage.util.StringUtils;

/**
 * 带教绩效统计导出
 * <AUTHOR>
 * @date 2019-1-16下午2:44:18
 */
public class ExportDeptPerformStaticsUtil {
	//定义表头 
    private static String[] invoiceExcelHeader = {"序号","科室名称","带教老师人数","入科总人次","实际组织次数/入科教育次数","入科教育出勤率",
    	"已入科人次/入科总人次","指定带教完成率","出科人次/已轮转人次","合格出科率","组织次数","出勤率（已结束的教学活动）","登记手册审核率","病例审核率","学员评价平均分",
    	"带教评价平均分","专业基地评价平均分","培训基地评价平均分","我应该评价/已评价","出科考试数量","出科考试出勤率","出科考试合格率","出科考试平均分"};  
    //sheet名称
    private static String sheetName = "科室绩效统计";
    /**
     * 导出
     * @param list
     * @return
     * 2017-12-28上午9:36:13
     * <AUTHOR>
     * @param colspanMap 
     * @param showItemMap 
     */
    public static HSSFWorkbook  createExcel(List<ZyyTeacherPerformanceVO> list, Map<String, String> showItemMap,
    		Map<String, Integer> colspanMap,ZyyTeacherPerformanceQuery query) {    
        //这里需要说明一个问题：如果是 Offices 2007以前的Excel版本，new的对象是：**HSSFWorkbook** ，Offices 2007以后的Excel版本new的对象才是XSSFWorkbook
    	HSSFWorkbook  wb = new HSSFWorkbook();  
		
    	//生成一个工作表
		Sheet sheet = null;
		//生成单元格的样式style
		HSSFCellStyle  style = wb.createCellStyle();    
		style.setAlignment(HSSFCellStyle.ALIGN_CENTER); // 水平居中  
		style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER); // 垂直居中
		//生成第一行
		Row row = null;    
		
		//生成单元格的样式style
		HSSFCellStyle  styleHead = wb.createCellStyle();    
		styleHead.setAlignment(HSSFCellStyle.ALIGN_CENTER); // 水平居中  
		styleHead.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER); // 垂直居中
		styleHead.setBorderBottom(HSSFCellStyle.BORDER_THIN); //下边框
		styleHead.setBorderLeft(HSSFCellStyle.BORDER_THIN);//左边框
		styleHead.setBorderTop(HSSFCellStyle.BORDER_THIN);//上边框
		styleHead.setBorderRight(HSSFCellStyle.BORDER_THIN);//右边框
		Font font = wb.createFont(); 
		font.setBoldweight(Font.BOLDWEIGHT_BOLD); // 粗体
		styleHead.setFont(font);
		
		if (!CollectionUtils.isEmpty(list)){
			int sheetIndex = 0;
			int size = list.size();
			for (int i = 0; i < size; i++) {    
				// 10000 条数据一个sheet
				if (i % 10000 == 0 ){
					sheet = wb.createSheet(sheetName+sheetIndex);
					row = createHeaderRow(sheet,styleHead,showItemMap,colspanMap,query);
					addMergedRegion(sheet,colspanMap);
					sheetIndex++;
				}
				
				//得到当前行数的下一行（row.getRowNum()：得到当前行数）
				int start = row.getRowNum()+1;
				row = sheet.createRow(start);  
				ZyyTeacherPerformanceVO vo = list.get(i);    
				setRowCellValue(vo,row,style,i,showItemMap);
			}    
		}else{
			sheet = wb.createSheet(sheetName+0);
			row = sheet.createRow((int) 0);  
			createHeaderRow(sheet,styleHead,showItemMap,colspanMap,query);
			addMergedRegion(sheet,colspanMap);
		}
        return wb;    
    }
    
    /**
     * 合并单元格  （起始行，结束行，起始列，结束列） 
     * @param start
     * @param end
     * @param sheet
     * 2017-12-28上午11:22:45
     * <AUTHOR>
     * @param colspanMap 
     */
    private static void addMergedRegion(Sheet sheet, Map<String, Integer> colspanMap) {
    	//列合并
    	for (int i = 0; i < 2; i++) {
    		sheet.addMergedRegion(new CellRangeAddress(0, 1, i, i));
		}
    	//行合并
    	Integer personColspan = colspanMap.get("personColspan");//人员信息
		Integer enterLeaveColspan = colspanMap.get("enterLeaveColspan");//入科教育信息
		Integer activityColspan = colspanMap.get("activityColspan");//教学活动
		Integer evaluateColspan = colspanMap.get("evaluateColspan");//评价
		Integer handCaseColspan = colspanMap.get("handCaseColspan");//手册病例
		Integer leaveColspan = colspanMap.get("leaveColspan");//出科考试
		
		Integer zero = Integer.valueOf(0);
		int startColNum=2;
		int endColNum=0;
		
		//判断合并的列   人员信息
		if (!zero.equals(personColspan)){
			endColNum+=(startColNum+personColspan-1);
			sheet.addMergedRegion(new CellRangeAddress(0, 0, startColNum, endColNum));
			startColNum += personColspan;
		}
		//判断合并的列   入科教育
		if (!zero.equals(enterLeaveColspan)){
			endColNum =(startColNum+enterLeaveColspan-1);
			sheet.addMergedRegion(new CellRangeAddress(0, 0, startColNum, endColNum));
			startColNum += enterLeaveColspan;
		}
		//判断合并的列   教学活动
		if (!zero.equals(activityColspan)){
			endColNum =(startColNum+activityColspan-1);
			sheet.addMergedRegion(new CellRangeAddress(0, 0, startColNum, endColNum));
			startColNum+=activityColspan;
		}
		if (!zero.equals(handCaseColspan)){
			endColNum =(startColNum+handCaseColspan-1);
			sheet.addMergedRegion(new CellRangeAddress(0, 0, startColNum, endColNum));
			startColNum+=handCaseColspan;
		}
		if (!zero.equals(evaluateColspan)){
			endColNum =(startColNum+evaluateColspan-1);
			sheet.addMergedRegion(new CellRangeAddress(0, 0, startColNum, endColNum));
			startColNum+=evaluateColspan;
		}
		if (!zero.equals(leaveColspan)){
			endColNum =(startColNum+leaveColspan-1);
			sheet.addMergedRegion(new CellRangeAddress(0, 0, startColNum, endColNum));
			startColNum+=leaveColspan;
		}
	}
	
	/**
	 * 赋值
	 * @param vo
	 * @param row
	 * @param style
	 * @param index
	 * <AUTHOR>
	 * @param showItemMap 
	 * @date 2018-9-14下午6:09:58
	 */
    private static void setRowCellValue(ZyyTeacherPerformanceVO vo,Row row,HSSFCellStyle  style,int index, Map<String, String> showItemMap) {
    	//赋值
    	Cell cell = null;
    	cell = row.createCell(0);
    	cell.setCellValue(index+1);  
    	cell.setCellStyle(style);
    	
    	cell = row.createCell(1);
    	cell.setCellValue(vo.getDeptName() == null ?"":vo.getDeptName()); 
    	cell.setCellStyle(style);
    	
    	int tmpIndex=2;
    	if (showItemMap.containsKey("1")){
    		cell = row.createCell(tmpIndex++);
    		cell.setCellValue(vo.getTeacherTotal()  == null ?"":vo.getTeacherTotal()); 
    		cell.setCellStyle(style);
    	}
    	if (showItemMap.containsKey("2")){
    		cell = row.createCell(tmpIndex++);
    		cell.setCellValue(vo.getStudentTotal()  == null ?"":vo.getStudentTotal()); 
    		cell.setCellStyle(style);
    	}
    	if (showItemMap.containsKey("7")){
    		cell = row.createCell(tmpIndex++);
    		cell.setCellValue(splicingStr(vo.getHoldTotal(),vo.getNeedHoldTotal())); 
    		cell.setCellStyle(style);
    	}
    	if (showItemMap.containsKey("8")){
    		cell = row.createCell(tmpIndex++);
    		cell.setCellValue(vo.getAttendEduRate() == null ?"":vo.getAttendEduRate()); 
    		cell.setCellStyle(style);
    	}
    	if (showItemMap.containsKey("9")){
    		cell = row.createCell(tmpIndex++);
    		cell.setCellValue(splicingStr(vo.getEnterDepartNum(),vo.getStudentTotal())); 
    		cell.setCellStyle(style);
    	}
    	if (showItemMap.containsKey("10")){
    		cell = row.createCell(tmpIndex++);
    		cell.setCellValue(vo.getHasTeacherRate()==null ?"":vo.getHasTeacherRate()); 
    		cell.setCellStyle(style);
    	}
    	if (showItemMap.containsKey("11")){
    		cell = row.createCell(tmpIndex++);
    		cell.setCellValue(splicingStr(vo.getQualifyTotal(),vo.getStudentTotal())); 
    		cell.setCellStyle(style);
    	}
    	if (showItemMap.containsKey("12")){
    		cell = row.createCell(tmpIndex++);
    		cell.setCellValue(vo.getLeaveRate() == null ?"":vo.getLeaveRate()); 
    		cell.setCellStyle(style);
    	}
    	
    	//具体的教学活动组织次数
    	List<ZyyActivityTypeVO> activityList = vo.getActivityTypeList();
    	if (!CollectionUtils.isEmpty(activityList)){
    		for (ZyyActivityTypeVO item : activityList) {
				if (item != null){
					cell = row.createCell(tmpIndex++);
		    		cell.setCellValue(item.getActivityNum() == null ?"":item.getActivityNum().toString()); 
		    		cell.setCellStyle(style);
				}
			}
    	}
    	
    	if (showItemMap.containsKey("3")){
    		cell = row.createCell(tmpIndex++);
    		cell.setCellValue(vo.getActivityTotal()== null ?"":vo.getActivityTotal()); 
    		cell.setCellStyle(style);
    	}
    	if (showItemMap.containsKey("4")){
    		cell = row.createCell(tmpIndex++);
    		cell.setCellValue(vo.getAttendActivityRate()  == null ?"":vo.getAttendActivityRate()); 
    		cell.setCellStyle(style);
    	}
    	if (showItemMap.containsKey("5")){
    		cell = row.createCell(tmpIndex++);
    		cell.setCellValue(vo.getHandbookRate()  == null ?"":vo.getHandbookRate()); 
    		cell.setCellStyle(style);
    	}
    	if (showItemMap.containsKey("6")){
    		cell = row.createCell(tmpIndex++);
    		cell.setCellValue(vo.getCaseRate()== null ?"":vo.getCaseRate()); 
    		cell.setCellStyle(style);
    	}
    	if (showItemMap.containsKey("13")){
    		cell = row.createCell(tmpIndex++);
    		cell.setCellValue(vo.getStudentEvalScore()== null ?"":vo.getStudentEvalScore()); 
    		cell.setCellStyle(style);
    	}
    	if (showItemMap.containsKey("14")){
    		cell = row.createCell(tmpIndex++);
    		cell.setCellValue(vo.getTeacherEvalScore()== null ?"":vo.getTeacherEvalScore()); 
    		cell.setCellStyle(style);
    	}
    	if (showItemMap.containsKey("15")){
    		cell = row.createCell(tmpIndex++);
    		cell.setCellValue(vo.getBaseEvalScore()== null ?"":vo.getBaseEvalScore()); 
    		cell.setCellStyle(style);
    	}
    	if (showItemMap.containsKey("16")){
    		cell = row.createCell(tmpIndex++);
    		cell.setCellValue(vo.getHospitalEvalScore()== null ?"":vo.getHospitalEvalScore()); 
    		cell.setCellStyle(style);
    	}
    	if (showItemMap.containsKey("21")){
    		cell = row.createCell(tmpIndex++);
    		cell.setCellValue(vo.getNeedDoneEval()== null ?"":vo.getNeedDoneEval()); 
    		cell.setCellStyle(style);
    	}
    	if (showItemMap.containsKey("17")){
    		cell = row.createCell(tmpIndex++);
    		cell.setCellValue(vo.getLeaveExamNum()== null ?"":vo.getLeaveExamNum()); 
    		cell.setCellStyle(style);
    	}
    	if (showItemMap.containsKey("18")){
    		cell = row.createCell(tmpIndex++);
    		cell.setCellValue(vo.getLeaveExamAttendRate()== null ?"":vo.getLeaveExamAttendRate()); 
    		cell.setCellStyle(style);
    	}
    	if (showItemMap.containsKey("19")){
    		cell = row.createCell(tmpIndex++);
    		cell.setCellValue(vo.getLeaveExamRate()== null ?"":vo.getLeaveExamRate()); 
    		cell.setCellStyle(style);
    	}
    	if (showItemMap.containsKey("20")){
    		cell = row.createCell(tmpIndex++);
    		cell.setCellValue(vo.getLeaveAvgScore()== null ?"":vo.getLeaveAvgScore()); 
    		cell.setCellStyle(style);
    	}
	}
    
	/**
	 * 生成标题行
	 * @param sheet
	 * @param style
	 * @param showItemMap
	 * @param colspanMap
	 * @return
	 */
	private static Row createHeaderRow(Sheet sheet,HSSFCellStyle  style, Map<String, String> showItemMap, 
			Map<String, Integer> colspanMap,ZyyTeacherPerformanceQuery query) {
		String[] firstRowHead = {"人员统计","出入科统计","教学活动统计","登记手册、病历统计","评价统计","出科考试统计"};
		//第一行
		Row row = sheet.createRow((int) 0);
		row.setHeight((short) 600);
		Cell cell = null;
		for (int i = 0; i < 2; i++) {
			cell = row.createCell(i);    
	        cell.setCellValue(invoiceExcelHeader[i]);  
	        cell.setCellStyle(style);
		}
		
		Integer personColspan = colspanMap.get("personColspan");//人员信息
		Integer enterLeaveColspan = colspanMap.get("enterLeaveColspan");//入科教育信息
		Integer activityColspan = colspanMap.get("activityColspan");//教学活动
		Integer evaluateColspan = colspanMap.get("evaluateColspan");//评价
		Integer handCaseColspan = colspanMap.get("handCaseColspan");//手册病例
		Integer leaveColspan = colspanMap.get("leaveColspan");//出科考试

		int nowColumnIndex = 2;
		Integer zero =Integer.valueOf(0);
		//判断合并的列   人员信息
		if (!zero.equals(personColspan)){
			cell = row.createCell(nowColumnIndex);
			nowColumnIndex += personColspan;
			cell.setCellValue(firstRowHead[0]);  
			cell.setCellStyle(style);
		}
		//判断合并的列   入科教育
		if (!zero.equals(enterLeaveColspan)){
			cell = row.createCell(nowColumnIndex);
			nowColumnIndex += enterLeaveColspan;
			cell.setCellValue(firstRowHead[1]);  
			cell.setCellStyle(style);
		}
		//判断合并的列   教学活动
		if (!zero.equals(activityColspan)){
			cell = row.createCell(nowColumnIndex);    
			nowColumnIndex += activityColspan;
			cell.setCellValue(firstRowHead[2]);  
			cell.setCellStyle(style);
		}
		//判断合并的列   登记手册病例
		if (!zero.equals(handCaseColspan)){
			cell = row.createCell(nowColumnIndex);    
			nowColumnIndex += handCaseColspan;
			cell.setCellValue(firstRowHead[3]);  
			cell.setCellStyle(style);
		}
		//判断合并的列   评价统计
		if (!zero.equals(evaluateColspan)){
			cell = row.createCell(nowColumnIndex);    
			nowColumnIndex += evaluateColspan;
			cell.setCellValue(firstRowHead[4]);  
			cell.setCellStyle(style);
		}
		//判断合并的列   出科考试统计
		if (!zero.equals(leaveColspan)){
			cell = row.createCell(nowColumnIndex);    
			nowColumnIndex += leaveColspan;
			cell.setCellValue(firstRowHead[5]);  
			cell.setCellStyle(style);
		}
		/** 补全最后一个单元格的边框 */
		cell = row.createCell(nowColumnIndex-1);    
		cell.setCellValue("");  
		cell.setCellStyle(style);

        //第二行
        row = sheet.createRow((int) 1);
        row.setHeight((short) 600);
		//获取头信息
		List<String> headerList = getHeaderTitleList(query);
		
		List<String> showHeaderList = checkExistItem2(headerList, showItemMap);
        for (int i = 0; i < showHeaderList.size(); i++) {
    		//获取每一个单元格
    		cell = row.createCell(i);    
    		//给单元格赋值
    		cell.setCellValue(showHeaderList.get(i));  
    		cell.setCellStyle(style);
        }  
		return row;
	}
	
	/**
	 * 判断是否显示这列
	 * @param i
	 * @param showItemMap
	 * @return
	 * <AUTHOR>
	 * @date 2019-1-17下午5:52:17
	 */
	private static List<String> checkExistItem2(List<String> headerList, Map<String, String> showItemMap) {
		
		if(!showItemMap.containsKey("1"))  headerList.remove("带教老师人数");
		if(!showItemMap.containsKey("2"))  headerList.remove("入科总人次");
		if(!showItemMap.containsKey("7"))  headerList.remove("实际组织次数/入科教育次数");
		if(!showItemMap.containsKey("8"))  headerList.remove("入科教育出勤率");
		if(!showItemMap.containsKey("9"))  headerList.remove("已入科人次/入科总人次");
		if(!showItemMap.containsKey("10")) headerList.remove("指定带教完成率");
		if(!showItemMap.containsKey("11")) headerList.remove("出科人次/已轮转人次");
		if(!showItemMap.containsKey("12")) headerList.remove("合格出科率");
		if(!showItemMap.containsKey("3"))  headerList.remove("组织次数");
		if(!showItemMap.containsKey("4"))  headerList.remove("出勤率（已结束的教学活动）");
		if(!showItemMap.containsKey("5"))  headerList.remove("登记手册审核率");
		if(!showItemMap.containsKey("6"))  headerList.remove("病例审核率");
		if(!showItemMap.containsKey("13")) headerList.remove("学员评价平均分");
		if(!showItemMap.containsKey("14")) headerList.remove("带教评价平均分");
		if(!showItemMap.containsKey("15")) headerList.remove("专业基地评价平均分");
		if(!showItemMap.containsKey("16")) headerList.remove("培训基地评价平均分");
		if(!showItemMap.containsKey("17")) headerList.remove("出科考试数量");
		if(!showItemMap.containsKey("18")) headerList.remove("出科考试出勤率");
		if(!showItemMap.containsKey("19")) headerList.remove("出科考试合格率");
		if(!showItemMap.containsKey("20")) headerList.remove("出科考试平均分");
		if(!showItemMap.containsKey("21")) headerList.remove("我应该评价/已评价");
		
		return headerList;
	}
	
	/**
	 * @desc 拼接字符串
	 * @param str1
	 * @param str2
	 * @return
	 */
	private static String splicingStr(String str1, String str2){
		StringBuilder builder = new StringBuilder();
		if (StringUtils.isNotEmpty(str1)){
			builder.append(str1);
		}
		builder.append(" / ");
		if (StringUtils.isNotEmpty(str2)){
			builder.append(str2);
		}
		return builder.toString();
	}
	
	//动态构造表头
	private static List<String> getHeaderTitleList(ZyyTeacherPerformanceQuery query) {
		List<String> headerTitleList = new ArrayList<String>();
		List<ZyyActivityTypeVO> activityTypeList = query.getActivityTypeList();
		Long provinceId = query.getProvinceId();
		boolean yunnanSystem = false;
		if (provinceId != null){
			yunnanSystem = RegionUtil.checkYunnanSystem(provinceId.toString());
		}
		int len = invoiceExcelHeader.length;
		for (int i = 0; i < len; i++) {
			headerTitleList.add(invoiceExcelHeader[i]);
			if (!yunnanSystem && i == 9){
				//开始插入活动相关的内容
				if (!CollectionUtils.isEmpty(activityTypeList)){
					for (ZyyActivityTypeVO item : activityTypeList) {
						if (item != null){
							Integer checkStatus = item.getCheckStatus();
							if (checkStatus != null && checkStatus.intValue() == 1){
								headerTitleList.add(item.getTypeName()+"组织次数");
							}
						}
					}
				}
			}
		}
		return headerTitleList;
	} 
}
