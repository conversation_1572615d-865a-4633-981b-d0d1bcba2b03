package com.hys.zyy.manage.util.excel;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFDataFormat;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.util.CollectionUtils;

import com.hys.zyy.manage.constants.Constants;
import com.hys.zyy.manage.model.ZyyCustColItemNewVO;
import com.hys.zyy.manage.model.ZyyCustomizeColumnsNewVO;
import com.hys.zyy.manage.model.ZyyCustomizeTableData;
import com.hys.zyy.manage.model.ZyyEvaluateTableNewVO;
import com.hys.zyy.manage.model.ZyyUserEvaluateNewVO;


public class ExportEvaluatedUtil {
	//定义表头
    private static String[] invoiceExcelHeader = {"序号", "评价者","评价者角色","所在单位","被评价者","被评价者角色","被评价者所在单位",
    	"学员轮转时间","评价时间","评价状态","评价得分"};  
    
    //sheet名称
    private static String sheetName = "评价信息";
    /**
     * 导出
     * @param list
     * @return
     * 2017-12-28上午9:36:13
     * <AUTHOR>
     */
    public static HSSFWorkbook  createExcel(List<ZyyUserEvaluateNewVO> list,boolean isDetail,ZyyEvaluateTableNewVO tableVo) {    
        //这里需要说明一个问题：如果是 Offices 2007以前的Excel版本，new的对象是：**HSSFWorkbook** ，Offices 2007以后的Excel版本new的对象才是XSSFWorkbook
    	HSSFWorkbook  wb = new HSSFWorkbook();  
    	List<String> detailTitleMerge =new ArrayList<String>();
    	List<Integer> detailTitleMergeNum =new ArrayList<Integer>();
    	List<String> detailTitle =new ArrayList<String>();
    	List<Long> colsId =new ArrayList<Long>();
    	String[] invoiceExcelHeader1 = invoiceExcelHeader;
    	if(isDetail){//如果是明细就合并表头
    		for (ZyyCustomizeColumnsNewVO col : tableVo.getTableColumns()) {
    			detailTitleMerge.add(col.getParentColumnName());
    			detailTitleMergeNum.add(col.getSubColumnsList().size());
				for (ZyyCustomizeColumnsNewVO childCol : col.getSubColumnsList()) {
						detailTitle.add(childCol.getColumnName());
						colsId.add(childCol.getId());
				}
			}
    		invoiceExcelHeader1 = new String[invoiceExcelHeader.length+detailTitle.size()];//重新初始化
    		int dataIndex = 0 ;
    		for (int i = 0; i < invoiceExcelHeader1.length; i++) {
    			if(i>10){
    				invoiceExcelHeader1[i] = detailTitle.get(dataIndex++);
    			}else{
    				invoiceExcelHeader1[i] = invoiceExcelHeader[i];
    			}
			}
    	}
    	
    	//生成一个工作表
		Sheet sheet = null;
		//生成单元格的样式style
		HSSFCellStyle  style = wb.createCellStyle();    
		style.setAlignment(HSSFCellStyle.ALIGN_CENTER); // 水平居中  
		style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER); // 垂直居中
		//生成第一行
		Row row = null;    
		
		//生成单元格的样式style
		HSSFCellStyle  styleHead = wb.createCellStyle();    
		styleHead.setAlignment(HSSFCellStyle.ALIGN_CENTER); // 水平居中  
		styleHead.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER); // 垂直居中
		Font font = wb.createFont();  
		//font.setColor(HSSFColor.RED.index);  
		font.setBoldweight(Font.BOLDWEIGHT_BOLD); // 粗体
		styleHead.setFont(font);
		
		
		
		if (!CollectionUtils.isEmpty(list)){
			int sheetIndex = 0;
			int size = list.size();
			for (int i = 0; i < size; i++) {    
				// 10000 条数据一个sheet
				if (i % 10000 == 0 ){
					sheet = wb.createSheet(sheetName+sheetIndex);
					
					row = createHeaderRow(sheet,styleHead,invoiceExcelHeader1,detailTitleMerge,detailTitleMergeNum);
					//创建一次表头仅执行一次就行
					addMergedRegion(sheet);
					sheetIndex++;
				}
				
				//得到当前行数的下一行（row.getRowNum()：得到当前行数）
				int start = row.getRowNum()+1;
				row = sheet.createRow(start);  
				ZyyUserEvaluateNewVO vo = list.get(i);    
				
				setRowCellValue(vo,row,style,i, isDetail ,colsId);
				
			}
			
			if(tableVo!=null){
				sheet = wb.createSheet("评价模板");
				addEvalTemplet(sheet, styleHead, style ,tableVo);
			}
		}else{
			sheet = wb.createSheet(sheetName+0);
			row = sheet.createRow((int) 0);  
			createHeaderRow(sheet,styleHead,invoiceExcelHeader1,detailTitleMerge,detailTitleMergeNum);
			//创建一次表头仅执行一次就行
			addMergedRegion(sheet);
			
			if(tableVo!=null){
				sheet = wb.createSheet("评价模板");
				addEvalTemplet(sheet, styleHead, style, tableVo);
			}
		}
		
		
		
        return wb;    
    }
    
    
    //添加评价模板
    private static void addEvalTemplet(Sheet sheet,HSSFCellStyle  styleHead,HSSFCellStyle  style, ZyyEvaluateTableNewVO tableVo) {
    	styleHead.setAlignment(HSSFCellStyle.ALIGN_LEFT);
    	
    	style.setAlignment(HSSFCellStyle.ALIGN_LEFT);
    	int rowIndex = 0 ;
    	Row row = sheet.createRow(rowIndex++);  
    	Cell cell = row.createCell(0);
    	cell.setCellValue(tableVo.getTableName());
    	cell.setCellStyle(styleHead);
    	
    	row = sheet.createRow(rowIndex++);  
    	cell = row.createCell(0);
    	cell.setCellValue("总分:("+tableVo.getTotalScore()+"分)");
    	styleHead.setAlignment(HSSFCellStyle.ALIGN_RIGHT);
    	cell.setCellStyle(styleHead);
    	
    	int index = 1;
    	Font font = sheet.getWorkbook().createFont();
    	font.setFontHeightInPoints((short) 10);//设置excel数据字体大小
		font.setBoldweight(Font.BOLDWEIGHT_BOLD); // 粗体
		styleHead.setFont(font);
    	
    	Font font1 = sheet.getWorkbook().createFont();
		font1.setFontHeightInPoints((short) 15);//设置excel数据字体大小
		font1.setBoldweight(Font.BOLDWEIGHT_BOLD); // 粗体
		font1.setItalic(true);
		
		
    	for (ZyyCustomizeColumnsNewVO col : tableVo.getTableColumns()) {
    		styleHead.setAlignment(HSSFCellStyle.ALIGN_LEFT);
			String firstLevelName = col.getParentColumnName();
			//生成单元格的样式style
			HSSFCellStyle  styleHead1 = (HSSFCellStyle) sheet.getWorkbook().createCellStyle();    
			styleHead1.setAlignment(HSSFCellStyle.ALIGN_LEFT); // 水平居中  
			styleHead1.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER); // 垂直居中
			styleHead1.setFont(font1);
			row = sheet.createRow(rowIndex++);  
			cell = row.createCell(0);
	    	cell.setCellValue((index++)+":"+firstLevelName);
	    	cell.setCellStyle(styleHead1);
	    	
			for (ZyyCustomizeColumnsNewVO childCol : col.getSubColumnsList()) {
				String seconedLevelName = childCol.getColumnName();
				row = sheet.createRow(rowIndex++);  
				cell = row.createCell(0);
		    	cell.setCellValue(seconedLevelName);
		    	cell.setCellStyle(styleHead);
		    	
		    	for (ZyyCustColItemNewVO item : childCol.getItemList()) {
		    		String itemName = item.getItemName() + "(" + item.getItemScore() +")";
					row = sheet.createRow(rowIndex++);  
					cell = row.createCell(0);
			    	cell.setCellValue(itemName);
			    	cell.setCellStyle(style);
				}
			}
		}
    	
    }
    /**
     * 合并单元格  （起始行，结束行，起始列，结束列） 
     * @param start
     * @param end
     * @param sheet
     * 2017-12-28上午11:22:45
     * <AUTHOR>
     */
    private static void addMergedRegion(Sheet sheet) {
    	int len = invoiceExcelHeader.length;
    	sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, len-1));
    	//行合并
    	sheet.addMergedRegion(new CellRangeAddress(1, 2, 0, 0));
    	sheet.addMergedRegion(new CellRangeAddress(1, 2, 7, 7));
    	sheet.addMergedRegion(new CellRangeAddress(1, 2, 8, 8));
    	sheet.addMergedRegion(new CellRangeAddress(1, 2, 9, 9));
    	sheet.addMergedRegion(new CellRangeAddress(1, 2, 10, 10));
    	//列合并
    	sheet.addMergedRegion(new CellRangeAddress(1, 1, 1,3));
    	//列合并
    	sheet.addMergedRegion(new CellRangeAddress(1, 1, 4,6));
	}
	
	/**
	 * 赋值
	 * @param vo
	 * @param row
	 * @param style
	 * @param index
	 * <AUTHOR>
	 * @date 2018-9-14下午6:09:58
	 */
    private static void setRowCellValue(ZyyUserEvaluateNewVO vo,Row row,HSSFCellStyle  style,int index ,boolean isDetail ,List<Long> colsId) {
    	//赋值
    	Cell cell = null;
    	cell = row.createCell(0);
    	cell.setCellValue(index+1);  
    	cell.setCellStyle(style);
    	
    	cell = row.createCell(1);
    	cell.setCellValue(vo.getRealName() == null ?"":vo.getRealName()); 
    	cell.setCellStyle(style);
    	
    	cell = row.createCell(2);
    	cell.setCellValue(vo.getEvaluateType() == null ?"":Constants.EVALUATE_MAP.get(vo.getEvaluateType())); 
    	cell.setCellStyle(style);
    	
    	cell = row.createCell(3);
    	cell.setCellValue(vo.getUnitName() == null ?"":vo.getUnitName()); 
    	cell.setCellStyle(style);
    	
    	cell = row.createCell(4);
    	cell.setCellValue(vo.getEvaluatedRealName()  == null ?"":vo.getEvaluatedRealName()); 
    	cell.setCellStyle(style);
    	
    	cell = row.createCell(5);
    	cell.setCellValue(vo.getTableType()  == null ?"":Constants.EVAL_TYPE_MAP.get(vo.getTableType())); 
    	cell.setCellStyle(style);
    	
    	cell = row.createCell(6);
    	cell.setCellValue(vo.getEvaluatedUnitName()  == null ?"":vo.getEvaluatedUnitName()); 
    	cell.setCellStyle(style);
    	
    	String et = vo.getEnterTime();
    	String lt = vo.getLeaveTime();
    	StringBuilder builder = new StringBuilder();
    	if (StringUtils.isNotBlank(et) && StringUtils.isNotBlank(lt)){
    		builder.append(et);
    		builder.append(" - ");
    		builder.append(lt);
    	} else {
    		builder.append("与轮转无关");
    	}
    	cell = row.createCell(7);
    	cell.setCellValue(builder.toString()); 
    	cell.setCellStyle(style);
    	
    	cell = row.createCell(8);
    	cell.setCellValue(vo.getCycleDate() == null ?"":vo.getCycleDate()); 
    	cell.setCellStyle(style);
    	
    	
    	cell = row.createCell(9);
    	cell.setCellValue(vo.getId()== null ?"未评价":"已评价"); 
    	cell.setCellStyle(style);
    	
    	
		
    	cell = row.createCell(10);
    	cell.setCellValue(Double.parseDouble(vo.getRealScore() == null ?"0":vo.getRealScore().toString())); 
    	cell.setCellStyle(style);
    	
    	
    	if(isDetail){//如果是明细就合并表头
    		int colDataIndex = 0 ;
    		Long id = vo.getId();
    		if (id != null){
    			int endColumn = colsId.size()+10 ;
	    		for (int i = 10; i < endColumn; i++) {
						cell = row.createCell(i+1)	;
						cell.setCellType(Cell.CELL_TYPE_NUMERIC);
						String key = vo.getId()+"_"+colsId.get(colDataIndex++);
						//
						String score = vo.getMapResultDetail().get(key)==null?"0":vo.getMapResultDetail().get(key).toString();
						if(StringUtils.isNotBlank(key)){
							System.out.println(vo.getMapResultDetail()+",,,,,,,,,"+key);
						}
			    		cell.setCellValue(Double.parseDouble(score)); 
			    		cell.setCellStyle(style);
				}
    		}
    	}
	}
	/**
     * 生成标题行
     * @param row
     * @param style
     * 2017-12-28上午9:36:00
     * <AUTHOR>
     */
	private static Row createHeaderRow(Sheet sheet,HSSFCellStyle  style,String[] invoiceExcelHeader,List<String> detailTitleMerge,List<Integer> detailTitleMergeNum) {
		//第一行
		Row row = sheet.createRow((int) 0);
        Cell cell = row.createCell(0);    
        cell.setCellValue("360°评价表导出");  
        cell.setCellStyle(style);
		//第二行
		row = sheet.createRow((int) 1);
		cell = row.createCell(0);    
        cell.setCellValue(invoiceExcelHeader[0]);  
        cell.setCellStyle(style);
		cell = row.createCell(1);    
        cell.setCellValue("评价者信息");  
        cell.setCellStyle(style);
        cell = row.createCell(4);    
        cell.setCellValue("被评价者信息");  
        cell.setCellStyle(style);
        
        cell = row.createCell(7);    
        cell.setCellValue(invoiceExcelHeader[7]);  
        cell.setCellStyle(style);
        
        cell = row.createCell(8);    
        cell.setCellValue(invoiceExcelHeader[8]);  
        cell.setCellStyle(style);
        
        cell = row.createCell(9);    
        cell.setCellValue(invoiceExcelHeader[9]);  
        cell.setCellStyle(style);
        
        cell = row.createCell(10);    
        cell.setCellValue(invoiceExcelHeader[10]);  
        cell.setCellStyle(style);
        
        
        
        //扩展的表头    评论项大类带合并
        int lastCol = 0 ;
        for (int i = 0; i < detailTitleMerge.size(); i++) {
        	lastCol+=detailTitleMergeNum.get(i);
        	cell = row.createCell(11+lastCol-detailTitleMergeNum.get(i)+(i>0?1:0));    
            cell.setCellValue(detailTitleMerge.get(i));  
            cell.setCellStyle(style);
        	//列合并
        	sheet.addMergedRegion(new CellRangeAddress(1, 1, 11+lastCol-detailTitleMergeNum.get(i)+(i>0?1:0),11+lastCol));
        }
        	
        //第三行
        row = sheet.createRow((int) 2);
		int len = invoiceExcelHeader.length;
        for (int i = 0; i < len; i++) {
            //获取每一个单元格
            cell = row.createCell(i);    
            //给单元格赋值
            cell.setCellValue(invoiceExcelHeader[i]);  
            cell.setCellStyle(style);
        }  
		return row;
	} 
	
	public static void main(String[] args) {
		String[] invoiceExcelHeader1 = invoiceExcelHeader;
		System.out.println(invoiceExcelHeader1.length);
    	invoiceExcelHeader1 = new String[invoiceExcelHeader.length+10];//重新初始化
    	for (int i = 0; i < invoiceExcelHeader1.length; i++) {
			System.out.println(i);
		}
	}
}
