package com.hys.zyy.manage.util.excel;

import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;

import com.hys.beq.entities.util.DateUtil;
import com.hys.beq.entities.vo.exam.ExamCourse;
import com.hys.beq.entities.vo.exam.Examinee;

/**
 * Created by lch on 2019/5/27.
 */
public class ExportExamJKUtil {

    //定义表头
    private static String[] courseHeaderArray = {"考试名称","考试科目","考试日期","考试时间",//"考试限制时长",
            "考生人数","进入考试人数","答题人数","交卷人数","缺考人数","迟到人数","违纪、作弊人数"};
    private static String[] invoiceExcelHeader = {"姓名", "性别", "证件号码",
            "所属机构","年级","专业","科室",
            "手机号", "考试用时", "进入考试时间","提交试卷时间","是否进入考试","是否答题","是否交卷",
            "是否迟到","违纪、作弊行为","截屏次数","切屏次数",
            //"考前人脸识别状态","考中人脸识别状态","考后人脸识别状态",
            "延时"};
    //sheet名称
    private static String sheetName = "监考信息";

    /**
     * @desc 导出监考-导出Excel
     * @param examCourse  科目
     * @param list 科目考生信息
     * @return
     */
    public static HSSFWorkbook createExcel(ExamCourse examCourse ,List<Examinee> list) {
        HSSFWorkbook  wb = new HSSFWorkbook();
        //生成一个工作表
        Sheet sheet = null;
        //生成单元格的样式style
        HSSFCellStyle style = wb.createCellStyle();
        style.setAlignment(HSSFCellStyle.ALIGN_CENTER); // 水平居中
        style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER); // 垂直居中
        //生成第一行
        Row row = null;

        //生成单元格的样式style
        HSSFCellStyle styleTitle = wb.createCellStyle();
        styleTitle.setAlignment(HSSFCellStyle.ALIGN_CENTER); // 水平居中
        styleTitle.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER); // 垂直居中
        Font font = wb.createFont();
        font.setColor(HSSFColor.RED.index);
        font.setFontHeightInPoints((short)15);
        font.setBoldweight(Font.BOLDWEIGHT_BOLD); // 粗体
        styleTitle.setFont(font);

        //生成单元格的样式style
        HSSFCellStyle styleHead = wb.createCellStyle();
        styleHead.setAlignment(HSSFCellStyle.ALIGN_CENTER); // 水平居中
        styleHead.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER); // 垂直居中
        Font fontHead = wb.createFont();
        fontHead.setBoldweight(Font.BOLDWEIGHT_BOLD); // 粗体
        fontHead.setFontHeightInPoints((short)13);
        styleHead.setFont(fontHead);

        sheet = wb.createSheet(sheetName);
        int rowNum = 0;
        //考试信息
        row = sheet.createRow(rowNum++);
        row.setHeightInPoints(33);
        //获取每一个单元格
        Cell courseInfoCell = row.createCell(0);
        //给单元格赋值
        courseInfoCell.setCellValue("考试信息");
        courseInfoCell.setCellStyle(styleTitle);
        //起始行,结束行 , 起始列, 结束列
        CellRangeAddress craCourse = new CellRangeAddress(0,0,0,10);
        sheet.addMergedRegion(craCourse);
        //科目信息头
        row = sheet.createRow(rowNum++);
        row.setHeightInPoints(29);
        int courseCellIndex = 0;
        for(String courseHeader : courseHeaderArray){
            //获取每一个单元格
            Cell courseHeaderCell = row.createCell(courseCellIndex++);
            //给单元格赋值
            courseHeaderCell.setCellValue(courseHeader);
            courseHeaderCell.setCellStyle(styleHead);
        }
        //科目信息数据行
        row = sheet.createRow(rowNum++);
        row.setHeightInPoints(25);
        //设置科目数据
        setCourseInfo(examCourse,row,style);
        //设置考生信息标题
        row = sheet.createRow(rowNum++);
        row.setHeightInPoints(33);
        Cell examineeInfoCell = row.createCell(0);
        examineeInfoCell.setCellValue("考生信息");
        examineeInfoCell.setCellStyle(styleTitle);
        CellRangeAddress craExaminee = new CellRangeAddress(3,3,0,16);
        sheet.addMergedRegion(craExaminee);
        //考生信息头
        row = sheet.createRow(rowNum++);
        row.setHeightInPoints(29);
        createHeaderRow(row,styleHead);
        //考生信息数据
        if(list !=null && list.size() > 0){
            setExamineeInfo(sheet,style,rowNum,list);
        }

        return wb;
    }

    /**
     * @desc 设置考生信息
     * @param sheet
     * @param rowNum
     * @param list
     */
    private static void setExamineeInfo(Sheet sheet, HSSFCellStyle style, int rowNum, List<Examinee> list) {
        Row examineeDataRow = null;
        for(Examinee examinee:list){
            int eiDataCellIndex = 0;
            examineeDataRow = sheet.createRow(rowNum++);
            examineeDataRow.setHeightInPoints(22);
            String studentName = examinee.getStudentName();//姓名
            setDataCellVal(examineeDataRow,style,eiDataCellIndex++,studentName);

            String sex = examinee.getSex();//性别
            setDataCellVal(examineeDataRow,style,eiDataCellIndex++,sex);

            String certificateNo = examinee.getCertificateNo();//证件号码
            setDataCellVal(examineeDataRow,style,eiDataCellIndex++,certificateNo);

            String belongOrg = examinee.getBelongOrg();//所属机构
            setDataCellVal(examineeDataRow,style,eiDataCellIndex++,belongOrg);

            String yearStr = examinee.getYearStr();//年级
            setDataCellVal(examineeDataRow,style,eiDataCellIndex++,yearStr);

            String major = examinee.getMajor();//专业
            setDataCellVal(examineeDataRow,style,eiDataCellIndex++,major);

            String dept = examinee.getDept();//科室
            setDataCellVal(examineeDataRow,style,eiDataCellIndex++,dept);

            String phoneNo = examinee.getPhoneNo();//手机号
            setDataCellVal(examineeDataRow,style,eiDataCellIndex++,phoneNo);

            Integer examUseTime = examinee.getExamUseTime();//考试用时
            setDataCellVal(examineeDataRow,style,eiDataCellIndex++,examUseTime+"分钟");

            String enterTime = examinee.getEnterTime();//进入考试时间
            setDataCellVal(examineeDataRow,style,eiDataCellIndex++,enterTime);
            
            String submitExamTime = examinee.getAnswerEndTimeStr();//交卷时间
            setDataCellVal(examineeDataRow,style,eiDataCellIndex++,submitExamTime);

            Integer status = examinee.getStatus() == null? 0 : examinee.getStatus();
            String isEnter = status == 0 ?"否":"是";//是否进入考试
            setDataCellVal(examineeDataRow,style,eiDataCellIndex++,isEnter);

//            String answerStartTime = examinee.getAnswerStartTimeStr();//开始答题时间-判断是否答题
//            String isAnswer = StringUtils.isBlank(answerStartTime) ? "否":"是";//是否答题
//            setDataCellVal(examineeDataRow,style,eiDataCellIndex++,isAnswer);
            
            String answerSituation = "";
            if(examinee.getAnswerQuesNums() != null && examinee.getAnswerQuesNums() == 0){
                answerSituation = "否";
            }else{
                answerSituation = examinee.getAnswerQuesNums()+"/"+examinee.getTotalQuesNums();
            }
            setDataCellVal(examineeDataRow,style,eiDataCellIndex++,answerSituation);//答题数量

            String isSubmit = status == 2 ?"是":"否";//是否交卷
            setDataCellVal(examineeDataRow,style,eiDataCellIndex++,isSubmit);

            Integer isLate = examinee.getIsLate();//是否迟到
            String isLateStr = isLate == 1 ? "是":"否";
            setDataCellVal(examineeDataRow,style,eiDataCellIndex++,isLateStr);

            Integer noDiscipline = examinee.getNoDiscipline();//违纪、作弊行为
            String noDisciplineStr = noDiscipline == 1 ? "是":"否";
            setDataCellVal(examineeDataRow,style,eiDataCellIndex++,noDisciplineStr);

            Integer screenshotTimes = examinee.getScreenshotTimes();//截屏次数
            setDataCellVal(examineeDataRow,style,eiDataCellIndex++, screenshotTimes+"次");

            Integer examOutNum = examinee.getExamOutNum();//切屏次数
            setDataCellVal(examineeDataRow,style,eiDataCellIndex++, examOutNum+"次");

            Integer passFaced1 = examinee.getPassFaced1();//考前人脸识别状态
            String passF1 = "未识别";
            if(passFaced1 == 1){
                passF1 = "识别成功";
            }else if(passFaced1 == 2){
                passF1 = "识别失败";
            }
            //setDataCellVal(examineeDataRow,style,eiDataCellIndex++,passF1);

            Integer passFaced2 = examinee.getPassFaced2();//考中人脸识别状态
            String passF2 = "未识别";
            if(passFaced2 == 1){
                passF2 = "识别成功";
            }else if(passFaced2 == 2){
                passF2 = "识别失败";
            }
            //setDataCellVal(examineeDataRow,style,eiDataCellIndex++,passF2);

            Integer passFaced3 = examinee.getPassFaced3();//考后人脸识别状态
            String passF3 = "未识别";
            if(passFaced3 == 1){
                passF3 = "识别成功";
            }else if(passFaced3 == 2){
                passF3 = "识别失败";
            }
            //setDataCellVal(examineeDataRow,style,eiDataCellIndex++,passF3);

            Integer prolongTime = examinee.getProlongTime();//延长时间
            setDataCellVal(examineeDataRow,style,eiDataCellIndex++,prolongTime+"分钟");
        }
    }


    /**
     * @desc 设置科目数据
     * @param examCourse
     * @param row
     * @param style
     */
    private static void setCourseInfo(ExamCourse examCourse,Row row,HSSFCellStyle style){
        if(examCourse != null){
            int courseCellIndex = 0;
            Cell courseDataCell = null;
            String examName = examCourse.getExamName();//"考试名称"
            courseDataCell = row.createCell(courseCellIndex++);
            courseDataCell.setCellValue(examName);
            courseDataCell.setCellStyle(style);
            String courseName = examCourse.getCourseName();//"考试科目"
            courseDataCell = row.createCell(courseCellIndex++);
            courseDataCell.setCellValue(courseName);
            courseDataCell.setCellStyle(style);
            String examDate = examCourse.getStartDate()+"至"+examCourse.getEndDate();//"考试日期"
            courseDataCell = row.createCell(courseCellIndex++);
            courseDataCell.setCellValue(examDate);
            courseDataCell.setCellStyle(style);
            String examTime = examCourse.getStartTime()+"至"+examCourse.getEndTime();//"考试时间"
            courseDataCell = row.createCell(courseCellIndex++);
            courseDataCell.setCellValue(examTime);
            courseDataCell.setCellStyle(style);

//            Integer limitTimeLength =examCourse.getLimitTimeLength();//考试限制时长
//            courseDataCell = row.createCell(courseCellIndex++);
//            courseDataCell.setCellValue(limitTimeLength+"分钟");
//            courseDataCell.setCellStyle(style);

            Integer studentNum = examCourse.getStudentNum();//"考生人数"
            courseDataCell = row.createCell(courseCellIndex++);
            courseDataCell.setCellValue(studentNum+"人");
            courseDataCell.setCellStyle(style);
            Integer examinersNum = examCourse.getExaminersNum();//"进入考试人数"
            courseDataCell = row.createCell(courseCellIndex++);
            courseDataCell.setCellValue(examinersNum+"人");
            courseDataCell.setCellStyle(style);
            Integer answerNum = examCourse.getAnswerNum();//"答题人数"
            courseDataCell = row.createCell(courseCellIndex++);
            courseDataCell.setCellValue(answerNum+"人");
            courseDataCell.setCellStyle(style);
            Integer submitExamNum = examCourse.getSubmitExamNum();//"交卷人数"
            courseDataCell = row.createCell(courseCellIndex++);
            courseDataCell.setCellValue(submitExamNum+"人");
            courseDataCell.setCellStyle(style);
            Integer missNum = examCourse.getMissNum();//"缺考人数"
            courseDataCell = row.createCell(courseCellIndex++);
            courseDataCell.setCellValue(missNum+"人");
            courseDataCell.setCellStyle(style);
            Integer lateNum = examCourse.getLateNum();//"迟到人数"
            courseDataCell = row.createCell(courseCellIndex++);
            courseDataCell.setCellValue(lateNum+"人");
            courseDataCell.setCellStyle(style);
            Integer disciplineNum = examCourse.getDisciplineNum();//"违纪、作弊人数"
            courseDataCell = row.createCell(courseCellIndex++);
            courseDataCell.setCellValue(disciplineNum+"人");
            courseDataCell.setCellStyle(style);
        }
    }

    /**
     * 赋值
     */
    private static void setInvoiceRowCellValue(Examinee examinee,Row row,HSSFCellStyle style) {
        //赋值
        Cell cell = null;

        String answerStartTimeStr = examinee.getAnswerStartTimeStr();
        String answerEndTimeStr = examinee.getAnswerEndTimeStr();
        try{
            if(StringUtils.isNotBlank(answerStartTimeStr)){
                examinee.setAnswerStartTime(DateUtil.parse(answerStartTimeStr, DateUtil.hour24HMSPattern));
            }
            if(StringUtils.isNotBlank(answerEndTimeStr)){
                examinee.setAnswerEndTime(DateUtil.parse(answerEndTimeStr, DateUtil.hour24HMSPattern));
            }
        }catch(Exception e){
            e.printStackTrace();
        }

        cell = row.createCell(0);
        cell.setCellValue(examinee.getExamName() == null ?"":examinee.getExamName());
        cell.setCellStyle(style);

        cell = row.createCell(1);
        cell.setCellValue(examinee.getExamCourseName() == null ?"":examinee.getExamCourseName());
        cell.setCellStyle(style);

        cell = row.createCell(2);
        cell.setCellValue(examinee.getExamStartDateStr() == null ?"":examinee.getExamStartDateStr());
        cell.setCellStyle(style);

        cell = row.createCell(3);
        cell.setCellValue(examinee.getStudentName() == null ?"":examinee.getStudentName());
        cell.setCellStyle(style);

        cell = row.createCell(4);
        cell.setCellValue(examinee.getSex() == null ?"":examinee.getSex());
        cell.setCellStyle(style);

        cell = row.createCell(5);
        cell.setCellValue(examinee.getCertificateNo()  == null ?"":examinee.getCertificateNo());
        cell.setCellStyle(style);

        cell = row.createCell(6);
        cell.setCellValue(examinee.getStatus() != null && examinee.getStatus() >= 1 ? "是" : "否");
        cell.setCellStyle(style);

        cell = row.createCell(7);
        cell.setCellValue(examinee.getStatus() != null && examinee.getStatus() == 2 ? "是" : "否");
        cell.setCellStyle(style);

        cell = row.createCell(8);
        if(examinee.getStatus() != null && examinee.getStatus() == 2){
            cell.setCellValue(examinee.getExamUseTime()+"分");
        }else{
            cell.setCellValue("未交卷");
        }

        cell.setCellStyle(style);

        cell = row.createCell(9);
        cell.setCellValue(examinee.getSubjectiveScore()  == null ?"":examinee.getSubjectiveScore());
        cell.setCellStyle(style);

        cell = row.createCell(10);
        cell.setCellValue(examinee.getObjectiveScore()  == null ?"":examinee.getObjectiveScore());
        cell.setCellStyle(style);

    }
    /**
     * 生成标题行
     * @param row
     * @param style
     * 2017-12-28上午9:36:00
     * <AUTHOR>
     */
    private static void createHeaderRow(Row row,HSSFCellStyle style) {
        int len = invoiceExcelHeader.length;
        for (int i = 0; i < len; i++) {
            //获取每一个单元格
            Cell cell = row.createCell(i);
            //给单元格赋值
            cell.setCellValue(invoiceExcelHeader[i]);
            cell.setCellStyle(style);
        }
    }


    /**
     * @desc 给单元格赋值
     * @param examineeDataRow
     * @param style
     * @param cellVal
     */
    private static void setDataCellVal(Row examineeDataRow,HSSFCellStyle style, Integer cellIndex,String cellVal){
        Cell examineeDataCell = examineeDataRow.createCell(cellIndex);
        examineeDataCell.setCellValue(cellVal);
        examineeDataCell.setCellStyle(style);
    }

}
