package com.hys.zyy.manage.util.excel;

import java.util.ArrayList;
import java.util.List;

import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.util.CollectionUtils;

import com.hys.zyy.manage.model.ZyyActivityTypeVO;
import com.hys.zyy.manage.model.vo.StatistStuQuery;
import com.hys.zyy.manage.model.vo.StatistStuVO;

/**
 * 学员绩效统计导出
 * <AUTHOR>
 * @date 2019-1-16下午2:44:18
 */
public class ExportStuPerformStaticsUtil {
	//定义表头  
    private static String[] invoiceExcelHeader = {"姓名","性别","证件号","人员类型","年级","专业基地","手机号","培训年限","当前科室",
    	"已轮转科室数/轮转科室总数","已轮转月份/轮转总时间","入科教育实际参加数/入科教育需要参加数","入科教育出勤率","合格出科数/已轮转科室数","合格出科率",
    	"实际参加次数/需要参加总数","出勤率1","迟到次数","早退次数","旷工天数","请假天数","登记手册总数","手册提交数","手册通过率","提交病历总数",
    	"病历平均分","患者评价平均分","护士评价平均分","带教评价平均分","科室评价平均分","专业基地评价平均分","培训基地评价平均分","我应该评价/已评价","出勤率2","考试平均分","总体排名"};  
    //sheet名称
    private static String sheetName = "绩效统计-学员";
    
    /**
     * 导出
     * @param list
     * @param query
     * @return
     */
    public static HSSFWorkbook  createExcel(List<StatistStuVO> list,StatistStuQuery query) {    
        //这里需要说明一个问题：如果是 Offices 2007以前的Excel版本，new的对象是：**HSSFWorkbook** ，Offices 2007以后的Excel版本new的对象才是XSSFWorkbook
    	HSSFWorkbook  wb = new HSSFWorkbook();  
    	//生成一个工作表
		Sheet sheet = null;
		//生成单元格的样式style
		HSSFCellStyle  style = wb.createCellStyle();    
		style.setAlignment(HSSFCellStyle.ALIGN_CENTER); // 水平居中  
		style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER); // 垂直居中
		//生成第一行
		Row row = null;    
		//生成单元格的样式style
		HSSFCellStyle  styleHead = wb.createCellStyle();    
		styleHead.setAlignment(HSSFCellStyle.ALIGN_CENTER); // 水平居中  
		styleHead.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER); // 垂直居中
		styleHead.setBorderBottom(HSSFCellStyle.BORDER_THIN); //下边框
		styleHead.setBorderLeft(HSSFCellStyle.BORDER_THIN);//左边框
		styleHead.setBorderTop(HSSFCellStyle.BORDER_THIN);//上边框
		styleHead.setBorderRight(HSSFCellStyle.BORDER_THIN);//右边框
		
		Font font = wb.createFont();  
		font.setBoldweight(Font.BOLDWEIGHT_BOLD); // 粗体
		styleHead.setFont(font);
		/* 获取Title */
		List<String> headerTitleList = checkExistItem(query);
		if (!CollectionUtils.isEmpty(list)){
			int sheetIndex = 0;
			int size = list.size();
			for (int i = 0; i < size; i++) {    
				// 10000 条数据一个sheet
				if (i % 10000 == 0 ){
					sheet = wb.createSheet(sheetName+sheetIndex);
					row = createHeaderRow(sheet,styleHead,headerTitleList,query);
					addMergedRegion(sheet,query);
					sheetIndex++;
				}
				//得到当前行数的下一行（row.getRowNum()：得到当前行数）
				int start = row.getRowNum()+1;
				row = sheet.createRow(start);
				row.setHeight((short) 400);
				StatistStuVO vo = list.get(i);
				setRowCellValue(vo,row,style,i,headerTitleList,query);
			}    
		}else{
			sheet = wb.createSheet(sheetName+0);
			row = sheet.createRow((int) 0);  
			createHeaderRow(sheet,styleHead,headerTitleList,query);
			addMergedRegion(sheet,query);
		}
        return wb;    
    }
    
    /**
     * 合并单元格  （起始行，结束行，起始列，结束列）
     * @param sheet
     * @param query
     */
    private static void addMergedRegion(Sheet sheet, StatistStuQuery query) {
    	
    	Integer baseInfoTag = 9;//基本信息
		Integer cycleInfoTag = 2;//轮转部分
		Integer outDeptInfoTag = query.getOutDeptInfoTag();//出入科
		Integer activityTag = query.getActivityTag();//教学活动
		Integer attendInfoTag = query.getAttendInfoTag();//考勤
		Integer hdBookInfoTag = query.getHdBookInfoTag();//登记手册
		Integer evalInfoTag = query.getEvalInfoTag();//评价
		Integer examInfoTag = query.getExamInfoTag();//考试
		
    	//列合并
		Integer startNumber = 0;
		Integer endNumber = 0;
		endNumber += (baseInfoTag-1);
		sheet.addMergedRegion(new CellRangeAddress(0, 0, startNumber, endNumber));
		startNumber += baseInfoTag;
		endNumber += cycleInfoTag;
    	sheet.addMergedRegion(new CellRangeAddress(0, 0, startNumber, endNumber));
    	//入科教育活动
    	if(outDeptInfoTag > 0){
    		startNumber+=cycleInfoTag;
    		endNumber += outDeptInfoTag;
    		sheet.addMergedRegion(new CellRangeAddress(0, 0, startNumber, endNumber));
    	}
    	//教学活动
    	if(activityTag > 0){
    		startNumber+=outDeptInfoTag;
    		endNumber += activityTag;
    		sheet.addMergedRegion(new CellRangeAddress(0, 0, startNumber, endNumber));
    	}
    	//考勤信息
    	if(attendInfoTag > 0){
    		startNumber+=activityTag;
    		endNumber += attendInfoTag;
    		sheet.addMergedRegion(new CellRangeAddress(0, 0, startNumber, endNumber));
    	}
    	//登记手册，病历信息
    	if(hdBookInfoTag > 0){
    		startNumber+=attendInfoTag;
    		endNumber += hdBookInfoTag;
    		sheet.addMergedRegion(new CellRangeAddress(0, 0, startNumber, endNumber));
    	}
    	//评价信息
    	if(evalInfoTag > 0){
    		startNumber+=hdBookInfoTag;
    		endNumber += evalInfoTag;
    		sheet.addMergedRegion(new CellRangeAddress(0, 0, startNumber, endNumber));
    	}
    	//出科考试
    	if(examInfoTag > 0){
    		startNumber+=evalInfoTag;
    		endNumber += examInfoTag;
    		sheet.addMergedRegion(new CellRangeAddress(0, 0, startNumber, endNumber));
    	}
	}
	
	/**
	 * 赋值
	 * @param vo
	 * @param row
	 * @param style
	 * @param index
	 * @param query
	 */
    private static void setRowCellValue(StatistStuVO vo,Row row,HSSFCellStyle  style,int index,
    		List<String> headerTitleList, StatistStuQuery query) {
    	//"姓名","性别","证件号","人员类型","年级","专业基地","手机号","培训年限","当前科室",
    	//赋值
    	Cell cell = null;
    	cell = row.createCell(0);//姓名
    	cell.setCellValue(vo.getRealName() == null ?"":vo.getRealName()); 
    	cell.setCellStyle(style);
    	cell = row.createCell(1);//性别
    	cell.setCellValue(vo.getSex().equals(1)?"男":"女"); 
    	cell.setCellStyle(style);
    	cell = row.createCell(2);//身份证号
    	cell.setCellValue(vo.getCertificateNo() == null ?"":vo.getCertificateNo()); 
    	cell.setCellStyle(style);
    	cell = row.createCell(3);//人员类型
    	String identityType = "未知";
    	//身份类型
    	if (vo.getResidencySource() != null){
    		if(vo.getResidencySource()==1) identityType="单位人";
    		if(vo.getResidencySource()==2) identityType="社会人";
    		if(vo.getResidencySource()==3) identityType="学位衔接";
    	}
    	cell.setCellValue(identityType);
    	cell.setCellStyle(style);
    	cell = row.createCell(4);//年级
    	cell.setCellValue(vo.getYear() == null ?"" : vo.getYear()); 
    	cell.setCellStyle(style);
    	cell = row.createCell(5);//专业基地
    	cell.setCellValue(vo.getBaseName() == null ?"":vo.getBaseName()); 
    	cell.setCellStyle(style);
    	cell = row.createCell(6);//手机号
    	cell.setCellValue(vo.getMobilNumber()  == null ?"":vo.getMobilNumber()); 
    	cell.setCellStyle(style);
    	cell = row.createCell(7);//培训年限
    	cell.setCellValue(vo.getSchoolSystem()==null ?(" "):(vo.getSchoolSystem()+" 年")); 
    	cell.setCellStyle(style);
    	cell = row.createCell(8);//当前科室
    	cell.setCellValue(vo.getDeptName()==null ?"":vo.getDeptName()); 
    	cell.setCellStyle(style);
    	cell = row.createCell(9);//已轮转科室
    	String cycleDept = vo.getCycledDeptNum()+"/"+vo.getCycleDeptNum();
    	cell.setCellValue(cycleDept); 
    	cell.setCellStyle(style);
    	cell = row.createCell(10);//已轮转月份
    	String cycleTime = vo.getCycledMonthNum()+"/"+vo.getCycleMonthNum()+" ("+(vo.getCycleType()==2?"周":"月")+")";
    	cell.setCellValue(cycleTime); 
    	cell.setCellStyle(style);
    	/* 轮转统计 */
    	int lastColumnSize = headerTitleList.size();
    	
    	// 确定一下教学活动开始的列数
    	int activityCol = 0;
    	for (int i = 11; i < lastColumnSize; i++) {
    		String headerTitle = headerTitleList.get(i);
    		if(headerTitle.equals("入科教育实际参加数/入科教育需要参加数")){
    			activityCol += 1;
    		}else if(headerTitle.equals("入科教育出勤率")){
    			activityCol += 1;
    		}else if(headerTitle.equals("合格出科数/已轮转科室数")){
    			activityCol += 1;
    		}else if(headerTitle.equals("合格出科率")){
    			activityCol += 1;
    		}
		}
    	 
    	int specialIndex = 0;
    	for(int m=11;m<lastColumnSize;m++){
    		String headerTitle = headerTitleList.get(m);
    		String cellValue="空数据";
    		if(headerTitle.equals("入科教育实际参加数/入科教育需要参加数")){
    			cellValue = (vo.getAttendedEnterTeachNum()==null?"":vo.getAttendedEnterTeachNum())
    					+"/"+(vo.getAttendEnterTeachNum()==null?"":vo.getAttendEnterTeachNum());
    			specialIndex += 1;
    		}else if(headerTitle.equals("入科教育出勤率")){
    			cellValue = vo.getEnterTeachAttendRate()==null?"":vo.getEnterTeachAttendRate();
    			specialIndex += 1;
    		}else if(headerTitle.equals("合格出科数/已轮转科室数")){
    			cellValue = (vo.getOutDeptOkNum()==null?"":vo.getOutDeptOkNum())+"/"+(vo.getOutDeptNum()==null?"":vo.getOutDeptNum());
    			specialIndex += 1;
    		}else if(headerTitle.equals("合格出科率")){
    			cellValue = vo.getOutDeptRate()==null?"":vo.getOutDeptRate();
    			specialIndex += 1;
    		}else if(headerTitle.equals("实际参加次数/需要参加总数")){
    			
    			cellValue = (vo.getAttendedActivityNum()==null?"":vo.getAttendedActivityNum())
    					+"/"+(vo.getAttendActivityNum()==null?"":vo.getAttendActivityNum());
    			System.out.println("此时的cellValue="+cellValue);
    		}else if(headerTitle.equals("出勤率1")){
    			cellValue = vo.getActivityAttendRate()==null?"":vo.getActivityAttendRate();
    		}else if(headerTitle.equals("迟到次数")){
    			cellValue = vo.getCd()+" 次";
    		}else if(headerTitle.equals("早退次数")){
    			cellValue = vo.getZt()+" 次";
    		}else if(headerTitle.equals("旷工天数")){
    			cellValue = vo.getKg()+" 天";
    		}else if(headerTitle.equals("请假天数")){
    			cellValue = vo.getQj()+" 天";
    		}else if(headerTitle.equals("登记手册总数")){
    			cellValue = vo.getHandBookNum()==null?"":vo.getHandBookNum()+"";
    		}else if(headerTitle.equals("手册提交数")){
    			cellValue = vo.getHandBookSubNum()==null?"":vo.getHandBookSubNum()+"";
    		}else if(headerTitle.equals("手册通过率")){
    			cellValue = vo.getHandBookPassRate()==null?"":vo.getHandBookPassRate()+"";
    		}else if(headerTitle.equals("提交病历总数")){
    			cellValue = vo.getCaseSubNum()==null?"":vo.getCaseSubNum()+"";
    		}else if(headerTitle.equals("病历平均分")){
    			cellValue = vo.getCaseAvgScore()==null?"":vo.getCaseAvgScore();
    		}else if(headerTitle.equals("患者评价平均分")){
    			cellValue = vo.getAvgScoreEvalPatient()==null?"":vo.getAvgScoreEvalPatient();
    		}else if(headerTitle.equals("护士评价平均分")){
    			cellValue = vo.getAvgScoreEvalNurse()==null?"":vo.getAvgScoreEvalNurse();
    		}else if(headerTitle.equals("带教评价平均分")){
    			cellValue = vo.getAvgScoreEvalTeach()==null?"":vo.getAvgScoreEvalTeach();
    		}else if(headerTitle.equals("科室评价平均分")){
    			cellValue = vo.getAvgScoreEvalDept()==null?"":vo.getAvgScoreEvalDept();
    		}else if(headerTitle.equals("专业基地评价平均分")){
    			cellValue = vo.getAvgScoreEvalBase()==null?"":vo.getAvgScoreEvalBase();
    		}else if(headerTitle.equals("培训基地评价平均分")){
    			cellValue = vo.getAvgScoreEvalHosp()==null?"":vo.getAvgScoreEvalHosp();
    		}else if(headerTitle.equals("我应该评价/已评价")){
    			cellValue = vo.getNeedDoneEval()==null?"":vo.getNeedDoneEval();
    		}else if(headerTitle.equals("出勤率2")){
    			cellValue = vo.getExamAttendRate()==null?"":vo.getExamAttendRate();
    		}else if(headerTitle.equals("考试平均分")){
    			cellValue = vo.getExamAvgScore()==null?"":vo.getExamAvgScore();
    		}else if(headerTitle.equals("总体排名")){
    			cellValue = vo.getExamRanking()==null?"":vo.getExamRanking()+"";
    		}
    		
    		if(!cellValue.equals("空数据")){
    			cell = row.createCell(m);
    			cell.setCellValue(cellValue); 
    			cell.setCellStyle(style);
    		}
    		
    		if (specialIndex == activityCol){
    			m += 1;
    			// 开始赋值教学活动新增的相关的字段 lzq
    			List<ZyyActivityTypeVO> activityTypeList = vo.getActivityTypeList();
    			if (!CollectionUtils.isEmpty(activityTypeList)){
    				int size = activityTypeList.size();
    				int lessSize = size - 1;
    				for (int i = 0; i < size; i++) {
    					ZyyActivityTypeVO item = activityTypeList.get(i);
    					if (item != null){
							//${item.activityJoinNum}/${item.activityNum}
							Integer activityJoinNum = item.getActivityJoinNum();
							Integer activityNum = item.getActivityNum();
							if (activityJoinNum == null){
								activityJoinNum = 0;
							}
							if (activityNum == null){
								activityNum = 0;
							}
							cell = row.createCell(m);
			    			cell.setCellValue(activityJoinNum+"/"+activityNum); 
			    			cell.setCellStyle(style);
			    			m += 1;//增加游标值
						}
					}
    			}
    			specialIndex += 1; // 这个if只能走一次，所以需要加1，保证下次循环不在走了
    			
    			m -= 1;// for循环会加1，所以这里需要减1
    		}
    		
    	}
	}
    
	/**
	 * 生成标题行
	 * @param sheet
	 * @param style
	 * @param query
	 * @return
	 */
	private static Row createHeaderRow(Sheet sheet,HSSFCellStyle style,List<String> headerTitleList, StatistStuQuery query) {
		String[] firstRowHead = {"学员基本信息","轮转统计","出入科统计","教学活动统计","考勤统计","登记手册、病历统计","评价统计","出科统计考试"};
		//第一行
		Row row = sheet.createRow((int) 0);
		row.setHeight((short) 600);
		Integer baseInfoTag = 9;//基本信息
		Integer cycleInfoTag = 2;//轮转部分
		Integer outDeptInfoTag = query.getOutDeptInfoTag();//出入科
		Integer activityTag = query.getActivityTag();//教学活动
		Integer attendInfoTag = query.getAttendInfoTag();//考勤
		Integer hdBookInfoTag = query.getHdBookInfoTag();//登记手册
		Integer evalInfoTag = query.getEvalInfoTag();//评价
		Integer examInfoTag = query.getExamInfoTag();//考试
		//计算总列数
		Integer totalCellNumber = baseInfoTag+cycleInfoTag+outDeptInfoTag+activityTag+attendInfoTag
				+hdBookInfoTag+evalInfoTag+examInfoTag;
		Cell cell = null;
		for (int i = 0; i < totalCellNumber; i++) {
			cell = row.createCell(i);    
	        cell.setCellStyle(style);
	        sheet.setColumnWidth((short)i, (short)5500);
	        if(i==0){
	        	cell.setCellValue(firstRowHead[0]);
	        }else if(i == 9){
	        	cell.setCellValue(firstRowHead[1]);
	        }
	        if(outDeptInfoTag > 0){
	        	if(i==(11)){
	        		cell.setCellValue(firstRowHead[2]);
	        	}
	        }
	        if(activityTag > 0){
	        	if(i== (11+outDeptInfoTag)){
	        		cell.setCellValue(firstRowHead[3]);
	        	}
	        }
	        if(attendInfoTag > 0){
	        	if(i== (11+outDeptInfoTag+activityTag)){
	        		cell.setCellValue(firstRowHead[4]);
	        	}
	        }
			if(hdBookInfoTag > 0){
	        	if(i== (11+outDeptInfoTag+activityTag+attendInfoTag)){
	        		cell.setCellValue(firstRowHead[5]);
	        	}
			}
			if(evalInfoTag > 0){
	        	if(i== (11+outDeptInfoTag+activityTag+attendInfoTag+hdBookInfoTag)){
	        		cell.setCellValue(firstRowHead[6]);
	        	}
			}
			if(examInfoTag > 0){
	        	if(i== (11+outDeptInfoTag+activityTag+attendInfoTag+hdBookInfoTag+evalInfoTag)){
	        		cell.setCellValue(firstRowHead[7]);
	        	}
			}
		}
        //第二行
        row = sheet.createRow((int) 1);
        row.setHeight((short) 600);
		
        for (int i = 0; i < headerTitleList.size(); i++) {
        	//获取每一个单元格
    		cell = row.createCell(i);    
    		//给单元格赋值
    		String headerTitleStr = headerTitleList.get(i).replace("1", "").replace("2", "");
    		cell.setCellValue(headerTitleStr);  
    		cell.setCellStyle(style);
        }  
		return row;
	}
	
	/**
	 * 判断是否显示这列
	 * @param query
	 * @return
	 */
	private static List<String> checkExistItem(StatistStuQuery query) {
		
		List<String>  headerTitleList = getHeaderTitleList(query);
		
		if(query.getEnterTeachFlag() == 0) headerTitleList.remove("入科教育实际参加数/入科教育需要参加数");//入科教育
		if(query.getEnterTeachRateFlag() == 0) headerTitleList.remove("入科教育出勤率");//入科教育出勤率
		if(query.getOutDeptNumFlag() == 0) headerTitleList.remove("合格出科数/已轮转科室数");//合格出科
		if(query.getOutDeptRate() == 0) headerTitleList.remove("合格出科率");//合格出科率
		if(query.getAttendedActivityFlag() == 0) headerTitleList.remove("实际参加次数/需要参加总数");//参加教学活动
		if(query.getActivityAttendRateFlag() == 0) headerTitleList.remove("出勤率1");//教学活动出勤率
		if(query.getCdFlag() == 0) headerTitleList.remove("迟到次数");//迟到
		if(query.getZtFlag() == 0) headerTitleList.remove("早退次数");//早退
		if(query.getKgFlag() == 0) headerTitleList.remove("旷工天数");//旷工
		if(query.getQjFlag() == 0) headerTitleList.remove("请假天数");//请假
		if(query.getHbFlag() == 0) headerTitleList.remove("登记手册总数");//手册总数
		if(query.getHbSubFlag() == 0) headerTitleList.remove("手册提交数");//手册提交数
		if(query.getHbPassRateFlag() == 0) headerTitleList.remove("手册通过率");//手册通过率
		if(query.getCaseSubFlag() == 0) headerTitleList.remove("提交病历总数");//病历提交数
		if(query.getCaseAvgScoreFlag() == 0) headerTitleList.remove("病历平均分");//病历平均分
		if(query.getEvalPatientFlag() == 0) headerTitleList.remove("患者评价平均分");//患者评价平均分
		if(query.getEvalNurseFlag() == 0) headerTitleList.remove("护士评价平均分");//护士评价
		if(query.getEvalTeachFlag() == 0) headerTitleList.remove("带教评价平均分");//带教评价
		if(query.getEvalDeptFlag() == 0) headerTitleList.remove("科室评价平均分");//科室评价
		if(query.getEvalBaseFlag() == 0) headerTitleList.remove("专业基地评价平均分");//专业基地评价
		if(query.getEvalHospFlag() == 0) headerTitleList.remove("培训基地评价平均分");//培训基地评价
		if(query.getNeedDoneEval() == 0) headerTitleList.remove("我应该评价/已评价");//我应该评价/已评价
		if(query.getExamAttendRateFlag() == 0) headerTitleList.remove("出勤率2");//出科考试出勤
		if(query.getExamAvgScoreFlag() == 0) headerTitleList.remove("考试平均分");//出科考试平均分
		if(query.getExamRankingFlag() == 0) headerTitleList.remove("总体排名");//出科考试排名
		
		return headerTitleList;
	}
	
	//动态构造表头
	private static List<String> getHeaderTitleList(StatistStuQuery query) {
		List<String> headerTitleList = new ArrayList<String>();
		List<ZyyActivityTypeVO> activityTypeList = query.getActivityTypeList();
		int len = invoiceExcelHeader.length;
		for (int i = 0; i < len; i++) {
			headerTitleList.add(invoiceExcelHeader[i]);
			if (i == 14){
				//开始插入活动相关的内容
				if (!CollectionUtils.isEmpty(activityTypeList)){
					for (ZyyActivityTypeVO item : activityTypeList) {
						if (item != null){
							Integer checkStatus = item.getCheckStatus();
							if (checkStatus != null && checkStatus.intValue() == 1){
								headerTitleList.add(item.getTypeName()+"实际参加次数/需要参加总数");
							}
						}
					}
				}
			}
		}
		return headerTitleList;
	} 
}
