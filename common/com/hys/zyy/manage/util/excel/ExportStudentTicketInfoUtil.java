package com.hys.zyy.manage.util.excel;

import java.io.InputStream;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFDateUtil;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFRichTextString;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.ss.util.NumberToTextConverter;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import com.hys.zyy.manage.model.ZyyStudentTicketInfo;
import com.hys.zyy.manage.model.vo.ZyyExamStudentVO;
/**
 * 导出学生准考证相关信息
 * <AUTHOR>
 *
 */
public class ExportStudentTicketInfoUtil {
	/**
	 * 构造导出数据
	 * @param list
	 * @return
	 */
	public static HSSFWorkbook createExportData(List<ZyyExamStudentVO> list) {
		HSSFWorkbook workbook = new HSSFWorkbook();
		// 设置表头的样式
		HSSFCellStyle headStyle = workbook.createCellStyle();
		HSSFFont titleFont = workbook.createFont();
		titleFont.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
		headStyle.setFont(titleFont);
		headStyle.setFillBackgroundColor(HSSFColor.GREY_50_PERCENT.index);
		// 表格换行
		HSSFCellStyle cellStyle = workbook.createCellStyle();
		cellStyle.setWrapText(true);
		
		HSSFSheet sheet = workbook.createSheet("准考证信息");
		sheet.createFreezePane(0, 1, 0, 1);
		//数据
		
		//表格标题行
		createTableHeadTr(sheet,list,headStyle);
		
		//表格具体的内容
		createTableBodyContent(sheet,list,cellStyle);
		
		return workbook;
	}
	
	/**
	 * 表格具体的内容
	 * @param sheet
	 * @param list
	 * <AUTHOR>
	 * @date 2020-4-14下午1:48:23
	 */
	private static void createTableBodyContent(HSSFSheet sheet,
			List<ZyyExamStudentVO> list,HSSFCellStyle cellStyle) {
		HSSFRow row = null;
		HSSFCell cell = null;
		//表格内容
		if(!CollectionUtils.isEmpty(list)){
			int size = list.size();
			for (int j = 0; j < size; j++) {
				ZyyExamStudentVO item = list.get(j);
				row = sheet.createRow(j+1);
				int index = 0;
				cell = row.createCell(index++);
				cell.setCellValue(new HSSFRichTextString((j+1)+""));
				cell = row.createCell(index++);
				cell.setCellValue(new HSSFRichTextString(item.getRealName()));
				cell = row.createCell(index++);
				cell.setCellValue(new HSSFRichTextString(getSexStr(item.getSex())));
				cell = row.createCell(index++);
				cell.setCellValue(new HSSFRichTextString(item.getOrgName()));
				cell = row.createCell(index++);
				cell.setCellValue(new HSSFRichTextString(item.getBaseName()));
				cell = row.createCell(index++);
				cell.setCellValue(new HSSFRichTextString(item.getTicketNumber()));
				cell = row.createCell(index++);
				cell.setCellValue(new HSSFRichTextString(getCertTypeStr(item.getCertType())));
				cell = row.createCell(index++);
				cell.setCellValue(new HSSFRichTextString(item.getCertificateNo()));
				
				//隐藏列  9 , 10 列
				cell = row.createCell(index++);
				cell.setCellValue(new HSSFRichTextString(list.get(j).getUserId().toString()));
				cell = row.createCell(index++);
				cell.setCellValue(new HSSFRichTextString(list.get(j).getZyyExamId().toString()));
				
				//科目
				List<ZyyStudentTicketInfo> childList = item.getChildList();
				if(!CollectionUtils.isEmpty(childList)){
					int childSize = childList.size();
					for (int k = 0; k < childSize; k++) {
						ZyyStudentTicketInfo childItem = childList.get(k);
						cell = row.createCell(index++);
						cell.setCellValue(new HSSFRichTextString(childItem.getId() == null ? "": childItem.getId().toString()));
						cell = row.createCell(index++);
						cell.setCellValue(new HSSFRichTextString(toStr(childItem.getGroupName())));
						cell = row.createCell(index++);
						cell.setCellStyle(cellStyle);
						cell.setCellValue(new HSSFRichTextString(toStr(childItem.getStartDate())));
						cell = row.createCell(index++);
						cell.setCellStyle(cellStyle);
						cell.setCellValue(new HSSFRichTextString(toStr(childItem.getStartTime())));
						cell = row.createCell(index++);
						cell.setCellStyle(cellStyle);
						cell.setCellValue(new HSSFRichTextString(toStr(childItem.getEndDate())));
						cell = row.createCell(index++);
						cell.setCellStyle(cellStyle);
						cell.setCellValue(new HSSFRichTextString(toStr(childItem.getEndTime())));
						cell = row.createCell(index++);
						cell.setCellValue(new HSSFRichTextString(toStr(childItem.getExamPlace())));
						cell = row.createCell(index++);
						cell.setCellValue(new HSSFRichTextString(toStr(childItem.getExamRoom())));
						cell = row.createCell(index++);
						cell.setCellValue(new HSSFRichTextString(toStr(childItem.getExamSeat())));
					}
				}
			}
		}
	}

	/**
	 * 表格首行标题行
	 * @param sheet
	 * @param list
	 * <AUTHOR>
	 * @date 2020-4-14下午1:45:23
	 */
	private static void createTableHeadTr(HSSFSheet sheet,
			List<ZyyExamStudentVO> list,HSSFCellStyle headStyle) {
		
		//列
		int columnSize = 8;
		//隐藏列 9 ， 10 列
		sheet.setColumnHidden(columnSize++, true);//学员id
		sheet.setColumnHidden(columnSize++, true);//考试id	
		//获取科目数
		if(!CollectionUtils.isEmpty(list)){
			ZyyExamStudentVO temp = list.get(0);
			if(!CollectionUtils.isEmpty(temp.getChildList())){
				//科目隐藏列
				int size = temp.getChildList().size();
				for (int i = 0; i < size; i++) {
					sheet.setColumnHidden((columnSize) + (i*9), true); //隐藏Id
				}
				columnSize += size * 9;
			}
		}
		for (int i = 0; i < columnSize; i++) {
			sheet.setColumnWidth(i, 6000);
		}
		
		
		HSSFRow row = sheet.createRow((short) 0);
		row.setRowStyle(headStyle);
		//设置表头
		int i = 0;
		HSSFCell cell = row.createCell(i++);
		cell.setCellValue(new HSSFRichTextString("序号"));
		cell = row.createCell(i++);
		cell.setCellValue(new HSSFRichTextString("姓名"));
		cell = row.createCell(i++);
		cell.setCellValue(new HSSFRichTextString("性别"));
		cell = row.createCell(i++);
		cell.setCellValue(new HSSFRichTextString("所属机构"));
		cell = row.createCell(i++);
		cell.setCellValue(new HSSFRichTextString("处室名称"));
		cell = row.createCell(i++);
		cell.setCellValue(new HSSFRichTextString("准考证号"));
		cell = row.createCell(i++);
		cell.setCellValue(new HSSFRichTextString("证件类型"));
		cell = row.createCell(i++);
		cell.setCellValue(new HSSFRichTextString("证件号码"));
		
		//隐藏列 9 ， 10 列
		cell = row.createCell(i++);
		cell.setCellValue(new HSSFRichTextString(""));
		cell = row.createCell(i++);
		cell.setCellValue(new HSSFRichTextString(""));
		
		//科目表头
		if(!CollectionUtils.isEmpty(list)){
			ZyyExamStudentVO temp = list.get(0);
			if(!CollectionUtils.isEmpty(temp.getChildList())){
				int size = temp.getChildList().size();
				for (int j = 0; j < size; j++) {
					cell = row.createCell(i++);
					//9列
					cell.setCellValue(new HSSFRichTextString(""));//隐藏id
					cell = row.createCell(i++);
					cell.setCellValue(new HSSFRichTextString("科目"+(j+1)));
					cell = row.createCell(i++);
					cell.setCellValue(new HSSFRichTextString("开始考试日期"));
					cell = row.createCell(i++);
					cell.setCellValue(new HSSFRichTextString("开始考试时间"));
					cell = row.createCell(i++);
					cell.setCellValue(new HSSFRichTextString("结束考试日期"));
					cell = row.createCell(i++);
					cell.setCellValue(new HSSFRichTextString("结束考试时间"));
					cell = row.createCell(i++);
					cell.setCellValue(new HSSFRichTextString("考点"));
					cell = row.createCell(i++);
					cell.setCellValue(new HSSFRichTextString("考场"));
					cell = row.createCell(i++);
					cell.setCellValue(new HSSFRichTextString("座位号"));
				}
			}
		}
	}

	private static String toStr(String str){
		return str==null?"":str ;
	}
	
	private static String getCertTypeStr(Integer certType){
		if (certType == null){
			return "";
		}
		if (1 == certType){
			return "居民身份证";
		}
		if (2 == certType){
			return "军官证";
		}
		if (3 == certType){
			return "护照";
		}
		if (4 == certType){
			return "港澳通行证";
		}
		if (5 == certType){
			return "台胞证";
		}
		return "";
	}
	
	private static String getSexStr(Integer sex){
		if (sex == null){
			return "";
		}
		if (1 == sex){
			return "男";
		}
		if (2 == sex){
			return "女";
		}
		return "";
	}
	
	/**
	 * 获取excel的内容
	 * @param file
	 * @return
	 * <AUTHOR>
	 * @date 2020-4-14下午2:36:24
	 */
	public static ZyyExamStudentVO getStudentTicketInfoFromExcel(MultipartFile file) {
		// IO流读取文件  
        InputStream input = null; 
        ZyyExamStudentVO studentVo = new ZyyExamStudentVO();
        List<ZyyExamStudentVO> studentList = new ArrayList<ZyyExamStudentVO>();
        ZyyExamStudentVO studentItem = null;
        List<ZyyStudentTicketInfo> childList = null;
        ZyyStudentTicketInfo child = null;
		try {
			if (file != null){
				input = file.getInputStream();
				
				HSSFWorkbook workbook = new HSSFWorkbook(input);
				HSSFSheet sheet = workbook.getSheetAt(0);
				
				if (sheet != null){
					
					int totalRows = sheet.getLastRowNum();
					
					HSSFRow firstRow  = sheet.getRow(0);
					int lastCellNum = firstRow.getLastCellNum();
					int childCellNum = lastCellNum - 10 ;
					//是否有子项
					boolean hasChild = false;
					if (childCellNum > 0){
						hasChild = true;
					}
					
					//读取Row,从第2行开始
					for (int i = 1; i <= totalRows; i++) {
						studentItem = new ZyyExamStudentVO();
						HSSFRow hSSFRow  = sheet.getRow(i);
						 
						String lineNo = getCellValue(hSSFRow.getCell(0)); 
						if (StringUtils.isNotBlank(lineNo)){
							//获取每一行的值
							setData(studentItem,hSSFRow);
							
							if (hasChild){
								//有子数据 
								childList = new ArrayList<ZyyStudentTicketInfo>();
								//子数据的个数
								int childNum = childCellNum/9;
								for (int j = 0; j < childNum; j++) {
									int index = (j*9);
									String id = getCellValue(hSSFRow.getCell(10+index)); 
									if (StringUtils.isNotBlank(id)){
										child = new ZyyStudentTicketInfo();
										//子项赋值
										setChildData(child,hSSFRow,index);
										
										childList.add(child);
									}
								}
								studentItem.setChildList(childList);
							}
							
							studentList.add(studentItem);
							
						}
					}
					studentVo.setStudentList(studentList);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		} finally{
			if (input != null){
				try {
					input.close();
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
		}
		return studentVo;
	}
	
	/**
	 * 子项赋值
	 * @param child
	 * @param hSSFRow
	 * @param index
	 * <AUTHOR>
	 * @date 2020-4-14下午3:28:38
	 */
	private static void setChildData(ZyyStudentTicketInfo child,HSSFRow hSSFRow, int index) {
		//隐藏id, 科目,开始考试日期 ,开始考试时间,结束考试日期,结束考试时间,考点,考场 ,座位号
		//9列
		String id = getCellValue(hSSFRow.getCell(10+index)); 
		String groupName = getCellValue(hSSFRow.getCell(11+index)); 
		String startDate = getCellValue(hSSFRow.getCell(12+index));
		String startTime = getCellValue(hSSFRow.getCell(13+index));
		String endDate = getCellValue(hSSFRow.getCell(14+index));
		String endTime = getCellValue(hSSFRow.getCell(15+index));
		String examPlace = getCellValue(hSSFRow.getCell(16+index));
		String examRoom = getCellValue(hSSFRow.getCell(17+index));
		String examSeat = getCellValue(hSSFRow.getCell(18+index));
		
		child.setId(Long.valueOf(id));
		child.setGroupName(groupName);
		child.setStartDate(startDate);
		child.setStartTime(startTime);
		child.setEndDate(endDate);
		child.setEndTime(endTime);
		child.setExamPlace(examPlace);
		child.setExamRoom(examRoom);
		child.setExamSeat(examSeat);
	}

	/**
	 * 获取每一行的值   
	 * @param studentItem
	 * @param hSSFRow
	 * <AUTHOR>
	 * @date 2020-4-14下午2:56:24
	 */
	private static void setData(ZyyExamStudentVO studentItem,HSSFRow hSSFRow) {
		//学生id 9 ， 考试id 10 ， 准考证号 6

		//准考证号
        String ticketNumber = getCellValue(hSSFRow.getCell(5)); 
        //学生id
        String userId = getCellValue(hSSFRow.getCell(8)); 
        //考试id
        String zyyExamId = getCellValue(hSSFRow.getCell(9)); 

        studentItem.setTicketNumber(ticketNumber);
        studentItem.setUserId(Long.valueOf(userId));
        studentItem.setZyyExamId(Long.valueOf(zyyExamId));
	}
	
	/** 
     * 根据excel单元格类型获取excel单元格值 
     * @param cell 
     * @return 
     */  
    private static String getCellValue(Cell cell) {  
        String cellvalue = "";  
        if (cell != null) {  
            // 判断当前Cell的Type  
            switch (cell.getCellType()) {  
            // 如果当前Cell的Type为NUMERIC  
            case HSSFCell.CELL_TYPE_NUMERIC: {  
                short format = cell.getCellStyle().getDataFormat();  
                if(format == 14 || format == 31 || format == 57 || format == 58){   //excel中的时间格式  
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");    
                    double value = cell.getNumericCellValue();    
                    Date date = DateUtil.getJavaDate(value);    
                    cellvalue = sdf.format(date);    
                }  
                // 判断当前的cell是否为Date  
                else if (HSSFDateUtil.isCellDateFormatted(cell)) {  //先注释日期类型的转换，在实际测试中发现HSSFDateUtil.isCellDateFormatted(cell)只识别2014/02/02这种格式。  
                    // 如果是Date类型则，取得该Cell的Date值           // 对2014-02-02格式识别不出是日期格式  
                    Date date = cell.getDateCellValue();  
                    DateFormat formater = new SimpleDateFormat("yyyy-MM-dd");  
                    cellvalue= formater.format(date);  
                } else { // 如果是纯数字  
                    // 取得当前Cell的数值  
                    cellvalue = NumberToTextConverter.toText(cell.getNumericCellValue());   
                      
                }  
                break;  
            }  
            // 如果当前Cell的Type为STRIN  
            case HSSFCell.CELL_TYPE_STRING:  
                // 取得当前的Cell字符串  
                cellvalue = cell.getStringCellValue().replaceAll("'", "''");  
                break;  
            case  HSSFCell.CELL_TYPE_BLANK:  
                cellvalue = null;  
                break;  
            // 默认的Cell值  
            default:{  
                cellvalue = " ";  
            }  
            }  
        } else {  
            cellvalue = "";  
        }  
        return cellvalue;  
    }
}
