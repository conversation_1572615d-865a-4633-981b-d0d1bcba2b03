package com.hys.zyy.manage.util.excel;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.util.CollectionUtils;

import com.hys.zyy.manage.model.ZyyActivityTypeVO;
import com.hys.zyy.manage.model.vo.ZyyTeacherPerformanceVO;
import com.hys.zyy.manage.query.ZyyTeacherPerformanceQuery;
import com.hys.zyy.manage.util.StringUtils;

/**
 * 带教绩效统计导出
 * <AUTHOR>
 * @date 2019-1-16下午2:44:18
 */
public class ExportTeacherPerformStaticsUtil {
	//定义表头 
    private static String[] invoiceExcelHeader = {"序号", "姓名", "医师工号", "证件号","科室","带学员人次","入科教育主持数量","出科合格率","合格出科人次/已轮转人次",
    	"组织次数","学员评价平均分","科室评价平均分","专业基地评价平均分","培训基地评价平均分","登记手册审核率","病历审核率","出科考试合格率","出科考试平均分"};  
    //8 = "主持教学活动次数",12 = "我应该评价/已评价"
    private static String[] invoiceExcelHeaderV2 = {"序号", "姓名","证件号","科室","带学员人次","入科教育主持数量","出科合格率","合格出科人次/已轮转人次",
    	"组织次数","主持教学活动次数","学员评价平均分","科室评价平均分","专业基地评价平均分","培训基地评价平均分","我应该评价/已评价",
    	"登记手册审核率","病历审核率","出科考试合格率","出科考试平均分"};  
    //12 = "我应该评价/已评价"
    private static String[] invoiceExcelHeaderV3 = {"序号", "姓名","证件号","科室","带学员人次","入科教育主持数量","出科合格率","合格出科人次/已轮转人次",
    	"组织次数","学员评价平均分","科室评价平均分","专业基地评价平均分","培训基地评价平均分","我应该评价/已评价","登记手册审核率","病历审核率","出科考试合格率","出科考试平均分"};  
    //sheet名称
    private static String sheetName = "带教绩效统计";
    /**
     * 导出
     * @param list
     * @return
     * 2017-12-28上午9:36:13
     * <AUTHOR>
     * @param colspanMap 
     * @param showItemMap 
     */
    public static HSSFWorkbook  createExcel(List<ZyyTeacherPerformanceVO> list, Map<String, String> showItemMap,
    		Map<String, Integer> colspanMap, Map<String,Boolean> systemMap,ZyyTeacherPerformanceQuery query) {    
        //这里需要说明一个问题：如果是 Offices 2007以前的Excel版本，new的对象是：**HSSFWorkbook** ，Offices 2007以后的Excel版本new的对象才是XSSFWorkbook
    	HSSFWorkbook  wb = new HSSFWorkbook();  
		
    	//生成一个工作表
		Sheet sheet = null;
		//生成单元格的样式style
		HSSFCellStyle  style = wb.createCellStyle();    
		style.setAlignment(HSSFCellStyle.ALIGN_CENTER); // 水平居中  
		style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER); // 垂直居中
		//生成第一行
		Row row = null;    
		//生成单元格的样式style
		HSSFCellStyle  styleHead = wb.createCellStyle();    
		styleHead.setAlignment(HSSFCellStyle.ALIGN_CENTER); // 水平居中  
		styleHead.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER); // 垂直居中
		styleHead.setBorderBottom(HSSFCellStyle.BORDER_THIN); //下边框
		styleHead.setBorderLeft(HSSFCellStyle.BORDER_THIN);//左边框
		styleHead.setBorderTop(HSSFCellStyle.BORDER_THIN);//上边框
		styleHead.setBorderRight(HSSFCellStyle.BORDER_THIN);//右边框
		Font font = wb.createFont();  
		//font.setColor(HSSFColor.RED.index);  
		font.setBoldweight(Font.BOLDWEIGHT_BOLD); // 粗体
		styleHead.setFont(font);
		
		if (!CollectionUtils.isEmpty(list)){
			int sheetIndex = 0;
			int size = list.size();
			for (int i = 0; i < size; i++) {    
				// 10000 条数据一个sheet
				if (i % 10000 == 0 ){
					sheet = wb.createSheet(sheetName+sheetIndex);
					row = createHeaderRow(sheet,styleHead,showItemMap,colspanMap,systemMap,query);
					addMergedRegion(sheet,colspanMap);
					sheetIndex++;
				}
				
				//得到当前行数的下一行（row.getRowNum()：得到当前行数）
				int start = row.getRowNum()+1;
				row = sheet.createRow(start);  
				ZyyTeacherPerformanceVO vo = list.get(i);    
				setRowCellValue(vo,row,style,i,showItemMap);
			}    
		}else{
			sheet = wb.createSheet(sheetName+0);
			row = sheet.createRow((int) 0);  
			createHeaderRow(sheet,styleHead,showItemMap,colspanMap,systemMap,query);
			addMergedRegion(sheet,colspanMap);
		}
        return wb;    
    }
    
    /**
     * 合并单元格  （起始行，结束行，起始列，结束列） 
     * @param start
     * @param end
     * @param sheet
     * 2017-12-28上午11:22:45
     * <AUTHOR>
     * @param colspanMap 
     */
    private static void addMergedRegion(Sheet sheet, Map<String, Integer> colspanMap) {
    	//列合并
    	for (int i = 0; i < 6; i++) {
    		sheet.addMergedRegion(new CellRangeAddress(0, 1, i, i));
		}
    	//行合并
    	Integer enterLeaveColspan = colspanMap.get("enterLeaveColspan");
		Integer evaluateColspan = colspanMap.get("evaluateColspan");
		Integer handCaseColspan = colspanMap.get("handCaseColspan");
		Integer leaveColspan = colspanMap.get("leaveColspan");
		Integer activityColspan = colspanMap.get("activityColspan");
		Integer zero = Integer.valueOf(0);
		
		int startColNum=6;
		int endColNum=0;
		if (!zero.equals(enterLeaveColspan)){
			endColNum+=(startColNum+enterLeaveColspan-1);
			sheet.addMergedRegion(new CellRangeAddress(0, 0, startColNum, endColNum));
			startColNum += enterLeaveColspan;
		}
		//判断合并的列   教学活动
		if (!zero.equals(activityColspan)){
			endColNum =(startColNum+activityColspan-1);
			sheet.addMergedRegion(new CellRangeAddress(0, 0, startColNum, endColNum));
			startColNum+=activityColspan;
		}
		if (!zero.equals(evaluateColspan)){
			endColNum =(startColNum+evaluateColspan-1);
			sheet.addMergedRegion(new CellRangeAddress(0, 0, startColNum, endColNum));
			startColNum+=evaluateColspan;
		}
		if (!zero.equals(handCaseColspan)){
			endColNum =(startColNum+handCaseColspan-1);
			sheet.addMergedRegion(new CellRangeAddress(0, 0, startColNum, endColNum));
			startColNum+=handCaseColspan;
		}
		if (!zero.equals(leaveColspan)){
			endColNum =(startColNum+leaveColspan-1);
			sheet.addMergedRegion(new CellRangeAddress(0, 0, startColNum, endColNum));
			startColNum+=leaveColspan;
		}
	}
	
	/**
	 * 赋值
	 * @param vo
	 * @param row
	 * @param style
	 * @param index
	 * <AUTHOR>
	 * @param showItemMap 
	 * @date 2018-9-14下午6:09:58
	 */
    private static void setRowCellValue(ZyyTeacherPerformanceVO vo,Row row,HSSFCellStyle  style,int index, Map<String, String> showItemMap) {
    	//赋值
    	Cell cell = null;
    	cell = row.createCell(0);
    	cell.setCellValue(index+1);  
    	cell.setCellStyle(style);
    	
    	cell = row.createCell(1);
    	cell.setCellValue(vo.getTeacherName() == null ?"":vo.getTeacherName()); 
    	cell.setCellStyle(style);
    	
		cell = row.createCell(2);
		cell.setCellValue(vo.getJobNumber() == null ? "" : vo.getJobNumber());
		cell.setCellStyle(style);
    	
    	cell = row.createCell(3);
    	cell.setCellValue(vo.getCertificateNo() == null ?"": vo.getCertificateNo()); 
    	cell.setCellStyle(style);
    	
    	cell = row.createCell(4);
    	cell.setCellValue(vo.getDeptName() == null ?"":vo.getDeptName()); 
    	cell.setCellStyle(style);
    	
    	cell = row.createCell(5);
    	cell.setCellValue(vo.getStudentTotal()  == null ?"":vo.getStudentTotal()); 
    	cell.setCellStyle(style);
    	
    	int tmpIndex=6;
    	if (showItemMap.containsKey("1")){
    		cell = row.createCell(tmpIndex);
    		tmpIndex++;
    		cell.setCellValue(vo.getHoldTotal()  == null ?"":vo.getHoldTotal()); 
    		cell.setCellStyle(style);
    	}
    	if (showItemMap.containsKey("2")){
    		cell = row.createCell(tmpIndex);
    		tmpIndex++;
    		cell.setCellValue(vo.getLeaveRate()  == null ?"":vo.getLeaveRate()); 
    		cell.setCellStyle(style);
    	}
    	if (showItemMap.containsKey("3")){
    		cell = row.createCell(tmpIndex);
    		tmpIndex++;
    		//${item.qualifyTotal } / ${item.leaveDepartTotal }
    		String qualifyTotal = vo.getQualifyTotal();
    		String leaveDepartTotal = vo.getLeaveDepartTotal();
    		StringBuilder builder = new StringBuilder();
    		if (StringUtils.isNotEmpty(qualifyTotal)){
    			builder.append(qualifyTotal);
    		}
    		builder.append(" / ");
    		if (StringUtils.isNotEmpty(leaveDepartTotal)){
    			builder.append(leaveDepartTotal);
    		}
    		cell.setCellValue(builder.toString()); 
    		cell.setCellStyle(style);
    	}
    	//具体的教学活动组织次数
    	List<ZyyActivityTypeVO> activityList = vo.getActivityTypeList();
    	if (!CollectionUtils.isEmpty(activityList)){
    		for (ZyyActivityTypeVO item : activityList) {
				if (item != null){
					cell = row.createCell(tmpIndex);
		    		tmpIndex++;
		    		cell.setCellValue(item.getActivityNum() == null ?"":item.getActivityNum().toString()); 
		    		cell.setCellStyle(style);
				}
			}
    	}
    	
    	if (showItemMap.containsKey("4")){
    		cell = row.createCell(tmpIndex);
    		tmpIndex++;
    		cell.setCellValue(vo.getActivityTotal() == null ?"":vo.getActivityTotal()); 
    		cell.setCellStyle(style);
    	}
    	
    	if (showItemMap.containsKey("13")){
    		cell = row.createCell(tmpIndex);
    		tmpIndex++;
    		cell.setCellValue(vo.getSpeakerActivityTotal() == null ?"":vo.getSpeakerActivityTotal()); 
    		cell.setCellStyle(style);
    	}
    	
    	if (showItemMap.containsKey("7")){
    		cell = row.createCell(tmpIndex);
    		tmpIndex++;
    		cell.setCellValue(vo.getStudentEvalScore() == null ?"":vo.getStudentEvalScore()); 
    		cell.setCellStyle(style);
    	}
    	
    	if (showItemMap.containsKey("8")){
    		cell = row.createCell(tmpIndex);
    		tmpIndex++;
    		cell.setCellValue(vo.getDepartEvalScore() == null ?"":vo.getDepartEvalScore()); 
    		cell.setCellStyle(style);
    	}
    	
    	if (showItemMap.containsKey("9")){
    		cell = row.createCell(tmpIndex);
    		tmpIndex++;
    		cell.setCellValue(vo.getBaseEvalScore() == null ?"":vo.getBaseEvalScore()); 
    		cell.setCellStyle(style);
    	}
    	
    	if (showItemMap.containsKey("10")){
    		cell = row.createCell(tmpIndex);
    		tmpIndex++;
    		cell.setCellValue(vo.getHospitalEvalScore() == null ?"":vo.getHospitalEvalScore()); 
    		cell.setCellStyle(style);
    	}
    	if (showItemMap.containsKey("14")){
    		cell = row.createCell(tmpIndex);
    		tmpIndex++;
    		cell.setCellValue(vo.getNeedDoneEval() == null ?"":vo.getNeedDoneEval()); 
    		cell.setCellStyle(style);
    	}
    	
    	if (showItemMap.containsKey("5")){
    		cell = row.createCell(tmpIndex);
    		tmpIndex++;
    		cell.setCellValue(vo.getHandbookRate() == null ?"":vo.getHandbookRate()); 
    		cell.setCellStyle(style);
    	}
    	if (showItemMap.containsKey("6")){
    		cell = row.createCell(tmpIndex);
    		tmpIndex++;
    		cell.setCellValue(vo.getCaseRate() == null ?"":vo.getCaseRate()); 
    		cell.setCellStyle(style);
    	}
    	if (showItemMap.containsKey("11")){
    		cell = row.createCell(tmpIndex);
    		tmpIndex++;
    		cell.setCellValue(vo.getLeaveExamRate() == null ?"":vo.getLeaveExamRate()); 
    		cell.setCellStyle(style);
    	}
    	if (showItemMap.containsKey("12")){
    		cell = row.createCell(tmpIndex);
    		tmpIndex++;
    		cell.setCellValue(vo.getLeaveAvgScore() == null ?"":vo.getLeaveAvgScore()); 
    		cell.setCellStyle(style);
    	}
    	
	}
    
	/**
	 * 生成标题行
	 * @param sheet
	 * @param style
	 * @param showItemMap
	 * @param colspanMap
	 * @return
	 */
	private static Row createHeaderRow(Sheet sheet,HSSFCellStyle  style, Map<String, String> showItemMap, 
			Map<String, Integer> colspanMap,Map<String,Boolean> systemMap,ZyyTeacherPerformanceQuery query) {
		String[] firstRowHead = {"出入科统计","教学活动统计","评价统计","登记手册、病历统计","出科考试统计"};
		//第一行
		Row row = sheet.createRow((int) 0);
		row.setHeight((short) 600);
		Cell cell = null;
		for (int i = 0; i < 6; i++) {
			cell = row.createCell(i);    
	        cell.setCellValue(invoiceExcelHeader[i]);  
	        cell.setCellStyle(style);
		}
		Integer enterLeaveColspan = colspanMap.get("enterLeaveColspan");
		Integer evaluateColspan = colspanMap.get("evaluateColspan");
		Integer handCaseColspan = colspanMap.get("handCaseColspan");
		Integer leaveColspan = colspanMap.get("leaveColspan");
		Integer activityColspan = colspanMap.get("activityColspan");
		
		Boolean xinjiangSystem = systemMap.get("xinjiangSystem");
		Boolean yunnanSystem = systemMap.get("yunnanSystem");
		
		int nowColumnIndex = 6;
		Integer zero =Integer.valueOf(0);
		//判断合并的列   入科教育
		if (!zero.equals(enterLeaveColspan)){
			cell = row.createCell(nowColumnIndex);
			nowColumnIndex += enterLeaveColspan;
			cell.setCellValue(firstRowHead[0]);  
			cell.setCellStyle(style);
		}
		//判断合并的列   教学活动
		if (!zero.equals(activityColspan)){
			cell = row.createCell(nowColumnIndex);    
			nowColumnIndex += activityColspan;
			cell.setCellValue(firstRowHead[1]);  
			cell.setCellStyle(style);
		}
		//判断合并的列   评价统计
		if (!zero.equals(evaluateColspan)){
			cell = row.createCell(nowColumnIndex);    
			nowColumnIndex += evaluateColspan;
			cell.setCellValue(firstRowHead[2]);  
			cell.setCellStyle(style);
		}
		//判断合并的列   登记手册病例
		if (!zero.equals(handCaseColspan)){
			cell = row.createCell(nowColumnIndex);    
			nowColumnIndex += handCaseColspan;
			cell.setCellValue(firstRowHead[3]);  
			cell.setCellStyle(style);
		}
		//判断合并的列   出科考试统计
		if (!zero.equals(leaveColspan)){
			cell = row.createCell(nowColumnIndex);    
			nowColumnIndex += leaveColspan;
			cell.setCellValue(firstRowHead[4]);  
			cell.setCellStyle(style);
		}

        //第二行
        row = sheet.createRow((int) 1);
        row.setHeight((short) 600);
		//获取头信息
		List<String> headerList = getHeaderTitleList(query,xinjiangSystem,yunnanSystem);
		 
		List<String> showHeaderList = checkExistItem2(headerList, showItemMap);
		
        for (int i = 0; i < showHeaderList.size(); i++) {
    		//获取每一个单元格
    		cell = row.createCell(i);    
    		//给单元格赋值
    		cell.setCellValue(showHeaderList.get(i));  
    		cell.setCellStyle(style);
        }  
		return row;
	}
	
	/**
	 * 判断是否显示这列
	 * @param i
	 * @param showItemMap
	 * @return
	 * <AUTHOR>
	 * @date 2019-1-17下午5:52:17
	 */
	private static List<String> checkExistItem2(List<String> headerList, Map<String, String> showItemMap) {
			
		if(!showItemMap.containsKey("1")) headerList.remove("入科教育主持数量");
		if(!showItemMap.containsKey("2")) headerList.remove("出科合格率");
		if(!showItemMap.containsKey("3")) headerList.remove("合格出科人次/已轮转人次");
		if(!showItemMap.containsKey("4")) headerList.remove("组织次数");
		if(!showItemMap.containsKey("7")) headerList.remove("学员评价平均分");
		if(!showItemMap.containsKey("8")) headerList.remove("科室评价平均分");
		if(!showItemMap.containsKey("9")) headerList.remove("专业基地评价平均分");
		if(!showItemMap.containsKey("10")) headerList.remove("培训基地评价平均分");
		if(!showItemMap.containsKey("5")) headerList.remove("登记手册审核率");
		if(!showItemMap.containsKey("6")) headerList.remove("病历审核率");
		if(!showItemMap.containsKey("11")) headerList.remove("出科考试合格率");
		if(!showItemMap.containsKey("12")) headerList.remove("出科考试平均分");
		if(!showItemMap.containsKey("13")) headerList.remove("主持教学活动次数");
		if(!showItemMap.containsKey("14")) headerList.remove("我应该评价/已评价");
		return headerList;
	} 
	
	//动态构造表头
	private static List<String> getHeaderTitleList(ZyyTeacherPerformanceQuery query,Boolean xinjiangSystem,Boolean yunnanSystem) {
		List<String> headerTitleList = new ArrayList<String>();
		List<ZyyActivityTypeVO> activityTypeList = query.getActivityTypeList();
		int len = invoiceExcelHeader.length;
		for (int i = 0; i < len; i++) {
			headerTitleList.add(invoiceExcelHeader[i]);
			if (i == 7){
				//开始插入活动相关的内容
				if (!CollectionUtils.isEmpty(activityTypeList)){
					for (ZyyActivityTypeVO item : activityTypeList) {
						if (item != null){
							Integer checkStatus = item.getCheckStatus();
							if (checkStatus != null && checkStatus.intValue() == 1){
								headerTitleList.add(item.getTypeName()+"组织次数");
							}
						}
					}
				}
			}
			
			if (xinjiangSystem){
				//8 = "主持教学活动次数",12 = "我应该评价/已评价"
				if (i == 8){
					headerTitleList.add("主持教学活动次数");
				}else if (i == 12){
					headerTitleList.add("我应该评价/已评价");
				} 
			} else {
				if (!yunnanSystem){
					//12 = "我应该评价/已评价"
					if (i == 12){
						headerTitleList.add("我应该评价/已评价");
					}
				}  
			}
		}
		return headerTitleList;
	} 
	
}
