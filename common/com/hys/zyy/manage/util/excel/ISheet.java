package com.hys.zyy.manage.util.excel;

/**
 * Excel
 * <AUTHOR>
 *
 */
public interface ISheet<T> extends IDelegate<T> {
	
	public long size();
	
	public String label();
	
	public IRow row(long index);
	
	public IRow[] rows();
	
	public IRow[] rows(long start, long end);
	
	public IWorkbook workbook();
	
	public ISheet foreach(int row, IRowCallbak callback) throws Exception;

	public ICell createCell(int row, int col);

	public ICell cell(int row, int column);

	public int index();
	
}