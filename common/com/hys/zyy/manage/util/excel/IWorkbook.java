package com.hys.zyy.manage.util.excel;


/**
 * Excel������
 * <AUTHOR>
 *
 */
public interface IWorkbook<T> extends IDelegate<T> {
	
	public T getDelegate();
	
	// Readable
	
	public ISheet sheet(int index);
	
	public ISheet[] sheets();
	
	public int getNumberOfSheets();
	
	// Writable
	
	public ISheet createSheet(int pos, String sheetname);
	
	public void write();
	
	public void writeTo(String filename);
	
}
