package com.hys.zyy.manage.util.excel;

import java.text.DecimalFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFDateUtil;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;

import com.hys.zyy.manage.util.DateUtil;

public class POIUtil {
	
	/**
	 * 计算字符串中中文字符的数量 参见 <a hrft= "https://www.cnblogs.com/straybirds/p/6392306.html">《汉字unicode编码范围》</a>
	 * @param input
	 * @return
	 */
	private static int chineseCharCountOf(String input) {
		int count = 0;// 汉字数量
		if (null != input) {
			String regEx = "[\\u4e00-\\u9fa5]";
			Pattern p = Pattern.compile(regEx);
			Matcher m = p.matcher(input);
			int len = m.groupCount();
			// 获取汉字个数
			while (m.find()) {
				for (int i = 0; i <= len; i++) {
					count = count + 1;
				}
			}
		}
		return count;
	}
	
	/**
	 * 自动调整列表宽度适应中文字符串
	 * @param sheet
	 * @param startColumnNum 要调整的起始列表号
	 * @param size  要调整的列表数量
	 */
	public static void autoColumnWidthForChineseChar(Sheet sheet, int startColumnNum, int size) {    
	    for (int columnNum = 0; columnNum < size; columnNum++) {
	    	/** 调整每一列宽度 */ 
			sheet.autoSizeColumn(columnNum);
	        /** 获取列宽 */
	        final int columnWidth = sheet.getColumnWidth(columnNum);
	        if(columnNum >= 256*256 ){
	        	/** 列宽已经超过最大列宽则放弃当前列遍历 */
	        	continue;
	        }
	        /** 新的列宽 */
	        int newWidth = columnWidth;
	        /** 遍历所有的行,查找有汉字的列计算新的最大列宽 */
	        for (int rowNum = 0; rowNum <= sheet.getLastRowNum(); rowNum++) {
	            Row currentRow;
	            if (sheet.getRow(rowNum) == null) {
	                continue;
	            } else {
	                currentRow = sheet.getRow(rowNum);
	            }
	            if (currentRow.getCell(columnNum) != null) {
	                Cell currentCell = currentRow.getCell(columnNum);
	                if (currentCell.getCellType() == HSSFCell.CELL_TYPE_STRING) {
	                	String value = currentCell.getStringCellValue();
	                	/** 计算字符串中中文字符的数量 */
	                    //int count = chineseCharCountOf(value);
	                    /**在该列字符长度的基础上加上汉字个数计算列宽 */
	                    //int length = value.length()*256+count*256*2;
	                	/** 使用字符串的字节长度计算列宽 */
	                    int length = value.getBytes().length*256;
	                    if (newWidth < length && length < 256*256) {
	                    	newWidth = length;
	                    }
	                }
	            }
	        }
	        if(newWidth != columnWidth){
	        	//设置列宽
	        	sheet.setColumnWidth(columnNum, newWidth);
	        }
	    }
	}
	
	public static String getCellValue(HSSFCell cell) {
		if(cell == null){
			return "";
		}
		switch (cell.getCellType()) {
			case HSSFCell.CELL_TYPE_STRING:
				return cell.getStringCellValue();
			case HSSFCell.CELL_TYPE_NUMERIC:
				if (HSSFDateUtil.isCellDateFormatted(cell)) {
					double d = cell.getNumericCellValue();
					Date date = HSSFDateUtil.getJavaDate(d);
					return DateUtil.format(date, DateUtil.FORMAT_SHORT);
				} else {
					return new DecimalFormat("0.0").format(cell.getNumericCellValue());
				}
		}
		return "";
	}
	
	public static Map<String, HSSFCellStyle> getCellStyle(HSSFWorkbook workbook) {
		Map<String, HSSFCellStyle> cellStyleMap = new HashMap<String, HSSFCellStyle>();
		// 标题样式
		HSSFCellStyle titleStyle = workbook.createCellStyle();
		titleStyle.setAlignment(HSSFCellStyle.ALIGN_CENTER); // 水平居中
		titleStyle.setVerticalAlignment(CellStyle.VERTICAL_CENTER);// 垂直居中
		titleStyle.setBorderLeft(HSSFCellStyle.BORDER_THIN);// 左边框
		titleStyle.setBorderTop(HSSFCellStyle.BORDER_THIN);// 上边框
		titleStyle.setBorderRight(HSSFCellStyle.BORDER_THIN);// 右边框
		HSSFFont titleFont = workbook.createFont();
		titleFont.setFontName("Arial");
		titleFont.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);// 粗体显示
		titleFont.setFontHeightInPoints((short) 18); // 字号
		titleStyle.setFont(titleFont);
		
		// 表头样式
		HSSFCellStyle headStyle = workbook.createCellStyle();
		// headStyle.setFillForegroundColor(IndexedColors.BLUE.LIGHT_CORNFLOWER_BLUE.getIndex());
		headStyle.setFillForegroundColor(IndexedColors.LIGHT_BLUE.getIndex());
		headStyle.setFillPattern(CellStyle.SOLID_FOREGROUND);
		headStyle.setAlignment(HSSFCellStyle.ALIGN_CENTER);
		headStyle.setVerticalAlignment(CellStyle.VERTICAL_CENTER);
		// 边框
		headStyle.setBorderBottom(HSSFCellStyle.BORDER_THIN); // 下边框
		headStyle.setBorderLeft(HSSFCellStyle.BORDER_THIN);// 左边框
		headStyle.setBorderTop(HSSFCellStyle.BORDER_THIN);// 上边框
		headStyle.setBorderRight(HSSFCellStyle.BORDER_THIN);// 右边框
		HSSFFont headFont = workbook.createFont();
		headFont.setColor(HSSFColor.WHITE.index); // 字体颜色
		headFont.setFontName("Arial");
		headFont.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
		headFont.setFontHeightInPoints((short) 10);
		headStyle.setFont(headFont);
		
		// 提示样式
		HSSFCellStyle tipStyle = workbook.createCellStyle();
		tipStyle.setAlignment(HSSFCellStyle.ALIGN_RIGHT);
		tipStyle.setVerticalAlignment(CellStyle.VERTICAL_CENTER);
		// 边框
		tipStyle.setBorderBottom(HSSFCellStyle.BORDER_THIN); // 下边框
		tipStyle.setBorderLeft(HSSFCellStyle.BORDER_THIN);// 左边框
		tipStyle.setBorderRight(HSSFCellStyle.BORDER_THIN);// 右边框
		HSSFFont tipFont = workbook.createFont();
		tipFont.setColor(HSSFColor.RED.index); // 字体颜色
		tipFont.setFontName("Arial");
		tipFont.setFontHeightInPoints((short) 10);
		tipStyle.setFont(tipFont);
		
		// 数据表格样式
		HSSFCellStyle dataStyle = workbook.createCellStyle();
		dataStyle.setAlignment(HSSFCellStyle.ALIGN_CENTER);
		dataStyle.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
		dataStyle.setBorderBottom(HSSFCellStyle.BORDER_THIN); // 下边框
		dataStyle.setBorderLeft(HSSFCellStyle.BORDER_THIN);// 左边框
		dataStyle.setBorderTop(HSSFCellStyle.BORDER_THIN);// 上边框
		dataStyle.setBorderRight(HSSFCellStyle.BORDER_THIN);// 右边框
		
		// 数据表格样式
		HSSFCellStyle dataStyleNoLocked = workbook.createCellStyle();
		dataStyleNoLocked.setAlignment(HSSFCellStyle.ALIGN_LEFT);
		dataStyleNoLocked.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
		dataStyleNoLocked.setBorderBottom(HSSFCellStyle.BORDER_THIN); // 下边框
		dataStyleNoLocked.setBorderLeft(HSSFCellStyle.BORDER_THIN);// 左边框
		dataStyleNoLocked.setBorderTop(HSSFCellStyle.BORDER_THIN);// 上边框
		dataStyleNoLocked.setBorderRight(HSSFCellStyle.BORDER_THIN);// 右边框
		
		// 成绩表格样式
		HSSFCellStyle scoreStyle = workbook.createCellStyle();
		scoreStyle.setDataFormat(workbook.createDataFormat().getFormat("0.0")); // 设置保留的小数位数
		scoreStyle.setAlignment(HSSFCellStyle.ALIGN_CENTER);
		scoreStyle.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
		scoreStyle.setBorderBottom(HSSFCellStyle.BORDER_THIN); // 下边框
		scoreStyle.setBorderLeft(HSSFCellStyle.BORDER_THIN);// 左边框
		scoreStyle.setBorderTop(HSSFCellStyle.BORDER_THIN);// 上边框
		scoreStyle.setBorderRight(HSSFCellStyle.BORDER_THIN);// 右边框
		scoreStyle.setLocked(false); // 不锁定单元格
		
		cellStyleMap.put("titleStyle", titleStyle);
		cellStyleMap.put("headStyle", headStyle);
		cellStyleMap.put("tipStyle", tipStyle);
		cellStyleMap.put("dataStyle", dataStyle);
		cellStyleMap.put("dataStyleNoLocked", dataStyleNoLocked);
		cellStyleMap.put("scoreStyle", scoreStyle);
		return cellStyleMap;
	}

}





























