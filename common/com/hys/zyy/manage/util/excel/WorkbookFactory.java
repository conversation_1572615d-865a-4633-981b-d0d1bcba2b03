package com.hys.zyy.manage.util.excel;

import java.io.File;
import java.io.InputStream;

import com.hys.zyy.manage.exception.FrameworkRuntimeException;
import com.hys.zyy.manage.util.excel.poi.WorkbookAdapter;

public class WorkbookFactory {
	
	public static IWorkbook createWorkbook(String filename) {
		if(filename == null || filename.lastIndexOf('.') == -1)
			throw new IllegalArgumentException();
		return createPoiWorkbook(filename);
	}
	
	public static IWorkbook createPoiWorkbook(String filename) {
		if(!validate(filename))
			throw new IllegalArgumentException();
		File file = new File(filename);
		return new WorkbookAdapter(file);
	}
	
	public static IWorkbook createPoiWorkbook(String filename, InputStream is) {
		if(!validate(filename))
			throw new IllegalArgumentException();
		return new WorkbookAdapter(filename, is);
	}
	
	public static IWorkbook createJxlWorkbook(String filename) {
		throw new FrameworkRuntimeException("not support");
	}
	
	public static boolean validate(String filename) {
		if(filename == null || filename.lastIndexOf('.') == -1)
			return false;
		return true;
	}

}
