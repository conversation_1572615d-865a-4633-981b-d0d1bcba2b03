package com.hys.zyy.manage.util.excel.poi;

import org.apache.poi.ss.usermodel.Cell;

import com.hys.zyy.manage.util.excel.ICell;
import com.hys.zyy.manage.util.excel.IRow;
import com.hys.zyy.manage.util.excel.ISheet;

@SuppressWarnings("unchecked")
public class CellAdapter implements ICell<Cell> {
	
	private Cell delegate;
	
	private IRow row;
	
	public final static ICell NULL = new ICell(){

		@Override
		public int colNumber() {
			return -1;
		}

		@Override
		public IRow row() {
			return RowAdapter.NULL;
		}

		@Override
		public int rowNumber() {
			return -1;
		}

		@Override
		public ISheet sheet() {
			return SheetAdapter.NULL;
		}

		@Override
		public int type() {
			return 0;
		}

		@Override
		public String value() {
			return "";
		}

		@Override
		public Object getDelegate() {
			return null;
		}

		@Override
		public void value(String values) {
		}

	};
	
	public CellAdapter(IRow row, Cell cell) {
		this.row = row;
		this.delegate = cell;
	}

	@Override
	public int colNumber() {
		if(delegate != null)
			return delegate.getColumnIndex();
		return -1;
	}

	@Override
	public IRow row() {
		return row;
	}

	@Override
	public int rowNumber() {
		if(delegate != null)
			return delegate.getRowIndex();
		return -1;
	}

	@Override
	public ISheet sheet() {
		return row.sheet();
	}

	@Override
	public int type() {
		if(delegate != null)
			return delegate.getCellType();
		return 0;
	}

	@Override
	public String value() {
		if(delegate != null) {
			if(Cell.CELL_TYPE_FORMULA == delegate.getCellType())
				return delegate.getStringCellValue();
			return delegate.toString();
		}
		return "";
	}
	
	@Override
	public void value(String values) {
		if(delegate != null && values != null)
			delegate.setCellValue(values);
	}

	@Override
	public Cell getDelegate() {
		return delegate;
	}

}
