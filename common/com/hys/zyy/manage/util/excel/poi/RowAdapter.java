package com.hys.zyy.manage.util.excel.poi;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;

import com.hys.zyy.manage.util.excel.ICell;
import com.hys.zyy.manage.util.excel.IRow;
import com.hys.zyy.manage.util.excel.ISheet;

@SuppressWarnings("unchecked")
public class RowAdapter implements IRow<Row> {
	
	private Row delegate;
	
	private ISheet sheet;
	
	public final static IRow NULL = new IRow() {

		@Override
		public ICell cell(long index) {
			return CellAdapter.NULL;
		}

		@Override
		public ICell[] cells() {
			return new ICell[0];
		}

		@Override
		public ICell[] cells(long start, long end) {
			return new ICell[0];
		}

		@Override
		public int number() {
			return -1;
		}

		@Override
		public ISheet sheet() {
			return SheetAdapter.NULL;
		}

		@Override
		public Object getDelegate() {
			return null;
		}

		@Override
		public int size() {
			return 0;
		}

		@Override
		public ICell createCell(int cn) {
			return CellAdapter.NULL;
		}
		
	};

	public RowAdapter(ISheet sheet, Row row) {
		this.sheet = sheet;
		this.delegate = row;
	}
	
	@Override
	public ICell cell(long index) {
		if(delegate == null || index < 0 || index >= size())
			return CellAdapter.NULL;
		ICell cell = new CellAdapter(this, delegate.getCell((int) index));
		return cell;
	}

	@Override
	public ICell[] cells() {
		if(delegate == null)
			return new ICell[0];
		ICell[] cells = new ICell[size()];
		for(int index = 0; index < size(); index++)
			cells[index] = new CellAdapter(this, delegate.getCell(index));
		return cells;
	}

	@Override
	public ICell[] cells(long start, long end) {
		if(delegate == null || start < 0 || end < 0 || start >= size() || end >= size() || end < start)
			return new ICell[0];
		ICell[] cells = new ICell[(int) (end - start + 1)];
		for(int index = (int) start, j = 0; index <= end; index++, j++)
			cells[j] = new CellAdapter(this, delegate.getCell(index));
		return cells;
	}

	@Override
	public int number() {
		if(delegate != null)
			return delegate.getRowNum();
		return -1;
	}
	
	@Override
	public int size() {
		if(delegate != null)
			return delegate.getPhysicalNumberOfCells();
		return 0;
	}

	@Override
	public ISheet sheet() {
		return sheet;
	}
	
	@Override
	public ICell createCell(int cn) {
		if(delegate == null)
			return CellAdapter.NULL;
		Cell c = delegate.getCell(cn);
		if(c == null)
			c = delegate.createCell(cn);
		ICell cell = new CellAdapter(this, c);
		return cell;
	}

	@Override
	public Row getDelegate() {
		return delegate;
	}

}
