package com.hys.zyy.manage.util.excel.poi;

import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;

import com.hys.zyy.manage.util.excel.ICell;
import com.hys.zyy.manage.util.excel.IRow;
import com.hys.zyy.manage.util.excel.IRowCallbak;
import com.hys.zyy.manage.util.excel.ISheet;
import com.hys.zyy.manage.util.excel.IWorkbook;

@SuppressWarnings("unchecked")
public class SheetAdapter implements ISheet<Sheet> {

	private Sheet delegate;
	
	private IWorkbook wb;
	
	private int index;
	
	public final static ISheet NULL = new ISheet(){

		@Override
		public IRow row(long index) {
			return RowAdapter.NULL;
		}

		@Override
		public IRow[] rows() {
			return new IRow[0];
		}

		@Override
		public IRow[] rows(long start, long end) {
			return new IRow[0];
		}

		@Override
		public long size() {
			return 0;
		}

		@Override
		public IWorkbook workbook() {
			return WorkbookAdapter.NULl;
		}

		@Override
		public ISheet foreach(int row, IRowCallbak callback) {
			return this;
		}
		
		@Override
		public Object getDelegate() {
			return null;
		}

		@Override
		public ICell createCell(int row, int col) {
			return CellAdapter.NULL;
		}
		
		@Override
		public ICell cell(int row, int column) {
			return CellAdapter.NULL;
		}

		@Override
		public String label() {
			return "";
		}
		
		@Override
		public int index() {
			return -1;
		}
	};
	
	public SheetAdapter(IWorkbook wb, int index, Sheet sheet) {
		this.wb = wb;
		this.index = index;
		this.delegate = sheet;
	}
	
	public SheetAdapter(WorkbookAdapter wb, Sheet sheet) {
		this.wb = wb;
		this.delegate = sheet;
	}

	@Override
	public String label() {
		return this.delegate.getSheetName();
	}
	
	@Override
	public IRow row(long index) {
		if(delegate == null || index < 0 || index >= size())
			return RowAdapter.NULL;
		IRow row = new RowAdapter(this, delegate.getRow((int) index));
		return row;
	}

	@Override
	public IRow[] rows() {
		if(delegate == null)
			return new IRow[0];
		IRow[] rows = new IRow[(int)size()];
		for(int index = 0; index < size(); index++)
			rows[index] = new RowAdapter(this, delegate.getRow(index));
		return rows;
	}

	@Override
	public IRow[] rows(long start, long end) {
		if(delegate == null || start < 0 || end < 0 || start >= size() || end >= size() || end < start)
			return new IRow[0];
		IRow[] rows = new IRow[(int)(end - start + 1)];
		for(int index = (int) start, j = 0; index <= end; index++, j++)
			rows[j] = new RowAdapter(this, delegate.getRow(index));
		return rows;
	}

	@Override
	public long size() {
		if(delegate != null)
			return delegate.getPhysicalNumberOfRows();
		return 0;
	}

	@Override
	public IWorkbook workbook() {
		return wb;
	}
	
	@Override
	public ISheet foreach(int row, IRowCallbak callback) throws Exception {
		if(row < 0 || row >= size())
			row = 0;
		if(callback != null) {
			IRow[] rows = rows();
			for(int i = row; i < rows.length; i++)
				callback.on(rows[i], rows[i].number());
		}
		return this;
	}
	
	@Override
	public ICell createCell(int rn, int cn) {
		if(delegate == null)
			return CellAdapter.NULL;
		Row r = delegate.getRow(rn);
		if(r == null)
			r = delegate.createRow(rn);
		IRow row = new RowAdapter(this, r);
		ICell cell = row.createCell(cn);
		return cell;
	}
	
	@Override
	public ICell cell(int row, int column) {
		IRow r = row(row);
		return r.cell(column);
	}

	@Override
	public Sheet getDelegate() {
		return delegate;
	}
	
	@Override
	public int index() {
		return this.index;
	}
}
