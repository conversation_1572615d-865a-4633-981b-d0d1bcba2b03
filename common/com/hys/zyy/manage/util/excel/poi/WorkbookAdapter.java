package com.hys.zyy.manage.util.excel.poi;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;

import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import com.hys.zyy.manage.util.excel.ISheet;
import com.hys.zyy.manage.util.excel.IWorkbook;

@SuppressWarnings("unchecked")
public class WorkbookAdapter implements IWorkbook<Workbook> {
	
	public static final String EXTENSION_2003 = ".xls";

	public static final String EXTENSION_2007 = ".xlsx";
	
	private Workbook delegate;
	
	private File out;
	
	public final static IWorkbook NULl = new IWorkbook() {

		@Override
		public Object getDelegate() {
			return null;
		}

		@Override
		public int getNumberOfSheets() {
			return 0;
		}

		@Override
		public ISheet sheet(int index) {
			return SheetAdapter.NULL;
		}

		@Override
		public ISheet[] sheets() {
			return new ISheet[0];
		}

		@Override
		public ISheet createSheet(int pos, String sheetname) {
			return SheetAdapter.NULL;
		}

		@Override
		public void write() {
		}

		@Override
		public void writeTo(String filename) {
		}
		
	};
	
	public WorkbookAdapter(File file) {
		this.delegate = createWorkbook(file);
		if(delegate == null)
			throw new IllegalArgumentException();
		this.out = file;
	}
	
	public WorkbookAdapter(String filename, InputStream is) {
		this.delegate = createWorkbook(filename, is);
		if(delegate == null)
			throw new IllegalArgumentException();
		try {
			this.out = File.createTempFile(String.valueOf(System.currentTimeMillis()), "temp");
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	@Override
	public int getNumberOfSheets() {
		return delegate.getNumberOfSheets();
	}

	@Override
	public ISheet sheet(int index) {
		if(index < 0 || index >= getNumberOfSheets())
			return SheetAdapter.NULL;
		ISheet sheet = new SheetAdapter(this, index, delegate.getSheetAt(index));
		return sheet;
	}

	@Override
	public ISheet[] sheets() {
		ISheet[] sheets = new ISheet[getNumberOfSheets()];
		for(int index = 0; index < getNumberOfSheets(); index++)
			sheets[index] = new SheetAdapter(this, index, delegate.getSheetAt(index));
		return sheets;
	}
	
	@Override
	public ISheet createSheet(int pos, String sheetname) {
		if(delegate == null)
			return SheetAdapter.NULL;
		Sheet st = delegate.getSheet(sheetname);
		if(st == null)
			st = delegate.createSheet(sheetname);
		ISheet sheet = new SheetAdapter(this, st);
		return sheet;
	}

	@Override
	public void write() {
		if(delegate == null || out == null)
			return;
		try {
			out.createNewFile();
		} catch (IOException e) {
			e.printStackTrace();
		}
		
		BufferedOutputStream bof = null;
		try {
			bof = new BufferedOutputStream(new FileOutputStream(out));
		} catch (FileNotFoundException e) {
		}
		
		try {
			delegate.write(bof);
		} catch (IOException e) {
			e.printStackTrace();
		} 
		finally {
			try {
				if(bof != null)
					bof.close();
			} catch (IOException e) {
				
			}
		}
	}
	
	@Override
	public void writeTo(String filename) {
		this.out = new File(filename);
		this.write();
	}
	
	@Override
	public Workbook getDelegate() {
		return delegate;
	}
	
	private Workbook createWorkbook(File file) {
		String filename = file.getName();
		try
		{
			if(filename.endsWith(EXTENSION_2003)) {
				if(file.exists())
					return new HSSFWorkbook(new BufferedInputStream(new FileInputStream(file)));
				else
					return new HSSFWorkbook();
			}
			else if(filename.endsWith(EXTENSION_2007)) {
				if(file.exists())
					return new XSSFWorkbook(new BufferedInputStream(new FileInputStream(file)));
				else
					return new XSSFWorkbook();
			}
		}
		catch(Exception e) {
			e.printStackTrace();
		}
		return null;
	}
	
	
	private Workbook createWorkbook(String filename, InputStream is) {
		try
		{
			if(filename.endsWith(EXTENSION_2003)) {
				return new HSSFWorkbook(is);
			}
			else if(filename.endsWith(EXTENSION_2007)) {
				return new XSSFWorkbook(is);
			}
		}
		catch(Exception e) {
			e.printStackTrace();
		}
		return null;
	}
}
