package com.hys.zyy.manage.util.html;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

import org.apache.poi.poifs.filesystem.POIFSFileSystem;

/**
 * String htmlStr = HtmlGenerator.generate("ehandbook.ftl", variables);
 * 
 * <AUTHOR>
 * @date 2020-3-9下午3:12:14
 */
public class HtmlToWordUtil {
	
	public static void main(String[] args) {
		 String body =  " <div> <a style='color:red;' >测试的内容1111</a> <br /> <img src='http://t9.baidu.com/it/u=583874135,70653437&fm=79&app=86&f=JPEG?w=3607&h=2408'  /> </div> ";
		String content = "<html><head><style></style></head><body>" + body +  "</body></html>";
		 String wordPath = "D:\\testword";
		 String fileName = "测试导出44";
		 File file = createWord(wordPath, fileName, content);
	}

	public static File createWord(String outPutPath,String fileName,String content) {
		File tmpFile = null;
		OutputStream os = null;
		InputStream is = null;
		try {
			File file = new File(outPutPath);
			if (!file.exists()) {
		    	file.setWritable(true, false);
		        file.mkdirs();
		      }
			tmpFile = File.createTempFile(fileName, ".docx", new File(outPutPath));
			is = new ByteArrayInputStream(content.getBytes("utf-8"));
			os = new FileOutputStream(tmpFile);
			POIFSFileSystem fs = new POIFSFileSystem();
			// 对应于org.apache.poi.hdf.extractor.WordDocument
			fs.createDocument(is, "WordDocument");
			fs.writeFilesystem(os);
			 
		} catch (Exception e) {
			e.printStackTrace();
		} finally{
			try {
				if (os != null){
					os.close();
				}
				if (is != null){
					is.close();
				}
				// fs.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		return tmpFile;
	}

	public static File createWordBatch(String outPutPath,String fileName,String content) {
		return createWordBatch(outPutPath,fileName,content,"docx");
	}
	public static File createWordBatch(String outPutPath,String fileName,String content,String format) {
		File tmpFile = null;
		OutputStream os = null;
		InputStream is = null;
		try {
			File file = new File(outPutPath);
			if (!file.exists()) {
		    	file.setWritable(true, false);
		        file.mkdirs();
		    }
			 
			tmpFile =  new File(outPutPath, fileName+"."+format);
			is = new ByteArrayInputStream(content.getBytes("utf-8"));
			os = new FileOutputStream(tmpFile);
			POIFSFileSystem fs = new POIFSFileSystem();
			// 对应于org.apache.poi.hdf.extractor.WordDocument
			fs.createDocument(is, "WordDocument");
			fs.writeFilesystem(os);
			 
		} catch (Exception e) {
			e.printStackTrace();
		} finally{
			try {
				if (os != null){
					os.close();
				}
				if (is != null){
					is.close();
				}
				// fs.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		return tmpFile;
	}
	
}
