package com.hys.zyy.manage.util.html;

import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

public class Tag extends Html
{
  private String tagName;
  private List classes;
  private Map styles;
  private Map attributes;
  private List subHtmlList;
  private List siblingHtmlList;

  public Tag()
  {
    this(null);
  }

  public Tag(String tagName)
  {
    this.tagName = tagName;
  }

  public String getTagName()
  {
    return this.tagName;
  }

  public Tag cls(String cls)
  {
    if (this.classes == null)
      this.classes = new ArrayList();
    this.classes.add(cls);
    return this;
  }

  public Tag css(String key, String value)
  {
    if (this.styles == null)
      this.styles = new HashMap();
    this.styles.put(key, value);
    return this;
  }

  public Tag css(Map styles)
  {
    if (this.styles == null)
      this.styles = new HashMap();
    this.styles.putAll(styles);
    return this;
  }

  public boolean hasPositionCSS()
  {
    if (this.styles == null)
      return false;
    return this.styles.containsKey("position");
  }

  public Tag attr(String key, String value)
  {
    if (this.attributes == null)
      this.attributes = new HashMap();
    this.attributes.put(key, value);
    return this;
  }

  public Tag sub(Html html)
  {
    if (this.subHtmlList == null)
      this.subHtmlList = new ArrayList();
    this.subHtmlList.add(html);
    return this;
  }

  public Tag sub(String text)
  {
    return sub(new TextHtml(text));
  }

  public Tag sibling(Html html)
  {
    if (this.siblingHtmlList == null)
      this.siblingHtmlList = new ArrayList();
    this.siblingHtmlList.add(html);
    return this;
  }

  public void writeHtml(PrintWriter pw)
  {
    if (this.tagName == null)
    {
      int i = 0;
      int j = this.subHtmlList == null ? 0 : this.subHtmlList.size();
      while (i < j)
      {
        ((Html)this.subHtmlList.get(i)).writeHtml(pw);
        i++;
      }
      return;
    }
    pw.write('<' + this.tagName);
    pw.write(classBuffer().toString());
    pw.write(styleBuffer().toString());
    pw.write(attributeBuffer().toString());
    if ((this.tagName.equalsIgnoreCase("COL")) && ((this.subHtmlList == null) || (this.subHtmlList.size() == 0)))
    {
    	pw.write(" />");
      return;
    }
    pw.write(">");
    int i = 0;
    int j = this.subHtmlList == null ? 0 : this.subHtmlList.size();
    while (i < j)
    {
      ((Html)this.subHtmlList.get(i)).writeHtml(pw);
      i++;
    }
    pw.write("</" + this.tagName + '>');
    if (this.siblingHtmlList != null)
    {
      i = 0;
      j = this.siblingHtmlList.size();
      while (i < j)
      {
        ((Html)this.siblingHtmlList.get(i)).writeHtml(pw);
        i++;
      }
    }
  }

  public StringBuffer classBuffer()
  {
    StringBuffer sb = new StringBuffer();
    sb.append(" class=\"");
    sb.append(getClassList());
    sb.append('"');
    return sb;
  }

  public StringBuffer getClassList()
  {
    StringBuffer sb = new StringBuffer();
    if ((this.classes != null) && (this.classes.size() > 0))
    {
      int i = this.classes.size();
      for (int j = 0; j < i - 1; j++)
      {
    	  sb.append(this.classes.get(j));
    	  sb.append(' ');
      }
      sb.append(this.classes.get(i - 1));
    }
    return sb;
  }

  private StringBuffer styleBuffer()
  {
    StringBuffer sb = new StringBuffer();
    if (this.styles != null)
    {
      StringBuffer sb1 = new StringBuffer();
      Iterator it = this.styles.entrySet().iterator();
      while (it.hasNext())
      {
        Map.Entry entry = (Map.Entry)it.next();
        sb1.append(entry.getKey()).append(':').append(entry.getValue()).append(';');
      }
      if (sb1.length() > 0)
    	  sb.append(" style=\"").append(sb1).append('"');
    }
    return sb;
  }

  private StringBuffer attributeBuffer()
  {
    StringBuffer sb = new StringBuffer();
    if (this.attributes != null)
    {
      Iterator it = this.attributes.entrySet().iterator();
      while (it.hasNext())
      {
        Map.Entry entry = (Map.Entry)it.next();
        sb.append(' ').append(entry.getKey()).append("=\"").append(HtmlUtils.attributeHtmlEncode(entry.getValue().toString())).append('"');
      }
    }
    return sb;
  }

  public String toString()
  {
    return toHtml();
  }

  public Object clone()
    throws CloneNotSupportedException
  {
    Tag tag = (Tag)super.clone();
    tag.attributes = this.attributes;
    tag.styles = this.styles;
    return tag;
  }
}