package com.hys.zyy.manage.util.redis;

import org.apache.commons.lang.StringUtils;

import com.alibaba.fastjson.JSON;
import com.hys.security.model.Account;
import com.hys.zyy.manage.constants.RedisConstants;
import com.hys.zyy.manage.model.ZyyUserExtendVO;

/**
 * app的redis数据相关工具类
 * <AUTHOR>
 * @date 2018-12-27下午4:24:24
 */
public class RedisAppUtil {
	
	//1小时
	private static final int EXPIRE_SECONDS = 3600;
	
	/**
	 * 保存账户信息
	 * @param sessionId
	 * @param account
	 * @param redisClientTemplate
	 * <AUTHOR>
	 * @date 2018-12-27下午5:12:55
	 */
	public static void saveAccountToRedis(String sessionId,Account account,RedisClientTemplate redisClientTemplate){
		if (StringUtils.isNotBlank(sessionId) && account != null){
			String accountJson = JSON.toJSONString(account);
			String key = RedisConstants.RCT_SECURITY_ACCOUNT_20181227+sessionId;
			redisClientTemplate.setex(key, EXPIRE_SECONDS, accountJson);
		}
	}
	/**
	 * 获取账户信息
	 * @param sessionId
	 * @param redisClientTemplate
	 * @return
	 * <AUTHOR>
	 * @date 2018-12-27下午5:12:39
	 */
	public static Account getAccountFromRedis(String sessionId,RedisClientTemplate redisClientTemplate){
		if (StringUtils.isBlank(sessionId)){
			return null;
		}
		String key = RedisConstants.RCT_SECURITY_ACCOUNT_20181227+sessionId;
		String accountJson = redisClientTemplate.get(key);
		if (StringUtils.isBlank(accountJson)){
			return null;
		}
		return JSON.parseObject(accountJson,Account.class);
	}
	
	/**
	 * 保存用户详细信息
	 * @param sessionId
	 * @param zyyUserExtendVO
	 * @param redisClientTemplate
	 * <AUTHOR>
	 * @date 2018-12-27下午5:35:16
	 */
	public static void saveUserExtendToRedis(String sessionId,ZyyUserExtendVO zyyUserExtendVO,RedisClientTemplate redisClientTemplate){
		if (StringUtils.isNotBlank(sessionId) && zyyUserExtendVO != null){
			String userJson = JSON.toJSONString(zyyUserExtendVO);
			String key = RedisConstants.RCT_USER_EXTEND_20181227+sessionId;
			redisClientTemplate.setex(key, EXPIRE_SECONDS, userJson);
		}
	}
	
	/**
	 * 获取用户信息
	 * @param sessionId
	 * @param redisClientTemplate
	 * @return
	 * <AUTHOR>
	 * @date 2018-12-27下午5:35:25
	 */
	public static ZyyUserExtendVO getUserExtendFromRedis(String sessionId,RedisClientTemplate redisClientTemplate){
		if (StringUtils.isBlank(sessionId)){
			return null;
		}
		String key = RedisConstants.RCT_USER_EXTEND_20181227+sessionId;
		String userJson = redisClientTemplate.get(key);
		if (StringUtils.isBlank(userJson)){
			return null;
		}
		return JSON.parseObject(userJson,ZyyUserExtendVO.class);
	}
	//删除
	public static void delUserExtend(String sessionId,RedisClientTemplate redisClientTemplate){
		if(StringUtils.isNotBlank(sessionId)){
			String key = RedisConstants.RCT_USER_EXTEND_20181227+sessionId;
			redisClientTemplate.del(key);
		}
	}
	//删除
	public static void delAccount(String sessionId,RedisClientTemplate redisClientTemplate){
		if(StringUtils.isNotBlank(sessionId)){
			String key = RedisConstants.RCT_SECURITY_ACCOUNT_20181227+sessionId;
			redisClientTemplate.del(key);
		}
	}
	
}
