package com.hys.zyy.manage.util.redis;

import redis.clients.jedis.Jedis;

/**
 * redis 数据源
 * <AUTHOR>
 *
 */
public interface RedisDataSource {

	/**
	 * 获得redis 客户端
	 * @return
	 */
	Jedis getRedisClient();
	
	/**
	 * 返还redis to pool
	 * @param jedis
	 */
	void returnResource(Jedis jedis);
	
	/**
	 * 返还redis to pool
	 * @param jedis
	 * @param broken
	 */
	void returnResource(Jedis jedis,boolean broken);
}
