package com.hys.zyy.manage.util.redis;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;

import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisSentinelPool;

@Repository("redisDataSource")
@Lazy
public class RedisDataSourceImpl implements RedisDataSource{
	
	private static final Logger log = LoggerFactory.getLogger(RedisDataSourceImpl.class);
	
	@Autowired
	private JedisSentinelPool jedisPool;

	@Override
	public Jedis getRedisClient() {
		try {
			Jedis jedis = jedisPool.getResource();
			return jedis;
		} catch (Exception e) {
			log.error("getRedisClient error",e);
		}
		return null;
	}

	@Override
	public void returnResource(Jedis jedis) {
		jedisPool.returnResource(jedis);
	}

	@Override
	public void returnResource(Jedis jedis, boolean broken) {
		if(broken){
			jedisPool.returnBrokenResource(jedis);
		}else{
			jedisPool.returnResource(jedis);
		}
	}
}
