package com.hys.zyy.manage.util.wabacus;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.List;

import javax.sql.DataSource;

import com.hys.zyy.manage.service.impl.ApplicationConfigurationManager;
import com.hys.zyy.manage.util.SpringUtils;
import com.hys.zyy.manage.util.StringPool;
import com.wabacus.config.Config;
import com.wabacus.config.database.datasource.AbsDataSource;
import com.wabacus.exception.WabacusRuntimeException;

/**
 * 统一系统的数据源
 * <AUTHOR>
 *
 */
public class ZyyDataSourceLocator extends AbsDataSource {

	private DataSource ds;
	
	private String dsName;			// 没用
	
	public ZyyDataSourceLocator() {
		
		this.ds = SpringUtils.getSingleBeanOfType(DataSource.class);
		
		// 在初始化数据源时，将webroot改掉
		Config.webroot = ApplicationConfigurationManager.getContextPath() + StringPool.FORWARD_SLASH;
		Config.showreport_url = Config.webroot + Config.showreport_url;
		Config.showreport_onpage_url = ApplicationConfigurationManager.getContextPath() + Config.showreport_onpage_url;
		Config.showreport_onword_url = ApplicationConfigurationManager.getContextPath() + Config.showreport_onword_url;
		Config.showreport_onrichexcel_url = ApplicationConfigurationManager.getContextPath() + Config.showreport_onrichexcel_url;
		Config.showreport_onplainexcel_url = ApplicationConfigurationManager.getContextPath() + Config.showreport_onplainexcel_url;
		Config.showreport_onpdf_url = ApplicationConfigurationManager.getContextPath() + Config.showreport_onpdf_url;
		
		// 处理一下全局的 ymPrompt.js 文件
		List<String> javascripts = Config.getInstance().getUlstGlobalJavascript();
		for(int i = 0; i < javascripts.size(); i++) {
			String js = javascripts.get(0);
			javascripts.set(i, ApplicationConfigurationManager.getContextPath() + js);
		} 
	}
	
	@Override
	public Connection getConnection() {
		try
        {
            return ds.getConnection();
        }catch(SQLException e)
        {
            throw new WabacusRuntimeException("获取"+this.getName()+"数据源的数据库连接失败",e);
        }
	}

	@Override
	public DataSource getDataSource() {
		return this.ds;
	}
	
	public void setDsName(String dsName) {
		this.dsName = dsName;
	}
	
}
