package com.hys.zyy.manage.web;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.beanutils.ConvertUtils;
import org.apache.commons.beanutils.converters.BigDecimalConverter;
import org.apache.commons.beanutils.converters.BooleanConverter;
import org.apache.commons.beanutils.converters.DateConverter;
import org.apache.commons.beanutils.converters.DoubleConverter;
import org.apache.commons.beanutils.converters.IntegerConverter;
import org.apache.commons.beanutils.converters.LongConverter;
import org.apache.commons.beanutils.converters.ShortConverter;

import com.hys.zyy.manage.util.StringPool;
import com.hys.zyy.manage.util.StringUtils;

/**
 * 请求工具类
 * <AUTHOR>
 */
public class RequestUtils {
	
	public static final String ATTR_USER_REAL_NAME = "session.user.realname";

	/**
	* 获取客户端IP地址，此方法用在proxy环境中
	* @param req
	* @return
	*/
	public static String getRemoteAddr(HttpServletRequest req) {
		String ip = req.getHeader("X-Forwarded-For");
		if(StringUtils.isNotBlank(ip)){
			String[] ips = StringUtils.split(ip,',');
			if(ips!=null){
				for(String tmpip : ips){
					if(StringUtils.isBlank(tmpip))
						continue;
					tmpip = tmpip.trim();
					if(isIPAddr(tmpip) && !tmpip.startsWith("10.") && !tmpip.startsWith("192.168.") && !"127.0.0.1".equals(tmpip)){
						return tmpip.trim();
					}
				}
			}
		}
		ip = req.getHeader("x-real-ip");
		if(isIPAddr(ip))
			return ip;
		ip = req.getRemoteAddr();
		if(ip.indexOf('.') == -1)
			ip = "127.0.0.1";
		return ip;
	}
	
	/**
	 * 获取访问的域名
	 * @param req
	 * @return
	 */
	public static String getDomainName(HttpServletRequest req) {
		return req.getServerName();
	}
	
	/**
	 * 获取用户方位URI
	 * @param req
	 * @return
	 */
	public static String getRequestURI(HttpServletRequest req) {
		return req.getRequestURI();
	}
	
	/**
	 * 获取全路径 
	 */
	public static String getRequestURL(HttpServletRequest req) {
		return req.getRequestURL().toString();
	}
	
	public static String getContextPath(HttpServletRequest req) {
		return req.getContextPath();
	}
	
	public static String getDomainNameAndProjectName(HttpServletRequest req) {
		String url = StringPool.BLANK;
		String requestURL = RequestUtils.getRequestURL(req);
		String contextPath = RequestUtils.getContextPath(req);
		if (StringUtils.isNotBlank(requestURL) && StringUtils.isNotBlank(contextPath))
			url = requestURL.split(contextPath)[0] + contextPath;
		return url;
	}
	
	public static String getDomainNameAndPort(HttpServletRequest req) {
		return req.getServerName() + ":" + req.getServerPort();
	}

	/**
	 * 获取用户代理信息
	 * @param req
	 * @return
	 */
	public static String getUserAgent(HttpServletRequest req) {
		String userAgent = req.getHeader("user-agent");
		if(userAgent == null)
			userAgent = "";
		return userAgent;
	}

	/**
	 * 用户由哪里跳转自该网页
	 * @param req
	 * @return
	 */
	public static String getRequestRefer(HttpServletRequest req) {
		String value = req.getHeader("referer");
		if(value == null)
			value = "";
		return value;
	}
	
	/**
	 * 获取客户端登陆用户
	 * @param req
	 * @return
	 */
	public static String getRequestClientUser(HttpServletRequest req) {
		String username = (String)req.getAttribute(ATTR_USER_REAL_NAME);
		if(StringUtils.isBlank(username))
			username = "anonymous";
		return username;
	}

	/**
	 * 设置客户端登陆用户
	 * @param req
	 * @param string
	 */
	public static void setRequestClientUser(HttpServletRequest req,
			String username) {
		if(username != null)
			req.setAttribute(ATTR_USER_REAL_NAME, username);
	}
	
	/**
	* 判断字符串是否是一个IP地址
	* @param addr
	* @return
	*/
	public static boolean isIPAddr(String addr){
		if(StringUtils.isEmpty(addr))
			return false;
		String[] ips = StringUtils.split(addr, '.');
		if(ips.length != 4)
			return false;
		try{
			int ipa = Integer.parseInt(ips[0]);
			int ipb = Integer.parseInt(ips[1]);
			int ipc = Integer.parseInt(ips[2]);
			int ipd = Integer.parseInt(ips[3]);
			return ipa >= 0 && ipa <= 255 && ipb >= 0 && ipb <= 255 && ipc >= 0
						&& ipc <= 255 && ipd >= 0 && ipd <= 255;
		}
		catch(Exception e){}
		
		return false;
	}
	
	/**
	 * 获取Boolean类型的请求参数值（有可能返回null）
	 */
	public static Boolean getBooleanParameter(HttpServletRequest req, String parameterName) {
		Boolean result = null;
		if (StringUtils.isBlank(parameterName)) {
			return result;
		}
		String parameter = req.getParameter(parameterName);
		if (StringUtils.isBlank(parameter)) {
			return result;
		}
		try {
			result = Boolean.parseBoolean(parameter);
		} catch (Exception ex) {}
		return result;
	}
	
	public static Integer getIntegerParameter(HttpServletRequest req, String parameterName) {
		Integer result = null;
		if (StringUtils.isBlank(parameterName))
			return result;
		String parameter = req.getParameter(parameterName);
		if (StringUtils.isBlank(parameter))
			return result;
		try {
			result = Integer.parseInt(parameter);
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		return result;
	}
	
	public static Long getLongParameter(HttpServletRequest req, String parameterName) {
		Long result = null;
		if (StringUtils.isBlank(parameterName))
			return result;
		String parameter = req.getParameter(parameterName);
		if (StringUtils.isBlank(parameter))
			return result;
		try {
			result = Long.parseLong(parameter);
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		return result;
	}
	
	/**
	 * 将request请求参数封装为bean
	 */
	@SuppressWarnings("rawtypes")
	public static <T> T getParamBean(HttpServletRequest request, Class<T> beanClass) {
		try {
			T t = beanClass.newInstance();
			Map map = request.getParameterMap();
			// 设置以下包装类的默认值为null
			ConvertUtils.register(new BooleanConverter(null), Boolean.class);
			ConvertUtils.register(new LongConverter(null), Long.class);
			ConvertUtils.register(new ShortConverter(null), Short.class);
			ConvertUtils.register(new IntegerConverter(null), Integer.class);
			ConvertUtils.register(new DoubleConverter(null), Double.class);
			ConvertUtils.register(new BigDecimalConverter(null), BigDecimal.class);
			ConvertUtils.register(new DateConverter(), Date.class);
			// 将Map中的值设入bean中，其中Map中的key必须与目标对象中的属性名相同，否则不能实现拷贝
			BeanUtils.populate(t, map);
			return t;
		} catch (Exception ex) {
			throw new RuntimeException(ex);
		}
	}

}













