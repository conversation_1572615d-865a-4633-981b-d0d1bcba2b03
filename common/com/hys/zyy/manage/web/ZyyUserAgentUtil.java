package com.hys.zyy.manage.web;

import javax.servlet.http.HttpServletRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.hys.zyy.manage.util.StringUtils;

import eu.bitwalker.useragentutils.OperatingSystem;
import eu.bitwalker.useragentutils.UserAgent;

/**
 * 获取请求设备工具类
 * <AUTHOR>
 */
public class ZyyUserAgentUtil {

	private static Logger logger = LoggerFactory.getLogger(ZyyUserAgentUtil.class);
	
	private static final String COMPUTER = "computer", TABLET = "tablet", MOBILE = "mobile";
	
	/**
	 * 获取UserAgent
	 */
	public static String getUserAgent(HttpServletRequest req) {
		return req.getHeader("User-Agent");
	}
	
	public static String getUserAgent() {
		return getUserAgent(((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest());
	}
	
	/**
	 * 获取操作系统对象
	 */
	private static OperatingSystem getOperatingSystem(String userAgent) {
		UserAgent agent = UserAgent.parseUserAgentString(userAgent);
		return agent.getOperatingSystem();
	}
	
	/**
	 * 获取OS：windows/ios/android
	 */
	public static String getOs(HttpServletRequest req) {
		String userAgent = getUserAgent(req);
		return getOs(userAgent);
	}
	
	public static String getOs() {
		String userAgent = getUserAgent(((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest());
		return getOs(userAgent);
	}
	
	public static String getOs(String userAgent) {
		OperatingSystem operatingSystem = getOperatingSystem(userAgent);
		String os = operatingSystem.getGroup().getName().toLowerCase();
		logger.info("os is:{}", os);
		return os;
	}
	
	/**
	 * 获取设备类型
	 */
	public static String getDeviceType(HttpServletRequest req) {
		String userAgent = getUserAgent(req);
		return getDeviceType(userAgent);
	}
	
	public static String getDeviceType() {
		String userAgent = getUserAgent(((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest());
		return getDeviceType(userAgent);
	}
	
	public static String getDeviceType(String userAgent) {
		OperatingSystem operatingSystem = getOperatingSystem(userAgent);
		String deviceType = operatingSystem.getDeviceType().toString().toLowerCase();
		logger.info("deviceType is:{}", deviceType);
		return deviceType;
	}
	
	public static boolean isMobile(HttpServletRequest req) {
		boolean isMobile = false;
		String devicetype = getDeviceType(req);
		if (StringUtils.isBlank(devicetype))
			return isMobile;
		if (COMPUTER.equals(devicetype))
			isMobile = false;
		else if (TABLET.equals(devicetype))
			isMobile = false;
		else if (MOBILE.equals(devicetype))
			isMobile = true;
		logger.info("isMobile is {}", isMobile);
		return isMobile;
	}
	
	public static boolean isMobile() {
		return isMobile(((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest());
	}

}