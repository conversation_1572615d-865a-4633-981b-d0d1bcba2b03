package com.hys.zyy.manage.web.filter;

import java.io.IOException;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import com.hys.zyy.manage.util.FilterConfigUtils;

/**
 * 性能filter。
 * 如果处理的总时间超过该值，则filter会以warning的方式记录该次操作。
 * 
 */
public class PerformanceFilter implements Filter {
    int threshold = 3000;
    boolean includeQueryString = false;
    private static final Logger log = Logger.getLogger(PerformanceFilter.class);

    public void destroy() {
    }
    
    @Override
	public void init(FilterConfig config) throws ServletException {
    	this.threshold = FilterConfigUtils.getIntParameter(config, "threshold", threshold);
        this.includeQueryString = FilterConfigUtils.getBooleanParameter(config, "includeQueryString", includeQueryString);
        log.info("PerformanceFilter started with threshold:"+threshold+"ms includeQueryString:"+includeQueryString);
	}
    
    @Override
	public void doFilter(ServletRequest request, ServletResponse response,
			FilterChain chain) throws IOException, ServletException {
    	
    	String requestString = dumpRequest(request);

        Throwable failed = null;
        long start = System.currentTimeMillis();
        try {
            chain.doFilter(request, response);
        } catch (Throwable e) {
            failed = e;
            rethrowThrowable(failed);
        } finally {
            long duration = System.currentTimeMillis() - start;
            if (failed != null) {
                log.error(requestString+",F,"+duration+"ms");
            } else if (duration > threshold) {
                log.warn(requestString+",Y,"+duration+"ms");
            } else if (log.isInfoEnabled()) {
                log.info(requestString+",Y,"+duration+"ms");
            }
        }
	}

    private static void rethrowThrowable(Throwable failed) throws Error, IOException, ServletException {
        if (failed != null) {
            if (failed instanceof Error) {
                throw (Error) failed;
            } else if (failed instanceof RuntimeException) {
                throw (RuntimeException) failed;
            } else if (failed instanceof IOException) {
                throw (IOException) failed;
            } else if (failed instanceof ServletException) {
                throw (ServletException) failed;
            }else {
            	throw new RuntimeException(failed);
            }
        }
    }

    /**
     * 取得request的内容(HTTP方法, URI)
     *
     * @param request HTTP请求
     *
     * @return 字符串
     */
    protected String dumpRequest(ServletRequest request) {
        HttpServletRequest req = (HttpServletRequest)request;
        StringBuffer buffer = new StringBuffer(req.getMethod());

        buffer.append("__").append(req.getRequestURI());

        if(includeQueryString) {
            String queryString = req.getQueryString();
            if (StringUtils.isNotBlank(queryString)) {
                buffer.append("?").append(queryString);
            }
        }
        
        return buffer.toString();
    }

}
