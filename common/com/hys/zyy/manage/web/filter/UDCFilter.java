package com.hys.zyy.manage.web.filter;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletContext;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.DailyRollingFileAppender;
import org.apache.log4j.Level;
import org.apache.log4j.LogManager;
import org.apache.log4j.Logger;
import org.apache.log4j.PatternLayout;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.PathMatcher;

import com.hys.zyy.manage.web.RequestUtils;

/**
 * 用户数据采集器
 * <AUTHOR>
 *
 */
public class UDCFilter implements Filter {
	
	private static final String[] DEFAULT_IGNORE_LIST = new String[] {
    "/**/*.js",
    "/**/*.css",
    "/**/*.png",
    "/**/*.gif",
    "/**/*.jpg"
	};
	
	private final List<String> ignorePatterns = new ArrayList<String>();
	
	// Ant 匹配模式
	private final PathMatcher pm = new AntPathMatcher();
	
	@Override
	public void init(FilterConfig config) throws ServletException {
		// 在当前log4j中增加配置（注：这里做了个假设是log4j）
		Logger logger = LogManager.getLogger(UDCFilter.class);
		logger.setLevel(Level.INFO);
		
		String logfile = this.locateLogFilePath(config.getServletContext()) + "udc.log";
		
		// Layout Config
		PatternLayout layout = new PatternLayout();
		layout.setConversionPattern("%-d{yyyy-MM-dd,HH:mm} 来自:%X{user}[%X{ip}]%n请求:%X{uri}[%X{domain}] 响应时间:%X{times}%n环境:%X{userAgent}[%X{refer}]%n");
		
		// Appender Config
		DailyRollingFileAppender appender;
		try {
			appender = new DailyRollingFileAppender(layout, logfile, "yyyy-MM-dd");
			appender.setAppend(true);
			appender.setEncoding("UTF-8");
			
			logger.addAppender(appender);
		} catch (IOException e) {
			e.printStackTrace();
		}
		
	}
	
	@Override
	public void doFilter(ServletRequest request, ServletResponse response,
                         FilterChain chain) throws IOException, ServletException {
		
		// 过滤忽略的请求
		HttpServletRequest req = (HttpServletRequest)request;
		String uri = RequestUtils.getRequestURI(req);
		
		if(ifNotHasIgnoreRequest(uri)) {
			long currentTimeMillis = System.currentTimeMillis();
            
			// Domain
			String domainName = RequestUtils.getDomainName(req);
			MDC.put("domain", domainName);
			
			// URI
			MDC.put("uri", uri);
			
			// IP
			String ip = RequestUtils.getRemoteAddr(req);
			MDC.put("ip", ip);
			
			// Browser UserAgent
			String userAgent = RequestUtils.getUserAgent(req);
			MDC.put("userAgent", userAgent);
			
			// Request Refer
			String refer = RequestUtils.getRequestRefer(req);
			MDC.put("refer", refer);
			
			// User
			String user = RequestUtils.getRequestClientUser(req);
			MDC.put("user", user);
			
			chain.doFilter(request, response);
			
			// Response Time
			MDC.put("times", String.valueOf(System.currentTimeMillis() - currentTimeMillis));
			
			LoggerFactory.getLogger(UDCFilter.class).info("");
		}
		else {
			chain.doFilter(request, response);
		}
	}
	
	@Override
	public void destroy() {
		
	}
	
	/**
	 * 增加URI到忽略列表
	 * @param uri
	 */
	public void addIgnorePattern(String uri) {
		if(StringUtils.isBlank(uri))
			return;
		this.ignorePatterns.add(uri);
	}
	
	/**
	 * 判断如果没有忽略这个请求
	 * @param uri
	 * @return
	 */
	private boolean ifNotHasIgnoreRequest(String uri) {
		// 默认忽略列表
		for(String uriPattern : DEFAULT_IGNORE_LIST)
			if(pm.match(uriPattern, uri))
				return false;
		// 自定义忽略列表
		for(String uriPattern : this.ignorePatterns)
			if(pm.match(uriPattern, uri))
				return false;
		return true;
	}
	
    /**
     * 返回UDC日志文件存放的文件路径
     * @param context
     * @return
     */
    private String locateLogFilePath(ServletContext context) {
        String path = context.getInitParameter("udc.path");
        if(path == null || "".equals(path.trim()))
            path = context.getRealPath("/") + "logs";
        if(path != null && !path.endsWith(File.separator))
            path = path + File.separator;
        return path;
    }
    
}
