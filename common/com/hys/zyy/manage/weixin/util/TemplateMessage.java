package com.hys.zyy.manage.weixin.util;

import java.util.Map;

public class TemplateMessage {
	
	/**
	 * 标题颜色
	 */
	private String topcolor;
	
	/**
	 * URL
	 */
	private String url;
	
	/**
	 * 消息模板ID
	 */
	private String template_id;
	
	/**
	 * 发送到用户OPENID
	 */
	private String touser;
	
	/**
	 * 消息数据
	 */
	private Map<String,TemplateMessageData> data;

	public String getTouser() {
		return touser;
	}

	public void setTouser(String touser) {
		this.touser = touser;
	}

	public String getTemplate_id() {
		return template_id;
	}

	public void setTemplate_id(String template_id) {
		this.template_id = template_id;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getTopcolor() {
		return topcolor;
	}

	public void setTopcolor(String topcolor) {
		this.topcolor = topcolor;
	}

	public Map<String,TemplateMessageData> getData() {
		return data;
	}

	public void setData(Map<String,TemplateMessageData> data) {
		this.data = data;
	}

}
