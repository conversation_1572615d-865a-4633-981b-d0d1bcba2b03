package com.hys.zyy.manage.weixin.util;

import java.math.BigInteger;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

import org.bouncycastle.util.encoders.UrlBase64;

import com.hys.zyy.manage.constants.ExamQuestionConstants;
import com.hys.zyy.manage.util.Md5Util;

/**
 * <AUTHOR>
 * @Description 微信工具类
 * @Date 2019/10/18 11:02
 */
public class WechatUtil {
     
    /**
     * md5对字符串加密
     * @param plainText
     * @return
     */
    public static String toMD5(String plainText) {
        byte[] secretBytes = null;
        try {
            secretBytes = MessageDigest.getInstance("md5").digest(
                    plainText.getBytes());
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("没有md5这个算法！");
        }
        String md5code = new BigInteger(1, secretBytes).toString(16);// 16进制数字
        // 如果生成数字未满32位，需要前面补0
        for (int i = 0; i < 32 - md5code.length(); i++) {
            md5code = "0" + md5code;
        }
        return md5code;
    }
    
    
    /**
     * 根据用户名称及sessionKey创建token
     * @param userId
     * @param openId 本项目使用的是openId
     * @return
     */
    public static String createToken(Long userId,String openId){
        String sessionKey= toMD5(openId+ExamQuestionConstants.WXMINI_TOKENKEY);
        String token=userId+"."+sessionKey;
        byte[] bytes2 = token.getBytes();
        byte[] encoded = UrlBase64.encode(bytes2);
        String enCode=new String(encoded);
        return enCode;
    }
    
    //解码
    public static String decode(String data) throws Exception{
        byte[] b = UrlBase64.decode(data.getBytes());
        return new String(b);
    }



}
