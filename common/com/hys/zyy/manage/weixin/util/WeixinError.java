package com.hys.zyy.manage.weixin.util;

import java.io.Serializable;

public class WeixinError implements Serializable {
	
	public WeixinError(){
		
	}

	public Long getErrcode() {
		return errcode;
	}

	public void setErrcode(Long errcode) {
		this.errcode = errcode;
	}

	public Long getErrmsg() {
		return errmsg;
	}

	public void setErrmsg(Long errmsg) {
		this.errmsg = errmsg;
	}

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	//返回错误代码
	private Long errcode;
	
	//返回错误消息
	private Long errmsg;	
	
	
}
