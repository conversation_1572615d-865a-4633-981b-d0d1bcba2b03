package com.hys.zyy.manage.weixin.util;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;

import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import com.hys.zyy.manage.constants.Constants;
import com.hys.zyy.manage.constants.ExamQuestionConstants;

import net.sf.json.JSONObject;

public class WeixinUtils {
	
	/**
	 * 获取access_token 公众号的全局唯一接口调用凭据
	 * @return
	 */
	public static WeixinAccessToken getAccessToken(){
		String urlString="https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s";
		WeixinAccessToken result;
		try{
			
			String resultString="";
			//测试
//			URL url = new URL(String.format(urlString, "wx00a6134644d70ffb","bc9578d9a6ad2ae2cd513b80b5a13bfd")); 
			//正式
			URL url = new URL(String.format(urlString, Constants.WEIXIN_APPID,Constants.WEIXIN_SECRET)); 
	        HttpURLConnection connection =(HttpURLConnection) url.openConnection();
	    	connection.connect();
		   	BufferedReader reader =new BufferedReader(new InputStreamReader(connection.getInputStream(),"utf-8"));
		   	String lines;
		   	while((lines= reader.readLine())!=null){
		   		resultString += lines;  
		   	}
		   	reader.close();
		   	connection.disconnect(); 

			JSONObject jsonObject=JSONObject.fromObject(resultString);
			result=(WeixinAccessToken)JSONObject.toBean(jsonObject, WeixinAccessToken.class);
		   	
		}
		catch(Exception e){
			e.printStackTrace();
			result=null;
		}
		return result;
	}
	
	public static WeixinAccessToken getAccessTokenMiniprogram(){
		String urlString="https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s";
		WeixinAccessToken result;
		try{
			
			String resultString="";
			//测试
//			URL url = new URL(String.format(urlString, "wx9cb4f64ef6773786","6233fd22c767062d2048af141bfb8036")); 
			
			//正式
			URL url = new URL(String.format(urlString, ExamQuestionConstants.WXMINI_APPID,ExamQuestionConstants.WXMINI_APPSECRET)); 
	        HttpURLConnection connection =(HttpURLConnection) url.openConnection();
	    	connection.connect();
		   	BufferedReader reader =new BufferedReader(new InputStreamReader(connection.getInputStream(),"utf-8"));
		   	String lines;
		   	while((lines= reader.readLine())!=null){
		   		resultString += lines;  
		   	}
		   	reader.close();
		   	connection.disconnect(); 

			JSONObject jsonObject=JSONObject.fromObject(resultString);
			result=(WeixinAccessToken)JSONObject.toBean(jsonObject, WeixinAccessToken.class);
		   	
		}
		catch(Exception e){
			e.printStackTrace();
			result=null;
		}
		return result;
	}
	
	
	/**
	 *  获取access_token 网页授权用户access_token
	 * @param code
	 * @return
	 */
	public static WeixinAccessToken getAccessTokenByCode(String code){
		String urlString="https://api.weixin.qq.com/sns/oauth2/access_token?appid=%s&secret=%s&code=%s&grant_type=authorization_code";
		WeixinAccessToken result;
		try{
			String resultString="";
			//测试
//			URL url = new URL(String.format(urlString, "wx00a6134644d70ffb","bc9578d9a6ad2ae2cd513b80b5a13bfd",code)); 
			//正式上线
			URL url = new URL(String.format(urlString, Constants.WEIXIN_APPID,Constants.WEIXIN_SECRET,code)); 
	        HttpURLConnection connection =(HttpURLConnection) url.openConnection();
	    	connection.connect();
		   	BufferedReader reader =new BufferedReader(new InputStreamReader(connection.getInputStream(),"utf-8"));
		   	String lines;
		   	while((lines= reader.readLine())!=null){
		   		resultString += lines;  
		   	}
		   	reader.close();
		   	connection.disconnect(); 

			JSONObject jsonObject=JSONObject.fromObject(resultString);
			
			result=(WeixinAccessToken)JSONObject.toBean(jsonObject, WeixinAccessToken.class);
		}
		catch(Exception e){
			e.printStackTrace();
			result=null;
		}
		/*
		result =new WeixinAccessToken();
		result.setOpenid("1234567678");
		result.setAccess_token("1234567678");
		*/
		return result;
	}
	
	
	/**
	 *  校验用户access_token 是否有效
	 * @param access_token
	 * @param openid
	 * @return
	 */
	public static  Boolean verificationAccessToken(String access_token,String openid){
		String urlString="https://api.weixin.qq.com/sns/auth?access_token=%s&openid=%s ";
		WeixinError result;
		try{
			String resultString="";
			URL url = new URL(String.format(urlString, access_token,openid)); 
	        HttpURLConnection connection =(HttpURLConnection) url.openConnection();
	    	connection.connect();
		   	BufferedReader reader =new BufferedReader(new InputStreamReader(connection.getInputStream(),"utf-8"));
		   	String lines;
		   	while((lines= reader.readLine())!=null){
		   		resultString += lines;  
		   	}
		   	reader.close();
		   	connection.disconnect(); 

			JSONObject jsonObject=JSONObject.fromObject(resultString);
			result=(WeixinError)JSONObject.toBean(jsonObject, WeixinError.class);
			result.setErrcode(0l);
			return result.getErrcode().equals(0l);
		   	
		}
		catch(Exception e){
			e.printStackTrace();
			return false;
		}
	}
	
	
	
	/**
	 *  发送模板消息
	 * @param templateMessage 模板消息对像
	 * @return 是否发送成功
	 * 
	 *	TemplateMessage tm=new TemplateMessage();
	 *	tm.setTemplate_id("ltbJV_WfuP6vAUB6K5HJYvKtfPMrNZrZrSYs677Aj4M");
	 *	tm.setTouser("oL9TVwyQufnDnA3fTdq1tDpM5HyA");
	 *	tm.setUrl("http://weixin.qq.com/download");
	 *	Map<String,TemplateMessageData> dataMap=new HashMap<String,TemplateMessageData>();
	 *	dataMap.put("first", new TemplateMessageData("测式消息first","#173177"));
	 *	dataMap.put("keyword1", new TemplateMessageData("测式消息keyword1","#173177"));
	 *	dataMap.put("keyword2", new TemplateMessageData("测式消息keyword2","#173177"));
	 *	dataMap.put("remark", new TemplateMessageData("测式消息remark","#173177"));
	 *	tm.setData(dataMap);
	 *	WeixinUtils.sendTemplateMessage(tm);
	 * 
	 */
	public static Boolean sendTemplateMessage(TemplateMessage templateMessage){
		String urlString="https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=%s";
		WeixinAccessToken token=getAccessToken();
		Boolean b=false;
		OutputStream write = null;
	    BufferedReader reader = null;
		try{
			String resultString="";

	        JSONObject wjsonObject = JSONObject.fromObject(templateMessage); 
	        String messageString=wjsonObject.toString();
	        URL url = new URL(String.format(urlString,token.getAccess_token())); 
	        HttpURLConnection connection =(HttpURLConnection) url.openConnection();
	        connection.setRequestMethod("POST");
	        //connection.setRequestProperty("accept", "*/*");
	        //connection.setRequestProperty("connection", "Keep-Alive");
	        //connection.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
	        // conn.setConnectTimeout(10000);//连接超时 单位毫秒
	        // conn.setReadTimeout(2000);//读取超时 单位毫秒
	        connection.setDoOutput(true);
	        connection.setDoInput(true);
	        write=connection.getOutputStream();
	        write.write(messageString.getBytes("UTF-8"));
		   	reader =new BufferedReader(new InputStreamReader(connection.getInputStream(),"utf-8"));
		   	String lines;
		   	while((lines= reader.readLine())!=null){
		   		resultString += lines;  
		   	}
		   	reader.close();
		   	connection.disconnect(); 

			JSONObject rjsonObject=JSONObject.fromObject(resultString);
			WeixinError result=(WeixinError)JSONObject.toBean(rjsonObject, WeixinError.class);
		    b=result.getErrcode()==0;
		   	
		}
		catch(Exception e){
			e.printStackTrace();
			return false;
		}
		finally {
			try{
                if(write!=null){
                	write.close();
                }
                if(reader!=null){
                	reader.close();
                }
            }
            catch(IOException ex){
                ex.printStackTrace();
            }
		}
		return b;
		
	}
	
	// 是否关注公众号  0-未关注，1-已关注
	public static boolean checkFollowWinxinPlat(String openid){
		String urlString = "https://api.weixin.qq.com/cgi-bin/user/info?access_token=%s&openid=%s&lang=zh_CN";
		WeixinAccessToken token=getAccessToken();
		try{
			String resultString="";
			URL url = new URL(String.format(urlString, token.getAccess_token(),openid)); 
	        HttpURLConnection connection =(HttpURLConnection) url.openConnection();
	    	connection.connect();
		   	BufferedReader reader =new BufferedReader(new InputStreamReader(connection.getInputStream(),"utf-8"));
		   	String lines;
		   	while((lines= reader.readLine())!=null){
		   		resultString += lines;  
		   	}
		   	reader.close();
		   	connection.disconnect(); 

			JSONObject jsonObject=JSONObject.fromObject(resultString);
			Object subscribeObj = jsonObject.get("subscribe");
			if (subscribeObj != null && subscribeObj.toString().equals("1")){
				// 是否关注公众号  0-未关注，1-已关注
				return true;
			}
		}
		catch(Exception e){
			e.printStackTrace();
		}
		return false;
	}
	
	/**
	 * 获取ticket , 现实公众号的二维码
	 * @return
	 * <AUTHOR>
	 * @date 2018-11-28下午2:50:00
	 */
	public static String qrcodeCreate(){
		String ticket = "";
		String urlString="https://api.weixin.qq.com/cgi-bin/qrcode/create?access_token=%s";
		WeixinAccessToken token=getAccessToken();
		OutputStream write = null;
	    BufferedReader reader = null;
		try{
			StringBuffer resultString = new StringBuffer();
			String scene_id = "2";
	        String messageString = "{\"expire_seconds\": 2592000, \"action_name\": \"QR_STR_SCENE\", \"action_info\": {\"scene\": {\"scene_str\": "+scene_id+"}}}";
	        URL url = new URL(String.format(urlString,token.getAccess_token())); 
	        HttpURLConnection connection =(HttpURLConnection) url.openConnection();
	        connection.setRequestMethod("POST");
	        connection.setDoOutput(true);
	        connection.setDoInput(true);
	        write=connection.getOutputStream();
	        write.write(messageString.getBytes("UTF-8"));
		   	reader =new BufferedReader(new InputStreamReader(connection.getInputStream(),"utf-8"));
		   	String lines;
		   	while((lines= reader.readLine())!=null){
		   		resultString.append(lines);
		   	}
		   	reader.close();
		   	connection.disconnect(); 
			JSONObject rjsonObject=JSONObject.fromObject(resultString.toString());
			ticket = rjsonObject.getString("ticket");
		   	
		} catch(Exception e){
			e.printStackTrace();
		} finally {
			try{
                if(write!=null){
                	write.close();
                }
                if(reader!=null){
                	reader.close();
                }
            }
            catch(IOException ex){
                ex.printStackTrace();
            }
		}
		return ticket;
	}
	
	public static void main(String[] args) {
		WeixinAccessToken token = getAccessTokenMiniprogram();
		String accessToken = token.getAccess_token();
		String sceneStr = "cerifacateNo=130434198912015215";
		getminiqrQr(sceneStr, accessToken);
	}
	
	
	public static Map getminiqrQr(String sceneStr, String accessToken) {
        RestTemplate rest = new RestTemplate();
        InputStream inputStream = null;
        OutputStream outputStream = null;
        try {
            String url = "https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token="+accessToken;
            Map<String,Object> param = new HashMap<String,Object>();
            param.put("scene", sceneStr);
            param.put("page", "pages/index/index");
            param.put("width", 430);
            param.put("auto_color", false);
            Map<String,Object> line_color = new HashMap<String,Object>();
            line_color.put("r", 0);
            line_color.put("g", 0);
            line_color.put("b", 0);
            param.put("line_color", line_color);
            System.out.println("调用生成微信URL接口传参:" + param);
            MultiValueMap<String, String> headers = new LinkedMultiValueMap<String, String>();
            HttpEntity requestEntity = new HttpEntity(param, headers);
            ResponseEntity<byte[]> entity = rest.exchange(url, HttpMethod.POST, requestEntity, byte[].class, new Object[0]);
            System.out.println("调用小程序生成微信永久小程序码URL接口返回结果:" + entity.getBody());
            byte[] result = entity.getBody();
//            LOG.info(Base64.encodeBase64String(result));
            inputStream = new ByteArrayInputStream(result);

            File file = new File("d:/12222222.png");
            if (!file.exists()){
                file.createNewFile();
            }
            outputStream = new FileOutputStream(file);
            int len = 0;
            byte[] buf = new byte[1024];
            while ((len = inputStream.read(buf, 0, 1024)) != -1) {
                outputStream.write(buf, 0, len);
            }
            outputStream.flush();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if(inputStream != null){
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if(outputStream != null){
                try {
                    outputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return null;    
    }
	/**
	 * 获取小程序码
	 * @param sceneStr
	 * @return
	 * <AUTHOR>
	 * @date 2020-3-26下午2:21:07
	 */
	public static byte[] getWeixinMiniQr(Long provinceId,Long userId) {
        RestTemplate rest = new RestTemplate();
        try {
        	String sceneStr = "proUserId="+provinceId+"-"+userId;
        	WeixinAccessToken token = getAccessTokenMiniprogram();
    		String accessToken = token.getAccess_token();
            String url = "https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token="+accessToken;
            Map<String,Object> param = new HashMap<String,Object>();
            param.put("scene", sceneStr);
            param.put("page", "pages/index/index");
            param.put("width", 430);
            param.put("auto_color", false);
            Map<String,Object> line_color = new HashMap<String,Object>();
            line_color.put("r", 0);
            line_color.put("g", 0);
            line_color.put("b", 0);
            param.put("line_color", line_color);
            MultiValueMap<String, String> headers = new LinkedMultiValueMap<String, String>();
            HttpEntity requestEntity = new HttpEntity(param, headers);
            ResponseEntity<byte[]> entity = rest.exchange(url, HttpMethod.POST, requestEntity, byte[].class, new Object[0]);
            byte[] result = entity.getBody();
            return result;
        } catch (Exception e) {
            e.printStackTrace();
        } 
        return null;    
    }

	
}
