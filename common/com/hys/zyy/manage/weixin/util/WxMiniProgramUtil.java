package com.hys.zyy.manage.weixin.util;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;

import net.sf.json.JSONObject;

import com.hys.zyy.manage.constants.Constants;
import com.hys.zyy.manage.constants.ExamQuestionConstants;

/**
 * 微信小程序工具类
 * <AUTHOR>
 * @date 2020-4-29上午10:34:53
 */
public class WxMiniProgramUtil {
	/**
	 * 微信小程序 根据code获取openid
	 * @param code
	 * @return
	 * <AUTHOR>
	 * @date 2020-4-29上午10:49:28
	 */
	public static WxMiniProgramToken getAccessTokenByCode(String code){
		String urlString= ExamQuestionConstants.WXMINI_JSCODE2SESSIONURL+"?appid=%s&secret=%s&js_code=%s&grant_type=authorization_code";
		WxMiniProgramToken result;
		try{
			String resultString="";
			//测试
//			URL url = new URL(String.format(urlString, "wx00a6134644d70ffb","bc9578d9a6ad2ae2cd513b80b5a13bfd",code)); 
			//正式上线
			URL url = new URL(String.format(urlString, ExamQuestionConstants.WXMINI_APPID,ExamQuestionConstants.WXMINI_APPSECRET,code)); 
	        HttpURLConnection connection =(HttpURLConnection) url.openConnection();
	    	connection.connect();
		   	BufferedReader reader =new BufferedReader(new InputStreamReader(connection.getInputStream(),"utf-8"));
		   	String lines;
		   	while((lines= reader.readLine())!=null){
		   		resultString += lines;  
		   	}
		   	reader.close();
		   	connection.disconnect(); 

			JSONObject jsonObject=JSONObject.fromObject(resultString);
			
			result=(WxMiniProgramToken)JSONObject.toBean(jsonObject, WxMiniProgramToken.class);
		}
		catch(Exception e){
			e.printStackTrace();
			result=null;
		}
		return result;
	}
}
