package com.hys.zyy.manage.config;

import com.hys.zyy.manage.util.ConfigPropertiesUtil;
import org.apache.log4j.LogManager;
import org.apache.log4j.Logger;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Locale;

/**
 * spring容器加载器
 */
@Component
public class ApplicationConfigUtils implements ApplicationContextAware {

    private static Logger logger = LogManager.getLogger(ApplicationConfigUtils.class);

    private static ApplicationContext context=null;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        context=applicationContext;

        logger.info("开始加载环境对应的题库对接地址");
        ConfigPropertiesUtil.init(getActiveProfiles());
    }

    /**
     * 获取bean
     * @param beanName
     * @return
     */
    public static Object getBean(String beanName){
        return context.getBean(beanName);
    }

    /**
     * 获取国际化配置
     * @param key
     * @return
     */
    public static String getMessage(String key){
        return context.getMessage(key,null, Locale.getDefault());
    }

    /**
     * 获取当前激活的配置文件
     * @return
     */
    public static String getActiveProfiles(){
        return context.getEnvironment().getActiveProfiles()[0];
    }

}
