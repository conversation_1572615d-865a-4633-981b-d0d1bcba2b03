package com.hys.zyy.manage.config.datasource;

import java.util.Map;
import java.util.Properties;

import javax.sql.DataSource;

import org.apache.commons.dbcp.BasicDataSource;
import org.logicalcobwebs.proxool.ConnectionPoolDefinitionIF;
import org.logicalcobwebs.proxool.ProxoolDataSource;
import org.logicalcobwebs.proxool.ProxoolFacade;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ResourceLoaderAware;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;

import com.google.common.collect.Maps;
import com.hys.zyy.manage.exception.InvalidArgumentException;
import com.hys.zyy.manage.util.GetterUtil;
import com.hys.zyy.manage.util.StringUtils;

public abstract class DataSourceConfig implements InitializingBean, ResourceLoaderAware {
	
	private static final String DB_CONFIG_FILE = "classpath:db.properties";
	
	private ResourceLoader resourceLoader;
	
	private Properties prop = new Properties();
	
	@Override
	public void setResourceLoader(ResourceLoader resourceLoader) {
		this.resourceLoader = resourceLoader;
	}
	
	public void afterPropertiesSet() throws Exception {
		// 加载数据库配置文件
		Resource resource = resourceLoader.getResource(DB_CONFIG_FILE);
		prop.load(resource.getInputStream());
	}
	
	/**
	 * 根据Profile创建对应的数据源
	 * @param profile
	 * @return
	 */
	public Map<Object, Object> createTargetDataSources(String profile) {
		Map<Object, Object> map = Maps.newHashMap();
		for(String key : prop.stringPropertyNames()) {
			String prefix = profile + ".";
			if(StringUtils.startsWithIgnoreCase(key, prefix) && StringUtils.contains(key, ".jdbc.")) {
				String dataSourceName = StringUtils.substringBetween(key, prefix, ".jdbc.");
				if(StringUtils.isNotBlank(dataSourceName) && !map.containsKey(dataSourceName)) 
					map.put(dataSourceName, this.tryGetDataSource(prefix, dataSourceName));
			}
		}
		return map;
	}
	
	private ProxoolDataSource tryGetDataSource(String prefix, String dataSourceName) {
		String driverClass = prop.getProperty(prefix + dataSourceName + ".jdbc.driver", "oracle.jdbc.driver.OracleDriver");
		String url = prop.getProperty(prefix + dataSourceName + ".jdbc.url");
		String username = prop.getProperty(prefix + dataSourceName + ".jdbc.username");
		String password = prop.getProperty(prefix + dataSourceName + ".jdbc.password", "");
		if(StringUtils.isBlank(url) || StringUtils.isBlank(username))
			throw new InvalidArgumentException();
		/*
		BasicDataSource ds = new BasicDataSource();
		ds.setUrl(url);
		ds.setDriverClassName(driverClass);
		ds.setUsername(username);
		ds.setPassword(password);
		// 其他参数
		ds.setInitialSize(GetterUtil.getInteger(prop.getProperty("datasource.initialSize"), 10));
		ds.setMaxIdle(GetterUtil.getInteger(prop.getProperty("datasource.maxIdle"), 10));
		ds.setMaxActive(GetterUtil.getInteger(prop.getProperty("datasource.maxActive"), 10));
		ds.setMinIdle(GetterUtil.getInteger(prop.getProperty("datasource.minIdle"), 10));
		ds.setMaxWait(GetterUtil.getInteger(prop.getProperty("datasource.maxWait"), 0));
		ds.setValidationQuery(prop.getProperty("datasource.validationQuery", ""));
		ds.setTestOnBorrow(GetterUtil.getBoolean(prop.getProperty("datasource.testOnBorrow"), false));
		*/
		
		ProxoolDataSource ds= new ProxoolDataSource("rct");
		ds.setDriverUrl(url);
		ds.setDriver(driverClass);
		ds.setUser(username);
		ds.setPassword(password);
		// 其他参数
		ds.setMaximumActiveTime(GetterUtil.getInteger(prop.getProperty("datasource.MaximumActiveTime"), 10));
		ds.setSimultaneousBuildThrottle(GetterUtil.getInteger(prop.getProperty("datasource.SimultaneousBuildThrottle"), 10));
		ds.setMaximumConnectionCount(GetterUtil.getInteger(prop.getProperty("datasource.MaximumConnectionCount"), 10));
		ds.setMinimumConnectionCount(GetterUtil.getInteger(prop.getProperty("datasource.MinimumConnectionCount"), 2));
		ds.setPrototypeCount(GetterUtil.getInteger(prop.getProperty("datasource.PrototypeCount"), 2));
		ds.setHouseKeepingSleepTime(GetterUtil.getInteger(prop.getProperty("datasource.HouseKeepingSleepTime"), 30));
		ds.setHouseKeepingTestSql(prop.getProperty("datasource.setHouseKeepingTestSql"));
		ds.setTestBeforeUse(GetterUtil.getBoolean(prop.getProperty("datasource.TestBeforeUse"), true));
		return ds;
	}
	
	public abstract DataSource dataSource();

}
