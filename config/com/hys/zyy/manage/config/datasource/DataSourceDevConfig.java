package com.hys.zyy.manage.config.datasource;

import javax.sql.DataSource;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.hys.zyy.manage.jdbc.HGroupDataSource;
import com.hys.zyy.manage.util.annotation.Dev;

/**
 * 配置系统开发数据源
 * <AUTHOR>
 *
 */
@Configuration @Dev
public class DataSourceDevConfig extends DataSourceConfig {
	
	@Bean
	public DataSource dataSource() {
		// 将多个数据源
		HGroupDataSource ds = new HGroupDataSource();
		ds.setTargetDataSources(super.createTargetDataSources("dev"));
		return ds;
	}
	
}
