package com.hys.zyy.manage.config.datasource;

import javax.sql.DataSource;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.hys.zyy.manage.jdbc.HGroupDataSource;
import com.hys.zyy.manage.util.annotation.Prod;


/**
 * 配置系统生产数据源
 * <AUTHOR>
 *
 */
@Configuration @Prod
public class DataSourceProdConfig extends DataSourceConfig {

	@Bean
	public DataSource dataSource() {
		// 将多个数据源
		HGroupDataSource ds = new HGroupDataSource();
		ds.setTargetDataSources(super.createTargetDataSources("prod"));
		return ds;
	}
	
}
