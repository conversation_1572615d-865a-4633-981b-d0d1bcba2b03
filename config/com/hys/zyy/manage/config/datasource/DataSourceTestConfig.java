package com.hys.zyy.manage.config.datasource;

import com.hys.zyy.manage.jdbc.HGroupDataSource;
import com.hys.zyy.manage.util.annotation.Test;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;


/**
 * 配置系统测试数据源
 * <AUTHOR>
 *
 */
@Configuration @Test
public class DataSourceTestConfig extends DataSourceConfig {

	@Bean
	public DataSource dataSource() {
		// 将多个数据源
		HGroupDataSource ds = new HGroupDataSource();
		ds.setTargetDataSources(super.createTargetDataSources("test"));
		return ds;
	}
	
}
