package examples;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Lambda表达式获取集合中元素某个属性的最大值示例
 */
public class LambdaMaxValueExamples {
    
    // 示例实体类
    static class Student {
        private String name;
        private Integer age;
        private BigDecimal score;
        private Date birthDate;
        
        public Student(String name, Integer age, BigDecimal score, Date birthDate) {
            this.name = name;
            this.age = age;
            this.score = score;
            this.birthDate = birthDate;
        }
        
        // getters
        public String getName() { return name; }
        public Integer getAge() { return age; }
        public BigDecimal getScore() { return score; }
        public Date getBirthDate() { return birthDate; }
        
        @Override
        public String toString() {
            return String.format("Student{name='%s', age=%d, score=%s}", name, age, score);
        }
    }
    
    public static void main(String[] args) {
        // 创建测试数据
        List<Student> students = Arrays.asList(
            new Student("张三", 20, new BigDecimal("85.5"), new Date()),
            new Student("李四", 22, new BigDecimal("92.0"), new Date()),
            new Student("王五", 19, new BigDecimal("78.5"), new Date()),
            new Student("赵六", 21, new BigDecimal("96.5"), new Date())
        );
        
        System.out.println("=== 使用Stream API获取最大值 ===");
        
        // 1. 获取年龄最大值
        Optional<Integer> maxAge = students.stream()
            .map(Student::getAge)
            .max(Integer::compareTo);
        System.out.println("最大年龄: " + maxAge.orElse(0));
        
        // 2. 获取分数最大值
        Optional<BigDecimal> maxScore = students.stream()
            .map(Student::getScore)
            .max(BigDecimal::compareTo);
        System.out.println("最高分数: " + maxScore.orElse(BigDecimal.ZERO));
        
        // 3. 获取年龄最大的学生对象
        Optional<Student> oldestStudent = students.stream()
            .max(Comparator.comparing(Student::getAge));
        System.out.println("年龄最大的学生: " + oldestStudent.orElse(null));
        
        // 4. 获取分数最高的学生对象
        Optional<Student> topStudent = students.stream()
            .max(Comparator.comparing(Student::getScore));
        System.out.println("分数最高的学生: " + topStudent.orElse(null));
        
        // 5. 处理空集合的情况
        List<Student> emptyList = new ArrayList<>();
        Optional<Integer> maxAgeEmpty = emptyList.stream()
            .map(Student::getAge)
            .max(Integer::compareTo);
        System.out.println("空集合最大年龄: " + maxAgeEmpty.orElse(-1));
        
        // 6. 使用mapToInt提高性能（针对基本类型）
        OptionalInt maxAgeInt = students.stream()
            .mapToInt(Student::getAge)
            .max();
        System.out.println("最大年龄(mapToInt): " + maxAgeInt.orElse(0));
        
        // 7. 多条件比较 - 先按分数，再按年龄
        Optional<Student> bestStudent = students.stream()
            .max(Comparator.comparing(Student::getScore)
                .thenComparing(Student::getAge));
        System.out.println("综合最优学生: " + bestStudent.orElse(null));
        
        // 8. 自定义比较器
        Optional<Student> customMax = students.stream()
            .max((s1, s2) -> {
                int scoreCompare = s1.getScore().compareTo(s2.getScore());
                if (scoreCompare != 0) return scoreCompare;
                return s1.getAge().compareTo(s2.getAge());
            });
        System.out.println("自定义比较最优学生: " + customMax.orElse(null));
        
        System.out.println("\n=== 实用工具方法 ===");
        
        // 9. 封装的工具方法
        Integer maxAgeUtil = getMaxValue(students, Student::getAge);
        System.out.println("工具方法获取最大年龄: " + maxAgeUtil);
        
        BigDecimal maxScoreUtil = getMaxValue(students, Student::getScore);
        System.out.println("工具方法获取最高分数: " + maxScoreUtil);
        
        // 10. 获取多个最大值属性
        Map<String, Object> maxValues = getMaxValues(students);
        System.out.println("所有最大值: " + maxValues);
    }
    
    /**
     * 通用获取最大值的工具方法
     */
    public static <T, R extends Comparable<R>> R getMaxValue(List<T> list, 
                                                           java.util.function.Function<T, R> mapper) {
        return list.stream()
            .map(mapper)
            .filter(Objects::nonNull)  // 过滤null值
            .max(Comparable::compareTo)
            .orElse(null);
    }
    
    /**
     * 获取学生集合中各属性的最大值
     */
    public static Map<String, Object> getMaxValues(List<Student> students) {
        Map<String, Object> result = new HashMap<>();
        
        if (students == null || students.isEmpty()) {
            return result;
        }
        
        result.put("maxAge", getMaxValue(students, Student::getAge));
        result.put("maxScore", getMaxValue(students, Student::getScore));
        result.put("oldestStudent", students.stream()
            .max(Comparator.comparing(Student::getAge))
            .orElse(null));
        result.put("topStudent", students.stream()
            .max(Comparator.comparing(Student::getScore))
            .orElse(null));
            
        return result;
    }
}
