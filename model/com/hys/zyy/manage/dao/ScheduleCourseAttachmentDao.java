package com.hys.zyy.manage.dao;

import java.util.List;

import com.hys.zyy.manage.model.ScheduleCourseAttachment;
import com.hys.zyy.manage.model.ScheduleParam;

public interface ScheduleCourseAttachmentDao {
    int deleteByPrimaryKey(Long id);

    int insert(ScheduleCourseAttachment record);

    int insertSelective(ScheduleCourseAttachment record);

    ScheduleCourseAttachment selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ScheduleCourseAttachment record);

    int updateByPrimaryKey(ScheduleCourseAttachment record);
    
    List<ScheduleCourseAttachment> getListAttachmentByPtacticeId(ScheduleParam bean);
    
    
    
}