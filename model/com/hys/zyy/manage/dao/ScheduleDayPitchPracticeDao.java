package com.hys.zyy.manage.dao;

import java.util.List;
import java.util.Map;

import com.hys.zyy.manage.model.ScheduleDayPitch;
import com.hys.zyy.manage.model.ScheduleDayPitchPractice;
import com.hys.zyy.manage.model.ScheduleParam;
import com.hys.zyy.manage.query.Pager;

public interface ScheduleDayPitchPracticeDao {
    int deleteByPrimaryKey(Long id);
    int deleteByscheduleId(Long scheduleId);

    int insert(ScheduleDayPitchPractice record);
    
    int insert(List<ScheduleDayPitchPractice> list);

    int insertSelective(ScheduleDayPitchPractice record);

    ScheduleDayPitchPractice selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ScheduleDayPitchPractice record);

    int updateByPrimaryKey(ScheduleDayPitchPractice record);
    List<ScheduleDayPitchPractice> selectDayPitchByScheduleId(Long schduleId);
    Map<String, ScheduleDayPitchPractice> selectMapDayPitchByScheduleId(Long schduleId);
    
    List<ScheduleDayPitchPractice> getPracticeInfo(Long scheduleId);//导出见习模板，key:日期_课节, value: 组_科室
    
    //导入见习数据，批量插入
    int insertFromExcel(List<ScheduleDayPitchPractice> list);
    
    List<ScheduleDayPitchPractice> getPracticeInfo2(ScheduleParam param);
    Map<String, String> selectMapPracticeByScheduleId(ScheduleParam param);
    List<ScheduleDayPitchPractice> getPracticeInfo(String practiceId,String pitchNumber);
    
    
	 /**
	  * 管理课程表：医院-见习课程
	  * @param bean
	  * @return
	  */
	 List<ScheduleDayPitchPractice> getPracticeListInfo(Pager<ScheduleDayPitchPractice> pager,ScheduleParam bean);
	 
	//我的课程表：学员查询见习信息
	 List<ScheduleDayPitchPractice> getPracticeMapDate(ScheduleParam bean);
	 Map<String, String> selectMapPracticeData(ScheduleParam param);
	 /**
		 * 二维码显示：时间限制二维码有效时间上课前半小时至下课结束时间
		 */
	 boolean isShowQRCode(Long practiceId);
	 
	 //课程表调整，查看课程表，多个课程表的展示
	 List<ScheduleDayPitchPractice> selectDayPitchPracticeByScheduleParam(ScheduleParam bean);
	 Map<String, String> selectMapPracticeinfo(ScheduleParam param);
	 
	 Map<String, String> selectMapPracticeDeptGroup(ScheduleParam param);
	 
	 
	 List<ScheduleDayPitchPractice> getPracticeList(ScheduleDayPitchPractice dayPitchPractice);
	 
	 /**
	  * 更新见习的科室   和  状态 ，通过（天  + 节 + 组）  
	  * @Description: TODO
	  * @param @param dayPitchPractice     
	  * <AUTHOR>
	  * @date 2020-3-10 上午11:04:50
	  */
	 void updateScheduleDayPitchPracticeDept(ScheduleDayPitchPractice dayPitchPractice);
    
}