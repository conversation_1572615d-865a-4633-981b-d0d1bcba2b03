package com.hys.zyy.manage.dao;

import com.hys.zyy.manage.model.SchedulePracticeAttachment;

public interface SchedulePracticeAttachmentDao {
    int deleteByPrimaryKey(Long id);

    int insert(SchedulePracticeAttachment record);

    int insertSelective(SchedulePracticeAttachment record);

    SchedulePracticeAttachment selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(SchedulePracticeAttachment record);

    int updateByPrimaryKey(SchedulePracticeAttachment record);
}