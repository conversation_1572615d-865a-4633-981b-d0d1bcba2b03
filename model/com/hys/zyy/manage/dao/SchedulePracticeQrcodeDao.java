package com.hys.zyy.manage.dao;

import com.hys.zyy.manage.model.SchedulePracticeQrcode;

public interface SchedulePracticeQrcodeDao {
    int deleteByPrimaryKey(Long id);

    int insert(SchedulePracticeQrcode record);

    int insertSelective(SchedulePracticeQrcode record);

    SchedulePracticeQrcode selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(SchedulePracticeQrcode record);

    int updateByPrimaryKey(SchedulePracticeQrcode record);
}