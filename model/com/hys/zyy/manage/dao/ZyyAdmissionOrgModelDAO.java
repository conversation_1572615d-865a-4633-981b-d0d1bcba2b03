package com.hys.zyy.manage.dao;

import java.util.List;

import com.hys.zyy.manage.model.ZyyOrgRctVO;
import com.hys.zyy.manage.model.vo.ZyyAdmissionModelOrgVO;
import com.hys.zyy.manage.model.vo.ZyyAdmissionModelVO;



/**
 * 准考证机构配置  
 * <AUTHOR>
 * @date 2020-2-24下午2:40:01
 */
public interface ZyyAdmissionOrgModelDAO {
	
	ZyyAdmissionModelOrgVO getModelOrg(Long modelId, String item);

	void saveAllModelOrg(List<ZyyAdmissionModelOrgVO> list);

	List<ZyyOrgRctVO> findAdmissionModelOrgList(Long modelId);

	List<ZyyAdmissionModelVO> findModelListByOrgBaseDeptId(Long id);
	/**
	 * 删除
	 * @param modelId
	 * @param orgId
	 * <AUTHOR>
	 * @date 2020-5-20上午10:04:39
	 */
	void deleteAdmissionModelOrg(Long modelId, String orgId); 
	 
}
