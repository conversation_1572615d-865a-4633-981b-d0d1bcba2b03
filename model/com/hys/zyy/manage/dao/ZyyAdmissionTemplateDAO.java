package com.hys.zyy.manage.dao;

import com.hys.zyy.manage.model.ZyyAdmissionTemplate;
import com.hys.zyy.manage.model.ZyyAdmissionTemplateSubject;
import com.hys.zyy.manage.model.ZyyUserExtendVO;

public interface ZyyAdmissionTemplateDAO {

	public void addAdmissionTemplate(ZyyAdmissionTemplate template);

	public ZyyAdmissionTemplate queryAdmissionTemplate(ZyyUserExtendVO zyyUser);

	public void editAdmissionTemplate(ZyyAdmissionTemplate template);

	public void addAdmissionTemplateSubject(ZyyAdmissionTemplateSubject subject);

	public void deleteAdmissionTemplateSubject(ZyyAdmissionTemplateSubject subject);

	public void editAdmissionTemplateSubject(ZyyAdmissionTemplateSubject subject);

}
