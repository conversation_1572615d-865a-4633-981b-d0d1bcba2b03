package com.hys.zyy.manage.dao;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.hys.zyy.manage.model.ZyyAttendanceExportVO;
import com.hys.zyy.manage.model.ZyyAttendanceLeaveType;
import com.hys.zyy.manage.model.ZyyAttendanceLeaveTypeVO;
import com.hys.zyy.manage.model.ZyyAttendanceRule;
import com.hys.zyy.manage.model.ZyyAttendanceRuleVO;
import com.hys.zyy.manage.model.ZyyCycleTime;
import com.hys.zyy.manage.model.ZyyLeaveAudit;
import com.hys.zyy.manage.model.ZyyLeaveAuditVO;
import com.hys.zyy.manage.model.ZyyResidencyLeaveVO;
import com.hys.zyy.manage.model.ZyyUser;
import com.hys.zyy.manage.model.ZyyUserExtendVO;
import com.hys.zyy.manage.model.zyyResidencyLeaveAttachment;
import com.hys.zyy.manage.query.Pager;

public interface ZyyAttendanceLeaveDAO {

	void queryAllLeaveRules(ZyyUser zyyUser, Pager<ZyyAttendanceRule> pager);

	int addAttendanceLeaveRule(ZyyAttendanceRule rule);
	
	int addAttendanceLeaveRule(ZyyAttendanceRuleVO ruleVo);
	
	int updateAttendanceLeaveRule(ZyyAttendanceRule rule);
	
	/**
	 * @desc 更新请假类型详细规则（动态更新）
	 * @param ruleVo
	 * @return
	 */
	int dynamicUpdateRule(ZyyAttendanceRuleVO ruleVo);
	
	int deleteAttendanceLeaveRule(Long id);

	void queryAttendanceLeaveApplication(ZyyUser zyyUser, Pager<ZyyResidencyLeaveVO> pager, ZyyResidencyLeaveVO query);

	List<ZyyResidencyLeaveVO> queryLeaveApplication(ZyyUser zyyUser);
	
	List<ZyyAttendanceRule> queryLeaveRules(ZyyUser zyyUser);

	ZyyUserExtendVO queryUserInfo(ZyyUser zyyUser);

	int addAttendanceLeaveApplication(ZyyResidencyLeaveVO leaveApplication);

	int deleteAttendanceLeaveApplication(Long id);
	
	ZyyResidencyLeaveVO queryLeaveApplicationById(Long id);

	void queryTeacherAudit(Pager<ZyyResidencyLeaveVO> pager, ZyyUserExtendVO query);
	
	void queryDeptAudit(Pager<ZyyResidencyLeaveVO> pager, ZyyUserExtendVO query);

	void queryHospitalAudit(Pager<ZyyResidencyLeaveVO> pager, ZyyUserExtendVO query);
	
	void queryTutorAudit(Pager<ZyyResidencyLeaveVO> pager, ZyyUserExtendVO query);
	
	void queryBaseAudit(Pager<ZyyResidencyLeaveVO> pager, ZyyUserExtendVO query);

	int auditLeaveApplication(ZyyUserExtendVO zyyUser, ZyyLeaveAudit audit);

	int deleteAudit(ZyyLeaveAudit audit);

	ZyyLeaveAudit queryAudit(ZyyLeaveAudit audit);

	int updateLeaveApplicationAuditStatus(ZyyResidencyLeaveVO leaveApplication);

	ZyyCycleTime queryDeptInfo(ZyyUserExtendVO query);
	
	List<ZyyCycleTime> queryDeptInfo(ZyyCycleTime query);

	List<ZyyLeaveAuditVO> queryAuditRecord(Long id);
	
	/**
	 * 根据多个请假的id查询审核记录 判断是否有审核记录用
	 * 
	 * @param ids
	 * @return
	 * <AUTHOR>
	 * @date 2018-5-24下午4:38:15
	 */
	List<ZyyLeaveAuditVO> queryAuditRecordByIds(List<Long> ids);

	void queryAttendanceExportData(ZyyUserExtendVO zyyUser, Pager<ZyyAttendanceExportVO> pager, ZyyUserExtendVO query, Date startTime, Date endTime);
	
	List<ZyyAttendanceRuleVO> queryLeaveStatistics(ZyyUserExtendVO zyyUser, Date startDate, Date endDate);

	List<ZyyLeaveAudit> checkIsAudited(ZyyLeaveAudit audit, int auditUserType);

	void addLeaveType(ZyyAttendanceLeaveType leaveType);

	void queryAllLeaveTypes(ZyyUser zyyUser, Pager<ZyyAttendanceLeaveTypeVO> pager);
	
	/**
	 * @desc 新版考勤 -获取医院设置的请假类型设置
	 * @param zyyUser
	 * @param pager
	 */
	void queryHospLeaveTypes(ZyyUser zyyUser,Pager<ZyyAttendanceLeaveTypeVO> pager);
	
	/**
	 * @desc 查看当前医院是否保存过当前类型的请假类型
	 * @param query
	 * @return
	 */
	List<ZyyAttendanceLeaveTypeVO> queryHospLeaveTypes(ZyyAttendanceLeaveTypeVO query);

	List<ZyyAttendanceRule> queryLeaveRules(Long leaveTypeId);

	List<ZyyAttendanceLeaveTypeVO> queryAllLeaveTypes(Long zyyUserOrgId);

	void updateLeaveApplicationTeacherAuditStatus(ZyyResidencyLeaveVO leaveApplication);

	void updateLeaveApplicationDeptAuditStatus(ZyyResidencyLeaveVO leaveApplication);

	List<ZyyAttendanceRule> queryLeaveRules(Long leaveTypeId, Float leaveNum);

	List<ZyyCycleTime> queryCycleTimeInfo(ZyyUser zyyUser);

	/**
	 * @desc 获取请假类型
	 * @param leaveType
	 * @return
	 */
	List<ZyyAttendanceLeaveTypeVO> getLeaveTypeList(ZyyAttendanceLeaveTypeVO leaveTypeVO);
	
	/**
	 * @desc 获取该医院下设置的请假类型下设置的详情
	 * @param zyyAttendanceRuleVO
	 * @return
	 */
	List<ZyyAttendanceRuleVO> getLeaveRules(ZyyAttendanceRuleVO zyyAttendanceRuleVO);
	
	/**
	 * @desc 获取请假类型规则
	 * @param ruleId
	 * @return
	 */
	ZyyAttendanceRuleVO getAttendRuleVoById(Long ruleId);

	/**
	 * @desc 更新规则(二级审核更新时，更新三级审核天数)
	 * @param lastRule
	 */
	void updateRuleByParam(ZyyAttendanceRuleVO lastRule);

	/**
	 * @desc 启用，停用请假类型
	 * @param typeVo
	 * @return
	 */
	Integer startOrStopUsed(ZyyAttendanceLeaveTypeVO typeVo);

	/**
	 * @desc 同步请假规则数据，为空时，则需要新增请假类型，然后保存规则
	 * @param orgTypeVo
	 * @param ruleList
	 */
	void updateToNewRules(ZyyAttendanceLeaveTypeVO orgTypeVo,
			List<ZyyAttendanceRule> ruleList) throws Exception;
	
	/**
	 * @desc 新增请假类型规则数据
	 * @param orgTypeVo
	 * @param ruleList
	 * @throws Exception
	 */
	void addToNewRules(ZyyAttendanceLeaveTypeVO orgTypeVo,
			List<ZyyAttendanceRule> ruleList) throws Exception;

	/**
	 * @desc 查询带教需要审核的数据
	 * @param pager
	 * @param query
	 * @param processList
	 */
	void queryTeacherAuditNew(Pager<ZyyResidencyLeaveVO> pager,
			ZyyUserExtendVO query, Map<Long, Integer> processList);

	/**
	 * @desc 获取人员请假详情
	 * @param zyyUserId
	 * @return
	 */
	List<ZyyResidencyLeaveVO> getResiLeaveVos(Date startDate,Date endDate,Long zyyUserId);

	/**
	 * @desc 获取详情
	 * @param typeId
	 * @return
	 */
	ZyyAttendanceLeaveTypeVO getTypeDetailById(Long typeId);
	
	/**
	 * 根据ID获取所有附件
	 * @param leaveId
	 * @return
	 */
	List<zyyResidencyLeaveAttachment> getAllAttachmentsByLeaveId(Long leaveId);
	
	/**
	 * 添加附件
	 * @param attachment
	 * @return
	 */
	int addAttachment(zyyResidencyLeaveAttachment attachment);
	
	/**
	 * @desc 移除附件
	 * @param attachment
	 */
	public void removeAttachment(zyyResidencyLeaveAttachment attachment) throws Exception;
	
	public int batchRemoveAttachments(Long[] ids);
	
	public int batchRemoveAttachments(String[] ids);
	
	/**
	 * 查询附件
	 * @param id
	 * @return
	 */
	zyyResidencyLeaveAttachment getAttachmentById(Long id);
}
