package com.hys.zyy.manage.dao;

import java.util.List;

import com.hys.zyy.manage.model.ZyyBaseContinuity;
import com.hys.zyy.manage.model.ZyyDept;
import com.hys.zyy.manage.model.ZyyDeptVO;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：张伟清 2012-06-20
 * 
 * 描述：基地联排科室 DAO
 * 
 * 说明:
 */
public interface ZyyBaseContinuityDAO {

	/**
	 * 查询基地联排科室信息
	 * @param continuity
	 * @return
	 */
	public List<ZyyBaseContinuity> getZyyBaseContinuity(ZyyBaseContinuity continuity) ;
	
	/**
	 * 添加基地联排科室信息 
	 */
	public Long saveZyyBaseContinuity(ZyyBaseContinuity continuity);
	
	/**
	 * 添加连排科室表
	 */
	public int[] saveZyyBaseContinuityDept(List<ZyyBaseContinuity> list, Long id);
	
	/**
	 * 删除基地联排科室信息 
	 */
	public void deleteZyyBaseContinuity(Long id);
	
	/**
	 * 更新基地联排科室信息 
	 */
	public void updateZyyBaseContinuity(List<ZyyBaseContinuity> list,
			ZyyBaseContinuity continuity);
	
	/**
	 *	查找没有连排的科室
	 */
	public List<ZyyDeptVO> getZyyBaseContinuityList(ZyyBaseContinuity zyyBaseContinuity);
	
	public List<ZyyDept> getZyyBaseContinuityDept(Long cid) ;
	
	/**
	 * 根据基地ID和连排名称得到对象
	 * @param baseId
	 * @param name
	 */
	public ZyyBaseContinuity getObjectByBaseIdAndName(Long id, Long baseId, String name);
	
}

