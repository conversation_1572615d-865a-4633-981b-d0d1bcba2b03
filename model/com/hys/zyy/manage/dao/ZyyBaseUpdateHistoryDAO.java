package com.hys.zyy.manage.dao;

import java.util.List;

import com.hys.zyy.manage.model.ZyyBaseUpdateHistoryVO;
import com.hys.zyy.manage.util.DataGrid;

public interface ZyyBaseUpdateHistoryDAO {

	public Long insertSelective(ZyyBaseUpdateHistoryVO record);

	public int updateByPrimaryKeySelective(ZyyBaseUpdateHistoryVO record);

	public ZyyBaseUpdateHistoryVO selectByPrimaryKey(Long id);

	public void selectByParams(DataGrid dg, ZyyBaseUpdateHistoryVO query, boolean pagination);
	
	public List<ZyyBaseUpdateHistoryVO> selectByParams(ZyyBaseUpdateHistoryVO query);
	
	public List<ZyyBaseUpdateHistoryVO> selectZyyOrg(ZyyBaseUpdateHistoryVO query);

}