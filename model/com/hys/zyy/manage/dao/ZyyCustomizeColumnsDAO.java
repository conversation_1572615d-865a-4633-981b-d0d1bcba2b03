package com.hys.zyy.manage.dao;

import java.util.List;

import com.hys.zyy.manage.model.ZyyCustomizeColumns;
import com.hys.zyy.manage.model.ZyyCustomizeColumnsItem;
import com.hys.zyy.manage.model.ZyyCustomizeColumnsItemVO;
import com.hys.zyy.manage.model.ZyyCustomizeColumnsVO;
import com.mysql.jdbc.log.Log;

public interface ZyyCustomizeColumnsDAO {
	
	/**
	 * 查询评价指标库
	 * @param parentColumnId 父ID
	 * @param orgId 机构ID
	 * @return
	 */
	public List<ZyyCustomizeColumnsVO> getZyyCustomizeColumnsList(Long parentColumnId,Long orgId,Integer tableType);
	/**
	 * 增加自定义列指标库
	 * @param columns
	 */
	public Long addZyyCustomizeColumns(ZyyCustomizeColumns columns);
	/**
	 * 更新自定义列指标库
	 * @param columns
	 */
	public void updateZyyCustomizeColumns(ZyyCustomizeColumns columns);
	/**
	 * 按照ID删除自定义列指标库
	 * @param id
	 */
	public void deleteZyyCustomizeColumns(Long id);
	/**
	 * 增加自定义列指标库
	 * @param columns
	 */
	public void addZyyCustomizeColumnsItem(ZyyCustomizeColumnsItem columnsItem);
	/**
	 * 更新自定义列指标库
	 * @param columns
	 */
	public void updateZyyCustomizeColumnsItem(ZyyCustomizeColumnsItem columnsItem);
	/**
	 * 根据项目Id查询分数配置
	 * @param columnId
	 * @return
	 */
	public List<ZyyCustomizeColumnsItemVO> getColumnsItemListBycolumnId(Long columnId);
	
	
	/**
	 * 查询评价表用到的所有指标以及大分类
	 * @param tableId
	 * @return
	 */
	public List<ZyyCustomizeColumnsVO> getListByTableId(Long tableId);
	
	/**
	 * 查询评价表所有条目
	 * @param tableId
	 * @return
	 */
	List<ZyyCustomizeColumnsItem> getColumnsItemListByTableId(Long tableId);

}
