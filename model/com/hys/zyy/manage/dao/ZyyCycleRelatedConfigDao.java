package com.hys.zyy.manage.dao;

import java.util.List;

import com.hys.zyy.manage.model.ZyyCycleRelatedConfig;

public interface ZyyCycleRelatedConfigDao {
	
	void insert(ZyyCycleRelatedConfig config);
	
	List<ZyyCycleRelatedConfig> getConfigs(ZyyCycleRelatedConfig config);
	
	void update(ZyyCycleRelatedConfig config);
	
	/**
	 * 根据医院id查询
	 * @param userOrgId
	 * @return
	 * <AUTHOR>
	 * @date 2019-7-30下午6:24:38
	 */
	ZyyCycleRelatedConfig getConfigsByOrgId(Long userOrgId);
}
