package com.hys.zyy.manage.dao;

import java.util.List;

import com.hys.zyy.manage.action.reg.viewmodel.ManualVerifyPageQuery;
import com.hys.zyy.manage.model.ZyyCycleRisiHdCheck;
import com.hys.zyy.manage.model.ZyyCycleRisiHdCheckVO;
import com.hys.zyy.manage.model.ZyyCycleTableResiCycle;
import com.hys.zyy.manage.model.ZyyCycleTeacherVO;
import com.hys.zyy.manage.model.ZyyHandbookAuditVO;
import com.hys.zyy.manage.model.ZyyUserExtendVO;
import com.hys.zyy.manage.query.Pager;
import com.hys.zyy.manage.query.ZyyDeptCheckHandbookQuery;

public interface ZyyCycleRisiHdCheckDAO {
	
	public boolean addCycleRisiHdCheck(ZyyCycleRisiHdCheck hdCheck);
	
	public List<ZyyCycleTeacherVO> getTeacherCheckList(Long residency,
			Long deptId, Long deptStdId,Long cycleId);
	
	public List<ZyyCycleRisiHdCheck> getDeptCheckList(Long residency,
			Long deptId, Long deptStdId, Long cycleId);
	
	public List<ZyyCycleRisiHdCheck> getBaseOrgCheckList(Long residency,
			Long deptId, Long deptStdId,Integer vaerifiers);

	/**
	 * 根据条件删除审核记录表
	 * @param hdCheck
	 */
	void delZyyCycleResiHdCheck(ZyyCycleRisiHdCheck hdCheck);
	
	/**
	 * 查询出符合条件的学员
	 */
	public void getCycleResidencyList(Pager<ZyyUserExtendVO> pager, ManualVerifyPageQuery query, Long teacherId, Integer queryFlag);
	
	/**
	 * @desc 新疆FOR APP
	 * @param pager
	 * @param query
	 * @param teacherId
	 * @param queryFlag
	 */
	public void getCycleResidencyListXjForApp(Pager<ZyyUserExtendVO> pager, ManualVerifyPageQuery query, Long teacherId, Integer queryFlag);
	
	/**
	 * 查询出符合条件的学员
	 */
	public void getCycleResidencyListForApp(Pager<ZyyUserExtendVO> pager, ManualVerifyPageQuery query, Long teacherId);
	
	/**
	 * 根据学员查询出来他的登记手册审核页面 展示的内容
	 * 这个方法,当轮转部分有带教老师审核时可以使用
	 */
	public List<ZyyHandbookAuditVO> getHandbookAuditVOListTeacher(Long residencyId, ManualVerifyPageQuery query);
	
	/**
	 * 根据学员查询出来他的登记手册审核页面 展示的内容
	 * 这个方法,当轮转部分有带教老师审核时可以使用
	 */
	public List<ZyyHandbookAuditVO> getHandbookAuditVOListTeacherNew(Long residencyId, ManualVerifyPageQuery query);
	
	/**
	 * 根据学员查询出来他的登记手册审核页面 展示的内容
	 * 这个方法,当轮转部分有科室审核时可以使用
	 */
	public List<ZyyHandbookAuditVO> getHandbookAuditVOListDept(Long residencyId, ManualVerifyPageQuery query);
	
	/**
	 * 根据学员查询出来他的登记手册审核页面 展示的内容
	 * 这个方法,当轮转部分没有带教审核,也没有科室审核,有学科和医院审核的时候 可以使用
	 */
	public List<ZyyHandbookAuditVO> getHandbookAuditVOListBaseOrHosp(Long residencyId, ManualVerifyPageQuery query);
	
	public ZyyCycleRisiHdCheck queryZyyCycleRisiHdCheckById(Long id);
	//更新轮转部分审核记录
	public void updateZyyCycleRisiHdCheck(ZyyCycleRisiHdCheck hcvo);
	/**
	 * 根据不同的用户类型 来查询轮转部分  当前审核者的多次的审核记录
	 * @param chvo
	 * @param flag
	 * @return
	 */
	public List<ZyyCycleRisiHdCheckVO> queryHchkList(ZyyCycleRisiHdCheck chvo,Integer flag);
	/**
	 * 获取学员在轮转科室的终审结果
	 * @param residencyId
	 * @param deptId
	 * @return
	 */
	public ZyyHandbookAuditVO getHandbookFinalCheckStatus(Long residencyId, Long deptId);
	
	public List<ZyyCycleRisiHdCheckVO> queryHchkListForPrintHandbook(ZyyCycleRisiHdCheckVO chvo);
	
	
	/**
	 * 手册时间 
	 * @param cycle
	 * @return
	 */
	List<ZyyCycleTeacherVO> getHdbkNotInTeacherDate(ZyyCycleTableResiCycle cycle);
	/**
	 * 手册时间 
	 * @param cycle
	 * @return
	 */
	List<ZyyCycleTeacherVO> getHdbkNotInCycleDate(ZyyCycleTableResiCycle cycle);
	
	public List<String> getCheckNum(Long residency, Long cycleId,String startDate,String endDate);
	
	public List<ZyyCycleRisiHdCheck> getDeptCheckListNew(ZyyDeptCheckHandbookQuery  query);

	public List<ZyyHandbookAuditVO> getHandbookAuditVOListTeacherForApp(
			Long residencyId, ManualVerifyPageQuery query);

	public List<ZyyHandbookAuditVO> getHandbookAuditVOListDeptForApp(
			Long residencyId, ManualVerifyPageQuery query);

	public List<ZyyHandbookAuditVO> getHandbookAuditVOListBaseOrHospForApp(
			Long residencyId, ManualVerifyPageQuery query);
	
	
	/**
	 * 查询是否有专业基地或者培训基地的审核记录
	 * @param residency
	 * @param deptId
	 * @param deptStdId
	 * @param vaerifiers
	 * @return
	 */
	public ZyyCycleRisiHdCheck getBaseOrgCheckListNew(Long cycleId,Integer vaerifiers);
	
	/**
	 * 由于审核通过以后，会调轮转，导致zyy_cycle_resi_hd_check记录的cycle_teacher_id不对了，所以更新一下; 这个id是zyy_cycle_timeline的id
	 * @param item
	 * @param query
	 * <AUTHOR>
	 * @date 2019-5-7下午2:01:22
	 */
	public void updateCycleTeacherId(ZyyCycleRisiHdCheck item,
			ZyyDeptCheckHandbookQuery query);
	/**
	 * 查询轮转下的所有的学员
	 * @param pager
	 * @param cycleTableId
	 * <AUTHOR>
	 * @date 2019-5-8下午2:33:07
	 */
	public void findDeptCheckListByCycleTableId(
			Pager<ZyyCycleRisiHdCheck> pager, Long cycleTableId);
	/**
	 * 由于审核通过以后，会调轮转，导致zyy_cycle_resi_hd_check记录的cycle_teacher_id不对了，所以更新一下; 这个id是zyy_cycle_timeline的id
	 * @param item
	 * <AUTHOR>
	 * @date 2019-5-8下午3:38:51
	 */
	public void updateCycleTeacherIdNew(ZyyCycleRisiHdCheck item);
	/**
	 * 指定时间以前的带教审核，全部更新为审核通过
	 * @param item
	 * @param endDate
	 * <AUTHOR>
	 * @date 2019-5-8下午5:27:05
	 */
	public void updateTeacherCheckPassByDate(ZyyCycleRisiHdCheck item,
			String endDate);
	
	/**
	 * 指定医院下，指定日期的学员的信息
	 * @param pager
	 * @param hospitalId
	 * @param endDate
	 * <AUTHOR>
	 * @date 2019-5-9上午10:30:31
	 */
	public void findDeptListByHospitalId(Pager<ZyyCycleRisiHdCheck> pager,
			Long hospitalId, String endDate);
	
	/**
	 * 批量添加科室审核通过的记录
	 * @param deptCheckList
	 * <AUTHOR>
	 * @date 2019-5-9上午10:37:47
	 */
	public void addNewPassCycleRisiHdCheck(
			List<ZyyCycleRisiHdCheck> deptCheckList);
	
	/**
	 * 批量添加带教审核通过的记录
	 * @param pager
	 * @param hospitalId
	 * @param endDate
	 * <AUTHOR>
	 * @date 2019-5-9上午11:18:26
	 */
	public void findTeacherListByHospitalId(Pager<ZyyCycleRisiHdCheck> pager,
			Long hospitalId, String endDate);
	/**
	 * 批量添加带教审核通过的记录
	 * @param deptCheckList
	 * <AUTHOR>
	 * @date 2019-5-9上午11:27:41
	 */
	public void addNewTeacherPassCycleRisiHdCheck(
			List<ZyyCycleRisiHdCheck> deptCheckList);
	/**
	 * 根据id全部更新为审核通过
	 * @param idList
	 * <AUTHOR>
	 * @date 2019-5-9下午1:32:13
	 */
	public void updateCycleTimelineFinalStatus(List<Long> idList);
	
	/**
	 * 带教审核时间列表
	 * @param id
	 * @param deptId
	 * @param deptStdId
	 * @return
	 * <AUTHOR>
	 * @date 2019-7-12下午2:53:13
	 */
	public List<ZyyCycleRisiHdCheck> getTeacherCheckPassDateList(Long id,
			Long deptId, Long deptStdId);
	
	/**
	 * 根据人，科室，标准科室id查询审核记录
	 * @param userId
	 * @param deptId
	 * @param stdDeptId
	 * @return
	 * <AUTHOR>
	 * @date 2019-8-7下午2:21:08
	 */
	public List<ZyyCycleRisiHdCheckVO> queryHchkListByUserDeptStdDeptId(
			Long userId, Long deptId, Long stdDeptId);
	
}
