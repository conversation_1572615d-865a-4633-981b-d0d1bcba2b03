package com.hys.zyy.manage.dao;

import java.util.List;

import com.hys.zyy.manage.model.ZyyCycleTableHistory;
import com.hys.zyy.manage.model.ZyyCycleTableHistoryVO;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：张伟清 2012-10-25
 * 
 * 描述：轮转表历史记录
 * 
 * 说明:
 */
public interface ZyyCycleTableHistoryDAO {

	/**
	 * 根据医院ID 与 创建年限 查询轮转表信息
	 * @param history
	 * @return
	 */
	public List<ZyyCycleTableHistoryVO> getZyyCycleTableHistory(ZyyCycleTableHistory history) ;
	
	/**
	 * 根据医院ID 与 创建年限 查询轮转表信息  查找删除过的数据
	 * @param history
	 * @return
	 */
	public List<ZyyCycleTableHistoryVO> getZyyCylceTableHistoryByDeLTable(ZyyCycleTableHistory history);
	
	/**
	 * 根据机构 查询历史轮转表 年度列表
	 * @param orgId
	 * @return
	 */
	public List<ZyyCycleTableHistory> findTableHistoryYearByOrg(Long zyyOrgId) ;
	
	/**
	 * 添加轮转表历史记录
	 * @param history
	 * @return
	 */
	public int addZyyCycleTableHistory(ZyyCycleTableHistory history) ;
	
	/**
	 * 通过Insert Into 添加轮转表历史记录
	 * @param history
	 * @return
	 */
	public int addTableHistoryByInserInto(ZyyCycleTableHistory history) ;
	
	/**
	 * 通过Insert Into 添加轮转表住院医师轮转(历史记录)
	 * @return
	 */
	public int addTableHistoryCycleByInserInto(ZyyCycleTableHistory history) ;
	
	/**
	 * 根据ID 查询指定轮转表历史记录
	 * @param id
	 * <AUTHOR>
	 * @return
	 */
	public ZyyCycleTableHistory getZyyCycleTableHistoryById(Long id);
	
	public void updateLastUpdateDate(ZyyCycleTableHistory record);
}
