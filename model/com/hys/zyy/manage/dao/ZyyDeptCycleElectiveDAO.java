package com.hys.zyy.manage.dao;

import java.util.List;

import com.hys.zyy.manage.model.ZyyDeptCycleElective;
import com.hys.zyy.manage.model.ZyyDeptVO;

public interface ZyyDeptCycleElectiveDAO {

	/**
	 * 查询选修轮转科室表 
	 */
	public List<ZyyDeptVO> getZyyDeptCycleElectiveList(ZyyDeptCycleElective zyyDeptCycleElective);
	
	public List<ZyyDeptVO> getZyyDeptCycleElectiveList(Long cid);
	
	/**
	 * 删除选修轮转科室表 
	 */
	public void deleteZyyDeptCycleElectiveList(ZyyDeptCycleElective zyyDeptCycleElective);
	
	/**
	 * 添加选修轮转科室表 被废弃
	 */
	public int[] saveZyyDeptCycleElectiveList(List<ZyyDeptCycleElective> list);
	
	//v2
	//增加选修科室
	public Long addZyyDeptCycleElective(ZyyDeptCycleElective p);
	
	//修改选修科室
	public void updateZyyDeptCycleElective(List<ZyyDeptCycleElective> list, ZyyDeptCycleElective p);
	
	//增加选修科室子表
	public int [] addZyyDeptCycleElectiveDept(List<ZyyDeptCycleElective> list, Long id);
	
	//查看选修科室
	public List<ZyyDeptCycleElective> getDeptCycleElectiveList(ZyyDeptCycleElective p);
	
	public List<ZyyDeptCycleElective> getDeptCycleElectiveList(Long baseId,Integer educationSystem);
	
	//删除选修科室
	public void deleteZyyDeptCycleElectiveById(Long id);
	
	/**
	 * 根据基地ID和选修方案名称得到对象
	 * @param baseId
	 * @param name
	 */
	public ZyyDeptCycleElective getObjectByBaseIdAndName(Long id, Long baseId, String name);
	
}
