package com.hys.zyy.manage.dao;

import java.util.List;

import com.hys.zyy.manage.model.ZyyDeptCyclePriority;
import com.hys.zyy.manage.model.ZyyDeptVO;
import com.hys.zyy.manage.query.ZyyDeptCyclePriorityQuery;

public interface ZyyDeptCyclePriorityDAO {

	/**
	 * 查询优先轮转科室表 
	 */
	public List<ZyyDeptVO> getZyyDeptCyclePriorityList(ZyyDeptCyclePriorityQuery query);
	
	public List<ZyyDeptVO> getZyyDeptCyclePriorityList(Long pid);
	
	/**
	 * 删除优先轮转科室表 
	 */
	public void deleteZyyDeptCyclePriorityList(ZyyDeptCyclePriorityQuery query);
	
	/**
	 * 添加优先轮转科室表 
	 */
	public int[] saveZyyDeptCyclePriorityList(List<ZyyDeptCyclePriority> list);
	
	//v2
	//增加优先科室
	public Long addZyyDeptCyclePriority(ZyyDeptCyclePriority p);
	
	//修改优先科室
	public void updateZyyDeptCyclePriority(List<ZyyDeptCyclePriority> list, ZyyDeptCyclePriority p);
	
	//增加优先科室子表
	public int [] addZyyDeptCyclePriorityDept(List<ZyyDeptCyclePriority> list, Long id);
	
	//查看优先科室
	public List<ZyyDeptCyclePriority> getDeptCyclePriorityList(ZyyDeptCyclePriority p);
	
	//删除优先科室
	public void deleteZyyDeptCyclePriorityById(Long id);
	
	/**
	 * 根据基地ID和优先名称得到对象
	 * @param baseId
	 * @param name
	 */
	public ZyyDeptCyclePriority getObjectByBaseIdAndName(Long id, Long baseId, String name);
	
}
