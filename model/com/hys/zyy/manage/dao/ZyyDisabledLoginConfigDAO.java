package com.hys.zyy.manage.dao;

import java.util.List;

import com.hys.zyy.manage.model.ZyyDisabledLoginConfigVO;
import com.hys.zyy.manage.model.ZyyOrgQuery;
import com.hys.zyy.manage.model.ZyyOrgVO;
import com.hys.zyy.manage.model.ZyyUserExtendVO;
import com.hys.zyy.manage.model.ZyyUserType;
import com.hys.zyy.manage.util.DataGrid;

/**
 * 限制登录配置DAO接口
 * <AUTHOR>
 */
public interface ZyyDisabledLoginConfigDAO {
	
	public String insertSelective(ZyyDisabledLoginConfigVO record);
	
	public int updateByPrimaryKeySelective(ZyyDisabledLoginConfigVO record);
	
	public ZyyDisabledLoginConfigVO selectByPrimaryKey(String id);
	
	public void selectByParams(ZyyUserExtendVO zyyUser, DataGrid dg, ZyyDisabledLoginConfigVO query, boolean pagination);

	public List<ZyyOrgVO> selectZyyOrgs(ZyyUserExtendVO zyyUser, ZyyOrgQuery query);
	
	public List<ZyyUserType> selectZyyUserTypes(ZyyUserType query);
	
	public List<ZyyDisabledLoginConfigVO> disabledLoginCheck(ZyyUserExtendVO zyyUser);
	
	public void execute(Long zyyOrgId, Integer zyyUserType);
}
