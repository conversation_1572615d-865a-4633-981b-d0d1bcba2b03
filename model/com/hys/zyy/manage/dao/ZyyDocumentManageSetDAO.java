package com.hys.zyy.manage.dao;

import java.util.List;

import com.hys.zyy.manage.model.ZyyDocumentManageSet;
import com.hys.zyy.manage.model.ZyyDocumentManageSetVO;
import com.hys.zyy.manage.query.Pager;

public interface ZyyDocumentManageSetDAO {

    Pager<ZyyDocumentManageSetVO> getDocumentManageSetList(Pager<ZyyDocumentManageSetVO> pager);

    int save(ZyyDocumentManageSet documentManageSet);

    boolean edit(ZyyDocumentManageSet documentManageSet);

    public List<ZyyDocumentManageSetVO> selectByParams(ZyyDocumentManageSetVO query);

    double getMemorySize(Long zyyUserOrgId);
}
