package com.hys.zyy.manage.dao;

import java.util.List;

import com.hys.zyy.manage.model.ZyyEFFormIndicator;

/**
 * 评价表指数据访问接口
 * <AUTHOR>
 * @version 1.0  2012-08-30
 */
public interface ZyyEFFormIndicatorDAO {
	

	/**
	 * 取得评价表指标
	 * @param id
	 * @return
	 */
	public ZyyEFFormIndicator getZyyEFFormIndicatorById(Long id);
	
	/**
	 * 取得指定评价表的所有评价表指标
	 * @param formId
	 * @return
	 */
	public List<ZyyEFFormIndicator> getZyyEFFormIndicatorByFormId(Long formId);
	
	/**
	 * 通过表ID与指标ID取得评价表指标
	 * @param formId
	 * @param indicatorId
	 * @return
	 */
	public ZyyEFFormIndicator getZyyEFFormIndicatorByFormIdAndIndicatorId(Long formId,Long indicatorId);
	
	/**
	 * 新建评价表指标
	 * @param zyyEFFormIndicator
	 * @return 评价表指标ID
	 */
	public Long createZyyEFFormIndicator(ZyyEFFormIndicator zyyEFFormIndicator);
	
	/**
	 * 更新评价表指标
	 * @param zyyEFFormIndicator
	 * @return
	 */
	public boolean updateZyyEFFormIndicator(ZyyEFFormIndicator zyyEFFormIndicator);
	
	/**
	 * 删除评价表指标
	 * @param id
	 * @return
	 */
	public boolean deleteZyyEFFormIndicator(Long id);
}
