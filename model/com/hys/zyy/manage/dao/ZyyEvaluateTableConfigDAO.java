package com.hys.zyy.manage.dao;

import java.util.List;

import com.hys.zyy.manage.model.ZyyEvaluateTableConfig;
import com.hys.zyy.manage.model.ZyyUser;
import com.hys.zyy.manage.query.Pager;
import com.hys.zyy.manage.query.ZyyEvaluateTableConfigQuery;

/**
 * 评价表配置DAO
 * <AUTHOR>
 * @date 2018-8-20下午4:36:43
 */
public interface ZyyEvaluateTableConfigDAO {
	
	/**
	 * 查询评价表配置列表
	 * @param zyyUser
	 * @param pager
	 * @param query
	 * <AUTHOR>
	 * @date 2018-8-20下午5:25:16
	 */
	void findEvaluateTableConfigList(ZyyUser zyyUser, Pager<ZyyEvaluateTableConfig> pager,ZyyEvaluateTableConfigQuery query);
	
	/**
	 * 批量保存
	 * @param list
	 * <AUTHOR>
	 * @date 2018-8-21上午11:19:20
	 */
	void saveAllEvaluateTableConfig(List<ZyyEvaluateTableConfig> list);
	
	/**
	 * 根据id查询
	 * @param id
	 * @return
	 * <AUTHOR>
	 * @date 2018-8-21下午1:18:05
	 */
	ZyyEvaluateTableConfig getEvaluateTableConfigById(Long id);
	
	/**
	 * 更新
	 * @param config
	 * <AUTHOR>
	 * @date 2018-8-21下午1:18:43
	 */
	void updateEvaluateTableConfigById(ZyyEvaluateTableConfig config);
	
	/**
	 * 更新状态 
	 * @param status
	 * @param id
	 * <AUTHOR>
	 * @date 2018-8-21下午1:49:56
	 */
	void updateStatusById(Integer status ,Long id);
	
	/**
	 * 全部更新为禁用
	 * @param evaluateType
	 * @param orgId
	 * <AUTHOR>
	 * @date 2018-8-21下午1:50:04
	 */
	void updateAllStatusDisableById(Integer tableType,Integer evaluateType,Long orgId);
	
	/**
	 * 查询已经开启的评论表
	 * @param tableType
	 * @param evaluateType
	 * @param orgId
	 * @return
	 * <AUTHOR>
	 * @date 2018-8-21下午2:04:10
	 */
	ZyyEvaluateTableConfig getEnableConfigByEvaluateType(Integer tableType,Integer evaluateType,
			Long orgId);
	
	/**
	 * 查询所有已经开启的评论表
	 * @param pager
	 * @param query
	 * <AUTHOR>
	 * @date 2018-9-4下午3:07:59
	 */
	void findAliveEvaluateTableConfigList(Pager<ZyyEvaluateTableConfig> pager,ZyyEvaluateTableConfigQuery query);
	
	/**
	 * 根据模板tableNewId查询所有的评价表
	 * @param tableNewId
	 * @param orgId 指定医院
	 * @return
	 * <AUTHOR>
	 * @date 2018-10-23上午9:54:31
	 */
	List<ZyyEvaluateTableConfig> findEvaluateTableConfigByTableNewId(
			Long tableNewId,Long orgId);
	
	/**
	 * 根据多个模板tableNewId查询所有的评价表数量
	 * @param tableNewIdList
	 * @return
	 * <AUTHOR>
	 * @date 2018-10-23上午10:20:40
	 */
	List<ZyyEvaluateTableConfig> findEvaluateTableConfigByTableNewIdList(
			List<Long> tableNewIdList);
	
	
	/**
	 * 删除
	 */
	void deleteEvaluateTableConfig(Long id);
	
}
