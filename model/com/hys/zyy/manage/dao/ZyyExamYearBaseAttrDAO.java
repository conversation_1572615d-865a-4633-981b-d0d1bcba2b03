package com.hys.zyy.manage.dao;

import java.util.List;

import com.hys.zyy.manage.model.ZyyExamYearBaseAttr;
import com.hys.zyy.manage.model.ZyyExamYearBaseAttrSub;
import com.hys.zyy.manage.model.ZyyUserExtendVO;
import com.hys.zyy.manage.query.Pager;

public interface ZyyExamYearBaseAttrDAO {
	//保存
	void addExamYearBaseAttr(ZyyExamYearBaseAttr zyyExamYearBaseAttr);
	//分页查询
	void findExamYearBaseAttrPage(Pager<ZyyExamYearBaseAttr> pager,ZyyExamYearBaseAttr query);
	//修改  名称，状态
	void updateExamYearBaseAttr(ZyyExamYearBaseAttr zyyExamYearBaseAttr);
	//删除
	void deleteExamYearBaseAttrById(Long id);
	//根据id查询
	ZyyExamYearBaseAttr getExamYearBaseAttrById(Long id);
	//查询题库属性的子表信息
	List<ZyyExamYearBaseAttrSub> findExamYearBaseAttrSubList(List<Long> idList);
	//删除子表信息
	void deleteExamYearBaseAttrSubByPid(Long pid);
	//批量保存子表信息
	void saveAllZyyExamYearBaseAttrSub(List<ZyyExamYearBaseAttrSub> list);
	//根据用户条件 , 查询题库属性列表
	List<ZyyExamYearBaseAttrSub> findZyyExamYearBaseAttrSubList(
			ZyyUserExtendVO userExtend);
}
