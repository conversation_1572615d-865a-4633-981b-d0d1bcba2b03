package com.hys.zyy.manage.dao;

import java.util.List;

import com.hys.zyy.manage.model.ZyyGraduateTargetStudent;
import com.hys.zyy.manage.model.ZyyGraduateTargetStudentVO;
import com.hys.zyy.manage.model.ZyyUserExtendVO;
import com.hys.zyy.manage.util.DataGrid;

/**
 * 
* <p>Description: 云南省专硕生和订单定向生  学生的信息  </p>
* <AUTHOR> 
* @date 2018-6-4下午1:30:41
 */
public interface ZyyGraduateTargetStudentDAO {
	/**
	 * 根据手机号查询
	 * 
	 * @param mobileNumberList
	 * @return
	 * <AUTHOR> 
	 * @date 2018-6-4下午1:38:26
	 */
	List<ZyyGraduateTargetStudent> findAllStudentByMobileNumber(List<String> mobileNumberList);
	
	/**
	 * 根据身份证号查询
	 * @param certificateNoList
	 * @return
	 * <AUTHOR> 
	 * @date 2018-6-4下午2:07:53
	 */
	List<ZyyGraduateTargetStudent> findAllStudentByCertificate(List<String> certificateNoList);
	
	public Long add(ZyyGraduateTargetStudent record);
	
	public int edit(ZyyGraduateTargetStudent record);
	
	/**
	 * 保存
	 * @param list
	 * <AUTHOR> 
	 * @date 2018-6-4下午2:41:17
	 */
	void saveAllStudent(List<ZyyGraduateTargetStudent> list);
	
	/**
	 * 根据身份证号查询
	 * @param certificateNo
	 * @return
	 * <AUTHOR> 
	 * @date 2018-6-5下午4:42:42
	 */
	ZyyGraduateTargetStudent getStudentByCertificateNo(String certificateNo);
	
	/**
	 * 根据id查询
	 * @param id
	 * @return
	 * <AUTHOR> 
	 * @date 2018-6-5下午4:55:52
	 */
	ZyyGraduateTargetStudent getStudentById(Long id);
	
	/**
	 * 根据手机号查询
	 * @param mobileNumber
	 * @return
	 * <AUTHOR> 
	 * @date 2018-6-6上午9:23:26
	 */
	ZyyGraduateTargetStudent getStudentByMobile(String mobileNumber);
	/**
	 * 根据id更新手机号
	 * @param mobileNumber
	 * @param id
	 * <AUTHOR> 
	 * @date 2018-6-6下午1:28:51
	 */
	void updateMobileNumberById(String mobileNumber, Long id);
	
	/**
	 * 保存复制用户记录
	 * <AUTHOR>
	 * @date 2018-8-9上午11:38:43
	 */
	void saveZyyCopyUserLog(ZyyGraduateTargetStudent userInfo, Long newId);

	public ZyyGraduateTargetStudent queryRecruitStatus(Long zyyUserId);
	
	public void selectByParams(ZyyUserExtendVO zyyUser, DataGrid dg, ZyyGraduateTargetStudentVO query, boolean pagination);
	
	public int deleteByPrimaryKey(Long id);
}
