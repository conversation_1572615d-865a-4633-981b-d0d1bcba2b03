package com.hys.zyy.manage.dao;

import com.hys.zyy.manage.model.graduation.GraduationBatchSet;
import com.hys.zyy.manage.model.graduation.GraduationBatchSetAndStudent;
import com.hys.zyy.manage.model.graduation.GraduationBatchSetVO;
import com.hys.zyy.manage.model.graduation.StudentGraduationManageVO;
import com.hys.zyy.manage.query.Pager;

import java.util.List;

/**
 * @author: HLB
 * @desc: 结业批次管理服务类 dao类
 * @version: V1.0.0
 */
public interface ZyyGraduationBatchSetDAO {

    /**
     * 获取结业批次管理数据
     * @Param: pager [] 分页参数
     * @Author: hanlibin
     * @Date: 2021/12/7
     * @return: 分页
     */

    Pager<GraduationBatchSet> getGraduationBatchSet(Pager<GraduationBatchSet> pager,Long provinceId);
    /**
     * 保存或者修改批次信息
     * @param graduationBatchSetVO 批次和学员id 共同的类
     * @return  是否成功
     */
    boolean saveOrUpdate(GraduationBatchSetVO graduationBatchSetVO);
    /**
     * 获取学员结业管理数据
     * @Param: pager [] 分页参数
     * @Author: hanlibin
     * @Date: 2021/12/7
     * @return: 分页
     */
    Pager<StudentGraduationManageVO> getStudentGraduationManageList(Pager<StudentGraduationManageVO> pager, StudentGraduationManageVO query);

    /**
     * 获取学员结业管理数据
     * @Param: 不分页
     * @Author: hanlibin
     * @Date: 2021/12/7
     * @return: 分页
     */
    List<GraduationBatchSetAndStudent> getStudentList(StudentGraduationManageVO query);
    /**
    * @Author: hanlibin
    * @Desc: 保存批次和学员关联关系
    * @Param: [batchSetAndStudents, batchId]
    * @return: void
    */

    void saveBatchSetAndStudent(List<GraduationBatchSetAndStudent> batchSetAndStudents,Long batchId);

    /**
    * @Author: hanlibin
    * @Desc: 获取该批次所有的学员信息
    * @Param: [batchId]
    * @return: java.util.List<com.hys.zyy.manage.model.graduation.GraduationBatchSetAndStudent>
    */

    List<GraduationBatchSetAndStudent> getGraduationBatchSetAndStudentByBatchId(Long batchId);

    /**
    * @Author: hanlibin
    * @Desc: 删除数据
    * @Param: [graduationBatchSetVO]
    * @return: void
    */

    void delete(GraduationBatchSetVO graduationBatchSetVO);

    /**
     * @Author: hanlibin
     * @Desc: 获取批次关联的学生id
     * @Param: [batchId]
     * @return: java.util.List<com.hys.zyy.manage.model.ZyyBaseVO>
     */
    List<Long> getBatchStudentIds(Long batchId, String studentIds);

    /**
    * @Author: hanlibin
    * @Desc: 根据批次名称获取数据
    * @Param: [batchName]
    * @return: java.util.List<com.hys.zyy.manage.model.graduation.GraduationBatchSet>
    */

    List<Long> getByBatchName(String batchName,Long provinceId);
    /**
     * @Author: hanlibin
     * @Desc: 获取省份下的批次下拉列表
     * @Param: [provinceId]
     * @return: java.util.List<com.hys.zyy.manage.model.graduation.GraduationBatchSetVO>
     */

    List<GraduationBatchSet> getBatchSetList(Long provinceId);
    /**
     * @Author: hanlibin
     * @Desc: 获取批次关联的学生id（查出已经有结业成绩的id，编辑页面全部取消功能，这些id 不能取消。）
     * @Param: [batchId]
     * @return: java.util.List<com.hys.zyy.manage.model.ZyyBaseVO>
     */
    List<Long> getBatchStudentIds2(Long batchId, String studentIds);

    /**
    * @Author: hanlibin
    * @Desc: 根據id获取
    * @Param: [id]
    * @return: com.hys.zyy.manage.model.graduation.GraduationBatchSet
    */

    GraduationBatchSet getById(Long id);
    /**
     * @desc 获取批次已经关联的学生
     * <AUTHOR>
     * @date 2018-8-21 下午6:24:50
     */
    Pager<StudentGraduationManageVO> getStudentByBatchId(Pager<StudentGraduationManageVO> pager,StudentGraduationManageVO query);
}
