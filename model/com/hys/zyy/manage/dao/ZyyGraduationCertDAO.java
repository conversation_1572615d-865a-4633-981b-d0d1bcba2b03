package com.hys.zyy.manage.dao;

import java.util.List;
import java.util.Map;

import com.hys.zyy.manage.model.ZyyGraduationCertNo;
import com.hys.zyy.manage.model.ZyyGraduationCertVo;
import com.hys.zyy.manage.model.ZyyUser;
import com.hys.zyy.manage.model.ZyyUserExtendVO;
import com.hys.zyy.manage.query.Pager;

public interface ZyyGraduationCertDAO {
	
	/**
	 * @Desc 分页查询结业证书 
	 * @param zyyUser
	 * @param pager
	 * @param query
	 */

	void queryGraduationCert(ZyyUserExtendVO zyyUser, Pager<ZyyGraduationCertVo> pager,
			ZyyGraduationCertVo query);

	public List<Map<String, Object>> exportGraduationCert(ZyyUserExtendVO zyyUser, ZyyGraduationCertVo query);

	
	/**
	 * @Desc 导入证书
	 * @param zyyUser
	 * @param gradCert
	 * @return
	 */
	int importGraduationCert(ZyyUser zyyUser, ZyyGraduationCertVo gradCert);
	
	/**
	 * @Desc 根据条件查询证书列表
	 * @param query
	 * @return
	 */
	List<ZyyGraduationCertVo> findListBy(ZyyGraduationCertVo query);
	
	/**
	 * @desc 查询人员详细信息
	 * @param query
	 * @return
	 */
	List<ZyyGraduationCertVo> findUserInfo(ZyyGraduationCertVo query);

	/**
	 * @Desc 插入证书数据
	 * @param zyyGraduationCertVo
	 */
	void addGraduationCertGroup(ZyyGraduationCertVo zyyGraduationCertVo);

	/**
	 * @desc 根据ID获取
	 * @param cerId
	 * @return
	 */
	ZyyGraduationCertVo getCertDetailById(Long cerId);
	
	public int updateAuditStatusIs1(ZyyGraduationCertVo zyyGraduationCertVo);

	/**
	 * @desc 获取培训基地
	 * @param stuBaseName
	 * @return
	 */
	ZyyGraduationCertVo getGradOrgCode(String stuBaseName);

	/**
	 * @desc 获取当前年度，当前培训基地下证书序列号使用情况
	 * @param ue
	 * @return
	 */
	ZyyGraduationCertNo getZyyGraduationCertNo(ZyyGraduationCertVo ue);

	/**
	 * @desc 保存证书声称记录
	 * @param zgcerNo
	 */
	void addGraduationCerNo(ZyyGraduationCertNo zgcerNo);
}
