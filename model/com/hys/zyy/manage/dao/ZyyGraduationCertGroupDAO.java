package com.hys.zyy.manage.dao;

import java.util.List;

import com.hys.zyy.manage.model.ZyyGraduationCertGroup;
import com.hys.zyy.manage.model.ZyyGraduationCertGroupVo;
import com.hys.zyy.manage.model.ZyyUser;
import com.hys.zyy.manage.query.Pager;

public interface ZyyGraduationCertGroupDAO {

	/**
	 * @desc 分页查询结业证书批次 
	 * @param zyyUser
	 * @param pager
	 * @param query
	 */
	void queryCertGroup(ZyyUser zyyUser, Pager<ZyyGraduationCertGroupVo> pager, ZyyGraduationCertGroupVo query);

	/**
	 * @desc 更新批次信息
	 * @param zyyGraduGroup
	 * @return
	 */
	int updateGraduationCertGroup(ZyyGraduationCertGroupVo zyyGraduationCertGroupVo);

	/**
	 * @desc 新增批次信息
	 * @param zyyGraduGroup
	 */
	void addGraduationCertGroup(ZyyGraduationCertGroup zyyGraduGroup);
	
	public List<ZyyGraduationCertGroupVo> findByGroupName(String groupName, Long provinceId);
	
	public int delete(ZyyGraduationCertGroup zyyGraduGroup);
	
	public ZyyGraduationCertGroupVo get(Long groupId);

	/**
	 * @Desc 获取当前核对时间段
	 * @param provinceId
	 * @return
	 */
	List<ZyyGraduationCertGroupVo> findNowGroup(Long provinceId);
	
	/**
	 * @Desc 获取分组列表
	 * @param vo
	 * @return
	 */
	List<ZyyGraduationCertGroupVo> findByParam(ZyyUser zyyUser, ZyyGraduationCertGroupVo vo);
}
