package com.hys.zyy.manage.dao;

import java.util.List;

import com.hys.zyy.manage.model.ZyyHospitalGradeGroup;
import com.hys.zyy.manage.model.ZyyRecruitFormDict;

public interface ZyyHospitalGradeGroupDAO {
	/**
	 * 查询医院分班分组
	 * @param provinceId
	 * @param hospitalId
	 * @param yearId
	 * @return
	 * <AUTHOR>
	 * @date 2019-6-13上午11:28:40
	 */
	List<ZyyHospitalGradeGroup> findZyyHospitalGradeGroupList(Long provinceId,Long hospitalId,Long yearId);
	
	/**
	 * 查询医院分班分组,某一个专业的
	 * @param provinceId
	 * @param hospitalId
	 * @param yearId
	 * @return
	 * <AUTHOR>
	 * @date 2019-6-21上午11:28:43
	 */
	List<ZyyHospitalGradeGroup> findZyyHospitalGradeGroupListProfvalue(
			Long provinceId, Long hospitalId, Long yearId,String profValue);
	
	/**
	 * 保存
	 * @param dataList
	 * <AUTHOR>
	 * @date 2019-6-13上午11:41:53
	 */
	void saveAllZyyHospitalGradeGroupList(List<ZyyHospitalGradeGroup> dataList);
	
	/**
	 * 删除
	 * @param hospitalId
	 * @param yearId
	 * <AUTHOR>
	 * @date 2019-6-13上午11:42:43
	 */
	void deleteByHospitalYearId(Long hospitalId,Long yearId);
	/**
	 * 查询当前医院下所有的分班分组设置信息
	 * @param hospitalId
	 * @return
	 * <AUTHOR>
	 * @date 2019-6-17上午10:00:50
	 */
	List<ZyyHospitalGradeGroup> findZyyHospitalGradeGroupListByHospitalId(
			Long hospitalId);
	
    List<ZyyRecruitFormDict> getZyyRecruitFormDictList(String categoryCode,String name);
    
    List<ZyyRecruitFormDict> find(ZyyRecruitFormDict query);
    
    void addEditFormDict(ZyyRecruitFormDict dict);
    
    ZyyRecruitFormDict getZyyRecruitFormDict(String categoryCode, String name, String code);
    
    ZyyRecruitFormDict getZyyRecruitFormDictById(Long id);
    
    void updateFormDict(ZyyRecruitFormDict dict);
    
	void updateYm(ZyyRecruitFormDict dict);
    
    void changeEditFormDictStatus(ZyyRecruitFormDict dict);
    
    List<ZyyRecruitFormDict>getFormDictValuesByCategoryCode(String categoryCode);
}
