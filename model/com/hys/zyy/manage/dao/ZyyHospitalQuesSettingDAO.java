package com.hys.zyy.manage.dao;

import java.util.List;

import com.hys.zyy.manage.model.ZyyDeptQuestionAttr;
import com.hys.zyy.manage.model.ZyyHospitalQuesSetting;

public interface ZyyHospitalQuesSettingDAO {
	//新增
	void addZyyHospitalQuesSetting(ZyyHospitalQuesSetting zyyHospitalQuesSetting);
	//修改
	void updateZyyHospitalQuesSetting(ZyyHospitalQuesSetting zyyHospitalQuesSetting);
	//查询
	ZyyHospitalQuesSetting getZyyHospitalQuesSettingByHospitalId(Long hospitalId);
	
	
	//添加题库属性
	void addZyyDeptQuestionAttr(Long deptId,Long questionAttrId);
	//清空题库属性
	void deleteZyyDeptQuestionAttrByDeptId(Long deptId);
	//查询题库属性
	ZyyDeptQuestionAttr getZyyDeptQuestionAttr(Long deptId, Long questionAttrId);
	//根据科室查询题库属性
	List<ZyyDeptQuestionAttr> findQuestAttrListByDeptId(Long deptId);
}
