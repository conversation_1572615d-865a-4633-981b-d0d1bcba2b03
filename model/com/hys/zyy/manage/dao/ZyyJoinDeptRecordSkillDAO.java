package com.hys.zyy.manage.dao;

import java.util.List;
import java.util.Map;

import com.hys.zyy.manage.model.ZyyJoinDeptRecordSkill;

/**
 * 出入科记录表-成绩表DAO
 * <AUTHOR>
 * @date 2018-8-2下午2:33:37
 */
public interface ZyyJoinDeptRecordSkillDAO {
	/**
	 *  批量保存
	 * <AUTHOR>
	 * @date 2018-8-2下午2:25:20
	 */
	public void saveAllJoinDeptRecordSkill(List<ZyyJoinDeptRecordSkill> list);
	
	/**
	 * 根据出入科记录表ID  zyy_join_dept_record的id 查询
	 * <AUTHOR>
	 * @date 2018-8-2下午2:26:39
	 */
	public List<ZyyJoinDeptRecordSkill> findAllByJdrId(Long jdrId);
	
	/**
	 * 根据jdrId删除多行数据
	 * <AUTHOR>
	 * @date 2018-8-2下午2:33:11
	 */
	public int deleteJoinDeptRecordSkillByJdrId(Long jdrId);
	
	/**
	 * 根据多个id查询
	 * @param idList
	 * @return
	 * <AUTHOR>
	 * @date 2018-8-16下午7:20:28
	 */
	public List<ZyyJoinDeptRecordSkill> findAllByJdrIdList(String idList);
}
