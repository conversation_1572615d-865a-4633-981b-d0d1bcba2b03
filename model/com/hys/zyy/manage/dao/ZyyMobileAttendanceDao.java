package com.hys.zyy.manage.dao;

import java.util.Date;
import java.util.List;

import com.hys.zyy.manage.model.ZyyAttendanceUser;
import com.hys.zyy.manage.model.ZyyOrgVO;
import com.hys.zyy.manage.model.ZyyResidencyAttendanceVO;
import com.hys.zyy.manage.model.ZyyUser;

public interface ZyyMobileAttendanceDao {

	List<ZyyResidencyAttendanceVO> getAttendanceStatistics(ZyyUser zyyUser, Date startDate, Date endDate);

	List<ZyyAttendanceUser> getAttendanceDetailByMonth(ZyyUser zyyUser, Date startDate, Date endDate);

	List<ZyyAttendanceUser> getAttendanceDetailByTeacher(ZyyUser zyyUser, Date startDate, Date endDate);

	List<ZyyResidencyAttendanceVO> getAttendanceStatisticsByTeacher(ZyyUser zyyUser, Date startDate, Date endDate);

	List<ZyyOrgVO> getHospitalByName(String name, Long orgId);

	List<ZyyAttendanceUser> getAttendanceDetailByManager(ZyyUser zyyUser, Date startDate, Date endDate, Long orgId);

	List<ZyyResidencyAttendanceVO> getAttendanceStatisticsByManager(ZyyUser zyyUser, Date startDate, Date endDate,
			Long orgId);

	List<ZyyAttendanceUser> getAttendanceDetailByBase(ZyyUser zyyUser, Date startDate, Date endDate);

	List<ZyyResidencyAttendanceVO> getAttendanceStatisticsByBase(ZyyUser zyyUser, Date startDate, Date endDate);
	
	List<ZyyResidencyAttendanceVO> getAttendanceStatisticsByDept(ZyyUser zyyUser, Date startDate, Date endDate);
	
	List<ZyyAttendanceUser> getAttendanceDetailByDept(ZyyUser zyyUser, Date startDate, Date endDate);
	
	List<ZyyAttendanceUser> getAttendanceDetailByUserId(ZyyUser zyyUser, Date startDate, Date endDate);

	void updateAttendanceStatus(List<ZyyAttendanceUser> attendanceList);
}
