package com.hys.zyy.manage.dao;

import java.util.List;

import com.hys.zyy.manage.model.ZyyNeedValuateRecord;
import com.hys.zyy.manage.model.ZyyUserExtendVO;
import com.hys.zyy.manage.model.vo.ZyyEvaluateUserVO;
import com.hys.zyy.manage.query.Pager;
import com.hys.zyy.manage.query.ZyyUserQuery;

/**
 * 待评价内容记录表 DAO
 * <AUTHOR>
 * @date 2018-9-4上午10:40:27
 */
public interface ZyyNeedValuateRecordDAO {
	/**
	 * 删除，然后才保存新数据 这三个值，可以确定一类数据
	 * @param record
	 * @param query
	 * <AUTHOR>
	 * @date 2018-9-4上午10:57:45
	 */
	void deleteNeedValuateRecord(ZyyNeedValuateRecord record,ZyyEvaluateUserVO query);
	
	/**
	 * 保存 
	 * @param record
	 * <AUTHOR>
	 * @date 2018-9-4上午11:00:16
	 */
	void saveNeedValuateRecord(ZyyNeedValuateRecord record);
	
	/**
	 * 查询所有的带教
	 * @param pager
	 * @param query
	 * <AUTHOR>
	 * @date 2018-9-4下午4:26:03
	 */
	void findAllTeacherList(Pager<ZyyUserExtendVO> pager, ZyyUserQuery query);
	
	/**
	 * 根据条件查询记录
	 * @param record
	 * @param query
	 * @return
	 * <AUTHOR>
	 * @date 2018-9-6下午5:24:55
	 */
	ZyyNeedValuateRecord getNeedValuateRecordByConditions(ZyyNeedValuateRecord record,ZyyEvaluateUserVO query);
	
	/**
	 * 根据多个唯一值删除
	 * @param uniqueKeys
	 * <AUTHOR>
	 * @date 2018-9-13上午10:19:50
	 */
	void deleteNeedValuateRecordByUniqueKeys(String uniqueKeys);
	/**
	 * 批量保存
	 * @param list
	 * <AUTHOR>
	 * @date 2018-9-13上午10:20:31
	 */
	void saveAllNeedValuateRecord(List<ZyyNeedValuateRecord> list);
	
}
