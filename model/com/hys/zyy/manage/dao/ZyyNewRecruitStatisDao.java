package com.hys.zyy.manage.dao;

import java.util.List;

import com.hys.zyy.manage.model.ZyyRecruitStatis;
import com.hys.zyy.manage.query.ZyyRecruitStatisQuery;


/**
 * 报名招录统计分析  新的实现DAO
 * <AUTHOR>
 */
public interface ZyyNewRecruitStatisDao {
	
	/**、
	 * 统一一次查询出来这个省厅下的所有医院的所有学科的人数
	 * @param query
	 * @return
	 */
	public List<ZyyRecruitStatis> getSignUpStatisByHosp(ZyyRecruitStatisQuery query);

	public List<ZyyRecruitStatis> getAdmitStatisByHosp(ZyyRecruitStatisQuery query, Integer statisType);

	/**
	 * 此方法是报名统计中 把所有医院的所有的小结数据   统一一次查询出来
	 * @param query
	 * @return
	 */
	List<ZyyRecruitStatis> getSignUpSummaryStatisByHosp(ZyyRecruitStatisQuery query);
	
}
