package com.hys.zyy.manage.dao;

import java.util.List;

import com.hys.zyy.manage.model.ZyyBaseVO;
import com.hys.zyy.manage.model.ZyyOldUserBaseSetVO;
import com.hys.zyy.manage.model.ZyyRecruitFormDictVO;
import com.hys.zyy.manage.util.DataGrid;

public interface ZyyOldUserBaseSetDAO {
	
	public int updateState(ZyyOldUserBaseSetVO record);
	
	public void batchUpdateState(List<ZyyOldUserBaseSetVO> records);
	
	public void batchAdd(ZyyOldUserBaseSetVO record, Long[] zyyBaseStdIds);
	
	public void batchAdd(List<ZyyOldUserBaseSetVO> records);
	
	public void selectByParams(DataGrid dg, ZyyRecruitFormDictVO query, boolean pagination);
	
	public List<ZyyBaseVO> findZyyBases(ZyyOldUserBaseSetVO query);

}
