package com.hys.zyy.manage.dao;

import java.util.List;

import com.hys.zyy.manage.model.vo.ZyyPortalExamSpecialVO;

public interface ZyyPortalExamSpecialDAO {
	/**
	 * 查询正在考试的学生信息 -- rct
	 * @param examId
	 * @return
	 * <AUTHOR>
	 * @date 2020-7-1上午11:45:41
	 */
	List<ZyyPortalExamSpecialVO> findZyyPortalExamSpecialVOListRct(Long examId);
	/**
	 * 更新考试状态，开始时间，结束时间
	 * @param item
	 * <AUTHOR>
	 * @date 2020-7-1下午1:16:58
	 */
	void updateEmNumberStudent(ZyyPortalExamSpecialVO item);
	/**
	 * 更新总分数
	 * @param item
	 * <AUTHOR>
	 * @date 2020-7-1下午1:23:09
	 */
	void updateEmExamineeInfo(ZyyPortalExamSpecialVO item);
	/**
	 * 更新总分数
	 * @param item
	 * <AUTHOR>
	 * @date 2020-7-1下午1:27:27
	 */
	void updateEmExamCourseScore(ZyyPortalExamSpecialVO item);
	/**
	 * 新增
	 * @param item
	 * <AUTHOR>
	 * @date 2020-7-1下午1:28:52
	 */
	void addEmExamCourseScore(ZyyPortalExamSpecialVO item);
	/**
	 * 查询正在考试的学生信息 -- portal
	 * @param examId
	 * @return
	 * <AUTHOR>
	 * @date 2020-7-1下午2:00:09
	 */
	List<ZyyPortalExamSpecialVO> findZyyPortalExamSpecialVOListPortal(
			Long examId);
}
