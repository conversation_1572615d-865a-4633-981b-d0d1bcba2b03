package com.hys.zyy.manage.dao;

import java.util.List;

import com.hys.zyy.manage.model.ZyyPrepareResidencyWillVO;
import com.hys.zyy.manage.util.DataGrid;

public interface ZyyPrepareResidencyWillDAO {

	public String insertSelective(ZyyPrepareResidencyWillVO record);

	public int updateByPrimaryKeySelective(ZyyPrepareResidencyWillVO record);

	public ZyyPrepareResidencyWillVO selectByPrimaryKey(String id);

	public List<ZyyPrepareResidencyWillVO> selectByParams(ZyyPrepareResidencyWillVO query);

	public void selectByParams(DataGrid dg, ZyyPrepareResidencyWillVO query, boolean pagination);

}