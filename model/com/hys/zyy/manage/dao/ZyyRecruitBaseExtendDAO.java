package com.hys.zyy.manage.dao;

import java.util.List;

import com.hys.zyy.manage.model.ZyyRecruitBaseExtend;
import com.hys.zyy.manage.model.ZyyRecruitBaseExtendVO;
import com.hys.zyy.manage.query.ZyyRecruitBaseExtendQuery;

/**
 * 
 * 标题：zyy
 * 
 * 作者：Tony Mar 20, 2012
 * 
 * 描述：基地扩展信息
 * 
 * 说明:
 */
public interface ZyyRecruitBaseExtendDAO {
	
	/**
	 * 修改基地扩展信息
	 */
	public int updateRecruitBaseExtend(ZyyRecruitBaseExtend extend);
	
	public void batchUpdate(List<ZyyRecruitBaseExtendVO> records);
	
	/**
	 * 修改基地扩展信息(提交)
	 * @param recruitBaseExtendList
	 */
	public void updateRecruitBaseExtendAll(List<ZyyRecruitBaseExtend> recruitBaseExtendList);
	
	/**
	 * 执行医院修改申请
	 * 修改基地扩展信息
	 * @param recruitBaseExtendList
	 */
	public void updateRecruitBaseExtends(List<ZyyRecruitBaseExtend> recruitBaseExtendList);
	
	/**
	 * 通过医院id和年度id 查询基地扩展信息
	 * @param hostipalId
	 * @param yearId
	 * @return
	 */
	public List<ZyyRecruitBaseExtendVO> getRecruitBaseExtendByHospAndYear(Long hostipalId, Long yearId, Integer stageType);
	
	public List<ZyyRecruitBaseExtendVO> getRecruitBaseExtendByHospAndYearAndHospType(Integer hospType,Long hostipalId, Long yearId, Integer stageType, Integer stageId, Integer assistant);
	
	/**
	 * 通过医院id和年度id 查询基地扩展信息 招录人数
	 * @param hostipalId
	 * @param yearId
	 * @return
	 */
	public List<ZyyRecruitBaseExtendVO> getRecBaseExtByHospAndYear(Long hostipalId,Long yearId);
	
	/**
	 * 通过ID 与 招录志愿ID 查询指定扩展基地信息
	 * @return
	 */
	public List<ZyyRecruitBaseExtendVO> getRecruitBaseExtendByIdAndRecruit(Long id, Long recruitStageId, Integer resiSource);
	
	/**
	 * 查询本省市未开启的基地信息
	 * @param orgId
	 * @param yearId
	 * @param stageId 
	 * @return
	 */
	public List<ZyyRecruitBaseExtendVO> getRecruitBaseExtendByYearAndOrg(Long orgId, Long yearId, Integer stageId, Integer hospType);
	
	/**
	 * 添加扩展基地信息
	 * @param extend
	 */
	public int addZyyRecruitBaseExtend(ZyyRecruitBaseExtend extend) ;
	
	/**
	 * 批量保存扩展基地信息
	 * @param extendList
	 */
	public void addZyyRecruitBaseExtend(List<ZyyRecruitBaseExtend> extendList) ;
	
	/**
	 * 基地计划招生总人数
	 * @param orgId
	 * @param yearId
	 * @return
	 */
	public List<ZyyRecruitBaseExtendVO> getBaseStdPlanNum(Long orgId, Long hostipalId, Long yearId);
	
	/**
	 * 通过ID查询指定扩展基地信息
	 * @return
	 */
	public ZyyRecruitBaseExtendVO getRecruitBaseExtendById(Long id);
	
	/**
	 * 通过基地与年度ID查询扩展基地信息
	 * @return
	 */
	public ZyyRecruitBaseExtendVO getRecBaseExtendByBaseAndYear(Long baseId, Long yearId);
	
	/**
	 * 通过基地与年度ID,阶段ID查询扩展基地信息
	 * @param baseId
	 * @param yearId
	 * @param stageId
	 * @return
	 */
	public ZyyRecruitBaseExtendVO getBaseExtendByBaseAndYearAndStageId(Long baseId, Long yearId, Long stageId);

	public void removeZyyRecruitBaseExtendGreatThanStage(Long orgId,Long yearId, int stage);
	
	/**
	 * @desc 查询招生计划
	 * @param query
	 * @return
	 */
	List<ZyyRecruitBaseExtendVO> getZyyRecruitBaseExtendList(String type,ZyyRecruitBaseExtendQuery query);

	void updateRecruitBasePlan(List<ZyyRecruitBaseExtend> recruitBaseExtendList);
	
	/**
	 * 查询计划数量 -- lzq 2018-7-31
	 * @param zyyUserProvinceId  省份
	 * @param hospitalId 医院
	 * @param yearId 年度 
	 * @param baseId 专业
	 * @return
	 */
	public List<ZyyRecruitBaseExtend> getPlanNumByProHosYearBaseId(Long zyyUserProvinceId, Long hospitalId, Long yearId, Long baseId, Long stageId);
}