package com.hys.zyy.manage.dao;

import java.util.List;

import com.hys.zyy.manage.model.ZyyRecruitModify;
import com.hys.zyy.manage.model.ZyyRecruitModifyDetail;

/**
 * 
 * 标题：zyy
 * 
 * 作者：Tony Mar 21, 2012
 * 
 * 描述：医院修改计划招生人数详细
 * 
 * 说明:
 */
public interface ZyyRecruitModifyDetailDAO {
	
	/**
	 * 获取医院修改计划招生人数详细
	 * @param recruitModifyId
	 * @return
	 */
	public List<ZyyRecruitModifyDetail> getRecruitModifyDetailList(Long recruitModifyId);
	
	/**
	 * 获取医院修改计划招生人数详细
	 * @param detail
	 * @return
	 */
	public List<ZyyRecruitModifyDetail> getRecruitModifyDetailList(ZyyRecruitModify detail);
}
