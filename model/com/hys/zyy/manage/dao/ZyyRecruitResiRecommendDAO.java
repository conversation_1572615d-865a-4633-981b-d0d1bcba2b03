package com.hys.zyy.manage.dao;

import java.util.List;

import com.hys.zyy.manage.model.ZyyOrgStageVO;
import com.hys.zyy.manage.model.ZyyOrgVO;
import com.hys.zyy.manage.model.ZyyRecruitBaseExtendVO;
import com.hys.zyy.manage.model.ZyyRecruitResiRecommend;
import com.hys.zyy.manage.model.ZyyRecruitResiRecommendVO;
import com.hys.zyy.manage.model.ZyyRecruitResidencyWillVO;
import com.hys.zyy.manage.model.ZyyRecruitStage;



public interface ZyyRecruitResiRecommendDAO {

	/**
	 * 查询医院在这个年度这个阶段下的不同的学科的招录人数  --->包括单位人和社会人都会查出来
	 * @param hospId
	 * @param yearId
	 * @param stageId
	 * @return
	 */
	List<ZyyRecruitBaseExtendVO> getOrgBaseList(Long hospId, Long yearId, Long stage, Integer hospType);
	/**
	 * 获取招录的学科对应的单位人/社会人  在本志愿之前已经录取的人数
	 * @param will
	 * @param recruitBaseId
	 * @param residencySource
	 * @return
	 */
	List<Long> getBaseAdmitList(Integer will, Long recruitBaseId, Integer residencySource, Integer maxProcessLevel);
	/**
	 * 根据id查询出来招录阶段的信息
	 * @param id
	 * @return
	 */
	ZyyRecruitStage getZyyRecruitStageById(Long id);
	/**
	 * 查询学科下的报名通过的志愿  学员
	 * @param residencySource
	 * @param baseId
	 * @param yearId
	 * @param stage
	 * @param will
	 * @param willSeq
	 * @return
	 */
	List<ZyyRecruitResidencyWillVO> getBaseExtendUsers(Integer residencySource, Long hospitalId,
			Long baseId, Long yearId, Long stage, Integer will, Integer willSeq, List<Long> coll);
	/**
	 * 获取此招录年度志愿填报的信息
	 * @param orgId
	 * @param yearId
	 * @param stageType
	 * @param hospType
	 * @param stage
	 * @return
	 */
	ZyyOrgStageVO getZyyOrgStage(Long orgId, Long yearId, Integer stageType, Integer hospType, Long stage);
	/**
	 * 查询出在本志愿之前已经录取的学员的id的集合
	 * @param yearId
	 * @param stage
	 * @param will
	 * @param residencySource
	 * @param processLevel
	 * @return
	 */
	List<Long> getWillAdmitList(Long yearId, Long stage, Integer will, Integer residencySource, Integer processLevel);
	/**
	 * 将ZyyRecruitResiRecommend这个对象中的数据保存到数据库中
	 * @param recommend
	 */
	void addZyyRecruitResiRecommend(ZyyRecruitResiRecommend recommend);
	/**
	 * 获得录取操作中  最大的录取级别
	 * @param orgId
	 * @param residencySource
	 * @param hospType
	 * @return
	 */
	int getMaxProcessLevel(Long orgId, Integer residencySource, Integer hospType);
	/**
	 * 在保存之前  先根据医院id，阶段，第几志愿  来删除recommend表中的数据
	 * @param hospId
	 * @param stage
	 * @param will
	 */
	void delRecommendByHospStageWill(Long hospId, Long stage, Integer will, Long yearId, Integer hospType, Integer residencySource);
	/**
	 * 删除推荐表的数据
	 * @param provinceId
	 */
	void delRecommendChangeScore(Long provinceId);
	/**
	 * 判断是否可以下载
	 * @param yearId
	 * @param hospType
	 * @param residencySource
	 * @param hospitalId
	 * @param stage
	 * @param will
	 * @param provinceId
	 * @param flag
	 * @return
	 */
	int getRecommendcount(Long yearId, Integer hospType,
			Integer residencySource, Long hospitalId, Long stage, Integer will, Long provinceId);
	
	/**
	 * 获取推荐名单
	 * @param residencySource
	 * @param hospType
	 * @param yearId
	 * @param provinceId
	 * @param stage
	 * @param will
	 * @param hospitalId
	 * @param flag
	 * @return
	 */
	List<ZyyRecruitResiRecommendVO> getRecommendList(Integer residencySource,
			Integer hospType, Long yearId, Long provinceId, Long stage,
			Integer will, Long hospitalId, Integer flag);
	/**
	 * 查询医院
	 * @param orgId
	 * @param typeFlag
	 * @param order
	 * @return
	 */
	List<ZyyOrgVO> getZyyHospitalListByOrgId(Long orgId, int typeFlag, int order, Long hospitalId);

}
