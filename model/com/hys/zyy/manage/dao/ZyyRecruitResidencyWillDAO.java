package com.hys.zyy.manage.dao;

import java.util.List;

import com.hys.zyy.manage.model.ZyyRecruitResidencyWill;
import com.hys.zyy.manage.model.ZyyRecruitResidencyWillVO;
import com.hys.zyy.manage.query.ZyyRecruitQuery;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：张伟清 2012-03-21
 * 
 * 描述：
 * 
 * 说明:
 */
public interface ZyyRecruitResidencyWillDAO {

	/**
	 * 添加志愿信息
	 * 
	 * @param recWill
	 * @return
	 */
	public int addZyyRecruitResidencyWill(ZyyRecruitResidencyWill recWill) ;
	
	/**
	 * 修改住院医师志愿信息
	 * 
	 * @param will
	 * @return
	 */
	public int updateZyyRecruitResidencyWill(ZyyRecruitResidencyWill will) ;
	
	/**
	 * 根据主键查询志愿信息
	 * @param willId
	 * @return
	 */
	public List<ZyyRecruitResidencyWillVO> getZyyRecruitResidencyWillById(Long willId) ;
	
	/**
	 * 根据年度、用户、阶段 查询需要审核的志愿信息
	 * @param yearId
	 * @param userId
	 * @param stageId
	 * @return
	 */
	public List<ZyyRecruitResidencyWillVO> getZyyRecruitResiWillByQuali(Long yearId, Long userId, Long stageId) ;
	
	/**
	 * 查询住院医师志愿信息
	 * 
	 * @param query
	 * @return
	 */
	public List<ZyyRecruitResidencyWillVO> getRecruitWillListByStage(ZyyRecruitQuery query) ;
	
	/**
	 * 查询学员志愿信息
	 * @param 
	 * @return
	 */
	public List<ZyyRecruitResidencyWillVO> getRecruitWillListByUser(Long stageId, Long userId, Long hospitalId);
	
	/**
	 * 查询住院医师志愿信息
	 * @param query
	 * @return
	 * <AUTHOR>
	 */
	public List<ZyyRecruitResidencyWillVO> getRecruitWillListByStageJL(ZyyRecruitQuery query);
	
	/**
	 * 查询住院医师志愿信息
	 * 
	 * @param query
	 * @return
	 */
	public List<ZyyRecruitResidencyWillVO> getRecruitWillListByHosp(ZyyRecruitQuery query) ;
	
	/**
	 * 通过学员用年度ID 查询志愿信息
	 * @return
	 */
	public List<ZyyRecruitResidencyWillVO> getResidencyWillByUserAndYear(Long userId, Long yearId) ;
	
	/**
	 * 根据招录阶段ID 查询住院医师志愿信息
	 * 
	 * @param recStageId
	 * @param userId
	 * @return
	 */
	public List<ZyyRecruitResidencyWill> getRecruitWillByStageAndUser(Long recStageId, Long userId) ;
	
	/**
	 * 根据学员ID、年度ID 查询学员所有志愿信息
	 */
	public List<ZyyRecruitResidencyWillVO> getZyyRecruitResiWillList(Long userId, Long yearId) ;
	
	/**
	 * 根据学员ID 所有阶段的志愿信息
	 * 
	 * @param userId
	 * @return
	 */
	public List<ZyyRecruitResidencyWillVO> getZyyRecruitWillByStage(Long userId, Long orgId, Integer residencySource) ;
	
	public List<ZyyRecruitResidencyWillVO> getZyyRecruitWillByStageAndHospType(Long userId, Long orgId, Integer residencySource,Integer hospType);
	
	/**
	 * 查询用户已经录取的志愿信息
	 * @param userId
	 * @return
	 */
	public ZyyRecruitResidencyWillVO getRecruitWillByUser(Long userId, Integer recruitType) ;
	
	/**
	 * 查询用户阶段志愿信息
	 * @param userId 用户Id
	 * @param stageId 阶段Id
	 * @return
	 */
	public List<ZyyRecruitResidencyWillVO> getRecruitWillByUserAndStage(Long userId, Long yearId, Long stageId,Long orgId) ;
	
	/**
	 * 根据用户ID 删除志愿信息
	 * @param userId
	 * @return
	 */
	public int deleteZyyRecruitResidencyWill(Long userId) ; 
	
	/**
	 * 查询用户已经录取的志愿信息
	 * @param userId
	 * @param recruitType 招录类别 1.上海 2.北京 3.吉林
	 * @return
	 */
	public int deleteZyyResidencyWillById(Long willId, Long userId) ;
	
	/**
	 * 根据阶段id,医院id查询所有志愿  
	 * @param stageId
	 * @param hospitalId
	 * @return
	 */
	public List getZyyResidencyWillsByStageAndHospital(Long stageId, Long hospitalId);
	
	/**
	 * 根据学员ID 查询是否有调剂志愿信息
	 * @param userId
	 * @return
	 */
	public ZyyRecruitResidencyWillVO getRecruitAdjustedResiWill(Long userId) ;
	
	public List<ZyyRecruitResidencyWillVO> getRecruitAdjustedResiWill(Long userId, Long stageId, Integer isAdjusted, Integer isAdjustedFlag);
	/**
	 *根据学员id,调剂/录取级别查询志愿信息
	 * @param userId 学员id
	 * @param stageId 阶段id
	 * @param level 几级
	 * @param isAdjustedFlag 是否被调剂
	 * @param status 状态
	 * @return      
	 */
	public List<ZyyRecruitResidencyWillVO> getRecruitResidencyWill(Long userId, Long stageId, Integer level, Integer isAdjustedFlag, Integer status);
	
	/**
	 * 查询志愿人数还剩多少
	 * @param residency_source
	 * @param baseId
	 * @param hospitalId
	 * @param yearId
	 * @return
	 */
	public int getRecruitResidencyWillNum(int residency_source, long baseId, long hospitalId, long yearId, long stageId);
	
	/**
	 * 根据用户ID 删除调剂志愿信息
	 * @param userId
	 * @return
	 */
	public int deleteRecruitAdjustedWill(Long userId) ;
	/**
	 * 根据阶段ID 查询志愿列表
	 * @param stageId
	 * @return
	 */
	public List<ZyyRecruitResidencyWill> getZyyRecruitWishByStageId(Integer stageId) ;
	
	/**
	 *  查询志愿列表
	 * @param year 年度
	 * @param hospType 医院类别1 中医 2西医
	 * @param resiSource 学员类别 
	 * @param stage 阶段 1,2,3
	 * @return
	 */
	public List<ZyyRecruitResidencyWill> getZyyRecruitWish(Long year,Integer hospType,Integer resiSource,Integer stage) ;
	
	/**
	 * 根据阶段ID,志愿ID 查询专科列表
	 * @param stageId
	 * @return
	 */
	public List<ZyyRecruitResidencyWill> getZyyRecruitSubjectByIds(Integer stageId,Integer wishId) ;
	
	/**
	 *  查询专科列表
	 * @param year 年度
	 * @param hospType 医院类别1 中医 2西医
	 * @param resiSource 学员类别 
	 * @param stage 阶段 1,2,3
	 * @param wish 志愿 1,2,3
	 * @return
	 */
	public List<ZyyRecruitResidencyWill> getZyyRecruitSubject(Long year,Integer hospType,Integer resiSource,Integer stage,Integer wish) ;
	
	/**
	 * 取得用户是否已经报名资格审核
	 * @param zyyUserId
	 * @return
	 */
	public boolean getSignUpVerify(Long zyyUserId);
	
	public boolean getCheckMessage(Long userId, Long year, Integer hospType,
			Integer resiSource, Long hospitalId,Long stageId);

	public void updateZyyRecruitResidencyWillList(List<ZyyRecruitResidencyWillVO> willList);
	
	public void updateIsAdjust(List<ZyyRecruitResidencyWillVO> records);
	
	/**
	 * 根据用户id和阶段查询志愿信息,仅查询 特殊的几个状态 录取14，报道41，已淘汰20
	 * @param residencyId
	 * @param recruitStageId
	 * @return
	 */
	public List<ZyyRecruitResidencyWillVO> getRecruitResidencyWillByUserId(
			Long residencyId, Long recruitStageId);
	
	/**
	 * 查询已经录取或者拟录取的人数
	 * @param hospitalId
	 * @param baseId
	 * @param yearId
	 * @return
	 */
	public int getRecruiteTotalNum(Long hospitalId,Long baseId,Long yearId, Long stageId);
	
	/**
	 * 查询其他专业，已经录取或者拟录取的人数
	 * @param zyyUserProvinceId
	 * @param hospitalId
	 * @param hospType
	 * @param yearId
	 * @return
	 */
	public int getOtherBaseRecruiteTotalNum(Long zyyUserProvinceId, Long hospitalId,Integer hospType,Long yearId,Long stageId);
	
	/**
	 * 把非当前开启年度的志愿状态改为删除  ， 因为招录仅处理当前年度的数据，不删除，会导致数据错乱
	 * @param userId
	 * @param yearId
	 * <AUTHOR>
	 * @date 2019-9-2下午4:47:06
	 */
	public void deleteNotCurrentYearResidencyWill(Long userId, Long yearId);
	
	public List<ZyyRecruitResidencyWillVO> getRecruitBase(ZyyRecruitResidencyWillVO query);
	
	public List<ZyyRecruitResidencyWillVO> getRecruitResidencys(ZyyRecruitResidencyWillVO query);
}