package com.hys.zyy.manage.dao;

import java.util.List;
import java.util.Map;

import com.hys.zyy.manage.model.ZyyProcessDetailVO;
import com.hys.zyy.manage.model.ZyyResiHbAuditRecordVO;

public interface ZyyResiHbAuditRecordDAO {

	public List<ZyyProcessDetailVO> queryOrgProcess();

	public List<ZyyResiHbAuditRecordVO> selectByParams(ZyyResiHbAuditRecordVO query, Map<String, String> processLevelToCodeMap);

}