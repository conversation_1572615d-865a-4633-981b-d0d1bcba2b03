package com.hys.zyy.manage.dao;

import java.util.List;

import com.hys.zyy.manage.model.ZyyAdmissionTemplateSubject;
import com.hys.zyy.manage.model.ZyyRecruitResidencyWillVO;
import com.hys.zyy.manage.model.ZyyResidencyAdmission;
import com.hys.zyy.manage.model.ZyyResidencyAdmissionVO;
import com.hys.zyy.manage.model.ZyyUser;
import com.hys.zyy.manage.query.Pager;
import com.hys.zyy.manage.query.ZyyResidencyAdmissionQuery;


/**
 * 住院医师准考证信息数据存取接口
 *     
 *    
 * <AUTHOR>     
 * @version 1.0    
 * @created 2012-9-10 下午01:50:29
 * @see com.hys.zyy.manage.dao.jdbc.ZyyResidencyAdmissionJDBCDAO
 */
public interface ZyyResidencyAdmissionDAO {
	
	/**
	 * 查询报名资格审核通过的学员信息与准考证信息
	 * @param loginedUser 当前登录对象
	 * @return
	 */
	public List<ZyyResidencyAdmissionVO> getZyyUserAndResidencyAdmissionList(ZyyUser loginedUser, ZyyResidencyAdmissionQuery query);
	
	/**
	 * 查询报名资格审核通过的学员信息与准考证信息
	 * @param loginedUser 当前登录对象
	 * @return
	 */
	public void getZyyUserAndResidencyAdmissionList(ZyyUser loginedUser, ZyyResidencyAdmissionQuery query, Pager<ZyyResidencyAdmissionVO> pager);
	
	/**
	 * 批量新增学员准考证信息  
	 * @param admissionList
	 */
	public void batchAddAdmission(List<ZyyResidencyAdmission> admissionList);
	
	/**
	 * 批量修改学员准考证信息  
	 * @param admissionList
	 */
	public void batchUpdateAdmission(List<ZyyResidencyAdmission> admissionList);
	
	/**
	 *   根据学员id和年份id获取条数
	 * @param userId
	 * @param yearId
	 * @return
	 */

	public int getAdmissionCounts(Long userId, Long yearId, Long zyyOrgId,Long recruitStageId);
	
	/**
	 * 发布准考证信息
	 */
	public String batchUpdateAdmissionStatus(ZyyUser loginedUser, ZyyResidencyAdmissionQuery query);

	public List<ZyyResidencyAdmission> getResidencyAdmissionList(ZyyUser zyyUser, ZyyResidencyAdmissionQuery query);
	
	public List<ZyyAdmissionTemplateSubject> getAdmissionSubject(ZyyUser zyyUser);

	public List<ZyyRecruitResidencyWillVO> getResidencyWillList(ZyyUser zyyUser, ZyyResidencyAdmissionQuery query);
	
	/**
	 * 批量删除学员准考证信息  
	 * @param admissionList
	 */
	public void batchDeleteAdmission(List<ZyyResidencyAdmission> admissionList);
}
