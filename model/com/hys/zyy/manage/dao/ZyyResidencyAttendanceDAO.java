package com.hys.zyy.manage.dao;

import java.util.Date;
import java.util.List;

import com.hys.zyy.manage.model.ZyyResidencyAttendance;
import com.hys.zyy.manage.model.ZyyResidencyAttendanceVO;
import com.hys.zyy.manage.query.Pager;
import com.hys.zyy.manage.query.ZyyAttendanceQuery;

public interface ZyyResidencyAttendanceDAO {

	public List<ZyyResidencyAttendanceVO> getListZyyResidencyAttendance(ZyyAttendanceQuery zyyAttendanceQuery);
	
	public ZyyResidencyAttendance getFillAttendance(Long deptId,Date date);
	
	public boolean addZyyResidencyAttendance(ZyyResidencyAttendance zyyResidencyAttendance);
	
	public void updateZyyResidencyAttendance(ZyyResidencyAttendance zyyResidencyAttendance);
	
	public boolean deleteZyyResidencyAttendance(ZyyResidencyAttendance zyyResidencyAttendance);
	
	public Integer getAttendanceNumberForDate(Long userId,Integer type,Date date);
	
	public Integer getAttendanceNumberForDate(Long orgId,Long baseId,Long deptId,Integer type,Date date);
	
	public void getAttendanceListForDate(Long orgId,Long baseId,Long deptId,Integer type,Date date,Pager<ZyyResidencyAttendanceVO> pager);
	
	public List<ZyyResidencyAttendanceVO> getAttendanceReportListForBase(Date startDate,Date endDate,Long yearId,String name,Long baseId);
	
	public List<ZyyResidencyAttendanceVO> getAttendanceReportListForDept(Date startDate,Date endDate,Long yearId,String name,Long deptId,Integer type);
	
	public ZyyResidencyAttendanceVO getAttendanceReportForDept(Date startDate,Date endDate,Long userId,Long deptId,Integer type);
	
	public Date getAttendanceDeptLastUpdateDate(Date startDate,Date endDate,Long yearId,String name,Long deptId);
	
	public Date getFillAttendanceDeptLastUpdateDate(Date startDate,Date endDate,Long yearId,String name,Long deptId);
	
	public List<ZyyResidencyAttendanceVO> getAttendanceListForDateForApp(Long deptId,Integer type,String date,Long year);
	
	public List<ZyyResidencyAttendanceVO> getAttendanceListForApp(Long orgId,Long deptId,String date);
	
	public void deleteAttendanceById(Long id);
	
	public List<ZyyResidencyAttendanceVO> selectByParams(ZyyResidencyAttendanceVO query);
}
