package com.hys.zyy.manage.dao;

import java.util.List;

import com.hys.zyy.manage.model.ZyyCustomizeColumnsItemVO;
import com.hys.zyy.manage.model.ZyyCustomizeColumnsVO;
import com.hys.zyy.manage.model.ZyyCustomizeTableData;
import com.hys.zyy.manage.model.ZyyCycleTeacherVO;
import com.hys.zyy.manage.model.ZyyDeptVO;
import com.hys.zyy.manage.model.ZyyEvaluateResidencyVO;
import com.hys.zyy.manage.model.ZyyEvaluateShowVO;
import com.hys.zyy.manage.model.ZyyEvaluateTableVO;
import com.hys.zyy.manage.model.ZyyUserEvaluate;
import com.hys.zyy.manage.model.ZyyUserEvaluateVO;
import com.hys.zyy.manage.query.Pager;
import com.hys.zyy.manage.query.ZyyEvaluateQuery;


/**
 * 
 */

public interface ZyyResidencyEvaluateDAO extends ZyyJdbcDao {

	/**
	 * 学员评价代带教老师 
	 * @param query
	 * @param pager
	 * @return
	 */
	List<ZyyCycleTeacherVO> getResiCycleTeacherEvaluate(ZyyEvaluateQuery query, Pager<ZyyCycleTeacherVO> pager);

	/**
	 * 学员评价科室 
	 * @param query
	 * @param pager
	 * @return
	 */
	List<ZyyDeptVO> getResiCycleDeptEvaluate(ZyyEvaluateQuery query, Pager<ZyyDeptVO> pager);

	/**
	 * 根据id查询cycleTeacher表中的数据
	 * @param id
	 * @return
	 */
	ZyyCycleTeacherVO getCycleTeacherById(Long id);
	/**
	 * 获取科室对应的某类型的评价表
	 * @param deptId
	 * @param tableType
	 * @return
	 */
	ZyyEvaluateTableVO getEvaluateTableVOByDept(Long deptId, Integer tableType);

	/**
	 * 获取评价表的所有的列
	 * @param tableId
	 * @return
	 */
	List<ZyyCustomizeColumnsVO> getEvaluateTableColumns(Long tableId);
	/**
	 * 新增用户评价表的记录
	 * @param eval
	 */
	long addZyyUserEvaluate(ZyyUserEvaluateVO eval);
	/**
	 * 新增用户评价表的记录
	 * @param eval
	 */
	long addZyyUserEvaluate(ZyyUserEvaluate eval);
	/**
	 * 保存用户评价表填写的数据
	 * @param tableCoumnData
	 */
	void addZyyCustomizeTableData(ZyyCustomizeTableData tableCoumnData);
	/**
	 * 根据id获取用户评价表数据
	 * @param id
	 * @return
	 */
	ZyyUserEvaluateVO getUserEvaluateTableById(Long id);
	/**
	 * 根据评价表的id获取评价表中每一项的分数
	 * @param userEvaluId
	 * @return
	 */
	List<ZyyCustomizeTableData> getTableDataByUserEvaluId(Long userEvaluId);

	ZyyEvaluateTableVO getEvaluateTableVOById(Long id);
	/**
	 * 更新用户评价表
	 * @param evaluateTable
	 */
	void updateZyyUserEvaluate(ZyyUserEvaluateVO evaluateTable);
	/**
	 * 更新填写的分数
	 * @param data
	 */
	void updateZyyCustomizeTableData(ZyyCustomizeTableData data);

	ZyyDeptVO getZyyCycleTimelineById(Long id);
	/**
	 * 带教评价学员的集合
	 * @param query
	 * @param pager
	 * @return
	 */
	List<ZyyEvaluateResidencyVO> getCycleTeacherEvaluateResi(ZyyEvaluateQuery query,
			Pager<ZyyEvaluateResidencyVO> pager);

	/**
	 * 查询科室下的轮转的学员信息集合
	 * @param query
	 * @param pager
	 * @return
	 */
	List<ZyyEvaluateResidencyVO> getCycleDeptEvaluateResi(ZyyEvaluateQuery query,
			Pager<ZyyEvaluateResidencyVO> pager);
	/**
	 * 学员评价带教和带教评价学员 医院查看页面的展示 
	 * @param query
	 * @param pager
	 */
	void showResidencyTeacherEvaluate(ZyyEvaluateQuery query, Pager<ZyyEvaluateShowVO> pager);
	/**
	 * 学员评价科室和科室评价学员 医院查看页面的展示
	 * @param query
	 * @param pager
	 */
	void showResidencyDeptEvaluate(ZyyEvaluateQuery query, Pager<ZyyEvaluateShowVO> pager);

	/**
	 * @desc 通过评价表ID查看评价表中每一个选项的每个评分被人使用次数
	 * @param tableId
	 * @return
	 */
	List<ZyyCustomizeColumnsItemVO> getEvaluateItemSelectNum(ZyyUserEvaluateVO evaluateVO);

	/**
	 * @desc 查询评价表大类
	 * @param tableId
	 * @return
	 */
	List<ZyyCustomizeColumnsVO> getEvaluateTableClass(Long tableId);
	
	List<ZyyDeptVO> findDeptByTableId(Long tableId);
	
	
	/**
	 * 培训基地评价学员列表
	 * @param query
	 * @param pager
	 * @return
	 */
	List<ZyyEvaluateResidencyVO> getHospatilEvaluateResi(
			ZyyEvaluateQuery query, Pager<ZyyEvaluateResidencyVO> pager);
	
	/**
	 * 科室评价带教列表
	 * @param query
	 * @param pager
	 * @return
	 */
	List<ZyyEvaluateResidencyVO> getDeptEvaluateTeacher(
			ZyyEvaluateQuery query, Pager<ZyyEvaluateResidencyVO> pager);
}
