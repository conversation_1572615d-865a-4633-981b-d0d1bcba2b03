package com.hys.zyy.manage.dao;

import java.util.List;

import com.hys.zyy.manage.model.ZyyLeaveAuditVO;
import com.hys.zyy.manage.model.ZyyResidencyLeaveVO;
import com.hys.zyy.manage.model.ZyyUserExtendVO;
import com.hys.zyy.manage.query.Pager;

public interface ZyyResidencyLeaveDestroyDAO {
	
	public int updateSelective(ZyyResidencyLeaveVO record);
	
	public void batchUpdate(List<ZyyResidencyLeaveVO> records);
	
	public List<ZyyLeaveAuditVO> selectFinalAutiter(Long leaveId);
	
	public List<ZyyResidencyLeaveVO> selectByParams(ZyyResidencyLeaveVO query);

	public void selectByHospitalPrivilege(ZyyUserExtendVO zyyUser, Pager<ZyyResidencyLeaveVO> pager, ZyyUserExtendVO query, boolean pagination);
	
	public void selectByDepartmentPrivilege(ZyyUserExtendVO zyyUser, Pager<ZyyResidencyLeaveVO> pager, ZyyUserExtendVO query, boolean pagination);
	
	public void selectByTeacherPrivilege(ZyyUserExtendVO zyyUser, Pager<ZyyResidencyLeaveVO> pager, ZyyUserExtendVO query, boolean pagination);
	
	public void selectByBasePrivilege(ZyyUserExtendVO zyyUser, Pager<ZyyResidencyLeaveVO> pager, ZyyUserExtendVO query, boolean pagination);
	
	public void deleteResidencyLeave(Long zyyResidencyLeaveId);

}