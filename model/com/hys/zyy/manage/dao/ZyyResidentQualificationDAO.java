package com.hys.zyy.manage.dao;

import java.util.List;

import com.hys.zyy.manage.model.ZyyResiQualificationLog;
import com.hys.zyy.manage.model.ZyyResidentQualification;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：张伟清 2012-07-11
 * 
 * 描述：住院医师报名录取资格 dao
 * 
 * 说明:
 */
public interface ZyyResidentQualificationDAO {

	/**
	 * 根据录取资格ID 查询住院医师报名录取资格记录
	 * @param qualificationId	住院医师报名录取资格ID
	 * @param checkType			审核类别 1.报名资格审核 2.录取资格审核
	 * @return
	 */
	public List<ZyyResiQualificationLog> getZyyResiQualificationLogByQuaId(Long qualificationId, Integer checkType) ;
	/**
	 * 根据录取资格ID 查询住院医师报名指定级别录取资格记录
	 * @param qualificationId 住院医师报名录取资格ID
	 * @param checkType 审核类别 1.报名资格审核 2.录取资格审核
	 * @param level 审核级别
	 * @return
	 */
	public ZyyResiQualificationLog getZyyResiQualificationLog(Long qualificationId, Integer checkType,Integer level) ;
	

	/**
	 * 根据录取资格ID 查询住院医师报名指定级别录取资格记录
	 * @param qualificationId 住院医师报名录取资格ID
	 * @param checkType 审核类别 1.报名资格审核 2.录取资格审核
	 * @return
	 */
	public List<ZyyResiQualificationLog> getZyyResiQualificationLog(Long qualificationId, Integer checkType) ;
	
	/**
	 * 根据ID 查询住院医师报名录取资格记录
	 * @param userId	住院医师ID
	 * @param willId	住院医师志愿ID
	 * @param checkType	审核类别 1.报名资格审核 2.录取资格审核
	 * @param checkUserId 审核用户
	 * @return
	 */
	public List<ZyyResiQualificationLog> getZyyResiQualificationLogByUser(Long userId, Long willId, Integer checkType, Long checkUserId) ;
	
	public List<ZyyResiQualificationLog> getZyyResiQualificationLogByUser(Long userId, Long stageId);
	
	/**
	 * 根据年度与用户查询住院医师报名录取资格
	 */
	public ZyyResidentQualification getZyyResiQualiByYearAndUser(Long yearId, Long userId) ;
	
	/**
	 * 根据主键ID 查询报名录取资格
	 */
	public ZyyResidentQualification getZyyResiQualiById(Long id) ;
	
	/**
	 * 查询专愿报名录取资格
	 * @param willId
	 * @return
	 */
	public ZyyResidentQualification getZyyResiQualiByWillId(Long willId) ;
	
	/**
	 * 添加住院医师报名录取资格
	 */
	public int addZyyResidentQualification(ZyyResidentQualification quali) ;
	
	/**
	 * 删除住院医师报名录取资格
	 * @param quali
	 * @return
	 */
	public int deleteZyyResidentQualification(ZyyResidentQualification quali) ;
	
	
	/**
	 * 修改学员志愿最终录取结果
	 * @param userId 学员id
	 * @param stageId 阶段id
	 * @param level 级别
	 * @param isAdjustedFlag 是否调剂志愿
	 * @param result 录取结果
	 * @return
	 */
	public int updateIsAdjustedResidentQualification(Long userId, Long stageId, Integer level, Integer isAdjustedFlag, Integer result);
	
	/**
	 * 修改住院医师报名录取资格
	 */
	public int updateZyyResidentQualification(ZyyResidentQualification quali) ;
	
	/**
	 * 添加住院医师报名录取资格记录
	 */
	public int addZyyResiQualificationLog(ZyyResiQualificationLog log) ;

	/**
	 * 修改住院医师报名录取资格记录
	 */
	public int updateZyyResiQualificationLog(ZyyResiQualificationLog log) ;

	/**
	 * 批量添加住院医师报名录取资格记录
	 */
	public int addZyyResiQualificationLogBatch(List<ZyyResiQualificationLog> logList) ;
	
	/**
	 * 根据住院医师录取资格信息 删除录取资格记录
	 * @param quali
	 * @return
	 */
	public int deleteZyyResiQualificationLog(ZyyResidentQualification quali) ;
}
