package com.hys.zyy.manage.dao;

import com.hys.zyy.manage.model.ZyySkillExamFeedback;
import com.hys.zyy.manage.model.ZyyUserExtendVO;
import com.hys.zyy.manage.model.vo.ZyySkillExamFeedbackListVO;
import com.hys.zyy.manage.model.vo.ZyySkillExamFeedbackVO;
import com.hys.zyy.manage.query.Pager;

public interface ZyySkillExamFeedbackDAO {
	
	void save(ZyySkillExamFeedback feedback);
	
	void updateSelective(ZyySkillExamFeedback feedback);
	
	void findListByOrg(Pager<ZyySkillExamFeedbackListVO> pager, ZyySkillExamFeedbackListVO query,ZyyUserExtendVO currentUser);
	
	ZyySkillExamFeedbackVO findById(Long id);
}
