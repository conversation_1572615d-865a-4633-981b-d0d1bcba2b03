package com.hys.zyy.manage.dao;

import java.util.List;

import com.hys.zyy.manage.model.ZyySkillExamStudent;
import com.hys.zyy.manage.model.vo.ZyySkillExamStudentListVO;

public interface ZyySkillExamStudentDAO {
	
	void save(ZyySkillExamStudent zyySkillExamStudent);
	
	void save(List<ZyySkillExamStudent> zyySkillExamStudents);
	
	List<ZyySkillExamStudentListVO> findByExamId(Long examId,Long studentUserId);
	List<ZyySkillExamStudentListVO> findByExamId(Long examId);

	void updateSelectiveByZyySkillExamStudents(ZyySkillExamStudent zyySkillExamStudent,List<ZyySkillExamStudent> zyySkillExamStudents);
	void deleteByZyySkillExamStudents(List<ZyySkillExamStudent> zyySkillExamStudents);
}
