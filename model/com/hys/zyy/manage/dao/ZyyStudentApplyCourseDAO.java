package com.hys.zyy.manage.dao;

import java.util.List;

import com.hys.zyy.manage.model.ZyyStudentApplyCourse;
/**
 * 学员考试报名科目
 * <AUTHOR>
 * @date 2020-4-22下午1:45:00
 */
public interface ZyyStudentApplyCourseDAO {
	//新增
    int insert(ZyyStudentApplyCourse record);
    //删除
    int deleteByStudentExamApplyId(Long studentExamApplyId);
    //批量保存
    void saveAllList(List<ZyyStudentApplyCourse> list);
    // 查询学员已经选择的科目
	List<ZyyStudentApplyCourse> findStudentApplyCourseList(Long studentExamApplyId);
	/**
	 * 查询报名表对应的科目id 去除重复
	 * @param studentExamApplyIds
	 * @return
	 * <AUTHOR>
	 * @date 2020-7-10下午4:54:38
	 */
	List<ZyyStudentApplyCourse> findStudentApplyCourseListByApplyIds(
			String studentExamApplyIds);
}