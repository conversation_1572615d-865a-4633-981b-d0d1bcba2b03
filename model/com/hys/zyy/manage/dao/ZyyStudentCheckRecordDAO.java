package com.hys.zyy.manage.dao;

import java.util.List;

import com.hys.zyy.manage.model.ZyyStudentCheckRecord;
import com.hys.zyy.manage.model.ZyyUserExtendVO;
import com.hys.zyy.manage.model.vo.MapKeyValueVO;
import com.hys.zyy.manage.model.vo.ZyyExamCourseStudentVO;
/**
 * 学员报名表审核记录
 * <AUTHOR>
 * @date 2020-4-22下午1:45:17
 */
public interface ZyyStudentCheckRecordDAO {
    int deleteByPrimaryKey(Long id);

    int insert(ZyyStudentCheckRecord record);

    ZyyStudentCheckRecord selectByPrimaryKey(Long id);

    int updateByPrimaryKey(ZyyStudentCheckRecord record);
    /**
     * 查询退回 或者 审核不通过 的审核记录
     * @param userId
     * @param zyyExamId
     * @param checkStatus
     * @return
     * <AUTHOR>
     * @date 2020-4-24上午10:17:24
     */
	ZyyStudentCheckRecord getStudentCheckRecord(Long userId, Long zyyExamId,Integer checkStatus);
	/**
	 *  删除审核记录
	 * @param studentExamApplyId
	 * <AUTHOR>
	 * @date 2020-4-24上午10:17:30
	 */
	void deleteByStudentExamApplyId(Long studentExamApplyId);
	/**
	 * 删除审核记录
	 * @param applyCheckFlowId
	 * @param studentExamApplyId
	 * <AUTHOR>
	 * @date 2020-4-24上午10:17:35
	 */
	void deleteByConditions(Long applyCheckFlowId, Long studentExamApplyId);
	/**
	 * 获取我的审核记录
	 * @param query
	 * @param zyyUser
	 * @return
	 * <AUTHOR>
	 * @date 2020-4-24上午10:17:39
	 */
	ZyyStudentCheckRecord getMyCheckRecord(ZyyExamCourseStudentVO query,
			ZyyUserExtendVO zyyUser);
	/**
	 * 查询审核记录
	 * @param zyyExamId
	 * @param userId
	 * @return
	 * <AUTHOR>
	 * @date 2020-4-26上午11:18:41
	 */
	List<ZyyStudentCheckRecord> findExamStudentCheckRecordList(Long zyyExamId,
			Long userId);
	/**
	 * 查询审核记录  根据审核时间倒序
	 * @param studentExamApplyId
	 * @return
	 * <AUTHOR>
	 * @date 2020-4-27下午4:00:49
	 */
	List<ZyyStudentCheckRecord> findCheckRecordListByCheckTime(Long studentExamApplyId);
	/**
	 * 查询所有科目下最终审核已经通过的学生
	 * @param courseId
	 * @param applyCheckFlowId
	 * @return
	 * <AUTHOR>
	 * @date 2020-7-10下午4:14:16
	 */
	List<MapKeyValueVO> findPassedUserIdList(Long courseId,
			Long applyCheckFlowId);
}