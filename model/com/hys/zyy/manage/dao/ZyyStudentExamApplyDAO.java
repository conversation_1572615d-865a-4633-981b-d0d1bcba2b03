package com.hys.zyy.manage.dao;

import java.util.List;

import com.hys.zyy.manage.model.ZyyStudentExamApply;
import com.hys.zyy.manage.model.ZyyUserExtendVO;
import com.hys.zyy.manage.model.vo.MapKeyValueVO;
import com.hys.zyy.manage.model.vo.ZyyExamCourseStudentVO;
import com.hys.zyy.manage.model.vo.ZyyStudentExamApplyUserVO;
import com.hys.zyy.manage.query.Pager;
/**
 * 学员考试报名表
 * <AUTHOR>
 * @date 2020-4-22下午1:45:28
 */
public interface ZyyStudentExamApplyDAO {
    int deleteByPrimaryKey(Long id);

    int insert(ZyyStudentExamApply record);

    ZyyStudentExamApply selectByPrimaryKey(Long id);

    int updateByPrimaryKey(ZyyStudentExamApply record);
	// 更新zyy_user数据
	void updateZyyUserInfo(ZyyUserExtendVO zyyUser);
	// 更新zyy_user_extend数据
	void updateZyyUserExtendInfo(ZyyUserExtendVO zyyUser);
	//查询
	ZyyStudentExamApply getZyyStudentExamApply(Long zyyExamId, Long userId);
	
	/**
	 * 查询报名的学生列表
	 * @param pager
	 * @param query
	 * <AUTHOR>
	 * @date 2020-4-23上午11:07:12
	 */
	void findExamApplyStudentList(Pager<ZyyExamCourseStudentVO> pager,
			ZyyExamCourseStudentVO query,ZyyUserExtendVO zyyUser);

	/**
	 * 学生医院列表
	 * @param pager
	 * @param query
	 * @param zyyUser
	 * @return
	 * <AUTHOR>
	 * @date 2020-4-24下午3:38:47
	 */
	List<MapKeyValueVO> findStudentOrgList(ZyyExamCourseStudentVO query, ZyyUserExtendVO zyyUser);
	/**
	 * 学生专业列表
	 * @param pager
	 * @param query
	 * @param zyyUser
	 * @return
	 * <AUTHOR>
	 * @date 2020-4-24下午3:39:09
	 */
	List<MapKeyValueVO> findStudentBaseList(ZyyExamCourseStudentVO query, ZyyUserExtendVO zyyUser);
	/**
	 * 学生年度列表
	 * @param pager
	 * @param query
	 * @param zyyUser
	 * @return
	 * <AUTHOR>
	 * @date 2020-4-24下午3:39:32
	 */
	List<MapKeyValueVO> findStudentYearList(ZyyExamCourseStudentVO query, ZyyUserExtendVO zyyUser);
	/**
	 * 查看学员审核列表
	 * @param pager
	 * @param query
	 * <AUTHOR>
	 * @date 2020-4-26上午10:15:39
	 */
	void findCheckExamStudentListView(Pager<ZyyExamCourseStudentVO> pager,
			ZyyExamCourseStudentVO query,ZyyUserExtendVO zyyUser);
	/**
	 * 查询导出的学生信息
	 * @param query
	 * @param zyyUser
	 * <AUTHOR>
	 * @date 2020-4-26下午4:30:40
	 */
	List<ZyyStudentExamApplyUserVO> findExportCheckExamStudentList(ZyyStudentExamApplyUserVO query,
			ZyyUserExtendVO zyyUser);
}