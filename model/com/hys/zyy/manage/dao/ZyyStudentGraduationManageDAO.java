package com.hys.zyy.manage.dao;

import com.hys.zyy.manage.model.graduation.StudentGraduationManageImportVO;
import com.hys.zyy.manage.model.graduation.StudentGraduationManageVO;
import com.hys.zyy.manage.query.Pager;

import java.util.List;
import java.util.Set;

/**
 * @author: HLB
 * @desc: 学员结业管理服务类 dao类
 * @version: V1.0.0
 */
public interface ZyyStudentGraduationManageDAO {

    /**
     * 获取学员结业管理数据
     * @Param: pager [] 分页参数
     * @Author: hanlibin
     * @Date: 2021/12/7
     * @return: 分页
     */

    Pager<StudentGraduationManageVO> getStudentGraduationManageList(Pager<StudentGraduationManageVO> pager, StudentGraduationManageVO query);
    /**
     * 批量保存
     * @Author: hanlibin
     * @param list 数据
     */
    void batchSave(List<StudentGraduationManageVO> list);
    /**
     * 更新保存
     * @Author: hanlibin
     * @param vo 数据
     */
    void update(StudentGraduationManageVO vo);

    /**
    * @Author: hanlibin
    * @Desc: 根据身份证号获取结业成绩。
    * @Param: [idCards]
    * @return: java.util.List<com.hys.zyy.manage.model.graduation.StudentGraduationManageImportVO>
    */

    List<StudentGraduationManageVO> getStudentGraduationManageListByIdCard(List<String> idCards);

    /**
     * @Author: hanlibin
     * @Desc: 保存备注
     * @Param: [vo]
     * @return: com.hys.zyy.manage.json.ReturnResult
     */
    void saveRemark(StudentGraduationManageVO vo);
    /**
     * 更新保存
     * @Author: hanlibin
     * @param vo 数据
     */
    void insert(StudentGraduationManageVO vo);

    /**
     * @Author: hanlibin
     * @Desc: 校验批次是否存在
     * @Param: [batchNames]
     * @return: java.lang.String
     */
    String checkBatchByBatchNames(Set<String> batchNames);
}
