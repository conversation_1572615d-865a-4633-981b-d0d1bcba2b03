package com.hys.zyy.manage.dao;

import java.util.List;

import com.hys.zyy.manage.model.ZyyStudentTicket;
import com.hys.zyy.manage.model.ZyyStudentTicketInfo;
import com.hys.zyy.manage.model.vo.ZyyExamStudentVO;
import com.hys.zyy.manage.query.Pager;

public interface ZyyStudentTicketInfoDAO {
	//新增
	void addZyyStudentTicketInfo(ZyyStudentTicketInfo zyyStudentTicketInfo);
	//修改
	void updateZyyStudentTicketInfo(ZyyStudentTicketInfo zyyStudentTicketInfo);
	//删除
	void deleteZyyStudentTicketInfoByZyyExamId(Long zyyExamId,Long userId);
	//批量更新考点，考场
	void updateZyyStudentTicketInfoByZyyExamId(
			ZyyStudentTicketInfo zyyStudentTicketInfo);
	//根据考试id和学员id查询
	List<ZyyStudentTicketInfo> findZyyStudentTicketInfoList(Long zyyExamId,
			Long userId);
	//分页查询学员信息
	void findZyyExamStudentVOPage(ZyyExamStudentVO query,
			Pager<ZyyExamStudentVO> pager);
	//按组删除
	void deleteZyyStudentTicketInfoByGroupId(Long zyyExamId, Long groupId);
	//批量保存学员信息
	void saveAllZyyStudentTicketInfo(List<ZyyStudentTicketInfo> list);
	//更新准考证号
	void updateTiketNumber(ZyyExamStudentVO item);
	//删除准考证
	void deleteZyyStudentTicketByZyyExamId(Long zyyExamId, Long userId);
	//当前考试最大的编号
	Integer getTicketMaxSerailNumber(Long zyyExamId);
	//新增准考证
	void addZyyStudentTicket(ZyyStudentTicket studentTicket);
	/**
	 * 查询学员对应考试的准考证信息
	 * @param zyyExamId
	 * @param userId
	 * @return
	 * <AUTHOR>
	 * @date 2020-4-28上午9:52:49
	 */
	ZyyStudentTicket getZyyStudentTicket(Long zyyExamId, Long userId);
	
}
