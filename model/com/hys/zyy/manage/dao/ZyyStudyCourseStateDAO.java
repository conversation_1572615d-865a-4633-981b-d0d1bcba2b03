package com.hys.zyy.manage.dao;

import java.util.List;

import com.hys.zyy.manage.model.ZyyBase;
import com.hys.zyy.manage.model.ZyyStudyCourse;
import com.hys.zyy.manage.model.ZyyStudyCourseAssess;
import com.hys.zyy.manage.model.ZyyStudyCoursePerson;
import com.hys.zyy.manage.model.ZyyStudyCourseState;
import com.hys.zyy.manage.query.Pager;

/**
 * 
 * 标题：住院医
 * 
 * 作者：贾飞
 * 
 * 描述：
 * 
 * 说明:
 */
public interface ZyyStudyCourseStateDAO {

	//查看列表
	public List<ZyyStudyCourseState> getZyyStudyCourseStateList(Long userParId,Long orgId,Long personId,int isNotUser,int isNotOtherUser,int type,int courseType,String courseTypeFirst,String courseName);
	//添加评价
	public void zyyCourseAddAssess(Long userId,Long score,Long courseId,String myAssess);
	//查看我的评价
	public List<ZyyStudyCourseAssess> getZyyStudyCourseAssessList(Long courseId,Long userId);
	//查看所有评价
	public List<ZyyStudyCourseAssess> getZyyStudyCourseAssessListAll(Long year,Long courseId);
	//查看所有评价人数
	public List<ZyyStudyCourseAssess> getZyyStudyCourseAssessNum(Long year,Long courseId, Long orgId);
	//该医院课程开放形式
	public List<ZyyStudyCourseAssess> getFormList(Long orgId);
	//医院基地列表
	public List<ZyyStudyCourseState> getZyyStudyCourseListForManager(Long userParId,Long orgId,int year,int courseType,String courseTypeFirst,String courseName);
	//医院基地列表
	public List<ZyyStudyCourseState> getZyyStudyCourseListForBaseManager(Long userParId,Long orgId,int year,int courseType,String courseTypeFirst,String courseName,Long base);
	//该基地下该课程总人数
	public int getZyyBasePersonByCourse(Long openForm,Long courseId,Long orgId);
	//医院住院医师总人数
	public int getZyyHosptialPerson(Long orgId);
	//基地住院医师总人数
	public int getZyyBasePerson(Long courseId,Long orgId);
	//该医院下所有学员
	public List<ZyyStudyCoursePerson> getBasePersonList(Long orgId,Long baseId,Long year);
	//该医院下所有基地
	public List<ZyyStudyCoursePerson> getBaseList(Long orgId);
	//已学课程学员
	public List<ZyyStudyCourseAssess> getPersonList(Long courseId,Long choseYear);
	//该基地下学习列表
	public List<ZyyStudyCourseState> getZyyStudyCourseListForBase(Long userParId,Long orgId,int choseYear,Long baseId,int courseType,String courseTypeFirst,String courseName);
	//该学科下学习列表
	public List<ZyyStudyCourseState> getZyyStudyCourseListForDept(Long orgId,int choseYear,Long deptId,int courseType,String courseTypeFirst,String courseName);
	//登陆人已学习课时数
	public int getZyyStudyCourseHoursByUserId(Long personId);
	//点击学习
	public List<ZyyStudyCourse> getZyyStudyCourse(Long courseId);
	//点击学习--文字
	public List<ZyyStudyCourse> getZyyStudyCourseClob(Long courseId);
	//保存学习记录
	public void saveStudyLog(Long courseId,Long userId);
	//该基地下学员列表
	public List<ZyyStudyCoursePerson> getBasePerson(String baseId);
	//该基地属性
	public List<ZyyStudyCoursePerson> getBase(String baseId);
	//修改评价指数
	public void zyyCourseUpdateAssessNum(Long courseId,Long score);
	//评价指数
	public int getZyyCourseAssessNum(Long courseId);
	//登陆人所属基地
	public Long getZyyPersonBaseById(Long useId);
	//课件内超链接
	public List<ZyyStudyCourse> getZyyStudyVideo(Long courseId);
}


