package com.hys.zyy.manage.dao;

import java.util.List;

import com.hys.zyy.manage.model.ZyyTeachActivityRecordVO;
import com.hys.zyy.manage.query.Pager;

public interface ZyyTeachActivityRecordDAO {

	public Long insertSelective(ZyyTeachActivityRecordVO record);

	public int updateByPrimaryKeySelective(ZyyTeachActivityRecordVO record);
	
	public void remove(Long id);
	
	public List<ZyyTeachActivityRecordVO> selectResidencyCycleTimeSection(ZyyTeachActivityRecordVO query);
	
	public ZyyTeachActivityRecordVO selectByPrimaryKey(Long id);

	public void selectByParams(Pager<ZyyTeachActivityRecordVO> pager, ZyyTeachActivityRecordVO query, boolean pagination);
	
}