package com.hys.zyy.manage.dao;

import java.util.List;

import com.hys.zyy.manage.model.ZyyTeachActivityRecordFileVO;

public interface ZyyTeachActivityRecordFileDAO {

	public Long insertSelective(ZyyTeachActivityRecordFileVO record);
	
	public void batchInsert(List<ZyyTeachActivityRecordFileVO> records);
	
	public int updateByPrimaryKeySelective(ZyyTeachActivityRecordFileVO record);
	
	public int batchRemove(Long[] ids);
	
	public List<ZyyTeachActivityRecordFileVO> selectByParams(ZyyTeachActivityRecordFileVO query);

}