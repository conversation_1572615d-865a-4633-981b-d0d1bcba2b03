package com.hys.zyy.manage.dao;

import java.util.List;

import com.hys.zyy.manage.model.ZyyBaseUser;
import com.hys.zyy.manage.model.ZyyNeedValuateRecord;
import com.hys.zyy.manage.model.ZyyTeacherCombineLog;
import com.hys.zyy.manage.model.ZyyUserEvaluateNew;
import com.hys.zyy.manage.query.Pager;

public interface ZyyTeacherCombineLogDAO {
    //新增
    int addZyyTeacherCombineLog(ZyyTeacherCombineLog record);
    //分页查询
    void findZyyTeacherCombineLog(ZyyTeacherCombineLog query, Pager<ZyyTeacherCombineLog> page);
    /**
     * 更新带教相关的数据表
     * @param newUserId
     * @param oldUserIdList
     * <AUTHOR>
     * @date 2020-7-23下午4:59:17
     */
	void updateRelatedTeacherId(Long newUserId, List<Long> oldUserIdList);
	/**
	 * 查询去重之后的 用户专业关系
	 * @param allUserIds
	 * @param newUserId
	 * @return
	 * <AUTHOR>
	 * @date 2020-7-31下午2:53:52
	 */
	List<ZyyBaseUser> findAllZyyBaseUser(String allUserIds,Long newUserId);
	/**
	 * 删除用户的专业关系
	 * @param newUserId
	 * <AUTHOR>
	 * @date 2020-7-31下午2:54:22
	 */
	void deleteAllZyyBaseUser(Long newUserId);
	/**
	 * 保存用户专业关系
	 * @param list
	 * <AUTHOR>
	 * @date 2020-7-31下午2:54:35
	 */
	void saveAllZyyBaseUser(List<ZyyBaseUser> list);
	/**
	 * 查询evaluated_user_id,unique_key
	 * @param oldUserIdList
	 * @return
	 * <AUTHOR>
	 * @date 2020-7-31下午4:49:22
	 */
	List<ZyyUserEvaluateNew> findZyyUserEvaluateNewEvaluatedList(
			List<Long> oldUserIdList);
	/**
	 * 查询user_id,unique_key
	 * @param oldUserIdList
	 * @return
	 * <AUTHOR>
	 * @date 2020-7-31下午4:50:09
	 */
	List<ZyyUserEvaluateNew> findZyyUserEvaluateNewList(List<Long> oldUserIdList);
	/**
	 * 更新evaluated_user_id,unique_key
	 * @param paramList
	 * <AUTHOR>
	 * @date 2020-7-31下午4:50:39
	 */
	void updateZyyUserEvaluateNewEvaluatedUser(List<Object[]> paramList);
	/**
	 * 更新user_id,unique_key
	 * @param paramList
	 * <AUTHOR>
	 * @date 2020-7-31下午4:50:55
	 */
	void updateZyyUserEvaluateNewUser(List<Object[]> paramList);
	/**
	 * 查询 t.evaluated_user_id ,  t.unique_key
	 * @param oldUserIdList
	 * @return
	 * <AUTHOR>
	 * @date 2020-7-31下午5:08:10
	 */
	List<ZyyNeedValuateRecord> findZyyNeedValuateRecordEvaluatedList(
			List<Long> oldUserIdList);
	/**
	 * 查询  t.user_id, t.unique_key
	 * @param oldUserIdList
	 * @return
	 * <AUTHOR>
	 * @date 2020-7-31下午5:08:20
	 */
	List<ZyyNeedValuateRecord> findZyyNeedValuateRecordList(
			List<Long> oldUserIdList);
	/**
	 * 更新 t.evaluated_user_id ,  t.unique_key
	 * @param paramList
	 * <AUTHOR>
	 * @date 2020-7-31下午5:09:09
	 */
	void updateZyyNeedValuateRecordEvaluatedUser(List<Object[]> paramList);
	/**
	 * 更新 t.user_id, t.unique_key
	 * @param paramList
	 * <AUTHOR>
	 * @date 2020-7-31下午5:09:19
	 */
	void updateZyyNeedValuateRecordUser(List<Object[]> paramList);
	/**
	 * 更新用户
	 * @param oldUserIdList
	 * <AUTHOR>
	 * @date 2020-8-4下午1:31:06
	 */
	void updateZyyUser(List<Long> oldUserIdList);
}