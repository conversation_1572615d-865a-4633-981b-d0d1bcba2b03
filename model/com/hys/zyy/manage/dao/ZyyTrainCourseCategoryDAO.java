package com.hys.zyy.manage.dao;

import java.util.Date;
import java.util.List;

import com.hys.zyy.manage.model.ZyyTrainCourse;
import com.hys.zyy.manage.model.ZyyTrainCourseCategory;
import com.hys.zyy.manage.query.Pager;

/** 
 * 标题：住院医师
 *
 * 作者： 崔伟达  2012-7-24 上午10:05:33 
 *
 * 描述：dao层接口
 *
 * 说明： 
 *
 */

public interface ZyyTrainCourseCategoryDAO {
	//添加课程分类接口
	public int addZyyTrainCourseCategory(ZyyTrainCourseCategory ztcc);
	//修改课程分类接口
	public int updateZyyTrainCourseCategory(ZyyTrainCourseCategory ztcc);
	//删除课程分类接口
	public int deleteZyyTrainCourseCategory(Long id);
	//根据机构id获得课程分类集合接口
	public List<ZyyTrainCourseCategory> getCourseCategoryList(Long orgId);
	//根据parent_id父id和机构id获得课程分类集合接口
	public List<ZyyTrainCourseCategory> getTrainCourseCategoryListByParentId(Long pid,Long orgId);
	//根据parent_id父id和机构id获得课程分类集合接口带分页
	public List<ZyyTrainCourseCategory> getTraincourseCategoryListByParentIdPage(Long pid,Long orgId,Pager<ZyyTrainCourseCategory> pager);
	//根据课程分类id获得该分类下的课程
	public List<ZyyTrainCourse> getCourseByCourseCategoryId(Long id);
	//根据课程分类id，上线时间两个条件获得该分类下的课程
	public List<ZyyTrainCourse> getCourseByCourseCategoryId(Long id,Date date);
	//编辑分类下的课程接口 实现方式为设置该课程的课程分类id为0或者有中间表操作 先写到这里吧
	public ZyyTrainCourse getCourseByCid(Long id);
	//根据课程id更新课程方法接口
	public int updateCourseById(ZyyTrainCourse ztc);
	//根据课程id删除课程方法接口
}
