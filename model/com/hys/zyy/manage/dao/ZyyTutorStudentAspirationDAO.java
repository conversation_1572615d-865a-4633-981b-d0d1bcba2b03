package com.hys.zyy.manage.dao;

import java.util.List;

import com.hys.zyy.manage.model.ZyyTutorStudent;
import com.hys.zyy.manage.model.ZyyTutorStudentAspiration;
import com.hys.zyy.manage.model.ZyyTutorVO;
import com.hys.zyy.manage.query.Pager;


public interface ZyyTutorStudentAspirationDAO {
	
	/**
	 * 由导师的id查询出填报志愿为当前导师的学员
	 * @param orgId
	 * @param teacherId
	 * @return
	 */
	public List<ZyyTutorStudentAspiration> getStudentsByTeacherId(Long orgId, Long teacherId);
	
	/**
	 * @desc 通过ID获取
	 * @param id
	 * @return
	 */
	ZyyTutorStudentAspiration getById(Long id);
	
	/**
	 * 分页查询导师下的学员信息
	 * @param orgId
	 * @param teacherId
	 * @param query
	 * @param pager
	 * @return
	 */
	public List<ZyyTutorVO> getStudentsByConditions(Long orgId,List<Long> teacherIds, ZyyTutorVO query, Pager<ZyyTutorVO> pager);
	/**
	 * 更新导师的审核状态
	 * @param tutorVO
	 */
	public void updateCheckStatus(ZyyTutorVO tutorVO);
	/**
	 * 更新学员填报志愿中的导师的审核状态,通过志愿表的id来更新
	 * @param id
	 * @param status
	 */
	public void updateCheckStatusById(Long id, Integer status, Integer flag);
	/**
	 * 查询医院下的导师id集合
	 * @param orgId
	 * @return
	 */
	public List<Long> queryTeacherIdsByOrg(Long orgId);
	/**
	 * 当导师审核通过后在关系表中添加一条记录
	 * @param tutorVO
	 */
	public void addZyyTutorStudent(ZyyTutorVO tutorVO);
	/**
	 * 根据id查询当前的志愿表中的记录
	 * @param id
	 * @return
	 */
	public List<ZyyTutorStudentAspiration> queryAspirationById(Long id);
	/**
	 * 根据学员id和导师的id查询关系表中的记录
	 * @param sid
	 * @param tid
	 * @return
	 */
	public List<ZyyTutorStudent> queryAspirationBySidTid(Long sid,Long tid);
	/**
	 * 删除关系表中的导师和学员的关系记录
	 * @param sid
	 * @param tid
	 */
	public void deleteZyyTutorStudentBySid(Long sid);
	/**
	 * 根据导师的id查询出来导师计划招录/允许招录的人数,以及目前已带的人数
	 * @param teacherId
	 * @return
	 */
	public String getResidencyCount(Long teacherId);
	
}
