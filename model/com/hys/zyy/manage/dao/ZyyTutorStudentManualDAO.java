package com.hys.zyy.manage.dao;

import java.util.List;

import com.hys.zyy.manage.model.ZyyTutorManualUpload;
import com.hys.zyy.manage.model.ZyyTutorStudentManual;
import com.hys.zyy.manage.model.ZyyTutorStudentManualVO;
import com.hys.zyy.manage.model.ZyyUser;
import com.hys.zyy.manage.query.Pager;

public interface ZyyTutorStudentManualDAO {

	List<ZyyTutorManualUpload> getTutorStudentManualHistoryRecordById(Long manualId);

	void getTutorStudentManual(ZyyUser zyyUser, Pager<ZyyTutorStudentManualVO> pager);

	void addTutorStudentManual(ZyyTutorStudentManual zyyTutorStudentManual);

	void deleteTutorStudentManual(Long manualId);

	void addZyyTutorManualUpload(ZyyTutorManualUpload uploadManual);
	
	void deleteZyyTutorManualUpload(ZyyTutorManualUpload uploadManual);

	ZyyTutorManualUpload getZyyTutorManualUpload(Long manualId);

	ZyyTutorManualUpload getZyyTutorManualUploadById(long id);

	void getManual(ZyyUser zyyUser, Pager<ZyyTutorStudentManualVO> pager, ZyyTutorStudentManualVO query);

	void checkManual(List<ZyyTutorManualUpload> list);

	void deleteManual(List<ZyyTutorStudentManual> list);
}
