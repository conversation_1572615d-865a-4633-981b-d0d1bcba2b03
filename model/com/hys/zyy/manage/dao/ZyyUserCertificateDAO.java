package com.hys.zyy.manage.dao;

import java.util.List;

import com.hys.zyy.manage.model.ZyyUserCertificate;

/**
 * 个人证件表的dao
 * <AUTHOR>
 *
 */
public interface ZyyUserCertificateDAO {
	/**
	 * 根据用户的id查询用户的证件的集合
	 */
	List<ZyyUserCertificate>  queryZyyUserCertificateListByUserId(Long userId);
	/**
	 * 根据用户的id跟新用户的自我评价信息
	 * @param userId
	 * @param selfEvaluation
	 */
	void updateUserSelfEvaluation(Long userId,String selfEvaluation);
	/**
	 * 插入一条数据
	 * @param certificate
	 */
	public void insertZyyUserCertificate(ZyyUserCertificate certificate);
	/**
	 * 更新用户证件表的数据
	 * @param certificate
	 */
	public void updateZyyUserCertificate(ZyyUserCertificate certificate);
	//批量保存
	void saveAllCertList(List<ZyyUserCertificate> certList);
	/**
	 * 根据id查询带教的个人证件
	 * @param id
	 * @return
	 * <AUTHOR>
	 * @date 2019-5-21上午10:02:42
	 */
	ZyyUserCertificate getUserCertificatesById(Long id);
}
