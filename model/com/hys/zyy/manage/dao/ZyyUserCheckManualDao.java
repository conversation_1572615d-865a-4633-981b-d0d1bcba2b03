package com.hys.zyy.manage.dao;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.hys.zyy.manage.model.ZyyAttendanceUser;
import com.hys.zyy.manage.model.ZyyBase;
import com.hys.zyy.manage.model.ZyyCycleTableResiCycle;
import com.hys.zyy.manage.model.ZyyDept;
import com.hys.zyy.manage.model.ZyyDeptStdVO;
import com.hys.zyy.manage.model.ZyyDeptVO;
import com.hys.zyy.manage.model.ZyyRecruitYear;
import com.hys.zyy.manage.model.ZyyResidencyAttendanceVO;
import com.hys.zyy.manage.model.ZyyUser;
import com.hys.zyy.manage.model.ZyyUserCheckManual;
import com.hys.zyy.manage.model.ZyyUserCheckManualData;
import com.hys.zyy.manage.model.ZyyUserExtendVO;
import com.hys.zyy.manage.query.Pager;

public interface ZyyUserCheckManualDao {
	//医院基地 查询用户列表
	public void getUserList(Pager<ZyyUserExtendVO> pager,Long usertype,List<Long> grades,List<Long> yearSyss,List<Long> baseIds,String username,Long orgId);
	//根据用户Id 和 审核人类别 删除信息
	public int delCheckUserById(Long userId, Long userType,Long baseId,Long deptId,Long deptStdId);
	//添加数据	
	public int addCheckUserById(ZyyUserCheckManual zucm);
	//根据用户ID获取实际科室列表
	public List<ZyyDeptVO> getDeptListByUserId(Long userId);
	//根据实际科室ID 学科ID 用户ID 获得标准科室列表
	public List<ZyyDeptStdVO> getDeptSidLIstBydbuId(Long deptId,Long baseId,Long userId);
	//根据用户ID 学科ID 实际科室ID 标准科室ID 获得审核结果列表
	public List<ZyyUserCheckManual> getZyyUcmByUBddsId(Long userId,Long baseId,Long deptId,Long deptSidId);
	//根据用户ID 学科ID 获得审核结果列表
	public List<ZyyUserCheckManual> getZyyUcmByUBddsId(Long userId,Long baseId);
	
	//带教  查询用户列表
	public void getUserListByDid(Pager<ZyyUserExtendVO> pager,Long deptId,List<Long> grades,List<Long> yearSyss,String username,Long tid);
	//科室查询用户列表
	public void getUserListByDid(Pager<ZyyUserExtendVO> pager,Long deptId,List<Long> grades,List<Long> yearSyss,String username);
	/**
	 * 取得用户在指定学科，科室，标准科室的考核手册数据
	 * @param userId 用户ID
	 * @param baseId 学科ID
	 * @param deptId 科室ID
	 * @param deptStdId 标准科室ID
	 * @return
	 */
	public List<ZyyUserCheckManualData> getZyyUserCheckManualData(Long userId,Long baseId ,Long deptId,Long deptStdId,Long formId,Long type);
	
	/**
	 * 添加考核手册数据
	 * @param zyyUserCheckManualData
	 * @return
	 */
	public boolean insertZyyUserCheckManualData(ZyyUserCheckManualData zyyUserCheckManualData);
	
	/**
	 * 修改考核手册数据
	 * @param zyyUserCheckManualData
	 * @return
	 */
	public boolean updateZyyUserCheckManualData(ZyyUserCheckManualData zyyUserCheckManualData);
	
	/**
	 * 删除考核手册数据
	 * @param zyyUserCheckManualData
	 * @return
	 */
	public boolean deleteZyyUserCheckManualData(ZyyUserCheckManualData zyyUserCheckManualData);
	
	/**
	 * 
	 * 轮转科室时间段显示
	 * @param userId
	 * @param deptId
	 * @return
	 */
	public List<ZyyCycleTableResiCycle> getDate(Long userId, Long deptId);
	
	/**
	 * 根据id查询用户基本信息
	 * 
	 */
	public ZyyUserExtendVO getzyyUserById(Long id) ;
}
