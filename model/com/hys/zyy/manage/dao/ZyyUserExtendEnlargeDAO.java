package com.hys.zyy.manage.dao;

import com.hys.zyy.manage.model.ZyyUserExtendEnlarge;

/**
 * 用户信息扩展延伸
 * <AUTHOR>
 * 2018-4-26 上午10:46:37
 */
public interface ZyyUserExtendEnlargeDAO {
	/**
	 * 保存
	 * @param userExtendEnlarge
	 * <AUTHOR>
	 * 2018-4-26 上午10:54:41
	 */
	public int save(ZyyUserExtendEnlarge userExtendEnlarge);
	/**
	 * 查询
	 * @param userId
	 * @return
	 * <AUTHOR>
	 * 2018-4-26 上午11:05:46
	 */
	public ZyyUserExtendEnlarge getByUserId(Long userId);
	/**
	 * 更新
	 * @param userExtendEnlarge
	 * <AUTHOR>
	 * 2018-4-26 下午4:49:50
	 */
	public int update(ZyyUserExtendEnlarge userExtendEnlarge);
	public int edit(ZyyUserExtendEnlarge userExtendEnlarge);
	/**
	 * 学员个人信息编辑更新
	 * @param userExtendEnlarge
	 * @return
	 * <AUTHOR>
	 * @date 2019-3-14下午1:54:11
	 */
	public int updateByMyAccount(ZyyUserExtendEnlarge userExtendEnlarge);
	/**
	 * 哪个字段不为空，更新哪个字段
	 * @param userExtendEnlarge
	 * @return
	 * <AUTHOR>
	 * @date 2020-1-16下午2:30:24
	 */
	public int updateUserExtendEnlargeByUserId(ZyyUserExtendEnlarge userExtendEnlarge);

	public void clear(ZyyUserExtendEnlarge record);
}
