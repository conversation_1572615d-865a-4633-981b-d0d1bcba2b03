package com.hys.zyy.manage.dao;

import java.util.List;

import com.hys.zyy.manage.model.ZyyWorkQualityMonthReportVO;
import com.hys.zyy.manage.query.Pager;

public interface ZyyWorkQualityMonthReportDAO {
	
	public Long insertSelective(ZyyWorkQualityMonthReportVO record);
	
	public int updateSelective(ZyyWorkQualityMonthReportVO record);
	
	public void queryWorkQualityMonthReport(Pager<ZyyWorkQualityMonthReportVO> pager,ZyyWorkQualityMonthReportVO monthReport);
	
	public void selectByParams(Pager<ZyyWorkQualityMonthReportVO> pager, ZyyWorkQualityMonthReportVO query, boolean pagination);
	
	public ZyyWorkQualityMonthReportVO selectByPrimaryKey(Long id);

	public List<ZyyWorkQualityMonthReportVO> query(ZyyWorkQualityMonthReportVO query);
	
	public void batchUpdateState(List<Long> ids, Integer state);

}