package com.hys.zyy.manage.dao;

import java.util.List;

import com.hys.zyy.manage.model.ZyyYearServiceReportVO;
import com.hys.zyy.manage.util.DataGrid;

public interface ZyyYearServiceReportDAO {

	public String insertSelective(ZyyYearServiceReportVO record);

	public int updateByPrimaryKeySelective(ZyyYearServiceReportVO record);

	public ZyyYearServiceReportVO selectByPrimaryKey(String id);
	
	public List<ZyyYearServiceReportVO> selectByParams(ZyyYearServiceReportVO query);

	public void selectByParams(DataGrid dg, ZyyYearServiceReportVO query);

}
