package com.hys.zyy.manage.dao.jdbc;

import com.hys.security.util.SecurityUtils;
import com.hys.zyy.manage.dao.ZyyDepartExamDAO;
import com.hys.zyy.manage.model.ZyyExamStudent;
import com.hys.zyy.manage.model.ZyyUser;
import com.hys.zyy.manage.model.ZyyUserExtendVO;
import com.hys.zyy.manage.model.vo.ZyyDepartExamStudentVO;
import com.hys.zyy.manage.model.vo.ZyyDepartExamVO;
import com.hys.zyy.manage.model.vo.ZyyExamCourseStudentVO;
import com.hys.zyy.manage.model.vo.ZyyStudentEventVO;
import com.hys.zyy.manage.query.Pager;
import com.hys.zyy.manage.util.DateUtil;
import com.hys.zyy.manage.util.PageUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.jdbc.core.simple.ParameterizedBeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Repository("zyyDepartExamDAO")
public class ZyyDepartExamJDBCDAO extends AbstractJDBCDAO implements ZyyDepartExamDAO {

	@Override
	public void findZyyDepartExamVOPage(Pager<ZyyDepartExamVO> pager, ZyyDepartExamVO query) {
		ZyyUserExtendVO zyyUser = SecurityUtils.getZyyUser();
		StringBuffer buffer = new StringBuffer();
		buffer.append(" select t.ID,t.NAME,t.EXAM_ID, ");
		buffer.append(" t2.dept_name, ");
		buffer.append(" t3.START_DATE,t3.END_DATE,t3.COURSE_NAME,t3.COURSE_START_TIME,t3.COURSE_END_TIME, ");
		buffer.append(" t3.EXAM_DURATION,t3.SUBMIT_STATUS,t3.CHECK_STATUS,t3.id zyyExamCourseId ,t3.EXAM_COURSE_ID , ");
		buffer.append(" t3.paper_id,t3.score_publish_status,t.REPEAT_EXAM,t.EXAM_IN,t3.REPEAT_SUBMIT,t3.REPEAT_SUBMIT_NUM, ");
		buffer.append(" (select count(*) from zyy_exam_student t4 where t4.zyy_exam_course_id  = t3.id and t4.type = 2 ) studentTotal, ");
		buffer.append(" odeh.required_audit, CASE odeh.audit_user_type WHEN 5 THEN '培训基地' WHEN 7 THEN '专业基地' ELSE '无' END auditUserTypeStr, ");
		buffer.append(" to_date(t3.start_date || ' ' || t3.course_start_time, 'yyyy-mm-dd hh24:mi') AS startTime ");
		buffer.append(" FROM zyy_exam t JOIN zyy_out_dept_exam_history odeh ON odeh.zyy_exam_id = t.id ");
		if (zyyUser.isHospitalUser() || zyyUser.isBaseUser())
			buffer.append(" AND odeh.required_audit = 1 ");
		buffer.append(" inner join zyy_exam_course t3 on t3.zyy_exam_id = t.id ");
		buffer.append(" left join zyy_dept t2 on t2.id = t.create_dept_id ");
		buffer.append(" where 1=1 and t.type_code ='-1' ");//仅查询考试类型为出科考试的
		Long userId = query.getCreateUserId();
		Long zyyUserOrgId = query.getZyyUserOrgId();
		Integer checkStatus = query.getCheckStatus();
		Long deptId = query.getDeptId();
		String startDate = query.getStartDate();
		String endDate = query.getEndDate();
		String name = query.getName();
		Integer submitStatus = query.getSubmitStatus();
		if (userId != null){
			buffer.append(" and t.create_user_id =  ");
			buffer.append(userId);
		}
		if (zyyUserOrgId != null){
			buffer.append(" and t.zyy_user_org_id =  ");
			buffer.append(zyyUserOrgId);
		}
		if (checkStatus != null){
			buffer.append(" and t3.check_status =  ");
			buffer.append(checkStatus);
		}
		if (deptId != null){
			buffer.append(" and t.create_dept_id =  ");
			buffer.append(deptId);
		}
		if (startDate != null){
			buffer.append(" and t3.start_date >=  '");
			buffer.append(startDate);
			buffer.append("' ");
		}
		if (endDate != null){
			buffer.append(" and t3.end_date <=  '");
			buffer.append(endDate);
			buffer.append("' ");
		}
		if (StringUtils.isNotBlank(name)){
			buffer.append(" and t.name =  '");
			buffer.append(name.trim());
			buffer.append("' ");
		}
		if (submitStatus != null){
			buffer.append(" and t3.submit_status =  ");
			buffer.append(submitStatus);
		}
		if (zyyUser.isBaseUser()) {
			buffer.append(" AND odeh.audit_user_type = 7 ");
			buffer.append(" AND EXISTS (SELECT 1 FROM zyy_base_cycle_dept bcd WHERE bcd.dept_id = t.create_dept_id AND bcd.base_id = "
					+ zyyUser.getBaseId() + ") ");
		} else if (zyyUser.isHospitalUser())
			buffer.append(" AND odeh.audit_user_type = 5 ");
		if (StringUtils.isNotBlank(query.getExamMonth())) {
			buffer.append(" AND (t.start_date LIKE '" + query.getExamMonth() + "%' OR t.end_date LIKE '" + query.getExamMonth() + "%') ");
		}
		Integer sortFlag = query.getSortFlag();
		if (sortFlag != null && sortFlag == 2){
			buffer.append(" order by t3.check_status,  t.start_date desc ,t3.course_start_time desc  ");
		}else {
			buffer.append(" order by t3.create_date desc ");
		}
		
		List<ZyyDepartExamVO> list = this.getJdbcTemplate().query(PageUtil.getPageSql(buffer.toString(), pager.getPageSize(), pager.getPageOffset()),
				ParameterizedBeanPropertyRowMapper.newInstance(ZyyDepartExamVO.class));
		pager.setList(list);
		pager.setCount(getCount(buffer.toString()));
	}
	
	@Override
	public void outDeptExamView(Pager<ZyyDepartExamVO> pager, ZyyDepartExamVO query, boolean pagination) {
		List<ZyyDepartExamVO> results = new ArrayList<ZyyDepartExamVO>();
		ZyyUserExtendVO zyyUser = SecurityUtils.getCurrentUser();
		StringBuffer buffer = new StringBuffer();
		buffer.append(" select t.ID,t.NAME,t.EXAM_ID, ");
		buffer.append(" t2.dept_name, ");
		buffer.append(" t3.START_DATE,t3.END_DATE,t3.COURSE_NAME,t3.COURSE_START_TIME,t3.COURSE_END_TIME, ");
		buffer.append(" t3.EXAM_DURATION,t3.SUBMIT_STATUS,t3.CHECK_STATUS,t3.id zyyExamCourseId ,t3.EXAM_COURSE_ID , ");
		buffer.append(" t3.paper_id,t3.score_publish_status,t.REPEAT_EXAM,t.EXAM_IN,t3.REPEAT_SUBMIT,t3.REPEAT_SUBMIT_NUM, ");
		buffer.append(" (select count(*) from zyy_exam_student t4 where t4.zyy_exam_course_id  = t3.id and t4.type = 2 ) studentTotal, ");
		buffer.append(" odeh.required_audit, CASE odeh.audit_user_type WHEN 5 THEN '培训基地' WHEN 7 THEN '专业基地' ELSE '无' END auditUserTypeStr ");
		buffer.append(" FROM zyy_exam t ");
		buffer.append(" JOIN zyy_out_dept_exam_history odeh ON odeh.zyy_exam_id = t.id ");
		buffer.append(" join zyy_exam_course t3 on t3.zyy_exam_id = t.id ");
		buffer.append(" left join zyy_dept t2 on t2.id = t.create_dept_id ");
		buffer.append(" where 1=1 and t.type_code = '-1' ");
		Integer checkStatus = query.getCheckStatus();
		Long deptId = query.getDeptId();
		String startDate = query.getStartDate();
		String endDate = query.getEndDate();
		String name = query.getName();
		Integer submitStatus = query.getSubmitStatus();
		buffer.append(" AND t.zyy_user_org_id = ");
		buffer.append(zyyUser.getZyyUserOrgId());
		if (checkStatus != null) {
			if (checkStatus == -1)
				buffer.append(" AND odeh.required_audit = 0 ");
			else {
				buffer.append(" AND odeh.required_audit = 1 AND t3.check_status = ");
				buffer.append(checkStatus);
			}
		}
		if (deptId != null){
			buffer.append(" and t.create_dept_id =  ");
			buffer.append(deptId);
		}
		if (startDate != null){
			buffer.append(" and t3.start_date >=  '");
			buffer.append(startDate);
			buffer.append("' ");
		}
		if (endDate != null){
			buffer.append(" and t3.end_date <=  '");
			buffer.append(endDate);
			buffer.append("' ");
		}
		if (StringUtils.isNotBlank(name)){
			buffer.append(" and t.name =  '");
			buffer.append(name.trim());
			buffer.append("' ");
		}
		if (submitStatus != null){
			buffer.append(" and t3.submit_status =  ");
			buffer.append(submitStatus);
		}
		if (zyyUser.isBaseUser()) {
			buffer.append(" AND EXISTS (SELECT 1 FROM zyy_base_cycle_dept bcd WHERE bcd.dept_id = t.create_dept_id AND bcd.base_id = " + zyyUser.getBaseId() + ") ");
		} else if (zyyUser.isDeptUser())
			buffer.append(" AND EXISTS (SELECT 1 FROM zyy_dept d WHERE d.id = t.create_dept_id START WITH d.id = " + zyyUser.getDeptId() + " CONNECT BY PRIOR d.id = d.parent_dept_id) ");
		buffer.append(" ORDER BY to_date(t3.start_date || ' ' || t3.course_start_time, 'yyyy-mm-dd hh24:mi') DESC ");
		if (pagination) { // 需要分页
			results = this.getJdbcTemplate().query(PageUtil.getPageSql(buffer.toString(), pager.getPageSize(), pager.getPageOffset()), 
					ParameterizedBeanPropertyRowMapper.newInstance(ZyyDepartExamVO.class));
			pager.setCount(getCount(buffer.toString()));
		} else { // 不需要分页
			results = this.getJdbcTemplate().query(buffer.toString(),ParameterizedBeanPropertyRowMapper.newInstance(ZyyDepartExamVO.class));
		}
		pager.setList(results);
	}

	@Override
	public List<ZyyUser> findStudentEvent(ZyyStudentEventVO studentEventVO) {
		if(studentEventVO!=null&&studentEventVO.getSaveSourceType()!=null
				&&studentEventVO.getStartDate()!=null&&studentEventVO.getEndDate()!=null
				&&studentEventVO.getStartTime()!=null&&studentEventVO.getEndTime()!=null
				&&studentEventVO.getStudentUserIds()!=null&&studentEventVO.getStudentUserIds().size()>0){
			List<Object> params=new ArrayList<Object>();
			StringBuffer sql =new StringBuffer(
					"select	" +
							"	zu.id,zu.REAL_NAME	" +
							"from	" +
							"	zyy_exam_course zec	" +
							"	left join zyy_exam_student zes on zec.id=zes.zyy_exam_course_id	" +
							"	left join zyy_user zu on zu.id=zes.user_id	" +
							"where	" +
							"	(" +
							"		(zec.START_DATE>=? and zec.START_DATE<=?)	" +
							"		or	" +
							"		(zec.END_DATE>=? and zec.END_DATE<=?)	" +
							"		or 	" +
							"		(zec.START_DATE<=? and zec.END_DATE>=?)	" +
							"		or	" +
							"		(zec.START_DATE<=? and zec.END_DATE>=?)	" +
							"	)	" +
							"	and	" +
							"	(	" +
							"		(zec.COURSE_START_TIME>=? and zec.COURSE_START_TIME<=?)	" +
							"		or	" +
							"		(zec.COURSE_END_TIME>=? and zec.COURSE_END_TIME<=?)	" +
							"		or	" +
							"		(zec.COURSE_START_TIME<=? and zec.COURSE_END_TIME>=?)	" +
							"		or	" +
							"		(zec.COURSE_START_TIME<=? and zec.COURSE_END_TIME>=?)	" +
							"	)" +
							"	and zes.type = 2	"
			);
			/**
			 * 时间判断逻辑，日期交接且时间交集
			 */
			params.add(studentEventVO.getStartDate());params.add(studentEventVO.getEndDate());
			params.add(studentEventVO.getStartDate());params.add(studentEventVO.getEndDate());

			params.add(studentEventVO.getStartDate());params.add(studentEventVO.getStartDate());
			params.add(studentEventVO.getEndDate());params.add(studentEventVO.getEndDate());

			params.add(studentEventVO.getStartTime());params.add(studentEventVO.getEndTime());
			params.add(studentEventVO.getStartTime());params.add(studentEventVO.getEndTime());

			params.add(studentEventVO.getStartTime());params.add(studentEventVO.getStartTime());
			params.add(studentEventVO.getEndTime());params.add(studentEventVO.getEndTime());

			sql.append("	and zu.id in (	");
			for(int i=0;i<studentEventVO.getStudentUserIds().size();i++){
				Long uid=studentEventVO.getStudentUserIds().get(i);
				if(i>0){
					sql.append(",");
				}
				sql.append("?");
				params.add(uid);
			}
			sql.append("	)	");

			if(studentEventVO.getSaveId()!=null){
				sql.append("	and zec.id!=?	");
				params.add(studentEventVO.getSaveId());
			}

			sql.append("	group by zu.id,zu.REAL_NAME	");

			List<ZyyUser> list = getJdbcTemplate().query(sql.toString(),ParameterizedBeanPropertyRowMapper.newInstance(ZyyUser.class),params.toArray());
			return list;
		}
		return null;
	}

	@Override
	public void findDepartExamStudent(ZyyDepartExamStudentVO query,
			Pager<ZyyDepartExamStudentVO> pager) {
		List<Object> parms=new ArrayList<Object>();
		String sql = null;
		if(query.getByExamStudentSel()!=null&&query.getByExamStudentSel().intValue()==1){//技能考核查询方式
			sql=createSkillDepartExamStudentSql(query,parms);
		}else{
			sql=createDepartExamStudentSql(query);
		}
		 
		List<ZyyDepartExamStudentVO> resiList = getJdbcTemplate().query(
				PageUtil.getPageSql(sql, pager.getPageSize(), pager.getPageOffset()),
    			ParameterizedBeanPropertyRowMapper.newInstance(ZyyDepartExamStudentVO.class),parms.toArray() );
		
		pager.setList(resiList) ;
		
		pager.setCount(getCount(sql,parms.toArray())) ;
	}
	
	/**
	 * 构造技能考核查询学员的sql
	 * @param query
	 * @return
	 */
	private String createSkillDepartExamStudentSql(ZyyDepartExamStudentVO query,List<Object> parms) {
		StringBuffer buffer = new StringBuffer();
		buffer.append(" select t.audit_state leaveDeptStatus,t2.id userId,t2.real_name realName,t4.year,t6.alias_name baseName  ");
		buffer.append(" ,t9.certificate_no ");
		buffer.append(" ,t9.sex,t9.JOB_NUMBER jobNumber,t9.first_degree firstDegree,t9.highest_degree highestDegree,t5.school_system schoolSystem, ");
		buffer.append(" min(t8.start_date) enterDate,max(t8.end_date) leaveDate,   ");
		buffer.append("  (to_char(min(t8.start_date),'yyyy-mm-dd')|| '至' || to_char(max(t8.end_date),'yyyy-mm-dd')) as cycleTime  ");
		buffer.append(" from zyy_join_dept_record t  ");
		buffer.append(" join zyy_user t2 on t2.id = t.residency_id ");
		buffer.append(" join zyy_user_extend t9 on t9.zyy_user_id = t.residency_id ");
		
		buffer.append(" left join zyy_residency_user_extend t5 on t5.zyy_user_id = t2.id ");
		
		buffer.append(" join zyy_base_residency t3 on t3.residency_id = t.residency_id ");
		buffer.append(" join zyy_recruit_year t4 on t4.id = t3.recruit_year_id ");
		buffer.append(" join zyy_base t5 on t5.id = t3.base_id ");
		buffer.append(" join zyy_base_std t6 on t6.id = t5.base_std_id ");
		buffer.append(" join zyy_cycle_timeline t7 on t7.residency_id = t.residency_id and t7.dept_id = t.dept_id ");
		buffer.append(" join zyy_cycle_timeline_part t8 on t8.fid = t7.id  ");
		buffer.append(" and t8.start_date = t.start_date and t8.end_date = t.end_date  ");
		buffer.append(" join zyy_dept t10 on t10.id = t.dept_id ");
		
		buffer.append(" where 1 = 1 ");
		
		//约束带教关系
		if(query.getByTeacherUserId()!=null){
			buffer.append(" AND t2.id IN ( SELECT ct.residency_id  FROM zyy_cycle_teacher ct join zyy_cycle_timeline zct" +
					" ON zct.residency_id = ct.residency_id AND zct.dept_id = ct.dept_id " +
					"WHERE ct.teacher_id = ? ) ");
			parms.add(query.getByTeacherUserId());
		}
		
		//仅看已选择的人员
		if(query.getBySelUserId()!=null&&query.getBySelUserId().intValue()==1){
			if(query.getSelUserIds()!=null&&query.getSelUserIds().size()>0){
				if(query.getSelUserIds().size()<1000){
					
					buffer.append("   and t2.id in ( ");
					for(int i=0;i<query.getSelUserIds().size();i++){
						if(i==0){
							buffer.append("?");
						}else{
							buffer.append(",?");	
						}
						parms.add(query.getSelUserIds().get(i));
					}
					buffer.append(" )  ");
				}
			}
		}
		
		//审核状态 0 未出科 1 合格出科  2 不合格出科
		buffer.append("   and ( t.audit_state is null or t.audit_state = 0 or t.audit_state = 2)  ");
		
		StringBuilder cycleStatusSql = new StringBuilder("");
		if(query.getThisMonth() != null && query.getThisMonth() == 1){
			// 本月出科学员
			String monthFirstDay = DateUtil.format(DateUtil.getMonthFirstDay(new Date()),DateUtil.FORMAT_SHORT);
			String monthLastDay = DateUtil.format(DateUtil.getMonthLastDay(new Date()),DateUtil.FORMAT_SHORT);
			cycleStatusSql.append(" or (t.status=1 and (t8.end_date >= to_date('"); 
			cycleStatusSql.append(monthFirstDay);
			cycleStatusSql.append("', 'yyyy-mm-dd') and t8.end_date <= to_date('");
			cycleStatusSql.append(monthLastDay);
			cycleStatusSql.append("','yyyy-mm-dd') ) )");
		}
		//上月出科学员
		Integer lastMonth = query.getLastMonth();
		if (lastMonth != null && lastMonth == 1){
			Date lastMonthDate = DateUtil.addMonth(new Date(), -1);//上个月
			String monthFirstDay = DateUtil.format(DateUtil.getMonthFirstDay(lastMonthDate),DateUtil.FORMAT_SHORT);
			String monthLastDay = DateUtil.format(DateUtil.getMonthLastDay(lastMonthDate),DateUtil.FORMAT_SHORT);
			cycleStatusSql.append(" or (t.status=1 and (t8.end_date >= to_date('"); 
			cycleStatusSql.append(monthFirstDay);
			cycleStatusSql.append("', 'yyyy-mm-dd') and t8.end_date <= to_date('");
			cycleStatusSql.append(monthLastDay);
			cycleStatusSql.append("','yyyy-mm-dd') ) )");
		}
		if(query.getCycleIn() != null && query.getCycleIn() == 1){
			//正在轮转学员
			cycleStatusSql.append(" or (t.status=1) ");  
		}
		if(query.getCycleEnd() != null && query.getCycleEnd() == 1){
			//轮转结束学员
			cycleStatusSql.append(" or (t.status=1 and (t8.end_date < sysdate ) )");  
		}
		
		//cycleStatusSql.substring(3) 去掉 or 
		if(cycleStatusSql.length() > 0){
			buffer.append("and ( ").append(cycleStatusSql.substring(3)).append(" ) ");
		} 
		
		Long deptId = query.getDeptId();
		Long baseId = query.getBaseId();
		Long yearId = query.getYearId();
		String realName = query.getRealName();
		
		if(deptId != null){
			buffer.append(" and t.dept_id=  ");  
			buffer.append(deptId);
		}
		//年度
		if(yearId != null) {
			buffer.append(" and t4.id=  ");  
			buffer.append(yearId);
		}
		//学科
		if(baseId != null) {
			buffer.append(" and t5.id=  ");  
			buffer.append(baseId);
		}
		//姓名  身份证号 
		if(StringUtils.isNotBlank(realName)) {
			buffer.append(" and ( t2.real_name like '%");
			buffer.append(realName.trim());
			buffer.append("%' or t9.certificate_no like '%");
			buffer.append(realName.trim());
			buffer.append("%' ) ");
		}
		buffer.append(" group by t.audit_state,t2.id ,t2.real_name,t4.year,t6.alias_name ,t9.certificate_no ");
		
		buffer.append(" ,t9.sex,t9.JOB_NUMBER,t9.first_degree,t9.highest_degree,t5.school_system ");
		buffer.append(" order by t4.year desc,t2.id,enterDate  desc ");
		return buffer.toString();
	}
	
	private String createDepartExamStudentSql(ZyyDepartExamStudentVO query) {
		StringBuffer buffer = new StringBuffer();
		buffer.append(" WITH temp AS(select t.audit_state leaveDeptStatus,t2.id userId,t2.real_name realName,t4.year,t6.alias_name baseName, t10.id AS deptId, t10.dept_name deptName ");
		buffer.append(" ,t9.certificate_no, ");
		buffer.append(" min(t8.start_date) enterDate,max(t8.end_date) leaveDate,   ");
		buffer.append("  (to_char(min(t8.start_date),'yyyy-mm-dd')|| '至' || to_char(max(t8.end_date),'yyyy-mm-dd')) as cycleTime  ");
		buffer.append(" from zyy_join_dept_record t  ");
		buffer.append(" join zyy_user t2 on t2.id = t.residency_id ");
		buffer.append(" join zyy_user_extend t9 on t9.zyy_user_id = t.residency_id ");
		buffer.append(" join zyy_base_residency t3 on t3.residency_id = t.residency_id ");
		buffer.append(" join zyy_recruit_year t4 on t4.id = t3.recruit_year_id ");
		buffer.append(" join zyy_base t5 on t5.id = t3.base_id ");
		buffer.append(" join zyy_base_std t6 on t6.id = t5.base_std_id ");
		buffer.append(" join zyy_cycle_timeline t7 on t7.residency_id = t.residency_id and t7.dept_id = t.dept_id ");
		buffer.append(" join zyy_cycle_timeline_part t8 on t8.fid = t7.id  ");
		buffer.append(" and t8.start_date = t.start_date and t8.end_date = t.end_date  ");
		buffer.append(" join zyy_dept t10 on t10.id = t.dept_id ");
		
		buffer.append(" where 1 = 1 ");
		//审核状态 0 未出科 1 合格出科  2 不合格出科
		buffer.append("   and ( t.audit_state is null or t.audit_state = 0 or t.audit_state = 2)  ");
		
		StringBuilder cycleStatusSql = new StringBuilder("");
		if(query.getThisMonth() != null && query.getThisMonth() == 1){
			// 本月出科学员
			String monthFirstDay = DateUtil.format(DateUtil.getMonthFirstDay(new Date()),DateUtil.FORMAT_SHORT);
			String monthLastDay = DateUtil.format(DateUtil.getMonthLastDay(new Date()),DateUtil.FORMAT_SHORT);
			cycleStatusSql.append(" or (t.status=1 and (t8.end_date >= to_date('"); 
			cycleStatusSql.append(monthFirstDay);
			cycleStatusSql.append("', 'yyyy-mm-dd') and t8.end_date <= to_date('");
			cycleStatusSql.append(monthLastDay);
			cycleStatusSql.append("','yyyy-mm-dd') ) )");
		}
		//上月出科学员
		Integer lastMonth = query.getLastMonth();
		if (lastMonth != null && lastMonth == 1){
			Date lastMonthDate = DateUtil.addMonth(new Date(), -1);//上个月
			String monthFirstDay = DateUtil.format(DateUtil.getMonthFirstDay(lastMonthDate),DateUtil.FORMAT_SHORT);
			String monthLastDay = DateUtil.format(DateUtil.getMonthLastDay(lastMonthDate),DateUtil.FORMAT_SHORT);
			cycleStatusSql.append(" or (t.status=1 and (t8.end_date >= to_date('"); 
			cycleStatusSql.append(monthFirstDay);
			cycleStatusSql.append("', 'yyyy-mm-dd') and t8.end_date <= to_date('");
			cycleStatusSql.append(monthLastDay);
			cycleStatusSql.append("','yyyy-mm-dd') ) )");
		}
		if(query.getCycleIn() != null && query.getCycleIn() == 1){
			//正在轮转学员
			cycleStatusSql.append(" or (t.status=1) ");  
		}
		if(query.getCycleEnd() != null && query.getCycleEnd() == 1){
			//轮转结束学员
			cycleStatusSql.append(" or (t.status=1 and (t8.end_date < sysdate ) )");  
		}
		
		//cycleStatusSql.substring(3) 去掉 or 
		if(cycleStatusSql.length() > 0){
			buffer.append("and ( ").append(cycleStatusSql.substring(3)).append(" ) ");
		} 
		
		if(query.getCycleEnd() == null || query.getCycleEnd() == 0){
			//这个条件不知道什么作用
			//buffer.append(" AND SYSDATE >= t8.start_date AND SYSDATE <= TRUNC(t8.end_DATE)+1-1/86400 ");
		}
		
		Long deptId = query.getDeptId();
		Long baseId = query.getBaseId();
		Long yearId = query.getYearId();
		String realName = query.getRealName();
		
		if(deptId != null){
			buffer.append(" and t.dept_id=  ");  
			buffer.append(deptId);
		}
		//年度
		if(yearId != null) {
			buffer.append(" and t4.id=  ");  
			buffer.append(yearId);
		}
		//学科
		if(baseId != null) {
			buffer.append(" and t5.id=  ");  
			buffer.append(baseId);
		}
		//姓名  身份证号 
		if(StringUtils.isNotBlank(realName)) {
			buffer.append(" and ( t2.real_name like '%");
			buffer.append(realName.trim());
			buffer.append("%' or t9.certificate_no like '%");
			buffer.append(realName.trim());
			buffer.append("%' ) ");
		}
		buffer.append(" group by t.audit_state,t2.id ,t2.real_name,t4.year,t6.alias_name, t10.id, t10.dept_name,t9.certificate_no) ");
		buffer.append(" SELECT temp.*, jdr.id AS jdrId FROM temp ");
		buffer.append(" LEFT JOIN zyy_join_dept_record jdr ON jdr.residency_id = userId AND jdr.dept_id = deptId AND jdr.end_date = leavedate ");
		buffer.append(" ORDER BY year DESC, userId, enterDate DESC ");
		return buffer.toString();
	}

	@Override
	public List<ZyyDepartExamVO> findDepartExamStudentById(Long id) {
		String sql = " select t.zyy_exam_course_id zyyExamCourseId,t.user_id createUserId, t.jdr_id from zyy_exam_student t where t.type =2 and t.zyy_exam_course_id = ? ";
		List<ZyyDepartExamVO> list = getJdbcTemplate().query(sql,ParameterizedBeanPropertyRowMapper.newInstance(ZyyDepartExamVO.class),id);
		return list;
	}

	@Override
	public void deleteAllExamStudentByZyyExamCourseId(Long zyyExamCourseId) {
		String sql = " delete from zyy_exam_student t where t.type = 2 and t.zyy_exam_course_id = ? ";
		getJdbcTemplate().update(sql, zyyExamCourseId);
		
	}

	@Override
	public void saveAllExamStudent(List<ZyyExamStudent> newStudentList) {
		String sql = " insert into zyy_exam_student(zyy_exam_course_id,user_id,type,jdr_id) values(:zyyExamCourseId,:userId,:type,:jdrId) ";
		saveList(sql, newStudentList);
	}

	@Override
	public List<ZyyDepartExamVO> findDepartExamCourseByDepartExamId(Long id) {
		String sql = " select t.*  from ZYY_EXAM_COURSE t where t.zyy_exam_id = ? ";
		List<ZyyDepartExamVO> list = getJdbcTemplate().query(sql,ParameterizedBeanPropertyRowMapper.newInstance(ZyyDepartExamVO.class),id);
		return list;
	}

	@Override
	public List<ZyyExamCourseStudentVO> findDepartExamCourseStudentList(
			Long zyyExamCourseId, String queryCons) {
		StringBuffer buffer = new StringBuffer();
		buffer.append(" select distinct t.id userId,t.real_name realName,t2.mobil_number mobile,t2.certificate_no,t2.sex ");
		buffer.append(" from zyy_exam_student t3   ");
		buffer.append(" join zyy_user t  on t.id = t3.user_id  ");
		buffer.append(" left join zyy_user_extend t2 on t2.zyy_user_id = t.id  ");
		buffer.append(" where t3.type = 2 and t.zyy_user_type = 21 and t3.zyy_exam_course_id = ");
		buffer.append(zyyExamCourseId);
		if (StringUtils.isNotBlank(queryCons)){
			buffer.append(" and ( t.real_name = '");
			buffer.append(queryCons.trim());
			buffer.append("' or t2.mobil_number = '");
			buffer.append(queryCons.trim());
			buffer.append("' or t2.certificate_no = '");
			buffer.append(queryCons.trim());
			buffer.append("' ) ");
			
		}
		List<ZyyExamCourseStudentVO> list = getJdbcTemplate().query(buffer.toString(),
				ParameterizedBeanPropertyRowMapper.newInstance(ZyyExamCourseStudentVO.class));
		return list;
	}

}
