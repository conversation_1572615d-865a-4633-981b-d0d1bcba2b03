package com.hys.zyy.manage.model;

import java.util.Date;


public class ActivityAnalysisQVO extends ZyyBaseObject{
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	/**
	 * 学员ID
	 */
	private Long userId;
	/**
	 * 科室ID
	 */
	private Long deptId; 
	/**
	 * 学科ID
	 */
	private Long baseId; 
	/**
	 * 机构ID
	 */
	private Long orgId;
	
	/**
	 *开始时间  
	 */
	private Date startDate;
	
	/**
	 * 结束时间
	 */
	private Date endDate;
	/**
	 * 活动状态    1-尚未进行   2-正在进行   3-已结束  4-全部
	 */
	private Integer activityStatus;
	/**
	 * 用户状态 21学员 9科室7学科
	 */
	private Integer userType;
	/**
	 * 活动类型
	 */
	private Long activityTypeId;
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	public Long getDeptId() {
		return deptId;
	}
	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}
	public Long getBaseId() {
		return baseId;
	}
	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}
	public Long getOrgId() {
		return orgId;
	}
	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}
	public Date getStartDate() {
		return startDate;
	}
	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}
	public Date getEndDate() {
		return endDate;
	}
	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}
	public Integer getActivityStatus() {
		return activityStatus;
	}
	public void setActivityStatus(Integer activityStatus) {
		this.activityStatus = activityStatus;
	}
	public Integer getUserType() {
		return userType;
	}
	public void setUserType(Integer userType) {
		this.userType = userType;
	}
	public Long getActivityTypeId() {
		return activityTypeId;
	}
	public void setActivityTypeId(Long activityTypeId) {
		this.activityTypeId = activityTypeId;
	}
	
	

}
