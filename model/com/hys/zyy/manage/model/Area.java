package com.hys.zyy.manage.model;

/**
 * Created by lich on 2017/3/31.
 */
public class Area {

    private Long id;

    private Long parenrId;

    private String cityName;

    private Long oldSipId;

    private String cmeOrgId;

    private String cmeParentOrgId;

    private Long sort;
    /*
	 * 排序字段
	 */
	private String sortField;
	/*
	 * 排序方式（ASC or DESC）
	 */
	private String order;

    public Area() {
		super();
	}

	public Area(Long parenrId, String sortField, String order) {
		super();
		this.parenrId = parenrId;
		this.sortField = sortField;
		this.order = order;
	}

	public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getParenrId() {
        return parenrId;
    }

    public void setParenrId(Long parenrId) {
        this.parenrId = parenrId;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName == null ? null : cityName.trim();
    }

    public Long getOldSipId() {
        return oldSipId;
    }

    public void setOldSipId(Long oldSipId) {
        this.oldSipId = oldSipId;
    }

    public String getCmeOrgId() {
        return cmeOrgId;
    }

    public void setCmeOrgId(String cmeOrgId) {
        this.cmeOrgId = cmeOrgId == null ? null : cmeOrgId.trim();
    }

    public String getCmeParentOrgId() {
        return cmeParentOrgId;
    }

    public void setCmeParentOrgId(String cmeParentOrgId) {
        this.cmeParentOrgId = cmeParentOrgId == null ? null : cmeParentOrgId.trim();
    }

    public Long getSort() {
        return sort;
    }

    public void setSort(Long sort) {
        this.sort = sort;
    }

	public String getSortField() {
		return sortField;
	}

	public void setSortField(String sortField) {
		this.sortField = sortField;
	}

	public String getOrder() {
		return order;
	}

	public void setOrder(String order) {
		this.order = order;
	}

}