package com.hys.zyy.manage.model;

import java.io.Serializable;

public class BaseAccount implements Serializable {
	private static final long serialVersionUID = 1L;
	private Long accountId;
	private String accountName;
	private String accountPassword;
	private String accountRemark;
	private String bdpUserId;

	public BaseAccount() {
		super();
	}

	public Long getAccountId() {
		return accountId;
	}

	public void setAccountId(Long accountId) {
		this.accountId = accountId;
	}

	public String getAccountName() {
		return accountName;
	}

	public void setAccountName(String accountName) {
		this.accountName = accountName;
	}

	public String getAccountPassword() {
		return accountPassword;
	}

	public void setAccountPassword(String accountPassword) {
		this.accountPassword = accountPassword;
	}

	public String getAccountRemark() {
		return accountRemark;
	}

	public void setAccountRemark(String accountRemark) {
		this.accountRemark = accountRemark;
	}

	public String getBdpUserId() {
		return bdpUserId;
	}

	public void setBdpUserId(String bdpUserId) {
		this.bdpUserId = bdpUserId;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result
				+ ((accountId == null) ? 0 : accountId.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		BaseAccount other = (BaseAccount) obj;
		if (accountId == null) {
			if (other.accountId != null)
				return false;
		} else if (!accountId.equals(other.accountId))
			return false;
		return true;
	}

	@Override
	public String toString() {
		return "BaseAccount [accountId=" + accountId + ", accountName="
				+ accountName + ", accountPassword=" + accountPassword
				+ ", accountRemark=" + accountRemark + "]";
	}

}
