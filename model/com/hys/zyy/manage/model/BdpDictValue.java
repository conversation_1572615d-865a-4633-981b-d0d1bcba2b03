package com.hys.zyy.manage.model;

import java.io.Serializable;
import java.util.Date;

/**
 * BDP字典数据表
 */
public class BdpDictValue implements Serializable {
	private static final long serialVersionUID = 1L;
	/*
	 * 字典主键seq_bdp_dict_data.nextval
	 */
	private Long dicId;
	/*
	 * 字典排序
	 */
	private Integer dicSort;
	/*
	 * 字典名称
	 */
	private String dicName;
	/*
	 * 字典键值
	 */
	private String dicCode;
	/*
	 * 字典类型
	 */
	private String dicCategoryCode;
	/*
	 * 父级ID
	 */
	private Long parentId;
	/*
	 * 字典级别
	 */
	private Integer dicLevel;
	/*
	 * 字典类型主键
	 */
	private Long dicCategoryId;
	/*
	 * 状态（1正常 0停用）
	 */
	private String status;
	/*
	 * 创建者
	 */
	private String createBy;
	/*
	 * 创建时间
	 */
	private Date createTime;
	/*
	 * 更新者
	 */
	private String updateBy;
	/*
	 * 更新时间
	 */
	private Date updateTime;
	/*
	 * 备注
	 */
	private String remark;

	public BdpDictValue() {
		super();
	}

	public Long getDicId() {
		return dicId;
	}

	public void setDicId(Long dicId) {
		this.dicId = dicId;
	}

	public Integer getDicSort() {
		return dicSort;
	}

	public void setDicSort(Integer dicSort) {
		this.dicSort = dicSort;
	}

	public String getDicName() {
		return dicName;
	}

	public void setDicName(String dicName) {
		this.dicName = dicName == null ? null : dicName.trim();
	}

	public String getDicCode() {
		return dicCode;
	}

	public void setDicCode(String dicCode) {
		this.dicCode = dicCode == null ? null : dicCode.trim();
	}

	public String getDicCategoryCode() {
		return dicCategoryCode;
	}

	public void setDicCategoryCode(String dicCategoryCode) {
		this.dicCategoryCode = dicCategoryCode == null ? null : dicCategoryCode.trim();
	}

	public Long getParentId() {
		return parentId;
	}

	public void setParentId(Long parentId) {
		this.parentId = parentId;
	}

	public Integer getDicLevel() {
		return dicLevel;
	}

	public void setDicLevel(Integer dicLevel) {
		this.dicLevel = dicLevel;
	}

	public Long getDicCategoryId() {
		return dicCategoryId;
	}

	public void setDicCategoryId(Long dicCategoryId) {
		this.dicCategoryId = dicCategoryId;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status == null ? null : status.trim();
	}

	public String getCreateBy() {
		return createBy;
	}

	public void setCreateBy(String createBy) {
		this.createBy = createBy == null ? null : createBy.trim();
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getUpdateBy() {
		return updateBy;
	}

	public void setUpdateBy(String updateBy) {
		this.updateBy = updateBy == null ? null : updateBy.trim();
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark == null ? null : remark.trim();
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((dicId == null) ? 0 : dicId.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		BdpDictValue other = (BdpDictValue) obj;
		if (dicId == null) {
			if (other.dicId != null)
				return false;
		} else if (!dicId.equals(other.dicId))
			return false;
		return true;
	}

	@Override
	public String toString() {
		return "BdpDictValue [dicId=" + dicId + ", dicSort=" + dicSort + ", dicName=" + dicName + ", dicCode=" + dicCode
				+ ", dicCategoryCode=" + dicCategoryCode + ", parentId=" + parentId + ", dicLevel=" + dicLevel
				+ ", dicCategoryId=" + dicCategoryId + ", status=" + status + ", createBy=" + createBy + ", createTime="
				+ createTime + ", updateBy=" + updateBy + ", updateTime=" + updateTime + ", remark=" + remark + "]";
	}

}