package com.hys.zyy.manage.model;

import java.io.Serializable;

/**
 * bdpOrgId对应的住院医机构信息
 */
public class BdpOrg implements Serializable {
	private static final long serialVersionUID = 1L;
	/*
	 * 住院医机构ID
	 */
	private Long id;
	/*
	 * 住院医机构的省ID
	 */
	private Long provinceId;
	/*
	 * 住院医机构名称
	 */
	private String siteName;
	/*
	 * 住院医机构PC的域名，如果机构本身没有域名，则查找机构所属省的域名； 如果省级域名都没有，说明该机构已下线，移除即可。
	 */
	private String pcDomainName;
	/*
	 * 住院医机构H5的域名
	 */
	private String h5DomainName;
	/*
	 * BDP机构ID
	 */
	private Long bdpOrgId;

	public BdpOrg() {
		super();
	}

	public BdpOrg(Long bdpOrgId) {
		super();
		this.bdpOrgId = bdpOrgId;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getProvinceId() {
		return provinceId;
	}

	public void setProvinceId(Long provinceId) {
		this.provinceId = provinceId;
	}

	public String getSiteName() {
		return siteName;
	}

	public void setSiteName(String siteName) {
		this.siteName = siteName == null ? null : siteName.trim();
	}
	
	public String getPcDomainName() {
		return pcDomainName;
	}

	public void setPcDomainName(String pcDomainName) {
		this.pcDomainName = pcDomainName == null ? null : pcDomainName.trim();
	}

	public String getH5DomainName() {
		return h5DomainName;
	}

	public void setH5DomainName(String h5DomainName) {
		this.h5DomainName = h5DomainName == null ? null : h5DomainName.trim();
	}

	public Long getBdpOrgId() {
		return bdpOrgId;
	}

	public void setBdpOrgId(Long bdpOrgId) {
		this.bdpOrgId = bdpOrgId;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		BdpOrg other = (BdpOrg) obj;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		return true;
	}

	@Override
	public String toString() {
		return "BdpOrg [id=" + id + ", provinceId=" + provinceId + ", siteName=" + siteName + ", pcDomainName=" + pcDomainName + ", h5DomainName=" + h5DomainName + ", bdpOrgId=" + bdpOrgId + "]";
	}
	
}