package com.hys.zyy.manage.model;

import java.io.Serializable;

/**
 * BDP用户信息
 */
public class BdpUserInfo implements Serializable {
	private static final long serialVersionUID = 1L;
	/*
	 * 用户唯一标识
	 */
	private String userId;
	/*
	 * 用户账户（系统间通信标识）
	 */
	private String userAccount;
	/*
	 * 登录密码（加密）
	 */
	private String userPassword;
	/*
	 * 真实姓名
	 */
	private String realName;
	/*
	 * 用户状态1正常；0锁定；-1删除
	 */
	private Integer userStatus;
	/*
	 * 注册来源
	 * 11 云课堂 
	 * 12 考试 
	 * 13 住院医
	 */
	private String registOrigin;
	/*
	 * 活动平台
	 */
	private String userUsesys;
	/*
	 * 性别（1=男；2=女）
	 */
	private Integer sex;
	/*
	 * 出生日期
	 */
	private String birthday;
	/*
	 * IC卡号
	 */
	private String ic;
	/*
	 * ic卡号绑定时间
	 */
	private String icBindDate;
	/*
	 * 手机号码
	 */
	private String mobileNumber;
	/*
	 * 手机验证标志：0未验证，1已验证
	 */
	private Integer mobilePhoneVerify;
	/*
	 * 证件类型
	 */
	private String certificateType;

	private Integer certificateTypeInt;
	/*
	 * 证件号码
	 */
	private String certificateNo;
	/*
	 * 是否合并（0=否；1=是）
	 */
	private String isMerge;
	/*
	 * 用户类型：0普通用户1实名认证用户2专家
	 */
	private Integer userType;
	/*
	 * 微信openID
	 */
	private String openId;
	/*
	 * 用户微信昵称
	 */
	private String userWechatNickName;
	/*
	 * 苹果用户ID
	 */
	private String appleId;
	/*
	 * 用户头像
	 */
	private String headPic;
	/*
	 * 用户昵称
	 */
	private String nickName;
	/*
	 * 用户脸部档案地址
	 */
	private String facePic;
	/*
	 * 用户脸部档案ID
	 */
	private String faceId;
	/*
	 * 微信unionId
	 */
	private String unionId;
	/*
	 * 修改密码时间
	 */
	private String updatePwdTime;
	/*
	 * 电子邮箱
	 */
	private String email;
	/*
	 * 邮政编码
	 */
	private String postCode;
	/*
	 * 地址
	 */
	private String address;
	/*
	 * 最高学历
	 */
	private String highestRecordSchool;
	/*
	 * 学科
	 */
	private String specialtyId;
	/*
	 * 职称类型
	 */
	private String userTitleType;
	/*
	 * 职称
	 */
	private String title;
	/*
	 * 执业类别
	 */
	private String categoryCertificate;
	/*
	 * 执业范围
	 */
	private String scopeCertificate;
	/*
	 * 自填医院
	 */
	private String userHspName;
	/*
	 * 工作单位
	 */
	private String hospital;
	/*
	 * 简介
	 */
	private String intro;
	/*
	 * 擅长
	 */
	private String skill;
	/*
	 * 省
	 */
	private String workProvince;
	private String workProvinceName;
	/*
	 * 市
	 */
	private String workCity;
	private String workCityName;
	/*
	 * 区
	 */
	private String workCounties;
	private String workCountiesName;
	/*
	 * 科室
	 */
	private String deptCode;
	private String deptName;
	/*
	 * 最高学位
	 */
	private String highestDegree;
	/*
	 * 非医疗机构名称
	 */
	private String noHospitalOrgname;
	private String specialtyName;
	private String titleName;
	private String titleTypeName;
	/*
	 * 注册时间
	 */
	private String createTime;
	/*
	 * 修改时间
	 */
	private String updateTime;

	public BdpUserInfo() {
		super();
	}

	public BdpUserInfo(String userId, String registOrigin) {
		super();
		this.userId = userId;
		this.registOrigin = registOrigin;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId == null ? null : userId.trim();
	}

	public String getUserAccount() {
		return userAccount;
	}

	public void setUserAccount(String userAccount) {
		this.userAccount = userAccount == null ? null : userAccount.trim();
	}

	public String getUserPassword() {
		return userPassword;
	}

	public void setUserPassword(String userPassword) {
		this.userPassword = userPassword == null ? null : userPassword.trim();
	}

	public String getRealName() {
		return realName;
	}

	public void setRealName(String realName) {
		this.realName = realName == null ? null : realName.trim();
	}

	public Integer getUserStatus() {
		return userStatus;
	}

	public void setUserStatus(Integer userStatus) {
		this.userStatus = userStatus;
	}

	public String getRegistOrigin() {
		return registOrigin;
	}

	public void setRegistOrigin(String registOrigin) {
		this.registOrigin = registOrigin == null ? null : registOrigin.trim();
	}

	public String getUserUsesys() {
		return userUsesys;
	}

	public void setUserUsesys(String userUsesys) {
		this.userUsesys = userUsesys == null ? null : userUsesys.trim();
	}

	public Integer getSex() {
		return sex;
	}

	public void setSex(Integer sex) {
		this.sex = sex;
	}

	public String getBirthday() {
		return birthday;
	}

	public void setBirthday(String birthday) {
		this.birthday = birthday == null ? null : birthday.trim();
	}

	public String getIc() {
		return ic;
	}

	public void setIc(String ic) {
		this.ic = ic == null ? null : ic.trim();
	}

	public String getIcBindDate() {
		return icBindDate;
	}

	public void setIcBindDate(String icBindDate) {
		this.icBindDate = icBindDate == null ? null : icBindDate.trim();
	}

	public String getMobileNumber() {
		return mobileNumber;
	}

	public void setMobileNumber(String mobileNumber) {
		this.mobileNumber = mobileNumber == null ? null : mobileNumber.trim();
	}

	public Integer getMobilePhoneVerify() {
		return mobilePhoneVerify;
	}

	public void setMobilePhoneVerify(Integer mobilePhoneVerify) {
		this.mobilePhoneVerify = mobilePhoneVerify;
	}

	public String getCertificateType() {
		return certificateType;
	}

	public void setCertificateType(String certificateType) {
		this.certificateType = certificateType == null ? null : certificateType.trim();
	}

	public Integer getCertificateTypeInt() {
		return certificateTypeInt;
	}

	public void setCertificateTypeInt(Integer certificateTypeInt) {
		this.certificateTypeInt = certificateTypeInt;
	}

	public String getCertificateNo() {
		return certificateNo;
	}

	public void setCertificateNo(String certificateNo) {
		this.certificateNo = certificateNo == null ? null : certificateNo.trim();
	}

	public String getIsMerge() {
		return isMerge;
	}

	public void setIsMerge(String isMerge) {
		this.isMerge = isMerge == null ? null : isMerge.trim();
	}

	public Integer getUserType() {
		return userType;
	}

	public void setUserType(Integer userType) {
		this.userType = userType;
	}

	public String getOpenId() {
		return openId;
	}

	public void setOpenId(String openId) {
		this.openId = openId == null ? null : openId.trim();
	}

	public String getUserWechatNickName() {
		return userWechatNickName;
	}

	public void setUserWechatNickName(String userWechatNickName) {
		this.userWechatNickName = userWechatNickName == null ? null : userWechatNickName.trim();
	}

	public String getAppleId() {
		return appleId;
	}

	public void setAppleId(String appleId) {
		this.appleId = appleId == null ? null : appleId.trim();
	}

	public String getHeadPic() {
		return headPic;
	}

	public void setHeadPic(String headPic) {
		this.headPic = headPic == null ? null : headPic.trim();
	}

	public String getNickName() {
		return nickName;
	}

	public void setNickName(String nickName) {
		this.nickName = nickName == null ? null : nickName.trim();
	}

	public String getFacePic() {
		return facePic;
	}

	public void setFacePic(String facePic) {
		this.facePic = facePic == null ? null : facePic.trim();
	}

	public String getFaceId() {
		return faceId;
	}

	public void setFaceId(String faceId) {
		this.faceId = faceId == null ? null : faceId.trim();
	}

	public String getUnionId() {
		return unionId;
	}

	public void setUnionId(String unionId) {
		this.unionId = unionId == null ? null : unionId.trim();
	}

	public String getUpdatePwdTime() {
		return updatePwdTime;
	}

	public void setUpdatePwdTime(String updatePwdTime) {
		this.updatePwdTime = updatePwdTime == null ? null : updatePwdTime.trim();
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email == null ? null : email.trim();
	}

	public String getPostCode() {
		return postCode;
	}

	public void setPostCode(String postCode) {
		this.postCode = postCode == null ? null : postCode.trim();
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address == null ? null : address.trim();
	}

	public String getHighestRecordSchool() {
		return highestRecordSchool;
	}

	public void setHighestRecordSchool(String highestRecordSchool) {
		this.highestRecordSchool = highestRecordSchool == null ? null : highestRecordSchool.trim();
	}

	public String getSpecialtyId() {
		return specialtyId;
	}

	public void setSpecialtyId(String specialtyId) {
		this.specialtyId = specialtyId == null ? null : specialtyId.trim();
	}

	public String getUserTitleType() {
		return userTitleType;
	}

	public void setUserTitleType(String userTitleType) {
		this.userTitleType = userTitleType == null ? null : userTitleType.trim();
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title == null ? null : title.trim();
	}

	public String getCategoryCertificate() {
		return categoryCertificate;
	}

	public void setCategoryCertificate(String categoryCertificate) {
		this.categoryCertificate = categoryCertificate == null ? null : categoryCertificate.trim();
	}

	public String getScopeCertificate() {
		return scopeCertificate;
	}

	public void setScopeCertificate(String scopeCertificate) {
		this.scopeCertificate = scopeCertificate == null ? null : scopeCertificate.trim();
	}

	public String getUserHspName() {
		return userHspName;
	}

	public void setUserHspName(String userHspName) {
		this.userHspName = userHspName == null ? null : userHspName.trim();
	}

	public String getHospital() {
		return hospital;
	}

	public void setHospital(String hospital) {
		this.hospital = hospital == null ? null : hospital.trim();
	}

	public String getIntro() {
		return intro;
	}

	public void setIntro(String intro) {
		this.intro = intro == null ? null : intro.trim();
	}

	public String getSkill() {
		return skill;
	}

	public void setSkill(String skill) {
		this.skill = skill == null ? null : skill.trim();
	}

	public String getWorkProvince() {
		return workProvince;
	}

	public void setWorkProvince(String workProvince) {
		this.workProvince = workProvince == null ? null : workProvince.trim();
	}

	public String getWorkProvinceName() {
		return workProvinceName;
	}

	public void setWorkProvinceName(String workProvinceName) {
		this.workProvinceName = workProvinceName == null ? null : workProvinceName.trim();
	}

	public String getWorkCity() {
		return workCity;
	}

	public void setWorkCity(String workCity) {
		this.workCity = workCity == null ? null : workCity.trim();
	}

	public String getWorkCityName() {
		return workCityName;
	}

	public void setWorkCityName(String workCityName) {
		this.workCityName = workCityName == null ? null : workCityName.trim();
	}

	public String getWorkCounties() {
		return workCounties;
	}

	public void setWorkCounties(String workCounties) {
		this.workCounties = workCounties == null ? null : workCounties.trim();
	}

	public String getWorkCountiesName() {
		return workCountiesName;
	}

	public void setWorkCountiesName(String workCountiesName) {
		this.workCountiesName = workCountiesName == null ? null : workCountiesName.trim();
	}

	public String getDeptCode() {
		return deptCode;
	}

	public void setDeptCode(String deptCode) {
		this.deptCode = deptCode == null ? null : deptCode.trim();
	}

	public String getDeptName() {
		return deptName;
	}

	public void setDeptName(String deptName) {
		this.deptName = deptName == null ? null : deptName.trim();
	}

	public String getHighestDegree() {
		return highestDegree;
	}

	public void setHighestDegree(String highestDegree) {
		this.highestDegree = highestDegree == null ? null : highestDegree.trim();
	}

	public String getNoHospitalOrgname() {
		return noHospitalOrgname;
	}

	public void setNoHospitalOrgname(String noHospitalOrgname) {
		this.noHospitalOrgname = noHospitalOrgname == null ? null : noHospitalOrgname.trim();
	}

	public String getSpecialtyName() {
		return specialtyName;
	}

	public void setSpecialtyName(String specialtyName) {
		this.specialtyName = specialtyName == null ? null : specialtyName.trim();
	}

	public String getTitleName() {
		return titleName;
	}

	public void setTitleName(String titleName) {
		this.titleName = titleName == null ? null : titleName.trim();
	}

	public String getTitleTypeName() {
		return titleTypeName;
	}

	public void setTitleTypeName(String titleTypeName) {
		this.titleTypeName = titleTypeName == null ? null : titleTypeName.trim();
	}

	public String getCreateTime() {
		return createTime;
	}

	public void setCreateTime(String createTime) {
		this.createTime = createTime == null ? null : createTime.trim();
	}

	public String getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(String updateTime) {
		this.updateTime = updateTime == null ? null : updateTime.trim();
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((userId == null) ? 0 : userId.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		BdpUserInfo other = (BdpUserInfo) obj;
		if (userId == null) {
			if (other.userId != null)
				return false;
		} else if (!userId.equals(other.userId))
			return false;
		return true;
	}

	@Override
	public String toString() {
		return "BdpUserInfo{" +
				"userId='" + userId + '\'' +
				", userAccount='" + userAccount + '\'' +
				", userPassword='" + userPassword + '\'' +
				", realName='" + realName + '\'' +
				", userStatus=" + userStatus +
				", registOrigin='" + registOrigin + '\'' +
				", userUsesys='" + userUsesys + '\'' +
				", sex=" + sex +
				", birthday='" + birthday + '\'' +
				", ic='" + ic + '\'' +
				", icBindDate='" + icBindDate + '\'' +
				", mobileNumber='" + mobileNumber + '\'' +
				", mobilePhoneVerify=" + mobilePhoneVerify +
				", certificateType='" + certificateType + '\'' +
				", certificateTypeInt=" + certificateTypeInt +
				", certificateNo='" + certificateNo + '\'' +
				", isMerge='" + isMerge + '\'' +
				", userType=" + userType +
				", openId='" + openId + '\'' +
				", userWechatNickName='" + userWechatNickName + '\'' +
				", appleId='" + appleId + '\'' +
				", headPic='" + headPic + '\'' +
				", nickName='" + nickName + '\'' +
				", facePic='" + facePic + '\'' +
				", faceId='" + faceId + '\'' +
				", unionId='" + unionId + '\'' +
				", updatePwdTime='" + updatePwdTime + '\'' +
				", email='" + email + '\'' +
				", postCode='" + postCode + '\'' +
				", address='" + address + '\'' +
				", highestRecordSchool='" + highestRecordSchool + '\'' +
				", specialtyId='" + specialtyId + '\'' +
				", userTitleType='" + userTitleType + '\'' +
				", title='" + title + '\'' +
				", categoryCertificate='" + categoryCertificate + '\'' +
				", scopeCertificate='" + scopeCertificate + '\'' +
				", userHspName='" + userHspName + '\'' +
				", hospital='" + hospital + '\'' +
				", intro='" + intro + '\'' +
				", skill='" + skill + '\'' +
				", workProvince='" + workProvince + '\'' +
				", workProvinceName='" + workProvinceName + '\'' +
				", workCity='" + workCity + '\'' +
				", workCityName='" + workCityName + '\'' +
				", workCounties='" + workCounties + '\'' +
				", workCountiesName='" + workCountiesName + '\'' +
				", deptCode='" + deptCode + '\'' +
				", deptName='" + deptName + '\'' +
				", highestDegree='" + highestDegree + '\'' +
				", noHospitalOrgname='" + noHospitalOrgname + '\'' +
				", specialtyName='" + specialtyName + '\'' +
				", titleName='" + titleName + '\'' +
				", titleTypeName='" + titleTypeName + '\'' +
				", createTime='" + createTime + '\'' +
				", updateTime='" + updateTime + '\'' +
				'}';
	}
}