package com.hys.zyy.manage.model;

import java.util.Date;

public class CoursewareVo {

	private Long coursewareId;

    private Long courseId;

    private String coursewareName;

    private Short coursewareType;

    private String link;

    private Short sortSeq;

    private String orgCode;

    private Date createTime;

    private Date updateTime;

    private Integer targetSortSeq;

    private String jsUrl;

    private Integer playStatue;

    /** 播放时长*/
    private String whenLong;

    private String picPath;

    private Long teacherId;

    private String teacherName;

    private String teacherOrg;

    private String teacherIntro;

    private String teacherPic;
    
    private Integer chargeType;

    private Long price;
    
    private Integer coursewareSource;
    
	public Integer getCoursewareSource() {
		return coursewareSource;
	}

	public void setCoursewareSource(Integer coursewareSource) {
		this.coursewareSource = coursewareSource;
	}

	public Integer getChargeType() {
		return chargeType;
	}

	public void setChargeType(Integer chargeType) {
		this.chargeType = chargeType;
	}

	public Long getPrice() {
		return price;
	}

	public void setPrice(Long price) {
		this.price = price;
	}

	public Long getCoursewareId() {
		return coursewareId;
	}

	public void setCoursewareId(Long coursewareId) {
		this.coursewareId = coursewareId;
	}

	public Long getCourseId() {
		return courseId;
	}

	public void setCourseId(Long courseId) {
		this.courseId = courseId;
	}

	public String getCoursewareName() {
		return coursewareName;
	}

	public void setCoursewareName(String coursewareName) {
		this.coursewareName = coursewareName;
	}

	public Short getCoursewareType() {
		return coursewareType;
	}

	public void setCoursewareType(Short coursewareType) {
		this.coursewareType = coursewareType;
	}

	public String getLink() {
		return link;
	}

	public void setLink(String link) {
		this.link = link;
	}

	public Short getSortSeq() {
		return sortSeq;
	}

	public void setSortSeq(Short sortSeq) {
		this.sortSeq = sortSeq;
	}

	public String getOrgCode() {
		return orgCode;
	}

	public void setOrgCode(String orgCode) {
		this.orgCode = orgCode;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public Integer getTargetSortSeq() {
		return targetSortSeq;
	}

	public void setTargetSortSeq(Integer targetSortSeq) {
		this.targetSortSeq = targetSortSeq;
	}

	public String getJsUrl() {
		return jsUrl;
	}

	public void setJsUrl(String jsUrl) {
		this.jsUrl = jsUrl;
	}

	public Integer getPlayStatue() {
		return playStatue;
	}

	public void setPlayStatue(Integer playStatue) {
		this.playStatue = playStatue;
	}

	public String getWhenLong() {
		return whenLong;
	}

	public void setWhenLong(String whenLong) {
		this.whenLong = whenLong;
	}

	public String getPicPath() {
		return picPath;
	}

	public void setPicPath(String picPath) {
		this.picPath = picPath;
	}

	public Long getTeacherId() {
		return teacherId;
	}

	public void setTeacherId(Long teacherId) {
		this.teacherId = teacherId;
	}

	public String getTeacherName() {
		return teacherName;
	}

	public void setTeacherName(String teacherName) {
		this.teacherName = teacherName;
	}

	public String getTeacherOrg() {
		return teacherOrg;
	}

	public void setTeacherOrg(String teacherOrg) {
		this.teacherOrg = teacherOrg;
	}

	public String getTeacherIntro() {
		return teacherIntro;
	}

	public void setTeacherIntro(String teacherIntro) {
		this.teacherIntro = teacherIntro;
	}

	public String getTeacherPic() {
		return teacherPic;
	}

	public void setTeacherPic(String teacherPic) {
		this.teacherPic = teacherPic;
	}
    
}
