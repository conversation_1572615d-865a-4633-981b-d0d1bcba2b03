package com.hys.zyy.manage.model;
/**
 * 审核结果
 * <AUTHOR>
 */
public class EmAuditVO {

	// 考试ID
	private Long examId;

	// 审核流程Id
	private Long auditProcessId;

	// 审核角色
	private Long auditRole;

	// 考试人员主键
	private Long examineeId;

	// 审核结果
	private Integer auditResult;

	// 流程级别
	private Integer auditLevel;

	// 不通过原因
	private String reason;

	//为了单独上线此版本，添加修改文件。30261版本未上线此类
	private Integer isSuperiorAudit;

	private Integer isPassedSuperiorAudit;

	public Long getExamId() {
		return examId;
	}

	public void setExamId(Long examId) {
		this.examId = examId;
	}

	public Long getAuditProcessId() {
		return auditProcessId;
	}

	public void setAuditProcessId(Long auditProcessId) {
		this.auditProcessId = auditProcessId;
	}

	public Long getExamineeId() {
		return examineeId;
	}

	public void setExamineeId(Long examineeId) {
		this.examineeId = examineeId;
	}

	public Integer getAuditResult() {
		return auditResult;
	}

	public void setAuditResult(Integer auditResult) {
		this.auditResult = auditResult;
	}

	public String getReason() {
		return reason;
	}

	public void setReason(String reason) {
		this.reason = reason;
	}

	public Integer getAuditLevel() {
		return auditLevel;
	}

	public void setAuditLevel(Integer auditLevel) {
		this.auditLevel = auditLevel;
	}

	public Long getAuditRole() {
		return auditRole;
	}

	public void setAuditRole(Long auditRole) {
		this.auditRole = auditRole;
	}

	public Integer getIsSuperiorAudit() {
		return isSuperiorAudit;
	}

	public void setIsSuperiorAudit(Integer isSuperiorAudit) {
		this.isSuperiorAudit = isSuperiorAudit;
	}

	public Integer getIsPassedSuperiorAudit() {
		return isPassedSuperiorAudit;
	}

	public void setIsPassedSuperiorAudit(Integer isPassedSuperiorAudit) {
		this.isPassedSuperiorAudit = isPassedSuperiorAudit;
	}

	@Override
	public String toString() {
		return "EmAuditVO [examId=" + examId + ", auditProcessId="
				+ auditProcessId + ", auditRole=" + auditRole + ", examineeId="
				+ examineeId + ", auditResult=" + auditResult + ", auditLevel="
				+ auditLevel + ", reason=" + reason + ", isSuperiorAudit="
				+ isSuperiorAudit + ", isPassedSuperiorAudit="
				+ isPassedSuperiorAudit + "]";
	}
	
}
