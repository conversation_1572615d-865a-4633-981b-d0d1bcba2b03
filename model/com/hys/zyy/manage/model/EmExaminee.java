package com.hys.zyy.manage.model;

import java.util.Date;

public class EmExaminee extends ZyyBaseObject {
	private Long id;
	/*
	 * 考试ID
	 */
	private Long examId;
	private String lastUpdateUser;
	/*
	 * 1: 未报名（默认） 2:报名成功 3:报名失败 4:撤销报名 5:重新报名 6:待审核
	 */
	private Long status;
	/*
	 * 默认0
	 */
	private Long version;
	private Date createDate;
	private String createUser;
	private Date lastUpdateDate;
	/*
	 * 报名审核结果：1 通过 -1 不通过 0 未审核（默认）
	 */
	private Long signUpAuditResult;
	/*
	 * 学员ID
	 */
	private String emUserId;
	/*
	 * 是否已审核或者录入成绩（默认0）
	 */
	private Long isUsed;
	/*
	 * 默认1
	 */
	private Long filterResult;
	/*
	 * 默认0，培训计划和轮转手册登记
	 */
	private Long trainplanManualverify;
	/*
	 * 默认0，所有轮转科室出科考试
	 */
	private Long cycleDeptExamination;
	/*
	 * 默认0，培训年限是否满足
	 */
	private Long trainYearFulfil;
	/*
	 * 默认0，截至报名审核前累计请假时间: 1: ＞2月, 0: ≤2月
	 */
	private Long totalLeaveTime;
	/*
	 * 默认0，年度考核
	 */
	private Long annualAppraisal;
	/*
	 * 默认0，中医模块一考试（仅中医学科填写）
	 */
	private Long chineseMedicilExam;
	/*
	 * 默认0，国家执业医师资格考试: 1:有资格证、注册证,2: 有资格证、未注册,3: 成绩通过, 4: 成绩未通过, 5: 未报考
	 */
	private Long nationQualificationsExam;
	private String catalog;
	/*
	 * 默认0， 区分类别，用于考生分组，因为考生类别有可能相同
	 */
	private Long catalogNumber;
	private Long joinCourseCount;
	private Long isCourseQual;
	private String exemptionCourse;
	/*
	 * 默认0，公共科目考试
	 */
	private Long publicSubjectExam;

	private String examName;
	private String courseName;
	private Integer examCatalog;
	/*
	 * 总分（默认0）
	 */
	private String totalScore;

	public EmExaminee() {
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getExamId() {
		return examId;
	}

	public void setExamId(Long examId) {
		this.examId = examId;
	}

	public String getLastUpdateUser() {
		return lastUpdateUser;
	}

	public void setLastUpdateUser(String lastUpdateUser) {
		this.lastUpdateUser = lastUpdateUser;
	}

	public Long getStatus() {
		return status;
	}

	public void setStatus(Long status) {
		this.status = status;
	}

	public Long getVersion() {
		return version;
	}

	public void setVersion(Long version) {
		this.version = version;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public String getCreateUser() {
		return createUser;
	}

	public void setCreateUser(String createUser) {
		this.createUser = createUser;
	}

	public Date getLastUpdateDate() {
		return lastUpdateDate;
	}

	public void setLastUpdateDate(Date lastUpdateDate) {
		this.lastUpdateDate = lastUpdateDate;
	}

	public Long getSignUpAuditResult() {
		return signUpAuditResult;
	}

	public void setSignUpAuditResult(Long signUpAuditResult) {
		this.signUpAuditResult = signUpAuditResult;
	}

	public String getEmUserId() {
		return emUserId;
	}

	public void setEmUserId(String emUserId) {
		this.emUserId = emUserId;
	}

	public Long getIsUsed() {
		return isUsed;
	}

	public void setIsUsed(Long isUsed) {
		this.isUsed = isUsed;
	}

	public Long getFilterResult() {
		return filterResult;
	}

	public void setFilterResult(Long filterResult) {
		this.filterResult = filterResult;
	}

	public Long getTrainplanManualverify() {
		return trainplanManualverify;
	}

	public void setTrainplanManualverify(Long trainplanManualverify) {
		this.trainplanManualverify = trainplanManualverify;
	}

	public Long getCycleDeptExamination() {
		return cycleDeptExamination;
	}

	public void setCycleDeptExamination(Long cycleDeptExamination) {
		this.cycleDeptExamination = cycleDeptExamination;
	}

	public Long getTrainYearFulfil() {
		return trainYearFulfil;
	}

	public void setTrainYearFulfil(Long trainYearFulfil) {
		this.trainYearFulfil = trainYearFulfil;
	}

	public Long getTotalLeaveTime() {
		return totalLeaveTime;
	}

	public void setTotalLeaveTime(Long totalLeaveTime) {
		this.totalLeaveTime = totalLeaveTime;
	}

	public Long getAnnualAppraisal() {
		return annualAppraisal;
	}

	public void setAnnualAppraisal(Long annualAppraisal) {
		this.annualAppraisal = annualAppraisal;
	}

	public Long getChineseMedicilExam() {
		return chineseMedicilExam;
	}

	public void setChineseMedicilExam(Long chineseMedicilExam) {
		this.chineseMedicilExam = chineseMedicilExam;
	}

	public Long getNationQualificationsExam() {
		return nationQualificationsExam;
	}

	public void setNationQualificationsExam(Long nationQualificationsExam) {
		this.nationQualificationsExam = nationQualificationsExam;
	}

	public String getCatalog() {
		return catalog;
	}

	public void setCatalog(String catalog) {
		this.catalog = catalog;
	}

	public Long getCatalogNumber() {
		return catalogNumber;
	}

	public void setCatalogNumber(Long catalogNumber) {
		this.catalogNumber = catalogNumber;
	}

	public Long getJoinCourseCount() {
		return joinCourseCount;
	}

	public void setJoinCourseCount(Long joinCourseCount) {
		this.joinCourseCount = joinCourseCount;
	}

	public Long getIsCourseQual() {
		return isCourseQual;
	}

	public void setIsCourseQual(Long isCourseQual) {
		this.isCourseQual = isCourseQual;
	}

	public String getExemptionCourse() {
		return exemptionCourse;
	}

	public void setExemptionCourse(String exemptionCourse) {
		this.exemptionCourse = exemptionCourse;
	}

	public Long getPublicSubjectExam() {
		return publicSubjectExam;
	}

	public void setPublicSubjectExam(Long publicSubjectExam) {
		this.publicSubjectExam = publicSubjectExam;
	}

	public String getExamName() {
		return examName;
	}

	public void setExamName(String examName) {
		this.examName = examName;
	}

	public String getCourseName() {
		return courseName;
	}

	public void setCourseName(String courseName) {
		this.courseName = courseName;
	}

	public Integer getExamCatalog() {
		return examCatalog;
	}

	public void setExamCatalog(Integer examCatalog) {
		this.examCatalog = examCatalog;
	}

	public String getTotalScore() {
		return totalScore;
	}

	public void setTotalScore(String totalScore) {
		this.totalScore = totalScore;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		EmExaminee other = (EmExaminee) obj;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		return true;
	}

	@Override
	public String toString() {
		return "EmExaminee [id=" + id + ", examId=" + examId
				+ ", lastUpdateUser=" + lastUpdateUser + ", status=" + status
				+ ", version=" + version + ", createDate=" + createDate
				+ ", createUser=" + createUser + ", lastUpdateDate="
				+ lastUpdateDate + ", signUpAuditResult=" + signUpAuditResult
				+ ", emUserId=" + emUserId + ", isUsed=" + isUsed
				+ ", filterResult=" + filterResult + ", trainplanManualverify="
				+ trainplanManualverify + ", cycleDeptExamination="
				+ cycleDeptExamination + ", trainYearFulfil=" + trainYearFulfil
				+ ", totalLeaveTime=" + totalLeaveTime + ", annualAppraisal="
				+ annualAppraisal + ", chineseMedicilExam="
				+ chineseMedicilExam + ", nationQualificationsExam="
				+ nationQualificationsExam + ", catalog=" + catalog
				+ ", catalogNumber=" + catalogNumber + ", joinCourseCount="
				+ joinCourseCount + ", isCourseQual=" + isCourseQual
				+ ", exemptionCourse=" + exemptionCourse
				+ ", publicSubjectExam=" + publicSubjectExam + ", examName="
				+ examName + ", courseName=" + courseName + ", examCatalog="
				+ examCatalog + ", totalScore=" + totalScore + "]";
	}

}
