package com.hys.zyy.manage.model;

import java.util.List;

public class EmExamineeVO extends EmExaminee {
	/*
	 * 姓名
	 */
	private String realName;
	/*
	 * 性别
	 */
	private String sex;
	/*
	 * 证件号码
	 */
	private String certificateNo;
	/*
	 * 培训专业
	 */
	private String trainingMajorStr;
	
	private String trainingMajor;
	
	/*
	 * 培训起始年份
	 */
	private String trainingStartYear;
	/*
	 * 培训年限/学制
	 */
	private String schoolSystemStr;
	private Integer schoolSystem;
	/*
	 * 学员身份1 -单位人, 2 -社会人, 3-学位衔接
	 */
	private Integer residencySource;
	/*
	 * 身份类型（页面检索使用）：1，单位人	2，社会人	3，学位衔接
	 */
	private Integer identityType;
	private String identityTypeStr;
	/*
	 * 0否，1是
	 */
	private Integer degreeConvergence;
	/*
	 * 报考类别
	 */
	private String examTypeStr;
	private Integer examType;
	/*
	 * 报考阶段
	 */
	private String examStageStr;
	private String examStage;
	/*
	 * 医院/基地ID
	 */
	private Long hospitalId;
	/*
	 * 医院/基地名称
	 */
	private String hospitalName;
	/*
	 * 报名表ID
	 */
	private Long examineeId;
	/*
	 * 考试名称
	 */
	private String examName;
	/*
	 * 年度ID
	 */
	private Long recruitYearId;
	/*
	 * 工作单位
	 */
	private String workUnit;
	/*
	 * 图像路径
	 */
	private String photoPath;
	/*
	 * 手机号
	 */
	private String mobilNumber;
	/*
	 * 医师资格证书编号
	 */
	private String certificateNumber;
	
	private String practiceCertificateNumber;
	/*
	 * 一级审核（医院）审核结果
	 */
	private Integer auditResult1;
	private String auditResultStr;
	/*
	 * 二级审核（省厅）审核结果
	 */
	private Integer auditResult2;
	/*
	 * 审核级别
	 */
	private Integer auditLevel;
	/*
	 * 以,分割的考试ID字符串，eg：12065,12064
	 */
	private String examIds;
	/*
	 * 审核结果集[医院（一级审核）和省厅（二级审核）]auditResultList
	 */
	private List<EmAuditVO> auditResultList;
	
	private Integer mergerExam;

	public String getRealName() {
		return realName;
	}

	public void setRealName(String realName) {
		this.realName = realName == null ? null : realName.trim();
	}

	public String getSex() {
		return sex;
	}

	public void setSex(String sex) {
		this.sex = sex;
	}

	public String getCertificateNo() {
		return certificateNo;
	}

	public void setCertificateNo(String certificateNo) {
		this.certificateNo = certificateNo == null ? null : certificateNo
				.trim();
	}
	
	public String getTrainingMajorStr() {
		return trainingMajorStr;
	}

	public void setTrainingMajorStr(String trainingMajorStr) {
		this.trainingMajorStr = trainingMajorStr == null ? null :trainingMajorStr.trim();
	}
	
	public String getTrainingMajor() {
		return trainingMajor;
	}

	public void setTrainingMajor(String trainingMajor) {
		this.trainingMajor = trainingMajor == null ? null : trainingMajor.trim();
	}

	public String getTrainingStartYear() {
		return trainingStartYear;
	}

	public void setTrainingStartYear(String trainingStartYear) {
		this.trainingStartYear = trainingStartYear == null ? null
				: trainingStartYear.trim();
	}
	
	public String getSchoolSystemStr() {
		return schoolSystemStr;
	}

	public void setSchoolSystemStr(String schoolSystemStr) {
		this.schoolSystemStr = schoolSystemStr == null ? null : schoolSystemStr.trim();
	}

	public Integer getResidencySource() {
		return residencySource;
	}

	public void setResidencySource(Integer residencySource) {
		this.residencySource = residencySource;
	}

	public Integer getDegreeConvergence() {
		return degreeConvergence;
	}

	public void setDegreeConvergence(Integer degreeConvergence) {
		this.degreeConvergence = degreeConvergence;
	}
	
	public String getExamStageStr() {
		return examStageStr;
	}

	public void setExamStageStr(String examStageStr) {
		this.examStageStr = examStageStr;
	}

	public Long getHospitalId() {
		return hospitalId;
	}

	public void setHospitalId(Long hospitalId) {
		this.hospitalId = hospitalId;
	}

	public String getExamName() {
		return examName;
	}

	public void setExamName(String examName) {
		this.examName = examName;
	}

	public Integer getExamType() {
		return examType;
	}

	public void setExamType(Integer examType) {
		this.examType = examType;
	}
	
	public String getExamStage() {
		return examStage;
	}

	public void setExamStage(String examStage) {
		this.examStage = examStage == null ? null : examStage.trim();
	}

	public Integer getSchoolSystem() {
		return schoolSystem;
	}

	public void setSchoolSystem(Integer schoolSystem) {
		this.schoolSystem = schoolSystem;
	}

	public Long getRecruitYearId() {
		return recruitYearId;
	}

	public void setRecruitYearId(Long recruitYearId) {
		this.recruitYearId = recruitYearId;
	}

	public String getHospitalName() {
		return hospitalName;
	}

	public void setHospitalName(String hospitalName) {
		this.hospitalName = hospitalName;
	}

	public Integer getIdentityType() {
		return identityType;
	}

	public void setIdentityType(Integer identityType) {
		this.identityType = identityType;
	}

	public String getWorkUnit() {
		return workUnit;
	}

	public void setWorkUnit(String workUnit) {
		this.workUnit = workUnit == null ? null : workUnit.trim();
	}

	public String getPhotoPath() {
		return photoPath;
	}

	public void setPhotoPath(String photoPath) {
		this.photoPath = photoPath == null ? null : photoPath.trim();
	}

	public String getMobilNumber() {
		return mobilNumber;
	}

	public void setMobilNumber(String mobilNumber) {
		this.mobilNumber = mobilNumber;
	}

	public String getPracticeCertificateNumber() {
		return practiceCertificateNumber;
	}

	public void setPracticeCertificateNumber(String practiceCertificateNumber) {
		this.practiceCertificateNumber = practiceCertificateNumber == null ? null : practiceCertificateNumber.trim();
	}

	public String getExamTypeStr() {
		return examTypeStr;
	}

	public void setExamTypeStr(String examTypeStr) {
		this.examTypeStr = examTypeStr;
	}

	public String getCertificateNumber() {
		return certificateNumber;
	}

	public void setCertificateNumber(String certificateNumber) {
		this.certificateNumber = certificateNumber == null ? null : certificateNumber.trim();
	}
	
	public Integer getAuditResult1() {
		return auditResult1;
	}

	public void setAuditResult1(Integer auditResult1) {
		this.auditResult1 = auditResult1;
	}
	
	public Integer getAuditResult2() {
		return auditResult2;
	}

	public void setAuditResult2(Integer auditResult2) {
		this.auditResult2 = auditResult2;
	}

	public Integer getAuditLevel() {
		return auditLevel;
	}

	public void setAuditLevel(Integer auditLevel) {
		this.auditLevel = auditLevel;
	}

	public String getIdentityTypeStr() {
		String result = "";
		/*if (residencySource != null) {
			if (residencySource == 1) {
				result = "单位人";
			} else if (residencySource == 2) {
				if (degreeConvergence != null && degreeConvergence == 1) {
					result = "学位衔接";
				} else {
					result = "社会人";
				}
			}
		}*/
		if(degreeConvergence != null && degreeConvergence == 1){
			result = "学位衔接";
		}else{
			if (residencySource == 1) {
				result = "单位人";
			}else{
				result = "社会人";
			}
		}
		return result;
	}

	public void setIdentityTypeStr(String identityTypeStr) {
		this.identityTypeStr = identityTypeStr;
	}

	public String getAuditResultStr() {
		return auditResultStr;
	}

	public void setAuditResultStr(String auditResultStr) {
		this.auditResultStr = auditResultStr == null ? null : auditResultStr.trim();
	}

	public String getExamIds() {
		return examIds;
	}

	public void setExamIds(String examIds) {
		this.examIds = examIds == null ? null : examIds.trim();
	}
	
	public List<EmAuditVO> getAuditResultList() {
		return auditResultList;
	}

	public void setAuditResultList(List<EmAuditVO> auditResultList) {
		this.auditResultList = auditResultList;
	}

	public Long getExamineeId() {
		return examineeId;
	}

	public void setExamineeId(Long examineeId) {
		this.examineeId = examineeId;
	}

	public Integer getMergerExam() {
		return mergerExam;
	}

	public void setMergerExam(Integer mergerExam) {
		this.mergerExam = mergerExam;
	}
	
}
