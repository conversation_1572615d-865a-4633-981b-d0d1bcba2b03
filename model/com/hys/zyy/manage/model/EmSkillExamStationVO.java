package com.hys.zyy.manage.model;

import java.math.BigDecimal;



public class EmSkillExamStationVO extends EmSkillExamStation {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	private String orgName;
	
	private String year;
	
	private String baseName;
	
	private String userName;
	
	private String certificateNo;
	/**
	 * 技能考站考核成绩
	 */
	private BigDecimal skillStationExamScore;
	/**
	 * 技能考站考核结果 1:通过;2不通过
	 */
	private Integer skillStationExamResult;
	
	private String examName;
	
	private Long zyyUserId;
	/**
	 * 表em_skill_station_score的id
	 */
	private Long scoreId;
	
	public Long getZyyUserId() {
		return zyyUserId;
	}
	public void setZyyUserId(Long zyyUserId) {
		this.zyyUserId = zyyUserId;
	}
	public Long getScoreId() {
		return scoreId;
	}
	public void setScoreId(Long scoreId) {
		this.scoreId = scoreId;
	}
	public String getExamName() {
		return examName;
	}
	public void setExamName(String examName) {
		this.examName = examName;
	}
	public String getOrgName() {
		return orgName;
	}
	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}
	public String getYear() {
		return year;
	}
	public void setYear(String year) {
		this.year = year;
	}
	public String getBaseName() {
		return baseName;
	}
	public void setBaseName(String baseName) {
		this.baseName = baseName;
	}
	public String getUserName() {
		return userName;
	}
	public void setUserName(String userName) {
		this.userName = userName;
	}
	public String getCertificateNo() {
		return certificateNo;
	}
	public void setCertificateNo(String certificateNo) {
		this.certificateNo = certificateNo;
	}
	public BigDecimal getSkillStationExamScore() {
		return skillStationExamScore;
	}
	public void setSkillStationExamScore(BigDecimal skillStationExamScore) {
		this.skillStationExamScore = skillStationExamScore;
	}
	public Integer getSkillStationExamResult() {
		return skillStationExamResult;
	}
	public void setSkillStationExamResult(Integer skillStationExamResult) {
		this.skillStationExamResult = skillStationExamResult;
	}
	
}
