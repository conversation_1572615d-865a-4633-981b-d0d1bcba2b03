package com.hys.zyy.manage.model;

import java.util.Date;

/**
 * em_user_certificate 表
 * <AUTHOR>
 * 2017-6-15 下午2:51:31
 */
public class EmUserCertificate extends ZyyBaseObject {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	private Long id;
	
	private Long zyyUserId;
	/**
	 * 是否获得证书1：是；2：否
	 */
	private Integer hasCertificate;
	/**
	 * 证书编号
	 */
	private String certificateNo;
	/**
	 * 证书编号生成日期
	 */
	private Date certificateNoGenDate;
	
	private Date createDate;
	
	private Date lastUpdateDate;
	
	private Integer status;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getZyyUserId() {
		return zyyUserId;
	}

	public void setZyyUserId(Long zyyUserId) {
		this.zyyUserId = zyyUserId;
	}

	public Integer getHasCertificate() {
		return hasCertificate;
	}

	public void setHasCertificate(Integer hasCertificate) {
		this.hasCertificate = hasCertificate;
	}

	public String getCertificateNo() {
		return certificateNo;
	}

	public void setCertificateNo(String certificateNo) {
		this.certificateNo = certificateNo;
	}

	public Date getCertificateNoGenDate() {
		return certificateNoGenDate;
	}

	public void setCertificateNoGenDate(Date certificateNoGenDate) {
		this.certificateNoGenDate = certificateNoGenDate;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Date getLastUpdateDate() {
		return lastUpdateDate;
	}

	public void setLastUpdateDate(Date lastUpdateDate) {
		this.lastUpdateDate = lastUpdateDate;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}


}
