package com.hys.zyy.manage.model;


public class JoinDeptRecordQueryTable extends ZyyBaseObject{

	private static final long serialVersionUID = -3997840581594817308L;
	/**
	 * 学年制
	 */
	private String year;
	/**
	 * 科室
	 */
	private Long deptId;
	/**
	 * 身份证
	 */
	private String card;
	/**
	 * 学员状态：1，已经入科。0，未入科。
	 */
	private Integer status;
	
	/**
	 * 理论成绩
	 */
	private Boolean isImportTheory;
	/**
	 * 技能成绩
	 */
	private Boolean isImportSkill;
	
	/**
	 * 真实姓名
	 */
	private String realName;
	/**
	 * 轮转状态：1，正在进行。0，结束了
	 */
	private Integer cycleState;
	/**
	 * 省厅id
	 */
	private Long zyyUserProvinceId;
	/**
	 * 用户类型
	 */
	private Integer userType;
	/**
	 * 用户科室
	 */
	private Long userDept;
	/**
	 * 对应zyy_cycle_timeline的ID
	 */
	private Long ctId;
	/**
	 * 审核状态
	 */
	private Long auditState;
	/**
	 * 专业ID
	 */
	private Long baseStdId;
	/**
	 * 开始时间
	 */
	private String startDate;
	/**
	 * 结束时间
	 */
	private String endDate;
	/**
	 * 医院id
	 */
	private Long hospId;
	
	/**
	 * 考试ID
	 */
	private Long examId;
	
	//出科审核的评论
	String auditReamrk;
	//入科上传的附件
	String enterDeptAtt;
	
	
	private String examName;
	
	private Long uId;//考试学员ID
	
	/**
	 * 查询的时间 具体到某年某月
	 * @return
	 */
	String yearAndMonth;
	
	private String endYearAndMonth;
	
	/**
	 * 查询的时间 具体到某年某月
	 * @return
	 */
	private String timelineMonth;
	private String endTimelineMonth;
	
	//排序字段
	String orderFiled;
	private Integer residencySource;
	private Integer userCategory;
	
	public Long getHospId() {
		return hospId;
	}
	public void setHospId(Long hospId) {
		this.hospId = hospId;
	}
	public JoinDeptRecordQueryTable(){
		
	}
	public String getCard() {
		return card;
	}
	public void setCard(String card) {
		this.card = card;
	}
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	public String getRealName() {
		return realName;
	}
	public void setRealName(String realName) {
		this.realName = realName;
	}

	public Long getZyyUserProvinceId() {
		return zyyUserProvinceId;
	}

	public void setZyyUserProvinceId(Long zyyUserProvinceId) {
		this.zyyUserProvinceId = zyyUserProvinceId;
	}

	public Integer getUserType() {
		return userType;
	}

	public void setUserType(Integer userType) {
		this.userType = userType;
	}

	public Long getUserDept() {
		return userDept;
	}

	public void setUserDept(Long userDept) {
		this.userDept = userDept;
	}

	public Long getDeptId() {
		return deptId;
	}

	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}

	public Long getCtId() {
		return ctId;
	}

	public void setCtId(Long ctId) {
		this.ctId = ctId;
	}

	public String getYear() {
		return year;
	}

	public void setYear(String year) {
		this.year = year;
	}
	public Integer getCycleState() {
		return cycleState;
	}
	public void setCycleState(Integer cycleState) {
		this.cycleState = cycleState;
	}
	public Boolean getIsImportTheory() {
		return isImportTheory;
	}
	public void setIsImportTheory(Boolean isImportTheory) {
		this.isImportTheory = isImportTheory;
	}
	public Boolean getIsImportSkill() {
		return isImportSkill;
	}
	public void setIsImportSkill(Boolean isImportSkill) {
		this.isImportSkill = isImportSkill;
	}
	public Long getAuditState() {
		return auditState;
	}
	public void setAuditState(Long auditState) {
		this.auditState = auditState;
	}
	public Long getBaseStdId() {
		return baseStdId;
	}
	public void setBaseStdId(Long baseStdId) {
		this.baseStdId = baseStdId;
	}
	public String getStartDate() {
		return startDate;
	}
	public void setStartDate(String startDate) {
		this.startDate = startDate;
	}
	public String getEndDate() {
		return endDate;
	}
	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}
	public Long getExamId() {
		return examId;
	}
	public void setExamId(Long examId) {
		this.examId = examId;
	}
	public String getYearAndMonth() {
		return yearAndMonth;
	}
	public void setYearAndMonth(String yearAndMonth) {
		this.yearAndMonth = yearAndMonth;
	}
	
	public String getEndYearAndMonth() {
		return endYearAndMonth;
	}
	public void setEndYearAndMonth(String endYearAndMonth) {
		this.endYearAndMonth = endYearAndMonth == null ? null : endYearAndMonth.trim();
	}
	public String getOrderFiled() {
		return orderFiled;
	}
	public void setOrderFiled(String orderFiled) {
		this.orderFiled = orderFiled;
	}
	public Long getuId() {
		return uId;
	}
	public void setuId(Long uId) {
		this.uId = uId;
	}
	public String getExamName() {
		return examName;
	}
	public void setExamName(String examName) {
		this.examName = examName;
	}
	public String getAuditReamrk() {
		return auditReamrk;
	}
	public void setAuditReamrk(String auditReamrk) {
		this.auditReamrk = auditReamrk;
	}
	public String getEnterDeptAtt() {
		return enterDeptAtt;
	}
	public void setEnterDeptAtt(String enterDeptAtt) {
		this.enterDeptAtt = enterDeptAtt;
	}
	public String getTimelineMonth() {
		return timelineMonth;
	}
	public void setTimelineMonth(String timelineMonth) {
		this.timelineMonth = timelineMonth == null ? null : timelineMonth.trim();
	}
	public String getEndTimelineMonth() {
		return endTimelineMonth;
	}
	public void setEndTimelineMonth(String endTimelineMonth) {
		this.endTimelineMonth = endTimelineMonth == null ? null : endTimelineMonth.trim();
	}
	public Integer getResidencySource() {
		return residencySource;
	}
	public void setResidencySource(Integer residencySource) {
		this.residencySource = residencySource;
	}
	public Integer getUserCategory() {
		return userCategory;
	}
	public void setUserCategory(Integer userCategory) {
		this.userCategory = userCategory;
	}
	
}