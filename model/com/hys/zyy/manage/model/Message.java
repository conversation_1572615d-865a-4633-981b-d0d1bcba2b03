package com.hys.zyy.manage.model;

import org.codehaus.jackson.map.annotate.JsonSerialize;

@JsonSerialize(include=JsonSerialize.Inclusion.NON_NULL)
public class Message {
	
	private String errcode;		//返回状态码
	
	private String errmsg;		//状态信息
	
	private String token;		//返回token
	
	private String org_code;
	
	private String provinceOrgCode;
	
	private String version;
	
	private String list;
	
	public String getErrcode() {
		return errcode;
	}
	public void setErrcode(String errcode) {
		this.errcode = errcode;
	}
	public String getErrmsg() {
		return errmsg;
	}
	public void setErrmsg(String errmsg) {
		this.errmsg = errmsg;
	}
	public String getToken() {
		return token;
	}
	public void setToken(String token) {
		this.token = token;
	}
	public String getOrg_code() {
		return org_code;
	}
	public void setOrg_code(String org_code) {
		this.org_code = org_code;
	}
	public String getVersion() {
		return version;
	}
	public void setVersion(String version) {
		this.version = version;
	}
	public String getList() {
		return list;
	}
	public void setList(String list) {
		this.list = list;
	}
	public String getProvinceOrgCode() {
		return provinceOrgCode;
	}
	public void setProvinceOrgCode(String provinceOrgCode) {
		this.provinceOrgCode = provinceOrgCode;
	}	
}
