package com.hys.zyy.manage.model;

public class MyMessage extends ZyyBaseObject {

	private static final long serialVersionUID = 1L;
	private Long id;
	private String messageType;
	private String messageName;
	private String timeSection;
	private Integer resiCount;
	private String createrName;

	public MyMessage() {
		super();
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getMessageType() {
		return messageType;
	}

	public void setMessageType(String messageType) {
		this.messageType = messageType == null ? null : messageType.trim();
	}

	public String getMessageName() {
		return messageName;
	}

	public void setMessageName(String messageName) {
		this.messageName = messageName == null ? null : messageName.trim();
	}

	public String getTimeSection() {
		return timeSection;
	}

	public void setTimeSection(String timeSection) {
		this.timeSection = timeSection == null ? null : timeSection.trim();
	}

	public Integer getResiCount() {
		return resiCount;
	}

	public void setResiCount(Integer resiCount) {
		this.resiCount = resiCount;
	}

	public String getCreaterName() {
		return createrName;
	}

	public void setCreaterName(String createrName) {
		this.createrName = createrName == null ? null : createrName.trim();
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		MyMessage other = (MyMessage) obj;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		return true;
	}

	@Override
	public String toString() {
		return "MyMessage [id=" + id + ", messageType=" + messageType
				+ ", messageName=" + messageName + ", timeSection="
				+ timeSection + ", resiCount=" + resiCount + ", createrName="
				+ createrName + "]";
	}

}
