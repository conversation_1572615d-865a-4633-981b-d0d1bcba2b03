package com.hys.zyy.manage.model;

import java.io.Serializable;

/**
 * 七牛云的路径及名称
 * <AUTHOR>
 */
public class QiniuFileInfo implements Serializable {
	private static final long serialVersionUID = 1L;
	/*
	 * 文件名称
	 */
	private String fileName;
	/*
	 * 文件路径
	 */
	private String filePath;

	public QiniuFileInfo() {
		super();
	}

	public QiniuFileInfo(String fileName, String filePath) {
		super();
		this.fileName = fileName;
		this.filePath = filePath;
	}

	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName == null ? null : fileName.trim();
	}

	public String getFilePath() {
		return filePath;
	}

	public void setFilePath(String filePath) {
		this.filePath = filePath == null ? null : filePath.trim();
	}

}
