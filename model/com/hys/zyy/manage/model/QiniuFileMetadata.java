package com.hys.zyy.manage.model;

import java.io.Serializable;

/**
 * 七牛云资源元信息
 */
public class QiniuFileMetadata implements Serializable {
	private static final long serialVersionUID = 1L;
	/*
	 * 文件大小，int64 类型，单位为字节（Byte）
	 */
	private Long fsize;
	/*
	 * 文件HASH值，string 类型
	 */
	private String hash;
	/*
	 * 文件MIME类型，string 类型
	 */
	private String mimeType;
	/*
	 * 文件存储类型，uint32 类型，0表示标准存储，1 表示低频存储，2 表示归档存储，3 表示深度归档存储
	 */
	private Integer type;
	/*
	 * 文件上传时间Unix时间戳格式，单位为 100 纳秒
	 */
	private Long putTime;
	/*
	 * 文件 md5 值
	 */
	private String md5;

	public QiniuFileMetadata() {
		super();
	}

	public Long getFsize() {
		return fsize;
	}

	public void setFsize(Long fsize) {
		this.fsize = fsize;
	}

	public String getHash() {
		return hash;
	}

	public void setHash(String hash) {
		this.hash = hash == null ? null : hash.trim();
	}

	public String getMimeType() {
		return mimeType;
	}

	public void setMimeType(String mimeType) {
		this.mimeType = mimeType == null ? null : mimeType.trim();
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public Long getPutTime() {
		return putTime;
	}

	public void setPutTime(Long putTime) {
		this.putTime = putTime;
	}

	public String getMd5() {
		return md5;
	}

	public void setMd5(String md5) {
		this.md5 = md5 == null ? null : md5.trim();
	}

	@Override
	public String toString() {
		return "QiniuFileMetadata [fsize=" + fsize + ", hash=" + hash
				+ ", mimeType=" + mimeType + ", type=" + type + ", putTime="
				+ putTime + ", md5=" + md5 + "]";
	}

}