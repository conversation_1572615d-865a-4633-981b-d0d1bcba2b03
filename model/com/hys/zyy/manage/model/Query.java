package com.hys.zyy.manage.model;

import java.io.Serializable;
import java.util.Date;

import com.hys.zyy.manage.util.DateUtil;

@SuppressWarnings("unused")
public class Query implements Serializable {
	private static final long serialVersionUID = 1L;
	private Date startDate;
	private String startDateStr;
	private Date endDate;
	private String endDateStr;

	public Date getStartDate() {
		return DateUtil.parse(startDateStr, DateUtil.FORMAT_SHORT);
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public String getStartDateStr() {
		return startDateStr;
	}

	public void setStartDateStr(String startDateStr) {
		this.startDateStr = startDateStr == null ? null : startDateStr.trim();
	}

	public Date getEndDate() {
		return DateUtil.parse(endDateStr, DateUtil.FORMAT_SHORT);
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	public String getEndDateStr() {
		return endDateStr;
	}

	public void setEndDateStr(String endDateStr) {
		this.endDateStr = endDateStr == null ? null : endDateStr.trim();
	}

}
