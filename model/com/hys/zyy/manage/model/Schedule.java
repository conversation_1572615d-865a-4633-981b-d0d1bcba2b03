package com.hys.zyy.manage.model;

import java.util.Date;

/**
 * 
 * <AUTHOR>
 * 
 * @date 2019-06-12
 */
public class Schedule {
    /**
     * id
     */
    private Long id;

    /**
     * 课表名称
     */
    private String name;

    /**
     * 年级id，对应表zyy_recruit_year
     */
    private Long yearId;
    
    /**
     * 展示用的年级
     */
    private String year;

    /**
     * 专业VALUE,对应表zyy_prof
     */
    private String major;
    
    /**
     * 展示用的专业,对应表zyy_prof
     */
    private String profValue;

    /**
     * 开始时间
     */
    private Date startDate;

    /**
     * 结束时间
     */
    private Date endDate;
    
    
    /**
     * 开始时间Str
     */
    private Date startDateStr;

    /**
     * 结束时间Str
     */
    private Date endDateStr;

    /**
     * 创建人
     */
    private Long createUserid;
    /**
     * 医院id
     */
    private Long orgId;

    /**
     * ������
     */
    private Date createTime;

    /**
     * ������
     */
    private Integer status;

    /**
     * ������
     */
    private Integer weekNum;

    /**
     * ������
     */
    private Date lastModifyTime;

    /**
     * ������
     */
    private Date releaseTime;

    /**
     * ������
     */
    private Long releaseUserId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public Long getYearId() {
        return yearId;
    }

    public void setYearId(Long yearId) {
        this.yearId = yearId;
    }

    public String getMajor() {
        return major;
    }

    public void setMajor(String major) {
        this.major = major == null ? null : major.trim();
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Long getCreateUserid() {
        return createUserid;
    }

    public void setCreateUserid(Long createUserid) {
        this.createUserid = createUserid;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getWeekNum() {
        return weekNum;
    }

    public void setWeekNum(Integer weekNum) {
        this.weekNum = weekNum;
    }

    public Date getLastModifyTime() {
        return lastModifyTime;
    }

    public void setLastModifyTime(Date lastModifyTime) {
        this.lastModifyTime = lastModifyTime;
    }

    public Date getReleaseTime() {
        return releaseTime;
    }

    public void setReleaseTime(Date releaseTime) {
        this.releaseTime = releaseTime;
    }

    public Long getReleaseUserId() {
        return releaseUserId;
    }

    public void setReleaseUserId(Long releaseUserId) {
        this.releaseUserId = releaseUserId;
    }

	public Date getStartDateStr() {
		return startDateStr;
	}

	public void setStartDateStr(Date startDateStr) {
		this.startDateStr = startDateStr;
	}

	public Date getEndDateStr() {
		return endDateStr;
	}

	public void setEndDateStr(Date endDateStr) {
		this.endDateStr = endDateStr;
	}

	public String getYear() {
		return year;
	}

	public void setYear(String year) {
		this.year = year;
	}

	public String getProfValue() {
		return profValue;
	}

	public void setProfValue(String profValue) {
		this.profValue = profValue;
	}

	public Long getOrgId() {
		return orgId;
	}

	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}
    
    
    
}