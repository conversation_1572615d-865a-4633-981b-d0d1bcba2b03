package com.hys.zyy.manage.model;

import java.util.Date;

/**
 * 课程的附件
 * 
 * <AUTHOR>
 * 
 * @date 2019-06-12
 */
public class ScheduleCourseAttachment {
    /**
     * id
     */
    private Long id;

    /**
     * 见习日期
     */
    private Date practiceDay;

    /**
     * 第几周
     */
    private Integer weekNum;

    /**
     * 上传人
     */
    private Long createUserid;

    /**
     * 上传时间
     */
    private Date createTime;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 最后更新时间
     */
    private Date lastModifyTime;

    /**
     * 课节：第3节课
     */
    private Integer pitchNumber;

    /**
     * 部门id，科室id
     */
    private Long deptId;

    /**
     * 文件路径
     */
    private String filePath;

    /**
     * 见习id, 与见习表关联
     */
    private Long practiceId;

    /**
     * 课表ID
     */
    private Long scheduledId;
    
    /**
     * 医院id
     */
    private Long orgId;
    /**
     * 附件原名
     */
    private String originalName;
    /**
     * 附件别名,上传之后的名字
     */
    private String aliasName;
    
    private Integer type;//1见习2课节
    

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getPracticeDay() {
        return practiceDay;
    }

    public void setPracticeDay(Date practiceDay) {
        this.practiceDay = practiceDay;
    }

    public Integer getWeekNum() {
        return weekNum;
    }

    public void setWeekNum(Integer weekNum) {
        this.weekNum = weekNum;
    }

    public Long getCreateUserid() {
        return createUserid;
    }

    public void setCreateUserid(Long createUserid) {
        this.createUserid = createUserid;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getLastModifyTime() {
        return lastModifyTime;
    }

    public void setLastModifyTime(Date lastModifyTime) {
        this.lastModifyTime = lastModifyTime;
    }

    public Integer getPitchNumber() {
        return pitchNumber;
    }

    public void setPitchNumber(Integer pitchNumber) {
        this.pitchNumber = pitchNumber;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath == null ? null : filePath.trim();
    }


    public Long getScheduledId() {
        return scheduledId;
    }

    public void setScheduledId(Long scheduledId) {
        this.scheduledId = scheduledId;
    }

	public Long getPracticeId() {
		return practiceId;
	}

	public void setPracticeId(Long practiceId) {
		this.practiceId = practiceId;
	}

	

	public Long getOrgId() {
		return orgId;
	}

	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}

	public String getOriginalName() {
		return originalName;
	}

	public void setOriginalName(String originalName) {
		this.originalName = originalName;
	}

	public String getAliasName() {
		return aliasName;
	}

	public void setAliasName(String aliasName) {
		this.aliasName = aliasName;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}
    
}