package com.hys.zyy.manage.model;

import java.io.Serializable;

public class ScheduleCourseInfoDetailVO implements Serializable {
	
	

	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	/**
     * 课程id
     */
    private Long courseId;
    private String  courseName;

    /**
     * 开始周
     */
    private Integer startWeek;

    /**
     * 结束周
     */
    private Integer endWeek;

	public Long getCourseId() {
		return courseId;
	}

	public void setCourseId(Long courseId) {
		this.courseId = courseId;
	}

	public Integer getStartWeek() {
		return startWeek;
	}

	public void setStartWeek(Integer startWeek) {
		this.startWeek = startWeek;
	}

	public Integer getEndWeek() {
		return endWeek;
	}

	public void setEndWeek(Integer endWeek) {
		this.endWeek = endWeek;
	}

	
	public String getCourseName() {
		return courseName;
	}

	public void setCourseName(String courseName) {
		this.courseName = courseName;
	}

	@Override
	public String toString() {
		return "ScheduleCourseInfoDetailVO [courseId=" + courseId
				+ ", startWeek=" + startWeek + ", endWeek=" + endWeek + "]";
	}
	
	

}
