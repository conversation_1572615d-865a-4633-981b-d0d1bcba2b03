package com.hys.zyy.manage.model;

import java.util.ArrayList;
import java.util.List;

public class ScheduleCourseInfoVO  implements java.io.Serializable{
	

	/**
	 * 
	 */
	private static final long serialVersionUID = 4911947968806402953L;

	/**
     * 课程表id
     */
    private Long scheduleId;

    /**
     * 班级：一班
     */
    private String classNum;

    /**
     * 星期几的key:1~7
     */
    private Integer weekDay;

    /**
     * 中文星期几
     */
    private String weekDayChinese;

    /**
     * 第几节课id
     */
    private Long pitchId;
    
    /**
     *课节:第一节
     */
    private Integer pitchNumber;

    /**
     * 课程key
     */
    private String courseKey;
    
    private String pitchNumberChinese;
    
    private String className;
    
    private List<ScheduleCourseInfoDetailVO>  courseList = new ArrayList<ScheduleCourseInfoDetailVO>();
	public Long getScheduleId() {
		return scheduleId;
	}
	public void setScheduleId(Long scheduleId) {
		this.scheduleId = scheduleId;
	}
	public String getClassNum() {
		return classNum;
	}
	public void setClassNum(String classNum) {
		this.classNum = classNum;
	}
	
	public Integer getWeekDay() {
		return weekDay;
	}
	public void setWeekDay(Integer weekDay) {
		this.weekDay = weekDay;
	}
	public String getWeekDayChinese() {
		return weekDayChinese;
	}
	public void setWeekDayChinese(String weekDayChinese) {
		this.weekDayChinese = weekDayChinese;
	}
	public Long getPitchId() {
		return pitchId;
	}
	public void setPitchId(Long pitchId) {
		this.pitchId = pitchId;
	}
	public String getCourseKey() {
		return courseKey;
	}
	public void setCourseKey(String courseKey) {
		this.courseKey = courseKey;
	}
	
	public List<ScheduleCourseInfoDetailVO> getCourseList() {
		return courseList;
	}
	public void setCourseList(List<ScheduleCourseInfoDetailVO> courseList) {
		this.courseList = courseList;
	}
	
	public String getPitchNumberChinese() {
		return pitchNumberChinese;
	}
	public void setPitchNumberChinese(String pitchNumberChinese) {
		this.pitchNumberChinese = pitchNumberChinese;
	}
	public String getClassName() {
		return className;
	}
	public void setClassName(String className) {
		this.className = className;
	}
	public Integer getPitchNumber() {
		return pitchNumber;
	}

	public void setPitchNumber(Integer pitchNumber) {
		this.pitchNumber = pitchNumber;
	}
	@Override
	public String toString() {
		return "ScheduleCourseInfoVO [scheduleId=" + scheduleId + ", classNum="
				+ classNum + ", weekDay=" + weekDay + ", weekDayChinese="
				+ weekDayChinese + ", pitchId=" + pitchId + ", courseKey="
				+ courseKey + ", courseList=" + courseList + "]";
	}
	

}
