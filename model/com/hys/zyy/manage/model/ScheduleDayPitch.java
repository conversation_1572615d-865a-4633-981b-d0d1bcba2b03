package com.hys.zyy.manage.model;

import java.util.Date;

import org.apache.commons.lang.StringUtils;

import com.hys.zyy.manage.util.DateUtil;

/**
 * 课表具体每节课
 * 
 * <AUTHOR>
 * 
 * @date 2019-06-12
 */
public class ScheduleDayPitch {
    /**
     * id
     */
    private Long id;

    /**
     * 课程名称
     */
    private String courseName;

    /**
     * 课程id(原型有的叫学科，一个意思)
     */
    private Long courseId;
    
    /**
     * 课表id_班级id_课节id_上课日期: 1_1_1_2019-05-20
     */
    private String courseKey;

    /**
     * 上课时间（单位：天）
     */
    private Date classDay;
    
    
    /**
     * 上课时间（单位：天） 字符串
     */
    private String classDayStr;

    /**
     * 第几周
     */
    private Integer weekNum;
    
    private Integer weekDay;
    
    private String weekStr;//星期一

    /**
     * 创建人
     */
    private Long createUserid;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 状态
     */
    private Integer status;

    /**
     *最后更新时间
     */
    private Date lastModifyTime;

    /**
     * 课表ID
     */
    private Long scheduleId;

    /**
     *课节:第一节
     */
    private Integer pitchNumber;
    /**
     * 组合课节
     */
    private String pitchStr;

    /**
     * 节数ID
     */
    private Long pitchId;

    /**
     * class_num 授课班次
     */
    private String classNum;

    /**
     * 上课地点
     */
    private String classAdd;

    /**
     * 科室ID（住院医的实际科室zyy_dept表）
     */
    private Long deptId;
    
    private String deptName;//科室名称 

    /**
     * 授课老师(住院医的带教)
     */
    private Long teacherId;
    
    private String teacherName;

    /**
     * 授课内容
     */
    private String courseContent;

    /**
     * 学时（数字）
     */
    private String learnHour;

    /**
     * 对应的二维码地址
     */
    private String qrCodeUrl;

    /**
     * 签到人数（实时更新）
     */
    private Integer comeNumber;
    
    private String major;//专业，表schedule.major
    
    private Integer signSum;//签到人数
    
    
    private Integer classSum;//班级人数
    
    private Integer attachmentNum;//附件数量
    
    private String pitchTime;//课节时间段
    private String pitchTimeSection;
    
    private String isShowQRcode;//是否显示课节签到二维码
    
    private String yearName;
    
    private Integer nowStatus;
    
    
    private Integer stype;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCourseName() {
        return courseName;
    }

    public void setCourseName(String courseName) {
        this.courseName = courseName == null ? null : courseName.trim();
    }

    public Long getCourseId() {
        return courseId;
    }

    public void setCourseId(Long courseId) {
        this.courseId = courseId;
    }

    
    public String getCourseKey() {
		return courseKey;
	}

	public void setCourseKey(String courseKey) {
		this.courseKey = courseKey;
	}

	public Date getClassDay() {
        return classDay;
    }

    public void setClassDay(Date classDay) {
        this.classDay = classDay;
    }

    public Integer getWeekNum() {
        return weekNum;
    }

    public void setWeekNum(Integer weekNum) {
        this.weekNum = weekNum;
    }

    public Long getCreateUserid() {
        return createUserid;
    }

    public void setCreateUserid(Long createUserid) {
        this.createUserid = createUserid;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getLastModifyTime() {
        return lastModifyTime;
    }

    public void setLastModifyTime(Date lastModifyTime) {
        this.lastModifyTime = lastModifyTime;
    }

    public Long getScheduleId() {
        return scheduleId;
    }

    public void setScheduleId(Long scheduleId) {
        this.scheduleId = scheduleId;
    }

    public Integer getPitchNumber() {
        return pitchNumber;
    }

    public void setPitchNumber(Integer pitchNumber) {
        this.pitchNumber = pitchNumber;
    }

    public Long getPitchId() {
        return pitchId;
    }

    public void setPitchId(Long pitchId) {
        this.pitchId = pitchId;
    }

    public String getClassNum() {
        return classNum;
    }

    public void setClassNum(String classNum) {
        this.classNum = classNum == null ? null : classNum.trim();
    }

    public String getClassAdd() {
        return classAdd;
    }

    public void setClassAdd(String classAdd) {
        this.classAdd = classAdd == null ? null : classAdd.trim();
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public Long getTeacherId() {
        return teacherId;
    }

    public void setTeacherId(Long teacherId) {
        this.teacherId = teacherId;
    }

    public String getCourseContent() {
        return courseContent;
    }

    public void setCourseContent(String courseContent) {
        this.courseContent = courseContent == null ? null : courseContent.trim();
    }

    public String getLearnHour() {
        return learnHour;
    }

    public void setLearnHour(String learnHour) {
        this.learnHour = learnHour;
    }

    public String getQrCodeUrl() {
        return qrCodeUrl;
    }

    public void setQrCodeUrl(String qrCodeUrl) {
        this.qrCodeUrl = qrCodeUrl == null ? null : qrCodeUrl.trim();
    }

    public Integer getComeNumber() {
        return comeNumber;
    }

    public void setComeNumber(Integer comeNumber) {
        this.comeNumber = comeNumber;
    }

	public String getClassDayStr() {
		return classDayStr;
	}

	public void setClassDayStr(String classDayStr) {
		this.classDayStr = classDayStr;
	}

	public Integer getWeekDay() {
		return weekDay;
	}

	public void setWeekDay(Integer weekDay) {
		this.weekDay = weekDay;
	}
	
	
	

	public String getWeekStr() {
		return weekStr;
	}

	public void setWeekStr(String weekStr) {
		this.weekStr = weekStr;
	}

	public String getPitchStr() {
		return pitchStr;
	}

	public void setPitchStr(String pitchStr) {
		this.pitchStr = pitchStr;
	}

	public String getDeptName() {
		return deptName;
	}

	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}
	
	public String getTeacherName() {
		return teacherName;
	}

	public void setTeacherName(String teacherName) {
		this.teacherName = teacherName;
	}

	
	public String getMajor() {
		return major;
	}

	public void setMajor(String major) {
		this.major = major;
	}
	

	public Integer getSignSum() {
		return signSum;
	}

	public void setSignSum(Integer signSum) {
		this.signSum = signSum;
	}

	
	public Integer getClassSum() {
		return classSum;
	}

	public void setClassSum(Integer classSum) {
		this.classSum = classSum;
	}

	public Integer getAttachmentNum() {
		return attachmentNum;
	}

	public void setAttachmentNum(Integer attachmentNum) {
		this.attachmentNum = attachmentNum;
	}

	public String getPitchTime() {
		return pitchTime;
	}

	public void setPitchTime(String pitchTime) {
		this.pitchTime = pitchTime;
	}
	

	public String getIsShowQRcode() {
		return isShowQRcode;
	}

	public void setIsShowQRcode(String isShowQRcode) {
		this.isShowQRcode = isShowQRcode;
	}

	@Override
	public String toString() {
		return "ScheduleDayPitch [id=" + id + ", courseName=" + courseName
				+ ", courseId=" + courseId + ", classDay=" + classDay
				+ ", classDayStr=" + classDayStr + ", weekNum=" + weekNum
				+ ", weekDay=" + weekDay + ", createUserid=" + createUserid
				+ ", createTime=" + createTime + ", status=" + status
				+ ", lastModifyTime=" + lastModifyTime + ", scheduleId="
				+ scheduleId + ", pitchNumber=" + pitchNumber + ", pitchId="
				+ pitchId + ", classNum=" + classNum + ", classAdd=" + classAdd
				+ ", deptId=" + deptId + ", teacherId=" + teacherId
				+ ", courseContent=" + courseContent + ", learnHour="
				+ learnHour + ", qrCodeUrl=" + qrCodeUrl + ", comeNumber="
				+ comeNumber + "]";
	}

	public String getYearName() {
		return yearName;
	}

	public void setYearName(String yearName) {
		this.yearName = yearName;
	}

	public Integer getNowStatus() {
		if(getPitchTime()!=null){
			Date startTime = DateUtil.parse(getClassDayStr()+" "+getPitchTime().split("-")[0],DateUtil.FORMAT_MINUTES);
			Date endTime = DateUtil.parse(getClassDayStr()+" "+getPitchTime().split("-")[1],DateUtil.FORMAT_MINUTES);
			Date nowTime = DateUtil.parse(new Date(),DateUtil.FORMAT_MINUTES);
			//1灰色 代表过去的，8红色  代表当天的， 6绿色 代表之后的 
			if(DateUtil.compareTo(endTime, nowTime)<0){
				nowStatus = 1;
			}else if(DateUtil.between(nowTime, startTime, endTime)){
				nowStatus = 8;
			}if(DateUtil.compareTo(startTime, nowTime)>0){
				nowStatus = 6;
			}
		}
		return nowStatus;
	}

	public Integer getStype() {
		return stype;
	}

	public void setStype(Integer stype) {
		this.stype = stype;
	}

	public String getPitchTimeSection() {
		return pitchTimeSection;
	}

	public void setPitchTimeSection(String pitchTimeSection) {
		this.pitchTimeSection = pitchTimeSection;
	}

	public void setPitchTimeMeRge(){
		if(StringUtils.isNotBlank(pitchTimeSection)){
			String[] arr = pitchTimeSection.split(",");
			String startTime = "";
			String endTime = "";
			for (String string : arr) {
				String[] arrT = string.split("-");
			}
		}
	}
	
}