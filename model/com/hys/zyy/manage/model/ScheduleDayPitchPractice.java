package com.hys.zyy.manage.model;

import java.util.Date;

import com.hys.zyy.manage.util.DateUtil;

/**
 * 课表具体每节课见习专用
 * 
 * <AUTHOR>
 * 
 * @date 2019-06-12
 */
public class ScheduleDayPitchPractice {
    /**
     * 
     */
    private Long id;

    /**
     * 见习时间（单位：天）
     */
    private Date practiceDay;
    
    private String practiceDayStr;

    /**
     * 对应周几
     */
    private Integer weekNum;

    /**
     * 创建人
     */
    private Long createUserid;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 最后更新时间
     */
    private Date lastModifyTime;

    /**
     *课表ID
     */
    private Long scheduleId;

    /**
     *节数
     */
    private Integer pitchNumber;
    /**
     * 组合课节
     */
    private String pitchStr;

    /**
     * 节数ID
     */
    private Long pitchId;
    
    private String pitchTime;

    /**
     * 组
     */
    private Integer groupNum;

    /**
     * 见习的科室ID（住院医的实际科室zyy_dept表）
     */
    private Long deptId;
    
    private String deptName;
    
    private String practiceKey;//页面展示td的id,取值使用的key
    
    private String groupDept;//组_科室  组合字符串
    
    private Integer signSum;//签到人数
    
    private Integer groupSum;//所在组总人数
    
    private String major;//专业
    
    private String majorGroup;//专业和组
    
    private String weekStr;
    
    private Integer attachmentNum;//附件数量
    
    private String isShowQRcode;//是否显示见习签到二维码

    private Integer comeNumber;
    
    private Integer classSum;//班级人数
    
    private String yearName;
    
    private Long dayPitchId;
    
    private Long teacherId;
    private String teacherName;
    
    
    
    private String accountName;
    
    private Integer nowStatus;
    
    
    
    
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getPracticeDay() {
        return practiceDay;
    }

    public void setPracticeDay(Date practiceDay) {
        this.practiceDay = practiceDay;
    }

    public Integer getWeekNum() {
        return weekNum;
    }

    public void setWeekNum(Integer weekNum) {
        this.weekNum = weekNum;
    }

    public Long getCreateUserid() {
        return createUserid;
    }

    public void setCreateUserid(Long createUserid) {
        this.createUserid = createUserid;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getLastModifyTime() {
        return lastModifyTime;
    }

    public void setLastModifyTime(Date lastModifyTime) {
        this.lastModifyTime = lastModifyTime;
    }

    public Long getScheduleId() {
        return scheduleId;
    }

    public void setScheduleId(Long scheduleId) {
        this.scheduleId = scheduleId;
    }

    public Integer getPitchNumber() {
        return pitchNumber;
    }

    public void setPitchNumber(Integer pitchNumber) {
        this.pitchNumber = pitchNumber;
    }

    public Long getPitchId() {
        return pitchId;
    }

    public void setPitchId(Long pitchId) {
        this.pitchId = pitchId;
    }

    public Integer getGroupNum() {
        return groupNum;
    }

    public void setGroupNum(Integer groupNum) {
        this.groupNum = groupNum;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

	public String getPracticeKey() {
		return practiceKey;
	}

	public void setPracticeKey(String practiceKey) {
		this.practiceKey = practiceKey;
	}

	public String getPitchStr() {
		return pitchStr;
	}

	public void setPitchStr(String pitchStr) {
		this.pitchStr = pitchStr;
	}

	public String getGroupDept() {
		return groupDept;
	}

	public void setGroupDept(String groupDept) {
		this.groupDept = groupDept;
	}

	public String getPracticeDayStr() {
		return practiceDayStr;
	}

	public void setPracticeDayStr(String practiceDayStr) {
		this.practiceDayStr = practiceDayStr;
	}

	public String getDeptName() {
		return deptName;
	}

	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}

	public String getPitchTime() {
		return pitchTime;
	}

	public void setPitchTime(String pitchTime) {
		this.pitchTime = pitchTime;
	}

	public Integer getSignSum() {
		return signSum;
	}

	public void setSignSum(Integer signSum) {
		this.signSum = signSum;
	}

	public String getMajorGroup() {
		return majorGroup;
	}

	public void setMajorGroup(String majorGroup) {
		this.majorGroup = majorGroup;
	}

	public String getWeekStr() {
		return weekStr;
	}

	public void setWeekStr(String weekStr) {
		this.weekStr = weekStr;
	}

	public Integer getGroupSum() {
		return groupSum;
	}

	public void setGroupSum(Integer groupSum) {
		this.groupSum = groupSum;
	}

	public String getMajor() {
		return major;
	}

	public void setMajor(String major) {
		this.major = major;
	}

	public Integer getAttachmentNum() {
		return attachmentNum;
	}

	public void setAttachmentNum(Integer attachmentNum) {
		this.attachmentNum = attachmentNum;
	}

	public String getIsShowQRcode() {
		return isShowQRcode;
	}

	public void setIsShowQRcode(String isShowQRcode) {
		this.isShowQRcode = isShowQRcode;
	}

	public Integer getComeNumber() {
		return comeNumber;
	}

	public void setComeNumber(Integer comeNumber) {
		this.comeNumber = comeNumber;
	}

	public Integer getClassSum() {
		return classSum;
	}

	public void setClassSum(Integer classSum) {
		this.classSum = classSum;
	}

	public String getYearName() {
		return yearName;
	}

	public void setYearName(String yearName) {
		this.yearName = yearName;
	}

	public Integer getNowStatus() {
		if(getPitchTime()!=null){
			Date startTime = DateUtil.parse(getPracticeDayStr()+" "+getPitchTime().split("-")[0],DateUtil.FORMAT_MINUTES);
			Date endTime = DateUtil.parse(getPracticeDayStr()+" "+getPitchTime().split("-")[1],DateUtil.FORMAT_MINUTES);
			Date nowTime = DateUtil.parse(new Date(),DateUtil.FORMAT_MINUTES);
			//1灰色 代表过去的，8红色  代表当天的， 6绿色 代表之后的 
			if(DateUtil.compareTo(endTime, nowTime)<0){
				nowStatus = 1;
			}else if(DateUtil.between(nowTime, startTime, endTime)){
				nowStatus = 8;
			}if(DateUtil.compareTo(startTime, nowTime)>0){
				nowStatus = 6;
			}
		}
		return nowStatus;
	}

	public void setNowStatus(Integer nowStatus) {
		this.nowStatus = nowStatus;
	}

	public Long getTeacherId() {
		return teacherId;
	}

	public void setTeacherId(Long teacherId) {
		this.teacherId = teacherId;
	}

	public String getTeacherName() {
		return teacherName;
	}

	public void setTeacherName(String teacherName) {
		this.teacherName = teacherName;
	}

	public Long getDayPitchId() {
		return dayPitchId;
	}

	public void setDayPitchId(Long dayPitchId) {
		this.dayPitchId = dayPitchId;
	}

	public String getAccountName() {
		return accountName;
	}

	public void setAccountName(String accountName) {
		this.accountName = accountName;
	}
	
	
    
}