package com.hys.zyy.manage.model;

import java.util.Date;

import org.apache.commons.lang.StringUtils;

import com.hys.framework.util.DateUtil;


/**
 * 
*    
* 描述：   课程评价类
* 创建人：lyc  
* 创建时间：2020-2-13 下午1:42:14   
* @version
 */
public class ScheduleEval {
	private Long id;
	private Integer type;
	
	private Long userId;//学员ID  评价者的ID
	
	
	private Long evalTeacherId;
	
	//见习的相关属性
	private String scheduleDateStr;
	private Long pitchId;
	private Long scheduleId;
	
	//查看的时候查询条件 科室 带教
	private Long deptId;
	private Long teacherId;
	
	
	
	private String practiceDayStr;
	
	private Long orgId;
	
	private Date practiceDay;
	//课程ID
	private Long dayPitchId;
	/**
     * 课程名称
     */
    private String courseName;
    /**
     * 上课时间（单位：天） 字符串
     */
    private String classDayStr;
    /**
     * class_num 授课班次
     */
    private String classNum;
    /**
     * 学员所在组
     */
    private String  groupNum; 

    /**
     * 上课地点
     */
    private String classAdd;
    /**
     * 授课内容
     */
    private String courseContent;

    /**
     * 学时（数字）
     */
    private String learnHour;
    /**
     * 签到人数（实时更新）
     */
    private Integer comeNumber;
	
	
	
	//见习预留的ID 启用
	private Long dayPitchPracticeId;
	
	//对本次授课老师评价 1 优秀教师  2 合格教师 3 待提高教师
	private Integer evalLevel;
	//本次见习时间1、两小时及两小时以上2、不足两小时3、不足1小时
	private Integer practiceTime;
	//见习或者课程的评价内容
	private String evalContent;
	
	//对带教评价的文字内容
	private String evalLevelStr;
	
	private String practiceTimeStr;
	
	//展示的字段
	private String deptName;
	private String pitchTime;//课节时间段
	private String teacherCertNo;
	private String evalUserName;//评价者
	private String evalDateStr;
	private String teacherName;//授课老师
	
	
	private String startTime;
	private String endTime;
	
	
	//查询条件具体到天！
	private String startDate;
	private String endDate;
	
	private Integer isExport;//是否导出
	
	
	
	
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public String getScheduleDateStr() {
		return scheduleDateStr;
	}

	public void setScheduleDateStr(String scheduleDateStr) {
		this.scheduleDateStr = scheduleDateStr;
	}

	public Long getPitchId() {
		return pitchId;
	}

	public void setPitchId(Long pitchId) {
		this.pitchId = pitchId;
	}

	public Long getScheduleId() {
		return scheduleId;
	}

	public void setScheduleId(Long scheduleId) {
		this.scheduleId = scheduleId;
	}

	public Long getDeptId() {
		return deptId;
	}

	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}

	public Long getDayPitchId() {
		return dayPitchId;
	}

	public void setDayPitchId(Long dayPitchId) {
		this.dayPitchId = dayPitchId;
	}

	public String getPracticeDayStr() {
		return practiceDayStr;
	}

	public void setPracticeDayStr(String practiceDayStr) {
		this.practiceDayStr = practiceDayStr;
		if(!StringUtils.isNotBlank(practiceDayStr)){
			DateUtil.parse(practiceDayStr, "yyyy-MM-dd");
		}
	}

	public Integer getEvalLevel() {
		return evalLevel;
	}

	public void setEvalLevel(Integer evalLevel) {
		this.evalLevel = evalLevel;
	}

	public Integer getPracticeTime() {
		return practiceTime;
	}

	public void setPracticeTime(Integer practiceTime) {
		this.practiceTime = practiceTime;
	}

	public String getEvalContent() {
		return evalContent;
	}

	public void setEvalContent(String evalContent) {
		this.evalContent = evalContent;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public Long getEvalTeacherId() {
		return evalTeacherId;
	}

	public void setEvalTeacherId(Long evalTeacherId) {
		this.evalTeacherId = evalTeacherId;
	}

	public Long getDayPitchPracticeId() {
		return dayPitchPracticeId;
	}

	public void setDayPitchPracticeId(Long dayPitchPracticeId) {
		this.dayPitchPracticeId = dayPitchPracticeId;
	}

	public Date getPracticeDay() {
		if(!StringUtils.isNotBlank(practiceDayStr)){
			DateUtil.parse(practiceDayStr, "yyyy-MM-dd");
		}
		return practiceDay;
	}

	public void setPracticeDay(Date practiceDay) {
		this.practiceDay = practiceDay;
	}

	public String getStartTime() {
		return startTime;
	}

	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}

	public String getEndTime() {
		return endTime;
	}

	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}

	public String getTeacherName() {
		return teacherName;
	}

	public void setTeacherName(String teacherName) {
		this.teacherName = teacherName;
	}

	public String getStartDate() {
		return startDate;
	}

	public void setStartDate(String startDate) {
		this.startDate = startDate;
	}

	public String getEndDate() {
		return endDate;
	}

	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}

	public Long getOrgId() {
		return orgId;
	}

	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}

	public String getDeptName() {
		return deptName;
	}

	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}

	public String getPitchTime() {
		return pitchTime;
	}

	public void setPitchTime(String pitchTime) {
		this.pitchTime = pitchTime;
	}

	public String getTeacherCertNo() {
		return teacherCertNo;
	}

	public void setTeacherCertNo(String teacherCertNo) {
		this.teacherCertNo = teacherCertNo;
	}

	public String getEvalUserName() {
		return evalUserName;
	}

	public void setEvalUserName(String evalUserName) {
		this.evalUserName = evalUserName;
	}

	public String getEvalDateStr() {
		return evalDateStr;
	}

	public void setEvalDateStr(String evalDateStr) {
		this.evalDateStr = evalDateStr;
	}

	public Integer getIsExport() {
		return isExport;
	}

	public void setIsExport(Integer isExport) {
		this.isExport = isExport;
	}

	//1 优秀教师  2 合格教师 3 待提高教师
	public String getEvalLevelStr() {
		if(evalLevel!=null){
			switch (evalLevel) {
			case 1:
				evalLevelStr = "A优秀教师";
				break;
			case 2:
				evalLevelStr = "B合格教师";
				break;
			case 3:
				evalLevelStr = "C 待提高教师";
				break;
			default:
				evalLevelStr = "";
				break;
			}
		}
		return evalLevelStr;
	}

	//本次见习时间1、两小时及两小时以上2、不足两小时3、不足1小时
	public String getPracticeTimeStr() {
		if(practiceTime!=null){
			switch (practiceTime) {
			case 1:
				practiceTimeStr = "两小时及两小时以上";
				break;
			case 2:
				practiceTimeStr = "不足两小时";
				break;
			case 3:
				practiceTimeStr = "不足1小时";
				break;
			default:
				practiceTimeStr = "";
				break;
			}
		}
		return practiceTimeStr;
	}

	public String getCourseName() {
		return courseName;
	}

	public void setCourseName(String courseName) {
		this.courseName = courseName;
	}

	public String getClassDayStr() {
		return classDayStr;
	}

	public void setClassDayStr(String classDayStr) {
		this.classDayStr = classDayStr;
	}

	public String getClassNum() {
		return classNum;
	}

	public void setClassNum(String classNum) {
		this.classNum = classNum;
	}

	public String getClassAdd() {
		return classAdd;
	}

	public void setClassAdd(String classAdd) {
		this.classAdd = classAdd;
	}

	public String getCourseContent() {
		return courseContent;
	}

	public void setCourseContent(String courseContent) {
		this.courseContent = courseContent;
	}

	public String getLearnHour() {
		return learnHour;
	}

	public void setLearnHour(String learnHour) {
		this.learnHour = learnHour;
	}

	public Integer getComeNumber() {
		return comeNumber;
	}

	public void setComeNumber(Integer comeNumber) {
		this.comeNumber = comeNumber;
	}

	public void setEvalLevelStr(String evalLevelStr) {
		this.evalLevelStr = evalLevelStr;
	}

	public void setPracticeTimeStr(String practiceTimeStr) {
		this.practiceTimeStr = practiceTimeStr;
	}

	public Long getTeacherId() {
		return teacherId;
	}

	public void setTeacherId(Long teacherId) {
		this.teacherId = teacherId;
	}

	public String getGroupNum() {
		return groupNum;
	}

	public void setGroupNum(String groupNum) {
		this.groupNum = groupNum;
	}
	
	
}
