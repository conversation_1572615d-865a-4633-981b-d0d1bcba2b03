package com.hys.zyy.manage.model;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.Date;

import com.hys.framework.utils.date.DateUtil;


/**
*    
* 描述：   见习评价统计对应的实体类！！！
* 创建人：lyc  
* 创建时间：2020-2-21 下午5:41:55   
* @version
 */
public class ScheduleEvalStatistics {
	
	//见习的相关属性
	private Long scheduleId;
	private Date practiceDay;
	private String practiceDayStr;
	private Date classDay;
	private String classDayStr;
	private String pitchTime;//课节时间段
	
	
	private String courseName;
	private String courseContent;
	
	//见习科室
	private String deptName;
	private String teacherCertNo;
	private String teacherName;//授课老师
	
	//对本次授课老师评价 1 优秀教师  2 合格教师 3 待提高教师
	private Integer evalLevel1Count;
	private Integer evalLevel2Count;
	private Integer evalLevel3Count;
	
	//对应的评价教师数量所占比例
	private Double evalLevel1Ratio;
	private Double evalLevel2Ratio;
	private Double evalLevel3Ratio;
	
	
	//本次见习时间1、两小时及两小时以上2、不足两小时3、不足1小时
	private Integer practiceTime1Count;
	private Integer practiceTime2Count;
	private Integer practiceTime3Count;
	
	
	//对应的本次见习时间数量所占比例
	private Double practiceTime1Ratio;
	private Double practiceTime2Ratio;
	private Double practiceTime3Ratio;
		
		
	private String evalStuCount;//填写人次
	private String signStuCount;//签到人次
	public Long getScheduleId() {
		return scheduleId;
	}
	public void setScheduleId(Long scheduleId) {
		this.scheduleId = scheduleId;
	}
	public String getPracticeDayStr() {
		return practiceDayStr;
	}
	public void setPracticeDayStr(String practiceDayStr) {
		this.practiceDayStr = practiceDayStr;
	}
	public String getPitchTime() {
		return pitchTime==null?"":pitchTime;
	}
	public void setPitchTime(String pitchTime) {
		this.pitchTime = pitchTime;
	}
	public String getDeptName() {
		return deptName;
	}
	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}
	public String getTeacherCertNo() {
		return teacherCertNo;
	}
	public void setTeacherCertNo(String teacherCertNo) {
		this.teacherCertNo = teacherCertNo;
	}
	public String getTeacherName() {
		return teacherName;
	}
	public void setTeacherName(String teacherName) {
		this.teacherName = teacherName;
	}
	public Integer getEvalLevel1Count() {
		return evalLevel1Count;
	}
	public void setEvalLevel1Count(Integer evalLevel1Count) {
		this.evalLevel1Count = evalLevel1Count;
	}
	public Integer getEvalLevel2Count() {
		return evalLevel2Count;
	}
	public void setEvalLevel2Count(Integer evalLevel2Count) {
		this.evalLevel2Count = evalLevel2Count;
	}
	public Integer getEvalLevel3Count() {
		return evalLevel3Count;
	}
	public void setEvalLevel3Count(Integer evalLevel3Count) {
		this.evalLevel3Count = evalLevel3Count;
	}
	public Integer getPracticeTime1Count() {
		return practiceTime1Count;
	}
	public void setPracticeTime1Count(Integer practiceTime1Count) {
		this.practiceTime1Count = practiceTime1Count;
	}
	public Integer getPracticeTime2Count() {
		return practiceTime2Count;
	}
	public void setPracticeTime2Count(Integer practiceTime2Count) {
		this.practiceTime2Count = practiceTime2Count;
	}
	public Integer getPracticeTime3Count() {
		return practiceTime3Count;
	}
	public void setPracticeTime3Count(Integer practiceTime3Count) {
		this.practiceTime3Count = practiceTime3Count;
	}
	public String getEvalStuCount() {
		return evalStuCount;
	}
	public void setEvalStuCount(String evalStuCount) {
		this.evalStuCount = evalStuCount;
	}
	public String getSignStuCount() {
		return signStuCount;
	}
	public void setSignStuCount(String signStuCount) {
		this.signStuCount = signStuCount;
	}
	
	
	
	
	
	
	public Double getEvalLevel1Ratio() {
		if(evalLevel1Count==0){
			evalLevel1Ratio=0d;
		}else{
			evalLevel1Ratio = div(evalLevel1Count+"", evalStuCount+"", 4).doubleValue();
		}
		return evalLevel1Ratio;
	}
	public Double getEvalLevel2Ratio() {
		if(evalLevel2Count==0){
			evalLevel2Ratio=0d;
		}else{
			evalLevel2Ratio = div(evalLevel2Count+"", evalStuCount+"", 4).doubleValue();
		}
		return evalLevel2Ratio;
	}
	public Double getEvalLevel3Ratio() {
		if(evalLevel3Count==0){
			evalLevel3Ratio=0d;
		}else{
			evalLevel3Ratio = div(evalLevel3Count+"", evalStuCount+"", 4).doubleValue();
		}
		return evalLevel3Ratio;
	}
	public Double getPracticeTime1Ratio() {
		if(practiceTime1Count==0){
			practiceTime1Ratio=0d;
		}else{
			practiceTime1Ratio = div(practiceTime1Count+"", evalStuCount+"", 4).doubleValue();
		}
		return practiceTime1Ratio;
	}
	public Double getPracticeTime2Ratio() {
		if(practiceTime2Count==0){
			practiceTime2Ratio=0d;
		}else{
			practiceTime2Ratio = div(practiceTime2Count+"", evalStuCount+"", 4).doubleValue();
		}
		return practiceTime2Ratio;
	}
	public Double getPracticeTime3Ratio() {
		if(practiceTime3Count==0){
			practiceTime3Ratio=0d;
		}else{
			practiceTime3Ratio = div(practiceTime3Count+"", evalStuCount+"", 4).doubleValue();
		}
		return practiceTime3Ratio;
	}
	
	/**
     * 提供（相对）精确的除法运算。当发生除不尽的情况时，由scale参数指
     * 定精度，以后的数字四舍五入
     *
     * @param v1    被除数
     * @param v2    除数
     * @param scale 表示需要精确到小数点以后几位
     * @return 两个参数的商
     */
    public static BigDecimal div(String v1, String v2, int scale) {
        if (scale < 0) {
            throw new IllegalArgumentException("The scale must be a positive integer or zero");
        }
        BigDecimal b1 = new BigDecimal(v1);
        BigDecimal b2 = new BigDecimal(v2);
        String str = b1.divide(b2, scale, BigDecimal.ROUND_HALF_UP).toString();
        return new BigDecimal(new DecimalFormat("#.0000").format(Double.parseDouble(str))).movePointRight(2);
    }
	
	public static void main(String[] args) {
		Double d  = div("1", "2", 3).doubleValue();
		System.out.println(div("123", "126", 4).doubleValue());
	}
	public Date getPracticeDay() {
		return practiceDay;
	}
	public void setPracticeDay(Date practiceDay) {
		this.practiceDay = practiceDay;
	}
	public Date getClassDay() {
		return classDay;
	}
	public void setClassDay(Date classDay) {
		this.classDay = classDay;
	}
	public String getClassDayStr() {
		return classDayStr;
	}
	public void setClassDayStr(String classDayStr) {
		this.classDayStr = classDayStr;
	}
	public String getCourseName() {
		return courseName;
	}
	public void setCourseName(String courseName) {
		this.courseName = courseName;
	}
	public String getCourseContent() {
		return courseContent;
	}
	public void setCourseContent(String courseContent) {
		this.courseContent = courseContent;
	}
	
	
	
	
}
