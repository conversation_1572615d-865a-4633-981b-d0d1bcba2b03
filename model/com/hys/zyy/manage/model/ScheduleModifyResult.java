package com.hys.zyy.manage.model;

import java.util.List;
import java.util.Map;

public class ScheduleModifyResult {
	
	private Long scheduleId;//课程表id
	
	private Long yearId;
	
	private String yearName;//年度
	
	private Integer rowspan1;//第一、二列合并行数，：班数*课节数
	
	private Integer rowspan3;//第三列合并，每个班的课节数
	
	private  String major;//专业
	
	private Integer classNum;//班级个数
	
	private String className;//班级名称：查询课程有人员条件的，只显示所在班级课程表
	
	private Integer pitchNum;//课节个数
	
	private  Map<String,String>  map;//每节课的内容
	
	private Map<String,String> practiceMap;//见习数据
	
	private List<String> dateList;
	

	public List<String> getDateList() {
		return dateList;
	}

	public void setDateList(List<String> dateList) {
		this.dateList = dateList;
	}

	public Long getScheduleId() {
		return scheduleId;
	}

	public void setScheduleId(Long scheduleId) {
		this.scheduleId = scheduleId;
	}

	public Long getYearId() {
		return yearId;
	}

	public void setYearId(Long yearId) {
		this.yearId = yearId;
	}

	public String getYearName() {
		return yearName;
	}

	public void setYearName(String yearName) {
		this.yearName = yearName;
	}

	public Integer getRowspan1() {
		return rowspan1;
	}

	public void setRowspan1(Integer rowspan1) {
		this.rowspan1 = rowspan1;
	}

	public Integer getRowspan3() {
		return rowspan3;
	}

	public void setRowspan3(Integer rowspan3) {
		this.rowspan3 = rowspan3;
	}

	public String getMajor() {
		return major;
	}

	public void setMajor(String major) {
		this.major = major;
	}

	public Integer getClassNum() {
		return classNum;
	}

	public void setClassNum(Integer classNum) {
		this.classNum = classNum;
	}

	public Integer getPitchNum() {
		return pitchNum;
	}

	public void setPitchNum(Integer pitchNum) {
		this.pitchNum = pitchNum;
	}

	public Map<String, String> getMap() {
		return map;
	}

	public void setMap(Map<String, String> map) {
		this.map = map;
	}

	public String getClassName() {
		return className;
	}

	public void setClassName(String className) {
		this.className = className;
	}

	public Map<String, String> getPracticeMap() {
		return practiceMap;
	}

	public void setPracticeMap(Map<String, String> practiceMap) {
		this.practiceMap = practiceMap;
	}
	
	
	
	
	
	

}
