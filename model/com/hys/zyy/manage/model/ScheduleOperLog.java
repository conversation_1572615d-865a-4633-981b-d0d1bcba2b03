package com.hys.zyy.manage.model;

import java.util.Date;

/**
 * 课表操作日志表
 * 
 * <AUTHOR>
 * 
 * @date 2019-06-12
 */
public class ScheduleOperLog {
    /**
     * 涉及到的表(逗号分隔)
     */
    private Long id;

    /**
     * 涉及到的表(逗号分隔)
     */
    private Long scheduleId;

    /**
     * 涉及到的表(逗号分隔)
     */
    private Long createUserid;

    /**
     * 涉及到的表(逗号分隔)
     */
    private Date createTime;

    /**
     * 涉及到的表(逗号分隔)
     */
    private String operDesc;

    /**
     * 涉及到的表(逗号分隔)
     */
    private Short operType;

    /**
     * 涉及到的表(逗号分隔)
     */
    private Date operTable;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getScheduleId() {
        return scheduleId;
    }

    public void setScheduleId(Long scheduleId) {
        this.scheduleId = scheduleId;
    }

    public Long getCreateUserid() {
        return createUserid;
    }

    public void setCreateUserid(Long createUserid) {
        this.createUserid = createUserid;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getOperDesc() {
        return operDesc;
    }

    public void setOperDesc(String operDesc) {
        this.operDesc = operDesc == null ? null : operDesc.trim();
    }

    public Short getOperType() {
        return operType;
    }

    public void setOperType(Short operType) {
        this.operType = operType;
    }

    public Date getOperTable() {
        return operTable;
    }

    public void setOperTable(Date operTable) {
        this.operTable = operTable;
    }
}