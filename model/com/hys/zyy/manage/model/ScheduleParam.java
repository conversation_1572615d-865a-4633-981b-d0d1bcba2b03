package com.hys.zyy.manage.model;

import java.io.Serializable;

public class ScheduleParam implements Serializable{
	
	

	/**
	 * 
	 */
	private static final long serialVersionUID = 5053110180696522436L;

	private String year;
	
	private Long yearId;
	
	private String major;//专业
	
	private String startTime;
	private String endTime;
	
	private Long sid;
	
	private Long userId;
	
	private Long dayPtichId;//课ID
	
	private String userName;
	
	private String classNum;//第一班，二班
	
	private Long orgId;//医院id
	
	private Long deptId;//科室id
	
	private String teacherName;//授课老师
	private Long teacherId;//带教老师id
	
	private Long practiceId;//见习id
	private String classDate;//上课时间
	private Integer pitchNumber;
	private Integer type; //附件类型：1见习2课节
	
	private Long provinceId;//省id
	
	private String roleType;//人员角色类型11带教
	
	private String  groupNum;//学员所在组
	
	
	private String startDate;
	private String endDate;
	
	
	private Integer evalType;//评价类型 1、待评价2、已评价
	
	
	private String practiceDate;
	private Long scheduleId;
	
	public String getClassDate() {
		return classDate;
	}
	public void setClassDate(String classDate) {
		this.classDate = classDate;
	}
	public String getYear() {
		return year;
	}
	public void setYear(String year) {
		this.year = year;
	}
	public String getMajor() {
		return major;
	}
	public void setMajor(String major) {
		this.major = major;
	}
	public String getStartTime() {
		return startTime;
	}
	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}
	public String getEndTime() {
		return endTime;
	}
	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}
	public Long getSid() {
		return sid;
	}
	public void setSid(Long sid) {
		this.sid = sid;
	}
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	public Long getYearId() {
		return yearId;
	}
	public void setYearId(Long yearId) {
		this.yearId = yearId;
	}
	public String getUserName() {
		return userName;
	}
	public void setUserName(String userName) {
		this.userName = userName;
	}
	public String getClassNum() {
		return classNum;
	}
	public void setClassNum(String classNum) {
		this.classNum = classNum;
	}
	public Long getOrgId() {
		return orgId;
	}
	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}
	public Long getDeptId() {
		return deptId;
	}
	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}
	
	public String getTeacherName() {
		return teacherName;
	}
	public void setTeacherName(String teacherName) {
		this.teacherName = teacherName;
	}
	public Long getTeacherId() {
		return teacherId;
	}
	public void setTeacherId(Long teacherId) {
		this.teacherId = teacherId;
	}
	public Long getPracticeId() {
		return practiceId;
	}
	public void setPracticeId(Long practiceId) {
		this.practiceId = practiceId;
	}
	public Integer getPitchNumber() {
		return pitchNumber;
	}
	public void setPitchNumber(Integer pitchNumber) {
		this.pitchNumber = pitchNumber;
	}
	public Integer getType() {
		return type;
	}
	public void setType(Integer type) {
		this.type = type;
	}
	public Long getProvinceId() {
		return provinceId;
	}
	public void setProvinceId(Long provinceId) {
		this.provinceId = provinceId;
	}
	public String getRoleType() {
		return roleType;
	}
	public void setRoleType(String roleType) {
		this.roleType = roleType;
	}
	public String getGroupNum() {
		return groupNum;
	}
	public void setGroupNum(String groupNum) {
		this.groupNum = groupNum;
	}
	public String getStartDate() {
		return startDate;
	}
	public void setStartDate(String startDate) {
		this.startDate = startDate;
	}
	public String getEndDate() {
		return endDate;
	}
	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}
	public Integer getEvalType() {
		return evalType;
	}
	public void setEvalType(Integer evalType) {
		this.evalType = evalType;
	}
	public Long getDayPtichId() {
		return dayPtichId;
	}
	public void setDayPtichId(Long dayPtichId) {
		this.dayPtichId = dayPtichId;
	}
	public String getPracticeDate() {
		return practiceDate;
	}
	public void setPracticeDate(String practiceDate) {
		this.practiceDate = practiceDate;
	}
	public Long getScheduleId() {
		return scheduleId;
	}
	public void setScheduleId(Long scheduleId) {
		this.scheduleId = scheduleId;
	}
		
	

}
