package com.hys.zyy.manage.model;

import java.util.Date;

/**
 * 课表节数
 * 
 * <AUTHOR>
 * 
 * @date 2019-06-12
 */
public class SchedulePitch {
    /**
     * id
     */
    private Long id;

    /**
     * id
     */
    private Integer pitchNumber;

    /**
     * id
     */
    private String pitchNumberChinese;

    /**
     * id
     */
    private String startTime;

    /**
     * id
     */
    private String endTime;

    /**
     * id
     */
    private Long createUserid;

    /**
     * id
     */
    private Date createTime;

    /**
     * id
     */
    private Integer status;

    /**
     * id
     */
    private Date lastModifyTime;

    /**
     * id
     */
    private Long scheduleId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getPitchNumber() {
        return pitchNumber;
    }

    public void setPitchNumber(Integer pitchNumber) {
        this.pitchNumber = pitchNumber;
    }

    public String getPitchNumberChinese() {
        return pitchNumberChinese;
    }

    public void setPitchNumberChinese(String pitchNumberChinese) {
        this.pitchNumberChinese = pitchNumberChinese == null ? null : pitchNumberChinese.trim();
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime == null ? null : startTime.trim();
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime == null ? null : endTime.trim();
    }

    public Long getCreateUserid() {
        return createUserid;
    }

    public void setCreateUserid(Long createUserid) {
        this.createUserid = createUserid;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getLastModifyTime() {
        return lastModifyTime;
    }

    public void setLastModifyTime(Date lastModifyTime) {
        this.lastModifyTime = lastModifyTime;
    }

    public Long getScheduleId() {
        return scheduleId;
    }

    public void setScheduleId(Long scheduleId) {
        this.scheduleId = scheduleId;
    }
}