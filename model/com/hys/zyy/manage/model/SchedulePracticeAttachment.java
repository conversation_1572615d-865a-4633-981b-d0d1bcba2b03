package com.hys.zyy.manage.model;

import java.util.Date;

/**
 * 见习附件
 * 
 * <AUTHOR>
 * 
 * @date 2019-06-12
 */
public class SchedulePracticeAttachment {
    /**
     * id
     */
    private Long id;

    /**
     * 见习日期
     */
    private Date practiceDay;

    /**
     * 第几周
     */
    private Integer weekNum;

    /**
     * 创建人
     */
    private Long createUserid;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 最后更新时间
     */
    private Date lastModifyTime;

    /**
     * 课节：第二节
     */
    private Integer pitchNumber;

    /**
     * 课节id
     */
    private Long pitchId;

    /**
     * 科室id
     */
    private Long deptId;

    /**
     * 见习id
     */
    private String practiceId;

    /**
     * 附件路径
     */
    private Integer filePath;
    /**
     * 课程表id
     */
    private Long scheduleId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getPracticeDay() {
        return practiceDay;
    }

    public void setPracticeDay(Date practiceDay) {
        this.practiceDay = practiceDay;
    }

    public Integer getWeekNum() {
        return weekNum;
    }

    public void setWeekNum(Integer weekNum) {
        this.weekNum = weekNum;
    }

    public Long getCreateUserid() {
        return createUserid;
    }

    public void setCreateUserid(Long createUserid) {
        this.createUserid = createUserid;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getLastModifyTime() {
        return lastModifyTime;
    }

    public void setLastModifyTime(Date lastModifyTime) {
        this.lastModifyTime = lastModifyTime;
    }

    public Integer getPitchNumber() {
        return pitchNumber;
    }

    public void setPitchNumber(Integer pitchNumber) {
        this.pitchNumber = pitchNumber;
    }

    public Long getPitchId() {
        return pitchId;
    }

    public void setPitchId(Long pitchId) {
        this.pitchId = pitchId;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public String getPracticeId() {
        return practiceId;
    }

    public void setPracticeId(String practiceId) {
        this.practiceId = practiceId == null ? null : practiceId.trim();
    }

    public Integer getFilePath() {
        return filePath;
    }

    public void setFilePath(Integer filePath) {
        this.filePath = filePath;
    }

	public Long getScheduleId() {
		return scheduleId;
	}

	public void setScheduleId(Long scheduleId) {
		this.scheduleId = scheduleId;
	}
    
}