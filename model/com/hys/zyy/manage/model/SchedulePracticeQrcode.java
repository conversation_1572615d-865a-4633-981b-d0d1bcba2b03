package com.hys.zyy.manage.model;

import java.util.Date;

/**
 * 见习签到二维码
 * 
 * <AUTHOR>
 * 
 * @date 2019-06-12
 */
public class SchedulePracticeQrcode {
    /**
     * 签到人数(每次签到成功加1)
     */
    private Long id;

    /**
     * 签到人数(每次签到成功加1)
     */
    private Date practiceDay;

    /**
     * 签到人数(每次签到成功加1)
     */
    private Short weekNum;

    /**
     * 签到人数(每次签到成功加1)
     */
    private Long createUserid;

    /**
     * 签到人数(每次签到成功加1)
     */
    private Date createTime;

    /**
     * 签到人数(每次签到成功加1)
     */
    private Short status;

    /**
     * 签到人数(每次签到成功加1)
     */
    private Date lastModifyTime;

    /**
     * 签到人数(每次签到成功加1)
     */
    private Short pitchNumber;

    /**
     * 签到人数(每次签到成功加1)
     */
    private Long pitchId;

    /**
     * 签到人数(每次签到成功加1)
     */
    private Long deptId;

    /**
     * 签到人数(每次签到成功加1)
     */
    private String qrCodeUrl;

    /**
     * 签到人数(每次签到成功加1)
     */
    private Short comeNumber;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getPracticeDay() {
        return practiceDay;
    }

    public void setPracticeDay(Date practiceDay) {
        this.practiceDay = practiceDay;
    }

    public Short getWeekNum() {
        return weekNum;
    }

    public void setWeekNum(Short weekNum) {
        this.weekNum = weekNum;
    }

    public Long getCreateUserid() {
        return createUserid;
    }

    public void setCreateUserid(Long createUserid) {
        this.createUserid = createUserid;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Short getStatus() {
        return status;
    }

    public void setStatus(Short status) {
        this.status = status;
    }

    public Date getLastModifyTime() {
        return lastModifyTime;
    }

    public void setLastModifyTime(Date lastModifyTime) {
        this.lastModifyTime = lastModifyTime;
    }

    public Short getPitchNumber() {
        return pitchNumber;
    }

    public void setPitchNumber(Short pitchNumber) {
        this.pitchNumber = pitchNumber;
    }

    public Long getPitchId() {
        return pitchId;
    }

    public void setPitchId(Long pitchId) {
        this.pitchId = pitchId;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public String getQrCodeUrl() {
        return qrCodeUrl;
    }

    public void setQrCodeUrl(String qrCodeUrl) {
        this.qrCodeUrl = qrCodeUrl == null ? null : qrCodeUrl.trim();
    }

    public Short getComeNumber() {
        return comeNumber;
    }

    public void setComeNumber(Short comeNumber) {
        this.comeNumber = comeNumber;
    }
}