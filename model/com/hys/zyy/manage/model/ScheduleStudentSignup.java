package com.hys.zyy.manage.model;

import java.util.Date;

/**
 * 学员签到表
 * 
 * <AUTHOR>
 * 
 * @date 2019-06-12
 */
public class ScheduleStudentSignup {
    /**
     * 签到类型(1课程2见习3其他)
     */
    private Long id;

    /**
     * 学员id
     */
    private Long userId;
    
    /**
     * 用户名称
     */
    private String realName;

    /**
     * 签到时间
     */
    private Date signTime;

    /**
     * 签到状态(1成功0失败)
     */
    private Integer status;

    /**
     * 二维码对应的url
     */
    private String qrcodeUrl;

    /**
     * 签到类型(1见习2课程3其他)
     */
    private Integer signType;

    private Long pitchId;//课节id
    
    private Long scheduleId;//课程表id
    private Long dayPtichId;//课表具体某天某节课id
    
    private Long orgId;//医院id
    
    private String openId;//微信用户id
    
    private String major;//专业
    
    private String certificateNo;//证件号码
    private String sex;//性别1男，2女
    private String userName;//用户名，微信签到使用
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Date getSignTime() {
        return signTime;
    }

    public void setSignTime(Date signTime) {
        this.signTime = signTime;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getQrcodeUrl() {
        return qrcodeUrl;
    }

    public void setQrcodeUrl(String qrcodeUrl) {
        this.qrcodeUrl = qrcodeUrl == null ? null : qrcodeUrl.trim();
    }

    public Integer getSignType() {
        return signType;
    }

    public void setSignType(Integer signType) {
        this.signType = signType;
    }

	public Long getPitchId() {
		return pitchId;
	}

	public void setPitchId(Long pitchId) {
		this.pitchId = pitchId;
	}

	public Long getScheduleId() {
		return scheduleId;
	}

	public void setScheduleId(Long scheduleId) {
		this.scheduleId = scheduleId;
	}

	public Long getOrgId() {
		return orgId;
	}

	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}

	public String getOpenId() {
		return openId;
	}

	public void setOpenId(String openId) {
		this.openId = openId;
	}

	public String getRealName() {
		return realName;
	}

	public void setRealName(String realName) {
		this.realName = realName;
	}
	public String getMajor() {
		return major;
	}
	public void setMajor(String major) {
		this.major = major;
	}

	public String getCertificateNo() {
		return certificateNo;
	}

	public void setCertificateNo(String certificateNo) {
		this.certificateNo = certificateNo;
	}

	public String getSex() {
		return sex;
	}

	public void setSex(String sex) {
		this.sex = sex;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public Long getDayPtichId() {
		return dayPtichId;
	}

	public void setDayPtichId(Long dayPtichId) {
		this.dayPtichId = dayPtichId;
	}
	
	
    
}