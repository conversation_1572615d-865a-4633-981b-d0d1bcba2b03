package com.hys.zyy.manage.model;

import java.util.Date;

public class ScheduleWeekDate {
    /**
     * 
     */
    private Long id;

    /**
     * 课程表id
     */
    private Long scheduleId;

    /**
     * 第几周
     */
    private Integer weekNum;

    /**
     * 当前周的开始日期
     */
    private Date startDate;

    /**
     * 当前周的结束日期
     */
    private Date endDate;

    /**
     * 开始日期是周几:1~7
     */
    private Integer starWeekDate;

    /**
     * 结束日期是周几:1~7
     */
    private Integer endWeekDate;

    /**
     * 当前周总计几天:一般是7天，第一周或最后一周可能不是7天
     */
    private Integer weekSumday;

    /**
     * 创建人
     */
    private Long createUserid;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 状态
     */
    private Integer status;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getScheduleId() {
        return scheduleId;
    }

    public void setScheduleId(Long scheduleId) {
        this.scheduleId = scheduleId;
    }

    public Integer getWeekNum() {
        return weekNum;
    }

    public void setWeekNum(Integer weekNum) {
        this.weekNum = weekNum;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Integer getStarWeekDate() {
        return starWeekDate;
    }

    public void setStarWeekDate(Integer starWeekDate) {
        this.starWeekDate = starWeekDate;
    }

    public Integer getEndWeekDate() {
        return endWeekDate;
    }

    public void setEndWeekDate(Integer endWeekDate) {
        this.endWeekDate = endWeekDate;
    }

    public Integer getWeekSumday() {
        return weekSumday;
    }

    public void setWeekSumday(Integer weekSumday) {
        this.weekSumday = weekSumday;
    }

    public Long getCreateUserid() {
        return createUserid;
    }

    public void setCreateUserid(Long createUserid) {
        this.createUserid = createUserid;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}