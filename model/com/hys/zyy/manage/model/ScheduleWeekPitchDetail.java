package com.hys.zyy.manage.model;

import java.util.Date;

/**
 * 课程表周节班详情(总体规划，用于循环，还有一张每天每节课的表记录实际上课的安排)
 * 
 * <AUTHOR>
 * 
 * @date 2019-06-12
 */
public class ScheduleWeekPitchDetail {
    /**
     * 周节班Id
     */
    private Long id;

    /**
     * 周节班Id
     */
    private String courseName;

    /**
     * 周节班Id
     */
    private Long courseId;

    /**
     * 周节班Id
     */
    private Short startWeek;

    /**
     * 周节班Id
     */
    private Short endWeek;

    /**
     * 周节班Id
     */
    private Long createUserid;

    /**
     * 周节班Id
     */
    private Date createTime;

    /**
     * 周节班Id
     */
    private Short status;

    /**
     * 周节班Id
     */
    private Date lastModifyTime;

    /**
     * 周节班Id
     */
    private Long scheduleId;

    /**
     * 周节班Id
     */
    private Long weekPitchId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCourseName() {
        return courseName;
    }

    public void setCourseName(String courseName) {
        this.courseName = courseName == null ? null : courseName.trim();
    }

    public Long getCourseId() {
        return courseId;
    }

    public void setCourseId(Long courseId) {
        this.courseId = courseId;
    }

    public Short getStartWeek() {
        return startWeek;
    }

    public void setStartWeek(Short startWeek) {
        this.startWeek = startWeek;
    }

    public Short getEndWeek() {
        return endWeek;
    }

    public void setEndWeek(Short endWeek) {
        this.endWeek = endWeek;
    }

    public Long getCreateUserid() {
        return createUserid;
    }

    public void setCreateUserid(Long createUserid) {
        this.createUserid = createUserid;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Short getStatus() {
        return status;
    }

    public void setStatus(Short status) {
        this.status = status;
    }

    public Date getLastModifyTime() {
        return lastModifyTime;
    }

    public void setLastModifyTime(Date lastModifyTime) {
        this.lastModifyTime = lastModifyTime;
    }

    public Long getScheduleId() {
        return scheduleId;
    }

    public void setScheduleId(Long scheduleId) {
        this.scheduleId = scheduleId;
    }

    public Long getWeekPitchId() {
        return weekPitchId;
    }

    public void setWeekPitchId(Long weekPitchId) {
        this.weekPitchId = weekPitchId;
    }
}