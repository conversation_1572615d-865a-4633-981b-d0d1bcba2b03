package com.hys.zyy.manage.model;

/**
 * 学员报到
 * 
 * <AUTHOR> 2018-4-18
 */
public class StudentReport extends ZyyBaseObject {

	/**
	 * ID
	 */
	private Long id;
	private Long residencyId;
	/**
	 * 真实姓名
	 */
	private String realName;

	/**
	 * 证件类型 1-居民身份证 2-军官证 3-护照 4-港澳通行证 5台胞证 else-未知
	 */
	private Integer certificateType;

	/**
	 * 证件号码
	 */
	private String certificateNo;

	/**
	 * 性别 1-男 2-女 else-未知
	 */
	private Integer sex;

	/**
	 * 录取基地
	 */
	private String hospName;

	/**
	 * 录取专科
	 */
	private String baseName;

	/**
	 * 人员类型
	 * 
	 */
	private Integer residencySource;

	/**
	 * 1 -已报名(提交状态) 10 -已通知笔试 11 -已笔试 12 -已通知面试 13 -已面试 14 -已录取 20 -已淘汰 30
	 * -已通知签约 31 -已签约 32 -已毁约 40 -已通知报到 41 -已报到 42 -未报到 -1 -已经删除
	 */
	private Integer status;
	/*
	 * 是否操作学员报到（1：已操作；其他：未操作）
	 */
	private Integer state;

	/**
	 * 有无医师资格证书(1:有 0：无)
	 */
	private Integer hasCertificate;

	/**
	 * 医师资格证书
	 */
	private String certificateNumber;

	/**
	 * 移动电话
	 */
	private String mobilNumber;
	/*
	 * 所在州（市）
	 */
	private String liveProvinceName;
	/*
	 * 所在县（区）
	 */
	private String liveCityName;
	/*
	 * 工作单位
	 */
	private String workUnit;
	
	private String peixunYear;
	private Long recruitYearId;
	private Integer personDetail;
	private String personDetailStr;
	
	public Long getResidencyId() {
		return residencyId;
	}

	public void setResidencyId(Long residencyId) {
		this.residencyId = residencyId;
	}

	public Integer getPersonDetail() {
		return personDetail;
	}

	public void setPersonDetail(Integer personDetail) {
		this.personDetail = personDetail;
	}

	public String getPersonDetailStr() {
		return personDetailStr;
	}

	public void setPersonDetailStr(String personDetailStr) {
		this.personDetailStr = personDetailStr == null ? null : personDetailStr.trim();
	}

	public String getPeixunYear() {
		return peixunYear;
	}

	public void setPeixunYear(String peixunYear) {
		this.peixunYear = peixunYear == null ? null : peixunYear.trim();
	}

	public Long getRecruitYearId() {
		return recruitYearId;
	}

	public void setRecruitYearId(Long recruitYearId) {
		this.recruitYearId = recruitYearId;
	}

	public String getMobilNumber() {
		return mobilNumber;
	}

	public void setMobilNumber(String mobilNumber) {
		this.mobilNumber = mobilNumber;
	}

	public Integer getHasCertificate() {
		return hasCertificate;
	}

	public void setHasCertificate(Integer hasCertificate) {
		this.hasCertificate = hasCertificate;
	}

	public String getCertificateNumber() {
		return certificateNumber;
	}

	public void setCertificateNumber(String certificateNumber) {
		this.certificateNumber = certificateNumber;
	}

	public Integer getResidencySource() {
		return residencySource;
	}

	public void setResidencySource(Integer residencySource) {
		this.residencySource = residencySource;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getRealName() {
		return realName;
	}

	public void setRealName(String realName) {
		this.realName = realName;
	}

	public Integer getCertificateType() {
		return certificateType;
	}

	public void setCertificateType(Integer certificateType) {
		this.certificateType = certificateType;
	}

	public String getCertificateNo() {
		return certificateNo;
	}

	public void setCertificateNo(String certificateNo) {
		this.certificateNo = certificateNo;
	}

	public Integer getSex() {
		return sex;
	}

	public void setSex(Integer sex) {
		this.sex = sex;
	}

	public String getHospName() {
		return hospName;
	}

	public void setHospName(String hospName) {
		this.hospName = hospName;
	}

	public String getBaseName() {
		return baseName;
	}

	public void setBaseName(String baseName) {
		this.baseName = baseName;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}
	
	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	public String getLiveProvinceName() {
		return liveProvinceName;
	}

	public void setLiveProvinceName(String liveProvinceName) {
		this.liveProvinceName = liveProvinceName;
	}

	public String getLiveCityName() {
		return liveCityName;
	}

	public void setLiveCityName(String liveCityName) {
		this.liveCityName = liveCityName;
	}

	public String getWorkUnit() {
		return workUnit;
	}

	public void setWorkUnit(String workUnit) {
		this.workUnit = workUnit;
	}

}
