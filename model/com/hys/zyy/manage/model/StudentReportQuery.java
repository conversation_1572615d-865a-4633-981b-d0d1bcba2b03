package com.hys.zyy.manage.model;

public class StudentReportQuery extends StudentReport{
	
	/**
	 * 报考阶段ID
	 */
	private Integer stageId;
	
	/**
	 * 基地ID
	 */
	private Long willHospId;
	
	/**
	 * 专科ID
	 */
	private Long willBaseId;
	
	/**
	 * 医院类别
	 */
	private Integer category;

	
	/**
	 * 人员类别
	 */
	private Integer residencySource;
	
	/**
	 * 姓名
	 */
	private String name;
	
	
	/**
	 * 用户机构ID
	 */
	private Long zyyUserOrgId;
	
	
	/**
	 * 年度ID
	 */
	private Long yearId;
	
	public Long getYearId() {
		return yearId;
	}

	public void setYearId(Long yearId) {
		this.yearId = yearId;
	}

	public Long getZyyUserOrgId() {
		return zyyUserOrgId;
	}

	public void setZyyUserOrgId(Long zyyUserOrgId) {
		this.zyyUserOrgId = zyyUserOrgId;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Integer getResidencySource() {
		return residencySource;
	}

	public void setResidencySource(Integer residencySource) {
		this.residencySource = residencySource;
	}

	public Integer getCategory() {
		return category;
	}

	public void setCategory(Integer category) {
		this.category = category;
	}

	public Long getWillBaseId() {
		return willBaseId;
	}

	public void setWillBaseId(Long willBaseId) {
		this.willBaseId = willBaseId;
	}

	public Long getWillHospId() {
		return willHospId;
	}

	public void setWillHospId(Long willHospId) {
		this.willHospId = willHospId;
	}

	public Integer getStageId() {
		return stageId;
	}

	public void setStageId(Integer stageId) {
		this.stageId = stageId;
	}
	
	
	
}
