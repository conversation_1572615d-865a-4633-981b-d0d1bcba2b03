package com.hys.zyy.manage.model;

import java.util.List;

public class ViewCycleVO extends ZyyBaseObject{
	
	private static final long serialVersionUID = 4837821860903602524L;

	/**
	 * 医院ID
	 */
	private Long hospId ;
	
	/**
	 * 用户ID
	 */
	private Long userId ;
	
	/**
	 * 轮转科室ID
	 */
	private Long cycleDeptId;
	
	private Long deptId;
	
	/**
	 * 年份
	 */
	private String year;
	
	/**
	 * 起始时间
	 */
	private String startDate;
	/**
	 * 结束时间
	 */
	private String endDate;
	
	/**
	 * 类型
	 */
	private int type;
	
	/**
	 * 基地列表
	 */
	private	List<Long> baseList;
	
	/**
	 * 科室列表
	 */
	private List<Long> deptList;
	/**
	 * 科室对象列表
	 */
	private List<ZyyDept> zyyDeptList;
	
	/**
	 * 人名
	 */
	private String name;
	
	/**
	 * 身份证号
	 */
	private String certificateNo;
	
	/**
	 * 用户信息
	 */
	private ZyyUser zyyUser ;
	
	/**
	 * userList 
	 */
	private List<ZyyUserExtendVO> exVoList;
	
	/**
	 * 轮转科室列表
	 */
	private List<ZyyDeptVO> cycleDeptList ;
	
	/**
	 * 轮转类型 2 周 4 半月 
	 * 2012-11-08 xusq添加
	 */
	private Long cycleType;
	/**
	 * 2012-11-08 xusq添加
	 */
	private Long tableId;
	
	/**
	 * 2012-11-11 xusq添加
	 * 是否是轮转调整 1 是 其他或为空则不是
	 */
	private Integer isCycleChange;
	
	//一级科室ID
	private Long firstLevelDeptId;
	
	//一级科室名称
	private String cycleDeptName;
	/**
	 * 学员类型
	 */
	private Integer residencySource;
	/**
	 * 评价开始时间
	 */
	private Integer tableFillinStartDate;
	/**
	 * 评价结束时间
	 */
	private Integer tableFillinEndDate;
	//专业基地
	private Long baseId;

	private String deptStage;

	public String getDeptStage() {
		return deptStage;
	}

	public void setDeptStage(String deptStage) {
		this.deptStage = deptStage;
	}

	public Integer getResidencySource() {
		return residencySource;
	}

	public void setResidencySource(Integer residencySource) {
		this.residencySource = residencySource;
	}

	public List<ZyyUserExtendVO> getExVoList() {
		return exVoList;
	}

	public void setExVoList(List<ZyyUserExtendVO> exVoList) {
		this.exVoList = exVoList;
	}

	public String getYear() {
		return year;
	}

	public void setYear(String year) {
		this.year = year;
	}

	public int getType() {
		return type;
	}

	public void setType(int type) {
		this.type = type;
	}

	public List<Long> getBaseList() {
		return baseList;
	}

	public void setBaseList(List<Long> baseList) {
		this.baseList = baseList;
	}

	public List<Long> getDeptList() {
		return deptList;
	}

	public void setDeptList(List<Long> deptList) {
		this.deptList = deptList;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name == null ? null : name.trim();
	}

	public String getCertificateNo() {
		return certificateNo;
	}

	public void setCertificateNo(String certificateNo) {
		this.certificateNo = certificateNo;
	}

	public Long getHospId() {
		return hospId;
	}

	public void setHospId(Long hospId) {
		this.hospId = hospId;
	}

	public Long getCycleDeptId() {
		return cycleDeptId;
	}

	public void setCycleDeptId(Long cycleDeptId) {
		this.cycleDeptId = cycleDeptId;
	}

	public Long getDeptId() {
		return deptId;
	}

	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public List<ZyyDeptVO> getCycleDeptList() {
		return cycleDeptList;
	}

	public void setCycleDeptList(List<ZyyDeptVO> cycleDeptList) {
		this.cycleDeptList = cycleDeptList;
	}

	public ZyyUser getZyyUser() {
		return zyyUser;
	}

	public void setZyyUser(ZyyUser zyyUser) {
		this.zyyUser = zyyUser;
	}

	public String getStartDate() {
		return startDate;
	}

	public void setStartDate(String startDate) {
		this.startDate = startDate;
	}

	public String getEndDate() {
		return endDate;
	}

	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}

	public Long getCycleType() {
		return cycleType;
	}

	public void setCycleType(Long cycleType) {
		this.cycleType = cycleType;
	}

	public Long getTableId() {
		return tableId;
	}

	public void setTableId(Long tableId) {
		this.tableId = tableId;
	}

	public Integer getIsCycleChange() {
		return isCycleChange;
	}

	public void setIsCycleChange(Integer isCycleChange) {
		this.isCycleChange = isCycleChange;
	}

	public List<ZyyDept> getZyyDeptList() {
		return zyyDeptList;
	}

	public void setZyyDeptList(List<ZyyDept> zyyDeptList) {
		this.zyyDeptList = zyyDeptList;
	}

	public Long getFirstLevelDeptId() {
		return firstLevelDeptId;
	}

	public void setFirstLevelDeptId(Long firstLevelDeptId) {
		this.firstLevelDeptId = firstLevelDeptId;
	}

	public String getCycleDeptName() {
		return cycleDeptName;
	}

	public void setCycleDeptName(String cycleDeptName) {
		this.cycleDeptName = cycleDeptName;
	}

	public Integer getTableFillinStartDate() {
		return tableFillinStartDate;
	}

	public void setTableFillinStartDate(Integer tableFillinStartDate) {
		this.tableFillinStartDate = tableFillinStartDate;
	}

	public Integer getTableFillinEndDate() {
		return tableFillinEndDate;
	}

	public void setTableFillinEndDate(Integer tableFillinEndDate) {
		this.tableFillinEndDate = tableFillinEndDate;
	}

	public Long getBaseId() {
		return baseId;
	}

	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}
}
