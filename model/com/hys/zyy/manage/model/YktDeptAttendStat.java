package com.hys.zyy.manage.model;

import java.io.Serializable;

/**
 * 科室考勤统计
 */
public class YktDeptAttendStat implements Serializable {
	private static final long serialVersionUID = 1L;
	/*
	 * 科室ID
	 */
	private Long deptId;
	/*
	 * 科室名称
	 */
	private String deptName;
	/*
	 * 迟到人数
	 */
	private Integer lateNumber;
	/*
	 * 早退人数
	 */
	private Integer leaveEarlyNumber;
	/*
	 * 旷工人数
	 */
	private Integer absenteeismNumber;
	/*
	 * 请假人数
	 */
	private Integer leaveNumber;
	/*
	 * 状态（1=未操作；2=已操作；3=未上报；4=已上报；5=无需上报）
	 */
	private Integer reportState;
	/*
	 * 上报时间
	 */
	private String reportTime;

	public YktDeptAttendStat() {
		super();
	}

	public Long getDeptId() {
		return deptId;
	}

	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}

	public String getDeptName() {
		return deptName;
	}

	public void setDeptName(String deptName) {
		this.deptName = deptName == null ? null : deptName.trim();
	}

	public Integer getLateNumber() {
		return lateNumber;
	}

	public void setLateNumber(Integer lateNumber) {
		this.lateNumber = lateNumber;
	}

	public Integer getLeaveEarlyNumber() {
		return leaveEarlyNumber;
	}

	public void setLeaveEarlyNumber(Integer leaveEarlyNumber) {
		this.leaveEarlyNumber = leaveEarlyNumber;
	}

	public Integer getAbsenteeismNumber() {
		return absenteeismNumber;
	}

	public void setAbsenteeismNumber(Integer absenteeismNumber) {
		this.absenteeismNumber = absenteeismNumber;
	}

	public Integer getLeaveNumber() {
		return leaveNumber;
	}

	public void setLeaveNumber(Integer leaveNumber) {
		this.leaveNumber = leaveNumber;
	}

	public Integer getReportState() {
		return reportState;
	}

	public void setReportState(Integer reportState) {
		this.reportState = reportState;
	}
	
	public String getReportTime() {
		return reportTime;
	}

	public void setReportTime(String reportTime) {
		this.reportTime = reportTime == null ? null : reportTime.trim();
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((deptId == null) ? 0 : deptId.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		YktDeptAttendStat other = (YktDeptAttendStat) obj;
		if (deptId == null) {
			if (other.deptId != null)
				return false;
		} else if (!deptId.equals(other.deptId))
			return false;
		return true;
	}

	@Override
	public String toString() {
		return "YktDeptAttendStat [deptId=" + deptId + ", deptName=" + deptName + ", lateNumber=" + lateNumber
				+ ", leaveEarlyNumber=" + leaveEarlyNumber + ", absenteeismNumber=" + absenteeismNumber
				+ ", leaveNumber=" + leaveNumber + ", reportState=" + reportState + ", reportTime=" + reportTime + "]";
	}

}