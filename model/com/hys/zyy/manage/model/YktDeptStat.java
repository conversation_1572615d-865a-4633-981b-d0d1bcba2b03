package com.hys.zyy.manage.model;

import java.io.Serializable;
import java.util.List;

import org.codehaus.jackson.annotate.JsonIgnoreProperties;

/**
 * 科室统计
 */
@JsonIgnoreProperties({ "activityTypeStatStr", "finishEnterDeptResiNumber", "wantEnterDeptResiNumber",
	"finishPointTeacherResiNumber", "wantPointTeacherResiNumber", "finishLeaveDeptResiNumber", "finishCycleResiNumber" })
public class YktDeptStat implements Serializable {
	private static final long serialVersionUID = 6267348093646727853L;
	/*
	 * 科室ID
	 */
	private Long deptId;
	/*
	 * 科室名称
	 */
	private String deptName;
	/*
	 * 科室是否有轮转计划（0=无轮转；1=半月轮转；3=整月轮转）
	 */
	private Integer hasCycle;
	/*
	 * 科室组织教学活动次数JSON字符串
	 */
	private String activityTypeStatStr;
	/*
	 * 已入科人次
	 */
	private Integer finishEnterDeptResiNumber;
	/*
	 * 入科总人次
	 */
	private Integer wantEnterDeptResiNumber;
	/*
	 * 已指定带教人次
	 */
	private Integer finishPointTeacherResiNumber;
	/*
	 * 指定带教总人次
	 */
	private Integer wantPointTeacherResiNumber;
	/*
	 * 出科人次
	 */
	private Integer finishLeaveDeptResiNumber;
	/*
	 * 已轮转人次
	 */
	private Integer finishCycleResiNumber;
	/*
	 * 统计项
	 */
	private List<YktStatValue> stats;

	public YktDeptStat() {
		super();
	}

	public Long getDeptId() {
		return deptId;
	}

	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}

	public String getDeptName() {
		return deptName;
	}

	public void setDeptName(String deptName) {
		this.deptName = deptName == null ? null : deptName.trim();
	}

	public Integer getHasCycle() {
		return hasCycle;
	}

	public void setHasCycle(Integer hasCycle) {
		this.hasCycle = hasCycle;
	}

	public String getActivityTypeStatStr() {
		return activityTypeStatStr;
	}

	public void setActivityTypeStatStr(String activityTypeStatStr) {
		this.activityTypeStatStr = activityTypeStatStr == null ? null : activityTypeStatStr.trim();
	}

	public Integer getFinishEnterDeptResiNumber() {
		return finishEnterDeptResiNumber == null ? 0 : finishEnterDeptResiNumber;
	}

	public void setFinishEnterDeptResiNumber(Integer finishEnterDeptResiNumber) {
		this.finishEnterDeptResiNumber = finishEnterDeptResiNumber;
	}

	public Integer getWantEnterDeptResiNumber() {
		return wantEnterDeptResiNumber == null ? 0 : wantEnterDeptResiNumber;
	}

	public void setWantEnterDeptResiNumber(Integer wantEnterDeptResiNumber) {
		this.wantEnterDeptResiNumber = wantEnterDeptResiNumber;
	}

	public Integer getFinishPointTeacherResiNumber() {
		return finishPointTeacherResiNumber == null ? 0 : finishPointTeacherResiNumber;
	}

	public void setFinishPointTeacherResiNumber(Integer finishPointTeacherResiNumber) {
		this.finishPointTeacherResiNumber = finishPointTeacherResiNumber;
	}

	public Integer getWantPointTeacherResiNumber() {
		return wantPointTeacherResiNumber == null ? 0 : wantPointTeacherResiNumber;
	}

	public void setWantPointTeacherResiNumber(Integer wantPointTeacherResiNumber) {
		this.wantPointTeacherResiNumber = wantPointTeacherResiNumber;
	}

	public Integer getFinishLeaveDeptResiNumber() {
		return finishLeaveDeptResiNumber == null ? 0 : finishLeaveDeptResiNumber;
	}

	public void setFinishLeaveDeptResiNumber(Integer finishLeaveDeptResiNumber) {
		this.finishLeaveDeptResiNumber = finishLeaveDeptResiNumber;
	}

	public Integer getFinishCycleResiNumber() {
		return finishCycleResiNumber == null ? 0 : finishCycleResiNumber;
	}

	public void setFinishCycleResiNumber(Integer finishCycleResiNumber) {
		this.finishCycleResiNumber = finishCycleResiNumber;
	}

	public List<YktStatValue> getStats() {
		return stats;
	}

	public void setStats(List<YktStatValue> stats) {
		this.stats = stats;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((deptId == null) ? 0 : deptId.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		YktDeptStat other = (YktDeptStat) obj;
		if (deptId == null) {
			if (other.deptId != null)
				return false;
		} else if (!deptId.equals(other.deptId))
			return false;
		return true;
	}

	@Override
	public String toString() {
		return "YktDeptStat [deptId=" + deptId + ", deptName=" + deptName + ", hasCycle=" + hasCycle
				+ ", activityTypeStatStr=" + activityTypeStatStr + ", finishEnterDeptResiNumber="
				+ finishEnterDeptResiNumber + ", wantEnterDeptResiNumber=" + wantEnterDeptResiNumber
				+ ", finishPointTeacherResiNumber=" + finishPointTeacherResiNumber + ", wantPointTeacherResiNumber="
				+ wantPointTeacherResiNumber + ", finishLeaveDeptResiNumber=" + finishLeaveDeptResiNumber
				+ ", finishCycleResiNumber=" + finishCycleResiNumber + ", stats=" + stats + "]";
	}

}