package com.hys.zyy.manage.model;

import java.io.Serializable;
import java.util.Date;

import com.hys.zyy.manage.util.BaseModel;

/**
 * @author: HanLiBin
 * @desc: 住院医对接云课堂信息
 * @version: V1.0.0
 */
public class YktInfo extends BaseModel implements Serializable {
	private static final long serialVersionUID = 1L;
	/*
	 * ID
	 */
	private Long id;
	/*
	 * 站点名称
	 */
	private String domainName;
	/*
	 * APPID
	 */
	private String appid;
	/*
	 * 推送数据地址
	 */
	private String updateUrl;
	/*
	 * 跳转地址
	 */
	private String skipUrl;
	/*
	 * 机构/医院id
	 */
	private String orgId;
	/*
	 * 省ID
	 */
	private Long provinceId;
	/*
	 * 初始化[培训基地年级、专业、科室]接口地址
	 */
	private String initGpDictValue;
	/*
	 * 新增/修改[培训基地年级、专业、科室]接口地址
	 */
	private String syncGpDict;
	/*
	 * 年级初始化状态（0=未完成；1=已完成）
	 */
	private Integer initYearState;
	/*
	 * 年级初始化时间
	 */
	private Date initYearTime;
	/*
	 * 专业初始化状态（0=未完成；1=已完成）
	 */
	private Integer initBaseState;
	/*
	 * 专业初始化时间
	 */
	private Date initBaseTime;
	/*
	 * 科室初始化状态（0=未完成；1=已完成）
	 */
	private Integer initDeptState;
	/*
	 * 科室初始化时间
	 */
	private Date initDeptTime;
	/*
	 * 科室架构初始化接口地址
	 */
	private String initDeptTreeUrl;
	/*
	 * 科室架构初始化状态（0=未完成；1=已完成）
	 */
	private Integer initDeptTreeState;
	/*
	 * 科室架构初始化时间
	 */
	private Date initDeptTreeTime;
	/*
	 * 是否开启学员信息推送至云课堂按钮（0=否；1=是）
	 */
	private Integer residencySync;
	/*
	 * 是否开启带教信息推送至云课堂按钮（0=否；1=是）
	 */
	private Integer teacherSync;
	/*
	 * 备注
	 */
	private String remark;
	/*
	 * 状态（1=有效；-1=失效）
	 */
	private Integer state;

	private Date createTime, updateTime;
	/*
	 * 科室升级接口
	 */
	private String upGradeGpDictValue;
	/*
     * 云课堂带教机构码
     */
    private Long tOrgId;
    /*
     * 云课堂学生机构码
     */
    private Long sOrgId;

	public YktInfo() {
		super();
	}

	public YktInfo(String domainName) {
		super();
		this.domainName = domainName;
	}

	public YktInfo(Long id, Integer initDeptTreeState) {
		super();
		this.id = id;
		this.initDeptTreeState = initDeptTreeState;
	}

	public YktInfo(Long id, Integer initYearState, Integer initBaseState, Integer initDeptState) {
		super();
		this.id = id;
		this.initYearState = initYearState;
		this.initBaseState = initBaseState;
		this.initDeptState = initDeptState;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getDomainName() {
		return domainName;
	}

	public void setDomainName(String domainName) {
		this.domainName = domainName == null ? null : domainName.trim();
	}

	public String getAppid() {
		return appid;
	}

	public void setAppid(String appid) {
		this.appid = appid == null ? null : appid.trim();
	}

	public String getUpdateUrl() {
		return updateUrl;
	}

	public void setUpdateUrl(String updateUrl) {
		this.updateUrl = updateUrl == null ? null : updateUrl.trim();
	}

	public String getSkipUrl() {
		return skipUrl;
	}

	public void setSkipUrl(String skipUrl) {
		this.skipUrl = skipUrl == null ? null : skipUrl.trim();
	}

	public String getOrgId() {
		return orgId;
	}

	public void setOrgId(String orgId) {
		this.orgId = orgId == null ? null : orgId.trim();
	}

	public Long getProvinceId() {
		return provinceId;
	}

	public void setProvinceId(Long provinceId) {
		this.provinceId = provinceId;
	}

	public String getInitGpDictValue() {
		return initGpDictValue;
	}

	public void setInitGpDictValue(String initGpDictValue) {
		this.initGpDictValue = initGpDictValue == null ? null : initGpDictValue.trim();
	}

	public String getSyncGpDict() {
		return syncGpDict;
	}

	public void setSyncGpDict(String syncGpDict) {
		this.syncGpDict = syncGpDict == null ? null : syncGpDict.trim();
	}

	public Integer getInitYearState() {
		return initYearState;
	}

	public void setInitYearState(Integer initYearState) {
		this.initYearState = initYearState;
	}

	public Date getInitYearTime() {
		return initYearTime;
	}

	public void setInitYearTime(Date initYearTime) {
		this.initYearTime = initYearTime;
	}

	public Integer getInitBaseState() {
		return initBaseState;
	}

	public void setInitBaseState(Integer initBaseState) {
		this.initBaseState = initBaseState;
	}

	public Date getInitBaseTime() {
		return initBaseTime;
	}

	public void setInitBaseTime(Date initBaseTime) {
		this.initBaseTime = initBaseTime;
	}

	public Integer getInitDeptState() {
		return initDeptState;
	}

	public void setInitDeptState(Integer initDeptState) {
		this.initDeptState = initDeptState;
	}

	public Date getInitDeptTime() {
		return initDeptTime;
	}

	public void setInitDeptTime(Date initDeptTime) {
		this.initDeptTime = initDeptTime;
	}

	public String getInitDeptTreeUrl() {
		return initDeptTreeUrl;
	}

	public void setInitDeptTreeUrl(String initDeptTreeUrl) {
		this.initDeptTreeUrl = initDeptTreeUrl == null ? null : initDeptTreeUrl.trim();
	}

	public Integer getInitDeptTreeState() {
		return initDeptTreeState;
	}

	public void setInitDeptTreeState(Integer initDeptTreeState) {
		this.initDeptTreeState = initDeptTreeState;
	}

	public Date getInitDeptTreeTime() {
		return initDeptTreeTime;
	}

	public void setInitDeptTreeTime(Date initDeptTreeTime) {
		this.initDeptTreeTime = initDeptTreeTime;
	}

	public Integer getResidencySync() {
		return residencySync;
	}

	public void setResidencySync(Integer residencySync) {
		this.residencySync = residencySync;
	}

	public Integer getTeacherSync() {
		return teacherSync;
	}

	public void setTeacherSync(Integer teacherSync) {
		this.teacherSync = teacherSync;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark == null ? null : remark.trim();
	}

	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public String getUpGradeGpDictValue() {
		return upGradeGpDictValue;
	}

	public void setUpGradeGpDictValue(String upGradeGpDictValue) {
		this.upGradeGpDictValue = upGradeGpDictValue == null ? null : upGradeGpDictValue.trim();
	}
	
	public Long gettOrgId() {
		return tOrgId;
	}

	public void settOrgId(Long tOrgId) {
		this.tOrgId = tOrgId;
	}

	public Long getsOrgId() {
		return sOrgId;
	}

	public void setsOrgId(Long sOrgId) {
		this.sOrgId = sOrgId;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		YktInfo other = (YktInfo) obj;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		return true;
	}

}