package com.hys.zyy.manage.model;

public class YktInfoVO extends YktInfo {

	private String provinceName, orgName, residencySyncStr, teacherSyncStr, stateStr, createTimeStr, initYearTimeStr, initDeptTreeTimeStr;

	public YktInfoVO() {
		super();
	}

	public YktInfoVO(String domainName) {
		super(domainName);
	}

	public YktInfoVO(Long id, Integer initDeptTreeState) {
		super(id, initDeptTreeState);
	}

	public YktInfoVO(Long id, Integer initYearState, Integer initBaseState, Integer initDeptState) {
		super(id, initYearState, initBaseState, initDeptState);
	}

	public String getProvinceName() {
		return provinceName;
	}

	public void setProvinceName(String provinceName) {
		this.provinceName = provinceName;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName == null ? null : orgName.trim();
	}

	public String getResidencySyncStr() {
		return residencySyncStr;
	}

	public void setResidencySyncStr(String residencySyncStr) {
		this.residencySyncStr = residencySyncStr == null ? null : residencySyncStr.trim();
	}

	public String getTeacherSyncStr() {
		return teacherSyncStr;
	}

	public void setTeacherSyncStr(String teacherSyncStr) {
		this.teacherSyncStr = teacherSyncStr == null ? null : teacherSyncStr.trim();
	}

	public String getStateStr() {
		return stateStr;
	}

	public void setStateStr(String stateStr) {
		this.stateStr = stateStr == null ? null : stateStr.trim();
	}

	public String getCreateTimeStr() {
		return createTimeStr;
	}

	public void setCreateTimeStr(String createTimeStr) {
		this.createTimeStr = createTimeStr == null ? null : createTimeStr.trim();
	}

	public String getInitYearTimeStr() {
		return initYearTimeStr;
	}

	public void setInitYearTimeStr(String initYearTimeStr) {
		this.initYearTimeStr = initYearTimeStr == null ? null : initYearTimeStr.trim();
	}

	public String getInitDeptTreeTimeStr() {
		return initDeptTreeTimeStr;
	}

	public void setInitDeptTreeTimeStr(String initDeptTreeTimeStr) {
		this.initDeptTreeTimeStr = initDeptTreeTimeStr == null ? null : initDeptTreeTimeStr.trim();
	}

}