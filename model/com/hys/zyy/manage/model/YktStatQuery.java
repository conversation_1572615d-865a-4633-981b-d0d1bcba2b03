package com.hys.zyy.manage.model;

import java.io.Serializable;

/**
 * 云课堂统计查询参数模型
 * <AUTHOR>
 */
public class YktStatQuery implements Serializable {
	private static final long serialVersionUID = 1L;
	/*
	 * 签名
	 */
	private String sign;
	/*
	 * 时间戳
	 */
	private Long timeSpan;
	/*
	 * 住院医机构ID
	 */
	private Long zyyOrgId;
	/*
	 * BDP机构ID
	 */
	private Long bdpOrgId;
	/*
	 * 访问的住院医接口名
	 */
	private String methodName;
	/*
	 * 云课堂站点域名
	 */
	private String domainName;
	/*
	 * 指标的使用类型（9=科室使用；11=带教使用）
	 */
	private Integer userType;
	/*
	 * 绩效统计月份
	 */
	private String statMonth;

	public YktStatQuery() {
		super();
	}

	public YktStatQuery(Long zyyOrgId) {
		super();
		this.zyyOrgId = zyyOrgId;
	}

	public String getSign() {
		return sign;
	}

	public void setSign(String sign) {
		this.sign = sign;
	}

	public Long getTimeSpan() {
		return timeSpan;
	}

	public void setTimeSpan(Long timeSpan) {
		this.timeSpan = timeSpan;
	}

	public Long getZyyOrgId() {
		return zyyOrgId;
	}

	public void setZyyOrgId(Long zyyOrgId) {
		this.zyyOrgId = zyyOrgId;
	}

	public Long getBdpOrgId() {
		return bdpOrgId;
	}

	public void setBdpOrgId(Long bdpOrgId) {
		this.bdpOrgId = bdpOrgId;
	}

	public String getDomainName() {
		return domainName;
	}

	public void setDomainName(String domainName) {
		this.domainName = domainName == null ? null : domainName.trim();
	}

	public String getMethodName() {
		return methodName;
	}

	public void setMethodName(String methodName) {
		this.methodName = methodName == null ? null : methodName.trim();
	}

	public Integer getUserType() {
		return userType;
	}

	public void setUserType(Integer userType) {
		this.userType = userType;
	}

	public String getStatMonth() {
		return statMonth;
	}

	public void setStatMonth(String statMonth) {
		this.statMonth = statMonth == null ? null : statMonth.trim();
	}

	@Override
	public String toString() {
		return "YktStatQuery [sign=" + sign + ", timeSpan=" + timeSpan + ", zyyOrgId=" + zyyOrgId + ", bdpOrgId="
				+ bdpOrgId + ", methodName=" + methodName + ", domainName=" + domainName + ", userType=" + userType
				+ ", statMonth=" + statMonth + "]";
	}
	
}