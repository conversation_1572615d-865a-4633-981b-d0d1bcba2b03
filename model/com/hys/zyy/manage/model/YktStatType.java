package com.hys.zyy.manage.model;

import java.io.Serializable;

/**
 * 统计指标
 * <AUTHOR>
 */
public class YktStatType implements Serializable {
	private static final long serialVersionUID = 1L;
	/*
	 * 指标ID
	 */
	private Long typeId;
	/*
	 * 指标名称
	 */
	private String typeName;

	public YktStatType() {
		super();
	}

	public YktStatType(Long typeId, String typeName) {
		super();
		this.typeId = typeId;
		this.typeName = typeName;
	}

	public Long getTypeId() {
		return typeId;
	}

	public void setTypeId(Long typeId) {
		this.typeId = typeId;
	}

	public String getTypeName() {
		return typeName;
	}

	public void setTypeName(String typeName) {
		this.typeName = typeName == null ? null : typeName.trim();
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((typeId == null) ? 0 : typeId.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		YktStatType other = (YktStatType) obj;
		if (typeId == null) {
			if (other.typeId != null)
				return false;
		} else if (!typeId.equals(other.typeId))
			return false;
		return true;
	}

	@Override
	public String toString() {
		return "YktStatType [typeId=" + typeId + ", typeName=" + typeName + "]";
	}

}