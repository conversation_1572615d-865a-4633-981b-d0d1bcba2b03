package com.hys.zyy.manage.model;

import java.io.Serializable;
import java.util.List;

import org.codehaus.jackson.annotate.JsonIgnoreProperties;

/**
 * 带教统计
 */
@JsonIgnoreProperties({ "teacherId", "teacherName", "mobileNumber", "certificateNo", "resiStatStr", "activityTypeStatStr" })
public class YktTeacherStat implements Serializable {
	private static final long serialVersionUID = 1L;
	/*
	 * 带教ID
	 */
	private Long teacherId;
	/*
	 * 带教姓名
	 */
	private String teacherName;
	/*
	 * 带教账号
	 */
	private String teacherAccount;
	/*
	 * 带教手机号码
	 */
	private String mobileNumber;
	/*
	 * 带教证件号码
	 */
	private String certificateNo;
	/*
	 * 带教学员人数JSON字符串
	 */
	private String resiStatStr;
	/*
	 * 带教组织教学活动次数JSON字符串
	 */
	private String activityTypeStatStr;
	/*
	 * 统计项
	 */
	private List<YktStatValue> stats;

	public YktTeacherStat() {
		super();
	}

	public Long getTeacherId() {
		return teacherId;
	}

	public void setTeacherId(Long teacherId) {
		this.teacherId = teacherId;
	}

	public String getTeacherName() {
		return teacherName;
	}

	public void setTeacherName(String teacherName) {
		this.teacherName = teacherName == null ? null : teacherName.trim();
	}

	public String getTeacherAccount() {
		return teacherAccount;
	}

	public void setTeacherAccount(String teacherAccount) {
		this.teacherAccount = teacherAccount == null ? null : teacherAccount.trim();
	}

	public String getMobileNumber() {
		return mobileNumber;
	}

	public void setMobileNumber(String mobileNumber) {
		this.mobileNumber = mobileNumber == null ? null : mobileNumber.trim();
	}

	public String getCertificateNo() {
		return certificateNo;
	}

	public void setCertificateNo(String certificateNo) {
		this.certificateNo = certificateNo == null ? null : certificateNo.trim();
	}
	
	public String getResiStatStr() {
		return resiStatStr;
	}

	public void setResiStatStr(String resiStatStr) {
		this.resiStatStr = resiStatStr == null ? null : resiStatStr.trim();
	}

	public String getActivityTypeStatStr() {
		return activityTypeStatStr;
	}

	public void setActivityTypeStatStr(String activityTypeStatStr) {
		this.activityTypeStatStr = activityTypeStatStr == null ? null : activityTypeStatStr.trim();
	}

	public List<YktStatValue> getStats() {
		return stats;
	}

	public void setStats(List<YktStatValue> stats) {
		this.stats = stats;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((teacherId == null) ? 0 : teacherId.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		YktTeacherStat other = (YktTeacherStat) obj;
		if (teacherId == null) {
			if (other.teacherId != null)
				return false;
		} else if (!teacherId.equals(other.teacherId))
			return false;
		return true;
	}

	@Override
	public String toString() {
		return "YktTeacherStat [teacherId=" + teacherId + ", teacherName=" + teacherName + ", teacherAccount="
				+ teacherAccount + ", mobileNumber=" + mobileNumber + ", certificateNo=" + certificateNo
				+ ", resiStatStr=" + resiStatStr + ", activityTypeStatStr=" + activityTypeStatStr + ", stats=" + stats + "]";
	}

}