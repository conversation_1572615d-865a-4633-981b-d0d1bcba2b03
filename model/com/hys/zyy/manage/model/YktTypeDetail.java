package com.hys.zyy.manage.model;

import java.io.Serializable;

public class YktTypeDetail implements Serializable {
	private static final long serialVersionUID = 1L;
	/*
	 * 带教账号
	 */
	private String teacherAccount;
	/*
	 * 带教学员人数细分JSON字符串
	 */
	private String typeDetailStr;

	public YktTypeDetail() {
		super();
	}

	public YktTypeDetail(String teacherAccount, String typeDetailStr) {
		super();
		this.teacherAccount = teacherAccount;
		this.typeDetailStr = typeDetailStr;
	}

	public String getTeacherAccount() {
		return teacherAccount;
	}

	public void setTeacherAccount(String teacherAccount) {
		this.teacherAccount = teacherAccount == null ? null : teacherAccount.trim();
	}

	public String getTypeDetailStr() {
		return typeDetailStr;
	}

	public void setTypeDetailStr(String typeDetailStr) {
		this.typeDetailStr = typeDetailStr == null ? null : typeDetailStr.trim();
	}

}