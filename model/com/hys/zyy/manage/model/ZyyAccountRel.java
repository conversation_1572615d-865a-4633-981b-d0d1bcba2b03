package com.hys.zyy.manage.model;

import java.util.Date;

public class ZyyAccountRel extends ZyyBaseObject {

	/**
	 * 
	 */
	private static final long serialVersionUID = -6102839514386598765L;

	private Long id;// 唯一标识
	private Long userId;// 建立关联主账户ID
	private Long subUserId;// 建立关联子账户ID
	private String relShowDesc;// 子账号关系角色描述信息（xx科室管理员，xxx带教）
	private Long zyyOrgId;// 医院ID
	private Long createUserId;// 创建人ID
	private Date createTime;// 创建时间
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	public Long getSubUserId() {
		return subUserId;
	}
	public void setSubUserId(Long subUserId) {
		this.subUserId = subUserId;
	}
	public String getRelShowDesc() {
		return relShowDesc;
	}
	public void setRelShowDesc(String relShowDesc) {
		this.relShowDesc = relShowDesc;
	}
	public Long getZyyOrgId() {
		return zyyOrgId;
	}
	public void setZyyOrgId(Long zyyOrgId) {
		this.zyyOrgId = zyyOrgId;
	}
	public Long getCreateUserId() {
		return createUserId;
	}
	public void setCreateUserId(Long createUserId) {
		this.createUserId = createUserId;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
}
