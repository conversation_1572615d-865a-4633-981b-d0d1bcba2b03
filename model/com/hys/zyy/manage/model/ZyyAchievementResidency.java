package com.hys.zyy.manage.model;

import java.util.List;

public class ZyyAchievementResidency {

	private Long id;
	private Long achievementTableID;//考核表ID
	private Long residencyId;
	private Float type1;//手册完成率
	private Integer type2;//出勤情况
	private Integer type2_1;//迟到
	private Integer type2_2;//早退
	private Integer type2_3;//旷工
	private String type2Str;
	private Integer type3;//登陆次数
	private Float type4;//教学活动出勤率
	private Integer type5;//带教工作量
	private Integer type6;//手册审核量
	private Integer type7;//手册审核率
	/************************/
	private String name;//学员名称
	private Long deptId;//科室ID
	private Integer c1;//type1使用
	private Integer c2;//type1使用
	private Integer allDay;//整天数
	private Integer day;//天数
	private Integer loginCnt;//登录次数
	private Integer activityNum;
	private Integer activityJoinNum;
	private List<Long> residencyIds;//type6使用统计带教数据中学员的数量
	
	/************************/
	private String jobNumber;
	private String certificateNo;
	private String year;
	private String aliasName;
	private String orgName;
	private String deptLevel;
	private String deptName;
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getAchievementTableID() {
		return achievementTableID;
	}
	public void setAchievementTableID(Long achievementTableID) {
		this.achievementTableID = achievementTableID;
	}
	public Long getResidencyId() {
		return residencyId;
	}
	public void setResidencyId(Long residencyId) {
		this.residencyId = residencyId;
	}
	public Float getType1() {
		return type1;
	}
	public void setType1(Float type1) {
		this.type1 = type1;
	}
	public Integer getType3() {
		return type3;
	}
	public void setType3(Integer type3) {
		this.type3 = type3;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public Integer getC1() {
		return c1;
	}
	public void setC1(Integer c1) {
		this.c1 = c1;
	}
	public Integer getC2() {
		return c2;
	}
	public void setC2(Integer c2) {
		this.c2 = c2;
	}
	public Integer getAllDay() {
		return allDay;
	}
	public void setAllDay(Integer allDay) {
		this.allDay = allDay;
	}
	public Integer getDay() {
		return day;
	}
	public void setDay(Integer day) {
		this.day = day;
	}
	public Integer getLoginCnt() {
		return loginCnt;
	}
	public void setLoginCnt(Integer loginCnt) {
		this.loginCnt = loginCnt;
	}
	public Long getDeptId() {
		return deptId;
	}
	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}
	public Integer getType2() {
		return type2;
	}
	public void setType2(Integer type2) {
		this.type2 = type2;
	}
	public Float getType4() {
		return type4;
	}
	public void setType4(Float type4) {
		this.type4 = type4;
	}
	public String getJobNumber() {
		return jobNumber;
	}
	public void setJobNumber(String jobNumber) {
		this.jobNumber = jobNumber;
	}
	public String getCertificateNo() {
		return certificateNo;
	}
	public void setCertificateNo(String certificateNo) {
		this.certificateNo = certificateNo;
	}
	public String getYear() {
		return year;
	}
	public void setYear(String year) {
		this.year = year;
	}
	public String getAliasName() {
		return aliasName;
	}
	public void setAliasName(String aliasName) {
		this.aliasName = aliasName;
	}
	public String getOrgName() {
		return orgName;
	}
	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}
	public Integer getType5() {
		return type5;
	}
	public void setType5(Integer type5) {
		this.type5 = type5;
	}
	public Integer getType6() {
		return type6;
	}
	public void setType6(Integer type6) {
		this.type6 = type6;
	}
	public Integer getType7() {
		return type7;
	}
	public void setType7(Integer type7) {
		this.type7 = type7;
	}
	public String getDeptLevel() {
		return deptLevel;
	}
	public void setDeptLevel(String deptLevel) {
		this.deptLevel = deptLevel;
	}
	public String getDeptName() {
		return deptName;
	}
	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}
	public Integer getActivityNum() {
		return activityNum;
	}
	public void setActivityNum(Integer activityNum) {
		this.activityNum = activityNum;
	}
	public Integer getActivityJoinNum() {
		return activityJoinNum;
	}
	public void setActivityJoinNum(Integer activityJoinNum) {
		this.activityJoinNum = activityJoinNum;
	}
	public List<Long> getResidencyIds() {
		return residencyIds;
	}
	public void setResidencyIds(List<Long> residencyIds) {
		this.residencyIds = residencyIds;
	}
	public Integer getType2_1() {
		return type2_1;
	}
	public void setType2_1(Integer type2_1) {
		this.type2_1 = type2_1;
	}
	public Integer getType2_2() {
		return type2_2;
	}
	public void setType2_2(Integer type2_2) {
		this.type2_2 = type2_2;
	}
	public Integer getType2_3() {
		return type2_3;
	}
	public void setType2_3(Integer type2_3) {
		this.type2_3 = type2_3;
	}
	public String getType2Str() {
		return type2Str;
	}
	public void setType2Str(String type2Str) {
		this.type2Str = type2Str;
	}
}
