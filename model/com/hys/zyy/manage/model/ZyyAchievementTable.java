package com.hys.zyy.manage.model;

import java.util.Date;

import com.hys.zyy.manage.util.DateUtil;

public class ZyyAchievementTable {

	public static final int TYPE_1=1;
	public static final int TYPE_2=2;
	public static final int TYPE_3=3;
	public static final int TYPE_4=4;
	public static final int TYPE_5=5;
	public static final int TYPE_6=6;
	public static final int TYPE_7=7;
	
	private Long id;//编号
	private Date startDate;//绩效开始时间
	private Date endDate;//绩效结束时间
	private String year;//年级
	private String baseStd;//专业
	private String item;//考核的项目
	private String itemKey;//存储key值
	private Date createDate;//生成时间
	private Long createResidencyId;//创建者ID
	private Long zyyUserOrgId;// 创建者医院ID
	private Long deptId;
	private Integer deptLevel;
	private Integer type;
	private String deptName;
	
	public ZyyAchievementTable(){}
	
	public ZyyAchievementTable(ZyyAchievementTableVO vo) {
		startDate=DateUtil.parse(vo.getStartDate(), "yyyy-MM-dd");
		endDate=DateUtil.parse(vo.getEndDate(), "yyyy-MM-dd");
		year=vo.getYear();
		baseStd=vo.getBaseStd();
		item=vo.getItem();
		createDate=DateUtil.parse(vo.getCreateDate(), "yyyy-MM-dd");
		createResidencyId=vo.getCreateResidencyId();
		zyyUserOrgId=vo.getZyyUserOrgId();
		deptId=vo.getDeptId();
		deptLevel=vo.getDeptLevel();
		type=vo.getType();
	}
	
	public Date getStartDate() {
		return startDate;
	}
	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}
	public Date getEndDate() {
		return endDate;
	}
	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}
	public String getYear() {
		return year;
	}
	public void setYear(String year) {
		this.year = year;
	}
	public String getBaseStd() {
		return baseStd;
	}
	public void setBaseStd(String baseStd) {
		this.baseStd = baseStd;
	}
	public String getItem() {
		return item;
	}
	public void setItem(String item) {
		this.item = item;
	}
	public Date getCreateDate() {
		return createDate;
	}
	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getCreateResidencyId() {
		return createResidencyId;
	}
	public void setCreateResidencyId(Long createResidencyId) {
		this.createResidencyId = createResidencyId;
	}
	public Long getZyyUserOrgId() {
		return zyyUserOrgId;
	}
	public void setZyyUserOrgId(Long zyyUserOrgId) {
		this.zyyUserOrgId = zyyUserOrgId;
	}

	public Long getDeptId() {
		return deptId;
	}

	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}

	public Integer getDeptLevel() {
		return deptLevel;
	}

	public void setDeptLevel(Integer deptLevel) {
		this.deptLevel = deptLevel;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public String getDeptName() {
		return deptName;
	}

	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}

	public String getItemKey() {
		return itemKey;
	}

	public void setItemKey(String itemKey) {
		this.itemKey = itemKey;
	}
}
