package com.hys.zyy.manage.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ZyyAchievementTableVO {

	private Long id;// 编号
	private String startDate;// 绩效开始时间
	private String endDate;// 绩效结束时间
	private String year;// 年级
	private String baseStd;// 专业
	private String item;// 考核的项目
	private String itemKey;//
	private String createDate;// 生成时间
	private Long createResidencyId;//创建者ID
	private Long zyyUserOrgId;// 创建者医院ID
	private Long tableId;// 目标表ID
	private Integer downType;//下载类型。1：excel。2：pdf
	private Integer deptLevel;
	private Long deptId;
	private String deptName;
	private Integer type;//1:学员。2：带教
	private List<Long> baseStdList;
	private List<String> yearList;

	public void yearToList() {

		yearList = new ArrayList<String>();

		if (year != null && year != "") {
			String[] arr = year.split(",");
			for (String a : arr) {
				yearList.add(a);
			}
		}
	}
	
	public void baseStdToList(){
		baseStdList=new ArrayList<Long>();
		
		if(baseStd!=null && baseStd!=""){
			String[] arr=baseStd.split(",");
			for(String a:arr){
				baseStdList.add(Long.parseLong(a));
			}
		}
	}
	

	public String getYear() {
		return year;
	}

	public void setYear(String year) {
		this.year = year;
	}

	public String getBaseStd() {
		return baseStd;
	}

	public void setBaseStd(String baseStd) {
		this.baseStd = baseStd;
	}

	public String getItem() {
		return item;
	}

	public void setItem(String item) {
		this.item = item;
	}

	public String getStartDate() {
		return startDate;
	}

	public void setStartDate(String startDate) {
		this.startDate = startDate;
	}

	public String getEndDate() {
		return endDate;
	}

	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}

	public String getCreateDate() {
		return createDate;
	}

	public void setCreateDate(String createDate) {
		this.createDate = createDate;
	}

	public Long getTableId() {
		return tableId;
	}

	public void setTableId(Long tableId) {
		this.tableId = tableId;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public List<Long> getBaseStdList() {
		return baseStdList;
	}

	public void setBaseStdList(List<Long> baseStdList) {
		this.baseStdList = baseStdList;
	}

	public List<String> getYearList() {
		return yearList;
	}

	public void setYearList(List<String> yearList) {
		this.yearList = yearList;
	}

	public Long getCreateResidencyId() {
		return createResidencyId;
	}

	public void setCreateResidencyId(Long createResidencyId) {
		this.createResidencyId = createResidencyId;
	}

	public Long getZyyUserOrgId() {
		return zyyUserOrgId;
	}

	public void setZyyUserOrgId(Long zyyUserOrgId) {
		this.zyyUserOrgId = zyyUserOrgId;
	}

	public Integer getDownType() {
		return downType;
	}

	public void setDownType(Integer downType) {
		this.downType = downType;
	}

	public Integer getDeptLevel() {
		return deptLevel;
	}

	public void setDeptLevel(Integer deptLevel) {
		this.deptLevel = deptLevel;
	}

	public Long getDeptId() {
		return deptId;
	}

	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public String getDeptName() {
		return deptName;
	}

	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}

	public String getItemKey() {
		return itemKey;
	}

	public void setItemKey(String itemKey) {
		this.itemKey = itemKey;
	}
}
