package com.hys.zyy.manage.model;

import java.util.Date;

public class ZyyActivityAttachment extends ZyyBaseObject {
	private static final long serialVersionUID = -4108677804444495334L;
	private Long id;// ID
	private Long activityId;// 活动ID
	private String activityName; // 活动名称
	private String attachmentName;// 附件名称
	private String attachmentUrl;// 附件路径
	private Integer attachmentStatus;// 附件状态0已删除 1未删除
	/*
	 * 文件存储在本地服务器
	 */
	private Boolean isLocalFile;
	/*
	 * 文件存储在七牛云
	 */
	private Boolean isQiNiuFile;
	private Boolean isImg;
	private Boolean isPdf;
	private String thumbnail;
	private Date createDate;
	
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getActivityId() {
		return activityId;
	}

	public void setActivityId(Long activityId) {
		this.activityId = activityId;
	}

	public String getActivityName() {
		return activityName;
	}

	public void setActivityName(String activityName) {
		this.activityName = activityName == null ? null : activityName.trim();
	}

	public String getAttachmentName() {
		return attachmentName;
	}

	public void setAttachmentName(String attachmentName) {
		this.attachmentName = attachmentName;
	}

	public String getAttachmentUrl() {
		return attachmentUrl;
	}

	public void setAttachmentUrl(String attachmentUrl) {
		this.attachmentUrl = attachmentUrl;
	}

	public Integer getAttachmentStatus() {
		return attachmentStatus;
	}

	public void setAttachmentStatus(Integer attachmentStatus) {
		this.attachmentStatus = attachmentStatus;
	}

	public Boolean getIsLocalFile() {
		return isLocalFile;
	}

	public void setIsLocalFile(Boolean isLocalFile) {
		this.isLocalFile = isLocalFile;
	}

	public Boolean getIsQiNiuFile() {
		return isQiNiuFile;
	}

	public void setIsQiNiuFile(Boolean isQiNiuFile) {
		this.isQiNiuFile = isQiNiuFile;
	}

	public Boolean getIsImg() {
		return isImg;
	}

	public void setIsImg(Boolean isImg) {
		this.isImg = isImg;
	}

	public String getThumbnail() {
		return thumbnail;
	}

	public void setThumbnail(String thumbnail) {
		this.thumbnail = thumbnail;
	}

	public Boolean getIsPdf() {
		return isPdf;
	}

	public void setIsPdf(Boolean isPdf) {
		this.isPdf = isPdf;
	}
	
}
