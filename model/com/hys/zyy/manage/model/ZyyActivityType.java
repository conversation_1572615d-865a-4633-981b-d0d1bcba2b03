package com.hys.zyy.manage.model;

public class ZyyActivityType extends ZyyBaseObject{
	
	private static final long serialVersionUID = 12651321321225522L;
	/**
	 * id
	 */
	private Long id;
	/**
	 * 活动类型名称
	 */
	private String typeName;
	/**
	 * 医院ID
	 */
	private Long orgId;
	/**
	 * 状态 1有效 -1删除
	 */
	private Integer status;
	/**
	 * 启用状态 1启用 0停用
	 */
	private Integer useStatus;
	/**
	 * 教学活动类型标识 1.医疗查房,2.教学查房,3.小讲课,4.病例讨论,5.门诊教学,6.晨读文献,7.工作坊,8.学术讲座
	 */
	private Integer typeFlag;
	
	private String typeDesc;
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getTypeName() {
		return typeName;
	}
	public void setTypeName(String typeName) {
		this.typeName = typeName;
	}
	public Long getOrgId() {
		return orgId;
	}
	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	public Integer getUseStatus() {
		return useStatus;
	}
	public void setUseStatus(Integer useStatus) {
		this.useStatus = useStatus;
	}
	public Integer getTypeFlag() {
		return typeFlag;
	}
	public void setTypeFlag(Integer typeFlag) {
		this.typeFlag = typeFlag;
	}
	public String getTypeDesc() {
		return typeDesc;
	}
	public void setTypeDesc(String typeDesc) {
		this.typeDesc = typeDesc;
	}
}
