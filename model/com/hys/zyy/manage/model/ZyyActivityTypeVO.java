package com.hys.zyy.manage.model;

public class ZyyActivityTypeVO extends ZyyActivityType{
	private static final long serialVersionUID = 353839293643713L;
	
	/**
	 * 活动数量
	 */
	private Integer activityNum;
	/**
	 * 活动参与数量
	 */
	private Integer activityJoinNum;
	/**
	 * 参与人数
	 */
	private Integer peoNum;
	/**
	 * 实际人数
	 */
	private Integer peoJoinNum;
	/**
	 * 出勤率
	 */
	private Integer numPer;
	/**
	 * 绩效考核模块统计使用
	 */
	private Long userId;
	
	//是否选中  1 : 选中  null ： 未选中
	private Integer checkStatus;
	
	public Integer getActivityNum() {
		return activityNum;
	}
	public void setActivityNum(Integer activityNum) {
		this.activityNum = activityNum;
	}
	public Integer getPeoNum() {
		return peoNum;
	}
	public void setPeoNum(Integer peoNum) {
		this.peoNum = peoNum;
	}
	public Integer getNumPer() {
		return numPer;
	}
	public void setNumPer(Integer numPer) {
		this.numPer = numPer;
	}
	public Integer getActivityJoinNum() {
		return activityJoinNum;
	}
	public void setActivityJoinNum(Integer activityJoinNum) {
		this.activityJoinNum = activityJoinNum;
	}
	public Integer getPeoJoinNum() {
		return peoJoinNum;
	}
	public void setPeoJoinNum(Integer peoJoinNum) {
		this.peoJoinNum = peoJoinNum;
	}
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	public Integer getCheckStatus() {
		return checkStatus;
	}
	public void setCheckStatus(Integer checkStatus) {
		this.checkStatus = checkStatus;
	}
}
