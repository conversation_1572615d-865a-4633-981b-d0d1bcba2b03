package com.hys.zyy.manage.model;

import java.util.Date;

public class ZyyActivityUser extends ZyyBaseObject{

	private static final long serialVersionUID = 12651321321225522L;
	/**
	 * id
	 */
	private Long id;
	/**
	 * 活动ID
	 */
	private Long activityId;
	/**
	 * 参与人ID
	 */
	private Long userId;
	/**
	 *  是否出勤 0 未上报 1 出勤  2未出勤
	 */
	private Long attendStatus;

	/**
	 *  活动中是否出勤  1 出勤  2未出勤
	 */
	private Long attendStatusTwo;
	/**
	 * 学员的姓名
	 */
	private String realName;
	/**
	 *  是否出勤 0 未发1 已发
	 */
	private Long emailFlag;

	//更新时间
	private Date updateDate;

	public Long getEmailFlag() {
		return emailFlag;
	}
	public void setEmailFlag(Long emailFlag) {
		this.emailFlag = emailFlag;
	}
	public String getRealName() {
		return realName;
	}
	public void setRealName(String realName) {
		this.realName = realName;
	}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getActivityId() {
		return activityId;
	}
	public void setActivityId(Long activityId) {
		this.activityId = activityId;
	}
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	public Long getAttendStatus() {
		return attendStatus;
	}
	public void setAttendStatus(Long attendStatus) {
		this.attendStatus = attendStatus;
	}
	public Date getUpdateDate() {
		return updateDate;
	}
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	public Long getAttendStatusTwo() {
		return attendStatusTwo;
	}

	public void setAttendStatusTwo(Long attendStatusTwo) {
		this.attendStatusTwo = attendStatusTwo;
	}
}
