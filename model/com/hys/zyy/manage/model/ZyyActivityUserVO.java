package com.hys.zyy.manage.model;

import java.util.Date;

public class ZyyActivityUserVO extends ZyyActivityUser{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	private String showSeq;
	
	private Date startDate;
	
	private Date endDate;
	/**
	 * 性别
	 */
	private Integer sex;
	/**
	 * 学科基地名称
	 */
	private String aliasName;
	/**
	 * 年级
	 */
	private String year;
	/**
	 * 学员的email
	 */
	private String email;
	/**
	 * 学员登录名
	 */
	private String userAccountName;
	/**
	 * 学员参与的教学活动名称
	 */
	private String activityName;
	/**
	 * 证件号码
	 */
	private String certificateNo;
	/**
	 * JOB_NUMBER工号 
	 */
	private String jobNumber;
	
	/**
	 * 参加人数
	 */
	private int joinPersonNum;
	
	private String createrName;
	/*
	 * 专业ID
	 */
	private Long baseId;
	public String getJobNumber() {
		return jobNumber;
	}

	public void setJobNumber(String jobNumber) {
		this.jobNumber = jobNumber;
	}

	public String getCertificateNo() {
		return certificateNo;
	}

	public void setCertificateNo(String certificateNo) {
		this.certificateNo = certificateNo;
	}

	public String getActivityName() {
		return activityName;
	}

	public void setActivityName(String activityName) {
		this.activityName = activityName;
	}

	public String getUserAccountName() {
		return userAccountName;
	}

	public void setUserAccountName(String userAccountName) {
		this.userAccountName = userAccountName;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public Integer getSex() {
		return sex;
	}

	public void setSex(Integer sex) {
		this.sex = sex;
	}

	public String getAliasName() {
		return aliasName;
	}

	public void setAliasName(String aliasName) {
		this.aliasName = aliasName;
	}

	public String getYear() {
		return year;
	}

	public void setYear(String year) {
		this.year = year;
	}

	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	public int getJoinPersonNum() {
		return joinPersonNum;
	}

	public void setJoinPersonNum(int joinPersonNum) {
		this.joinPersonNum = joinPersonNum;
	}

	public String getCreaterName() {
		return createrName;
	}

	public void setCreaterName(String createrName) {
		this.createrName = createrName;
	}

	public String getShowSeq() {
		return showSeq;
	}

	public void setShowSeq(String showSeq) {
		this.showSeq = showSeq;
	}

	public Long getBaseId() {
		return baseId;
	}

	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}
	
}
