package com.hys.zyy.manage.model;

import java.util.Date;
import java.util.List;

/**
 * 准考证模板类
 * <AUTHOR>
 */
public class ZyyAdmissionTemplate extends ZyyBaseObject {

	private static final long serialVersionUID = -5786767540592983541L;
	
	private Long id;
	
	private String title;
	
	private String detail;
	
	private Date createDate;
	
	private Integer status;
	
	private Long zyyProvinceId;
	
	private List<ZyyAdmissionTemplateSubject> subject;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getDetail() {
		return detail;
	}

	public void setDetail(String detail) {
		this.detail = detail;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}
	

	public Long getZyyProvinceId() {
		return zyyProvinceId;
	}

	public void setZyyProvinceId(Long zyyProvinceId) {
		this.zyyProvinceId = zyyProvinceId;
	}

	public List<ZyyAdmissionTemplateSubject> getSubject() {
		return subject;
	}

	public void setSubject(List<ZyyAdmissionTemplateSubject> subject) {
		this.subject = subject;
	}
	
}
