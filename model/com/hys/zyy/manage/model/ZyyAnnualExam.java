package com.hys.zyy.manage.model;

import java.util.Date;

/**
 * 
 */
public class ZyyAnnualExam extends ZyyBaseObject {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	/**
	 * id
	 */
	private Long id;
	/**
	 * 科室id
	 */
	private Long baseId;
	/**
	 * 创建人的id
	 */
	private Long baseCreaterId;
	/**
	 * 考试的id
	 */
	private Long emExamId;
	/**
	 * 科目的id
	 */
	private Long examCourseId;
	/**
	 * 创建日期
	 */
	private Date createTime;
	/**
	 * 状态 1-正常
	 */
	private Integer status;
	/**
	 * 目录节点的id
	 */
	private Long catalogId;
	
	
	public Long getBaseId() {
		return baseId;
	}
	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}
	public Long getBaseCreaterId() {
		return baseCreaterId;
	}
	public void setBaseCreaterId(Long baseCreaterId) {
		this.baseCreaterId = baseCreaterId;
	}
	public Long getCatalogId() {
		return catalogId;
	}
	public void setCatalogId(Long catalogId) {
		this.catalogId = catalogId;
	}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	
	public Long getEmExamId() {
		return emExamId;
	}
	public void setEmExamId(Long emExamId) {
		this.emExamId = emExamId;
	}
	public Long getExamCourseId() {
		return examCourseId;
	}
	public void setExamCourseId(Long examCourseId) {
		this.examCourseId = examCourseId;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	
	
}
