package com.hys.zyy.manage.model;

import java.io.Serializable;
import java.util.Date;

public class ZyyApplyCheckFlow implements Serializable {
    /**
     * ID
     */
    private Long id;

    /**
     * 考试ID
     */
    private Long zyyExamId;

    /**
     * 审核级别  1-7  一级审核  ~ 七级审核
     */
    private Integer checkLevel;

    /**
     * 指定审核人
     */
    private Long appointUserId;

    /**
     * 审核角色类型   参考用户角色
     */
    private Integer checkType;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getZyyExamId() {
        return zyyExamId;
    }

    public void setZyyExamId(Long zyyExamId) {
        this.zyyExamId = zyyExamId;
    }

    public Integer getCheckLevel() {
        return checkLevel;
    }

    public void setCheckLevel(Integer checkLevel) {
        this.checkLevel = checkLevel;
    }

    public Long getAppointUserId() {
        return appointUserId;
    }

    public void setAppointUserId(Long appointUserId) {
        this.appointUserId = appointUserId;
    }

    public Integer getCheckType() {
        return checkType;
    }

    public void setCheckType(Integer checkType) {
        this.checkType = checkType;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime == null ? null : startTime.trim();
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime == null ? null : endTime.trim();
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }
}