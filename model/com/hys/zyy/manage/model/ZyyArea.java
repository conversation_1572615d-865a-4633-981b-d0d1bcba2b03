package com.hys.zyy.manage.model;
/**
 * 住院医地区表
 * <AUTHOR>
 */
public class ZyyArea extends ZyyBaseObject {
	private static final long serialVersionUID = 1L;
	/*
	 * ID
	 */
	private Long id;
	/*
	 * 父级ID
	 */
	private Long parenrId;
	/*
	 * 地区名称
	 */
	private String cityName;
	/*
	 * 老的SIPID，数据同步同
	 */
	private Long oldSipId;

	private String cmeOrgId;

	private String cmeParentOrgId;
	/*
	 * 排序
	 */
	private Long sort;

	public ZyyArea() {
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getParenrId() {
		return parenrId;
	}

	public void setParenrId(Long parenrId) {
		this.parenrId = parenrId;
	}

	public String getCityName() {
		return cityName;
	}

	public void setCityName(String cityName) {
		this.cityName = cityName == null ? null : cityName.trim();
	}

	public Long getOldSipId() {
		return oldSipId;
	}

	public void setOldSipId(Long oldSipId) {
		this.oldSipId = oldSipId;
	}

	public String getCmeOrgId() {
		return cmeOrgId;
	}

	public void setCmeOrgId(String cmeOrgId) {
		this.cmeOrgId = cmeOrgId == null ? null : cmeOrgId.trim();
	}

	public String getCmeParentOrgId() {
		return cmeParentOrgId;
	}

	public void setCmeParentOrgId(String cmeParentOrgId) {
		this.cmeParentOrgId = cmeParentOrgId == null ? null : cmeParentOrgId
				.trim();
	}

	public Long getSort() {
		return sort;
	}

	public void setSort(Long sort) {
		this.sort = sort;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ZyyArea other = (ZyyArea) obj;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		return true;
	}

	@Override
	public String toString() {
		return "ZyyArea [id=" + id + ", parenrId=" + parenrId + ", cityName="
				+ cityName + ", oldSipId=" + oldSipId + ", cmeOrgId="
				+ cmeOrgId + ", cmeParentOrgId=" + cmeParentOrgId + ", sort="
				+ sort + "]";
	}

}