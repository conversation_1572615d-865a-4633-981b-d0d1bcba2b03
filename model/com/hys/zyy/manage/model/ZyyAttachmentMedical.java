package com.hys.zyy.manage.model;

import java.util.Date;

public class ZyyAttachmentMedical extends ZyyBaseObject {
	
	private static final long serialVersionUID = -9138834196281047496L;
	
	private Long id;// ID
	private Long medicalId;// 病例ID
	private String attachmentName;// 附件名称
	private String attachmentUrl;// 附件路径
	private Integer attachmentStatus;// 附件状态0已删除 1未删除
	private Date createDate;
	
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getMedicalId() {
		return medicalId;
	}

	public void setMedicalId(Long medicalId) {
		this.medicalId = medicalId;
	}

	public String getAttachmentName() {
		return attachmentName;
	}

	public void setAttachmentName(String attachmentName) {
		this.attachmentName = attachmentName;
	}

	public String getAttachmentUrl() {
		return attachmentUrl;
	}

	public void setAttachmentUrl(String attachmentUrl) {
		this.attachmentUrl = attachmentUrl;
	}

	public Integer getAttachmentStatus() {
		return attachmentStatus;
	}

	public void setAttachmentStatus(Integer attachmentStatus) {
		this.attachmentStatus = attachmentStatus;
	}
	
}
