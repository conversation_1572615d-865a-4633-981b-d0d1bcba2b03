package com.hys.zyy.manage.model;

import java.io.Serializable;
import java.util.Date;

/**
 * 考勤上报逾期申请操作表
 * <AUTHOR>
 */
public class ZyyAttendApply implements Serializable {
	private static final long serialVersionUID = 1L;
	/*
	 * ID
	 */
	private Long id;
	/*
	 * 申请人
	 */
	private Long applyUser;
	/*
	 * 申请时间
	 */
	private Date applyTime;
	/*
	 * 申请类型暂不用
	 */
	private Integer applyType;
	/*
	 * 申请月份
	 */
	private String applyMonth;
	/*
	 * 申请科室
	 */
	private Long deptId;
	/*
	 * 审核状态：0、未审核默认；1、审核通过；2、审核不通过
	 */
	private Integer verifyStatus;
	/*
	 * 审核时间
	 */
	private Date verifyTime;
	/*
	 * 审核备注
	 */
	private String verifyRemark;
	/*
	 * 审核人
	 */
	private Long verifyUser;
	/*
	 * 最后一次修改时间
	 */
	private Date lastUpdateTime;
	/*
	 * 是否最新，1：是；0：否
	 */
	private Integer isNew;
	private Long zyyProvinceId;
	private Long zyyHospitalId;

	public ZyyAttendApply() {
		super();
	}

	public ZyyAttendApply(String applyMonth, Long deptId, Long zyyProvinceId, Long zyyHospitalId) {
		super();
		this.applyMonth = applyMonth;
		this.deptId = deptId;
		this.zyyProvinceId = zyyProvinceId;
		this.zyyHospitalId = zyyHospitalId;
	}

	public String getApplyMonth() {
		return applyMonth;
	}

	public void setApplyMonth(String applyMonth) {
		this.applyMonth = applyMonth == null ? null : applyMonth.trim();
	}

	public Long getDeptId() {
		return deptId;
	}

	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}

	public Long getZyyProvinceId() {
		return zyyProvinceId;
	}

	public void setZyyProvinceId(Long zyyProvinceId) {
		this.zyyProvinceId = zyyProvinceId;
	}

	public Long getZyyHospitalId() {
		return zyyHospitalId;
	}

	public void setZyyHospitalId(Long zyyHospitalId) {
		this.zyyHospitalId = zyyHospitalId;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getApplyUser() {
		return applyUser;
	}

	public void setApplyUser(Long applyUser) {
		this.applyUser = applyUser;
	}

	public Date getApplyTime() {
		return applyTime;
	}

	public void setApplyTime(Date applyTime) {
		this.applyTime = applyTime;
	}

	public Integer getApplyType() {
		return applyType;
	}

	public void setApplyType(Integer applyType) {
		this.applyType = applyType;
	}

	public Integer getVerifyStatus() {
		return verifyStatus;
	}

	public void setVerifyStatus(Integer verifyStatus) {
		this.verifyStatus = verifyStatus;
	}

	public Date getVerifyTime() {
		return verifyTime;
	}

	public void setVerifyTime(Date verifyTime) {
		this.verifyTime = verifyTime;
	}

	public String getVerifyRemark() {
		return verifyRemark;
	}

	public void setVerifyRemark(String verifyRemark) {
		this.verifyRemark = verifyRemark == null ? null : verifyRemark.trim();
	}

	public Long getVerifyUser() {
		return verifyUser;
	}

	public void setVerifyUser(Long verifyUser) {
		this.verifyUser = verifyUser;
	}

	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}

	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}

	public Integer getIsNew() {
		return isNew;
	}

	public void setIsNew(Integer isNew) {
		this.isNew = isNew;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ZyyAttendApply other = (ZyyAttendApply) obj;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		return true;
	}

	@Override
	public String toString() {
		return "ZyyAttendApply [id=" + id + ", applyUser=" + applyUser
				+ ", applyTime=" + applyTime + ", applyType=" + applyType
				+ ", applyMonth=" + applyMonth + ", deptId=" + deptId
				+ ", verifyStatus=" + verifyStatus + ", verifyTime="
				+ verifyTime + ", verifyRemark=" + verifyRemark
				+ ", verifyUser=" + verifyUser + ", lastUpdateTime="
				+ lastUpdateTime + ", isNew=" + isNew + ", zyyProvinceId="
				+ zyyProvinceId + ", zyyHospitalId=" + zyyHospitalId + "]";
	}

}
