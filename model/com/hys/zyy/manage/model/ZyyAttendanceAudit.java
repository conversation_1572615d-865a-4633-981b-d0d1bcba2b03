package com.hys.zyy.manage.model;

import java.util.Date;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：李海龙 May 4, 2012
 * 
 * 描述：
 * 
 * 说明:
 */
public class ZyyAttendanceAudit extends ZyyBaseObject {

	/**
	 * 
	 */
	private static final long serialVersionUID = -2723462017565138270L;

	private Long id;//唯一标识
	private Long zyyOrgId;//机构ID
	private Long resiLeaveId;//人员请假ID
	private Integer auditOrder;//当前审核级别 1一级  2 二级 3 三级
	private String auditSeq;//审核顺序
	private Integer auditorType;//请假审核-当前需要审核的人员类型
	private Integer teachAuditStatus;//带教审核状态-1待审核 2审核通过 3审核不通过
	private Integer deptAuditStatus;//科室审核状态-1待审核 2审核通过 3审核不通过
	private Integer tutorAuditStatus;//导师审核状态-1待审核 2审核通过 3审核不通过
	private Integer baseAuditStatus;//专业基地审核状态-1待审核 2审核通过 3审核不通过
	private Integer hospAuditStatus;//医院审核状态-1待审核 2审核通过 3审核不通过
	private Date createDate;//创建人ID
	private Date lastUpdateDate;//最后更新人ID
	
	public ZyyAttendanceAudit() {
		super();
	}
	
	public ZyyAttendanceAudit(Long resiLeaveId) {
		super();
		this.resiLeaveId = resiLeaveId;
	}

	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getZyyOrgId() {
		return zyyOrgId;
	}
	public void setZyyOrgId(Long zyyOrgId) {
		this.zyyOrgId = zyyOrgId;
	}
	public Long getResiLeaveId() {
		return resiLeaveId;
	}
	public void setResiLeaveId(Long resiLeaveId) {
		this.resiLeaveId = resiLeaveId;
	}
	public Integer getAuditOrder() {
		return auditOrder;
	}
	public void setAuditOrder(Integer auditOrder) {
		this.auditOrder = auditOrder;
	}
	public String getAuditSeq() {
		return auditSeq;
	}
	public void setAuditSeq(String auditSeq) {
		this.auditSeq = auditSeq;
	}
	public Integer getAuditorType() {
		return auditorType;
	}
	public void setAuditorType(Integer auditorType) {
		this.auditorType = auditorType;
	}
	public Integer getTeachAuditStatus() {
		return teachAuditStatus;
	}
	public void setTeachAuditStatus(Integer teachAuditStatus) {
		this.teachAuditStatus = teachAuditStatus;
	}
	public Integer getDeptAuditStatus() {
		return deptAuditStatus;
	}
	public void setDeptAuditStatus(Integer deptAuditStatus) {
		this.deptAuditStatus = deptAuditStatus;
	}
	public Integer getTutorAuditStatus() {
		return tutorAuditStatus;
	}
	public void setTutorAuditStatus(Integer tutorAuditStatus) {
		this.tutorAuditStatus = tutorAuditStatus;
	}
	public Integer getBaseAuditStatus() {
		return baseAuditStatus;
	}
	public void setBaseAuditStatus(Integer baseAuditStatus) {
		this.baseAuditStatus = baseAuditStatus;
	}
	public Integer getHospAuditStatus() {
		return hospAuditStatus;
	}
	public void setHospAuditStatus(Integer hospAuditStatus) {
		this.hospAuditStatus = hospAuditStatus;
	}
	public Date getCreateDate() {
		return createDate;
	}
	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}
	public Date getLastUpdateDate() {
		return lastUpdateDate;
	}
	public void setLastUpdateDate(Date lastUpdateDate) {
		this.lastUpdateDate = lastUpdateDate;
	}
}
