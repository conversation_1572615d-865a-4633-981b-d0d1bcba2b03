package com.hys.zyy.manage.model;

import java.util.Map;

public class ZyyAttendanceExportVO extends ZyyBaseObject{

	private static final long serialVersionUID = 5408463993171180328L;
	
	private Long userId;

	private String realName;
	
	private String year;
	
	private String aliasName;
	
	private String jobNumber;
	
	private Integer leaveEarlyNumber;
	
	private Integer lateNumber;
	
	private Integer otherNumber;
	
	private Map<Long, ZyyAttendanceRuleVO> leaveType;
	
	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public String getRealName() {
		return realName;
	}

	public void setRealName(String realName) {
		this.realName = realName;
	}

	public String getYear() {
		return year;
	}

	public void setYear(String year) {
		this.year = year;
	}

	public String getAliasName() {
		return aliasName;
	}

	public void setAliasName(String aliasName) {
		this.aliasName = aliasName;
	}

	public String getJobNumber() {
		return jobNumber;
	}

	public void setJobNumber(String jobNumber) {
		this.jobNumber = jobNumber;
	}

	public Integer getLeaveEarlyNumber() {
		return leaveEarlyNumber;
	}

	public void setLeaveEarlyNumber(Integer leaveEarlyNumber) {
		this.leaveEarlyNumber = leaveEarlyNumber;
	}

	public Integer getLateNumber() {
		return lateNumber;
	}

	public void setLateNumber(Integer lateNumber) {
		this.lateNumber = lateNumber;
	}

	public Integer getOtherNumber() {
		return otherNumber;
	}

	public void setOtherNumber(Integer otherNumber) {
		this.otherNumber = otherNumber;
	}

	public Map<Long, ZyyAttendanceRuleVO> getLeaveType() {
		return leaveType;
	}

	public void setLeaveType(Map<Long, ZyyAttendanceRuleVO> leaveType) {
		this.leaveType = leaveType;
	}
	
}
