package com.hys.zyy.manage.model;

/**
 * 请假类型
 *
 */
public class ZyyAttendanceLeaveType extends ZyyBaseObject{

	private static final long serialVersionUID = 4524858962962810607L;
	
	private Long id;
	
	private String leaveTypeName;
	
	private Integer leaveTypeFlag;//1病假 2.事假 3.婚假 4.产假 5.年假 6.丧家 
	
	private Long zyyOrgId;

	private Integer status;//0已删除 1 未删除
	
	private Integer usedStatus;//使用状态，0停用 1启用
	
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getLeaveTypeName() {
		return leaveTypeName;
	}

	public void setLeaveTypeName(String leaveTypeName) {
		this.leaveTypeName = leaveTypeName;
	}

	public Long getZyyOrgId() {
		return zyyOrgId;
	}

	public void setZyyOrgId(Long zyyOrgId) {
		this.zyyOrgId = zyyOrgId;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Integer getLeaveTypeFlag() {
		return leaveTypeFlag;
	}

	public void setLeaveTypeFlag(Integer leaveTypeFlag) {
		this.leaveTypeFlag = leaveTypeFlag;
	}

	public Integer getUsedStatus() {
		return usedStatus;
	}

	public void setUsedStatus(Integer usedStatus) {
		this.usedStatus = usedStatus;
	}
	
}
