package com.hys.zyy.manage.model;

import java.util.Date;

/**
 * 考勤规则
 * <AUTHOR>
 */
public class ZyyAttendanceRule extends ZyyBaseObject {

	private static final long serialVersionUID = 4375121883178833176L;
		
	private Long id;
	
	private Long zyyOrgId;//机构ID
	
	private Long leaveType;//请假类型
	
	private Integer leaveMaxDay;//请假天数
	
	private Integer leaveProcess;//审核级别 1一级  2 二级 3 三级
	
	private Integer auditUserType;//审核人身份
	
	private Integer judgeType;//1 小于等于   2大于
	
	private Date createDate;//创建时间
	
	private Date lastUpdateDate;//最后更新时间
	
	private Integer status;//状态 1正常 0 删除

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}
	
	public Long getZyyOrgId() {
		return zyyOrgId;
	}

	public void setZyyOrgId(Long zyyOrgId) {
		this.zyyOrgId = zyyOrgId;
	}

	public Long getLeaveType() {
		return leaveType;
	}

	public void setLeaveType(Long leaveType) {
		this.leaveType = leaveType;
	}

	public Integer getLeaveMaxDay() {
		return leaveMaxDay;
	}

	public void setLeaveMaxDay(Integer leaveMaxDay) {
		this.leaveMaxDay = leaveMaxDay;
	}

	public Integer getLeaveProcess() {
		return leaveProcess;
	}

	public void setLeaveProcess(Integer leaveProcess) {
		this.leaveProcess = leaveProcess;
	}
	
	public Integer getAuditUserType() {
		return auditUserType;
	}

	public void setAuditUserType(Integer auditUserType) {
		this.auditUserType = auditUserType;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Date getLastUpdateDate() {
		return lastUpdateDate;
	}

	public void setLastUpdateDate(Date lastUpdateDate) {
		this.lastUpdateDate = lastUpdateDate;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Integer getJudgeType() {
		return judgeType;
	}

	public void setJudgeType(Integer judgeType) {
		this.judgeType = judgeType;
	}
}
