package com.hys.zyy.manage.model;

import java.util.List;

public class ZyyAttendanceRuleVO extends ZyyAttendanceRule {

	private static final long serialVersionUID = 7026514747776588773L;

	private Float num;
	
	private String leaveNumCount;
	private Integer leaveLevel;//审核级别
	private Integer lastLevel;//是否是末级  1是  0 否
	private Integer leaveMaxDayMax;//规则指定最大天数
	private Integer leaveMaxDayMin;//规则指定最小天数
	
	private List<Integer> usedUserTypes;

	public List<Integer> getUsedUserTypes() {
		return usedUserTypes;
	}

	public void setUsedUserTypes(List<Integer> usedUserTypes) {
		this.usedUserTypes = usedUserTypes;
	}

	public Float getNum() {
		return num;
	}

	public void setNum(Float num) {
		this.num = num;
	}

	public String getLeaveNumCount() {
		return leaveNumCount;
	}

	public void setLeaveNumCount(String leaveNumCount) {
		this.leaveNumCount = leaveNumCount;
	}

	public Integer getLeaveLevel() {
		return leaveLevel;
	}

	public void setLeaveLevel(Integer leaveLevel) {
		this.leaveLevel = leaveLevel;
	}

	public Integer getLastLevel() {
		return lastLevel;
	}

	public void setLastLevel(Integer lastLevel) {
		this.lastLevel = lastLevel;
	}

	public Integer getLeaveMaxDayMax() {
		return leaveMaxDayMax;
	}

	public void setLeaveMaxDayMax(Integer leaveMaxDayMax) {
		this.leaveMaxDayMax = leaveMaxDayMax;
	}

	public Integer getLeaveMaxDayMin() {
		return leaveMaxDayMin;
	}

	public void setLeaveMaxDayMin(Integer leaveMaxDayMin) {
		this.leaveMaxDayMin = leaveMaxDayMin;
	}
}
