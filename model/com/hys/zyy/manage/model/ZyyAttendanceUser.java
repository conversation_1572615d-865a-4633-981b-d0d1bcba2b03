package com.hys.zyy.manage.model;

import java.util.Date;

import org.apache.commons.lang.builder.ReflectionToStringBuilder;

public class ZyyAttendanceUser extends ZyyBaseObject{
	
	private static final long serialVersionUID = -8134162794398894887L;

	public ZyyAttendanceUser(Long id, String name, String dpt, Integer cd, Integer zt,
			Integer kg, int sj, Long cdId, Long ztId, Long kgId, Long sjId,
			int cdStatus, int ztStatus, int kgStatus, int sjStatus) {
		super();
		this.id = id;
		this.name = name;
		this.dpt = dpt;
		this.cd = cd;
		this.zt = zt;
		this.kg = kg;
		this.sj = sj;
		this.cdId = cdId;
		this.ztId = ztId;
		this.kgId = kgId;
		this.sjId = sjId;
		this.cdStatus = cdStatus;
		this.ztStatus = ztStatus;
		this.kgStatus = kgStatus;
		this.sjStatus = sjStatus;
	}

	private Long id;										// 学员ID
	
	private String name;									// 学员姓名
	
	private String dpt;										// 学员科室
	
	private Integer cd=0;											// 迟到次数
	
	private Integer zt=0;											// 早退次数
	
	private Integer kg=0;											// 旷工次数
	
	private int sj;											// 事假次数
	
	private Long cdId;										// 迟到Id
	
	private Long ztId;										// 早退Id
	
	private Long kgId;										// 旷工Id
	
	private Long sjId;										// 事假Id
	
	private int cdStatus;									// 迟到状态
	
	private int ztStatus;									// 早退状态
	
	private int kgStatus;									// 旷工状态
	
	private int sjStatus;									// 事假状态
	
	private Date attendanceDate;
	
	private int attendanceType;
	
	private Integer leaveDateNumber = 0; //请假天数
	
//	private 
	
	public Integer getLeaveDateNumber() {
		return leaveDateNumber;
	}


	public void setLeaveDateNumber(Integer leaveDateNumber) {
		this.leaveDateNumber = leaveDateNumber;
	}


	public Date getAttendanceDate() {
		return attendanceDate;
	}


	public void setAttendanceDate(Date attendanceDate) {
		this.attendanceDate = attendanceDate;
	}


	public int getAttendanceType() {
		return attendanceType;
	}


	public void setAttendanceType(int attendanceType) {
		this.attendanceType = attendanceType;
	}


	public ZyyAttendanceUser() {
	}

	
	public Long getCdId() {
		return cdId;
	}

	public void setCdId(Long cdId) {
		this.cdId = cdId;
	}

	public Long getZtId() {
		return ztId;
	}

	public void setZtId(Long ztId) {
		this.ztId = ztId;
	}

	public Long getKgId() {
		return kgId;
	}

	public void setKgId(Long kgId) {
		this.kgId = kgId;
	}

	public Long getSjId() {
		return sjId;
	}

	public void setSjId(Long sjId) {
		this.sjId = sjId;
	}

	public int getCdStatus() {
		return cdStatus;
	}

	public void setCdStatus(int cdStatus) {
		this.cdStatus = cdStatus;
	}

	public int getZtStatus() {
		return ztStatus;
	}

	public void setZtStatus(int ztStatus) {
		this.ztStatus = ztStatus;
	}

	public int getKgStatus() {
		return kgStatus;
	}

	public void setKgStatus(int kgStatus) {
		this.kgStatus = kgStatus;
	}

	public int getSjStatus() {
		return sjStatus;
	}

	public void setSjStatus(int sjStatus) {
		this.sjStatus = sjStatus;
	}
	
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getDpt() {
		return dpt;
	}

	public void setDpt(String dpt) {
		this.dpt = dpt;
	}

	public Integer getCd() {
		return cd;
	}

	public void setCd(Integer cd) {
		this.cd = cd;
	}

	public Integer getZt() {
		return zt;
	}

	public void setZt(Integer zt) {
		this.zt = zt;
	}

	public Integer getKg() {
		return kg;
	}

	public void setKg(Integer kg) {
		this.kg = kg;
	}

	public int getSj() {
		return sj;
	}

	public void setSj(int sj) {
		this.sj = sj;
	}

	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this);
	}
	
}
