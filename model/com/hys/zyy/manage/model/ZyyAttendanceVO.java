package com.hys.zyy.manage.model;

import java.util.ArrayList;
import java.util.Collection;

import org.apache.commons.lang.builder.ReflectionToStringBuilder;

/**
 * 日常考勤VO
 * <AUTHOR>
 *
 */
public class ZyyAttendanceVO extends ZyyBaseObject{
	
	private static final long serialVersionUID = -794427822751976441L;

	private int dayOfMonth;									// 月第几天
	
	private ArrayList<ZyyAttendanceUser> users = new ArrayList<ZyyAttendanceUser>();			// 当天学员信息

	public int getDayOfMonth() {
		return dayOfMonth;
	}

	public void setDayOfMonth(int dayOfMonth) {
		this.dayOfMonth = dayOfMonth;
	}

	public Collection<ZyyAttendanceUser> getUsers() {
		return users;
	}
	
	public void addUser(ZyyAttendanceUser user) {
		this.users.add(user);
	}
	
	public void addUser(Long id, String name, String dpt, int cd, int zt,
			int kg, int sj, Long cdId, Long ztId, Long kgId, Long sjId,
			int cdStatus, int ztStatus, int kgStatus, int sjStatus) {
		ZyyAttendanceUser user = new ZyyAttendanceUser( id,  name,  dpt,  cd,  zt,
				 kg,  sj,  cdId,  ztId,  kgId,  sjId,
				 cdStatus,  ztStatus,  kgStatus,  sjStatus);
		this.users.add(user);
	}
	
	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this);
	}
}