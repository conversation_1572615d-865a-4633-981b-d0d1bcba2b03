package com.hys.zyy.manage.model;

import org.apache.commons.lang.builder.ReflectionToStringBuilder;

public class ZyyAuditProcessDetailVO extends ZyyBaseObject{
	
	private static final long serialVersionUID = 3811460947524674281L;

	private int userType;
	
	private int auditLevel;
	
	private int resiType;
	
	private Long orgId;

	public int getUserType() {
		return userType;
	}

	public void setUserType(int userType) {
		this.userType = userType;
	}

	public int getAuditLevel() {
		return auditLevel;
	}

	public void setAuditLevel(int auditLevel) {
		this.auditLevel = auditLevel;
	}

	public int getResiType() {
		return resiType;
	}

	public void setResiType(int resiType) {
		this.resiType = resiType;
	}

	public Long getOrgId() {
		return orgId;
	}

	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}

	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this);
	}

	public void write(ZyyProcessDetail entity) {
		entity.setVerifiersOrg(orgId);
		entity.setVerifiers(userType);
		if(resiType != 0)
			entity.setAdjustType(this.resiType);
	}
	
}
