package com.hys.zyy.manage.model;

import java.util.List;

import org.apache.commons.lang.builder.ReflectionToStringBuilder;

import com.hys.zyy.manage.constants.Constants;

public class ZyyAuditProcessListVO extends ZyyBaseObject{
	
	private static final long serialVersionUID = -8363403240586251030L;

	private ZyyAuditProcessVO process1;
	
	private ZyyAuditProcessVO process2;
	
	private ZyyAuditProcessVO process3;
	
	private ZyyAuditProcessVO process4;
	
	private ZyyAuditProcessVO process5;
	
	private ZyyAuditProcessVO process6;
	
	private ZyyAuditProcessVO process7;
	
	private ZyyAuditProcessVO process8;
	
	public ZyyAuditProcessListVO() {
		this.process1 = new ZyyAuditProcessVO();
		this.process2 = new ZyyAuditProcessVO();
		this.process3 = new ZyyAuditProcessVO();
		this.process4 = new ZyyAuditProcessVO();
		this.process5 = new ZyyAuditProcessVO();
		this.process6 = new ZyyAuditProcessVO();
		this.process7 = new ZyyAuditProcessVO();
		this.process8 = new ZyyAuditProcessVO();
	}

	/**
	 * 社会人 报名资格审核
	 * @return
	 */
	public ZyyAuditProcessVO getProcess1() {
		return process1;
	}

	public void setProcess1(ZyyAuditProcessVO process1) {
		this.process1 = process1;
	}

	/**
	 * 社会人 录取操作
	 * @return
	 */
	public ZyyAuditProcessVO getProcess2() {
		return process2;
	}

	public void setProcess2(ZyyAuditProcessVO process2) {
		this.process2 = process2;
	}

	/**
	 * 社会人 录取资格审核
	 * @return
	 */
	public ZyyAuditProcessVO getProcess3() {
		return process3;
	}

	public void setProcess3(ZyyAuditProcessVO process3) {
		this.process3 = process3;
	}

	/**
	 * 社会人 志愿调剂审核
	 * @return
	 */
	public ZyyAuditProcessVO getProcess4() {
		return process4;
	}

	public void setProcess4(ZyyAuditProcessVO process4) {
		this.process4 = process4;
	}

	/**
	 * 单位人 报名资格审核
	 * @return
	 */
	public ZyyAuditProcessVO getProcess5() {
		return process5;
	}

	public void setProcess5(ZyyAuditProcessVO process5) {
		this.process5 = process5;
	}

	/**
	 * 单位人 录取操作
	 * @return
	 */
	public ZyyAuditProcessVO getProcess6() {
		return process6;
	}

	public void setProcess6(ZyyAuditProcessVO process6) {
		this.process6 = process6;
	}

	/**
	 * 单位人 录取资格审核
	 * @return
	 */
	public ZyyAuditProcessVO getProcess7() {
		return process7;
	}

	public void setProcess7(ZyyAuditProcessVO process7) {
		this.process7 = process7;
	}

	/**
	 * 单位人 志愿调剂
	 * @return
	 */
	public ZyyAuditProcessVO getProcess8() {
		return process8;
	}

	public void setProcess8(ZyyAuditProcessVO process8) {
		this.process8 = process8;
	}

	public void read(List<ZyyProcessDetail> list) {
		if(list == null)
			return;
		for(ZyyProcessDetail detail : list) {
			Long pid = detail.getProcessId();
			Integer rs = detail.getResidencySource();
			ZyyAuditProcessVO inst = null;
			// 社会人
			if(pid == 2 && rs == Constants.RESIDENCY_SOURCE_2) inst = this.process1;
			if(pid == 7 && rs == Constants.RESIDENCY_SOURCE_2) inst = this.process2;
			if(pid == 3 && rs == Constants.RESIDENCY_SOURCE_2) inst = this.process3;
			if(pid == 8 && rs == Constants.RESIDENCY_SOURCE_2) inst = this.process4;
			// 单位人
			if(pid == 2 && rs == Constants.RESIDENCY_SOURCE_1) inst = this.process5;
			if(pid == 7 && rs == Constants.RESIDENCY_SOURCE_1) inst = this.process6;
			if(pid == 3 && rs == Constants.RESIDENCY_SOURCE_1) inst = this.process7;
			if(pid == 8 && rs == Constants.RESIDENCY_SOURCE_1) inst = this.process8;
			
			if(inst != null)
				inst.read(detail);
			
		}
	}
	
	public void readCfg(List<ZyyRecruitConfig> list) {
		if(list == null)
			return;
		for(ZyyRecruitConfig cfg : list) {
			int rs = cfg.getResidencySource();
			if(rs == Constants.RESIDENCY_SOURCE_2) {
				this.process1.setSequence(cfg.getIsRegAudits());
				this.process2.setSequence(cfg.getIsEnrollAudits());
				this.process3.setSequence(cfg.getIsEnrollApplyAudits());
				this.process4.setSequence(cfg.getIsSwapAudits());
			}
			if(rs == Constants.RESIDENCY_SOURCE_1) {
				this.process5.setSequence(cfg.getIsRegAudits());
				this.process6.setSequence(cfg.getIsEnrollAudits());
				this.process7.setSequence(cfg.getIsEnrollApplyAudits());
				this.process8.setSequence(cfg.getIsSwapAudits());
			}
		}
	}
	
	public void write(List<ZyyProcessDetail> list) {
		int counter = 1;
		List<ZyyAuditProcessDetailVO> details = process1.getDetails();
		for(ZyyAuditProcessDetailVO detail : details) {
			ZyyProcessDetail entity = new ZyyProcessDetail();
			entity.setIsFinal(details.size() == counter ? 1 : 0);
			entity.setProcessId(2l);
			entity.setProcessLevel(counter++);
			entity.setResidencySource(Constants.RESIDENCY_SOURCE_2);
			detail.write(entity);
			list.add(entity);
		}
		
		counter = 1;
		details = process2.getDetails();
		for(ZyyAuditProcessDetailVO detail : details) {
			ZyyProcessDetail entity = new ZyyProcessDetail();
			entity.setIsFinal(details.size() == counter ? 1 : 0);
			entity.setProcessId(7l);
			entity.setProcessLevel(counter++);
			entity.setResidencySource(Constants.RESIDENCY_SOURCE_2);
			detail.write(entity);
			list.add(entity);
		}
		
		counter = 1;
		details = process3.getDetails();
		for(ZyyAuditProcessDetailVO detail : details) {
			ZyyProcessDetail entity = new ZyyProcessDetail();
			entity.setIsFinal(details.size() == counter ? 1 : 0);
			entity.setProcessId(3l);
			entity.setProcessLevel(counter++);
			entity.setResidencySource(Constants.RESIDENCY_SOURCE_2);
			detail.write(entity);
			list.add(entity);
		}
		
		counter = 1;
		details = process4.getDetails();
		for(ZyyAuditProcessDetailVO detail : details) {
			ZyyProcessDetail entity = new ZyyProcessDetail();
			entity.setIsFinal(details.size() == counter ? 1 : 0);
			entity.setProcessId(8l);
			entity.setProcessLevel(counter++);
			entity.setResidencySource(Constants.RESIDENCY_SOURCE_2);
			detail.write(entity);
			list.add(entity);
		}
		
		counter = 1;
		details = process5.getDetails();
		for(ZyyAuditProcessDetailVO detail : details) {
			ZyyProcessDetail entity = new ZyyProcessDetail();
			entity.setIsFinal(details.size() == counter ? 1 : 0);
			entity.setProcessId(2l);
			entity.setProcessLevel(counter++);
			entity.setResidencySource(Constants.RESIDENCY_SOURCE_1);
			detail.write(entity);
			list.add(entity);
		}
		
		counter = 1;
		details = process6.getDetails();
		for(ZyyAuditProcessDetailVO detail : details) {
			ZyyProcessDetail entity = new ZyyProcessDetail();
			entity.setIsFinal(details.size() == counter ? 1 : 0);
			entity.setProcessId(7l);
			entity.setProcessLevel(counter++);
			entity.setResidencySource(Constants.RESIDENCY_SOURCE_1);
			detail.write(entity);
			list.add(entity);
		}
		
		counter = 1;
		details = process7.getDetails();
		for(ZyyAuditProcessDetailVO detail : details) {
			ZyyProcessDetail entity = new ZyyProcessDetail();
			entity.setIsFinal(details.size() == counter ? 1 : 0);
			entity.setProcessId(3l);
			entity.setProcessLevel(counter++);
			entity.setResidencySource(Constants.RESIDENCY_SOURCE_1);
			detail.write(entity);
			list.add(entity);
		}
		
		counter = 1;
		details = process8.getDetails();
		for(ZyyAuditProcessDetailVO detail : details) {
			ZyyProcessDetail entity = new ZyyProcessDetail();
			entity.setIsFinal(details.size() == counter ? 1 : 0);
			entity.setProcessId(8l);
			entity.setProcessLevel(counter++);
			entity.setResidencySource(Constants.RESIDENCY_SOURCE_1);
			detail.write(entity);
			list.add(entity);
		}
		
	}
	
	public void writeCfg(List<ZyyRecruitConfig> list) {
		ZyyRecruitConfig entity = new ZyyRecruitConfig();
		entity.setResidencySource(Constants.RESIDENCY_SOURCE_2);
		entity.setIsRegAudits(process1.getSequence());
		entity.setIsEnrollAudits(process2.getSequence());
		entity.setIsEnrollApplyAudits(process3.getSequence());
		entity.setIsSwapAudits(process4.getSequence());
		list.add(entity);
		
		entity = new ZyyRecruitConfig();
		entity.setResidencySource(Constants.RESIDENCY_SOURCE_1);
		entity.setIsRegAudits(process5.getSequence());
		entity.setIsEnrollAudits(process6.getSequence());
		entity.setIsEnrollApplyAudits(process7.getSequence());
		entity.setIsSwapAudits(process8.getSequence());
		list.add(entity);
	}

	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this);
	}

}
