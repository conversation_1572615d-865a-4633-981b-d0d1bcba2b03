package com.hys.zyy.manage.model;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang.builder.ReflectionToStringBuilder;

public class ZyyAuditProcessVO extends ZyyBaseObject{
	
	private static final long serialVersionUID = -426893050933071943L;

	private int sequence;
	
	private List<ZyyAuditProcessDetailVO> details;

	public ZyyAuditProcessVO() {
		this.details = new ArrayList<ZyyAuditProcessDetailVO>();
	}
	
	public int getSequence() {
		return sequence;
	}

	public void setSequence(int sequence) {
		this.sequence = sequence;
	}

	public List<ZyyAuditProcessDetailVO> getDetails() {
		return details;
	}

	public void setDetails(List<ZyyAuditProcessDetailVO> details) {
		this.details = details;
	}

	public void read(ZyyProcessDetail detail) {
		if(detail == null)
			return;
		ZyyAuditProcessDetailVO vo = new ZyyAuditProcessDetailVO();
		vo.setAuditLevel(detail.getProcessLevel());
		vo.setUserType(detail.getVerifiers());
		vo.setOrgId(detail.getVerifiersOrg());
		if(detail.getAdjustType() != null)
			vo.setResiType(detail.getAdjustType());
		this.details.add(vo);
	}

	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this);
	}
	
}
