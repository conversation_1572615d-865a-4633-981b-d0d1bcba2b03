package com.hys.zyy.manage.model;

import java.util.Date;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：李海龙 Mar 19, 2012
 * 
 * 描述：
 * 
 * 说明:
 */
public class ZyyBase extends ZyyBaseObject {

	private static final long serialVersionUID = 1839135588653098898L;

	/**
	 * 主键ID
	 */
	private Long id;
	
	/**
	 * 标准基地ID
	 */
	private Long baseStdId;
	
	/**
	 * 医院ID
	 */
	private Long hospitalId;
	
	/**
	 * 基地自评报告描述
	 */
	private String describe;
	
	/**
	 * 主任签名
	 */
	private String submitter;
	
	/**
	 * 身份证号码(验证)
	 */
	private String credentials;
	
	/**
	 * 基地状态
	 */
	private Integer status;
	
	/**
	 * 版本
	 */
	private Integer version;
	
	/**
	 * 卫生厅相关批示
	 */
	private String departmentInstruct;
	
	/**
	 * 医院相关批示
	 */
	private String hospitalInstruct;
	
	/**
	 * 证件类型
	 */
	private Integer cardType;
	
	/**
	 * 最后操作时间
	 */
	private Date lastUpdateDate;
	
	/**
	 * 审批用户类别
	 */
	private Integer approvalUserTypeId;
	
	/**
	 * 基地名称
	 */
	private  String name;

	/**
	 * 基地别名
	 */
	private String aliasName;
	/*
	 * 基地认证时间
	 */
	private Date baseCertificationTime;
	private String baseCertificationTimeStr;

	public ZyyBase() {
		super();
	}

	public ZyyBase(Long id, String baseCertificationTimeStr) {
		super();
		this.id = id;
		this.baseCertificationTimeStr = baseCertificationTimeStr;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getBaseStdId() {
		return baseStdId;
	}

	public void setBaseStdId(Long baseStdId) {
		this.baseStdId = baseStdId;
	}

	public Long getHospitalId() {
		return hospitalId;
	}

	public void setHospitalId(Long hospitalId) {
		this.hospitalId = hospitalId;
	}

	public String getDescribe() {
		return describe;
	}

	public void setDescribe(String describe) {
		this.describe = describe;
	}

	public String getSubmitter() {
		return submitter;
	}

	public void setSubmitter(String submitter) {
		this.submitter = submitter;
	}

	public String getCredentials() {
		return credentials;
	}

	public void setCredentials(String credentials) {
		this.credentials = credentials;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Integer getVersion() {
		return version;
	}

	public void setVersion(Integer version) {
		this.version = version;
	}

	public String getDepartmentInstruct() {
		return departmentInstruct;
	}

	public void setDepartmentInstruct(String departmentInstruct) {
		this.departmentInstruct = departmentInstruct;
	}

	public String getHospitalInstruct() {
		return hospitalInstruct;
	}

	public void setHospitalInstruct(String hospitalInstruct) {
		this.hospitalInstruct = hospitalInstruct;
	}

	public Integer getCardType() {
		return cardType;
	}

	public void setCardType(Integer cardType) {
		this.cardType = cardType;
	}

	public Date getLastUpdateDate() {
		return lastUpdateDate;
	}

	public void setLastUpdateDate(Date lastUpdateDate) {
		this.lastUpdateDate = lastUpdateDate;
	}

	public Integer getApprovalUserTypeId() {
		return approvalUserTypeId;
	}

	public void setApprovalUserTypeId(Integer approvalUserTypeId) {
		this.approvalUserTypeId = approvalUserTypeId;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getAliasName() {
		return aliasName;
	}

	public void setAliasName(String aliasName) {
		this.aliasName = aliasName;
	}

	public Date getBaseCertificationTime() {
		return baseCertificationTime;
	}

	public void setBaseCertificationTime(Date baseCertificationTime) {
		this.baseCertificationTime = baseCertificationTime;
	}

	public String getBaseCertificationTimeStr() {
		return baseCertificationTimeStr;
	}

	public void setBaseCertificationTimeStr(String baseCertificationTimeStr) {
		this.baseCertificationTimeStr = baseCertificationTimeStr == null ? null : baseCertificationTimeStr.trim();
	}
	
}