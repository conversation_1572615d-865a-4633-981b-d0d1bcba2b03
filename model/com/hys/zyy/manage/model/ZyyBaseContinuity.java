package com.hys.zyy.manage.model;

import java.util.Date;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：张伟清 2012-06-20
 * 
 * 描述：基地连排科室
 * 
 * 说明:
 */
public class ZyyBaseContinuity extends ZyyBaseObject {

	private static final long serialVersionUID = -4770073588402934577L;

	/**
	 * 主键ID
	 */
	private Long id ;
	
	/**
	 * 医院ID
	 */
	private Long hospitalId ;
	
	/**
	 * 基地ID
	 */
	private Long zyyBaseId ;
	
	/**
	 * 学制 1.1年 2.2年 3.3年
	 */
	private Integer educationSystem ;
	
	/**
	 * 操作用户ID
	 */
	private Long zyyUserId ;
	
	/**
	 * 最后更新时间
	 */
	private Date lastUpdateDate ;
	
	/**
	 * 科室ID
	 */
	private Long zyyDeptId ;
	
	/**
	 * 科室名称
	 */
	private String deptName ;
	/**
	 * 连排名称
	 */
	private String conName ;
	
	public String getConName() {
		return conName;
	}
	
	/**
	 * 连排名称
	 */
	private String name;

	public void setConName(String conName) {
		this.conName = conName;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getHospitalId() {
		return hospitalId;
	}

	public void setHospitalId(Long hospitalId) {
		this.hospitalId = hospitalId;
	}

	public Long getZyyBaseId() {
		return zyyBaseId;
	}

	public void setZyyBaseId(Long zyyBaseId) {
		this.zyyBaseId = zyyBaseId;
	}

	public Integer getEducationSystem() {
		return educationSystem;
	}

	public void setEducationSystem(Integer educationSystem) {
		this.educationSystem = educationSystem;
	}

	public Long getZyyUserId() {
		return zyyUserId;
	}

	public void setZyyUserId(Long zyyUserId) {
		this.zyyUserId = zyyUserId;
	}

	public Date getLastUpdateDate() {
		return lastUpdateDate;
	}

	public void setLastUpdateDate(Date lastUpdateDate) {
		this.lastUpdateDate = lastUpdateDate;
	}

	public Long getZyyDeptId() {
		return zyyDeptId;
	}

	public void setZyyDeptId(Long zyyDeptId) {
		this.zyyDeptId = zyyDeptId;
	}
	
	public String getDeptName() {
		return deptName;
	}

	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}
}