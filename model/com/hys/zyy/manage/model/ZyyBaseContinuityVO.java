package com.hys.zyy.manage.model;

import java.util.List;

public class ZyyBaseContinuityVO extends ZyyBaseObject{

	private static final long serialVersionUID = -6360841422562256235L;

	/**
	 *	基地联排科室信息ID
	 */
	private Long zbcId;
	
	/**
	 * 学制
	 */
	private Integer educationSystem;
	
	/**
	 *	基地id 
	 */
	private Long baseId;
	
	/**
	 *  基地联排科室信息List
	 */
	private List<ZyyBaseContinuity> list;
	
	/**
	 * 连排名称
	 */
	private String name;
	
	public Integer getEducationSystem() {
		return educationSystem;
	}

	public void setEducationSystem(Integer educationSystem) {
		this.educationSystem = educationSystem;
	}

	public Long getBaseId() {
		return baseId;
	}

	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}

	public Long getZbcId() {
		return zbcId;
	}

	public void setZbcId(Long zbcId) {
		this.zbcId = zbcId;
	}

	public List<ZyyBaseContinuity> getList() {
		return list;
	}

	public void setList(List<ZyyBaseContinuity> list) {
		this.list = list;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}
}
