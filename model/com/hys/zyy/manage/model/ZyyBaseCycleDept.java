package com.hys.zyy.manage.model;
/**
 * 
 * 标题：住院医师
 * 
 * 作者：李海龙 Mar 19, 2012
 * 
 * 描述：
 * 
 * 说明:
 */

public class ZyyBaseCycleDept extends ZyyBaseObject {

	private static final long serialVersionUID = -1262812399163123192L;

	/**
	 * 主键ID
	 */
	private Long id;
	
	/**
	 * 基地ID
	 */
	private Long baseId;
	
	/**
	 * 科室ID
	 */
	private Long deptId;
	
	/**
	 * 科室类别
	 */
	private Integer ycleDeptType;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getBaseId() {
		return baseId;
	}

	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}

	public Long getDeptId() {
		return deptId;
	}

	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}

	public Integer getYcleDeptType() {
		return ycleDeptType;
	}

	public void setYcleDeptType(Integer ycleDeptType) {
		this.ycleDeptType = ycleDeptType;
	}
}
