package com.hys.zyy.manage.model;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：张伟清 2012-04-03
 * 
 * 描述：标准轮转科室
 * 
 * 说明:
 */
public class ZyyBaseDeptStd extends ZyyBaseObject{
	
	private static final long serialVersionUID = -8601646556397547600L;
	
	/**
	 * 主键ID
	 */
	private Long id ;
	
	/**
	 * 标准基地ID
	 */
	private Long baseStdId ;
	
	/**
	 * 标准科室ID
	 */
	private Long deptStdId ;
	
	/**
	 * 轮转时间
	 */
	private String cycleTime ;
	
	/**
	 * 是否必选
	 */
	private Integer isRequired ;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getBaseStdId() {
		return baseStdId;
	}

	public void setBaseStdId(Long baseStdId) {
		this.baseStdId = baseStdId;
	}

	public Long getDeptStdId() {
		return deptStdId;
	}

	public void setDeptStdId(Long deptStdId) {
		this.deptStdId = deptStdId;
	}

	public String getCycleTime() {
		return cycleTime;
	}

	public void setCycleTime(String cycleTime) {
		this.cycleTime = cycleTime;
	}

	public Integer getIsRequired() {
		return isRequired;
	}

	public void setIsRequired(Integer isRequired) {
		this.isRequired = isRequired;
	}
}