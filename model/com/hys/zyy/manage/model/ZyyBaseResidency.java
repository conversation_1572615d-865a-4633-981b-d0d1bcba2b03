package com.hys.zyy.manage.model;

import java.util.Date;

/**
 * 标题：住院医师
 * 作者：张伟清 2012-05-10
 * 描述：基地住院医师
 * 说明:
 */
public class ZyyBaseResidency extends ZyyBaseObject{
	private static final long serialVersionUID = 1L;

	/**
	 * 主键ID
	 */
	private Long id ;
	
	/**
	 * 医院ID
	 */
	private Long hospitalId ;
	
	/**
	 * 基地ID
	 */
	private Long baseId ;
	
	/**
	 * 住院医师ID
	 */
	private Long residencyId ;
	
	/**
	 * 年度ID
	 */
	private Long recruitYearId ;
	
	/**
	 * 年度
	 */
	private String recruitYear ;
	
	/**
	 * 医师年级
	 */
	private String year;
	
	/**
	 * 医师来源 1.招录系统 2.导入 3.其他
	 */
	private Integer residencySource ;
	
	/**
	 * 最后修改时间
	 */
	private Date lastUpdateDate ;
	
	/**
	 * 轮转表ID
	 */
	private Long tableId ;
	
	/**
	 * 查询类型 0.本轮转表轮转 1.非本轮转表轮转
	 */
	private Integer selectType ;

	public ZyyBaseResidency() {
		super();
	}

	public ZyyBaseResidency(Long residencyId, Long recruitYearId) {
		super();
		this.residencyId = residencyId;
		this.recruitYearId = recruitYearId;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getHospitalId() {
		return hospitalId;
	}

	public void setHospitalId(Long hospitalId) {
		this.hospitalId = hospitalId;
	}

	public Long getBaseId() {
		return baseId;
	}

	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}

	public Long getResidencyId() {
		return residencyId;
	}

	public void setResidencyId(Long residencyId) {
		this.residencyId = residencyId;
	}

	public Long getRecruitYearId() {
		return recruitYearId;
	}

	public void setRecruitYearId(Long recruitYearId) {
		this.recruitYearId = recruitYearId;
	}

	public Integer getResidencySource() {
		return residencySource;
	}

	public void setResidencySource(Integer residencySource) {
		this.residencySource = residencySource;
	}

	public Date getLastUpdateDate() {
		return lastUpdateDate;
	}

	public void setLastUpdateDate(Date lastUpdateDate) {
		this.lastUpdateDate = lastUpdateDate;
	}

	public Long getTableId() {
		return tableId;
	}

	public void setTableId(Long tableId) {
		this.tableId = tableId;
	}

	public Integer getSelectType() {
		return selectType;
	}

	public void setSelectType(Integer selectType) {
		this.selectType = selectType;
	}

	public String getRecruitYear() {
		return recruitYear;
	}

	public void setRecruitYear(String recruitYear) {
		this.recruitYear = recruitYear;
	}

	public String getYear() {
		return year;
	}

	public void setYear(String year) {
		this.year = year;
	}
}