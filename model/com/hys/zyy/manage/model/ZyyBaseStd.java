package com.hys.zyy.manage.model;

import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：李海龙 Mar 19, 2012
 * 
 * 描述：
 * 
 * 说明:
 */

public class ZyyBaseStd extends ZyyBaseObject {



	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	/**
	 * 主键ID
	 */
	private Long id;
	
	/**
	 * 组织机构ID
	 */
	private Long zyyOrgId; 
	
	/**
	 * 名字
	 */
	private  String name;
	
	/**
	 * 床位数(张)(标准)
	 */
	private String bedsStd;
	
	/**
	 * 床位使用率(标准)
	 */
	private String bedsRate;
	
	/**
	 * 平均住院日(标准)
	 */
	private String inHospitalDay;
	
	/**
	 * 年门诊量(标准)
	 */
	private String outpatients;
	
	/**
	 * 年收治住院病人数)(标准)
	 */
	private String inHospitals;
	
	/**
	 * 年急诊量(标准)
	 */
	private String emergencyCases;
	
	/**
	 * 医疗工作量(标准)
	 */
	private String medicalWorkload;
	
	/**
	 * 医疗质量(标准)
	 */
	private String medicalQuality;
	
	/**
	 * 人员配备(标准)
	 */
	private String staffing;
	
	/**
	 * 专科医师指导条件(标准)
	 */
	private String guideCondition;
	
	/**
	 * 带头人条件(标准)
	 */
	private String leaderCondition;
	
	/**
	 * 编码
	 */
	private String code;
	
	/**
	 * 别名
	 */
	private String aliasName;
	private String oldDictValue;
	/**
	 * 基地类别
	 */
	private Integer baseType;
	
	/**
	 * 状态
	 */
	private Integer status;
	
	/**
	 * 最后更新时间
	 */
	private Date lastUpdateDate;
	
	/**
	 * 前言
	 */
	private String preface;

	/**
	 * 培训目标
	 */
	private String target;
	
	/**
	 * 备注
	 */
	private String memo;
	
	/**
	 * 中西医
	 */
	private Integer hospType;
	
	/**
	 * @desc 是否是紧缺专业  1.是
	 */
	private Integer isLacked;
	
	/**
	 * 招录统计信息
	 */
	private List<ZyyRecruitStatis> baseStatis ;
	
	private HashMap<String,Integer> signUpNumber;
	
	private HashMap<String,Integer> admitNumber; 
	
	//专业代码
	private String professionalSubjectId;
	//专业代码名称
	private String professionalSubjectName;
	//是否标准专科      是否标准专科（1：是；2：否）
	private Integer isProfessionalSubject;
	
	public ZyyBaseStd() {
		super();
	}

	public ZyyBaseStd(Long id, String name) {
		super();
		this.id = id;
		this.name = name;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getZyyOrgId() {
		return zyyOrgId;
	}

	public void setZyyOrgId(Long zyyOrgId) {
		this.zyyOrgId = zyyOrgId;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getBedsStd() {
		return bedsStd;
	}

	public void setBedsStd(String bedsStd) {
		this.bedsStd = bedsStd;
	}

	public String getBedsRate() {
		return bedsRate;
	}

	public void setBedsRate(String bedsRate) {
		this.bedsRate = bedsRate;
	}

	public String getInHospitalDay() {
		return inHospitalDay;
	}

	public void setInHospitalDay(String inHospitalDay) {
		this.inHospitalDay = inHospitalDay;
	}

	public String getOutpatients() {
		return outpatients;
	}

	public void setOutpatients(String outpatients) {
		this.outpatients = outpatients;
	}

	public String getInHospitals() {
		return inHospitals;
	}

	public void setInHospitals(String inHospitals) {
		this.inHospitals = inHospitals;
	}

	public String getEmergencyCases() {
		return emergencyCases;
	}

	public void setEmergencyCases(String emergencyCases) {
		this.emergencyCases = emergencyCases;
	}

	public String getMedicalWorkload() {
		return medicalWorkload;
	}

	public void setMedicalWorkload(String medicalWorkload) {
		this.medicalWorkload = medicalWorkload;
	}

	public String getMedicalQuality() {
		return medicalQuality;
	}

	public void setMedicalQuality(String medicalQuality) {
		this.medicalQuality = medicalQuality;
	}

	public String getStaffing() {
		return staffing;
	}

	public void setStaffing(String staffing) {
		this.staffing = staffing;
	}

	public String getGuideCondition() {
		return guideCondition;
	}

	public void setGuideCondition(String guideCondition) {
		this.guideCondition = guideCondition;
	}

	public String getLeaderCondition() {
		return leaderCondition;
	}

	public void setLeaderCondition(String leaderCondition) {
		this.leaderCondition = leaderCondition;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getAliasName() {
		return aliasName;
	}

	public void setAliasName(String aliasName) {
		this.aliasName = aliasName;
	}

	public String getOldDictValue() {
		return oldDictValue;
	}

	public void setOldDictValue(String oldDictValue) {
		this.oldDictValue = oldDictValue;
	}

	public Integer getBaseType() {
		return baseType;
	}

	public void setBaseType(Integer baseType) {
		this.baseType = baseType;
	}
	
	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Date getLastUpdateDate() {
		return lastUpdateDate;
	}

	public void setLastUpdateDate(Date lastUpdateDate) {
		this.lastUpdateDate = lastUpdateDate;
	}

	public String getPreface() {
		return preface;
	}

	public void setPreface(String preface) {
		this.preface = preface;
	}

	public String getTarget() {
		return target;
	}

	public void setTarget(String target) {
		this.target = target;
	}

	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public Integer getHospType() {
		return hospType;
	}

	public void setHospType(Integer hospType) {
		this.hospType = hospType;
	}

	public List<ZyyRecruitStatis> getBaseStatis() {
		return baseStatis;
	}

	public void setBaseStatis(List<ZyyRecruitStatis> baseStatis) {
		this.baseStatis = baseStatis;
	}

	public HashMap<String,Integer> getSignUpNumber() {
		return signUpNumber;
	}

	public void setSignUpNumber(HashMap<String,Integer> signUpNumber) {
		this.signUpNumber = signUpNumber;
	}

	public HashMap<String,Integer> getAdmitNumber() {
		return admitNumber;
	}

	public void setAdmitNumber(HashMap<String,Integer> admitNumber) {
		this.admitNumber = admitNumber;
	}

	public Integer getIsLacked() {
		return isLacked;
	}

	public void setIsLacked(Integer isLacked) {
		this.isLacked = isLacked;
	}

	public String getProfessionalSubjectId() {
		return professionalSubjectId;
	}

	public void setProfessionalSubjectId(String professionalSubjectId) {
		this.professionalSubjectId = professionalSubjectId;
	}
	
	public String getProfessionalSubjectName() {
		return professionalSubjectName;
	}

	public void setProfessionalSubjectName(String professionalSubjectName) {
		this.professionalSubjectName = professionalSubjectName;
	}

	public Integer getIsProfessionalSubject() {
		return isProfessionalSubject;
	}

	public void setIsProfessionalSubject(Integer isProfessionalSubject) {
		this.isProfessionalSubject = isProfessionalSubject;
	}
	
}
