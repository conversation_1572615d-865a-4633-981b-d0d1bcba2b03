package com.hys.zyy.manage.model;

public class ZyyBaseStdGroupPlan extends ZyyBaseObject {

	/**
	 * 
	 */
	private static final long serialVersionUID = -2480308627479403602L;

	private Long zyyOrgId;// 机构ID
	private Integer recruitCount;// 招生计划数量(机构其他数量)
	private Integer recruitCount2;//招生计划（剩余招生计划）
	private Integer hospType;//机构类型， 1、中医  2、西医
	private Long yearId;//年份ID
	/*
	 * 阶段ID
	 */
	private Integer stageId;
	
	public ZyyBaseStdGroupPlan() {
		super();
	}

	public ZyyBaseStdGroupPlan(Long zyyOrgId, Integer hospType, Long yearId, Integer stageId) {
		super();
		this.zyyOrgId = zyyOrgId;
		this.hospType = hospType;
		this.yearId = yearId;
		this.stageId = stageId;
	}
	
	public ZyyBaseStdGroupPlan(Long zyyOrgId, Integer recruitCount, Integer recruitCount2, Integer hospType, Long yearId, Integer stageId) {
		super();
		this.zyyOrgId = zyyOrgId;
		this.recruitCount = recruitCount;
		this.recruitCount2 = recruitCount2;
		this.hospType = hospType;
		this.yearId = yearId;
		this.stageId = stageId;
	}

	public Long getZyyOrgId() {
		return zyyOrgId;
	}
	public void setZyyOrgId(Long zyyOrgId) {
		this.zyyOrgId = zyyOrgId;
	}
	public Integer getRecruitCount() {
		return recruitCount;
	}
	public void setRecruitCount(Integer recruitCount) {
		this.recruitCount = recruitCount;
	}
	public Integer getHospType() {
		return hospType;
	}
	public void setHospType(Integer hospType) {
		this.hospType = hospType;
	}
	public Long getYearId() {
		return yearId;
	}
	public void setYearId(Long yearId) {
		this.yearId = yearId;
	}
	public Integer getRecruitCount2() {
		return recruitCount2;
	}
	public void setRecruitCount2(Integer recruitCount2) {
		this.recruitCount2 = recruitCount2;
	}
	public Integer getStageId() {
		return stageId;
	}
	public void setStageId(Integer stageId) {
		this.stageId = stageId;
	}
	
}
