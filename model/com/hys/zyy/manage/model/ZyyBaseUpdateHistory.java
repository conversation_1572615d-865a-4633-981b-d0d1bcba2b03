package com.hys.zyy.manage.model;

import java.io.Serializable;
import java.util.Date;

import com.hys.zyy.manage.util.BaseModel;

/**
 * 基地状态更新记录表
 */
public class ZyyBaseUpdateHistory extends BaseModel implements Serializable {
	private static final long serialVersionUID = 1L;
	/*
	 * ID
	 */
	private Long id;
	/*
	 * 医院ID
	 */
	private Long hospitalId;
	/*
	 * 专业ID
	 */
	private Long zyyBaseId;
	/*
	 * 基地状态[1：已认定；2：已撤销；3：整改（停止招生）；4：整改]
	 */
	private Integer baseState;
	/*
	 * 基地状态更新原因
	 */
	private String baseStateUpdateReason;
	/*
	 * 基地状态更新时间
	 */
	private Date baseStateUpdateTime;
	/*
	 * 是否最新状态（-1：失效；0：否；1：是）
	 */
	private Integer isNewState;

	private Date createTime;
	private Date updateTime;

	public ZyyBaseUpdateHistory() {
		super();
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getHospitalId() {
		return hospitalId;
	}

	public void setHospitalId(Long hospitalId) {
		this.hospitalId = hospitalId;
	}

	public Long getZyyBaseId() {
		return zyyBaseId;
	}

	public void setZyyBaseId(Long zyyBaseId) {
		this.zyyBaseId = zyyBaseId;
	}

	public Integer getBaseState() {
		return baseState;
	}

	public void setBaseState(Integer baseState) {
		this.baseState = baseState;
	}

	public String getBaseStateUpdateReason() {
		return baseStateUpdateReason;
	}

	public void setBaseStateUpdateReason(String baseStateUpdateReason) {
		this.baseStateUpdateReason = baseStateUpdateReason == null ? null : baseStateUpdateReason.trim();
	}

	public Date getBaseStateUpdateTime() {
		return baseStateUpdateTime;
	}

	public void setBaseStateUpdateTime(Date baseStateUpdateTime) {
		this.baseStateUpdateTime = baseStateUpdateTime;
	}

	public Integer getIsNewState() {
		return isNewState;
	}

	public void setIsNewState(Integer isNewState) {
		this.isNewState = isNewState;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ZyyBaseUpdateHistory other = (ZyyBaseUpdateHistory) obj;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		return true;
	}

	@Override
	public String toString() {
		return "ZyyBaseUpdateHistory [id=" + id + ", hospitalId=" + hospitalId
				+ ", zyyBaseId=" + zyyBaseId + ", baseState=" + baseState
				+ ", baseStateUpdateReason=" + baseStateUpdateReason
				+ ", baseStateUpdateTime=" + baseStateUpdateTime
				+ ", isNewState=" + isNewState + ", createTime=" + createTime
				+ ", updateTime=" + updateTime + "]";
	}

}
