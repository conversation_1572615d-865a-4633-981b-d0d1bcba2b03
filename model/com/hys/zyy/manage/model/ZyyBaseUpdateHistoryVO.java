package com.hys.zyy.manage.model;

import java.util.Date;

public class ZyyBaseUpdateHistoryVO extends ZyyBaseUpdateHistory {
	/*
	 * 医院名称
	 */
	private String hospitalName;
	/*
	 * 专业名称
	 */
	private String baseName;
	/*
	 * 基地状态
	 */
	private String baseStateStr;
	/*
	 * 基地认证时间
	 */
	private Date baseCertificationTime;
	private String baseCertificationTimeStr;
	
	private String baseStateUpdateTimeStr;
	/*
	 * zyy_base_update_history表数据操作类型：INSERT OR UPDATE
	 */
	private String operateType;
	
	private Integer[] baseStateArr;

	public String getHospitalName() {
		return hospitalName;
	}

	public void setHospitalName(String hospitalName) {
		this.hospitalName = hospitalName == null ? null : hospitalName.trim();
	}

	public String getBaseName() {
		return baseName;
	}

	public void setBaseName(String baseName) {
		this.baseName = baseName == null ? null : baseName.trim();
	}

	public String getBaseStateStr() {
		return baseStateStr;
	}

	public void setBaseStateStr(String baseStateStr) {
		this.baseStateStr = baseStateStr == null ? null : baseStateStr.trim();
	}

	public Date getBaseCertificationTime() {
		return baseCertificationTime;
	}

	public void setBaseCertificationTime(Date baseCertificationTime) {
		this.baseCertificationTime = baseCertificationTime;
	}

	public String getBaseStateUpdateTimeStr() {
		return baseStateUpdateTimeStr;
	}

	public void setBaseStateUpdateTimeStr(String baseStateUpdateTimeStr) {
		this.baseStateUpdateTimeStr = baseStateUpdateTimeStr == null ? null : baseStateUpdateTimeStr.trim();
	}

	public String getBaseCertificationTimeStr() {
		return baseCertificationTimeStr;
	}

	public void setBaseCertificationTimeStr(String baseCertificationTimeStr) {
		this.baseCertificationTimeStr = baseCertificationTimeStr == null ? null : baseCertificationTimeStr.trim();
	}

	public String getOperateType() {
		return operateType;
	}

	public void setOperateType(String operateType) {
		this.operateType = operateType == null ? null : operateType.trim();
	}

	public Integer[] getBaseStateArr() {
		return baseStateArr;
	}

	public void setBaseStateArr(Integer[] baseStateArr) {
		this.baseStateArr = baseStateArr;
	}

}
