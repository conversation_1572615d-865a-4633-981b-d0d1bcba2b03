package com.hys.zyy.manage.model;

import java.util.List;

import com.hys.security.model.Resource;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：张伟清 2012-04-12
 * 
 * 描述：
 * 
 * 说明:
 */

public class ZyyBaseVO extends ZyyBase {

	private static final long serialVersionUID = -8929934392117588370L;

	/**
	 * 科室列表
	 */
	private List<ZyyDeptVO> deptList;
	
	/**
	 * 轮转时间列表(基地可以有多个轮转时间,每个时间下有多个科室和时间)
	 */
	private List<ZyyDeptCycleTime> cycleTimelList;

	/**
	 * 基地名称
	 */
	private String stdName;
	
	/**
	 * 招录基地ID
	 */
	private Long recruitBaseId ;

	/**
	 * 学员列表
	 */
	private List<ZyyUserExtendVO> userList;
	/**
	 * 学员列表总表
	 */
	private List<ZyyCycleTableResidency> userTabList;
	/**
	 * 基地基本信息ids
	 */
	private Long[] ids;

	/**
	 * 角色id
	 */
	private Long roleId;

	/**
	 * 角色名称
	 */
	private String roleName;

	/**
	 * 资源列表
	 */
	private List<Resource> resourceList;
	
	/**
	 * 连排list 
	 */
	private List<ZyyBaseContinuityVO> zyyBaseContinuityVOList;
	
	/**
	 * 优先排列表list
	 */
	private List<ZyyDeptCyclePriorityVO> zyyDeptCyclePriorityList;
	
	/**
	 * 选修科室列表list
	 */
	private List<ZyyDeptCycleElectiveVO> zyyDeptCycleElectiveList;
	
	/**
	 * 轮转时间汇总
	 */
	private Double allCycleTime ;
	
	/**
	 * 科室编号
	 */
	private Integer deptNumber ;
	
	/**
	 * 其地人员考勤记录
	 */
	private List<ZyyResidencyAttendanceVO> AttendanceList;
	
	/**
	 * 计数器
	 */
	private int counter ;
	/**
	 * 迟到人数
	 */
	private int lateNumber;
	/**
	 * 早退人数
	 */
	private int leaveEarlyNumber;
	/**
	 * 旷工人数
	 */
	private int absenteeismNumber;
	/**
	 * 请假人数
	 */
	private int leaveNumber;
	/**
	 * 全勤人数
	 */
	private int fullNumber;
	
	/**
	 * 科室类型 中医 西医
	 */
	private int hospType;
	/**
	 * 教学管理统计：活动类型列表
	 */
	private List<ZyyActivityTypeVO> actibityTypeList;
	/**
	 * BDP字典值CODE
	 */
	private String bdpDicCode;

	public List<ZyyActivityTypeVO> getActibityTypeList() {
		return actibityTypeList;
	}

	public void setActibityTypeList(List<ZyyActivityTypeVO> actibityTypeList) {
		this.actibityTypeList = actibityTypeList;
	}

	public String getBdpDicCode() {
		return bdpDicCode;
	}

	public void setBdpDicCode(String bdpDicCode) {
		this.bdpDicCode = bdpDicCode == null ? null : bdpDicCode.trim();
	}

	public int getHospType() {
		return hospType;
	}

	public void setHospType(int hospType) {
		this.hospType = hospType;
	}

	public ZyyBaseVO() {
	}
	
	public ZyyBaseVO(ZyyBase base) {
		setId(base.getId());
		setBaseStdId(base.getBaseStdId());
		setHospitalId(base.getHospitalId());
		setDescribe(base.getDescribe());
		setSubmitter(base.getSubmitter());
		setCredentials(base.getCredentials());
		setStatus(base.getStatus());
		setVersion(base.getVersion());
		setDepartmentInstruct(base.getDepartmentInstruct());
		setHospitalInstruct(base.getHospitalInstruct());
		setCardType(base.getCardType());
		setLastUpdateDate(base.getLastUpdateDate());
		setApprovalUserTypeId(base.getApprovalUserTypeId());
		setName(base.getName());
		setAliasName(base.getAliasName());
	}

	public List<Resource> getResourceList() {
		return resourceList;
	}

	public void setResourceList(List<Resource> resourceList) {
		this.resourceList = resourceList;
	}

	public Long getRoleId() {
		return roleId;
	}

	public void setRoleId(Long roleId) {
		this.roleId = roleId;
	}

	public String getRoleName() {
		return roleName;
	}

	public void setRoleName(String roleName) {
		this.roleName = roleName;
	}

	public Long[] getIds() {
		return ids;
	}

	public void setIds(Long[] ids) {
		this.ids = ids;
	}

	public String getStdName() {
		return stdName;
	}

	public void setStdName(String stdName) {
		this.stdName = stdName;
	}

	public List<ZyyDeptVO> getDeptList() {
		return deptList;
	}

	public void setDeptList(List<ZyyDeptVO> deptList) {
		this.deptList = deptList;
	}

	public List<ZyyUserExtendVO> getUserList() {
		return userList;
	}

	public void setUserList(List<ZyyUserExtendVO> userList) {
		this.userList = userList;
	}
	
	public List<ZyyBaseContinuityVO> getZyyBaseContinuityVOList() {
		return zyyBaseContinuityVOList;
	}

	public void setZyyBaseContinuityVOList(
			List<ZyyBaseContinuityVO> zyyBaseContinuityVOList) {
		this.zyyBaseContinuityVOList = zyyBaseContinuityVOList;
	}

	public Double getAllCycleTime() {
		return allCycleTime;
	}

	public void setAllCycleTime(Double allCycleTime) {
		this.allCycleTime = allCycleTime;
	}

	public Integer getDeptNumber() {
		return deptNumber;
	}

	public void setDeptNumber(Integer deptNumber) {
		this.deptNumber = deptNumber;
	}

	public int getCounter() {
		return counter;
	}

	public void setCounter(int counter) {
		this.counter = counter;
	}

	public Long getRecruitBaseId() {
		return recruitBaseId;
	}

	public void setRecruitBaseId(Long recruitBaseId) {
		this.recruitBaseId = recruitBaseId;
	}

	public List<ZyyCycleTableResidency> getUserTabList() {
		return userTabList;
	}

	public void setUserTabList(List<ZyyCycleTableResidency> userTabList) {
		this.userTabList = userTabList;
	}
	

	public List<ZyyDeptCycleTime> getCycleTimelList() {
		return cycleTimelList;
	}

	public void setCycleTimelList(List<ZyyDeptCycleTime> cycleTimelList) {
		this.cycleTimelList = cycleTimelList;
	}

	public List<ZyyDeptCyclePriorityVO> getZyyDeptCyclePriorityList() {
		return zyyDeptCyclePriorityList;
	}

	public void setZyyDeptCyclePriorityList(
			List<ZyyDeptCyclePriorityVO> zyyDeptCyclePriorityList) {
		this.zyyDeptCyclePriorityList = zyyDeptCyclePriorityList;
	}

	public int getLateNumber() {
		return lateNumber;
	}

	public void setLateNumber(int lateNumber) {
		this.lateNumber = lateNumber;
	}

	public int getLeaveEarlyNumber() {
		return leaveEarlyNumber;
	}

	public void setLeaveEarlyNumber(int leaveEarlyNumber) {
		this.leaveEarlyNumber = leaveEarlyNumber;
	}

	public int getAbsenteeismNumber() {
		return absenteeismNumber;
	}

	public void setAbsenteeismNumber(int absenteeismNumber) {
		this.absenteeismNumber = absenteeismNumber;
	}

	public int getLeaveNumber() {
		return leaveNumber;
	}

	public void setLeaveNumber(int leaveNumber) {
		this.leaveNumber = leaveNumber;
	}

	public int getFullNumber() {
		return fullNumber;
	}

	public void setFullNumber(int fullNumber) {
		this.fullNumber = fullNumber;
	}

	public List<ZyyResidencyAttendanceVO> getAttendanceList() {
		return AttendanceList;
	}

	public void setAttendanceList(List<ZyyResidencyAttendanceVO> attendanceList) {
		AttendanceList = attendanceList;
	}

	public List<ZyyDeptCycleElectiveVO> getZyyDeptCycleElectiveList() {
		return zyyDeptCycleElectiveList;
	}

	public void setZyyDeptCycleElectiveList(
			List<ZyyDeptCycleElectiveVO> zyyDeptCycleElectiveList) {
		this.zyyDeptCycleElectiveList = zyyDeptCycleElectiveList;
	}
}
