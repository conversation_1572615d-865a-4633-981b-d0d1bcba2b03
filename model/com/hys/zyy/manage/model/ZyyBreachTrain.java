package com.hys.zyy.manage.model;

import com.hys.zyy.manage.util.annotation.Column;
import com.hys.zyy.manage.util.annotation.Id;
import com.hys.zyy.manage.util.annotation.Table;

import java.util.Date;

/**
 * 违约学员管理
 * <AUTHOR>
 *
 */
@Table("ZYY_BREACH_TRAIN")
public class ZyyBreachTrain extends ZyyBaseObject {

	private static final long serialVersionUID = 523059867133134130L;

	@Id("ZYY_BREACH_TRAIN_SEQ.nextval")
	@Column("id")
	private Long id;

	@Column("flag")
	private Long flag;

	@Column("real_name")
	private String realName;

	@Column("sex")
	private String sex;

	@Column("certificate_no")
	private String certificateNo;

	@Column("city")
	private String city;

	@Column("city_county")
	private String cityCounty;

	@Column("org_name")
	private String orgName;

	@Column("subject_name")
	private String subjectName;

	@Column("subject_start_time")
	private String subjectStartTime;

	@Column("graduate_time")
	private Date graduateTime;

	@Column("graduate_no")
	private String graduateNo;

	@Column("cause")
	private String cause;

	@Column("is_agreement")
	private String isAgreement;

	@Column("register_no")
	private String registerNo;

	@Column("is_employ")
	private String isEmploy;

	@Column("agreement_start_time")
	private String agreementStartTime;

	@Column("agreement_end_time")
	private String agreementEndTime;

	@Column("remark")
	private String remark;

	@Column("CREATE_DATE")
	private Date createDate;

	@Column("CREATE_USER_ID")
	private Long createUserId;

	@Column("UPDATE_DATE")
	private Date updateDate;

	@Column("UPDATE_USER_ID")
	private Long updateUserId;


	public Long getUpdateUserId() {
		return updateUserId;
	}

	public void setUpdateUserId(Long updateUserId) {
		this.updateUserId = updateUserId;
	}

	public Long getCreateUserId() {
		return createUserId;
	}

	public void setCreateUserId(Long createUserId) {
		this.createUserId = createUserId;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Date getUpdateDate() {
		return updateDate;
	}

	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getFlag() {
		return flag;
	}
	public void setFlag(Long flag) {
		this.flag = flag;
	}
	public String getRealName() {
		return realName;
	}
	public void setRealName(String realName) {
		this.realName = realName;
	}
	public String getSex() {
		return sex;
	}
	public void setSex(String sex) {
		this.sex = sex;
	}
	public String getCertificateNo() {
		return certificateNo;
	}
	public void setCertificateNo(String certificateNo) {
		this.certificateNo = certificateNo;
	}
	public String getCity() {
		return city;
	}
	public void setCity(String city) {
		this.city = city;
	}
	public String getCityCounty() {
		return cityCounty;
	}
	public void setCityCounty(String cityCounty) {
		this.cityCounty = cityCounty;
	}
	public String getOrgName() {
		return orgName;
	}
	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}
	public String getSubjectName() {
		return subjectName;
	}
	public void setSubjectName(String subjectName) {
		this.subjectName = subjectName;
	}
	public String getSubjectStartTime() {
		return subjectStartTime;
	}
	public void setSubjectStartTime(String subjectStartTime) {
		this.subjectStartTime = subjectStartTime;
	}
	public Date getGraduateTime() {
		return graduateTime;
	}
	public void setGraduateTime(Date graduateTime) {
		this.graduateTime = graduateTime;
	}
	public String getGraduateNo() {
		return graduateNo;
	}
	public void setGraduateNo(String graduateNo) {
		this.graduateNo = graduateNo;
	}
	public String getCause() {
		return cause;
	}
	public void setCause(String cause) {
		this.cause = cause;
	}
	public String getIsAgreement() {
		return isAgreement;
	}
	public void setIsAgreement(String isAgreement) {
		this.isAgreement = isAgreement;
	}
	public String getRegisterNo() {
		return registerNo;
	}
	public void setRegisterNo(String registerNo) {
		this.registerNo = registerNo;
	}
	public String getIsEmploy() {
		return isEmploy;
	}
	public void setIsEmploy(String isEmploy) {
		this.isEmploy = isEmploy;
	}
	public String getAgreementStartTime() {
		return agreementStartTime;
	}
	public void setAgreementStartTime(String agreementStartTime) {
		this.agreementStartTime = agreementStartTime;
	}
	public String getAgreementEndTime() {
		return agreementEndTime;
	}
	public void setAgreementEndTime(String agreementEndTime) {
		this.agreementEndTime = agreementEndTime;
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	
}


