package com.hys.zyy.manage.model;

import java.util.Date;

/**
 * 病例管理
 * <AUTHOR>
 * @date 2018-12-5下午2:31:48
 */
public class ZyyCase extends ZyyBaseObject {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	//主键
	private Long id;	 		
	//医院ID
	private Long hospitalId;	 		
	//科室ID
	private Long deptId;	  
	//学员ID
	private Long residencyId;	 
	//开始时间
	private Date startDate;	
	private String startDateStr;
	//结束时间
	private Date endDate;	
	private String endDateStr;
	//病历模版
	private Long caseTempId;	 
	//病例类型 
	private Integer caseType;
	//病历号
	private String caseNo;	 
	//病人姓名
	private String patientName; 
	//接诊日期
	private String recieveDate; 
	//提交次数
	private Integer submitTime;	 
	//创建时间
	private Date createDate;
	//学员名称
	private String realName;
	
	//不存库字段
	//最终审核状态
	private Integer finalStatus;
	
	//培训专业
	private String baseName;
	
	//培训基地名称
	private String orgName;
	
	//证件号码
	private String certificateNo;
	
	//培训年级
	private String yearName;
	
	//培训年限
	private Integer schoolSystem;
	
	
	public String getBaseName() {
		return baseName;
	}
	public void setBaseName(String baseName) {
		this.baseName = baseName;
	}
	public String getOrgName() {
		return orgName;
	}
	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}
	public String getCertificateNo() {
		return certificateNo;
	}
	public void setCertificateNo(String certificateNo) {
		this.certificateNo = certificateNo;
	}
	public String getYearName() {
		return yearName;
	}
	public void setYearName(String yearName) {
		this.yearName = yearName;
	}
	public Integer getSchoolSystem() {
		return schoolSystem;
	}
	public void setSchoolSystem(Integer schoolSystem) {
		this.schoolSystem = schoolSystem;
	}
	public String getRealName() {
		return realName;
	}
	public void setRealName(String realName) {
		this.realName = realName;
	}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getHospitalId() {
		return hospitalId;
	}
	public void setHospitalId(Long hospitalId) {
		this.hospitalId = hospitalId;
	}
	public Long getDeptId() {
		return deptId;
	}
	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}
	public Long getResidencyId() {
		return residencyId;
	}
	public void setResidencyId(Long residencyId) {
		this.residencyId = residencyId;
	}
	public Date getStartDate() {
		return startDate;
	}
	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}
	public String getStartDateStr() {
		return startDateStr;
	}
	public void setStartDateStr(String startDateStr) {
		this.startDateStr = startDateStr;
	}
	public Date getEndDate() {
		return endDate;
	}
	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}
	public String getEndDateStr() {
		return endDateStr;
	}
	public void setEndDateStr(String endDateStr) {
		this.endDateStr = endDateStr;
	}
	public Long getCaseTempId() {
		return caseTempId;
	}
	public void setCaseTempId(Long caseTempId) {
		this.caseTempId = caseTempId;
	}
	public Integer getCaseType() {
		return caseType;
	}
	public void setCaseType(Integer caseType) {
		this.caseType = caseType;
	}
	public String getCaseNo() {
		return caseNo;
	}
	public void setCaseNo(String caseNo) {
		this.caseNo = caseNo;
	}
	public String getPatientName() {
		return patientName;
	}
	public void setPatientName(String patientName) {
		this.patientName = patientName;
	}
	public String getRecieveDate() {
		return recieveDate;
	}
	public void setRecieveDate(String recieveDate) {
		this.recieveDate = recieveDate;
	}
	public Integer getSubmitTime() {
		return submitTime;
	}
	public void setSubmitTime(Integer submitTime) {
		this.submitTime = submitTime;
	}
	public Date getCreateDate() {
		return createDate;
	}
	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}
	public Integer getFinalStatus() {
		return finalStatus;
	}
	public void setFinalStatus(Integer finalStatus) {
		this.finalStatus = finalStatus;
	}
}
