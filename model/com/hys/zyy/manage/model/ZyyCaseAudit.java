package com.hys.zyy.manage.model;

import java.util.Date;
import java.util.List;

public class ZyyCaseAudit {

	//主键ID
	private Long id;

	//病例表ID
	private Long caseId;
	
	//医院id
	private Long orgId;
	
	//学员姓名
	private String realName;
	
	//轮转科室名称
	private String cycleDeptName;
	
	//轮转开始时间
	private Date cycleStartTime; 
	
	//轮转结束时间
	private Date cycleEndTime;
	
	//病例号
	private Integer caseNumber;
	
	//病例类型 1：住院病历 2：大病历 3：门诊病例
	private Integer caseType;
	
	//病例ID
	private Long caseTempId;
	
	//提交下标数
	private Integer submitIndex;
	
	//提交时间
	private String submitTime;
	
	//审核者类型 11：带教 9：科室 5：培训基地
	private Integer verifiersType;
	
	//审核状态  0：未审核  1：审核通过 2：审核未通过
	private Integer status;
	
	//是否当前流程审核者 1：是 0：否
	private Integer isCurrentAuditor;
	
	//审核过程级别 
	private Integer processLevel;
	
	//审核人ID
	private Long auditorId;
	
	//分数
	private Integer score;
	
	//审核意见
	private String comments;
	
	//审核时间
	private String auditorTime;
	
	//终审状态 1：审核通过 2：审核未通过
	private Integer finalStatus;
	
	//培训年级
	private String year;
	
	//培训年限
	private Integer schoolSystem;
	
	//审核记录
	private List<ZyyCaseAudit> recordList;
	
	//审核人名称
	private String auditUserName;
	
	public String getAuditUserName() {
		return auditUserName;
	}

	public void setAuditUserName(String auditUserName) {
		this.auditUserName = auditUserName;
	}

	public List<ZyyCaseAudit> getRecordList() {
		return recordList;
	}

	public void setRecordList(List<ZyyCaseAudit> recordList) {
		this.recordList = recordList;
	}

	public Long getCaseTempId() {
		return caseTempId;
	}

	public void setCaseTempId(Long caseTempId) {
		this.caseTempId = caseTempId;
	}

	public String getYear() {
		return year;
	}

	public void setYear(String year) {
		this.year = year;
	}

	public Integer getSchoolSystem() {
		return schoolSystem;
	}

	public void setSchoolSystem(Integer schoolSystem) {
		this.schoolSystem = schoolSystem;
	}

	public Integer getFinalStatus() {
		return finalStatus;
	}

	public void setFinalStatus(Integer finalStatus) {
		this.finalStatus = finalStatus;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getCaseId() {
		return caseId;
	}

	public void setCaseId(Long caseId) {
		this.caseId = caseId;
	}

	public Long getOrgId() {
		return orgId;
	}

	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}

	public String getRealName() {
		return realName;
	}

	public void setRealName(String realName) {
		this.realName = realName;
	}

	public String getCycleDeptName() {
		return cycleDeptName;
	}

	public void setCycleDeptName(String cycleDeptName) {
		this.cycleDeptName = cycleDeptName;
	}

	
	public Date getCycleStartTime() {
		return cycleStartTime;
	}

	public void setCycleStartTime(Date cycleStartTime) {
		this.cycleStartTime = cycleStartTime;
	}

	public Date getCycleEndTime() {
		return cycleEndTime;
	}

	public void setCycleEndTime(Date cycleEndTime) {
		this.cycleEndTime = cycleEndTime;
	}

	public Integer getCaseNumber() {
		return caseNumber;
	}

	public void setCaseNumber(Integer caseNumber) {
		this.caseNumber = caseNumber;
	}

	public Integer getCaseType() {
		return caseType;
	}

	public void setCaseType(Integer caseType) {
		this.caseType = caseType;
	}

	public Integer getSubmitIndex() {
		return submitIndex;
	}

	public void setSubmitIndex(Integer submitIndex) {
		this.submitIndex = submitIndex;
	}

	public String getSubmitTime() {
		return submitTime;
	}

	public void setSubmitTime(String submitTime) {
		this.submitTime = submitTime;
	}

	public Integer getVerifiersType() {
		return verifiersType;
	}

	public void setVerifiersType(Integer verifiersType) {
		this.verifiersType = verifiersType;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Integer getIsCurrentAuditor() {
		return isCurrentAuditor;
	}

	public void setIsCurrentAuditor(Integer isCurrentAuditor) {
		this.isCurrentAuditor = isCurrentAuditor;
	}

	public Integer getProcessLevel() {
		return processLevel;
	}

	public void setProcessLevel(Integer processLevel) {
		this.processLevel = processLevel;
	}

	public Long getAuditorId() {
		return auditorId;
	}

	public void setAuditorId(Long auditorId) {
		this.auditorId = auditorId;
	}

	public Integer getScore() {
		return score;
	}

	public void setScore(Integer score) {
		this.score = score;
	}

	public String getComments() {
		return comments;
	}

	public void setComments(String comments) {
		this.comments = comments;
	}

	public String getAuditorTime() {
		return auditorTime;
	}

	public void setAuditorTime(String auditorTime) {
		this.auditorTime = auditorTime;
	}
	
}
