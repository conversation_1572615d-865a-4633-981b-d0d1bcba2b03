package com.hys.zyy.manage.model;

public class ZyyCaseAuditQuery extends ZyyBaseObject{

	private static final long serialVersionUID = 1L;
	
	//本级审核状态
	private Integer status;
	
	//姓名
	private String realName;
	
	//科室ID
	private Long deptId;
	
	//机构ID
	private Long orgId;
	
	//用户类型
	private Integer userType;
	
	//登陆人ID
	private Long zyyUserId;
	
	//年级Id
	private Long yearId;
	
	//年限
	private Integer schoolSystem;
	
	//最终审核结果
	private Integer finalStatus;
	
	public Integer getFinalStatus() {
		return finalStatus;
	}

	public void setFinalStatus(Integer finalStatus) {
		this.finalStatus = finalStatus;
	}

	public Integer getSchoolSystem() {
		return schoolSystem;
	}

	public void setSchoolSystem(Integer schoolSystem) {
		this.schoolSystem = schoolSystem;
	}

	public Long getYearId() {
		return yearId;
	}

	public void setYearId(Long yearId) {
		this.yearId = yearId;
	}

	public Long getZyyUserId() {
		return zyyUserId;
	}

	public void setZyyUserId(Long zyyUserId) {
		this.zyyUserId = zyyUserId;
	}

	public Integer getUserType() {
		return userType;
	}

	public void setUserType(Integer userType) {
		this.userType = userType;
	}

	public Long getOrgId() {
		return orgId;
	}

	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public String getRealName() {
		return realName;
	}

	public void setRealName(String realName) {
		this.realName = realName;
	}

	public Long getDeptId() {
		return deptId;
	}

	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}

	
}
