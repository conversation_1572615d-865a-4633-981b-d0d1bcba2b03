package com.hys.zyy.manage.model;

import java.util.List;

/**
 * 病例审核主表
 * <AUTHOR>
 *
 */
public class ZyyCaseProcess {
	
	//主键ID
	private Long id;
	
	//机构ID
	private Long orgId;
	
	//是否逐级审核   1：是    0：否
	private Integer isCascade;
	
	//病例审核详细列表
	private List<ZyyCaseProcessDetail> list;


	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getOrgId() {
		return orgId;
	}

	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}

	public Integer getIsCascade() {
		return isCascade;
	}

	public void setIsCascade(Integer isCascade) {
		this.isCascade = isCascade;
	}

	public List<ZyyCaseProcessDetail> getList() {
		return list;
	}

	public void setList(List<ZyyCaseProcessDetail> list) {
		this.list = list;
	}
	
}
