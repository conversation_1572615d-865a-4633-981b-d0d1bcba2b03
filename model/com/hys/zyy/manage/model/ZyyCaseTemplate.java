package com.hys.zyy.manage.model;

import java.util.List;

/**
 * 病例模板
 * <AUTHOR>
 *
 */
public class ZyyCaseTemplate extends ZyyBaseObject{

	//主键
	private Long id;
	
	//模板名称
	private String name;

	//机构ID
	private Long orgId;

	//模板类型 1：住院病历模板 2：大病历模板 3：门诊病历模板
	private Integer type;
	
	//状态 1：正常 2：禁用
	private Integer status;
	
	//模板对应的内容列表
	private List<ZyyCaseTemplateBase> templateBasesList;
	
	public List<ZyyCaseTemplateBase> getTemplateBasesList() {
		return templateBasesList;
	}

	public void setTemplateBasesList(List<ZyyCaseTemplateBase> templateBasesList) {
		this.templateBasesList = templateBasesList;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Long getOrgId() {
		return orgId;
	}

	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}
	
}
