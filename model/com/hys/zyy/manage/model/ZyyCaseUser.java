package com.hys.zyy.manage.model;

import java.util.Date;

public class ZyyCaseUser {
     //主键
    private Long id;

     //外键
    private Long caseId;

     //姓名
    private String name;

     //性别
    private Integer sex;

     //年龄
    private Integer age;

     //婚姻
    private String marital;

     //出生地
    private String birthPlace;

     //民族
    private String race;

     //职业
    private String occupation;

     //病史叙述者
    private String informant;

     //可靠程度
    private String reliable;

     //工作单位
    private String company;

     //现住址
    private String address;

     //入院日期
    private Date admissionDate;
    private String admissionDateStr;

     //记录日期
    private Date recordDate;
    private String recordDateStr;

     //主诉
    private String chiefComplaint;

     //现病史
    private String presentHistory;

     //既往史
    private String passHistory;

     //家族史
    private String familyHistory;

     //个人史
    private String personHistory;

     //婚姻史
    private String maritalHistory;

     //月经及生育史
    private String childHistory;

     //就诊科室
    private String visitDepart;

     //就诊日期
    private Date visitDate;
    private String visitDateStr;

     //联系电话
    private String mobile;

     //过敏史
    private String allergyHistory;

     //体格检查
    private String bodyCheck;

     //临床诊断
    private String diagnosis;

     //处理意见
    private String suggestion;

     //处方
    private String prescription;

     //辅助检查
    private String assitCheck;

     //创建时间
    private Date createTime;
    
    //不存库字段
    //病历号
  	private String caseNo;	 
  	//接诊日期
  	private String recieveDate; 

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCaseId() {
        return caseId;
    }

    public void setCaseId(Long caseId) {
        this.caseId = caseId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getSex() {
        return sex;
    }

    public void setSex(Integer sex) {
        this.sex = sex;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public String getMarital() {
        return marital;
    }

    public void setMarital(String marital) {
        this.marital = marital;
    }

    public String getBirthPlace() {
        return birthPlace;
    }

    public void setBirthPlace(String birthPlace) {
        this.birthPlace = birthPlace;
    }

    public String getRace() {
        return race;
    }

    public void setRace(String race) {
        this.race = race;
    }

    public String getOccupation() {
        return occupation;
    }

    public void setOccupation(String occupation) {
        this.occupation = occupation;
    }

    public String getInformant() {
        return informant;
    }

    public void setInformant(String informant) {
        this.informant = informant;
    }

    public String getReliable() {
        return reliable;
    }

    public void setReliable(String reliable) {
        this.reliable = reliable;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Date getAdmissionDate() {
        return admissionDate;
    }

    public void setAdmissionDate(Date admissionDate) {
        this.admissionDate = admissionDate;
    }

    public Date getRecordDate() {
        return recordDate;
    }

    public void setRecordDate(Date recordDate) {
        this.recordDate = recordDate;
    }

    public String getChiefComplaint() {
        return chiefComplaint;
    }

    public void setChiefComplaint(String chiefComplaint) {
        this.chiefComplaint = chiefComplaint;
    }

    public String getPresentHistory() {
        return presentHistory;
    }

    public void setPresentHistory(String presentHistory) {
        this.presentHistory = presentHistory;
    }

    public String getPassHistory() {
        return passHistory;
    }

    public void setPassHistory(String passHistory) {
        this.passHistory = passHistory;
    }

    public String getFamilyHistory() {
        return familyHistory;
    }

    public void setFamilyHistory(String familyHistory) {
        this.familyHistory = familyHistory;
    }

    public String getPersonHistory() {
        return personHistory;
    }

    public void setPersonHistory(String personHistory) {
        this.personHistory = personHistory;
    }

    public String getMaritalHistory() {
        return maritalHistory;
    }

    public void setMaritalHistory(String maritalHistory) {
        this.maritalHistory = maritalHistory;
    }

    public String getChildHistory() {
        return childHistory;
    }

    public void setChildHistory(String childHistory) {
        this.childHistory = childHistory;
    }

    public String getVisitDepart() {
        return visitDepart;
    }

    public void setVisitDepart(String visitDepart) {
        this.visitDepart = visitDepart;
    }

    public Date getVisitDate() {
        return visitDate;
    }

    public void setVisitDate(Date visitDate) {
        this.visitDate = visitDate;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getAllergyHistory() {
        return allergyHistory;
    }

    public void setAllergyHistory(String allergyHistory) {
        this.allergyHistory = allergyHistory;
    }

    public String getBodyCheck() {
        return bodyCheck;
    }

    public void setBodyCheck(String bodyCheck) {
        this.bodyCheck = bodyCheck;
    }

    public String getDiagnosis() {
        return diagnosis;
    }

    public void setDiagnosis(String diagnosis) {
        this.diagnosis = diagnosis;
    }

    public String getSuggestion() {
        return suggestion;
    }

    public void setSuggestion(String suggestion) {
        this.suggestion = suggestion;
    }

    public String getPrescription() {
        return prescription;
    }

    public void setPrescription(String prescription) {
        this.prescription = prescription;
    }

    public String getAssitCheck() {
        return assitCheck;
    }

    public void setAssitCheck(String assitCheck) {
        this.assitCheck = assitCheck;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

	public String getCaseNo() {
		return caseNo;
	}

	public void setCaseNo(String caseNo) {
		this.caseNo = caseNo;
	}

	public String getRecieveDate() {
		return recieveDate;
	}

	public void setRecieveDate(String recieveDate) {
		this.recieveDate = recieveDate;
	}

	public String getAdmissionDateStr() {
		return admissionDateStr;
	}

	public void setAdmissionDateStr(String admissionDateStr) {
		this.admissionDateStr = admissionDateStr;
	}

	public String getRecordDateStr() {
		return recordDateStr;
	}

	public void setRecordDateStr(String recordDateStr) {
		this.recordDateStr = recordDateStr;
	}

	public String getVisitDateStr() {
		return visitDateStr;
	}

	public void setVisitDateStr(String visitDateStr) {
		this.visitDateStr = visitDateStr;
	}
}