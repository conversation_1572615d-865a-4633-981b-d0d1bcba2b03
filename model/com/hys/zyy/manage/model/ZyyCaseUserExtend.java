package com.hys.zyy.manage.model;

import java.util.Date;

public class ZyyCaseUserExtend {
     //主键
    private Long id;

     //外键
    private Long caseId;

     //系统回顾
    private String systemReview;

     //呼吸系统
    private String breathSystem;

     //循环系统
    private String cadiovascularSystem;

     //消化系统
    private String digestiveSystem;

     //泌尿系统
    private String urinarySystem;

     //血液系统
    private String bloodSystem;

     //内分泌系统及代谢
    private String endocrineSystem;

     //神经精神系统
    private String nervousSpiritSystem;

     //肌肉骨骼系统
    private String muscleBoneSystem;

     //体温
    private String temperature;

     //脉搏
    private Integer pulseRate;

     //血压
    private String bloodPresure;

     //体重
    private Double weight;

     //一般情况
    private String generalInfo;

     //皮肤黏膜
    private String skin;

     //淋巴结
    private String lymphNodes;

     //头部
    private String head;

     //颈部
    private String neck;

     //胸部
    private String chest;

     //肺部
    private String lungs;

     //心脏
    private String heart;

     //周围血管
    private String peripheralVessals;

     //腹部
    private String abdomen;

     //肛门直肠
    private String anorectal;

     //生殖器
    private String genitals;

     //骨骼肌肉
    private String boneMuscle;

     //神经系统
    private String nervousSystem;

     //专科情况
    private String specialSituation;

     //病历摘要
    private String caseSummary;

     //初步诊断
    private String firstDiagnosis;

     //修订诊断
    private String editDiagnosis;

     //出院诊断
    private String outDiagnosis;

     //创建时间
    private Date createDate;
    
    //不存库字段
    //病历号
  	private String caseNo;	 
  	//接诊日期
  	private String recieveDate; 
  	//呼吸
  	private String breath;
  	 //辅助检查
    private String assitCheck;
    //临床诊断
    private String diagnosis;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCaseId() {
        return caseId;
    }

    public void setCaseId(Long caseId) {
        this.caseId = caseId;
    }

    public String getSystemReview() {
        return systemReview;
    }

    public void setSystemReview(String systemReview) {
        this.systemReview = systemReview;
    }

    public String getBreathSystem() {
        return breathSystem;
    }

    public void setBreathSystem(String breathSystem) {
        this.breathSystem = breathSystem;
    }

    public String getCadiovascularSystem() {
        return cadiovascularSystem;
    }

    public void setCadiovascularSystem(String cadiovascularSystem) {
        this.cadiovascularSystem = cadiovascularSystem;
    }

    public String getDigestiveSystem() {
        return digestiveSystem;
    }

    public void setDigestiveSystem(String digestiveSystem) {
        this.digestiveSystem = digestiveSystem;
    }

    public String getUrinarySystem() {
        return urinarySystem;
    }

    public void setUrinarySystem(String urinarySystem) {
        this.urinarySystem = urinarySystem;
    }

    public String getBloodSystem() {
        return bloodSystem;
    }

    public void setBloodSystem(String bloodSystem) {
        this.bloodSystem = bloodSystem;
    }

    public String getEndocrineSystem() {
        return endocrineSystem;
    }

    public void setEndocrineSystem(String endocrineSystem) {
        this.endocrineSystem = endocrineSystem;
    }

    public String getNervousSpiritSystem() {
        return nervousSpiritSystem;
    }

    public void setNervousSpiritSystem(String nervousSpiritSystem) {
        this.nervousSpiritSystem = nervousSpiritSystem;
    }

    public String getMuscleBoneSystem() {
        return muscleBoneSystem;
    }

    public void setMuscleBoneSystem(String muscleBoneSystem) {
        this.muscleBoneSystem = muscleBoneSystem;
    }

    public String getTemperature() {
        return temperature;
    }

    public void setTemperature(String temperature) {
        this.temperature = temperature;
    }

    public Integer getPulseRate() {
        return pulseRate;
    }

    public void setPulseRate(Integer pulseRate) {
        this.pulseRate = pulseRate;
    }
   
    public String getBloodPresure() {
		return bloodPresure;
	}

	public void setBloodPresure(String bloodPresure) {
		this.bloodPresure = bloodPresure;
	}

	public Double getWeight() {
		return weight;
	}

	public void setWeight(Double weight) {
		this.weight = weight;
	}

	public String getBreath() {
		return breath;
	}

	public void setBreath(String breath) {
		this.breath = breath;
	}

	public String getGeneralInfo() {
        return generalInfo;
    }

    public void setGeneralInfo(String generalInfo) {
        this.generalInfo = generalInfo;
    }

    public String getSkin() {
        return skin;
    }

    public void setSkin(String skin) {
        this.skin = skin;
    }

    public String getLymphNodes() {
        return lymphNodes;
    }

    public void setLymphNodes(String lymphNodes) {
        this.lymphNodes = lymphNodes;
    }

    public String getHead() {
        return head;
    }

    public void setHead(String head) {
        this.head = head;
    }

    public String getNeck() {
        return neck;
    }

    public void setNeck(String neck) {
        this.neck = neck;
    }

    public String getChest() {
        return chest;
    }

    public void setChest(String chest) {
        this.chest = chest;
    }

    public String getLungs() {
        return lungs;
    }

    public void setLungs(String lungs) {
        this.lungs = lungs;
    }

    public String getHeart() {
        return heart;
    }

    public void setHeart(String heart) {
        this.heart = heart;
    }

    public String getPeripheralVessals() {
        return peripheralVessals;
    }

    public void setPeripheralVessals(String peripheralVessals) {
        this.peripheralVessals = peripheralVessals;
    }

    public String getAbdomen() {
        return abdomen;
    }

    public void setAbdomen(String abdomen) {
        this.abdomen = abdomen;
    }

    public String getAnorectal() {
        return anorectal;
    }

    public void setAnorectal(String anorectal) {
        this.anorectal = anorectal;
    }

    public String getGenitals() {
        return genitals;
    }

    public void setGenitals(String genitals) {
        this.genitals = genitals;
    }

    public String getBoneMuscle() {
        return boneMuscle;
    }

    public void setBoneMuscle(String boneMuscle) {
        this.boneMuscle = boneMuscle;
    }

    public String getNervousSystem() {
        return nervousSystem;
    }

    public void setNervousSystem(String nervousSystem) {
        this.nervousSystem = nervousSystem;
    }

    public String getSpecialSituation() {
        return specialSituation;
    }

    public void setSpecialSituation(String specialSituation) {
        this.specialSituation = specialSituation;
    }

    public String getCaseSummary() {
        return caseSummary;
    }

    public void setCaseSummary(String caseSummary) {
        this.caseSummary = caseSummary;
    }

    public String getFirstDiagnosis() {
        return firstDiagnosis;
    }

    public void setFirstDiagnosis(String firstDiagnosis) {
        this.firstDiagnosis = firstDiagnosis;
    }

    public String getEditDiagnosis() {
        return editDiagnosis;
    }

    public void setEditDiagnosis(String editDiagnosis) {
        this.editDiagnosis = editDiagnosis;
    }

    public String getOutDiagnosis() {
        return outDiagnosis;
    }

    public void setOutDiagnosis(String outDiagnosis) {
        this.outDiagnosis = outDiagnosis;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

	public String getCaseNo() {
		return caseNo;
	}

	public void setCaseNo(String caseNo) {
		this.caseNo = caseNo;
	}

	public String getRecieveDate() {
		return recieveDate;
	}

	public void setRecieveDate(String recieveDate) {
		this.recieveDate = recieveDate;
	}

	public String getAssitCheck() {
		return assitCheck;
	}

	public void setAssitCheck(String assitCheck) {
		this.assitCheck = assitCheck;
	}

	public String getDiagnosis() {
		return diagnosis;
	}

	public void setDiagnosis(String diagnosis) {
		this.diagnosis = diagnosis;
	}
}