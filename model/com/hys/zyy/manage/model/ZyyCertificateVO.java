package com.hys.zyy.manage.model;

import java.io.Serializable;
import java.util.Date;

public class ZyyCertificateVO implements Serializable {

	private static final long serialVersionUID = -6638099499292324784L;

	private String zyySyncCertificateId;
	
	private Long zyyUserId;
	
	private String syncId;
	
	private Integer hasCertificate;
	
	private String certificateNo;
	
	private Date certificateNoGenDate;
	
	private Integer status;	
	
	private Integer certificateStatus;

	public String getZyySyncCertificateId() {
		return zyySyncCertificateId;
	}

	public void setZyySyncCertificateId(String zyySyncCertificateId) {
		this.zyySyncCertificateId = zyySyncCertificateId;
	}

	public Long getZyyUserId() {
		return zyyUserId;
	}

	public void setZyyUserId(Long zyyUserId) {
		this.zyyUserId = zyyUserId;
	}
	
	public String getSyncId() {
		return syncId;
	}

	public void setSyncId(String syncId) {
		this.syncId = syncId;
	}

	public Integer getHasCertificate() {
		return hasCertificate;
	}

	public void setHasCertificate(Integer hasCertificate) {
		this.hasCertificate = hasCertificate;
	}

	public String getCertificateNo() {
		return certificateNo;
	}

	public void setCertificateNo(String certificateNo) {
		this.certificateNo = certificateNo;
	}

	public Date getCertificateNoGenDate() {
		return certificateNoGenDate;
	}

	public void setCertificateNoGenDate(Date certificateNoGenDate) {
		this.certificateNoGenDate = certificateNoGenDate;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Integer getCertificateStatus() {
		return certificateStatus;
	}

	public void setCertificateStatus(Integer certificateStatus) {
		this.certificateStatus = certificateStatus;
	}
	
}
