package com.hys.zyy.manage.model;

import java.util.Date;

public class ZyyCheckManual extends ZyyBaseObject{
	
	private static final long serialVersionUID = 5146762942988010265L;

	private Long id;//主键Id
	
	private Long userId;//用户Id
	
	private Long baseId;//科室Id
	
	private Long deptId;//学科Id
	
	private Integer isCheckingIn;//考勤是否通过 	0:否 1:是
	
	private Integer isServiceAttitude;//服务态度、医患关系  是否通过 	0:否 1:是
	
	private Integer isWorkDuty;//工作责任心,无差错 是否通过 0:否 1:是
	
	private Integer isMedicalStyle;//医疗作风,廉洁行医 是否通过 0:否 1:是
	
	private Integer isUnity;//团结协作,遵守制度 是否通过 0:否 1:是
	
	private Integer isTreatAbility;//诊治能力 是否通过 0:否 1:是
	
	private Integer isThoughtAbility;//临床思维能力 是否通过 0:否 1:是
	
	private Integer isTeachAbility;//教学能力 是否通过 0:否 1:是
	
	private Long takeStudy;//参加各种形式学习
	
	private Integer isScientific;//参加各种科研情况 0:否 1:是
	
	private Integer isAccident;//医疗无差错事故 是否通过 0:否 1:是
	
	private Integer isRegister;//登记手册情况 是否通过 0:否 1:是
	
	private Float medicalHistory;//病历质量
	
	private Float treatSick;//接诊病人
	
	private Float collegeAbility;//专科能力
	
	private Float recipeWill;//门诊处方/病房遗嘱
	
	private Float theoryScore;//出科理论成绩 
	
	private Integer allScore;//考核总成绩 
	
	private String baseComment;//评语
	
	private Long deptStdId;//标准科室ID

	private Date createDate;//创建时间
	
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Long getDeptStdId() {
		return deptStdId;
	}

	public void setDeptStdId(Long deptStdId) {
		this.deptStdId = deptStdId;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public Long getDeptId() {
		return deptId;
	}

	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}

	public Long getBaseId() {
		return baseId;
	}

	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}

	public Integer getIsCheckingIn() {
		return isCheckingIn;
	}

	public void setIsCheckingIn(Integer isCheckingIn) {
		this.isCheckingIn = isCheckingIn;
	}

	public Integer getIsServiceAttitude() {
		return isServiceAttitude;
	}

	public void setIsServiceAttitude(Integer isServiceAttitude) {
		this.isServiceAttitude = isServiceAttitude;
	}

	public Integer getIsWorkDuty() {
		return isWorkDuty;
	}

	public void setIsWorkDuty(Integer isWorkDuty) {
		this.isWorkDuty = isWorkDuty;
	}

	public Integer getIsMedicalStyle() {
		return isMedicalStyle;
	}

	public void setIsMedicalStyle(Integer isMedicalStyle) {
		this.isMedicalStyle = isMedicalStyle;
	}

	public Integer getIsUnity() {
		return isUnity;
	}

	public void setIsUnity(Integer isUnity) {
		this.isUnity = isUnity;
	}

	public Integer getIsTreatAbility() {
		return isTreatAbility;
	}

	public void setIsTreatAbility(Integer isTreatAbility) {
		this.isTreatAbility = isTreatAbility;
	}

	public Integer getIsThoughtAbility() {
		return isThoughtAbility;
	}

	public void setIsThoughtAbility(Integer isThoughtAbility) {
		this.isThoughtAbility = isThoughtAbility;
	}

	public Integer getIsTeachAbility() {
		return isTeachAbility;
	}

	public void setIsTeachAbility(Integer isTeachAbility) {
		this.isTeachAbility = isTeachAbility;
	}

	public Long getTakeStudy() {
		return takeStudy;
	}

	public void setTakeStudy(Long takeStudy) {
		this.takeStudy = takeStudy;
	}

	public Integer getIsScientific() {
		return isScientific;
	}

	public void setIsScientific(Integer isScientific) {
		this.isScientific = isScientific;
	}

	public Integer getIsAccident() {
		return isAccident;
	}

	public void setIsAccident(Integer isAccident) {
		this.isAccident = isAccident;
	}

	public Integer getIsRegister() {
		return isRegister;
	}

	public void setIsRegister(Integer isRegister) {
		this.isRegister = isRegister;
	}

	public Float getMedicalHistory() {
		return medicalHistory;
	}

	public void setMedicalHistory(Float medicalHistory) {
		this.medicalHistory = medicalHistory;
	}
	
	public Float getTreatSick() {
		return treatSick;
	}

	public void setTreatSick(Float treatSick) {
		this.treatSick = treatSick;
	}

	public Float getCollegeAbility() {
		return collegeAbility;
	}

	public void setCollegeAbility(Float collegeAbility) {
		this.collegeAbility = collegeAbility;
	}

	public Float getRecipeWill() {
		return recipeWill;
	}

	public void setRecipeWill(Float recipeWill) {
		this.recipeWill = recipeWill;
	}

	public Float getTheoryScore() {
		return theoryScore;
	}

	public void setTheoryScore(Float theoryScore) {
		this.theoryScore = theoryScore;
	}

	public Integer getAllScore() {
		return allScore;
	}

	public void setAllScore(Integer allScore) {
		this.allScore = allScore;
	}

	public String getBaseComment() {
		return baseComment;
	}

	public void setBaseComment(String baseComment) {
		this.baseComment = baseComment;
	}


}
