package com.hys.zyy.manage.model;

import java.util.Date;

/**
 * 收藏记录表
 * <AUTHOR>
 *
 */
public class ZyyCollectionRecord {

	private Long collectionId;
	
	private Long coursewareId;
	
	private Long zyyUserId;
	
	private Date createTime;
	
	private Date updateTime;
	
	// 1公共科目   2 专业课
	private Integer coursewareType;
	
	public Integer getCoursewareType() {
		return coursewareType;
	}

	public void setCoursewareType(Integer coursewareType) {
		this.coursewareType = coursewareType;
	}

	public Long getCollectionId() {
		return collectionId;
	}

	public void setCollectionId(Long collectionId) {
		this.collectionId = collectionId;
	}

	public Long getCoursewareId() {
		return coursewareId;
	}

	public void setCoursewareId(Long coursewareId) {
		this.coursewareId = coursewareId;
	}

	public Long getZyyUserId() {
		return zyyUserId;
	}

	public void setZyyUserId(Long zyyUserId) {
		this.zyyUserId = zyyUserId;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
	
}
