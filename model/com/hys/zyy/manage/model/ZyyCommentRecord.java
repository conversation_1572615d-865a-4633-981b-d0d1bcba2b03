package com.hys.zyy.manage.model;

import java.util.Date;

/**
 * 评论课件表
 * <AUTHOR>
 *
 */
public class ZyyCommentRecord {

	private Long commentId;
	
	private Long coursewareId;
	
	private Long zyyUserId;
	
	private String content;
	
	private Date createTime;
	
	private Date updateTime;
	
	private Integer stars;
	
	// 1公共科目   2 专业课
	private Integer coursewareType;
	
	private String realName;
	
	private String photoPath;
	
	private String createTimeStr;
	
	private String relativeTimeStr;
	
	
	public String getRelativeTimeStr() {
		return relativeTimeStr;
	}

	public void setRelativeTimeStr(String relativeTimeStr) {
		this.relativeTimeStr = relativeTimeStr;
	}

	public String getCreateTimeStr() {
		return createTimeStr;
	}

	public void setCreateTimeStr(String createTimeStr) {
		this.createTimeStr = createTimeStr;
	}

	public String getRealName() {
		return realName;
	}

	public void setRealName(String realName) {
		this.realName = realName;
	}

	public String getPhotoPath() {
		return photoPath;
	}

	public void setPhotoPath(String photoPath) {
		this.photoPath = photoPath;
	}

	public Integer getStars() {
		return stars;
	}

	public void setStars(Integer stars) {
		this.stars = stars;
	}

	public Integer getCoursewareType() {
		return coursewareType;
	}

	public void setCoursewareType(Integer coursewareType) {
		this.coursewareType = coursewareType;
	}

	public Long getCommentId() {
		return commentId;
	}

	public void setCommentId(Long commentId) {
		this.commentId = commentId;
	}

	public Long getCoursewareId() {
		return coursewareId;
	}

	public void setCoursewareId(Long coursewareId) {
		this.coursewareId = coursewareId;
	}

	public Long getZyyUserId() {
		return zyyUserId;
	}

	public void setZyyUserId(Long zyyUserId) {
		this.zyyUserId = zyyUserId;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
	
}
