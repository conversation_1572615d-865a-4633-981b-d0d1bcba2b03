package com.hys.zyy.manage.model;

import java.util.Date;

public class ZyyCourseVo {

	private static final long serialVersionUID = 1L;

	private Long courseId;

    private String courseName;

    private String catagoryCode;

    private String subCatagoryCode;

    private Short chargeType;

    private Long price;

    private Long teacherId;

    private String courseDesc;

    private String posterPic;

    private String orgCode;

    private Date createTime;

    private Date updateTime;
    
    private String courseIntro;

    private String learnTarget;

    /** 状态 1：正常 2：已下架*/
    private Integer state;

    /** 讲师姓名*/
    private String teacherName;

    /** 用于判断编辑时候是否重复*/
    private String courseNameDB;

    /** 收藏数*/
    private Integer collectionNum;

    /** 购买人数*/
    private Integer bayNum;
    
    /** 课件数*/
    private Integer coursewareNum;
    
    /** 课件进行中数*/
    private Integer runingNum;

    /** 课件完成数*/
    private Integer finishNum;
    
    /** 完成率*/
    private String percentage;
    
    public String getPercentage() {
		return percentage;
	}

	public void setPercentage(String percentage) {
		this.percentage = percentage;
	}

	public Integer getRuningNum() {
        return runingNum;
    }

    public void setRuningNum(Integer runingNum) {
        this.runingNum = runingNum;
    }

    public Integer getFinishNum() {
        return finishNum;
    }

    public void setFinishNum(Integer finishNum) {
        this.finishNum = finishNum;
    }

    public Integer getCoursewareNum() {
        return coursewareNum;
    }

    public void setCoursewareNum(Integer coursewareNum) {
        this.coursewareNum = coursewareNum;
    }

    public Integer getCollectionNum() {
        return collectionNum;
    }

    public void setCollectionNum(Integer collectionNum) {
        this.collectionNum = collectionNum;
    }

    public Integer getBayNum() {
        return bayNum;
    }

    public void setBayNum(Integer bayNum) {
        this.bayNum = bayNum;
    }

    public String getCourseNameDB() {
        return courseNameDB;
    }

    public void setCourseNameDB(String courseNameDB) {
        this.courseNameDB = courseNameDB;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public String getTeacherName() {
        return teacherName;
    }

    public void setTeacherName(String teacherName) {
        this.teacherName = teacherName;
    }

    public Long getCourseId() {
        return courseId;
    }

    public void setCourseId(Long courseId) {
        this.courseId = courseId;
    }

    public String getCourseName() {
        return courseName;
    }

    public void setCourseName(String courseName) {
        this.courseName = courseName;
    }

    public String getCatagoryCode() {
        return catagoryCode;
    }

    public void setCatagoryCode(String catagoryCode) {
        this.catagoryCode = catagoryCode;
    }

    public String getSubCatagoryCode() {
        return subCatagoryCode;
    }

    public void setSubCatagoryCode(String subCatagoryCode) {
        this.subCatagoryCode = subCatagoryCode;
    }

    public Short getChargeType() {
        return chargeType;
    }

    public void setChargeType(Short chargeType) {
        this.chargeType = chargeType;
    }

    public Long getPrice() {
        return price;
    }

    public void setPrice(Long price) {
        this.price = price;
    }

    public Long getTeacherId() {
        return teacherId;
    }

    public void setTeacherId(Long teacherId) {
        this.teacherId = teacherId;
    }

    public String getCourseDesc() {
        return courseDesc;
    }

    public void setCourseDesc(String courseDesc) {
        this.courseDesc = courseDesc;
    }

    public String getPosterPic() {
        return posterPic;
    }

    public void setPosterPic(String posterPic) {
        this.posterPic = posterPic;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

	public String getCourseIntro() {
		return courseIntro;
	}

	public void setCourseIntro(String courseIntro) {
		this.courseIntro = courseIntro;
	}

	public String getLearnTarget() {
		return learnTarget;
	}

	public void setLearnTarget(String learnTarget) {
		this.learnTarget = learnTarget;
	}
	
}
