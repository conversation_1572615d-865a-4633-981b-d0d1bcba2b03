package com.hys.zyy.manage.model;

public class ZyyCustomizeColumns extends ZyyBaseObject {
	
	private static final long serialVersionUID = 19639875536095677L;
	/**
	 * ID
	 */
	private Long id;
	/**
	 * 上级列ID
	 */
	private Long parentColumnId;
	/**
	 * 列名称
	 */
	private String columnName;
	/**
	 * 1单选
	 */
	private Integer columnType;
	/**
	 * 列说明
	 */
	private String columnExplanation;
	/**
	 * 状态 -1删除;0停用;1有效
	 */
	private Integer status;
	/**
	 * 机构ID
	 */
	private Long zyyOrgId;
	/**
	 * 1学员评价带教、2带教评价学员、3学员评价科室、4科室评价学员
	 */
	private Integer tableType;
	
	
	public Integer getTableType() {
		return tableType;
	}
	public void setTableType(Integer tableType) {
		this.tableType = tableType;
	}
	public Long getZyyOrgId() {
		return zyyOrgId;
	}
	public void setZyyOrgId(Long zyyOrgId) {
		this.zyyOrgId = zyyOrgId;
	}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getParentColumnId() {
		return parentColumnId;
	}
	public void setParentColumnId(Long parentColumnId) {
		this.parentColumnId = parentColumnId;
	}
	public String getColumnName() {
		return columnName;
	}
	public void setColumnName(String columnName) {
		this.columnName = columnName;
	}
	public Integer getColumnType() {
		return columnType;
	}
	public void setColumnType(Integer columnType) {
		this.columnType = columnType;
	}
	public String getColumnExplanation() {
		return columnExplanation;
	}
	public void setColumnExplanation(String columnExplanation) {
		this.columnExplanation = columnExplanation;
	}
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	
}
