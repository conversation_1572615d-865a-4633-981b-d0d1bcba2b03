package com.hys.zyy.manage.model;

/**
 * @desc 住院医360评价
 * <AUTHOR>
 *
 */
public class ZyyCustomizeColumnsNew extends ZyyBaseObject {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 7821511643625033411L;
	
	/**
	 * ID
	 */
	private Long id;
	/**
	 * 上级列ID
	 */
	private Long parentColumnId;
	/**
	 * 列名称
	 */
	private String columnName;
	/**
	 * 1单选
	 */
	private Integer columnType;
	/**
	 * 列说明
	 */
	private String columnExplanation;
	/**
	 * 状态 -1删除;0停用;1有效
	 */
	private Integer status;
	/**
	 * 机构ID
	 */
	private Long zyyOrgId;
	/**
	 * 1：对学员评价  2：对带教评价  3：对责任导师评价 4：对科室评价 5：对专业基地评价 6：对培训基地的评价
	 */
	private Integer tableType;
	
	
	public Integer getTableType() {
		return tableType;
	}
	public void setTableType(Integer tableType) {
		this.tableType = tableType;
	}
	public Long getZyyOrgId() {
		return zyyOrgId;
	}
	public void setZyyOrgId(Long zyyOrgId) {
		this.zyyOrgId = zyyOrgId;
	}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getParentColumnId() {
		return parentColumnId;
	}
	public void setParentColumnId(Long parentColumnId) {
		this.parentColumnId = parentColumnId;
	}
	public String getColumnName() {
		return columnName;
	}
	public void setColumnName(String columnName) {
		this.columnName = columnName;
	}
	public Integer getColumnType() {
		return columnType;
	}
	public void setColumnType(Integer columnType) {
		this.columnType = columnType;
	}
	public String getColumnExplanation() {
		return columnExplanation;
	}
	public void setColumnExplanation(String columnExplanation) {
		this.columnExplanation = columnExplanation;
	}
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	
}
