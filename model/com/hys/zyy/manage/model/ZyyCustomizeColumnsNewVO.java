package com.hys.zyy.manage.model;

import java.util.List;

/**
 * 住院医，360评价
 * <AUTHOR>
 *
 */
public class ZyyCustomizeColumnsNewVO extends ZyyCustomizeColumnsNew{
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 2322608354367619356L;
	
	public Integer tableNum;
	/**
	 * 评价项目
	 */
	public List<ZyyCustomizeColumnsNewVO> subColumnsList ;
	/**
	 * 项目对应的itemList
	 */
	public  List<ZyyCustColItemNewVO> itemList ;
	/**
	 * 父列的名称
	 */
	private String parentColumnName;
	/**
	 * 评价表的ID
	 */
	private Long tableId;
	/**
	 * 学员评分条目的id  zyy_customize_table_data的id
	 */
	private Long tableDataId;
	
	private String orgName;
		
	private Double avgScore = 0.0;//平均分，评价统计使用
	
	// 1是选中 空为没有选中
	private Integer selectStatus;
	
	public String getOrgName() {
		return orgName;
	}
	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}
	public Long getTableDataId() {
		return tableDataId;
	}
	public void setTableDataId(Long tableDataId) {
		this.tableDataId = tableDataId;
	}
	public String getParentColumnName() {
		return parentColumnName;
	}
	public void setParentColumnName(String parentColumnName) {
		this.parentColumnName = parentColumnName;
	}
	public Integer getTableNum() {
		return tableNum;
	}
	public void setTableNum(Integer tableNum) {
		this.tableNum = tableNum;
	}
	public List<ZyyCustomizeColumnsNewVO> getSubColumnsList() {
		return subColumnsList;
	}
	public void setSubColumnsList(List<ZyyCustomizeColumnsNewVO> subColumnsList) {
		this.subColumnsList = subColumnsList;
	}
	public List<ZyyCustColItemNewVO> getItemList() {
		return itemList;
	}
	public void setItemList(List<ZyyCustColItemNewVO> itemList) {
		this.itemList = itemList;
	}
	public Double getAvgScore() {
		return avgScore;
	}
	public void setAvgScore(Double avgScore) {
		this.avgScore = avgScore;
	}
	public Long getTableId() {
		return tableId;
	}
	public void setTableId(Long tableId) {
		this.tableId = tableId;
	}
	public Integer getSelectStatus() {
		return selectStatus;
	}
	public void setSelectStatus(Integer selectStatus) {
		this.selectStatus = selectStatus;
	}
}
