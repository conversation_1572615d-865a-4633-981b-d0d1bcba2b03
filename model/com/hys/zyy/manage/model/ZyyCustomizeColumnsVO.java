package com.hys.zyy.manage.model;

import java.util.List;

public class ZyyCustomizeColumnsVO extends ZyyCustomizeColumns{
	
	private static final long serialVersionUID = -8343113376921378665L;

	public Integer tableNum;
	/**
	 * 评价项目
	 */
	public  List<ZyyCustomizeColumnsVO> subColumnsList ;
	/**
	 * 项目对应的itemList
	 */
	public  List<ZyyCustomizeColumnsItemVO> itemList ;
	/**
	 * 父列的名称
	 */
	private String parentColumnName;
	/**
	 * 学员评分条目的id  zyy_customize_table_data的id
	 */
	private Long tableDataId;
	
	private String orgName;
		
	private Double avgScore = 0.0;//平均分，评价统计使用
	
	public String getOrgName() {
		return orgName;
	}
	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}
	public Long getTableDataId() {
		return tableDataId;
	}
	public void setTableDataId(Long tableDataId) {
		this.tableDataId = tableDataId;
	}
	public String getParentColumnName() {
		return parentColumnName;
	}
	public void setParentColumnName(String parentColumnName) {
		this.parentColumnName = parentColumnName;
	}
	public Integer getTableNum() {
		return tableNum;
	}
	public void setTableNum(Integer tableNum) {
		this.tableNum = tableNum;
	}
	public List<ZyyCustomizeColumnsVO> getSubColumnsList() {
		return subColumnsList;
	}
	public void setSubColumnsList(List<ZyyCustomizeColumnsVO> subColumnsList) {
		this.subColumnsList = subColumnsList;
	}
	public List<ZyyCustomizeColumnsItemVO> getItemList() {
		return itemList;
	}
	public void setItemList(List<ZyyCustomizeColumnsItemVO> itemList) {
		this.itemList = itemList;
	}
	public Double getAvgScore() {
		return avgScore;
	}
	public void setAvgScore(Double avgScore) {
		this.avgScore = avgScore;
	}
}
