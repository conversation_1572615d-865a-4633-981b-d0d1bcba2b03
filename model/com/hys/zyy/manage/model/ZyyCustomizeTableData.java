package com.hys.zyy.manage.model;

import java.util.Date;

/**
 * 
 */
public class ZyyCustomizeTableData extends ZyyBaseObject {

	private static final long serialVersionUID = 1L;
	/**
	 * 主键
	 */
	private Long id;
	/**
	 * 用户评价表的id
	 */
	private Long evaluateId;
	/**
	 * 评价表中某一列的id
	 */
	private Long columnsId;
	/**
	 * 此列的填写值    虽然是值,但是对应的是评分项item的id
	 */
	private String dataValue;
	/**
	 * 创建日期
	 */
	private Date createDate;

	
	private Integer itemScore;
	

	public Integer getItemScore() {
		return itemScore;
	}

	public void setItemScore(Integer itemScore) {
		this.itemScore = itemScore;
	}
	
	
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getEvaluateId() {
		return evaluateId;
	}

	public void setEvaluateId(Long evaluateId) {
		this.evaluateId = evaluateId;
	}

	public Long getColumnsId() {
		return columnsId;
	}

	public void setColumnsId(Long columnsId) {
		this.columnsId = columnsId;
	}

	public String getDataValue() {
		return dataValue;
	}

	public void setDataValue(String dataValue) {
		this.dataValue = dataValue;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}
	
}
