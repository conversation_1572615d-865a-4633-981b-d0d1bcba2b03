package com.hys.zyy.manage.model;

import java.util.Date;

public class ZyyCycleApplyInfo {
    private Long id;

    /*
     * 申请人
     *
     * @mbggenerated
     */
    private Long applyUser;

    private Date applyTime;

    /*
     * 申请类型1、带教2、入科3、出科
     *
     * @mbggenerated
     */
    private Integer applyType;

    /*
     * 轮转ID
     *
     * @mbggenerated
     */
    private Long applyCycleId;

    /*
     * 学员
     *
     * @mbggenerated
     */
    private Long applyCycleResidencyId;

    /*
     * 轮转开始时间
     *
     * @mbggenerated
     */
    private Date applyCycleStartDate;

    /*
     * 审核状态0、未审核默认 1、审核通过2、审核不通过
     *
     * @mbggenerated
     */
    private Integer verifyStatus;

    /*
     * 审核时间
     *
     * @mbggenerated
     */
    private Date verifyTime;

    private String verifyRemark;

    private Long verifyUser;

    private Date lastUpdateTime;
    
    //是否最新1是0否
    private Integer isNew;
    //轮转结束时间
    private Date applyCycleEndDate;
    
    
    /*
     * 轮转科室ID
     *
     * @mbggenerated
     */
    private Long applyCycleDeptId;
    
    private String cycleStartDateStr;
    private String cycleEndDateStr;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getApplyUser() {
        return applyUser;
    }

    public void setApplyUser(Long applyUser) {
        this.applyUser = applyUser;
    }

    public Date getApplyTime() {
        return applyTime;
    }

    public void setApplyTime(Date applyTime) {
        this.applyTime = applyTime;
    }

    public Integer getApplyType() {
        return applyType;
    }

    public void setApplyType(Integer applyType) {
        this.applyType = applyType;
    }

    public Long getApplyCycleId() {
        return applyCycleId;
    }

    public void setApplyCycleId(Long applyCycleId) {
        this.applyCycleId = applyCycleId;
    }

    public Long getApplyCycleResidencyId() {
        return applyCycleResidencyId;
    }

    public void setApplyCycleResidencyId(Long applyCycleResidencyId) {
        this.applyCycleResidencyId = applyCycleResidencyId;
    }

    public Date getApplyCycleStartDate() {
        return applyCycleStartDate;
    }

    public void setApplyCycleStartDate(Date applyCycleStartDate) {
        this.applyCycleStartDate = applyCycleStartDate;
    }

    public Integer getVerifyStatus() {
        return verifyStatus;
    }

    public void setVerifyStatus(Integer verifyStatus) {
        this.verifyStatus = verifyStatus;
    }

    public Date getVerifyTime() {
        return verifyTime;
    }

    public void setVerifyTime(Date verifyTime) {
        this.verifyTime = verifyTime;
    }

    public String getVerifyRemark() {
        return verifyRemark;
    }

    public void setVerifyRemark(String verifyRemark) {
        this.verifyRemark = verifyRemark == null ? null : verifyRemark.trim();
    }

    public Long getVerifyUser() {
        return verifyUser;
    }

    public void setVerifyUser(Long verifyUser) {
        this.verifyUser = verifyUser;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

	public Integer getIsNew() {
		return isNew;
	}

	public void setIsNew(Integer isNew) {
		this.isNew = isNew;
	}

	public Date getApplyCycleEndDate() {
		return applyCycleEndDate;
	}

	public void setApplyCycleEndDate(Date applyCycleEndDate) {
		this.applyCycleEndDate = applyCycleEndDate;
	}

	public String getCycleStartDateStr() {
		return cycleStartDateStr;
	}

	public void setCycleStartDateStr(String cycleStartDateStr) {
		this.cycleStartDateStr = cycleStartDateStr;
	}

	public String getCycleEndDateStr() {
		return cycleEndDateStr;
	}

	public void setCycleEndDateStr(String cycleEndDateStr) {
		this.cycleEndDateStr = cycleEndDateStr;
	}

	public Long getApplyCycleDeptId() {
		return applyCycleDeptId;
	}

	public void setApplyCycleDeptId(Long applyCycleDeptId) {
		this.applyCycleDeptId = applyCycleDeptId;
	}
    
    
    
    
}