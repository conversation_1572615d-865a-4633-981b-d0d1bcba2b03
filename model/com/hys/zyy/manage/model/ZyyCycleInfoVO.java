package com.hys.zyy.manage.model;

import java.io.Serializable;

import com.hys.zyy.manage.util.StringUtils;

public class ZyyCycleInfoVO implements Serializable {
	private static final long serialVersionUID = 1L;
	/*
	 * 学员ID
	 */
	private Long residencyId;
	/*
	 * 学员姓名
	 */
	private String realName;
	/*
	 * 轮转科室
	 */
	private String cycleDept;
	/*
	 * 科室轮转时间段
	 */
	private String dateSection;
	/*
	 * 轮转状态（1：已完成；0：正在轮转；-1：未轮转）
	 */
	private Integer cycleState;
	/*
	 * 入科教育
	 */
	private String enterTeach;
	/*
	 * 迟到次数
	 */
	private Integer lateCount;
	/*
	 * 早退次数
	 */
	private Integer earlyCount;
	/*
	 * 旷工天数
	 */
	private Integer absenteeismSum;
	/*
	 * 请假天数
	 */
	private Float leaveSum;
	private String leaveSumStr;
	/*
	 * 入科状态
	 */
	private String enterDeptStr;
	/*
	 * 出科状态
	 */
	private String outDeptStr;
	/*
	 * 理论成绩
	 */
	private String theoryGradeStr;
	/*
	 * 技能成绩
	 */
	private String skillGradeStr;

	public ZyyCycleInfoVO() {
		super();
	}

	public ZyyCycleInfoVO(Long residencyId) {
		super();
		this.residencyId = residencyId;
	}

	public Long getResidencyId() {
		return residencyId;
	}

	public void setResidencyId(Long residencyId) {
		this.residencyId = residencyId;
	}

	public String getRealName() {
		return realName;
	}

	public void setRealName(String realName) {
		this.realName = realName;
	}

	public String getCycleDept() {
		return cycleDept;
	}

	public void setCycleDept(String cycleDept) {
		this.cycleDept = cycleDept;
	}

	public String getDateSection() {
		return dateSection;
	}

	public void setDateSection(String dateSection) {
		this.dateSection = dateSection;
	}

	public Integer getCycleState() {
		return cycleState;
	}

	public void setCycleState(Integer cycleState) {
		this.cycleState = cycleState;
	}

	public String getEnterTeach() {
		return enterTeach;
	}

	public void setEnterTeach(String enterTeach) {
		this.enterTeach = enterTeach;
	}

	public Integer getLateCount() {
		return lateCount;
	}

	public void setLateCount(Integer lateCount) {
		this.lateCount = lateCount;
	}

	public Integer getEarlyCount() {
		return earlyCount;
	}

	public void setEarlyCount(Integer earlyCount) {
		this.earlyCount = earlyCount;
	}

	public Integer getAbsenteeismSum() {
		return absenteeismSum;
	}

	public void setAbsenteeismSum(Integer absenteeismSum) {
		this.absenteeismSum = absenteeismSum;
	}

	public Float getLeaveSum() {
		return leaveSum;
	}

	public void setLeaveSum(Float leaveSum) {
		this.leaveSum = leaveSum;
	}

	public String getLeaveSumStr() {
		return leaveSumStr;
	}

	public void setLeaveSumStr(String leaveSumStr) {
		this.leaveSumStr = leaveSumStr;
	}

	public String getEnterDeptStr() {
		return enterDeptStr;
	}

	public void setEnterDeptStr(String enterDeptStr) {
		this.enterDeptStr = enterDeptStr;
	}

	public String getOutDeptStr() {
		return outDeptStr;
	}

	public void setOutDeptStr(String outDeptStr) {
		this.outDeptStr = outDeptStr;
	}

	public String getTheoryGradeStr() {
		return theoryGradeStr;
	}

	public void setTheoryGradeStr(String theoryGradeStr) {
		this.theoryGradeStr = theoryGradeStr;
	}

	public String getSkillGradeStr() {
		return skillGradeStr;
	}

	public void setSkillGradeStr(String skillGradeStr) {
		this.skillGradeStr = skillGradeStr;
	}

	public void clear() {
		setEnterTeach(StringUtils.EMPTY);
		setLateCount(null);
		setEarlyCount(null);
		setAbsenteeismSum(null);
		setLeaveSum(null);
		setLeaveSumStr(StringUtils.EMPTY);
		setEnterDeptStr(StringUtils.EMPTY);
		setOutDeptStr(StringUtils.EMPTY);
		setTheoryGradeStr(StringUtils.EMPTY);
		setSkillGradeStr(StringUtils.EMPTY);
	}

}
