package com.hys.zyy.manage.model;

import java.io.Serializable;
import java.util.Date;

import com.hys.zyy.manage.util.BaseModel;

/**
 * 公卫手册
 * <AUTHOR>
 */
public class ZyyCycleManual extends BaseModel implements Serializable {
	private static final long serialVersionUID = 1L;
	/*
	 * ID
	 */
	private Long id;
	/*
	 * 学员ID
	 */
	private Long residencyId;
	/*
	 * 科室ID
	 */
	private Long deptId;
	/*
	 * 轮转开始时间
	 */
	private Date cycleStartDate;
	/*
	 * 轮转结束时间
	 */
	private Date cycleEndDate;
	/*
	 * 手册类型（1=培训内容；2=理论小讲课；3=技能操作；4=成绩汇总表；5=培训小结）
	 */
	private Integer manualType;
	/*
	 * 手册所属时间
	 */
	private Date manualDate;
	/*
	 * 备注
	 */
	private String remark;
	/*
	 * 审核状态（0=已填写；1=审核中；2=审核通过；3=审核未通过）
	 */
	private Integer auditState;
	/*
	 * 提交审核时间
	 */
	private Date submitAuditTime;
	/*
	 * 状态（-1：失效；1：有效）
	 */
	private Integer state;

	private Date createTime;
	private Date updateTime;

	public ZyyCycleManual() {
		super();
	}

	public ZyyCycleManual(Long id, Integer auditState) {
		super();
		this.id = id;
		this.auditState = auditState;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getResidencyId() {
		return residencyId;
	}

	public void setResidencyId(Long residencyId) {
		this.residencyId = residencyId;
	}

	public Long getDeptId() {
		return deptId;
	}

	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}

	public Date getCycleStartDate() {
		return cycleStartDate;
	}

	public void setCycleStartDate(Date cycleStartDate) {
		this.cycleStartDate = cycleStartDate;
	}

	public Date getCycleEndDate() {
		return cycleEndDate;
	}

	public void setCycleEndDate(Date cycleEndDate) {
		this.cycleEndDate = cycleEndDate;
	}

	public Integer getManualType() {
		return manualType;
	}

	public void setManualType(Integer manualType) {
		this.manualType = manualType;
	}

	public Date getManualDate() {
		return manualDate;
	}

	public void setManualDate(Date manualDate) {
		this.manualDate = manualDate;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark == null ? null : remark.trim();
	}

	public Integer getAuditState() {
		return auditState;
	}

	public void setAuditState(Integer auditState) {
		this.auditState = auditState;
	}

	public Date getSubmitAuditTime() {
		return submitAuditTime;
	}

	public void setSubmitAuditTime(Date submitAuditTime) {
		this.submitAuditTime = submitAuditTime;
	}

	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ZyyCycleManual other = (ZyyCycleManual) obj;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		return true;
	}

	@Override
	public String toString() {
		return "ZyyCycleManual [id=" + id + ", residencyId=" + residencyId + ", deptId=" + deptId + ", cycleStartDate="
				+ cycleStartDate + ", cycleEndDate=" + cycleEndDate + ", manualType=" + manualType + ", manualDate="
				+ manualDate + ", remark=" + remark + ", auditState=" + auditState + ", submitAuditTime="
				+ submitAuditTime + ", state=" + state + ", createTime=" + createTime + ", updateTime=" + updateTime
				+ "]";
	}

}