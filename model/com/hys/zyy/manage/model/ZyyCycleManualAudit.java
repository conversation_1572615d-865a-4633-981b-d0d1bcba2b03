package com.hys.zyy.manage.model;

import java.io.Serializable;
import java.util.Date;

import com.hys.zyy.manage.util.BaseModel;

/**
 * 公卫手册审核
 * <AUTHOR>
 */
public class ZyyCycleManualAudit extends BaseModel implements Serializable {
	private static final long serialVersionUID = 1L;
	/*
	 * ID
	 */
	private Long id;
	/*
	 * 公卫手册ID
	 */
	private Long zyyCycleManualId;
	/*
	 * 带教审核（1=待审核；2=审核通过；3=审核未通过）
	 */
	private Integer teacherAudit;
	/*
	 * 带教审核意见
	 */
	private String teacherAuditNote;
	/*
	 * 带教审核人
	 */
	private Long teacherAuditerId;
	/*
	 * 带教审核时间
	 */
	private Date teacherAuditTime;
	/*
	 * 科室审核（1=待审核；2=审核通过；3=审核未通过）
	 */
	private Integer deptAudit;
	/*
	 * 科室审核意见
	 */
	private String deptAuditNote;
	/*
	 * 科室审核人
	 */
	private Long deptAuditerId;
	/*
	 * 科室审核时间
	 */
	private Date deptAuditTime;
	/*
	 * 医院审核（1=待审核；2=审核通过；3=审核未通过）
	 */
	private Integer hospitalAudit;
	/*
	 * 医院审核意见
	 */
	private String hospitalAuditNote;
	/*
	 * 医院审核人
	 */
	private Long hospitalAuditerId;
	/*
	 * 医院审核时间
	 */
	private Date hospitalAuditTime;
	/*
	 * 提交次数
	 */
	private Integer submitCount;
	/*
	 * 是否最新（1=是；0=否）
	 */
	private Integer isNew;
	/*
	 * 状态（-1：失效；1：有效）
	 */
	private Integer state;

	private Date createTime;
	private Date updateTime;

	public ZyyCycleManualAudit() {
		super();
	}

	public ZyyCycleManualAudit(Long zyyCycleManualId) {
		super();
		this.zyyCycleManualId = zyyCycleManualId;
	}

	public ZyyCycleManualAudit(Long zyyCycleManualId, Integer teacherAudit) {
		super();
		this.zyyCycleManualId = zyyCycleManualId;
		this.teacherAudit = teacherAudit;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getZyyCycleManualId() {
		return zyyCycleManualId;
	}

	public void setZyyCycleManualId(Long zyyCycleManualId) {
		this.zyyCycleManualId = zyyCycleManualId;
	}

	public Integer getTeacherAudit() {
		return teacherAudit;
	}

	public void setTeacherAudit(Integer teacherAudit) {
		this.teacherAudit = teacherAudit;
	}

	public String getTeacherAuditNote() {
		return teacherAuditNote;
	}

	public void setTeacherAuditNote(String teacherAuditNote) {
		this.teacherAuditNote = teacherAuditNote == null ? null : teacherAuditNote.trim();
	}

	public Long getTeacherAuditerId() {
		return teacherAuditerId;
	}

	public void setTeacherAuditerId(Long teacherAuditerId) {
		this.teacherAuditerId = teacherAuditerId;
	}

	public Date getTeacherAuditTime() {
		return teacherAuditTime;
	}

	public void setTeacherAuditTime(Date teacherAuditTime) {
		this.teacherAuditTime = teacherAuditTime;
	}

	public Integer getDeptAudit() {
		return deptAudit;
	}

	public void setDeptAudit(Integer deptAudit) {
		this.deptAudit = deptAudit;
	}

	public String getDeptAuditNote() {
		return deptAuditNote;
	}

	public void setDeptAuditNote(String deptAuditNote) {
		this.deptAuditNote = deptAuditNote == null ? null : deptAuditNote.trim();
	}

	public Long getDeptAuditerId() {
		return deptAuditerId;
	}

	public void setDeptAuditerId(Long deptAuditerId) {
		this.deptAuditerId = deptAuditerId;
	}

	public Date getDeptAuditTime() {
		return deptAuditTime;
	}

	public void setDeptAuditTime(Date deptAuditTime) {
		this.deptAuditTime = deptAuditTime;
	}

	public Integer getHospitalAudit() {
		return hospitalAudit;
	}

	public void setHospitalAudit(Integer hospitalAudit) {
		this.hospitalAudit = hospitalAudit;
	}

	public String getHospitalAuditNote() {
		return hospitalAuditNote;
	}

	public void setHospitalAuditNote(String hospitalAuditNote) {
		this.hospitalAuditNote = hospitalAuditNote == null ? null : hospitalAuditNote.trim();
	}

	public Long getHospitalAuditerId() {
		return hospitalAuditerId;
	}

	public void setHospitalAuditerId(Long hospitalAuditerId) {
		this.hospitalAuditerId = hospitalAuditerId;
	}

	public Date getHospitalAuditTime() {
		return hospitalAuditTime;
	}

	public void setHospitalAuditTime(Date hospitalAuditTime) {
		this.hospitalAuditTime = hospitalAuditTime;
	}

	public Integer getSubmitCount() {
		return submitCount;
	}

	public void setSubmitCount(Integer submitCount) {
		this.submitCount = submitCount;
	}

	public Integer getIsNew() {
		return isNew;
	}

	public void setIsNew(Integer isNew) {
		this.isNew = isNew;
	}

	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ZyyCycleManualAudit other = (ZyyCycleManualAudit) obj;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		return true;
	}

	@Override
	public String toString() {
		return "ZyyCycleManualAudit [id=" + id + ", zyyCycleManualId=" + zyyCycleManualId + ", teacherAudit="
				+ teacherAudit + ", teacherAuditNote=" + teacherAuditNote + ", teacherAuditerId=" + teacherAuditerId
				+ ", teacherAuditTime=" + teacherAuditTime + ", deptAudit=" + deptAudit + ", deptAuditNote="
				+ deptAuditNote + ", deptAuditerId=" + deptAuditerId + ", deptAuditTime=" + deptAuditTime
				+ ", hospitalAudit=" + hospitalAudit + ", hospitalAuditNote=" + hospitalAuditNote
				+ ", hospitalAuditerId=" + hospitalAuditerId + ", hospitalAuditTime=" + hospitalAuditTime
				+ ", submitCount=" + submitCount + ", isNew=" + isNew + ", state=" + state + ", createTime="
				+ createTime + ", updateTime=" + updateTime + "]";
	}
	
}