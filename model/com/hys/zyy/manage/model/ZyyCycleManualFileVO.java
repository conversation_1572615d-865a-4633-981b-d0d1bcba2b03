package com.hys.zyy.manage.model;

public class ZyyCycleManualFileVO extends ZyyCycleManualFile {
	private Boolean isImg;
	private Boolean isPdf;
	private String thumbnail, createTimeStr;

	public ZyyCycleManualFileVO() {
		super();
	}

	public ZyyCycleManualFileVO(Long zyyCycleManualId) {
		super(zyyCycleManualId);
	}

	public Boolean getIsImg() {
		return isImg;
	}

	public void setIsImg(Boolean isImg) {
		this.isImg = isImg;
	}

	public Boolean getIsPdf() {
		return isPdf;
	}

	public void setIsPdf(Boolean isPdf) {
		this.isPdf = isPdf;
	}

	public String getThumbnail() {
		return thumbnail;
	}

	public void setThumbnail(String thumbnail) {
		this.thumbnail = thumbnail == null ? null : thumbnail.trim();
	}

	public String getCreateTimeStr() {
		return createTimeStr;
	}

	public void setCreateTimeStr(String createTimeStr) {
		this.createTimeStr = createTimeStr == null ? null : createTimeStr.trim();
	}

}