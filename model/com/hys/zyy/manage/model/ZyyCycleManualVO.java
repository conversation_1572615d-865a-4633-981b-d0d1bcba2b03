package com.hys.zyy.manage.model;

import java.util.List;

public class ZyyCycleManualVO extends ZyyCycleManual {

	private Integer cycleState, submitState, teacherAudit, deptAudit, hospitalAudit;
	private Long zyyCycleManualAuditId, yearId;
	private String uuid, deptName, cycleStartDateStr, cycleEndDateStr, cycleStateStr, submitAuditTimeStr, auditStateStr,
			timeSection, manualTypeStr, manualDateStr, realName, year, teacherName, teacherAuditStr, deptAuditStr,
			hospitalAuditStr, jsonStr, submitStateStr, submitTimeStr, submitStartDateStr, submitEndDateStr;
	private List<ZyyCycleManualVO> manualList;

	public ZyyCycleManualVO() {
		super();
	}

	public ZyyCycleManualVO(Long id, Integer auditState) {
		super(id, auditState);
	}

	public Integer getCycleState() {
		return cycleState;
	}

	public void setCycleState(Integer cycleState) {
		this.cycleState = cycleState;
	}

	public Integer getSubmitState() {
		return submitState;
	}

	public void setSubmitState(Integer submitState) {
		this.submitState = submitState;
	}

	public Integer getTeacherAudit() {
		return teacherAudit;
	}

	public void setTeacherAudit(Integer teacherAudit) {
		this.teacherAudit = teacherAudit;
	}

	public Integer getDeptAudit() {
		return deptAudit;
	}

	public void setDeptAudit(Integer deptAudit) {
		this.deptAudit = deptAudit;
	}

	public Integer getHospitalAudit() {
		return hospitalAudit;
	}

	public void setHospitalAudit(Integer hospitalAudit) {
		this.hospitalAudit = hospitalAudit;
	}

	public Long getZyyCycleManualAuditId() {
		return zyyCycleManualAuditId;
	}

	public void setZyyCycleManualAuditId(Long zyyCycleManualAuditId) {
		this.zyyCycleManualAuditId = zyyCycleManualAuditId;
	}

	public Long getYearId() {
		return yearId;
	}

	public void setYearId(Long yearId) {
		this.yearId = yearId;
	}

	public String getUuid() {
		return uuid;
	}

	public void setUuid(String uuid) {
		this.uuid = uuid == null ? null : uuid.trim();
	}

	public String getDeptName() {
		return deptName;
	}

	public void setDeptName(String deptName) {
		this.deptName = deptName == null ? null : deptName.trim();
	}

	public String getCycleStartDateStr() {
		return cycleStartDateStr;
	}

	public void setCycleStartDateStr(String cycleStartDateStr) {
		this.cycleStartDateStr = cycleStartDateStr == null ? null : cycleStartDateStr.trim();
	}

	public String getCycleEndDateStr() {
		return cycleEndDateStr;
	}

	public void setCycleEndDateStr(String cycleEndDateStr) {
		this.cycleEndDateStr = cycleEndDateStr == null ? null : cycleEndDateStr.trim();
	}

	public String getCycleStateStr() {
		return cycleStateStr;
	}

	public void setCycleStateStr(String cycleStateStr) {
		this.cycleStateStr = cycleStateStr == null ? null : cycleStateStr.trim();
	}

	public String getSubmitAuditTimeStr() {
		return submitAuditTimeStr;
	}

	public void setSubmitAuditTimeStr(String submitAuditTimeStr) {
		this.submitAuditTimeStr = submitAuditTimeStr == null ? null : submitAuditTimeStr.trim();
	}

	public String getAuditStateStr() {
		return auditStateStr;
	}

	public void setAuditStateStr(String auditStateStr) {
		this.auditStateStr = auditStateStr == null ? null : auditStateStr.trim();
	}

	public String getManualTypeStr() {
		return manualTypeStr;
	}

	public void setManualTypeStr(String manualTypeStr) {
		this.manualTypeStr = manualTypeStr == null ? null : manualTypeStr.trim();
	}

	public String getManualDateStr() {
		return manualDateStr;
	}

	public void setManualDateStr(String manualDateStr) {
		this.manualDateStr = manualDateStr == null ? null : manualDateStr.trim();
	}

	public String getRealName() {
		return realName;
	}

	public void setRealName(String realName) {
		this.realName = realName == null ? null : realName.trim();
	}

	public String getYear() {
		return year;
	}

	public void setYear(String year) {
		this.year = year == null ? null : year.trim();
	}

	public String getTeacherName() {
		return teacherName;
	}

	public void setTeacherName(String teacherName) {
		this.teacherName = teacherName == null ? null : teacherName.trim();
	}

	public String getTeacherAuditStr() {
		return teacherAuditStr;
	}

	public void setTeacherAuditStr(String teacherAuditStr) {
		this.teacherAuditStr = teacherAuditStr == null ? null : teacherAuditStr;
	}

	public String getDeptAuditStr() {
		return deptAuditStr;
	}

	public void setDeptAuditStr(String deptAuditStr) {
		this.deptAuditStr = deptAuditStr == null ? null : deptAuditStr.trim();
	}

	public String getHospitalAuditStr() {
		return hospitalAuditStr;
	}

	public void setHospitalAuditStr(String hospitalAuditStr) {
		this.hospitalAuditStr = hospitalAuditStr == null ? null : hospitalAuditStr.trim();
	}

	public String getJsonStr() {
		return jsonStr;
	}

	public void setJsonStr(String jsonStr) {
		this.jsonStr = jsonStr == null ? null : jsonStr.trim();
	}

	public String getTimeSection() {
		return timeSection;
	}

	public void setTimeSection(String timeSection) {
		this.timeSection = timeSection == null ? null : timeSection.trim();
	}

	public String getSubmitStateStr() {
		return submitStateStr;
	}

	public void setSubmitStateStr(String submitStateStr) {
		this.submitStateStr = submitStateStr == null ? null : submitStateStr.trim();
	}

	public String getSubmitTimeStr() {
		return submitTimeStr;
	}

	public void setSubmitTimeStr(String submitTimeStr) {
		this.submitTimeStr = submitTimeStr == null ? null : submitTimeStr.trim();
	}

	public String getSubmitStartDateStr() {
		return submitStartDateStr;
	}

	public void setSubmitStartDateStr(String submitStartDateStr) {
		this.submitStartDateStr = submitStartDateStr == null ? null : submitStartDateStr.trim();
	}

	public String getSubmitEndDateStr() {
		return submitEndDateStr;
	}

	public void setSubmitEndDateStr(String submitEndDateStr) {
		this.submitEndDateStr = submitEndDateStr == null ? null : submitEndDateStr.trim();
	}

	public List<ZyyCycleManualVO> getManualList() {
		return manualList;
	}

	public void setManualList(List<ZyyCycleManualVO> manualList) {
		this.manualList = manualList;
	}

}