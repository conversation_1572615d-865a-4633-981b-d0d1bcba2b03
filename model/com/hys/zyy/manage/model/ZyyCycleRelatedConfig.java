package com.hys.zyy.manage.model;

import java.util.Date;

public class ZyyCycleRelatedConfig {
    private Long id;
    /*
     * 医院ID
     *
     * @mbggenerated
     */
    private Long orgId;
    /*
     * 创建人
     *
     * @mbggenerated
     */
    private Long createUser;
    /*
     * 创建时间
     *
     * @mbggenerated
     */
    private Date createTime;
    /*
     * 接收入科操作时间，默认0，数字1-30
     *
     * @mbggenerated
     */
    private Integer joinDeptAfterDays;
    /*
     * 指定带教操作时间限制，默认为0，数字1-30
     *
     * @mbggenerated
     */
    private Integer cycleTeacherAfterDays;
    /*
     * 出科审核操作时间，数字1-30
     *
     * @mbggenerated
     */
    private Integer leaveDeptAuditAfterDays;
    /**
     * 最后更新人
     */
    private Long lastModifyUser ;
    
    private Integer startMonth;
    private Integer endMonth;
    private Integer startDay;
    private Integer endDay;
    /*
     * 医院是否设置了科室考勤上报时间范围
     */
    private boolean attConfig;
    /*
     * 出科考试成绩是否同步至出科审核理论考核成绩（1=是；0=否）
     */
    private Integer leaveDeptScoreSyn;

    public Integer getLeaveDeptScoreSyn() {
        return leaveDeptScoreSyn;
    }

    public void setLeaveDeptScoreSyn(Integer leaveDeptScoreSyn) {
        this.leaveDeptScoreSyn = leaveDeptScoreSyn;
    }

    public ZyyCycleRelatedConfig() {
		super();
	}

	public ZyyCycleRelatedConfig(Long orgId) {
		super();
		this.orgId = orgId;
	}

	public ZyyCycleRelatedConfig(boolean attConfig) {
		super();
		this.attConfig = attConfig;
	}

	public Integer getStartMonth() {
		return startMonth;
	}

	public void setStartMonth(Integer startMonth) {
		this.startMonth = startMonth;
	}

	public Integer getEndMonth() {
		return endMonth;
	}

	public void setEndMonth(Integer endMonth) {
		this.endMonth = endMonth;
	}

	public Integer getStartDay() {
		return startDay;
	}

	public void setStartDay(Integer startDay) {
		this.startDay = startDay;
	}

	public Integer getEndDay() {
		return endDay;
	}

	public void setEndDay(Integer endDay) {
		this.endDay = endDay;
	}

	public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getOrgId() {
        return orgId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    public Long getCreateUser() {
        return createUser;
    }

    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getJoinDeptAfterDays() {
        return joinDeptAfterDays;
    }

    public void setJoinDeptAfterDays(Integer joinDeptAfterDays) {
        this.joinDeptAfterDays = joinDeptAfterDays;
    }

    public Integer getCycleTeacherAfterDays() {
        return cycleTeacherAfterDays;
    }

    public void setCycleTeacherAfterDays(Integer cycleTeacherAfterDays) {
        this.cycleTeacherAfterDays = cycleTeacherAfterDays;
    }

    public Integer getLeaveDeptAuditAfterDays() {
        return leaveDeptAuditAfterDays;
    }

    public void setLeaveDeptAuditAfterDays(Integer leaveDeptAuditAfterDays) {
        this.leaveDeptAuditAfterDays = leaveDeptAuditAfterDays;
    }

	public Long getLastModifyUser() {
		return lastModifyUser;
	}

	public void setLastModifyUser(Long lastModifyUser) {
		this.lastModifyUser = lastModifyUser;
	}

	public boolean isAttConfig() {
		return attConfig;
	}

	public void setAttConfig(boolean attConfig) {
		this.attConfig = attConfig;
	}
	
}