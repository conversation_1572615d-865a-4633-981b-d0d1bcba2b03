package com.hys.zyy.manage.model;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.codehaus.jackson.map.annotate.JsonSerialize;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.hys.zyy.manage.json.SimpleDateSerializer;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：张伟清 2012-07-01
 * 
 * 描述：轮转表住院医师轮转科室审核记录
 * 
 * 说明:
 */
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class ZyyCycleResiCycleCheck implements java.io.Serializable {

	private static final long serialVersionUID = -4061575888616151648L;

	/**
	 * 主键ID
	 */
	private Long id ;
	
	/**
	 * 轮转记录ID
	 */
	private Long cycleId ;
	
	/**
	 * 住院医师ID
	 */
	private Long residencyId ;
	/*
	 * 手册标准ID
	 */
	private Long manualStdId;
	private Integer commitTimes = 0;
	
	private Integer otherCommitTimes = 0;
	
	/**
	 * 轮转提交时间
	 */
	private Date commitDate;
	
	/**
	 * 其他提交时间
	 */
	private Date otherCommitDate;
	
	/**
	 * 带教老师ID
	 */
	private Long teacherId ;
	
	/**
	 * 带教老师姓名（多带教老师姓名用，隔开）
	 */
	private String teacherName;
	
	/**
	 * 是否审核
	 */
	private Integer isFinalAuditing;
	
	/**
	 * 带教老师审核状态 1.审核通过 2.审核不通过 3.未完成
	 */
	private Integer teacherCheckStatus = 4;
	
	private Integer otherTeacherCheckStatus = 4;
	
	private Integer finalTeacherCheckStatus = 4;
	
	/**
	 * 带教老师审核时间
	 */
	private Date teacherCheckDate ;

	private Date otherTeacherCheckDate ;
	
	private Date finalTeacherCheckDate ;
	
	/**
	 * 带教老师审核备注
	 */
	private String teacherCheckRemark ;
	
	private String otherTeacherCheckRemark ;
	
	private String finalTeacherCheckRemark ;
	
	/**
	 * 科室主任ID
	 */
	private Long directorId ;
	
	/**
	 * 科室主任审核状态 1.审核通过 2.审核不通过 3.未完成
	 */
	private Integer directorCheckStatus  = 4;
	
	private Integer otherDirectorCheckStatus = 4 ;
	
	private Integer finalDirectorCheckStatus  = 4;
	
	/**
	 * 科室主任审核时间
	 */
	private Date directorCheckDate ;
	
	private Date otherDirectorCheckDate ;
	
	private Date finalDirectorCheckDate ;
	
	/**
	 * 科室主任审核备注
	 */
	private String directorCheckRemark ;
	
	private String otherDirectorCheckRemark ;
	
	private String finalDirectorCheckRemark ;
	
	/**
	 * 医院ID
	 */
	private Long hospUserId ;
	
	/**
	 * 医院审核状态 1.审核通过 2.审核不通过 3.未完成
	 */
	private Integer hospCheckStatus = 4 ;
	
	private Integer otherHospCheckStatus  = 4;
	
	private Integer finalHospCheckStatus  = 4;
	
	/**
	 * 医院审核时间
	 */
	private Date hospCheckDate ;
	
	private Date otherHospCheckDate ;
	
	private Date finalHospCheckDate ;
	
	/**
	 * 医院审核备注
	 */
	private String hospCheckRemark ;
	
	private String otherHospCheckRemark ;
	
	private String finalHospCheckRemark ;
	
	/**
	 * 基地ID
	 */
	private Long baseUserId ;
	
	/**
	 * 基地审核状态 1.审核通过 2.审核不通过 3.未完成
	 */
	private Integer baseCheckStatus = 4 ;
	
	private Integer otherBaseCheckStatus = 4 ;
	
	private Integer finalBaseCheckStatus = 4 ;
	
	/**
	 * 基地审核时间
	 */
	private Date baseCheckDate ;
	
	private Date otherBaseCheckDate ;
	
	private Date finalBaseCheckDate ;
	
	/**
	 * 基地审核备注
	 */
	private String baseCheckRemark ;
	
	private String otherBaseCheckRemark ;
	
	private String finalBaseCheckRemark ;
	
	private String teacher;
	
	private String director;
	
	private String base;
	
	private String hosp;
	
	private String otherTeacher;
	
	private String otherDirector;
	
	private String otherBase;
	
	private String otherHosp;	
	
	private String finalTeacher;
	
	private String finalDirector;
	
	private String finalBase;
	
	private String finalHosp;	
	
	private Long stdDeptId;
	
	private Long zyyDeptId;
	
	private String stdDeptName;
	
	private String deptName;
	
	private String realName;
	
	private List<ZyyCycleResiCycleCheck> cycleCheckList;
	
	private ZyyDeptEvaluation deptEvaluation;			// 评价
	
	private List cycleCheckResult;						// 轮转相关审核结果
	
	private List otherCheckResult;						// 其他审核结果
	
	private List finalCheckResult;						// 终审审核结果
	
	private Integer itemCount = 0;
	
	private Date enterDate;
	
	private Date leaveDate;
	
	private boolean hasCycleCheckPrivilege;
	
	private boolean hasOtherCheckPrivilege;
	
	private boolean hasFinalCheckPrivilege;
	
	private Integer finalCheckStatus;
	
	private Date finalCheckDate;
	
	private String timeSection;
	
	
	private Integer commitStatus;
	
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getCycleId() {
		return cycleId;
	}

	public void setCycleId(Long cycleId) {
		this.cycleId = cycleId;
	}

	public Long getResidencyId() {
		return residencyId;
	}

	public void setResidencyId(Long residencyId) {
		this.residencyId = residencyId;
	}

	public Long getManualStdId() {
		return manualStdId;
	}

	public void setManualStdId(Long manualStdId) {
		this.manualStdId = manualStdId;
	}

	@JsonSerialize(using = SimpleDateSerializer.class)
	public Date getCommitDate() {
		return commitDate;
	}

	public void setCommitDate(Date commitDate) {
		this.commitDate = commitDate;
	}

	public Long getTeacherId() {
		return teacherId;
	}

	public void setTeacherId(Long teacherId) {
		this.teacherId = teacherId;
	}
	
	public String getTeacherName() {
		return teacherName;
	}

	public void setTeacherName(String teacherName) {
		this.teacherName = teacherName;
	}

	public Integer getTeacherCheckStatus() {
		return teacherCheckStatus != null ? teacherCheckStatus : 4;
	}

	public void setTeacherCheckStatus(Integer teacherCheckStatus) {
		this.teacherCheckStatus = teacherCheckStatus;
	}

	public Date getTeacherCheckDate() {
		return teacherCheckDate;
	}

	public void setTeacherCheckDate(Date teacherCheckDate) {
		this.teacherCheckDate = teacherCheckDate;
	}

	public String getTeacherCheckRemark() {
		return teacherCheckRemark;
	}

	public void setTeacherCheckRemark(String teacherCheckRemark) {
		this.teacherCheckRemark = teacherCheckRemark;
	}

	public Long getDirectorId() {
		return directorId;
	}

	public void setDirectorId(Long directorId) {
		this.directorId = directorId;
	}

	public Integer getDirectorCheckStatus() {
		return directorCheckStatus != null ? directorCheckStatus : 4;
	}

	public void setDirectorCheckStatus(Integer directorCheckStatus) {
		this.directorCheckStatus = directorCheckStatus;
	}

	public Date getDirectorCheckDate() {
		return directorCheckDate;
	}

	public void setDirectorCheckDate(Date directorCheckDate) {
		this.directorCheckDate = directorCheckDate;
	}

	public String getDirectorCheckRemark() {
		return directorCheckRemark;
	}

	public void setDirectorCheckRemark(String directorCheckRemark) {
		this.directorCheckRemark = directorCheckRemark;
	}

	public Long getHospUserId() {
		return hospUserId;
	}

	public void setHospUserId(Long hospUserId) {
		this.hospUserId = hospUserId;
	}

	public Integer getHospCheckStatus() {
		return hospCheckStatus != null ? hospCheckStatus : 4;
	}

	public void setHospCheckStatus(Integer hospCheckStatus) {
		this.hospCheckStatus = hospCheckStatus;
	}

	public Date getHospCheckDate() {
		return hospCheckDate;
	}

	public void setHospCheckDate(Date hospCheckDate) {
		this.hospCheckDate = hospCheckDate;
	}

	public String getHospCheckRemark() {
		return hospCheckRemark;
	}

	public void setHospCheckRemark(String hospCheckRemark) {
		this.hospCheckRemark = hospCheckRemark;
	}

	public Long getBaseUserId() {
		return baseUserId;
	}

	public void setBaseUserId(Long baseUserId) {
		this.baseUserId = baseUserId;
	}

	public Integer getBaseCheckStatus() {
		return baseCheckStatus != null ? baseCheckStatus : 4;
	}

	public void setBaseCheckStatus(Integer baseCheckStatus) {
		this.baseCheckStatus = baseCheckStatus;
	}

	public Date getBaseCheckDate() {
		return baseCheckDate;
	}

	public void setBaseCheckDate(Date baseCheckDate) {
		this.baseCheckDate = baseCheckDate;
	}

	public String getBaseCheckRemark() {
		return baseCheckRemark;
	}

	public void setBaseCheckRemark(String baseCheckRemark) {
		this.baseCheckRemark = baseCheckRemark;
	}

	public String getTeacher() {
		return teacher;
	}

	public void setTeacher(String teacher) {
		this.teacher = teacher;
	}

	public String getDirector() {
		return director;
	}

	public void setDirector(String director) {
		this.director = director;
	}

	public String getBase() {
		return base;
	}

	public void setBase(String base) {
		this.base = base;
	}

	public String getHosp() {
		return hosp;
	}

	public void setHosp(String hosp) {
		this.hosp = hosp;
	}

	public Long getStdDeptId() {
		return stdDeptId;
	}

	public void setStdDeptId(Long stdDeptId) {
		this.stdDeptId = stdDeptId;
	}

	public String getStdDeptName() {
		return stdDeptName;
	}

	public void setStdDeptName(String stdDeptName) {
		this.stdDeptName = stdDeptName;
	}

	public List<ZyyCycleResiCycleCheck> getCycleCheckList() {
		return cycleCheckList;
	}

	public void setCycleCheckList(List<ZyyCycleResiCycleCheck> cycleCheckList) {
		this.cycleCheckList = cycleCheckList;
	}

	public ZyyDeptEvaluation getDeptEvaluation() {
		return deptEvaluation;
	}

	public void setDeptEvaluation(ZyyDeptEvaluation deptEvaluation) {
		this.deptEvaluation = deptEvaluation;
	}

	/**
	 * 依照审核顺序添加各级的审核结果
	 * @param name
	 * @param status
	 * @param date
	 * @param remark
	 */
	public void addCycleCheckResult(String name, Integer status,
			Date date, String remark) {
		if(this.cycleCheckResult == null)
			this.cycleCheckResult = Lists.newArrayList();
		if(this.commitTimes == null || this.commitTimes == 0)
			status = -1;
		this.cycleCheckResult.add(createCheckResult(name, status, date, remark));
	}
	
	public void addOtherCheckResult(String name, Integer status,
			Date date, String remark) {
		if(this.otherCheckResult == null)
			this.otherCheckResult = Lists.newArrayList();
		if(this.otherCommitTimes == null || this.otherCommitTimes == 0)
			status = -1;
		this.otherCheckResult.add(createCheckResult(name, status, date, remark));
	}

	public void addFinalCheckResult(String name, Integer status,
			Date date, String remark) {
		if(this.finalCheckResult == null)
			this.finalCheckResult = Lists.newArrayList();
		this.finalCheckResult.add(createCheckResult(name, status, date, remark));
	}
	
	private static Map createCheckResult(String name, Integer status,
			Date date, String remark) {
		if(name == null)
			name = "";
		if(remark == null)
			remark = "";
		String statusLabel = "--";
		if(status != null) {
			if(0 == status || 4 == status)
				statusLabel = "未审核";
			else if(1 == status)
				statusLabel = "审核通过";
			else if(2 == status)
				statusLabel = "审核不通过";
			else if(3 == status)
				statusLabel = "未完成";
		} else {
			status = 4;
		}
		Map row = Maps.newHashMap();
		row.put("name", name);
		row.put("status", status);
		row.put("statusLabel", statusLabel);
		row.put("date", date);
		row.put("remark", remark);
		return row;
	}
	
	@JsonSerialize(using = SimpleDateSerializer.class)
	public Date getOtherCommitDate() {
		return otherCommitDate;
	}

	public void setOtherCommitDate(Date otherCommitDate) {
		this.otherCommitDate = otherCommitDate;
	}

	public List getCycleCheckResult() {
		return cycleCheckResult;
	}

	public void setCycleCheckResult(List cycleCheckResult) {
		this.cycleCheckResult = cycleCheckResult;
	}

	public List getOtherCheckResult() {
		return otherCheckResult;
	}

	public void setOtherCheckResult(List otherCheckResult) {
		this.otherCheckResult = otherCheckResult;
	}

	public List getFinalCheckResult() {
		return finalCheckResult;
	}

	public void setFinalCheckResult(List finalCheckResult) {
		this.finalCheckResult = finalCheckResult;
	}

	public void setOtherTeacherCheckStatus(Integer otherTeacherCheckStatus) {
		this.otherTeacherCheckStatus = otherTeacherCheckStatus;
	}

	public void setOtherTeacherCheckDate(Date otherTeacherCheckDate) {
		this.otherTeacherCheckDate = otherTeacherCheckDate;
	}

	public void setOtherTeacherCheckRemark(String otherTeacherCheckRemark) {
		this.otherTeacherCheckRemark = otherTeacherCheckRemark;
	}

	public void setOtherDirectorCheckStatus(Integer otherDirectorCheckStatus) {
		this.otherDirectorCheckStatus = otherDirectorCheckStatus;
	}

	public void setOtherDirectorCheckDate(Date otherDirectorCheckDate) {
		this.otherDirectorCheckDate = otherDirectorCheckDate;
	}

	public void setOtherDirectorCheckRemark(String otherDirectorCheckRemark) {
		this.otherDirectorCheckRemark = otherDirectorCheckRemark;
	}

	public void setOtherHospCheckStatus(Integer otherHospCheckStatus) {
		this.otherHospCheckStatus = otherHospCheckStatus;
	}

	public void setOtherHospCheckDate(Date otherHospCheckDate) {
		this.otherHospCheckDate = otherHospCheckDate;
	}

	public void setOtherHospCheckRemark(String otherHospCheckRemark) {
		this.otherHospCheckRemark = otherHospCheckRemark;
	}

	public void setOtherBaseCheckStatus(Integer otherBaseCheckStatus) {
		this.otherBaseCheckStatus = otherBaseCheckStatus;
	}

	public void setOtherBaseCheckDate(Date otherBaseCheckDate) {
		this.otherBaseCheckDate = otherBaseCheckDate;
	}

	public void setOtherBaseCheckRemark(String otherBaseCheckRemark) {
		this.otherBaseCheckRemark = otherBaseCheckRemark;
	}

	public void setOtherTeacher(String otherTeacher) {
		this.otherTeacher = otherTeacher;
	}

	public void setOtherDirector(String otherDirector) {
		this.otherDirector = otherDirector;
	}

	public void setOtherBase(String otherBase) {
		this.otherBase = otherBase;
	}

	public void setOtherHosp(String otherHosp) {
		this.otherHosp = otherHosp;
	}

	public Integer getOtherTeacherCheckStatus() {
		return otherTeacherCheckStatus != null ? otherTeacherCheckStatus : 4;
	}

	public Date getOtherTeacherCheckDate() {
		return otherTeacherCheckDate;
	}

	public String getOtherTeacherCheckRemark() {
		return otherTeacherCheckRemark;
	}

	public Integer getOtherDirectorCheckStatus() {
		return otherDirectorCheckStatus != null ? otherDirectorCheckStatus : 4;
	}

	public Date getOtherDirectorCheckDate() {
		return otherDirectorCheckDate;
	}

	public String getOtherDirectorCheckRemark() {
		return otherDirectorCheckRemark;
	}

	public Integer getOtherHospCheckStatus() {
		return otherHospCheckStatus != null ? otherHospCheckStatus : 4;
	}

	public Date getOtherHospCheckDate() {
		return otherHospCheckDate;
	}

	public String getOtherHospCheckRemark() {
		return otherHospCheckRemark;
	}

	public Integer getOtherBaseCheckStatus() {
		return otherBaseCheckStatus != null ? otherBaseCheckStatus : 4;
	}

	public Date getOtherBaseCheckDate() {
		return otherBaseCheckDate;
	}

	public String getOtherBaseCheckRemark() {
		return otherBaseCheckRemark;
	}

	public String getOtherTeacher() {
		return otherTeacher;
	}

	public String getOtherDirector() {
		return otherDirector;
	}

	public String getOtherBase() {
		return otherBase;
	}

	public String getOtherHosp() {
		return otherHosp;
	}

	public Integer getFinalTeacherCheckStatus() {
		return finalTeacherCheckStatus != null ? finalTeacherCheckStatus :4;
	}

	public void setFinalTeacherCheckStatus(Integer finalTeacherCheckStatus) {
		this.finalTeacherCheckStatus = finalTeacherCheckStatus;
	}

	public Date getFinalTeacherCheckDate() {
		return finalTeacherCheckDate;
	}

	public void setFinalTeacherCheckDate(Date finalTeacherCheckDate) {
		this.finalTeacherCheckDate = finalTeacherCheckDate;
	}

	public String getFinalTeacherCheckRemark() {
		return finalTeacherCheckRemark;
	}

	public void setFinalTeacherCheckRemark(String finalTeacherCheckRemark) {
		this.finalTeacherCheckRemark = finalTeacherCheckRemark;
	}

	public Integer getFinalDirectorCheckStatus() {
		return finalDirectorCheckStatus != null ? finalDirectorCheckStatus : 4;
	}

	public void setFinalDirectorCheckStatus(Integer finalDirectorCheckStatus) {
		this.finalDirectorCheckStatus = finalDirectorCheckStatus;
	}

	public Date getFinalDirectorCheckDate() {
		return finalDirectorCheckDate;
	}

	public void setFinalDirectorCheckDate(Date finalDirectorCheckDate) {
		this.finalDirectorCheckDate = finalDirectorCheckDate;
	}

	public String getFinalDirectorCheckRemark() {
		return finalDirectorCheckRemark;
	}

	public void setFinalDirectorCheckRemark(String finalDirectorCheckRemark) {
		this.finalDirectorCheckRemark = finalDirectorCheckRemark;
	}

	public Integer getFinalHospCheckStatus() {
		return finalHospCheckStatus != null ? finalHospCheckStatus : 4;
	}

	public void setFinalHospCheckStatus(Integer finalHospCheckStatus) {
		this.finalHospCheckStatus = finalHospCheckStatus;
	}

	public Date getFinalHospCheckDate() {
		return finalHospCheckDate;
	}

	public void setFinalHospCheckDate(Date finalHospCheckDate) {
		this.finalHospCheckDate = finalHospCheckDate;
	}

	public String getFinalHospCheckRemark() {
		return finalHospCheckRemark;
	}

	public void setFinalHospCheckRemark(String finalHospCheckRemark) {
		this.finalHospCheckRemark = finalHospCheckRemark;
	}

	public Integer getFinalBaseCheckStatus() {
		return finalBaseCheckStatus != null ? finalBaseCheckStatus : 4;
	}

	public void setFinalBaseCheckStatus(Integer finalBaseCheckStatus) {
		this.finalBaseCheckStatus = finalBaseCheckStatus;
	}

	public Date getFinalBaseCheckDate() {
		return finalBaseCheckDate;
	}

	public void setFinalBaseCheckDate(Date finalBaseCheckDate) {
		this.finalBaseCheckDate = finalBaseCheckDate;
	}

	public String getFinalBaseCheckRemark() {
		return finalBaseCheckRemark;
	}

	public void setFinalBaseCheckRemark(String finalBaseCheckRemark) {
		this.finalBaseCheckRemark = finalBaseCheckRemark;
	}

	public String getFinalTeacher() {
		return finalTeacher;
	}

	public void setFinalTeacher(String finalTeacher) {
		this.finalTeacher = finalTeacher;
	}

	public String getFinalDirector() {
		return finalDirector;
	}

	public void setFinalDirector(String finalDirector) {
		this.finalDirector = finalDirector;
	}

	public String getFinalBase() {
		return finalBase;
	}

	public void setFinalBase(String finalBase) {
		this.finalBase = finalBase;
	}

	public String getFinalHosp() {
		return finalHosp;
	}

	public void setFinalHosp(String finalHosp) {
		this.finalHosp = finalHosp;
	}

	public void setCommitTimes(Integer commitTimes) {
		this.commitTimes = commitTimes;
	}

	public Integer getOtherCommitTimes() {
		return otherCommitTimes != null ? otherCommitTimes : 0;
	}

	public void setOtherCommitTimes(Integer otherCommitTimes) {
		this.otherCommitTimes = otherCommitTimes;
	}

	public Integer getCommitTimes() {
		return commitTimes != null ? commitTimes : 0;
	}

	public Long getZyyDeptId() {
		return zyyDeptId;
	}

	public void setZyyDeptId(Long zyyDeptId) {
		this.zyyDeptId = zyyDeptId;
	}

	public String getDeptName() {
		return deptName;
	}

	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}

	@Override
	public String toString() {
		return "ZyyCycleResiCycleCheck [stdDeptName=" + stdDeptName + ", itemCount=" + itemCount + "]";
	}

	public Integer getItemCount() {
		return itemCount;
	}

	public void setItemCount(Integer itemCount) {
		this.itemCount = itemCount;
	}

	@JsonSerialize(using = SimpleDateSerializer.class)
	public Date getEnterDate() {
		return enterDate;
	}

	public void setEnterDate(Date enterDate) {
		this.enterDate = enterDate;
	}

	@JsonSerialize(using = SimpleDateSerializer.class)
	public Date getLeaveDate() {
		return leaveDate;
	}

	public void setLeaveDate(Date leaveDate) {
		this.leaveDate = leaveDate;
	}

	public boolean isHasCycleCheckPrivilege() {
		return hasCycleCheckPrivilege;
	}

	public void setHasCycleCheckPrivilege(boolean hasCycleCheckPrivilege) {
		this.hasCycleCheckPrivilege = hasCycleCheckPrivilege;
	}

	public boolean isHasOtherCheckPrivilege() {
		return hasOtherCheckPrivilege;
	}

	public void setHasOtherCheckPrivilege(boolean hasOtherCheckPrivilege) {
		this.hasOtherCheckPrivilege = hasOtherCheckPrivilege;
	}

	public boolean isHasFinalCheckPrivilege() {
		return hasFinalCheckPrivilege;
	}

	public void setHasFinalCheckPrivilege(boolean hasFinalCheckPrivilege) {
		this.hasFinalCheckPrivilege = hasFinalCheckPrivilege;
	}

	public Integer getFinalCheckStatus() {
		return finalCheckStatus;
	}

	public void setFinalCheckStatus(Integer finalCheckStatus) {
		this.finalCheckStatus = finalCheckStatus;
	}

	@JsonSerialize(using = SimpleDateSerializer.class)
	public Date getFinalCheckDate() {
		return finalCheckDate;
	}

	public void setFinalCheckDate(Date finalCheckDate) {
		this.finalCheckDate = finalCheckDate;
	}

	public String getTimeSection() {
		return timeSection;
	}

	public void setTimeSection(String timeSection) {
		this.timeSection = timeSection;
	}

	public Integer getIsFinalAuditing() {
		if(isFinalAuditing == null)
			isFinalAuditing = 0;
		return isFinalAuditing;
	}

	public void setIsFinalAuditing(Integer isFinalAuditing) {
		this.isFinalAuditing = isFinalAuditing;
	}

	public Integer getCommitStatus() {
		return commitStatus;
	}

	public void setCommitStatus(Integer commitStatus) {
		this.commitStatus = commitStatus;
	}

	public String getRealName() {
		return realName;
	}

	public void setRealName(String realName) {
		this.realName = realName == null ? null : realName.trim();
	}

}
