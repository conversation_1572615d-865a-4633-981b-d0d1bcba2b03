package com.hys.zyy.manage.model;

/**
 * 电子登记手册轮转科室及时间信息
 * <AUTHOR>
 */
public class ZyyCycleResiCycleVO extends ZyyBaseObject {

	private static final long serialVersionUID = 5116234709099114660L;
	
	private Long stdDeptId;
	
	private Long zyyDeptId;
	
	private String stdDeptName;
	
	private String deptName;
	
	private Integer cycleType;
	
	private String teacherName; //带教老师姓名（多带教老师姓名用，隔开）
	
	private String timeSection; //时间周期
	
	private String remark; //备注
	
	private Integer timeCount;//时间统计 月数或周数

	public Long getStdDeptId() {
		return stdDeptId;
	}

	public void setStdDeptId(Long stdDeptId) {
		this.stdDeptId = stdDeptId;
	}

	public Long getZyyDeptId() {
		return zyyDeptId;
	}

	public void setZyyDeptId(Long zyyDeptId) {
		this.zyyDeptId = zyyDeptId;
	}

	public String getStdDeptName() {
		return stdDeptName;
	}

	public void setStdDeptName(String stdDeptName) {
		this.stdDeptName = stdDeptName;
	}

	public String getDeptName() {
		return deptName;
	}

	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}

	public String getTeacherName() {
		return teacherName;
	}

	public void setTeacherName(String teacherName) {
		this.teacherName = teacherName;
	}

	public String getTimeSection() {
		return timeSection;
	}

	public void setTimeSection(String timeSection) {
		this.timeSection = timeSection;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public Integer getCycleType() {
		return cycleType;
	}

	public void setCycleType(Integer cycleType) {
		this.cycleType = cycleType;
	}

	public Integer getTimeCount() {
		return timeCount;
	}

	public void setTimeCount(Integer timeCount) {
		this.timeCount = timeCount;
	}
	
}
