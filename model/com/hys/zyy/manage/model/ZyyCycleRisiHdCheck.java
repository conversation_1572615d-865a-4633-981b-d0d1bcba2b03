package com.hys.zyy.manage.model;

import java.util.Date;

public class ZyyCycleRisiHdCheck extends ZyyBaseObject {

	private static final long serialVersionUID = -4770073532202934577L;
	/**
	 * ID
	 */
	private Long ID;
	/**
	 *  轮转记录ID
	 */
	private Long cycleId;
	/**
	 * 住院医师ID
	 */
	private Long residencyId ;
	/**
	 * 轮转提交时间
	 */
	private Date commitDate;
	/**
	 * 审核者类型
	 */
	private Integer verifierType;
	/**
	 * 轮转带教对应ID
	 */
	private Long cycleTeacherId;
	/**
	 * 轮转开始时间
	 */
	private Date cycleStartDate; 
	/**
	 * 轮转结束时间
	 */
	private Date cycleEndDate; 
	/**
	 * 审核者Id
	 */
	private Long verifierId;
	/**
	 * 审核状态 1.审核通过 2.审核不通过 4.未审核
	 */
	private Integer checkStatus = 4;
	/**
	 * 审核时间
	 */
	private Date checkDate; 
	/**
	 * 审核备注
	 */
	private String checkRemark;
	//开始时间
	private String startDate;
	//结束时间
	private String endDate;
	//科室id
	private Long deptId;
	
	public Long getID() {
		return ID;
	}
	public void setID(Long iD) {
		ID = iD;
	}
	public Long getCycleId() {
		return cycleId;
	}
	public void setCycleId(Long cycleId) {
		this.cycleId = cycleId;
	}
	public Long getResidencyId() {
		return residencyId;
	}
	public void setResidencyId(Long residencyId) {
		this.residencyId = residencyId;
	}
	public Date getCommitDate() {
		return commitDate;
	}
	public void setCommitDate(Date commitDate) {
		this.commitDate = commitDate;
	}
	public Integer getVerifierType() {
		return verifierType;
	}
	public void setVerifierType(Integer verifierType) {
		this.verifierType = verifierType;
	}
	public Long getCycleTeacherId() {
		return cycleTeacherId;
	}
	public void setCycleTeacherId(Long cycleTeacherId) {
		this.cycleTeacherId = cycleTeacherId;
	}
	public Date getCycleStartDate() {
		return cycleStartDate;
	}
	public void setCycleStartDate(Date cycleStartDate) {
		this.cycleStartDate = cycleStartDate;
	}
	public Date getCycleEndDate() {
		return cycleEndDate;
	}
	public void setCycleEndDate(Date cycleEndDate) {
		this.cycleEndDate = cycleEndDate;
	}
	public Long getVerifierId() {
		return verifierId;
	}
	public void setVerifierId(Long verifierId) {
		this.verifierId = verifierId;
	}
	public Integer getCheckStatus() {
		return checkStatus;
	}
	public void setCheckStatus(Integer checkStatus) {
		this.checkStatus = checkStatus;
	}
	public Date getCheckDate() {
		return checkDate;
	}
	public void setCheckDate(Date checkDate) {
		this.checkDate = checkDate;
	}
	public String getCheckRemark() {
		return checkRemark;
	}
	public void setCheckRemark(String checkRemark) {
		this.checkRemark = checkRemark;
	}
	public String getStartDate() {
		return startDate;
	}
	public void setStartDate(String startDate) {
		this.startDate = startDate;
	}
	public String getEndDate() {
		return endDate;
	}
	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}
	public Long getDeptId() {
		return deptId;
	}
	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}
	
}
