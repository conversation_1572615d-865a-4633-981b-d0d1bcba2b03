package com.hys.zyy.manage.model;

import java.util.Date;
import java.util.List;

import com.hys.zyy.manage.util.Utils;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：张伟清 2012-04-16
 * 
 * 描述：轮转表
 * 
 * 说明:
 */
public class ZyyCycleTable extends ZyyBaseObject {

	private static final long serialVersionUID = 9144404886721443616L;
	
	/**
	 * 主键ID
	 */
	private Long id ;
	
	/**
	 * 医院ID 
	 */
	private Long zyyOrgId ;
	
	/**
	 * 创建者
	 */
	private Long zyyUserId ;
	
	/**
	 * 最后更新者
	 */
	private Long lastZyyUserId ;
	
	/**
	 * 表名称
	 */
	private String tableName ;
	
	/**
	 * 建表时间
	 */
	private Date createDate ;
	
	/**
	 * 轮转开始时间
	 */
	private Date cycleStartDate ;
	
	/**
	 * 轮转结束时间
	 */
	private Date cycleEndDate ;
	
	/**
	 * 最后更新时间
	 */
	private Date lastUpdateDate ;
	
	/**
	 * 轮转表类别 1-自动 2-手动
	 */
	private Integer tableType ;
	
	/**
	 * 状态 1-正常 1-删除 3-不轮转(一览表轮转调整，用于改为不轮转)
	 */
	private Integer status ;
	
	/**
	 * 创建年度
	 */
	private String createYear;
	
	/**
	 * 轮转表当前步骤 1-第一步 2-第二步 3-第三步 4-第四步 5-第五步
	 */
	private Integer tableCurrentStep ;
	
	/**
	 * 基地ID
	 */
	private Long baseId ;
	
	/**
	 * 科室列表
	 */
	private List<ZyyDeptVO> deptList ;
	
	/**
	 * 轮转表排班类型 1-日 2-周 3-月 4-半月
	 */
	private Integer cycleType ;
	
	/**
	 * 轮转表排班类型值 1-日 2-周 3-月 4-半月
	 */
	private String cycleTypeName ;
	
	/**
	 * 轮转开始时间(周)
	 */
	private Integer cycleStartDateWeek ;
	/**
	 * 当前轮转表下 轮转数量
	 */
	private Integer cycleNumber;
	/**
	 * 轮转表开始日期是一号（轮转类型为月）
	 */
	private boolean startWithOne;
	
	public String getCreateYear() {
		return createYear;
	}

	public void setCreateYear(String createYear) {
		this.createYear = createYear;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getZyyOrgId() {
		return zyyOrgId;
	}

	public void setZyyOrgId(Long zyyOrgId) {
		this.zyyOrgId = zyyOrgId;
	}

	public Long getZyyUserId() {
		return zyyUserId;
	}

	public void setZyyUserId(Long zyyUserId) {
		this.zyyUserId = zyyUserId;
	}

	public Long getLastZyyUserId() {
		return lastZyyUserId;
	}

	public void setLastZyyUserId(Long lastZyyUserId) {
		this.lastZyyUserId = lastZyyUserId;
	}

	public String getTableName() {
		return tableName;
	}

	public void setTableName(String tableName) {
		this.tableName = tableName;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Date getCycleStartDate() {
		return cycleStartDate;
	}

	public void setCycleStartDate(Date cycleStartDate) {
		this.cycleStartDate = cycleStartDate;
	}

	public Date getCycleEndDate() {
		return cycleEndDate;
	}

	public void setCycleEndDate(Date cycleEndDate) {
		this.cycleEndDate = cycleEndDate;
	}

	public Date getLastUpdateDate() {
		return lastUpdateDate;
	}

	public void setLastUpdateDate(Date lastUpdateDate) {
		this.lastUpdateDate = lastUpdateDate;
	}

	public Integer getTableType() {
		return tableType;
	}

	public void setTableType(Integer tableType) {
		this.tableType = tableType;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Integer getTableCurrentStep() {
		return tableCurrentStep;
	}

	public void setTableCurrentStep(Integer tableCurrentStep) {
		this.tableCurrentStep = tableCurrentStep;
	}

	public Long getBaseId() {
		return baseId;
	}

	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}

	public List<ZyyDeptVO> getDeptList() {
		return deptList;
	}

	public void setDeptList(List<ZyyDeptVO> deptList) {
		this.deptList = deptList;
	}

	public Integer getCycleType() {
		return cycleType;
	}

	public void setCycleType(Integer cycleType) {
		this.cycleType = cycleType;
	}

	public Integer getCycleStartDateWeek() {
		return cycleStartDateWeek;
	}

	public void setCycleStartDateWeek(Integer cycleStartDateWeek) {
		this.cycleStartDateWeek = cycleStartDateWeek;
	}
	
	public Integer getCycleNumber() {
		return cycleNumber;
	}

	public void setCycleNumber(Integer cycleNumber) {
		this.cycleNumber = cycleNumber;
	}

	public String getCycleTypeName() {
		return cycleTypeName;
	}

	public void setCycleTypeName(String cycleTypeName) {
		this.cycleTypeName = cycleTypeName;
	}
	
	public boolean isStartWithOne() {
		if (this.cycleType == null || this.cycleStartDate == null)
			return false;
		if (cycleType == 3 && Utils.getMonthFirstDay(this.cycleStartDate).equals(this.cycleStartDate))
			return true;
		return false;
	}

	public void setStartWithOne(boolean startWithOne) {
		this.startWithOne = startWithOne;
	}
	
}
