package com.hys.zyy.manage.model;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：张伟清 2012-04-16
 * 
 * 描述：轮转表基地科室
 * 
 * 说明:
 */
public class ZyyCycleTableDept extends ZyyBaseObject {

	private static final long serialVersionUID = 8982445716216950205L;

	/**
	 * 主键ID
	 */
	private Long id ;
	
	/**
	 * 轮转表ID 
	 */
	private Long cycleTableId ;
	
	/**
	 * 基地ID
	 */
	private Long baseId ;
	
	/**
	 * 科室ID
	 */
	private Long deptId ;
	
	/**
	 * 状态
	 */
	private Integer status ;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getCycleTableId() {
		return cycleTableId;
	}

	public void setCycleTableId(Long cycleTableId) {
		this.cycleTableId = cycleTableId;
	}

	public Long getBaseId() {
		return baseId;
	}

	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}

	public Long getDeptId() {
		return deptId;
	}

	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}
}
