package com.hys.zyy.manage.model;

import java.util.Date;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：张伟清 2012-10-24
 * 
 * 描述：轮转表历史记录
 * 
 * 说明:
 */
public class ZyyCycleTableHistory extends ZyyBaseObject {

	private static final long serialVersionUID = 9165202730621124465L;

	/**
	 * 主键ID
	 */
	private Long id ;
	
	/**
	 * 医院ID
	 */
	private Long zyyOrgId ;
	
	/**
	 * 操作者ID
	 */
	private Long zyyUserId ;
	
	/**
	 * 轮转表ID
	 */
	private Long cycleTableId ;
	
	/**
	 * 历史名称
	 */
	private String historyName ;
	
	/**
	 * 最后更新时间
	 */
	private Date lastUpdateDate ;
	
	/**
	 * 历史表属性
	 */
	private Integer historyProp ;
	
	/**
	 * 历史表类别
	 */
	private Integer historyType ;
	
	/**
	 * 开始时间
	 */
	private Date startDate ;
	
	/**
	 * 结束时间
	 */
	private Date endDate ;
	
	/**
	 * 关联历史记录ID
	 */
	private Long relatedHistoryId ;
	
	/**
	 * 创建年度
	 */
	private String createYear ;
	
	/**
	 * 轮转表类别 1.自动 2.手动
	 */
	private Integer tableType ;
	
	/**
	 * 轮转类别 1.日 2.周 3.月 4.半月
	 */
	private Integer cycleType ;

	public ZyyCycleTableHistory() {
		super();
	}
	
	public ZyyCycleTableHistory(Long id) {
		super();
		this.id = id;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getZyyOrgId() {
		return zyyOrgId;
	}

	public void setZyyOrgId(Long zyyOrgId) {
		this.zyyOrgId = zyyOrgId;
	}

	public Long getZyyUserId() {
		return zyyUserId;
	}

	public Long getCycleTableId() {
		return cycleTableId;
	}

	public void setCycleTableId(Long cycleTableId) {
		this.cycleTableId = cycleTableId;
	}

	public void setZyyUserId(Long zyyUserId) {
		this.zyyUserId = zyyUserId;
	}

	public String getHistoryName() {
		return historyName;
	}

	public void setHistoryName(String historyName) {
		this.historyName = historyName;
	}

	public Date getLastUpdateDate() {
		return lastUpdateDate;
	}

	public void setLastUpdateDate(Date lastUpdateDate) {
		this.lastUpdateDate = lastUpdateDate;
	}

	public Integer getHistoryProp() {
		return historyProp;
	}

	public void setHistoryProp(Integer historyProp) {
		this.historyProp = historyProp;
	}

	public Integer getHistoryType() {
		return historyType;
	}

	public void setHistoryType(Integer historyType) {
		this.historyType = historyType;
	}

	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	public Long getRelatedHistoryId() {
		return relatedHistoryId;
	}

	public void setRelatedHistoryId(Long relatedHistoryId) {
		this.relatedHistoryId = relatedHistoryId;
	}
	
	public String getCreateYear() {
		return createYear;
	}

	public void setCreateYear(String createYear) {
		this.createYear = createYear;
	}

	public Integer getTableType() {
		return tableType;
	}

	public void setTableType(Integer tableType) {
		this.tableType = tableType;
	}

	public Integer getCycleType() {
		return cycleType;
	}

	public void setCycleType(Integer cycleType) {
		this.cycleType = cycleType;
	}
}