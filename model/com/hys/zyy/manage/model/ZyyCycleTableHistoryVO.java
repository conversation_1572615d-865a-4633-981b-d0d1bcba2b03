package com.hys.zyy.manage.model;

import java.util.List;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：张伟清 2012-10-24
 * 
 * 描述：轮转表历史记录
 * 
 * 说明:
 */
public class ZyyCycleTableHistoryVO extends ZyyCycleTableHistory {

	private static final long serialVersionUID = -1074839901552528751L;

	/**
	 * 创建用户名称
	 */
	private String createUserName ;
	
	/**
	 * 操作轮转表 用户类别
	 */
	private Integer zyyUserType ;
	
	/**
	 * 基地名称
	 */
	private String baseName ;
	
	/**
	 * 基地别名
	 */
	private String baseAliasName ;
	
	/**
	 * 组织机构名称
	 */
	private String orgName ;
	
	/**
	 * 组织机构别名
	 */
	private String orgAliasName ;
	
	/**
	 * 科室轮转列表
	 */
	private List<ZyyDeptVO> deptList ;
	
	/**
	 * 科室轮转列表
	 */
	private List<String> deptNameList ;
	
	/**
	 * 日期列表
	 */
	private List<ZyyDate> dateList;
	
	/**
	 * 周日期列表
	 */
	private List<ZyyCycleTableResiCycleVO> dateListForWeek;
	
	/**
	 * 周列表
	 */
	private List<ZyyCycleTableResiCycleVO> dateWeekList;
	
	/**
	 * 根据基地区分
	 */
	private List<ZyyBaseVO> baseList;
	

	/**
	 * 学员轮转列表
	 */
	private List<ZyyUserExtendVO> userExtList ;

	public String getCreateUserName() {
		return createUserName;
	}

	public void setCreateUserName(String createUserName) {
		this.createUserName = createUserName;
	}

	public Integer getZyyUserType() {
		return zyyUserType;
	}

	public void setZyyUserType(Integer zyyUserType) {
		this.zyyUserType = zyyUserType;
	}

	public String getBaseName() {
		return baseName;
	}

	public void setBaseName(String baseName) {
		this.baseName = baseName;
	}

	public String getBaseAliasName() {
		return baseAliasName;
	}

	public void setBaseAliasName(String baseAliasName) {
		this.baseAliasName = baseAliasName;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public String getOrgAliasName() {
		return orgAliasName;
	}

	public void setOrgAliasName(String orgAliasName) {
		this.orgAliasName = orgAliasName;
	}

	public List<ZyyDeptVO> getDeptList() {
		return deptList;
	}

	public void setDeptList(List<ZyyDeptVO> deptList) {
		this.deptList = deptList;
	}

	public List<ZyyUserExtendVO> getUserExtList() {
		return userExtList;
	}

	public void setUserExtList(List<ZyyUserExtendVO> userExtList) {
		this.userExtList = userExtList;
	}
	
	public List<ZyyDate> getDateList() {
		return dateList;
	}

	public void setDateList(List<ZyyDate> dateList) {
		this.dateList = dateList;
	}

	public List<ZyyBaseVO> getBaseList() {
		return baseList;
	}

	public void setBaseList(List<ZyyBaseVO> baseList) {
		this.baseList = baseList;
	}

	public List<String> getDeptNameList() {
		return deptNameList;
	}

	public void setDeptNameList(List<String> deptNameList) {
		this.deptNameList = deptNameList;
	}

	public List<ZyyCycleTableResiCycleVO> getDateWeekList() {
		return dateWeekList;
	}

	public void setDateWeekList(List<ZyyCycleTableResiCycleVO> dateWeekList) {
		this.dateWeekList = dateWeekList;
	}

	public List<ZyyCycleTableResiCycleVO> getDateListForWeek() {
		return dateListForWeek;
	}

	public void setDateListForWeek(List<ZyyCycleTableResiCycleVO> dateListForWeek) {
		this.dateListForWeek = dateListForWeek;
	}
}