package com.hys.zyy.manage.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.codehaus.jackson.map.annotate.JsonSerialize;

import com.hys.zyy.manage.json.SimpleDateSerializer;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：张伟清 2012-04-24
 * 
 * 描述：轮转表住院医师轮转
 * 
 * 说明:
 */
public class ZyyCycleTableResiCycle extends ZyyBaseObject {
	private static final long serialVersionUID = -1705570963218181521L;
	/**
	 * 主键ID
	 */
	private Long id ;

	/**
	 * 轮转表ID
	 */
	private Long cycleTableId ;
	
	/**
	 * 住院医师ID
	 */
	private Long residencyId ;
	
	/**
	 * 科室ID
	 */
	private Long deptId ;
	
	private String [] deptIds;
	
	/**
	 * 标准科室ID
	 */
	private Long zyyDeptStdId;
	
	private String stdDeptName;
	
	private String[] cycleTableResiMonth;
	
	/**
	 * 轮转类别
	 */
	private Integer cycleType ;
	
	/**
	 * 轮转时间
	 */
	private Integer cycleTime ;
	
	/**
	 * 开始时间
	 */
	private Date startDate ;
	
	/**
	 * 结束时间
	 */
	private Date endDate ;
	
	/**
	 * 顺序
	 */
	private Long seq ;
	
	/**
	 * 是否自动轮转
	 */
	private Integer isAuto ;
	
	/**
	 * 状态
	 */
	private Integer status ;
	
	/**
	 * 轮转状态
	 */
	private Integer cycleStatus ;
	
	/**
	 * 备注
	 */
	private String remark ;
	
	/**
	 * 排班人
	 */
	private Long scheduler ;
	
	/**
	 * 排班时间
	 */
	private Date scheduleDate ;
	
	/**
	 * 最后修改人
	 */
	private Long lastScheduler ;
	
	/**
	 * 最后修改时间
	 */
	private Date lastUpdateDate ;
	
	/**
	 * 带教老师最后审核状态 1.审核通过 2.审核不通过 3.未完成
	 */
	private Integer teacherCheckStatus ;

	/**
	 * 带教老师最后审核时间
	 */
	private Date teacherCheckDate ;
	
	private Long teacherId;
	
	/**
	 * 科室主任最后审核状态 1.审核通过 2.审核不通过 3.未完成
	 */
	private Integer directorCheckStatus ;
	
	/**
	 * 科室主任最后审核时间
	 */
	private Date directorCheckDate ;
	
	private Long directorId;
	
	/**
	 * 住院医师提交次数
	 */
	private Integer commitTimes ;
	
	/**
	 * 住院医师最后提交时间
	 */
	private Date residencyLastDate ;
	
	private Date hospCheckDate;//医院最后审核时间
	
	private Integer hospCheckStatus;//医院审核状态
	
	private Long hospUserId;
	
	private Date baseCheckDate;//基地审核时间
	
	private Integer baseCheckStatus;//基地审核状态
	
	private Long baseUserId;
	
	private String deptName;
	
	private Integer finalCheckStatus;	//	最终审核状态
	
	private Date finalCheckDate;		// 最终审核时间
	
	private String finalCheckUser;		// 最终审核人
	
	private String finalCheckSuggest;		// 最终意见
	
	private Date commitDate; //提交时间
	
	/**
	 * 第几周
	 */
	private String weeks ;
	
	private List<ZyyCycleTableResiCycle> list = new ArrayList<ZyyCycleTableResiCycle>();
	
	private List<ZyyCycleTime> stages;
	
	
	
	private String errorMsg;// 错误提示信息

	private int errorCode; // 错误码
	
	//轮转的时间段
	private String timeSection;
	
	public ZyyCycleTableResiCycle() {
		super();
	}

	public ZyyCycleTableResiCycle(Long deptId) {
		super();
		this.deptId = deptId;
	}

	public Date getCommitDate() {
		return commitDate;
	}

	public void setCommitDate(Date commitDate) {
		this.commitDate = commitDate;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getCycleTableId() {
		return cycleTableId;
	}

	public void setCycleTableId(Long cycleTableId) {
		this.cycleTableId = cycleTableId;
	}

	public Long getResidencyId() {
		return residencyId;
	}

	public void setResidencyId(Long residencyId) {
		this.residencyId = residencyId;
	}

	public Long getDeptId() {
		return deptId;
	}

	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}

	public Integer getCycleType() {
		return cycleType;
	}

	public void setCycleType(Integer cycleType) {
		this.cycleType = cycleType;
	}

	public Integer getCycleTime() {
		return cycleTime;
	}

	public void setCycleTime(Integer cycleTime) {
		this.cycleTime = cycleTime;
	}
	@JsonSerialize(using = SimpleDateSerializer.class) 
	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
	    this.startDate = startDate;
	}
	@JsonSerialize(using = SimpleDateSerializer.class) 
	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	public Long getSeq() {
		return seq;
	}

	public void setSeq(Long seq) {
		this.seq = seq;
	}

	public Integer getIsAuto() {
		return isAuto;
	}

	public void setIsAuto(Integer isAuto) {
		this.isAuto = isAuto;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Integer getCycleStatus() {
		return cycleStatus;
	}

	public void setCycleStatus(Integer cycleStatus) {
		this.cycleStatus = cycleStatus;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public Long getScheduler() {
		return scheduler;
	}

	public void setScheduler(Long scheduler) {
		this.scheduler = scheduler;
	}

	public Date getScheduleDate() {
		return scheduleDate;
	}

	public void setScheduleDate(Date scheduleDate) {
		this.scheduleDate = scheduleDate;
	}

	public Long getLastScheduler() {
		return lastScheduler;
	}

	public void setLastScheduler(Long lastScheduler) {
		this.lastScheduler = lastScheduler;
	}

	public Date getLastUpdateDate() {
		return lastUpdateDate;
	}

	public void setLastUpdateDate(Date lastUpdateDate) {
		this.lastUpdateDate = lastUpdateDate;
	}

	public Integer getTeacherCheckStatus() {
		return teacherCheckStatus;
	}

	public void setTeacherCheckStatus(Integer teacherCheckStatus) {
		this.teacherCheckStatus = teacherCheckStatus;
	}

	public Date getTeacherCheckDate() {
		return teacherCheckDate;
	}

	public void setTeacherCheckDate(Date teacherCheckDate) {
		this.teacherCheckDate = teacherCheckDate;
	}

	public Integer getDirectorCheckStatus() {
		return directorCheckStatus;
	}

	public void setDirectorCheckStatus(Integer directorCheckStatus) {
		this.directorCheckStatus = directorCheckStatus;
	}

	public Date getDirectorCheckDate() {
		return directorCheckDate;
	}

	public void setDirectorCheckDate(Date directorCheckDate) {
		this.directorCheckDate = directorCheckDate;
	}

	public Integer getCommitTimes() {
		return commitTimes;
	}

	public void setCommitTimes(Integer commitTimes) {
		this.commitTimes = commitTimes;
	}

	public Date getResidencyLastDate() {
		return residencyLastDate;
	}

	public void setResidencyLastDate(Date residencyLastDate) {
		this.residencyLastDate = residencyLastDate;
	}
	
	public Date getHospCheckDate() {
		return hospCheckDate;
	}

	public void setHospCheckDate(Date hospCheckDate) {
		this.hospCheckDate = hospCheckDate;
	}

	public Integer getHospCheckStatus() {
		return hospCheckStatus;
	}

	public void setHospCheckStatus(Integer hospCheckStatus) {
		this.hospCheckStatus = hospCheckStatus;
	}

	public Date getBaseCheckDate() {
		return baseCheckDate;
	}

	public void setBaseCheckDate(Date baseCheckDate) {
		this.baseCheckDate = baseCheckDate;
	}

	public Integer getBaseCheckStatus() {
		return baseCheckStatus;
	}

	public void setBaseCheckStatus(Integer baseCheckStatus) {
		this.baseCheckStatus = baseCheckStatus;
	}

	public String getDeptName() {
		return deptName;
	}

	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}

	public Integer getFinalCheckStatus() {
		return finalCheckStatus;
	}

	public void setFinalCheckStatus(Integer finalCheckStatus) {
		this.finalCheckStatus = finalCheckStatus;
	}

	public Date getFinalCheckDate() {
		return finalCheckDate;
	}

	public void setFinalCheckDate(Date finalCheckDate) {
		this.finalCheckDate = finalCheckDate;
	}

	public String getFinalCheckUser() {
		return finalCheckUser;
	}

	public void setFinalCheckUser(String finalCheckUser) {
		this.finalCheckUser = finalCheckUser;
	}

	public String getFinalCheckSuggest() {
		return finalCheckSuggest;
	}

	public void setFinalCheckSuggest(String finalCheckSuggest) {
		this.finalCheckSuggest = finalCheckSuggest;
	}

	public Long getZyyDeptStdId() {
		return zyyDeptStdId;
	}

	public void setZyyDeptStdId(Long zyyDeptStdId) {
		this.zyyDeptStdId = zyyDeptStdId;
	}

	public Long getTeacherId() {
		return teacherId;
	}

	public void setTeacherId(Long teacherId) {
		this.teacherId = teacherId;
	}

	public Long getDirectorId() {
		return directorId;
	}

	public void setDirectorId(Long directorId) {
		this.directorId = directorId;
	}

	public Long getHospUserId() {
		return hospUserId;
	}

	public void setHospUserId(Long hospUserId) {
		this.hospUserId = hospUserId;
	}

	public Long getBaseUserId() {
		return baseUserId;
	}

	public void setBaseUserId(Long baseUserId) {
		this.baseUserId = baseUserId;
	}

	public String getWeeks() {
		return weeks;
	}

	public void setWeeks(String weeks) {
		this.weeks = weeks;
	}

	public String[] getDeptIds() {
		return deptIds;
	}

	public void setDeptIds(String[] deptIds) {
		this.deptIds = deptIds;
	}

	public String[] getCycleTableResiMonth() {
		return cycleTableResiMonth;
	}

	public void setCycleTableResiMonth(String[] cycleTableResiMonth) {
		this.cycleTableResiMonth = cycleTableResiMonth;
	}

	public List<ZyyCycleTableResiCycle> getList() {
		return list;
	}

	public void setList(List<ZyyCycleTableResiCycle> list) {
		this.list = list;
	}

	public String getStdDeptName() {
		return stdDeptName;
	}

	public void setStdDeptName(String stdDeptName) {
		this.stdDeptName = stdDeptName;
	}

	public void setStages(List<ZyyCycleTime> stages) {
		this.stages = stages;
	}

	public List<ZyyCycleTime> getStages() {
		return stages;
	}

	public String getErrorMsg() {
		return errorMsg;
	}

	public void setErrorMsg(String errorMsg) {
		this.errorMsg = errorMsg;
	}

	public int getErrorCode() {
		return errorCode;
	}

	public void setErrorCode(int errorCode) {
		this.errorCode = errorCode;
	}

	public String getTimeSection() {
		return timeSection;
	}

	public void setTimeSection(String timeSection) {
		this.timeSection = timeSection;
	}
	
}