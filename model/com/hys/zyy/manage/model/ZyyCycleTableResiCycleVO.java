package com.hys.zyy.manage.model;

import java.util.Collection;
import java.util.Date;
import java.util.List;

import org.codehaus.jackson.map.annotate.JsonSerialize;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：张伟清 2012-04-24
 * 
 * 描述：轮转表住院医师轮转
 * 
 * 说明:
 */
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class ZyyCycleTableResiCycleVO extends ZyyCycleTableResiCycle {

	private static final long serialVersionUID = -5507979533483823057L;

	/**
	 * 科室名称
	 */
	private String deptName ;
	
	/**
	 * 最高人数限制(最小)
	 */
	private Integer deptMaxNumber1 ;
	
	/**
	 * 最高人数限制(最大)
	 */
	private Integer deptMaxNumber2 ;
	
	/**
	 * 科室级别
	 */
	private Integer deptLevel ;

	/**
	 * 月份
	 */
	private Integer month ;
	
	/**
	 * 年度
	 */
	private String year ;
	
	/**
	 * 月初
	 */
	private String beginMonth ;
	
	/**
	 * 月末
	 */
	private String endMonth ;
	
	/**
	 * 周初(周一)
	 */
	private String beginWeek;
	
	/**
	 * 周末(周日)
	 */
	private String endWeek;
	
	/**
	 * 本年度第几周 
	 */
	private String yearWeek;
	
	/**
	 * 真实姓名 
	 */
	private String realName;
	
	private String timeSection;
	
	/**
	 * 最高学历
	 */
	private Integer highestRecordSchool ;
	
	/**
	 * 当前人轮转的最后时间 
	 */
	private Date lastCycleDate; 
	
	/**
	 * 有无医师执业证书
	 */
	private Integer physiciansPracticingCert ;
	/**
	 * 有无医师资格证书
	 */
	private Integer hasCertificate;
	
	/**
	 * 1 -科研型研究生 2 -临床型研究生
	 */
	private Integer graduateType ;
	/**
	 * 创建轮转表的用户id 
	 */
	private Long createUser;
	
	/**
	 * 轮转表住院医师轮转科室审核记录
	 */
	private Collection<ZyyCycleResiCycleCheck> cycleCheckList ; 
	
	/**
	 *	基地（学科）基地ID
	 */
	private Long baseId;
	
	/**
	 * 基地（学科）名称
	 */
	private String baseName ;
	
	/**
	 * 基地（学科）别名名称
	 */
	private String baseAliasName ;
	
	/**
	 * 学届
	 */
	private String userYear;
	
	/**
	 * 是否联排科室
	 */
	private Integer isContDept ;
	
	/**
	 * 科室占比
	 */
	private Integer deptRatio;
	
	/**
	 *	学制  
	 */
	private Integer schoolSystem;
	
	/**
	 * 最后更新状态时间
	 */
	private Date lastUpdateStatusDate ; 
	
	/**
	 * 上、下半月 0.上半月 1.下半月 
	 */
	private Integer halfMonth ; 
	private Integer halfMonthNew; 
	
	/**
	 * 是否存在半月
	 */
	private boolean halfMonthFlag ;
	
	/**
	 * 本月周数量
	 */
	private Integer monthWeeks ;
	
	/**
	 * xusq添加，用于查询医师的科室信息 2012-10-15
	 */
	private String userDeptName;
	
	private List<ZyyCycleTime> stages;
	
	/**
	 * 轮转调整时，如果是一个月只安排了半月轮转的情况下，使用到，会记录另外半月的起止时间
	 */
	private Date otherHalfStateDate;

	/**
	 * 轮转调整时，如果是一个月只安排了半月轮转的情况下，使用到，会记录另外半月的起止时间
	 */
	private Date otherHalfEndDate;

	/**
	 * 轮转指定带教学员
	 */
	private List<ZyyCycleTeacherVO> cycleTeacherVOList;
	
	private Integer residencySource;
	
	private Integer degreeConvergence;
	
	
	private String teacherName ;//带教老师名称
	/*
	 * 轮转结束时间
	 */
	private String endDateStr;
	
	//工号
	private String jobNumber;
	
	private String yearAndMonthStr;
	
	public ZyyCycleTableResiCycleVO() {
		super();
	}

	public ZyyCycleTableResiCycleVO(Long deptId, String yearAndMonthStr) {
		super(deptId);
		this.yearAndMonthStr = yearAndMonthStr;
	}

	public Date getOtherHalfStateDate() {
		return otherHalfStateDate;
	}

	public void setOtherHalfStateDate(Date otherHalfStateDate) {
		this.otherHalfStateDate = otherHalfStateDate;
	}

	public Date getOtherHalfEndDate() {
		return otherHalfEndDate;
	}

	public void setOtherHalfEndDate(Date otherHalfEndDate) {
		this.otherHalfEndDate = otherHalfEndDate;
	}
	
	public Integer getResidencySource() {
		return residencySource;
	}

	public void setResidencySource(Integer residencySource) {
		this.residencySource = residencySource;
	}

	public Integer getDegreeConvergence() {
		return degreeConvergence;
	}

	public void setDegreeConvergence(Integer degreeConvergence) {
		this.degreeConvergence = degreeConvergence;
	}

	private int groupCount;
	
	public Integer getSchoolSystem() {
		return schoolSystem;
	}

	public void setSchoolSystem(Integer schoolSystem) {
		this.schoolSystem = schoolSystem;
	}

	public String getUserYear() {
		return userYear;
	}

	public void setUserYear(String userYear) {
		this.userYear = userYear;
	}

	public Integer getGraduateType() {
		return graduateType;
	}

	public void setGraduateType(Integer graduateType) {
		this.graduateType = graduateType;
	}

	public Long getCreateUser() {
		return createUser;
	}

	public void setCreateUser(Long createUser) {
		this.createUser = createUser;
	}

	public Integer getHighestRecordSchool() {
		return highestRecordSchool;
	}

	public void setHighestRecordSchool(Integer highestRecordSchool) {
		this.highestRecordSchool = highestRecordSchool;
	}

	public Integer getPhysiciansPracticingCert() {
		return physiciansPracticingCert;
	}

	public void setPhysiciansPracticingCert(Integer physiciansPracticingCert) {
		this.physiciansPracticingCert = physiciansPracticingCert;
	}

	public String getRealName() {
		return realName;
	}

	public void setRealName(String realName) {
		this.realName = realName;
	}

	public String getDeptName() {
		return deptName;
	}

	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}

	public Integer getDeptMaxNumber1() {
		return deptMaxNumber1;
	}

	public void setDeptMaxNumber1(Integer deptMaxNumber1) {
		this.deptMaxNumber1 = deptMaxNumber1;
	}

	public Integer getDeptMaxNumber2() {
		return deptMaxNumber2;
	}

	public void setDeptMaxNumber2(Integer deptMaxNumber2) {
		this.deptMaxNumber2 = deptMaxNumber2;
	}

	public Integer getMonth() {
		return month;
	}

	public void setMonth(Integer month) {
		this.month = month;
	}

	public String getYear() {
		return year;
	}

	public void setYear(String year) {
		this.year = year;
	}

	public String getBeginMonth() {
		return beginMonth;
	}

	public void setBeginMonth(String beginMonth) {
		this.beginMonth = beginMonth;
	}

	public String getEndMonth() {
		return endMonth;
	}

	public void setEndMonth(String endMonth) {
		this.endMonth = endMonth;
	}
	public Date getLastCycleDate() {
		return lastCycleDate;
	}

	public void setLastCycleDate(Date lastCycleDate) {
		this.lastCycleDate = lastCycleDate;
	}

	public Integer getDeptLevel() {
		return deptLevel;
	}

	public void setDeptLevel(Integer deptLevel) {
		this.deptLevel = deptLevel;
	}

	public Collection<ZyyCycleResiCycleCheck> getCycleCheckList() {
		return cycleCheckList;
	}

	public void setCycleCheckList(Collection<ZyyCycleResiCycleCheck> cycleCheckList) {
		this.cycleCheckList = cycleCheckList;
	}
	
	public Long getBaseId() {
		return baseId;
	}

	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}

	public String getBaseName() {
		return baseName;
	}

	public void setBaseName(String baseName) {
		this.baseName = baseName;
	}

	public String getBaseAliasName() {
		return baseAliasName;
	}

	public void setBaseAliasName(String baseAliasName) {
		this.baseAliasName = baseAliasName;
	}

	public Integer getIsContDept() {
		return isContDept;
	}

	public void setIsContDept(Integer isContDept) {
		this.isContDept = isContDept;
	}

	public Integer getDeptRatio() {
		return deptRatio;
	}

	public void setDeptRatio(Integer deptRatio) {
		this.deptRatio = deptRatio;
	}

	public Date getLastUpdateStatusDate() {
		return lastUpdateStatusDate;
	}

	public void setLastUpdateStatusDate(Date lastUpdateStatusDate) {
		this.lastUpdateStatusDate = lastUpdateStatusDate;
	}

	public Integer getHalfMonth() {
		return halfMonth;
	}

	public void setHalfMonth(Integer halfMonth) {
		this.halfMonth = halfMonth;
	}

	public Integer getHalfMonthNew() {
		return halfMonthNew;
	}

	public void setHalfMonthNew(Integer halfMonthNew) {
		this.halfMonthNew = halfMonthNew;
	}

	public String getUserDeptName() {
		return userDeptName;
	}

	public void setUserDeptName(String userDeptName) {
		this.userDeptName = userDeptName;
	}

	public Integer getMonthWeeks() {
		return monthWeeks;
	}

	public void setMonthWeeks(Integer monthWeeks) {
		this.monthWeeks = monthWeeks;
	}

	public String getBeginWeek() {
		return beginWeek;
	}

	public void setBeginWeek(String beginWeek) {
		this.beginWeek = beginWeek;
	}

	public String getEndWeek() {
		return endWeek;
	}

	public void setEndWeek(String endWeek) {
		this.endWeek = endWeek;
	}

	public String getYearWeek() {
		return yearWeek;
	}

	public void setYearWeek(String yearWeek) {
		this.yearWeek = yearWeek;
	}

	public boolean isHalfMonthFlag() {
		return halfMonthFlag;
	}

	public void setHalfMonthFlag(boolean halfMonthFlag) {
		this.halfMonthFlag = halfMonthFlag;
	}

	public List<ZyyCycleTime> getStages() {
		return stages;
	}

	public void setStages(List<ZyyCycleTime> stages) {
		this.stages = stages;
	}

	public List<ZyyCycleTeacherVO> getCycleTeacherVOList() {
		return cycleTeacherVOList;
	}

	public void setCycleTeacherVOList(List<ZyyCycleTeacherVO> cycleTeacherVOList) {
		this.cycleTeacherVOList = cycleTeacherVOList;
	}

	public String getTimeSection() {
		return timeSection;
	}

	public void setTimeSection(String timeSection) {
		this.timeSection = timeSection;
	}

	public int getGroupCount() {
		return groupCount;
	}

	public void setGroupCount(int groupCount) {
		this.groupCount = groupCount;
	}

	public String getTeacherName() {
		return teacherName;
	}

	public void setTeacherName(String teacherName) {
		this.teacherName = teacherName;
	}

	public String getEndDateStr() {
		return endDateStr;
	}

	public void setEndDateStr(String endDateStr) {
		this.endDateStr = endDateStr == null ? null : endDateStr.trim();
	}

	public String getJobNumber() {
		return jobNumber;
	}

	public void setJobNumber(String jobNumber) {
		this.jobNumber = jobNumber;
	}

	public Integer getHasCertificate() {
		return hasCertificate;
	}

	public void setHasCertificate(Integer hasCertificate) {
		this.hasCertificate = hasCertificate;
	}

	public String getYearAndMonthStr() {
		return yearAndMonthStr;
	}

	public void setYearAndMonthStr(String yearAndMonthStr) {
		this.yearAndMonthStr = yearAndMonthStr == null ? null : yearAndMonthStr.trim();
	}
	
}
