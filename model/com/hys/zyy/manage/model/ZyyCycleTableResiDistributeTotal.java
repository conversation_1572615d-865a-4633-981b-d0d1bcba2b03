package com.hys.zyy.manage.model;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.Map.Entry;

public class ZyyCycleTableResiDistributeTotal {

	public ZyyCycleTableResiDistributeTotal() {
		init();
	}

	public ZyyCycleTableResiDistributeTotal(Long deptId, String deptName) {
		this.deptId = deptId;
		this.deptName = deptName;
		init();
	}

	/**
	 * 查看是否填满
	 */
	public void setIsFull(ZyyCycleTableResiDistributeTotalVO vo) {
		for (DateType dateType : dateTypes) {
			if (limitMaxNum != null && limitMaxNum != 0) {
				if (vo.getCycleType() == 2) {
					if (dateType.getFirstHalf() >= limitMaxNum) {
						dateType.isFull = true;
					}
				} else {
					if (dateType.getFirstHalf() >= limitMaxNum && dateType.getSecondHalf() >= limitMaxNum) {
						dateType.isFull = true;
					}
				}
			}
		}
	}

	/**
	 * 添加统计人数，分为了上半月和下半月，如果周类型统计则填充到firstHalf中
	 * 
	 * @param index
	 * @param isFirstHarf
	 * @param num
	 */
	public void add(int index, Boolean isFirstHarf, int num) {
		DateType dateType = dateTypes.get(index);
		if (isFirstHarf) {
			dateType.setFirstHalf(dateType.getFirstHalf() + num);
		} else {
			dateType.setSecondHalf(dateType.getSecondHalf() + num);
		}
		dateTypes.set(index, dateType);
	}

	private void init() {
		dateTypes = new ArrayList<DateType>(12);
		for (int i = 0; i < 12; i++) {
			dateTypes.add(new DateType());
		}
	}

	private Long deptId;
	private String deptName;
	private Date startDate;
	private Date endDate;
	private Integer count;
	private Long residencyId;

	/**
	 * 限制最小人数
	 */
	private Long limitMinNum;
	/**
	 * 限制最大人数
	 */
	private Long limitMaxNum;

	private List<DateType> dateTypes;// 存储时间

	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public Integer getCount() {
		return count;
	}

	public void setCount(Integer count) {
		this.count = count;
	}

	public Long getDeptId() {
		return deptId;
	}

	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}

	public String getDeptName() {
		return deptName;
	}

	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}

	public Long getLimitMinNum() {
		return limitMinNum;
	}

	public void setLimitMinNum(Long limitMinNum) {
		this.limitMinNum = limitMinNum;
	}

	public Long getLimitMaxNum() {
		return limitMaxNum;
	}

	public void setLimitMaxNum(Long limitMaxNum) {
		this.limitMaxNum = limitMaxNum;
	}

	public List<DateType> getDateTypes() {
		return dateTypes;
	}

	public void setDateTypes(List<DateType> dateTypes) {
		this.dateTypes = dateTypes;
	}

	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	public Long getResidencyId() {
		return residencyId;
	}

	public void setResidencyId(Long residencyId) {
		this.residencyId = residencyId;
	}
}
