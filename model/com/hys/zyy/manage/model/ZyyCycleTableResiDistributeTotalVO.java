package com.hys.zyy.manage.model;

import java.util.List;

import com.hys.zyy.manage.page.PageQuery;

public class ZyyCycleTableResiDistributeTotalVO  extends PageQuery {

	public ZyyCycleTableResiDistributeTotalVO(){
		this.firstHalf=0;
		this.secondHalf=0;
	}
	
	private Long deptId;//科室ID
	
	private String startDate;//年或周；年：2016.10,周：
	
	private String endDate;//周统计使用：结束日期
	
	private Integer cycleType;//类型
	
	private Long stdId;//专业ID

	private Integer dateIndex;//时间的索引：0或null表示当前时间，<0表示之前的时间，>0表示之后的时间
	
	private Long zyyUserProvinceId;
	
	private Long zyyUserOrgId;//医院ID
	
	private String realName;//名称
	
	private Integer firstHalf;//上半月
	
	private Integer secondHalf;//下半月
	
	private Integer limitMaxNum;//科室最大人数
	
	private Integer queryType;//查询类型
	
	private Integer queryPermission ;//可以查询的权限：1，上月。2，下月。3，整月
	
	private List<Long> residencyIds;//人员ID
	
	private Integer type;//类型：0：全部科室；1：单个科室
	
	private Boolean isDisSaveBtn;//是否禁用弹出框保存按钮
	
	private Boolean isCycled;//已经排版
	
	public Integer getCycleType() {
		return cycleType;
	}

	public void setCycleType(Integer cycleType) {
		this.cycleType = cycleType;
	}

	public Long getStdId() {
		return stdId;
	}

	public void setStdId(Long stdId) {
		this.stdId = stdId;
	}

	public Long getZyyUserProvinceId() {
		return zyyUserProvinceId;
	}

	public void setZyyUserProvinceId(Long zyyUserProvinceId) {
		this.zyyUserProvinceId = zyyUserProvinceId;
	}

	public String getEndDate() {
		return endDate;
	}

	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}

	public String getStartDate() {
		return startDate;
	}

	public void setStartDate(String startDate) {
		this.startDate = startDate;
	}

	public Integer getDateIndex() {
		return dateIndex;
	}

	public void setDateIndex(Integer dateIndex) {
		this.dateIndex = dateIndex;
	}

	public Long getDeptId() {
		return deptId;
	}

	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}

	public Long getZyyUserOrgId() {
		return zyyUserOrgId;
	}

	public void setZyyUserOrgId(Long zyyUserOrgId) {
		this.zyyUserOrgId = zyyUserOrgId;
	}

	public String getRealName() {
		return realName;
	}

	public void setRealName(String realName) {
		this.realName = realName;
	}

	public Integer getFirstHalf() {
		return firstHalf;
	}

	public void setFirstHalf(Integer firstHalf) {
		this.firstHalf = firstHalf;
	}

	public Integer getSecondHalf() {
		return secondHalf;
	}

	public void setSecondHalf(Integer secondHalf) {
		this.secondHalf = secondHalf;
	}

	public Integer getLimitMaxNum() {
		return limitMaxNum;
	}

	public void setLimitMaxNum(Integer limitMaxNum) {
		this.limitMaxNum = limitMaxNum;
	}

	public Integer getQueryType() {
		return queryType;
	}

	public void setQueryType(Integer queryType) {
		this.queryType = queryType;
	}

	public Integer getQueryPermission() {
		return queryPermission;
	}

	public void setQueryPermission(Integer queryPermission) {
		this.queryPermission = queryPermission;
	}

	public List<Long> getResidencyIds() {
		return residencyIds;
	}

	public void setResidencyIds(List<Long> residencyIds) {
		this.residencyIds = residencyIds;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public Boolean getIsDisSaveBtn() {
		return isDisSaveBtn;
	}

	public void setIsDisSaveBtn(Boolean isDisSaveBtn) {
		this.isDisSaveBtn = isDisSaveBtn;
	}

	public Boolean getIsCycled() {
		return isCycled;
	}

	public void setIsCycled(Boolean isCycled) {
		this.isCycled = isCycled;
	}

	
}
