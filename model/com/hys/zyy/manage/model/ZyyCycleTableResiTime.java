package com.hys.zyy.manage.model;

import java.util.List;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：张伟清 2012-04-24
 * 
 * 描述：轮转表住院医师轮转时间
 * 
 * 说明:
 */
public class ZyyCycleTableResiTime extends ZyyBaseObject {

	private static final long serialVersionUID = 6662831124067255920L;

	/**
	 * 主键ID
	 */
	private Long id;

	/**
	 * 轮转表ID
	 */
	private Long cycleTableId;

	/**
	 * 住院医师ID
	 */
	private Long residencyId;

	/**
	 * 基地ID
	 */
	private Long baseId;

	/**
	 * 科室ID
	 */
	private Long deptId;

	/**
	 * 轮转类别 1.日 2.周 3.月 0.手动选择开始结束时间
	 */
	private Integer cycleType;

	/**
	 * 轮转时间
	 */
	private String cycleTime;
	
	/**
	 * 新轮转时间-小数转换
	 */
	private Integer newCycleTime ;
	
	/**
	 * 半月标记
	 */
	private boolean halfMoonFlag ;

	/**
	 * 状态
	 */
	private Integer status;

	/**
	 * 学员真实姓名
	 */
	private String realName;

	/**
	 * 科室当前轮转容纳人数
	 */
	private Integer deptNumber;
	
	/**
	 * 科室占比
	 */
	private Double deptRatio;

	/**
	 * 科室最小容纳人数
	 */
	private Integer deptMinNumber;

	/**
	 * 科室最高容纳人数
	 */
	private Integer deptMaxNumber;

	/**
	 * 科室优先级
	 */
	private Integer deptPriorLevel;

	/**
	 * 最高学历 1 -大学专科 2 -大学本科 3 -硕士研究生 4 -博士研究生
	 */
	private Integer highestRecordSchool;

	/**
	 * 有无医师执业证书 1.有 0.无
	 */
	private Integer physiciansPracticingCert;
	
	/**
	 * 学制(住院医师需要轮转时间)	1.一年制 2.二年制 3.三年制
	 */
	private Integer schoolSystem ;
	
	/**
	 * 1 -科研型研究生 2 -临床型研究生
	 */
	private Integer graduateType ;
	
	/**
	 * 是否联排科室
	 */
	private Integer isContDept ;
	
	/**
	 * 联排科室信息
	 */
	private List<ZyyCycleTableResiTime> childList ;
	
	/**
	 * 优先轮转科室表
	 */
	private Long cyclePriorityId ;
	
	/**
	 * 轮转科室时间表 主键ID
	 */
	private Long cycleTimeId ;
	
	/**
	 * 轮转时间详细
	 */
	private ZyyDeptCycleTimeDetail timeDetail ;  

	public Integer getGraduateType() {
		return graduateType;
	}

	public void setGraduateType(Integer graduateType) {
		this.graduateType = graduateType;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getCycleTableId() {
		return cycleTableId;
	}

	public void setCycleTableId(Long cycleTableId) {
		this.cycleTableId = cycleTableId;
	}

	public Long getResidencyId() {
		return residencyId;
	}

	public void setResidencyId(Long residencyId) {
		this.residencyId = residencyId;
	}

	public Long getBaseId() {
		return baseId;
	}

	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}

	public Long getDeptId() {
		return deptId;
	}

	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}

	public Integer getCycleType() {
		return cycleType;
	}

	public void setCycleType(Integer cycleType) {
		this.cycleType = cycleType;
	}

	public String getCycleTime() {
		return cycleTime;
	}

	public void setCycleTime(String cycleTime) {
		this.cycleTime = cycleTime;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public String getRealName() {
		return realName;
	}

	public void setRealName(String realName) {
		this.realName = realName;
	}

	public Integer getDeptNumber() {
		return deptNumber;
	}

	public void setDeptNumber(Integer deptNumber) {
		this.deptNumber = deptNumber;
	}

	public Integer getDeptMinNumber() {
		return deptMinNumber;
	}

	public void setDeptMinNumber(Integer deptMinNumber) {
		this.deptMinNumber = deptMinNumber;
	}

	public Integer getDeptMaxNumber() {
		return deptMaxNumber;
	}

	public void setDeptMaxNumber(Integer deptMaxNumber) {
		this.deptMaxNumber = deptMaxNumber;
	}

	public Integer getDeptPriorLevel() {
		return deptPriorLevel;
	}

	public void setDeptPriorLevel(Integer deptPriorLevel) {
		this.deptPriorLevel = deptPriorLevel;
	}

	public Integer getHighestRecordSchool() {
		return highestRecordSchool;
	}

	public void setHighestRecordSchool(Integer highestRecordSchool) {
		this.highestRecordSchool = highestRecordSchool;
	}

	public Integer getPhysiciansPracticingCert() {
		return physiciansPracticingCert;
	}

	public void setPhysiciansPracticingCert(Integer physiciansPracticingCert) {
		this.physiciansPracticingCert = physiciansPracticingCert;
	}

	public List<ZyyCycleTableResiTime> getChildList() {
		return childList;
	}

	public void setChildList(List<ZyyCycleTableResiTime> childList) {
		this.childList = childList;
	}

	public Double getDeptRatio() {
		return deptRatio;
	}

	public void setDeptRatio(Double deptRatio) {
		this.deptRatio = deptRatio;
	}

	public Integer getSchoolSystem() {
		return schoolSystem;
	}

	public void setSchoolSystem(Integer schoolSystem) {
		this.schoolSystem = schoolSystem;
	}

	public Integer getIsContDept() {
		return isContDept;
	}

	public void setIsContDept(Integer isContDept) {
		this.isContDept = isContDept;
	}

	public ZyyDeptCycleTimeDetail getTimeDetail() {
		return timeDetail;
	}

	public void setTimeDetail(ZyyDeptCycleTimeDetail timeDetail) {
		this.timeDetail = timeDetail;
	}

	public Long getCycleTimeId() {
		return cycleTimeId;
	}

	public void setCycleTimeId(Long cycleTimeId) {
		this.cycleTimeId = cycleTimeId;
	}

	public Long getCyclePriorityId() {
		return cyclePriorityId;
	}

	public void setCyclePriorityId(Long cyclePriorityId) {
		this.cyclePriorityId = cyclePriorityId;
	}

	public Integer getNewCycleTime() {
		return newCycleTime;
	}

	public void setNewCycleTime(Integer newCycleTime) {
		this.newCycleTime = newCycleTime;
	}

	public boolean isHalfMoonFlag() {
		return halfMoonFlag;
	}

	public void setHalfMoonFlag(boolean halfMoonFlag) {
		this.halfMoonFlag = halfMoonFlag;
	}
}