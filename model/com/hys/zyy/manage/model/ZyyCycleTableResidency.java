package com.hys.zyy.manage.model;

import java.util.List;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：张伟清 2012-04-24
 * 
 * 描述：轮转表住院医师
 * 
 * 说明:
 */
public class ZyyCycleTableResidency extends ZyyBaseObject {

	private static final long serialVersionUID = -1743766344945515089L;
	
	/**
	 * 主键ID
	 */
	private Long id ;
	
	/**
	 * 轮转表ID
	 */
	private Long cycleTableId ;
	
	/**
	 * 基地ID
	 */
	private Long baseId ;
	
	/**
	 * 住院医师ID
	 */
	private Long residencyId ;
	
	/**
	 * 年度ID
	 */
	private Long recruitYearId ;
	
	/**
	 * 状态
	 */
	private Integer status ;
	/**
	 * 年制
	 */
	private Integer educationSystem ;
	/**
	 * 轮转时间ID
	 */
	private Long cycleTimeId ;
	/**
	 * 连排科室ID
	 */
	private String baseContinuityId ;
	/**
	 * 优先排科室ID
	 */
	private Long cyclePriorityId ;
	/**
	 * 学员列表
	 */
	private List<ZyyUserExtendVO> userList;
	
	
	public List<ZyyUserExtendVO> getUserList() {
		return userList;
	}

	public void setUserList(List<ZyyUserExtendVO> userList) {
		this.userList = userList;
	}

	public Integer getEducationSystem() {
		return educationSystem;
	}

	public void setEducationSystem(Integer educationSystem) {
		this.educationSystem = educationSystem;
	}

	public Long getCycleTimeId() {
		return cycleTimeId;
	}

	public void setCycleTimeId(Long cycleTimeId) {
		this.cycleTimeId = cycleTimeId;
	}

	public String getBaseContinuityId() {
		return baseContinuityId;
	}

	public void setBaseContinuityId(String baseContinuityId) {
		this.baseContinuityId = baseContinuityId;
	}

	public Long getCyclePriorityId() {
		return cyclePriorityId;
	}

	public void setCyclePriorityId(Long cyclePriorityId) {
		this.cyclePriorityId = cyclePriorityId;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getCycleTableId() {
		return cycleTableId;
	}

	public void setCycleTableId(Long cycleTableId) {
		this.cycleTableId = cycleTableId;
	}

	public Long getBaseId() {
		return baseId;
	}

	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}

	public Long getResidencyId() {
		return residencyId;
	}

	public void setResidencyId(Long residencyId) {
		this.residencyId = residencyId;
	}

	public Long getRecruitYearId() {
		return recruitYearId;
	}

	public void setRecruitYearId(Long recruitYearId) {
		this.recruitYearId = recruitYearId;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}
}
