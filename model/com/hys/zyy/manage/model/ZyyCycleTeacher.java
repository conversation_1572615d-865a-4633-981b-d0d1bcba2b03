package com.hys.zyy.manage.model;

import java.util.Date;

/**
 * 制定带教老师实体类     
 * <AUTHOR>      
 * @version 1.0    
 * @created 2013-5-29 下午02:03:45
 */
public class ZyyCycleTeacher extends ZyyBaseObject {
	/**  描述  */    
	private static final long serialVersionUID = -811361206203605173L;

	/**
	 * 主键ID
	 */
	private Long id ;
	
	/**
	 * 教师ID
	 */
	private Long teacherId ;
	
	/**
	 * 带教开始时间
	 */
	private Date startDate ;
	
	/**
	 * 带教结束时间
	 */
	private Date endDate;
	
	/**
	 * 是否手册终审者 0:否 1:是
	 */
	private Integer isFinalAuditing;
	
	/**
	 * 创建时间
	 */
	private Date createDate;
	
	/**
	 * 学员ID
	 */
	private Long residencyId ;

	/**
	 * 轮转科室ID
	 */
	private Long deptId ;
	
	/**
	 * 带教开始时间
	 */
	private Date cycleStartDate ;
	
	/**
	 * 带教结束时间
	 */
	private Date cycleEndDate;
	
	/**
	 * 科室名称
	 */
	private String deptName;
	
	/**
	 * 最后修改时间
	 */
	private Date lastUpdateDate;
	
	private String teacherMonth;
	
	public ZyyCycleTeacher() {
	}
	
	public Date getLastUpdateDate() {
		return lastUpdateDate;
	}

	public void setLastUpdateDate(Date lastUpdateDate) {
		this.lastUpdateDate = lastUpdateDate;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getTeacherId() {
		return teacherId;
	}

	public void setTeacherId(Long teacherId) {
		this.teacherId = teacherId;
	}

	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	public Integer getIsFinalAuditing() {
		return isFinalAuditing;
	}

	public void setIsFinalAuditing(Integer isFinalAuditing) {
		this.isFinalAuditing = isFinalAuditing;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}
	
	public Long getResidencyId() {
		return residencyId;
	}

	public void setResidencyId(Long residencyId) {
		this.residencyId = residencyId;
	}
	public Long getDeptId() {
		return deptId;
	}

	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}

	public Date getCycleStartDate() {
		return cycleStartDate;
	}

	public void setCycleStartDate(Date cycleStartDate) {
		this.cycleStartDate = cycleStartDate;
	}

	public Date getCycleEndDate() {
		return cycleEndDate;
	}

	public void setCycleEndDate(Date cycleEndDate) {
		this.cycleEndDate = cycleEndDate;
	}

	public String getDeptName() {
		return deptName;
	}

	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}

	public String getTeacherMonth() {
		return teacherMonth;
	}

	public void setTeacherMonth(String teacherMonth) {
		this.teacherMonth = teacherMonth;
	}

	
	
}
