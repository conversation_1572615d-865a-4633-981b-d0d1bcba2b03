package com.hys.zyy.manage.model;

import java.util.Date;

import com.hys.zyy.manage.util.DateUtil;

public class ZyyCycleTeacherPerformance {
    private Long id;

    private Long cycleId;

    private Long cycleResidencyId;

    private Date cycleStartDate;

    private Date cycleEndDate;

    private Long cycleDeptId;

    private Date createTime;

    private Long createUser;

    private Integer performanceStatus;

    /**
     * 最后更新人
     */
    private Long lastModifyUser ;
    
    
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCycleId() {
        return cycleId;
    }

    public void setCycleId(Long cycleId) {
        this.cycleId = cycleId;
    }

    public Long getCycleResidencyId() {
        return cycleResidencyId;
    }

    public void setCycleResidencyId(Long cycleResidencyId) {
        this.cycleResidencyId = cycleResidencyId;
    }

    public Date getCycleStartDate() {
        return cycleStartDate;
    }

    public void setCycleStartDate(Date cycleStartDate) {
    	if(cycleStartDate!=null){
    		cycleStartDate = DateUtil.parse(DateUtil.format(cycleStartDate, DateUtil.FORMAT_SHORT),DateUtil.FORMAT_SHORT);
    	}
        this.cycleStartDate = cycleStartDate;
    }

    public Date getCycleEndDate() {
        return cycleEndDate;
    }

    public void setCycleEndDate(Date cycleEndDate) {
    	if(cycleEndDate!=null){
    		cycleEndDate = DateUtil.parse(DateUtil.format(cycleEndDate, DateUtil.FORMAT_SHORT),DateUtil.FORMAT_SHORT);
    	}
        this.cycleEndDate = cycleEndDate;
    }

    public Long getCycleDeptId() {
        return cycleDeptId;
    }

    public void setCycleDeptId(Long cycleDeptId) {
        this.cycleDeptId = cycleDeptId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getCreateUser() {
        return createUser;
    }

    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    public Integer getPerformanceStatus() {
        return performanceStatus;
    }

    public void setPerformanceStatus(Integer performanceStatus) {
        this.performanceStatus = performanceStatus;
    }

	public Long getLastModifyUser() {
		return lastModifyUser;
	}

	public void setLastModifyUser(Long lastModifyUser) {
		this.lastModifyUser = lastModifyUser;
	}
    
    
}