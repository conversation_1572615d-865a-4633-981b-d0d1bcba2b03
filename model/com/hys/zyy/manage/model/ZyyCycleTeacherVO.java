package com.hys.zyy.manage.model;

import java.util.Date;
import java.util.List;

/**
 * 指定带教老师相关表单实体对象
 * <AUTHOR>
 * @version 1.0
 * @created 2013-5-29 下午02:03:45
 */
public class ZyyCycleTeacherVO extends ZyyCycleTeacher {
	private static final long serialVersionUID = 3538390029293643713L;

	private Long stuId;

	/**
	 * 轮转ID
	 */
	private Long ctId;

	/**
	 * 学员姓名
	 */
	private String realName;

	/**
	 * 基地ID
	 */
	private Long baseId;

	/**
	 * 基地名称
	 */
	private String baseName;

	/**
	 * 科室ID
	 */
	private Long cycleDeptId;

	/**
	 * 科室名称
	 */
	private String deptName;

	/**
	 * 年制
	 */
	private int schoolSystem;

	/**
	 * 年级
	 */
	private int userYear;

	/**
	 * 轮转开始时间
	 */
	private Date deptCycleStartDate;

	/**
	 * 轮转结束时间
	 */
	private Date deptCycleEndDate;


	private List<ZyyCycleTeacherVO> cycleTeacherList;
	private Date oldStartDate; //原本带教开始时间
	private Date oldEndDate;//原本带教结束时间

	private int cycleStatus;	//1,正在轮转 2,尚未轮转 3,结束轮转 4,全部状态
	private int status;//出入科状态  1已入科，非1  未入科

	private String teacherName;

	/**
	 * 学员类型  1:科研型  2:临床型
	 * @return
	 */
	private Integer graduateType;

	/**
	 * 有无执照
	 */
	private Integer hasCertificate;

	/**
	 * 学员状态
	 */
	private Integer zyyUserStatus;

	/**
	 * 上级科室ID
	 */
	private Long parentDeptId;

	private int deptLevel;

	private Long userId;

	//------------------------
	/**
	 * 带教工号
	 */
	private String jobNumber;
	/**
	 * 用户评价表的ID
	 */
	private Long userEvaluateId;
	/**
	 * 是否可评价  1-可评价  2-未在评价时间段内,不可评价
	 */
	private Integer isEvaluate;
	/**
	 *
	 */
	private String timeSection;
	/**
	 * 年份
	 */
	private String year;
	/**
	 * 学科名称
	 */
	private String baseAliasName;
	/**
	 * 学员姓名
	 */
	private String resiName;
	/**
	 * 学员身份类型  1单位人  2社会人  3 学位衔接
	 */
	private Integer residencySource;
	/**
	 * 身份证号
	 */
	private String certificateNo;
	/**
	 * 合并带教的时间段
	 */
	private String teachingTimeSection;
	/**
	 * 新审核表的Id
	 */
	private Long checkId;
	//------------------------
	//登记手册修改中指定带教用
	private String cycleTeacherIds;//zyy_cycle_teacher表的id集合
	//zyy_cycle_teacher表中的id,此记录为这个时间段的手册审核者
	private String finalAuditingCid;
	//------------------------

	/*yjk 登记手册修改中用的两个字段 */
	private Integer commitStatus; //轮转部分 提交状态
	private Integer teacherCycleStatus; //带教审核状态
	private Long teacherHdChkId; //zyy_cycle_resi_hd_check表的id
	private Long hdChKId;//zyy_residency_handbook_dept表的ID


	private String fieldValue;//填写手册的时间


	//轮转后多少天 到 多少天
	private Integer fillinSdate;

	private Integer fillinEdate;

	/**
	 * 起止日期字符串
	 */
	private String startAndEndTimeStr;
	/**
	 * @desc
	 */
	private Long jdrId;//如可记录ID

	private String accountName;//账号

	private String teacherIds;//组合带教ID

	//拆分带教时间
	private List<ZyyTeacherMonth> teacherMonthList;
	//带教的合并行数
	private Integer rowspan;

	private Integer performanceStatus ;//绩效状态 ： 0未完成1已完成
	private Boolean isTimeOut;//是否逾期（手机端为入科是否逾期）

	private Boolean teacherIsTimeOut;//带教是否逾期

	private Integer verifyStatus;//审核状态0、未审核默认 1、审核通过2、审核不通过
	Long applyId;
	/*
	 * 带教账号
	 */
	private String teacherAccountName;
	/*
	 * 带教证件号码
	 */
	private String teacherCertificateNo;
	/*
	 * 人员类型
	 */
	private String residencySourceStr;
	/*
	 * 轮转时间
	 */
	private String cycleTimes;
	/*
	 * 带教时间
	 */
	private String teacherTimes;
	/*
	 * 指定带教创建时间
	 */
	private String createDateStr;
	/*
	 * 绩效月份带教时长（单位：天）
	 */
	private String teacherDays;
	private Integer userCategory;
	private String userCategoryStr;
	/**
	 * 允许带人人数
	 */
	private Integer allowTeachingNum;
	/**
	 * 当前带人人数
	 */
	private Integer teachingNum;

	public String getTeacherIds() {
		return teacherIds;
	}

	public void setTeacherIds(String teacherIds) {
		this.teacherIds = teacherIds;
	}

	public String getAccountName() {
		return accountName;
	}

	public void setAccountName(String accountName) {
		this.accountName = accountName;
	}

	private Boolean isQueryDateInCycleDate;//判断查询时间是否在轮转时间范围内

	public Long getHdChKId() {
		return hdChKId;
	}

	public void setHdChKId(Long hdChKId) {
		this.hdChKId = hdChKId;
	}

	public Integer getTeacherCycleStatus() {
		return teacherCycleStatus;
	}

	public void setTeacherCycleStatus(Integer teacherCycleStatus) {
		this.teacherCycleStatus = teacherCycleStatus;
	}

	public Long getTeacherHdChkId() {
		return teacherHdChkId;
	}

	public void setTeacherHdChkId(Long teacherHdChkId) {
		this.teacherHdChkId = teacherHdChkId;
	}

	public Long getCheckId() {
		return checkId;
	}

	public void setCheckId(Long checkId) {
		this.checkId = checkId;
	}
	public Integer getCommitStatus() {
		return commitStatus;
	}

	public void setCommitStatus(Integer commitStatus) {
		this.commitStatus = commitStatus;
	}


	public String getFinalAuditingCid() {
		return finalAuditingCid;
	}

	public void setFinalAuditingCid(String finalAuditingCid) {
		this.finalAuditingCid = finalAuditingCid;
	}

	public String getCycleTeacherIds() {
		return cycleTeacherIds;
	}

	public void setCycleTeacherIds(String cycleTeacherIds) {
		this.cycleTeacherIds = cycleTeacherIds;
	}

	public int getDeptLevel() {
		return deptLevel;
	}


	public String getJobNumber() {
		return jobNumber;
	}

	public void setJobNumber(String jobNumber) {
		this.jobNumber = jobNumber;
	}

	public Long getUserEvaluateId() {
		return userEvaluateId;
	}

	public void setUserEvaluateId(Long userEvaluateId) {
		this.userEvaluateId = userEvaluateId;
	}

	public Integer getIsEvaluate() {
		return isEvaluate;
	}

	public void setIsEvaluate(Integer isEvaluate) {
		this.isEvaluate = isEvaluate;
	}

	public String getTimeSection() {
		return timeSection;
	}

	public void setTimeSection(String timeSection) {
		this.timeSection = timeSection;
	}

	public String getYear() {
		return year;
	}

	public void setYear(String year) {
		this.year = year;
	}

	public String getBaseAliasName() {
		return baseAliasName;
	}

	public void setBaseAliasName(String baseAliasName) {
		this.baseAliasName = baseAliasName;
	}

	public String getResiName() {
		return resiName;
	}

	public void setResiName(String resiName) {
		this.resiName = resiName;
	}

	public String getTeachingTimeSection() {
		return teachingTimeSection;
	}

	public void setTeachingTimeSection(String teachingTimeSection) {
		this.teachingTimeSection = teachingTimeSection;
	}

	public void setDeptLevel(int deptLevel) {
		this.deptLevel = deptLevel;
	}

	public Long getParentDeptId() {
		return parentDeptId;
	}

	public void setParentDeptId(Long parentDeptId) {
		this.parentDeptId = parentDeptId;
	}

	public Integer getGraduateType() {
		return graduateType;
	}

	public void setGraduateType(Integer graduateType) {
		this.graduateType = graduateType;
	}

	public Integer getHasCertificate() {
		return hasCertificate;
	}

	public void setHasCertificate(Integer hasCertificate) {
		this.hasCertificate = hasCertificate;
	}

	public Integer getZyyUserStatus() {
		return zyyUserStatus;
	}

	public void setZyyUserStatus(Integer zyyUserStatus) {
		this.zyyUserStatus = zyyUserStatus;
	}

	public String getTeacherName() {
		return teacherName;
	}

	public void setTeacherName(String teacherName) {
		this.teacherName = teacherName;
	}

	public List<ZyyCycleTeacherVO> getCycleTeacherList() {
		return cycleTeacherList;
	}

	public void setCycleTeacherList(List<ZyyCycleTeacherVO> cycleTeacherList) {
		this.cycleTeacherList = cycleTeacherList;
	}

	public Date getDeptCycleStartDate() {
		return deptCycleStartDate;
	}

	public void setDeptCycleStartDate(Date deptCycleStartDate) {
		this.deptCycleStartDate = deptCycleStartDate;
	}

	public Date getDeptCycleEndDate() {
		return deptCycleEndDate;
	}

	public void setDeptCycleEndDate(Date deptCycleEndDate) {
		this.deptCycleEndDate = deptCycleEndDate;
	}

	public Long getCycleDeptId() {
		return cycleDeptId;
	}

	public void setCycleDeptId(Long cycleDeptId) {
		this.cycleDeptId = cycleDeptId;
	}

	public Long getStuId() {
		return stuId;
	}

	public void setStuId(Long stuId) {
		this.stuId = stuId;
	}

	public String getRealName() {
		return realName;
	}

	public void setRealName(String realName) {
		this.realName = realName;
	}

	public Long getBaseId() {
		return baseId;
	}

	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}

	public String getBaseName() {
		return baseName;
	}

	public void setBaseName(String baseName) {
		this.baseName = baseName;
	}

	public String getDeptName() {
		return deptName;
	}

	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}

	public int getSchoolSystem() {
		return schoolSystem;
	}

	public void setSchoolSystem(int schoolSystem) {
		this.schoolSystem = schoolSystem;
	}

	public int getUserYear() {
		return userYear;
	}

	public void setUserYear(int userYear) {
		this.userYear = userYear;
	}

	public int getCycleStatus() {
		return cycleStatus;
	}

	public void setCycleStatus(int cycleStatus) {
		this.cycleStatus = cycleStatus;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public Integer getFillinSdate() {
		return fillinSdate;
	}

	public void setFillinSdate(Integer fillinSdate) {
		this.fillinSdate = fillinSdate;
	}

	public Integer getFillinEdate() {
		return fillinEdate;
	}

	public void setFillinEdate(Integer fillinEdate) {
		this.fillinEdate = fillinEdate;
	}

	public Long getCtId() {
		return ctId;
	}

	public void setCtId(Long ctId) {
		this.ctId = ctId;
	}

	public String getFieldValue() {
		return fieldValue;
	}

	public void setFieldValue(String fieldValue) {
		this.fieldValue = fieldValue;
	}

	public Date getOldStartDate() {
		return oldStartDate;
	}

	public void setOldStartDate(Date oldStartDate) {
		this.oldStartDate = oldStartDate;
	}

	public Date getOldEndDate() {
		return oldEndDate;
	}

	public void setOldEndDate(Date oldEndDate) {
		this.oldEndDate = oldEndDate;
	}

	public Integer getResidencySource() {
		return residencySource;
	}

	public void setResidencySource(Integer residencySource) {
		this.residencySource = residencySource;
	}

	public String getCertificateNo() {
		return certificateNo;
	}

	public void setCertificateNo(String certificateNo) {
		this.certificateNo = certificateNo;
	}
	public int getStatus() {
		return status;
	}

	public void setStatus(int status) {
		this.status = status;
	}

	public String getStartAndEndTimeStr() {
		return startAndEndTimeStr;
	}

	public void setStartAndEndTimeStr(String startAndEndTimeStr) {
		this.startAndEndTimeStr = startAndEndTimeStr;
	}

	public Long getJdrId() {
		return jdrId;
	}

	public void setJdrId(Long jdrId) {
		this.jdrId = jdrId;
	}

	public Boolean getIsQueryDateInCycleDate() {
		return isQueryDateInCycleDate;
	}

	public void setIsQueryDateInCycleDate(Boolean isQueryDateInCycleDate) {
		this.isQueryDateInCycleDate = isQueryDateInCycleDate;
	}

	public List<ZyyTeacherMonth> getTeacherMonthList() {
		return teacherMonthList;
	}

	public void setTeacherMonthList(List<ZyyTeacherMonth> teacherMonthList) {
		this.teacherMonthList = teacherMonthList;
	}

	public Integer getRowspan() {
		return rowspan;
	}

	public void setRowspan(Integer rowspan) {
		this.rowspan = rowspan;
	}
	public Boolean getIsTimeOut() {
		return isTimeOut;
	}

	public void setIsTimeOut(Boolean isTimeOut) {
		this.isTimeOut = isTimeOut;
	}

	public Integer getPerformanceStatus() {
		return performanceStatus;
	}

	public void setPerformanceStatus(Integer performanceStatus) {
		this.performanceStatus = performanceStatus;
	}

	public Integer getVerifyStatus() {
		return verifyStatus;
	}

	public void setVerifyStatus(Integer verifyStatus) {
		this.verifyStatus = verifyStatus;
	}

	public Long getApplyId() {
		return applyId;
	}

	public void setApplyId(Long applyId) {
		this.applyId = applyId;
	}

	public Boolean getTeacherIsTimeOut() {
		return teacherIsTimeOut;
	}

	public void setTeacherIsTimeOut(Boolean teacherIsTimeOut) {
		this.teacherIsTimeOut = teacherIsTimeOut;
	}

	public String getTeacherAccountName() {
		return teacherAccountName;
	}

	public void setTeacherAccountName(String teacherAccountName) {
		this.teacherAccountName = teacherAccountName;
	}

	public String getTeacherCertificateNo() {
		return teacherCertificateNo;
	}

	public void setTeacherCertificateNo(String teacherCertificateNo) {
		this.teacherCertificateNo = teacherCertificateNo;
	}

	public String getResidencySourceStr() {
		return residencySourceStr;
	}

	public void setResidencySourceStr(String residencySourceStr) {
		this.residencySourceStr = residencySourceStr;
	}

	public String getCycleTimes() {
		return cycleTimes;
	}

	public void setCycleTimes(String cycleTimes) {
		this.cycleTimes = cycleTimes;
	}

	public String getTeacherTimes() {
		return teacherTimes;
	}

	public void setTeacherTimes(String teacherTimes) {
		this.teacherTimes = teacherTimes;
	}

	public String getCreateDateStr() {
		return createDateStr;
	}

	public void setCreateDateStr(String createDateStr) {
		this.createDateStr = createDateStr;
	}

	public String getTeacherDays() {
		return teacherDays;
	}

	public void setTeacherDays(String teacherDays) {
		this.teacherDays = teacherDays;
	}

	public Integer getUserCategory() {
		return userCategory;
	}

	public void setUserCategory(Integer userCategory) {
		this.userCategory = userCategory;
	}

	public String getUserCategoryStr() {
		return userCategoryStr;
	}

	public void setUserCategoryStr(String userCategoryStr) {
		this.userCategoryStr = userCategoryStr == null ? null : userCategoryStr.trim();
	}

	public Integer getAllowTeachingNum() {
		return allowTeachingNum;
	}

	public void setAllowTeachingNum(Integer allowTeachingNum) {
		this.allowTeachingNum = allowTeachingNum;
	}

	public Integer getTeachingNum() {
		return teachingNum;
	}

	public void setTeachingNum(Integer teachingNum) {
		this.teachingNum = teachingNum;
	}
}
