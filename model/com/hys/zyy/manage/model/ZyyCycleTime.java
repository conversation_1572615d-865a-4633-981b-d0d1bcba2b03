package com.hys.zyy.manage.model;

import java.util.Date;

/**
 * 学员科室轮转时间
 * 
 * <AUTHOR>
 * 
 */
public class ZyyCycleTime extends ZyyBaseObject {

	private static final long serialVersionUID = 723027538197004272L;

	private Date startDate;

	private Date endDate;

	private Long deptId;

	private Long residencyId;

	private String teacherName;
	private String teachDateStr;

	private String deptName;

	private String yearStr;

	private String checkStatus;
	
	private Integer finalCheckStatus;
	private String finalCheckStatusStr;

	private Long id;

	/**
	 * 学员状态：1，已经入科。0，未入科。
	 */
	private Integer status;

	/**
	 * 审核状态 1 合格出科 2 不合格出科 null或者0未出科
	 */
	private Integer auditState;
	
	public ZyyCycleTime() {
		super();
	}

	public ZyyCycleTime(Long residencyId, Date startDate, Date endDate) {
		super();
		this.startDate = startDate;
		this.endDate = endDate;
		this.residencyId = residencyId;
	}

	public Integer getFinalCheckStatus() {
		return finalCheckStatus;
	}

	public void setFinalCheckStatus(Integer finalCheckStatus) {
		this.finalCheckStatus = finalCheckStatus;
	}

	public String getFinalCheckStatusStr() {
		return finalCheckStatusStr;
	}

	public void setFinalCheckStatusStr(String finalCheckStatusStr) {
		this.finalCheckStatusStr = finalCheckStatusStr;
	}

	public String getCheckStatus() {
		return checkStatus;
	}

	public void setCheckStatus(String checkStatus) {
		this.checkStatus = checkStatus;
	}

	public String getYearStr() {
		return yearStr;
	}

	public void setYearStr(String yearStr) {
		this.yearStr = yearStr;
	}

	public String getDeptName() {
		return deptName;
	}

	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}

	public String getTeacherName() {
		return teacherName;
	}

	public void setTeacherName(String teacherName) {
		this.teacherName = teacherName;
	}

	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	public Long getDeptId() {
		return deptId;
	}

	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}

	public Long getResidencyId() {
		return residencyId;
	}

	public void setResidencyId(Long residencyId) {
		this.residencyId = residencyId;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Integer getAuditState() {
		return auditState;
	}

	public void setAuditState(Integer auditState) {
		this.auditState = auditState;
	}

	public String getTeachDateStr() {
		return teachDateStr;
	}

	public void setTeachDateStr(String teachDateStr) {
		this.teachDateStr = teachDateStr;
	}

}
