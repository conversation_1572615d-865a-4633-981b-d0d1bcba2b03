package com.hys.zyy.manage.model;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang.time.DateUtils;

/**
 * 学员时间段
 * <AUTHOR>
 *
 */
public class ZyyCycleTimeStage extends ZyyBaseObject{
	
	private static final long serialVersionUID = -8716606604758128670L;

	private Long userId;
	
	private Long deptId;
	
	private List<ZyyCycleTime> list;

	public ZyyCycleTimeStage(Long userId, Long deptId) {
		this.userId = userId;
		this.deptId = deptId;
		this.list = new ArrayList<ZyyCycleTime>();
	}

	public List<ZyyCycleTime> getStages() {
		return list;
	}

	public void add(ZyyCycleTime next) {
		boolean bool = false;
		for(ZyyCycleTime cur : list) {
			long value = next.getStartDate().getTime() - cur.getEndDate().getTime();
			if(value > 0 && value <= DateUtils.MILLIS_IN_DAY) {
				cur.setEndDate(next.getEndDate());
				bool = true;
				break;
			}
		}
		if(!bool)	this.list.add(next);
	}

}
