package com.hys.zyy.manage.model;

import java.util.Date;

import com.hys.zyy.manage.util.DateUtil;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：张伟清 2012-01-12
 * 
 * 描述：
 * 
 * 说明:
 */

public class ZyyDate extends ZyyBaseObject {

	private static final long serialVersionUID = 2493843382349235187L;

	/**
	 * 开始时间
	 */
	private Date startDate ;
	
	/**
	 * 结束时间
	 */
	private Date endDate ;
	
	/**
	 * 年度
	 */
	private String year ;
	
	/**
	 * 月份
	 */
	private String month ;
	
	/**
	 * 月初
	 */
	private String beginMonth ;

	/**
	 * 月末
	 */
	private String endMonth ;

	/**
	 * 本年度第几周 
	 */
	private String yearWeek ;

	/**
	 * 本月份第几周
	 */
	private String monthWeek ;
	
	/**
	 * 本月周数量
	 */
	private String monthWeeks ;

	public String getZyyDateInfo(){
		StringBuilder info = new StringBuilder() ;
		info.append("ZyyDate：[") ;
		info.append("startDate：" + DateUtil.format(this.startDate, "yyyy-MM-dd")).append(" ");
		info.append("endDate：" + DateUtil.format(this. endDate, "yyyy-MM-dd")).append(" ");
		info.append("year：" + this.year).append(" ");
		info.append("month：" + this.month).append(" ");
		info.append("beginMonth：" + this.beginMonth).append(" ");
		info.append("endMonth：" + this.endMonth).append(" ");
		info.append("yearWeek：" + this.yearWeek).append(" ");
		info.append("monthWeek：" + this.monthWeek).append(" ");
		info.append("monthWeeks：" + this.monthWeeks);
		info.append("]") ;
		return info.toString();
	}
	
	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	public String getYear() {
		return year;
	}

	public void setYear(String year) {
		this.year = year;
	}

	public String getMonth() {
		return month;
	}

	public void setMonth(String month) {
		this.month = month;
	}

	public String getBeginMonth() {
		return beginMonth;
	}

	public void setBeginMonth(String beginMonth) {
		this.beginMonth = beginMonth;
	}

	public String getEndMonth() {
		return endMonth;
	}

	public void setEndMonth(String endMonth) {
		this.endMonth = endMonth;
	}

	public String getYearWeek() {
		return yearWeek;
	}

	public void setYearWeek(String yearWeek) {
		this.yearWeek = yearWeek;
	}

	public String getMonthWeek() {
		return monthWeek;
	}

	public void setMonthWeek(String monthWeek) {
		this.monthWeek = monthWeek;
	}

	public String getMonthWeeks() {
		return monthWeeks;
	}

	public void setMonthWeeks(String monthWeeks) {
		this.monthWeeks = monthWeeks;
	}
}