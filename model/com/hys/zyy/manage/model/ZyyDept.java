package com.hys.zyy.manage.model;

import com.hys.zyy.manage.json.SimpleDateSerializer;
import org.codehaus.jackson.map.annotate.JsonSerialize;

import java.util.Date;
import java.util.List;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：李海龙 Mar 19, 2012
 * 
 * 描述：
 * 
 * 说明:
 */

public class ZyyDept extends ZyyBaseObject {

	private static final long serialVersionUID = 8222972991087551938L;
	
	/**
	 * 主键ID
	 */
	protected Long id;
	
	/**
	 * 医院ID
	 */
	private Long hospitalId;
	
	/**
	 * 科室名称
	 */
	protected String deptName;
	
	/**
	 * 上级科室编号
	 */
	private Long parentDeptId;
	
	/**
	 * 科室编码
	 */
	private String deptCode;
	
	/**
	 * 基础科室ID
	 */
	private Long baseDeptId;

	/**
	 * 科室类别
	 */
	private Integer deptType;

	/**
	 * 是否叶子节点
	 */
	private Integer isleaf;
	
	/**
	 * 科室级别
	 */
	private Integer deptLevel;
	
	/**
	 * 最后更新时间
	 */
	private Date lastUpdateDate;
	
	/**
	 * 行合并数目
	 */
	private int rowspan = 1;
	
	/**
	 * 下一级科室
	 */
	private List<ZyyDept> deptList;
	
	/**
	 * 
	 * 联合医院名称
	 * 
	 */
	private String unionHospitalName;

	/**
	 * 创建用户的科室ID
	 */
	private Long createDeptAccountId;
	/**
	 * 限制最小人数
	 */
	private Long limitMinNum;
	/**
	 * 限制最大人数
	 */
	private Long limitMaxNum;
	
	private Long level;											// 查询时科室所在层级
	
	private Long cycleId;
	
	private Long residencyId;
	
	private Long deptUserId;
	
	private Long tableId;
	
	private String cycleBaseName;					// 轮转基地
	
	private Date cycleStartDate;
	
	private Date cycleEndDate;
	
	private List<ZyyCycleResiCycleCheck> cycleCheck;
	
	private List<ZyyDeptStd> zyyDeptStd;
	
	private List<ZyyCycleTableResiCycle> cycleData;
	
	private String timeSection;

	private ZyyDept parentDept;
	
	/**
	 * 入科审核状态：1，已经入科。0，未入科。
	 */
	private Integer inDeptStatus;
	
	/**
	 * 出科审核状态：
	 * null or 0  未出科,
	 * 1 合格出科,
	 * 2 不合格出科
	 */
	private Integer outDeptState;
	
	private Integer auditState;
	
	private Integer status2;
	
	/**
	 * 是否临床科室  1：是 0:否
	 */
	private Integer isClinicalDept;
	
	private String yearAndMonthStr;
	/*
	 * 科室考勤上报状态（1=全部；2=已上报；3=未上报；4=无需上报）
	 */
	private Integer attAction;
	
	private Integer resiCount;

	//BDP_DIC_ID	N	NUMBER(18)	Y			BDP字典值ID
	private Long bdpDicId;
	//BDP_DIC_CODE	N	VARCHAR2(50)	Y			BDP字典值CODE
	private String bdpDicCode;

	//这个字段不存库
	//bdp的字典ID的父级ID
	private Long bdpParentDictId;

	private String deptStage;
	
	public ZyyDept() {
		super();
	}

	public ZyyDept(Long id, Long parentDeptId) {
		super();
		this.id = id;
		this.parentDeptId = parentDeptId;
	}

	public ZyyDept(Long hospitalId, String yearAndMonthStr, Integer attAction) {
		super();
		this.hospitalId = hospitalId;
		this.yearAndMonthStr = yearAndMonthStr;
		this.attAction = attAction;
	}

	public Integer getIsClinicalDept() {
		return isClinicalDept;
	}

	public void setIsClinicalDept(Integer isClinicalDept) {
		this.isClinicalDept = isClinicalDept;
	}

	public Integer getAuditState() {
		return auditState;
	}

	public void setAuditState(Integer auditState) {
		this.auditState = auditState;
	}

	public Integer getStatus2() {
		return status2;
	}

	public void setStatus2(Integer status2) {
		this.status2 = status2;
	}

	public Integer getInDeptStatus() {
		return inDeptStatus;
	}

	public void setInDeptStatus(Integer inDeptStatus) {
		this.inDeptStatus = inDeptStatus;
	}

	public Integer getOutDeptState() {
		return outDeptState;
	}

	public void setOutDeptState(Integer outDeptState) {
		this.outDeptState = outDeptState;
	}

	public Long getCreateDeptAccountId() {
		return createDeptAccountId;
	}

	public void setCreateDeptAccountId(Long createDeptAccountId) {
		this.createDeptAccountId = createDeptAccountId;
	}

	public List<ZyyDept> getDeptList() {
		return deptList;
	}

	public void setDeptList(List<ZyyDept> deptList) {
		this.deptList = deptList;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getHospitalId() {
		return hospitalId;
	}

	public void setHospitalId(Long hospitalId) {
		this.hospitalId = hospitalId;
	}

	public String getDeptName() {
		return deptName;
	}

	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}

	public Long getParentDeptId() {
		return parentDeptId;
	}

	public void setParentDeptId(Long parentDeptId) {
		this.parentDeptId = parentDeptId;
	}

	public String getDeptCode() {
		return deptCode;
	}

	public void setDeptCode(String deptCode) {
		this.deptCode = deptCode;
	}

	public Long getBaseDeptId() {
		return baseDeptId;
	}

	public void setBaseDeptId(Long baseDeptId) {
		this.baseDeptId = baseDeptId;
	}

	public Integer getDeptType() {
		return deptType;
	}

	public void setDeptType(Integer deptType) {
		this.deptType = deptType;
	}

	public Integer getIsleaf() {
		return isleaf;
	}

	public void setIsleaf(Integer isleaf) {
		this.isleaf = isleaf;
	}

	public Integer getDeptLevel() {
		return deptLevel;
	}

	public void setDeptLevel(Integer deptLevel) {
		this.deptLevel = deptLevel;
	}

	public Date getLastUpdateDate() {
		return lastUpdateDate;
	}

	public void setLastUpdateDate(Date lastUpdateDate) {
		this.lastUpdateDate = lastUpdateDate;
	}

	public int getRowspan() {
		return rowspan;
	}

	public void setRowspan(int rowspan) {
		
		if(rowspan == 0){
			rowspan = 1;
		}
		this.rowspan = rowspan;
	}
	
	public String getUnionHospitalName() {
		return unionHospitalName;
	}

	public void setUnionHospitalName(String unionHospitalName) {
		this.unionHospitalName = unionHospitalName;
	}

	public Long getLevel() {
		return level;
	}

	public void setLevel(Long level) {
		this.level = level;
	}

	public Long getCycleId() {
		return cycleId;
	}

	public void setCycleId(Long cycleId) {
		this.cycleId = cycleId;
	}

	public List<ZyyCycleResiCycleCheck> getCycleCheck() {
		return cycleCheck;
	}

	public void setCycleCheck(List<ZyyCycleResiCycleCheck> cycleCheck) {
		this.cycleCheck = cycleCheck;
	}

	public Long getTableId() {
		return tableId;
	}

	public void setTableId(Long tableId) {
		this.tableId = tableId;
	}

	@JsonSerialize(using = SimpleDateSerializer.class)
	public Date getCycleStartDate() {
		return cycleStartDate;
	}

	public void setCycleStartDate(Date cycleStartDate) {
		this.cycleStartDate = cycleStartDate;
	}

	@JsonSerialize(using = SimpleDateSerializer.class)
	public Date getCycleEndDate() {
		return cycleEndDate;
	}

	public void setCycleEndDate(Date cycleEndDate) {
		this.cycleEndDate = cycleEndDate;
	}

	public String getCycleBaseName() {
		return cycleBaseName;
	}

	public void setCycleBaseName(String cycleBaseName) {
		this.cycleBaseName = cycleBaseName;
	}

	public List<ZyyDeptStd> getZyyDeptStd() {
		return zyyDeptStd;
	}

	public void setZyyDeptStd(List<ZyyDeptStd> zyyDeptStd) {
		this.zyyDeptStd = zyyDeptStd;
	}

	public void setCycleData(List<ZyyCycleTableResiCycle> cycleData) {
		this.cycleData = cycleData;
	}

	public List<ZyyCycleTableResiCycle> getCycleData() {
		return cycleData;
	}

	public Long getResidencyId() {
		return residencyId;
	}

	public void setResidencyId(Long residencyId) {
		this.residencyId = residencyId;
	}

	public Long getDeptUserId() {
		return deptUserId;
	}

	public void setDeptUserId(Long deptUserId) {
		this.deptUserId = deptUserId;
	}

	public String getTimeSection() {
		return timeSection;
	}

	public void setTimeSection(String timeSection) {
		this.timeSection = timeSection;
	}

	public Long getLimitMinNum() {
		return limitMinNum;
	}

	public void setLimitMinNum(Long limitMinNum) {
		this.limitMinNum = limitMinNum;
	}

	public Long getLimitMaxNum() {
		return limitMaxNum;
	}

	public void setLimitMaxNum(Long limitMaxNum) {
		this.limitMaxNum = limitMaxNum;
	}

	public ZyyDept getParentDept() {
		return parentDept;
	}

	public void setParentDept(ZyyDept parentDept) {
		this.parentDept = parentDept;
	}

	public String getYearAndMonthStr() {
		return yearAndMonthStr;
	}

	public void setYearAndMonthStr(String yearAndMonthStr) {
		this.yearAndMonthStr = yearAndMonthStr == null ? null : yearAndMonthStr.trim();
	}

	public Integer getAttAction() {
		return attAction;
	}

	public void setAttAction(Integer attAction) {
		this.attAction = attAction;
	}

	public Integer getResiCount() {
		return resiCount;
	}

	public void setResiCount(Integer resiCount) {
		this.resiCount = resiCount;
	}


	public Long getBdpDicId() {
		return bdpDicId;
	}

	public void setBdpDicId(Long bdpDicId) {
		this.bdpDicId = bdpDicId;
	}

	public String getBdpDicCode() {
		return bdpDicCode;
	}

	public void setBdpDicCode(String bdpDicCode) {
		this.bdpDicCode = bdpDicCode;
	}

	public Long getBdpParentDictId() {
		return bdpParentDictId;
	}

	public void setBdpParentDictId(Long bdpParentDictId) {
		this.bdpParentDictId = bdpParentDictId;
	}

	public String getDeptStage() {
		return deptStage;
	}

	public void setDeptStage(String deptStage) {
		this.deptStage = deptStage;
	}
}
