package com.hys.zyy.manage.model;
/**
 * 选修轮转科室 2013-6-18 xusq
 */
public class ZyyDeptCycleElective extends ZyyBaseObject {

	/**
	 * 
	 */
	private static final long serialVersionUID = 3543623864642178750L;

	/**
	 * ID
	 */
	private Long id;
	
	/**
	 * 医院ID
	 */
	private Long hospitalId; 
	
	/**
	 * 科室ID
	 */
	private Long zyyDeptId;
	
	/**
	 * 基地ID
	 */
	private Long zyyBaseId;
	
	/**
	 * 学制
	 */
	private Integer educationSystem;
	/**
	 * 选修科室名称
	 */
	private String electiveDeptName;
	
	/**
	 * 选修方案名称
	 */
	private String name;
	
	/**
	 * 科室名称
	 */
	private String deptName;
	
	/**
	 * 共有几个候选科室
	 */
	private Long deptNum;
	
	/**
	 * 在候选科室中选几个
	 */
	private Long chooseDeptNum;


	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getHospitalId() {
		return hospitalId;
	}

	public void setHospitalId(Long hospitalId) {
		this.hospitalId = hospitalId;
	}

	public Long getZyyDeptId() {
		return zyyDeptId;
	}

	public void setZyyDeptId(Long zyyDeptId) {
		this.zyyDeptId = zyyDeptId;
	}

	public Long getZyyBaseId() {
		return zyyBaseId;
	}

	public void setZyyBaseId(Long zyyBaseId) {
		this.zyyBaseId = zyyBaseId;
	}

	public Integer getEducationSystem() {
		return educationSystem;
	}

	public void setEducationSystem(Integer educationSystem) {
		this.educationSystem = educationSystem;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getDeptName() {
		return deptName;
	}

	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}

	public String getElectiveDeptName() {
		return electiveDeptName;
	}

	public void setElectiveDeptName(String electiveDeptName) {
		this.electiveDeptName = electiveDeptName;
	}

	public Long getDeptNum() {
		return deptNum;
	}

	public void setDeptNum(Long deptNum) {
		this.deptNum = deptNum;
	}

	public Long getChooseDeptNum() {
		return chooseDeptNum;
	}

	public void setChooseDeptNum(Long chooseDeptNum) {
		this.chooseDeptNum = chooseDeptNum;
	}
	
}
