package com.hys.zyy.manage.model;

import java.util.List;

public class ZyyDeptCycleElectiveVO extends ZyyBaseObject{

	private static final long serialVersionUID = -3563046921077601699L;

	/**
	 * 基地对应科室 
	 */
	private List<ZyyBaseVO> list;
	
	/**
	 *	基地选修科室信息ID
	 */
	private Long zdcpId;
	
	/**
	 * 年制 
	 */
	private int educationSystem;
	
	/**
	 *	基地id 
	 */
	private Long baseId;
	
	/**
	 *  基地选修科室信息List
	 */
	private List<ZyyDeptCycleElective> electiveDeptlist;
	
	/**
	 * 选修方案名称
	 */
	private String name;
	
	/**
	 * 共有几个候选科室
	 */
	private Long deptNum;
	
	/**
	 * 在候选科室中选几个
	 */
	private Long chooseDeptNum;

	public int getEducationSystem() {
		return educationSystem;
	}

	public void setEducationSystem(int educationSystem) {
		this.educationSystem = educationSystem;
	}

	public List<ZyyBaseVO> getList() {
		return list;
	}

	public void setList(List<ZyyBaseVO> list) {
		this.list = list;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Long getBaseId() {
		return baseId;
	}

	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}

	public Long getZdcpId() {
		return zdcpId;
	}

	public void setZdcpId(Long zdcpId) {
		this.zdcpId = zdcpId;
	}

	public List<ZyyDeptCycleElective> getElectiveDeptlist() {
		return electiveDeptlist;
	}

	public void setElectiveDeptlist(List<ZyyDeptCycleElective> electiveDeptlist) {
		this.electiveDeptlist = electiveDeptlist;
	}

	public Long getDeptNum() {
		return deptNum;
	}

	public void setDeptNum(Long deptNum) {
		this.deptNum = deptNum;
	}

	public Long getChooseDeptNum() {
		return chooseDeptNum;
	}

	public void setChooseDeptNum(Long chooseDeptNum) {
		this.chooseDeptNum = chooseDeptNum;
	}

}
