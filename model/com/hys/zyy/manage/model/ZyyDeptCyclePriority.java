package com.hys.zyy.manage.model;

public class ZyyDeptCyclePriority extends ZyyBaseObject {

	/**
	 * 
	 */
	private static final long serialVersionUID = 3543623864642178750L;

	/**
	 * ID
	 */
	private Long id;
	
	/**
	 * 医院ID
	 */
	private Long hospitalId; 
	
	/**
	 * 科室ID
	 */
	private Long zyyDeptId;
	
	/**
	 * 基地ID
	 */
	private Long zyyBaseId;
	
	/**
	 * 学制
	 */
	private Integer educationSystem;
	/**
	 * 优先排科室名称
	 */
	private String priName;
	
	/**
	 * 2013-6-20 xusq 
	 * 区分是优先还是滞后 1 优先 2 滞后
	 */
	private int type;
	
	public String getPriName() {
		return priName;
	}
	
	/**
	 * 连排名称
	 */
	private String name;
	
	/**
	 * 科室名称
	 */
	private String deptName;

	public void setPriName(String priName) {
		this.priName = priName;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getHospitalId() {
		return hospitalId;
	}

	public void setHospitalId(Long hospitalId) {
		this.hospitalId = hospitalId;
	}

	public Long getZyyDeptId() {
		return zyyDeptId;
	}

	public void setZyyDeptId(Long zyyDeptId) {
		this.zyyDeptId = zyyDeptId;
	}

	public Long getZyyBaseId() {
		return zyyBaseId;
	}

	public void setZyyBaseId(Long zyyBaseId) {
		this.zyyBaseId = zyyBaseId;
	}

	public Integer getEducationSystem() {
		return educationSystem;
	}

	public void setEducationSystem(Integer educationSystem) {
		this.educationSystem = educationSystem;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getDeptName() {
		return deptName;
	}

	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}

	public int getType() {
		return type;
	}

	public void setType(int type) {
		this.type = type;
	}
	
}
