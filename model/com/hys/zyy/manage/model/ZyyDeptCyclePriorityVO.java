package com.hys.zyy.manage.model;

import java.util.List;

public class ZyyDeptCyclePriorityVO extends ZyyBaseObject{

	private static final long serialVersionUID = 8061932882182378346L;

	/**
	 * 基地对应科室 
	 */
	private List<ZyyBaseVO> list;
	
	/**
	 *	基地优先科室信息ID
	 */
	private Long zdcpId;
	
	/**
	 * 年制 
	 */
	private int educationSystem;
	
	/**
	 *	基地id 
	 */
	private Long baseId;
	
	/**
	 *  基地优先科室信息List
	 */
	private List<ZyyDeptCyclePriority> priorityDeptlist;
	
	/**
	 * 优先名称
	 */
	private String name;
	
	/**
	 * 2013-6-20 xusq 
	 * 区分是优先还是滞后 1 优先 2 滞后
	 */
	private int type;

	public int getEducationSystem() {
		return educationSystem;
	}

	public void setEducationSystem(int educationSystem) {
		this.educationSystem = educationSystem;
	}

	public List<ZyyBaseVO> getList() {
		return list;
	}

	public void setList(List<ZyyBaseVO> list) {
		this.list = list;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Long getBaseId() {
		return baseId;
	}

	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}

	public Long getZdcpId() {
		return zdcpId;
	}

	public void setZdcpId(Long zdcpId) {
		this.zdcpId = zdcpId;
	}

	public List<ZyyDeptCyclePriority> getPriorityDeptlist() {
		return priorityDeptlist;
	}

	public void setPriorityDeptlist(List<ZyyDeptCyclePriority> priorityDeptlist) {
		this.priorityDeptlist = priorityDeptlist;
	}

	public int getType() {
		return type;
	}

	public void setType(int type) {
		this.type = type;
	}
}
