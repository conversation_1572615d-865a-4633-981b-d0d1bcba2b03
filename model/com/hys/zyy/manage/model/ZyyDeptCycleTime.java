package com.hys.zyy.manage.model;

import java.util.List;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：张伟清 2012-05-23
 * 
 * 描述：轮转科室时间表
 * 
 * 说明:
 */
public class ZyyDeptCycleTime extends ZyyBaseObject {

	private static final long serialVersionUID = -836624330807308255L;

	/**
	 * 主键ID
	 */
	private Long id ;
	
	/**
	 * 医院ID
	 */
	private Long hospitalId ;
	
	/**
	 * 科室ID
	 */
	private Long zyyDeptId ;
	
	/**
	 * 基地ID
	 */
	private Long zyyBaseId ;
	
	/**
	 * 轮转时间下的科室列表
	 */
	private List<ZyyDeptCycleTimeDetail> cycleTimeDetailList;
	
	/**
	 * 学制
	 */
	private Integer educationSystem ;
	
	/**
	 * 轮转时间
	 */
	private Integer cycleTime ;
	
	/**
	 * 轮转时间名称
	 */
	private String cycleTimeName;
	
	
	/**
	 * 月总数,DB里类型为number(10,1)
	 */
	private Double monthTotal;
	
	/**
	 * 周总数
	 */
	private Integer weekTotal; 
	
	/**
	 * 状态
	 * 1 -正常; -1 -删除
	 */
	private Integer status;

	
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getHospitalId() {
		return hospitalId;
	}

	public void setHospitalId(Long hospitalId) {
		this.hospitalId = hospitalId;
	}

	public Long getZyyDeptId() {
		return zyyDeptId;
	}

	public void setZyyDeptId(Long zyyDeptId) {
		this.zyyDeptId = zyyDeptId;
	}

	public Long getZyyBaseId() {
		return zyyBaseId;
	}

	public void setZyyBaseId(Long zyyBaseId) {
		this.zyyBaseId = zyyBaseId;
	}

	public Integer getEducationSystem() {
		return educationSystem;
	}

	public void setEducationSystem(Integer educationSystem) {
		this.educationSystem = educationSystem;
	}

	public Integer getCycleTime() {
		return cycleTime;
	}

	public void setCycleTime(Integer cycleTime) {
		this.cycleTime = cycleTime;
	}

	
	

	public String getCycleTimeName() {
		return cycleTimeName;
	}

	public void setCycleTimeName(String cycleTimeName) {
		this.cycleTimeName = cycleTimeName;
	}

	public Double getMonthTotal() {
		return monthTotal;
	}

	public void setMonthTotal(Double monthTotal) {
		this.monthTotal = monthTotal;
	}

	public Integer getWeekTotal() {
		return weekTotal;
	}

	public void setWeekTotal(Integer weekTotal) {
		this.weekTotal = weekTotal;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public List<ZyyDeptCycleTimeDetail> getCycleTimeDetailList() {
		return cycleTimeDetailList;
	}

	public void setCycleTimeDetailList(
			List<ZyyDeptCycleTimeDetail> cycleTimeDetailList) {
		this.cycleTimeDetailList = cycleTimeDetailList;
	}
}
