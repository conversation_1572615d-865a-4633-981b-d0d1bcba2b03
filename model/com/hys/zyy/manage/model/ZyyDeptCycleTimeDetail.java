package com.hys.zyy.manage.model;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：陈来宾 2012-10-15
 * 
 * 描述：轮转科室时间表详细
 * 
 * 说明:
 */
public class ZyyDeptCycleTimeDetail extends ZyyBaseObject {

	/**
	 * 
	 */
	private static final long serialVersionUID = -7496167450553616932L;

	/**
	 * 主键ID
	 */
	private Long id ;
	
	/**
	 * 轮转科室时间表ID
	 */
	private Long cycleTimeId ;
	
	/**
	 * 轮转科室ID
	 */
	private Long zyyDeptId ;
	
	/**
	 * 轮转时间类别
	 * 1 -周; 2 -月
	 */
	private Long cycleTimeType ;
	
	/**
	 * 轮转时间
	 */
	private Double cycleTime ;
	
	/**
	 * 科室名称
	 */
	private String deptName;
	
	

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getZyyDeptId() {
		return zyyDeptId;
	}

	public void setZyyDeptId(Long zyyDeptId) {
		this.zyyDeptId = zyyDeptId;
	}

	public Double getCycleTime() {
		return cycleTime;
	}

	public void setCycleTime(Double cycleTime) {
		this.cycleTime = cycleTime;
	}

	public Long getCycleTimeId() {
		return cycleTimeId;
	}

	public void setCycleTimeId(Long cycleTimeId) {
		this.cycleTimeId = cycleTimeId;
	}

	public String getDeptName() {
		return deptName;
	}

	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}

	public Long getCycleTimeType() {
		return cycleTimeType;
	}

	public void setCycleTimeType(Long cycleTimeType) {
		this.cycleTimeType = cycleTimeType;
	}

}
