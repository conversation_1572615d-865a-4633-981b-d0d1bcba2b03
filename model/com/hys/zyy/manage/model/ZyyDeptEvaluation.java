package com.hys.zyy.manage.model;

import java.util.Date;

import com.hys.zyy.manage.util.annotation.Column;
import com.hys.zyy.manage.util.annotation.Id;
import com.hys.zyy.manage.util.annotation.Table;

/**
 * 科室评价记录
 * <AUTHOR>
 *
 */
@Table("zyy_dept_evaluation")
public class ZyyDeptEvaluation extends ZyyBaseObject{
	
	private static final long serialVersionUID = 6863725962435921670L;

	@Id("zyy_dept_evaluation_seq.nextval")
	@Column("id")
	private Long id;
	
	@Column("f")
	private Long f;
	
	@Column("t")
	private Long t;
	
	@Column("dept_id")
	private Long deptId;;
	
	@Column("std_dept_id")
	private Long stdDeptId;
	
	@Column("text")
	private String text;
	
	@Column("create_date")
	private Date createDate;

	public ZyyDeptEvaluation() {
		
	}
	
	public ZyyDeptEvaluation(Long t, String evaluation) {
		this.t = t;
		this.text = evaluation;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getText() {
		return text;
	}

	public void setText(String text) {
		this.text = text;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Long getDeptId() {
		return deptId;
	}

	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}

	public Long getStdDeptId() {
		return stdDeptId;
	}

	public void setStdDeptId(Long stdDeptId) {
		this.stdDeptId = stdDeptId;
	}

	public Long getF() {
		return f;
	}

	public void setF(Long f) {
		this.f = f;
	}

	public Long getT() {
		return t;
	}

	public void setT(Long t) {
		this.t = t;
	}

	@Override
	public String toString() {
		return "ZyyDeptEvaluation [id=" + id + ", f=" + f + ", t=" + t
				+ ", deptId=" + deptId + ", stdDeptId=" + stdDeptId + ", text="
				+ text + ", createDate=" + createDate + "]";
	}

}
