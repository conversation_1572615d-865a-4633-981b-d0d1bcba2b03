package com.hys.zyy.manage.model;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：张伟清 2012-04-16
 * 
 * 描述：轮转科室扩展表
 * 
 * 说明:
 */

public class ZyyDeptExt extends ZyyBaseObject {

	private static final long serialVersionUID = -5520437776527794574L;

	/**
	 * 科室ID
	 */
	private Long deptId ;
	
	/**
	 * 科室延迟天数
	 */
	private Integer delayDays ;
	
	/**
	 * 最高人数限制(最小) 
	 */
	private Integer deptMaxNumber1 ;
	
	/**
	 * 最高人数限制(最大)
	 */
	private Integer deptMaxNumber2 ;

	public Long getDeptId() {
		return deptId;
	}

	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}

	public Integer getDelayDays() {
		return delayDays;
	}

	public void setDelayDays(Integer delayDays) {
		this.delayDays = delayDays;
	}

	public Integer getDeptMaxNumber1() {
		return deptMaxNumber1;
	}

	public void setDeptMaxNumber1(Integer deptMaxNumber1) {
		this.deptMaxNumber1 = deptMaxNumber1;
	}

	public Integer getDeptMaxNumber2() {
		return deptMaxNumber2;
	}

	public void setDeptMaxNumber2(Integer deptMaxNumber2) {
		this.deptMaxNumber2 = deptMaxNumber2;
	}
}
