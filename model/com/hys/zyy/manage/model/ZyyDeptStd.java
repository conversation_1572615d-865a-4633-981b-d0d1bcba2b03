package com.hys.zyy.manage.model;

import java.util.Date;
import java.util.List;

import org.codehaus.jackson.map.annotate.JsonSerialize;

import com.alibaba.fastjson.serializer.SimpleDateFormatSerializer;
import com.hys.zyy.manage.json.SimpleDateSerializer;
import com.hys.zyy.manage.util.annotation.Column;
import com.hys.zyy.manage.util.annotation.Id;
import com.hys.zyy.manage.util.annotation.Table;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：张伟清 2012-05-03
 * 
 * 描述：住院医师轮转标准科室
 * 
 * 说明:
 */
@Table("zyy_dept_std")
public class ZyyDeptStd extends ZyyBaseObject {

	private static final long serialVersionUID = 3975546558251122775L;

	/**
	 * 主键ID
	 */
	@Id("ZYY_DEPT_STD_SEQ.nextval")
	@Column("ID")
	private Long id;

	/**
	 * 组织机构ID
	 */
	@Column("ZYY_ORG_ID")
	private Long zyyOrgId;

	/**
	 * 科室名称
	 */
	@Column("NAME")
	private String name;

	/**
	 * 备注
	 */
	@Column("REMARK")
	private String remark;
	@Column("IS_REQUIRED")
	private java.lang.Boolean isRequired;
	@Column("STATUS")
	private java.lang.Integer status;
	@Column("DEPT_STD_TYPE")
	private java.lang.Integer deptStdType;
	@Column("CYCLE_REMARK")
	private java.lang.String cycleRemark;
	@Column("CYCLE_PURPOSE")
	private java.lang.String cyclePurpose;
	@Column("BASIC_REQUIREMENT_DESC")
	private java.lang.String basicRequirementDesc;
	@Column("HIGH_REQUIREMENT_DESC")
	private java.lang.String highRequirementDesc;
	@Column("OTHER_REQUIREMENT_DESC")
	private java.lang.String otherRequirementDesc;
	@Column("REFERENCE_BOOK")
	private java.lang.String referenceBook;
	@Column("CYCLE_TIME")
	private java.lang.String cycleTime;
	
	private java.lang.Boolean isModify;		// 标准是否被修改
	
	private java.lang.Long baseId;
	
	private Date cycleCommitDate;							// 提交时间
	
	private Date otherCommitDate;

	private List<ZyyCycleResiCycleCheck> cycleCheck;		// 轮转记录审核
	
	private List<ZyyResidencyHbHspCheck> otherCheck;		// 其他部分审核
	
	private ZyyResidencyHandbookHosp finalCheck;			// 终审
        
    private java.lang.Boolean isPro;
	
    private List<ZyyDeptVO> deptVOList;  //yjk 标准科室(登记手册)对应的实际轮转科室集合(登记手册中用)

	private Long manualStdId;

	public Long getManualStdId() {
		return manualStdId;
	}

	public void setManualStdId(Long manualStdId) {
		this.manualStdId = manualStdId;
	}

	public List<ZyyDeptVO> getDeptVOList() {
		return deptVOList;
	}

	public void setDeptVOList(List<ZyyDeptVO> deptVOList) {
		this.deptVOList = deptVOList;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getZyyOrgId() {
		return zyyOrgId;
	}

	public void setZyyOrgId(Long zyyOrgId) {
		this.zyyOrgId = zyyOrgId;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public java.lang.Boolean getIsRequired() {
		return isRequired;
	}

	public void setIsRequired(java.lang.Boolean isRequired) {
		this.isRequired = isRequired;
	}

	public java.lang.Integer getStatus() {
		return status;
	}

	public void setStatus(java.lang.Integer status) {
		this.status = status;
	}

	public java.lang.Integer getDeptStdType() {
		return deptStdType;
	}

	public void setDeptStdType(java.lang.Integer deptStdType) {
		this.deptStdType = deptStdType;
	}

	public java.lang.String getCycleRemark() {
		return cycleRemark;
	}

	public void setCycleRemark(java.lang.String cycleRemark) {
		this.cycleRemark = cycleRemark;
	}

	public java.lang.String getCyclePurpose() {
		return cyclePurpose;
	}

	public void setCyclePurpose(java.lang.String cyclePurpose) {
		this.cyclePurpose = cyclePurpose;
	}

	public java.lang.String getBasicRequirementDesc() {
		return basicRequirementDesc;
	}

	public void setBasicRequirementDesc(java.lang.String basicRequirementDesc) {
		this.basicRequirementDesc = basicRequirementDesc;
	}

	public java.lang.String getHighRequirementDesc() {
		return highRequirementDesc;
	}

	public void setHighRequirementDesc(java.lang.String highRequirementDesc) {
		this.highRequirementDesc = highRequirementDesc;
	}

	public java.lang.String getOtherRequirementDesc() {
		return otherRequirementDesc;
	}

	public void setOtherRequirementDesc(java.lang.String otherRequirementDesc) {
		this.otherRequirementDesc = otherRequirementDesc;
	}

	public java.lang.String getReferenceBook() {
		return referenceBook;
	}

	public void setReferenceBook(java.lang.String referenceBook) {
		this.referenceBook = referenceBook;
	}

	public java.lang.String getCycleTime() {
		return cycleTime;
	}

	public void setCycleTime(java.lang.String cycleTime) {
		this.cycleTime = cycleTime;
	}

	public java.lang.Boolean getIsModify() {
		return isModify;
	}

	public void setIsModify(java.lang.Boolean isModify) {
		this.isModify = isModify;
	}

	public java.lang.Long getBaseId() {
		return baseId;
	}

	public void setBaseId(java.lang.Long baseId) {
		this.baseId = baseId;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime
				* result
				+ ((basicRequirementDesc == null) ? 0 : basicRequirementDesc
						.hashCode());
		result = prime * result
				+ ((cyclePurpose == null) ? 0 : cyclePurpose.hashCode());
		result = prime * result
				+ ((cycleRemark == null) ? 0 : cycleRemark.hashCode());
		result = prime
				* result
				+ ((highRequirementDesc == null) ? 0 : highRequirementDesc
						.hashCode());
		result = prime
				* result
				+ ((otherRequirementDesc == null) ? 0 : otherRequirementDesc
						.hashCode());
		result = prime * result
				+ ((referenceBook == null) ? 0 : referenceBook.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ZyyDeptStd other = (ZyyDeptStd) obj;
		if (basicRequirementDesc == null) {
			if (other.basicRequirementDesc != null)
				return false;
		} else if (!basicRequirementDesc.equals(other.basicRequirementDesc))
			return false;
		if (cyclePurpose == null) {
			if (other.cyclePurpose != null)
				return false;
		} else if (!cyclePurpose.equals(other.cyclePurpose))
			return false;
		if (cycleRemark == null) {
			if (other.cycleRemark != null)
				return false;
		} else if (!cycleRemark.equals(other.cycleRemark))
			return false;
		if (highRequirementDesc == null) {
			if (other.highRequirementDesc != null)
				return false;
		} else if (!highRequirementDesc.equals(other.highRequirementDesc))
			return false;
		if (otherRequirementDesc == null) {
			if (other.otherRequirementDesc != null)
				return false;
		} else if (!otherRequirementDesc.equals(other.otherRequirementDesc))
			return false;
		if (referenceBook == null) {
			if (other.referenceBook != null)
				return false;
		} else if (!referenceBook.equals(other.referenceBook))
			return false;
		return true;
	}

	public void setCycleCheck(List<ZyyCycleResiCycleCheck> cycleCheck) {
		this.cycleCheck = cycleCheck;
	}

	public List<ZyyCycleResiCycleCheck> getCycleCheck() {
		return cycleCheck;
	}

	public List<ZyyResidencyHbHspCheck> getOtherCheck() {
		return otherCheck;
	}

	public void setOtherCheck(List<ZyyResidencyHbHspCheck> otherCheck) {
		this.otherCheck = otherCheck;
	}

	public ZyyResidencyHandbookHosp getFinalCheck() {
		return finalCheck;
	}

	public void setFinalCheck(ZyyResidencyHandbookHosp finalCheck) {
		this.finalCheck = finalCheck;
	}

	public Date getCycleCommitDate() {
		return cycleCommitDate;
	}

	public void setCycleCommitDate(Date cycleCommitDate) {
		this.cycleCommitDate = cycleCommitDate;
	}

	public Date getOtherCommitDate() {
		return otherCommitDate;
	}

	public void setOtherCommitDate(Date otherCommitDate) {
		this.otherCommitDate = otherCommitDate;
	}

            public Boolean getIsPro() {
                return isPro;
            }

            public void setIsPro(Boolean isPro) {
                this.isPro = isPro;
            }
        
           

}