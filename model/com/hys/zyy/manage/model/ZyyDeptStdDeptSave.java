package com.hys.zyy.manage.model;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：张伟清 2012-04-03
 * 
 * 描述：医院科室标准科室 保存数据
 * 
 * 说明:
 */
public class ZyyDeptStdDeptSave extends ZyyBaseObject {

	private static final long serialVersionUID = -8841948335319602069L;

	/**
	 * 标准科室ID(保存数据)
	 */
	private Long deptStdIdSave;

	/**
	 * 医院科室ID(保存数据)
	 */
	private Long zyyDeptIdSave;

	/**
	 * 医院基地ID(保存数据)
	 */
	private Long zyyBaseIdSave;

	public Long getDeptStdIdSave() {
		return deptStdIdSave;
	}

	public void setDeptStdIdSave(Long deptStdIdSave) {
		this.deptStdIdSave = deptStdIdSave;
	}

	public Long getZyyDeptIdSave() {
		return zyyDeptIdSave;
	}

	public void setZyyDeptIdSave(Long zyyDeptIdSave) {
		this.zyyDeptIdSave = zyyDeptIdSave;
	}

	public Long getZyyBaseIdSave() {
		return zyyBaseIdSave;
	}

	public void setZyyBaseIdSave(Long zyyBaseIdSave) {
		this.zyyBaseIdSave = zyyBaseIdSave;
	}
}