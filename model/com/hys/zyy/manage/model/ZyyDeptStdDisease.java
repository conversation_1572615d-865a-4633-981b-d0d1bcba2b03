package com.hys.zyy.manage.model;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import ch.lambdaj.Lambda;

import com.hys.zyy.manage.util.annotation.Column;
import com.hys.zyy.manage.util.annotation.Id;
import com.hys.zyy.manage.util.annotation.Table;

@Table("ZYY_DEPT_STD_DISEASE")
public class ZyyDeptStdDisease extends ZyyBaseObject implements java.io.Serializable {
	private static final long serialVersionUID = 5454155825314635342L;
	
	//alias
	public static final String TABLE_ALIAS = "ZyyDeptStdDisease";
	public static final String ALIAS_ID = "id";
	public static final String ALIAS_ZYY_ORG_ID = "zyyOrgId";
	public static final String ALIAS_DEPT_STD_ID = "deptStdId";
	public static final String ALIAS_TITLE_DESC = "titleDesc";
	public static final String ALIAS_DISEASE_TYPE = "diseaseType";		// 1 -疾病 2 -技能 3 -其他
	public static final String ALIAS_REQUIRE_TYPE = "requireType";		// 1 -基本要求 2 -较高要求
	public static final String ALIAS_STATUS = "status";			// 1 -正常 -1 -删除
	
	public static final ZyyDeptStdDisease NULL = new ZyyDeptStdDisease();
	
	@Id("ZYY_DEPT_STD_DISEASE_SEQ.nextval")
	@Column("id")
	private java.lang.Long id;
	@Column("ZYY_ORG_ID")
	private java.lang.Long zyyOrgId;
	@Column("DEPT_STD_ID")
	private java.lang.Long deptStdId;
	@Column("TITLE_DESC")
	private java.lang.String titleDesc;
	@Column("DISEASE_TYPE")
	private Integer diseaseType;
	@Column("REQUIRE_TYPE")
	private Integer requireType;
	@Column("STATUS")
	private Integer status = 1;
	@Column("MEMO")
	private String memo;
	
	private List<ZyyDeptStdDiseaseDetail> details = new ArrayList<ZyyDeptStdDiseaseDetail>();
	
	public ZyyDeptStdDisease(){
	}

	public ZyyDeptStdDisease(
		java.lang.Long id
	){
		this.id = id;
	}

	public void setId(java.lang.Long value) {
		this.id = value;
	}
	
	public java.lang.Long getId() {
		return this.id;
	}
	public void setZyyOrgId(java.lang.Long value) {
		this.zyyOrgId = value;
	}
	
	public java.lang.Long getZyyOrgId() {
		return this.zyyOrgId;
	}
	public void setDeptStdId(java.lang.Long value) {
		this.deptStdId = value;
	}
	
	public java.lang.Long getDeptStdId() {
		return this.deptStdId;
	}
	public void setTitleDesc(java.lang.String value) {
		this.titleDesc = value;
	}
	
	public java.lang.String getTitleDesc() {
		return this.titleDesc;
	}
	public void setDiseaseType(Integer value) {
		this.diseaseType = value;
	}
	
	public Integer getDiseaseType() {
		return this.diseaseType;
	}
	public void setRequireType(Integer value) {
		this.requireType = value;
	}
	
	public Integer getRequireType() {
		return this.requireType;
	}
	public void setStatus(Integer value) {
		this.status = value;
	}
	
	public Integer getStatus() {
		return this.status;
	}
	
	private Set zyyDeptStdDiseaseDetails = new HashSet(0);
	public void setZyyDeptStdDiseaseDetails(Set<ZyyDeptStdDiseaseDetail> zyyDeptStdDiseaseDetail){
		this.zyyDeptStdDiseaseDetails = zyyDeptStdDiseaseDetail;
	}
	
	public Set<ZyyDeptStdDiseaseDetail> getZyyDeptStdDiseaseDetails() {
		return zyyDeptStdDiseaseDetails;
	}
	
	private ZyyDeptStd zyyDeptStd;
	
	public void setZyyDeptStd(ZyyDeptStd zyyDeptStd){
		this.zyyDeptStd = zyyDeptStd;
	}
	
	public ZyyDeptStd getZyyDeptStd() {
		return zyyDeptStd;
	}
	
	private ZyyOrg zyyOrg;
	
	public void setZyyOrg(ZyyOrg zyyOrg){
		this.zyyOrg = zyyOrg;
	}
	
	public ZyyOrg getZyyOrg() {
		return zyyOrg;
	}

	public String toString() {
		return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
			.append("Id",getId())
			.append("ZyyOrgId",getZyyOrgId())
			.append("DeptStdId",getDeptStdId())
			.append("TitleDesc",getTitleDesc())
			.append("DiseaseType",getDiseaseType())
			.append("RequireType",getRequireType())
			.append("Status",getStatus())
			.toString();
	}

	public List<ZyyDeptStdDiseaseDetail> getDetails() {
		return details;
	}

	public void setDetails(List<ZyyDeptStdDiseaseDetail> details) {
		this.details = details;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result
				+ ((titleDesc == null) ? 0 : titleDesc.hashCode());
		if(details != null)
			for(ZyyDeptStdDiseaseDetail detail : details)
				result = prime * result + detail.hashCode();
		
		return result;
	}

	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}
	
	public List<ZyyTrainDisease> getDiseases() {
		return Lambda.extract(this.getDetails(), Lambda.on(ZyyDeptStdDiseaseDetail.class).getDisease());
	}
	

	

	private List<ZyyTrainDisease> realyDiseases;
	
	public void setRealyDiseases(List<ZyyTrainDisease> realyDiseases) {
		this.realyDiseases = realyDiseases;
	}

	public List<ZyyTrainDisease> getRealyDiseases() {
		return realyDiseases;
	}

	
}

