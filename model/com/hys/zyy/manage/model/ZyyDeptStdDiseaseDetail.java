package com.hys.zyy.manage.model;

import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import com.hys.zyy.manage.util.annotation.Column;
import com.hys.zyy.manage.util.annotation.Id;
import com.hys.zyy.manage.util.annotation.Table;

@Table("ZYY_DEPT_STD_DISEASE_DETAIL")
public class ZyyDeptStdDiseaseDetail extends ZyyBaseObject implements java.io.Serializable {
	private static final long serialVersionUID = 5454155825314635342L;
	
	//alias
	public static final String TABLE_ALIAS = "ZyyDeptStdDiseaseDetail";
	public static final String ALIAS_ID = "id";
	public static final String ALIAS_DEPT_STD_DISEASE_ID = "deptStdDiseaseId";
	public static final String ALIAS_DISEASE_ID = "diseaseId";
	public static final String ALIAS_REQUIRE_SYMBOL = "requireSymbol";
	public static final String ALIAS_REQUIRE_AMOUNT = "requireAmount";
	
	@Id("ZYY_DEPT_STD_DISEASE_DTL_SEQ.nextval")
	@Column("id")
	private java.lang.Long id;
	@Column("DEPT_STD_DISEASE_ID")
	private java.lang.Long deptStdDiseaseId;
	@Column("DISEASE_ID")
	private java.lang.Long diseaseId;
	@Column("REQUIRE_SYMBOL")
	private java.lang.String requireSymbol;
	@Column("REQUIRE_AMOUNT")
	private java.lang.String requireAmount;
	
	private String diseaseName;
	
	private ZyyTrainDisease disease = new ZyyTrainDisease();

	public ZyyDeptStdDiseaseDetail(){
	}

	public ZyyDeptStdDiseaseDetail(
		java.lang.Long id
	){
		this.id = id;
	}

	public void setId(java.lang.Long value) {
		this.id = value;
	}
	
	public java.lang.Long getId() {
		return this.id;
	}
	public void setDeptStdDiseaseId(java.lang.Long value) {
		this.deptStdDiseaseId = value;
	}
	
	public java.lang.Long getDeptStdDiseaseId() {
		return this.deptStdDiseaseId;
	}
	public void setDiseaseId(java.lang.Long value) {
		this.diseaseId = value;
	}
	
	public java.lang.Long getDiseaseId() {
		return this.diseaseId;
	}
	public void setRequireSymbol(java.lang.String value) {
		this.requireSymbol = value;
	}
	
	public java.lang.String getRequireSymbol() {
		return this.requireSymbol;
	}
	public void setRequireAmount(java.lang.String value) {
		this.requireAmount = value;
	}
	
	public java.lang.String getRequireAmount() {
		return this.requireAmount;
	}
	
	private ZyyDeptStdDisease zyyDeptStdDisease;
	
	public void setZyyDeptStdDisease(ZyyDeptStdDisease zyyDeptStdDisease){
		this.zyyDeptStdDisease = zyyDeptStdDisease;
	}
	
	public ZyyDeptStdDisease getZyyDeptStdDisease() {
		return zyyDeptStdDisease;
	}
	
	private ZyyTrainDisease zyyTrainDisease;
	
	public void setZyyTrainDisease(ZyyTrainDisease zyyTrainDisease){
		this.zyyTrainDisease = zyyTrainDisease;
	}
	
	public ZyyTrainDisease getZyyTrainDisease() {
		return zyyTrainDisease;
	}

	public String toString() {
		return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
			.append("Id",getId())
			.append("DeptStdDiseaseId",getDeptStdDiseaseId())
			.append("DiseaseId",getDiseaseId())
			.append("RequireSymbol",getRequireSymbol())
			.append("RequireAmount",getRequireAmount())
			.toString();
	}
	
	public ZyyTrainDisease getDisease() {
		return disease;
	}

	public void setDisease(ZyyTrainDisease disease) {
		this.disease = disease;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result
				+ ((diseaseId == null) ? 0 : diseaseId.hashCode());
		result = prime * result
				+ ((requireAmount == null) ? 0 : requireAmount.hashCode());
		result = prime * result
				+ ((requireSymbol == null) ? 0 : requireSymbol.hashCode());
		return result;
	}

	public void setDiseaseName(String diseaseName) {
		this.diseaseName = diseaseName;
	}

	public String getDiseaseName() {
		return diseaseName;
	}

}

