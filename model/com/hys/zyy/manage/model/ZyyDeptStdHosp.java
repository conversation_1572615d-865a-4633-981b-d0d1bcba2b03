package com.hys.zyy.manage.model;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import com.hys.zyy.manage.util.annotation.Column;
import com.hys.zyy.manage.util.annotation.Id;
import com.hys.zyy.manage.util.annotation.Table;

@Table("zyy_dept_std_hosp")
public class ZyyDeptStdHosp extends ZyyBaseObject implements java.io.Serializable {
	private static final long serialVersionUID = 5454155825314635342L;
	
	//alias
	public static final String TABLE_ALIAS = "ZyyDeptStdHosp";
	public static final String ALIAS_ID = "id";
	public static final String ALIAS_DEPT_STD_ID = "deptStdId";
	public static final String ALIAS_HOSPITAL_ID = "hospitalId";
	public static final String ALIAS_IS_REQUIRED_HOSP = "isRequiredHosp";		// 1 -必选 0 -可选
	public static final String ALIAS_CYCLE_REMARK_HOSP = "cycleRemarkHosp";
	public static final String ALIAS_CYCLE_PURPOSE_HOSP = "cyclePurposeHosp";
	public static final String ALIAS_BASIC_REQUIREMENT_DESC_HOSP = "basicRequirementDescHosp";
	public static final String ALIAS_HIGH_REQUIREMENT_DESC_HOSP = "highRequirementDescHosp";
	public static final String ALIAS_OTHER_REQUIREMENT_DESC_HOSP = "otherRequirementDescHosp";
	public static final String ALIAS_REFERENCE_BOOK_HOSP = "referenceBookHosp";
	public static final String ALIAS_CYCLE_TIME_HOSP = "cycleTimeHosp";
	
	@Id("zyy_dept_std_hosp_seq.nextval")
	@Column("ID")
	private java.lang.Long id;
	@Column("DEPT_STD_ID")
	private java.lang.Long deptStdId;
	@Column("HOSPITAL_ID")
	private java.lang.Long hospitalId;
	@Column("IS_REQUIRED_HOSP")
	private java.lang.Boolean isRequiredHosp;
	@Column("CYCLE_REMARK_HOSP")
	private java.lang.String cycleRemarkHosp;
	@Column("CYCLE_PURPOSE_HOSP")
	private java.lang.String cyclePurposeHosp;
	@Column("BASIC_REQUIREMENT_DESC_HOSP")
	private java.lang.String basicRequirementDescHosp;
	@Column("HIGH_REQUIREMENT_DESC_HOSP")
	private java.lang.String highRequirementDescHosp;
	@Column("OTHER_REQUIREMENT_DESC_HOSP")
	private java.lang.String otherRequirementDescHosp;
	@Column("REFERENCE_BOOK_HOSP")
	private java.lang.String referenceBookHosp;
	@Column("CYCLE_TIME_HOSP")
	private java.lang.String cycleTimeHosp;

	public void read(ZyyDeptStd entity) {
		if(entity.getIsRequired() != null)
			this.isRequiredHosp = entity.getIsRequired();
		if(entity.getCycleRemark() != null)
			this.cycleRemarkHosp = entity.getCycleRemark();
		if(entity.getCyclePurpose() != null)
			this.cyclePurposeHosp = entity.getCyclePurpose();
		if(entity.getBasicRequirementDesc() != null)
			this.basicRequirementDescHosp = entity.getBasicRequirementDesc();
		if(entity.getHighRequirementDesc() != null)
			this.highRequirementDescHosp = entity.getHighRequirementDesc();
		if(entity.getOtherRequirementDesc() != null)
			this.otherRequirementDescHosp = entity.getOtherRequirementDesc();
		if(entity.getOtherRequirementDesc() != null)
			this.referenceBookHosp = entity.getOtherRequirementDesc();
		if(entity.getReferenceBook() != null)
			this.referenceBookHosp = entity.getReferenceBook();
		if(entity.getCycleTime() != null)
			this.cycleTimeHosp = entity.getCycleTime();
	}
	
	public ZyyDeptStdHosp(){
	}

	public ZyyDeptStdHosp(
		java.lang.Long id
	){
		this.id = id;
	}

	public void setId(java.lang.Long value) {
		this.id = value;
	}
	
	public java.lang.Long getId() {
		return this.id;
	}
	public void setDeptStdId(java.lang.Long value) {
		this.deptStdId = value;
	}
	
	public java.lang.Long getDeptStdId() {
		return this.deptStdId;
	}
	public void setHospitalId(java.lang.Long value) {
		this.hospitalId = value;
	}
	
	public java.lang.Long getHospitalId() {
		return this.hospitalId;
	}
	public void setIsRequiredHosp(java.lang.Boolean value) {
		this.isRequiredHosp = value;
	}
	
	public java.lang.Boolean getIsRequiredHosp() {
		return this.isRequiredHosp;
	}
	public void setCycleRemarkHosp(java.lang.String value) {
		this.cycleRemarkHosp = value;
	}
	
	public java.lang.String getCycleRemarkHosp() {
		return this.cycleRemarkHosp;
	}
	public void setCyclePurposeHosp(java.lang.String value) {
		this.cyclePurposeHosp = value;
	}
	
	public java.lang.String getCyclePurposeHosp() {
		return this.cyclePurposeHosp;
	}
	public void setBasicRequirementDescHosp(java.lang.String value) {
		this.basicRequirementDescHosp = value;
	}
	
	public java.lang.String getBasicRequirementDescHosp() {
		return this.basicRequirementDescHosp;
	}
	public void setHighRequirementDescHosp(java.lang.String value) {
		this.highRequirementDescHosp = value;
	}
	
	public java.lang.String getHighRequirementDescHosp() {
		return this.highRequirementDescHosp;
	}
	public void setOtherRequirementDescHosp(java.lang.String value) {
		this.otherRequirementDescHosp = value;
	}
	
	public java.lang.String getOtherRequirementDescHosp() {
		return this.otherRequirementDescHosp;
	}
	public void setReferenceBookHosp(java.lang.String value) {
		this.referenceBookHosp = value;
	}
	
	public java.lang.String getReferenceBookHosp() {
		return this.referenceBookHosp;
	}
	public void setCycleTimeHosp(java.lang.String value) {
		this.cycleTimeHosp = value;
	}
	
	public java.lang.String getCycleTimeHosp() {
		return this.cycleTimeHosp;
	}
	
	private ZyyDeptStd zyyDeptStd;
	
	public void setZyyDeptStd(ZyyDeptStd zyyDeptStd){
		this.zyyDeptStd = zyyDeptStd;
	}
	
	public ZyyDeptStd getZyyDeptStd() {
		return zyyDeptStd;
	}
	
	private ZyyOrg zyyOrg;
	
	public void setZyyOrg(ZyyOrg zyyOrg){
		this.zyyOrg = zyyOrg;
	}
	
	public ZyyOrg getZyyOrg() {
		return zyyOrg;
	}

	public String toString() {
		return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
			.append("Id",getId())
			.append("DeptStdId",getDeptStdId())
			.append("HospitalId",getHospitalId())
			.append("IsRequiredHosp",getIsRequiredHosp())
			.append("CycleRemarkHosp",getCycleRemarkHosp())
			.append("CyclePurposeHosp",getCyclePurposeHosp())
			.append("BasicRequirementDescHosp",getBasicRequirementDescHosp())
			.append("HighRequirementDescHosp",getHighRequirementDescHosp())
			.append("OtherRequirementDescHosp",getOtherRequirementDescHosp())
			.append("ReferenceBookHosp",getReferenceBookHosp())
			.append("CycleTimeHosp",getCycleTimeHosp())
			.toString();
	}
	
	public int hashCode() {
		return new HashCodeBuilder()
			.append(getId())
			.toHashCode();
	}
	
	public boolean equals(Object obj) {
		if(obj instanceof ZyyDeptStdHosp == false) return false;
		if(this == obj) return true;
		ZyyDeptStdHosp other = (ZyyDeptStdHosp)obj;
		return new EqualsBuilder()
			.append(getId(),other.getId())
			.isEquals();
	}

}

