package com.hys.zyy.manage.model;

import java.util.List;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：张伟清 2012-05-03
 * 
 * 描述：住院医师轮转标准科室 vo
 * 
 * 说明:
 */
public class ZyyDeptStdVO extends ZyyDeptStd {

	private static final long serialVersionUID = -7299201926381674511L;

	/**
	 * 轮转时间
	 */
	private String cycleTime ;
	
	
	/**
	 * 轮转科室ID
	 */
	private Long deptId ;
	
	private Long baseHdChkId; //学科审核时的 审核记录表的id  zyy_cycle_resi_hd_check的id
	private Integer baseCycleStatus;//学科审核状态
	private Long hospHdChkId; //医院审核时的 审核记录表的id  zyy_cycle_resi_hd_check的id
	private Integer hospCycleStatus;//医院审核状态
	private Integer rowspan=1;//登记手册修改用  2017-5-12

	public Integer getRowspan() {
		return rowspan;
	}

	public void setRowspan(Integer rowspan) {
		this.rowspan = rowspan;
	}

	private List<ZyyUserCheckManual> zyyusercheckList;
	
	private ZyyCheckManual zyyCheckManual;

	public Long getBaseHdChkId() {
		return baseHdChkId;
	}

	public void setBaseHdChkId(Long baseHdChkId) {
		this.baseHdChkId = baseHdChkId;
	}

	public Integer getBaseCycleStatus() {
		return baseCycleStatus;
	}

	public void setBaseCycleStatus(Integer baseCycleStatus) {
		this.baseCycleStatus = baseCycleStatus;
	}

	public Long getHospHdChkId() {
		return hospHdChkId;
	}

	public void setHospHdChkId(Long hospHdChkId) {
		this.hospHdChkId = hospHdChkId;
	}

	public Integer getHospCycleStatus() {
		return hospCycleStatus;
	}

	public void setHospCycleStatus(Integer hospCycleStatus) {
		this.hospCycleStatus = hospCycleStatus;
	}

	public List<ZyyUserCheckManual> getZyyusercheckList() {
		return zyyusercheckList;
	}

	public void setZyyusercheckList(List<ZyyUserCheckManual> zyyusercheckList) {
		this.zyyusercheckList = zyyusercheckList;
	}

	public String getCycleTime() {
		return cycleTime;
	}

	public void setCycleTime(String cycleTime) {
		this.cycleTime = cycleTime;
	}

	public Long getDeptId() {
		return deptId;
	}

	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}

	public ZyyCheckManual getZyyCheckManual() {
		return zyyCheckManual;
	}

	public void setZyyCheckManual(ZyyCheckManual zyyCheckManual) {
		this.zyyCheckManual = zyyCheckManual;
	}
}