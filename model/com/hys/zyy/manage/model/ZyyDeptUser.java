package com.hys.zyy.manage.model;

import java.io.Serializable;

public class ZyyDeptUser implements Serializable {
	private static final long serialVersionUID = 1L;
	private Long zyyDeptId;
	private Long zyyUserId;
	private Integer status;

	public ZyyDeptUser() {
		super();
	}

	public ZyyDeptUser(Long zyyDeptId, Long zyyUserId, Integer status) {
		super();
		this.zyyDeptId = zyyDeptId;
		this.zyyUserId = zyyUserId;
		this.status = status;
	}

	public Long getZyyDeptId() {
		return zyyDeptId;
	}

	public void setZyyDeptId(Long zyyDeptId) {
		this.zyyDeptId = zyyDeptId;
	}

	public Long getZyyUserId() {
		return zyyUserId;
	}

	public void setZyyUserId(Long zyyUserId) {
		this.zyyUserId = zyyUserId;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

}
