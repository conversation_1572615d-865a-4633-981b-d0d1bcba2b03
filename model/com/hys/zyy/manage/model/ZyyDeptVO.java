package com.hys.zyy.manage.model;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.hys.security.model.Resource;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：张伟清 2012-01-12
 * 
 * 描述：
 * 
 * 说明:
 */

public class ZyyDeptVO extends ZyyDept {

	private static final long serialVersionUID = -7991671780676531554L;

	/**
	 * 轮转表科室ID
	 */
	private Long tableDeptId;
	
	/**
	 * 轮转表ID
	 */
	private Long cycleTableId ;

	/**
	 * 科室延迟天数
	 */
	private Integer delayDays;

	/**
	 * 最高人数限制(最小)
	 */
	private Integer deptMaxNumber1;

	/**
	 * 最高人数限制(最大)
	 */
	private Integer deptMaxNumber2;

	/**
	 * 基地ID
	 */
	private Long baseId ;
	
	/**
	 * 基地名称
	 */
	private String baseName;

	/**
	 * 基地别名
	 */
	private String aliasName;

	/**
	 * 标准科室ID
	 */
	private Long deptStdId;

	/**
	 * 标准科室名称
	 */
	private String deptStdName;

	/**
	 * 基地列表
	 */
	private List<ZyyBase> baseList;

	/**
	 * 科室轮转信息
	 */
	private List<ZyyCycleTableResiCycleVO> cycleList;

	/**
	 * 科室轮转信息 map
	 */
	private Map<Object, List<ZyyCycleTableResiCycleVO>> cycleMap;
	
	/**
	 * 科室轮转信息-轮转历史记录 map
	 */
	private Map<Object, List<ZyyResidencyCycle>> historyCycleMap;

	/**
	 * 标准科室列表
	 */
	private List<String> deptNameList;
	
	/**
	 * 迟到用户
	 */
	private List<ZyyResidencyAttendanceVO> lateUserList;
	
	/**
	 * 早退用户
	 */
	private List<ZyyResidencyAttendanceVO> leaveEarlyUserList;
	
	/**
	 * 旷工用户
	 */
	private List<ZyyResidencyAttendanceVO> absenteeismUserList;
	
	/**
	 * 请假用户
	 */
	private List<ZyyResidencyAttendanceVO> leaveUserList;
	
	/**
	 * 考勤最后操作日期
	 */
	private Date attendanceLastUpdateDate;
	
	/**
	 * 全勤最后操作日期
	 */
	private Date fillAttendanceLastUpdateDate;

	/**
	 * 角色id
	 */
	private Long roleId;

	/**
	 * 角色名称
	 */
	private String roleName;
	
	/**
	 * 学制
	 */
	private Integer educationSystem ;
	
	/**
	 * 轮转时间
	 */
	private Integer cycleTime ;

	/**
	 * 资源列表
	 */
	private List<Resource> resourceList;
	
	/**
	 * 优先轮转科室表 ID
	 */
	private Long zyyDeptCyclePriorityId;
	
	/**
	 * 轮转指定带教学员    //yjk 学员在此轮转时间段内的带教老师的集合(zyy_cycle_teacher表的集合)
	 */
	private List<ZyyCycleTeacherVO> cycleTeacherVOList;
	
	/**
	 * 下级科室集合
	 */
	private List<ZyyDeptVO> subDeptList;
	
	/**
	 * 总共行数
	 */
	private int sumCount;
	
	/**
	 * 总共级数
	 */
	private int sumLevel;
	
	/**
	 * 实际轮转科室下的标准轮转科室
	 */
	private List<ZyyDeptStdVO> deptStdList;//yjk 标准科室的集合(登记手册中用的是登记手册名称)
	
	/**
	 * 教学管理统计：活动类型列表
	 */
	private List<ZyyActivityTypeVO> actibityTypeList;
	
	private String resiName;
	
	private Integer cycleStatus;
	/**
	 * 正在轮转的人数
	 */
	private Integer cycleNumber;
	
	//----
	/**
	 * 用户评价表的id
	 */
	private Long userEvaluateId;
	/**
	 * 评价表是否可修改   1-可评价  2-未在评价时间段内,不可评价
	 */
	private Integer isEvaluate;
	/**
	 * cycleTimeLine表中的id
	 */
	private Long cycleTimelineId;
	//----
	/*yjk 登记手册修改中用的字段 */
	private Long deptHdChkId;//科室审核记录表的id
	private Integer deptCycleStatus;//科室审核状态
	private Long otherChkId;//其它部分审核主表的id
	
	private String otherCommitStatus;//其它部分提交状态
	private Integer teacherOtherStatus;//其它部分带教审核
	private Integer deptOtherStatus;//其它部分科室审核
	private Integer baseOtherStatus;//其它部分学科审核
	private Integer hospOtherStatus;//其它部分医院审核
	private Integer finalCheckStatus;  //终审状态
	private Integer ochkCommitTimes;//其它部分第几次提交
	private Date ochkCommitDate;//其它部分提交时间
	private Date residencyLastDate;//手册最后提交时间
	/*
	 * 科室本月考勤是否已上报
	 */
	private boolean finishReport;
	/*
	 * 科室本月考勤上报时间
	 */
	private Date reportTime;
	/*
	 * 手册标准ID
	 */
	private Long manualStdId;

	public Long getManualStdId() {
		return manualStdId;
	}

	public void setManualStdId(Long manualStdId) {
		this.manualStdId = manualStdId;
	}

	public boolean isFinishReport() {
		return finishReport;
	}

	public void setFinishReport(boolean finishReport) {
		this.finishReport = finishReport;
	}

	public Date getReportTime() {
		return reportTime;
	}

	public void setReportTime(Date reportTime) {
		this.reportTime = reportTime;
	}

	public Date getResidencyLastDate() {
		return residencyLastDate;
	}

	public void setResidencyLastDate(Date residencyLastDate) {
		this.residencyLastDate = residencyLastDate;
	}

	//轮转后多少天 到 多少天
	private Integer fillinSdate;
	
	private Integer fillinEdate;
	
	public Integer getOchkCommitTimes() {
		return ochkCommitTimes;
	}

	public void setOchkCommitTimes(Integer ochkCommitTimes) {
		this.ochkCommitTimes = ochkCommitTimes;
	}

	public Date getOchkCommitDate() {
		return ochkCommitDate;
	}

	public void setOchkCommitDate(Date ochkCommitDate) {
		this.ochkCommitDate = ochkCommitDate;
	}

	public Long getDeptHdChkId() {
		return deptHdChkId;
	}

	public void setDeptHdChkId(Long deptHdChkId) {
		this.deptHdChkId = deptHdChkId;
	}

	public Long getOtherChkId() {
		return otherChkId;
	}

	public void setOtherChkId(Long otherChkId) {
		this.otherChkId = otherChkId;
	}

	public Integer getTeacherOtherStatus() {
		return teacherOtherStatus;
	}

	public void setTeacherOtherStatus(Integer teacherOtherStatus) {
		this.teacherOtherStatus = teacherOtherStatus;
	}

	public Integer getDeptOtherStatus() {
		return deptOtherStatus;
	}

	public void setDeptOtherStatus(Integer deptOtherStatus) {
		this.deptOtherStatus = deptOtherStatus;
	}

	public Integer getBaseOtherStatus() {
		return baseOtherStatus;
	}

	public void setBaseOtherStatus(Integer baseOtherStatus) {
		this.baseOtherStatus = baseOtherStatus;
	}

	public Integer getHospOtherStatus() {
		return hospOtherStatus;
	}

	public void setHospOtherStatus(Integer hospOtherStatus) {
		this.hospOtherStatus = hospOtherStatus;
	}

	public Integer getFinalCheckStatus() {
		return finalCheckStatus;
	}

	public void setFinalCheckStatus(Integer finalCheckStatus) {
		this.finalCheckStatus = finalCheckStatus;
	}

	public String getOtherCommitStatus() {
		return otherCommitStatus;
	}

	public void setOtherCommitStatus(String otherCommitStatus) {
		this.otherCommitStatus = otherCommitStatus;
	}

	public Integer getDeptCycleStatus() {
		return deptCycleStatus;
	}

	public void setDeptCycleStatus(Integer deptCycleStatus) {
		this.deptCycleStatus = deptCycleStatus;
	}

	public Integer getCycleNumber() {
		return cycleNumber;
	}

	public String getResiName() {
		return resiName;
	}

	public void setResiName(String resiName) {
		this.resiName = resiName;
	}

	public Integer getCycleStatus() {
		return cycleStatus;
	}

	public void setCycleStatus(Integer cycleStatus) {
		this.cycleStatus = cycleStatus;
	}

	public Long getUserEvaluateId() {
		return userEvaluateId;
	}

	public void setUserEvaluateId(Long userEvaluateId) {
		this.userEvaluateId = userEvaluateId;
	}

	public Integer getIsEvaluate() {
		return isEvaluate;
	}

	public void setIsEvaluate(Integer isEvaluate) {
		this.isEvaluate = isEvaluate;
	}

	public Long getCycleTimelineId() {
		return cycleTimelineId;
	}

	public void setCycleTimelineId(Long cycleTimelineId) {
		this.cycleTimelineId = cycleTimelineId;
	}

	public void setCycleNumber(Integer cycleNumber) {
		this.cycleNumber = cycleNumber;
	}

	public List<ZyyActivityTypeVO> getActibityTypeList() {
		return actibityTypeList;
	}

	public void setActibityTypeList(List<ZyyActivityTypeVO> actibityTypeList) {
		this.actibityTypeList = actibityTypeList;
	}

	public int getSumCount() {
		return sumCount;
	}

	public void setSumCount(int sumCount) {
		this.sumCount = sumCount;
	}

	public int getSumLevel() {
		return sumLevel;
	}

	public void setSumLevel(int sumLevel) {
		this.sumLevel = sumLevel;
	}

	public List<ZyyDeptVO> getSubDeptList() {
		return subDeptList;
	}

	public void setSubDeptList(List<ZyyDeptVO> subDeptList) {
		this.subDeptList = subDeptList;
	}

	public List<ZyyCycleTeacherVO> getCycleTeacherVOList() {
		return cycleTeacherVOList;
	}

	public void setCycleTeacherVOList(List<ZyyCycleTeacherVO> cycleTeacherVOList) {
		this.cycleTeacherVOList = cycleTeacherVOList;
	}

	public List<Resource> getResourceList() {
		return resourceList;
	}

	public void setResourceList(List<Resource> resourceList) {
		this.resourceList = resourceList;
	}

	public Long getRoleId() {
		return roleId;
	}

	public void setRoleId(Long roleId) {
		this.roleId = roleId;
	}

	public String getRoleName() {
		return roleName;
	}

	public void setRoleName(String roleName) {
		this.roleName = roleName;
	}

	public Long getTableDeptId() {
		return tableDeptId;
	}

	public void setTableDeptId(Long tableDeptId) {
		this.tableDeptId = tableDeptId;
	}

	public Integer getDelayDays() {
		return delayDays;
	}

	public void setDelayDays(Integer delayDays) {
		this.delayDays = delayDays;
	}

	public Integer getDeptMaxNumber1() {
		return deptMaxNumber1;
	}

	public void setDeptMaxNumber1(Integer deptMaxNumber1) {
		this.deptMaxNumber1 = deptMaxNumber1;
	}

	public Integer getDeptMaxNumber2() {
		return deptMaxNumber2;
	}

	public void setDeptMaxNumber2(Integer deptMaxNumber2) {
		this.deptMaxNumber2 = deptMaxNumber2;
	}

	public List<ZyyBase> getBaseList() {
		return baseList;
	}

	public void setBaseList(List<ZyyBase> baseList) {
		this.baseList = baseList;
	}

	public String getBaseName() {
		return baseName;
	}

	public void setBaseName(String baseName) {
		this.baseName = baseName;
	}

	public String getAliasName() {
		return aliasName;
	}

	public void setAliasName(String aliasName) {
		this.aliasName = aliasName;
	}

	public List<ZyyCycleTableResiCycleVO> getCycleList() {
		return cycleList;
	}

	public void setCycleList(List<ZyyCycleTableResiCycleVO> cycleList) {
		this.cycleList = cycleList;
	}

	public Map<Object, List<ZyyCycleTableResiCycleVO>> getCycleMap() {
		return cycleMap;
	}

	public void setCycleMap(Map<Object, List<ZyyCycleTableResiCycleVO>> cycleMap) {
		this.cycleMap = cycleMap;
	}

	public String getDeptStdName() {
		return deptStdName;
	}

	public void setDeptStdName(String deptStdName) {
		this.deptStdName = deptStdName;
	}

	public List<String> getDeptNameList() {
		return deptNameList;
	}

	public void setDeptNameList(List<String> deptNameList) {
		this.deptNameList = deptNameList;
	}

	public Long getDeptStdId() {
		return deptStdId;
	}

	public void setDeptStdId(Long deptStdId) {
		this.deptStdId = deptStdId;
	}

	public Integer getEducationSystem() {
		return educationSystem;
	}

	public void setEducationSystem(Integer educationSystem) {
		this.educationSystem = educationSystem;
	}

	public Integer getCycleTime() {
		return cycleTime;
	}

	public void setCycleTime(Integer cycleTime) {
		this.cycleTime = cycleTime;
	}

	public Long getBaseId() {
		return baseId;
	}

	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}

	public Long getZyyDeptCyclePriorityId() {
		return zyyDeptCyclePriorityId;
	}

	public void setZyyDeptCyclePriorityId(Long zyyDeptCyclePriorityId) {
		this.zyyDeptCyclePriorityId = zyyDeptCyclePriorityId;
	}

	public Long getCycleTableId() {
		return cycleTableId;
	}

	public void setCycleTableId(Long cycleTableId) {
		this.cycleTableId = cycleTableId;
	}

	public Map<Object, List<ZyyResidencyCycle>> getHistoryCycleMap() {
		return historyCycleMap;
	}

	public void setHistoryCycleMap(
			Map<Object, List<ZyyResidencyCycle>> historyCycleMap) {
		this.historyCycleMap = historyCycleMap;
	}

	public List<ZyyResidencyAttendanceVO> getLateUserList() {
		return lateUserList;
	}

	public void setLateUserList(List<ZyyResidencyAttendanceVO> lateUserList) {
		this.lateUserList = lateUserList;
	}

	public List<ZyyResidencyAttendanceVO> getLeaveEarlyUserList() {
		return leaveEarlyUserList;
	}

	public void setLeaveEarlyUserList(List<ZyyResidencyAttendanceVO> leaveEarlyUserList) {
		this.leaveEarlyUserList = leaveEarlyUserList;
	}

	public List<ZyyResidencyAttendanceVO> getAbsenteeismUserList() {
		return absenteeismUserList;
	}

	public void setAbsenteeismUserList(List<ZyyResidencyAttendanceVO> absenteeismUserList) {
		this.absenteeismUserList = absenteeismUserList;
	}

	public List<ZyyResidencyAttendanceVO> getLeaveUserList() {
		return leaveUserList;
	}

	public void setLeaveUserList(List<ZyyResidencyAttendanceVO> leaveUserList) {
		this.leaveUserList = leaveUserList;
	}

	public Date getAttendanceLastUpdateDate() {
		return attendanceLastUpdateDate;
	}

	public void setAttendanceLastUpdateDate(Date attendanceLastUpdateDate) {
		this.attendanceLastUpdateDate = attendanceLastUpdateDate;
	}

	public Date getFillAttendanceLastUpdateDate() {
		return fillAttendanceLastUpdateDate;
	}

	public void setFillAttendanceLastUpdateDate(
			Date fillAttendanceLastUpdateDate) {
		this.fillAttendanceLastUpdateDate = fillAttendanceLastUpdateDate;
	}

	public List<ZyyDeptStdVO> getDeptStdList() {
		return deptStdList;
	}

	public void setDeptStdList(List<ZyyDeptStdVO> deptStdList) {
		this.deptStdList = deptStdList;
	}

	public Integer getFillinSdate() {
		return fillinSdate;
	}

	public void setFillinSdate(Integer fillinSdate) {
		this.fillinSdate = fillinSdate;
	}

	public Integer getFillinEdate() {
		return fillinEdate;
	}

	public void setFillinEdate(Integer fillinEdate) {
		this.fillinEdate = fillinEdate;
	}



	
	
	
}