package com.hys.zyy.manage.model;

import java.io.Serializable;

/**
 * 数据字典表
 */
public class ZyyDictionary implements Serializable{
	private static final long serialVersionUID = 1L;
	/*
	 * ID
	 */
	private Long id;
	/*
	 * 字典编码
	 */
	private String dictionaryCode;
	/*
	 * 字典名称
	 */
	private String dictionaryName;
	/*
	 * 字典备注
	 */
	private String dictionaryRemark;
	/*
	 * 字典排序
	 */
	private Integer sortIndex;
	/*
	 * 字典分类
	 */
	private String dictionaryType;
	/*
	 * 字典状态（-1：失效；1：启用
	 */
	private Integer dictionaryState;

	public ZyyDictionary() {
		super();
	}

	public ZyyDictionary(String dictionaryType) {
		super();
		this.dictionaryType = dictionaryType;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getDictionaryCode() {
		return dictionaryCode;
	}

	public void setDictionaryCode(String dictionaryCode) {
		this.dictionaryCode = dictionaryCode;
	}

	public String getDictionaryName() {
		return dictionaryName;
	}

	public void setDictionaryName(String dictionaryName) {
		this.dictionaryName = dictionaryName;
	}

	public String getDictionaryRemark() {
		return dictionaryRemark;
	}

	public void setDictionaryRemark(String dictionaryRemark) {
		this.dictionaryRemark = dictionaryRemark;
	}

	public Integer getSortIndex() {
		return sortIndex;
	}

	public void setSortIndex(Integer sortIndex) {
		this.sortIndex = sortIndex;
	}

	public String getDictionaryType() {
		return dictionaryType;
	}

	public void setDictionaryType(String dictionaryType) {
		this.dictionaryType = dictionaryType;
	}

	public Integer getDictionaryState() {
		return dictionaryState;
	}

	public void setDictionaryState(Integer dictionaryState) {
		this.dictionaryState = dictionaryState;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ZyyDictionary other = (ZyyDictionary) obj;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		return true;
	}

	@Override
	public String toString() {
		return "ZyyDictionary [id=" + id + ", dictionaryCode=" + dictionaryCode
				+ ", dictionaryName=" + dictionaryName + ", dictionaryRemark="
				+ dictionaryRemark + ", sortIndex=" + sortIndex
				+ ", dictionaryType=" + dictionaryType + ", dictionaryState="
				+ dictionaryState + "]";
	}

}