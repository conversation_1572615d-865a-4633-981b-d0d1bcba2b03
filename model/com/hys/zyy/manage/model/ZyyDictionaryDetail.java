package com.hys.zyy.manage.model;

import java.io.Serializable;
import java.util.Date;

import com.hys.zyy.manage.util.BaseModel;

/**
 * 数据字典明细
 * <AUTHOR>
 */
public class ZyyDictionaryDetail extends BaseModel implements Serializable {
	private static final long serialVersionUID = 1L;
	/*
	 * ID
	 */
	private Long id;
	/*
	 * 数据字典分类ID
	 */
	private Long zyyDictionaryTypeId;
	/*
	 * BDP字典值ID
	 */
	private Long bdpDicId;
	/*
	 * BDP字典值CODE
	 */
	private String bdpDicCode;
	/*
	 * 明细编码
	 */
	private String detailCode;
	/*
	 * 明细名称
	 */
	private String detailName;
	/*
	 * 明细级别
	 */
	private Integer detailLevel;
	/*
	 * 父级ID
	 */
	private Long parentId;
	/*
	 * 备注
	 */
	private String remark;
	/*
	 * 排序
	 */
	private Integer sortIndex;
	/*
	 * 状态（-1：失效；1：启用）
	 */
	private Integer state;
	private Date createTime;
	private Date updateTime;

	public ZyyDictionaryDetail() {
		super();
	}

	public ZyyDictionaryDetail(Long zyyDictionaryTypeId, String detailCode) {
		super();
		this.zyyDictionaryTypeId = zyyDictionaryTypeId;
		this.detailCode = detailCode;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getZyyDictionaryTypeId() {
		return zyyDictionaryTypeId;
	}

	public void setZyyDictionaryTypeId(Long zyyDictionaryTypeId) {
		this.zyyDictionaryTypeId = zyyDictionaryTypeId;
	}

	public Long getBdpDicId() {
		return bdpDicId;
	}

	public void setBdpDicId(Long bdpDicId) {
		this.bdpDicId = bdpDicId;
	}

	public String getBdpDicCode() {
		return bdpDicCode;
	}

	public void setBdpDicCode(String bdpDicCode) {
		this.bdpDicCode = bdpDicCode == null ? null : bdpDicCode.trim();
	}

	public String getDetailCode() {
		return detailCode;
	}

	public void setDetailCode(String detailCode) {
		this.detailCode = detailCode == null ? null : detailCode.trim();
	}

	public String getDetailName() {
		return detailName;
	}

	public void setDetailName(String detailName) {
		this.detailName = detailName == null ? null : detailName.trim();
	}

	public Integer getDetailLevel() {
		return detailLevel;
	}

	public void setDetailLevel(Integer detailLevel) {
		this.detailLevel = detailLevel;
	}

	public Long getParentId() {
		return parentId;
	}

	public void setParentId(Long parentId) {
		this.parentId = parentId;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark == null ? null : remark.trim();
	}

	public Integer getSortIndex() {
		return sortIndex;
	}

	public void setSortIndex(Integer sortIndex) {
		this.sortIndex = sortIndex;
	}

	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ZyyDictionaryDetail other = (ZyyDictionaryDetail) obj;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		return true;
	}

	@Override
	public String toString() {
		return "ZyyDictionaryDetail [id=" + id + ", zyyDictionaryTypeId=" + zyyDictionaryTypeId + ", bdpDicId="
				+ bdpDicId + ", bdpDicCode=" + bdpDicCode + ", detailCode=" + detailCode + ", detailName=" + detailName
				+ ", detailLevel=" + detailLevel + ", parentId=" + parentId + ", remark=" + remark + ", sortIndex="
				+ sortIndex + ", state=" + state + ", createTime=" + createTime + ", updateTime=" + updateTime + "]";
	}

}
