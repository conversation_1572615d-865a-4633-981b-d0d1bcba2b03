package com.hys.zyy.manage.model;

import java.util.Date;

import com.hys.zyy.manage.util.BaseModel;

/**
 * 限制登录配置
 * <AUTHOR>
 */
public class ZyyDisabledLoginConfig extends BaseModel {
	private static final long serialVersionUID = 1L;
	/*
	 * ID
	 */
	private String disabledLoginConfigId;
	/*
	 * 省厅ID
	 */
	private Long zyyUserProvinceId;
	/*
	 * 组织机构ID
	 */
	private Long zyyOrgId;
	/*
	 * 组织机构域名
	 */
	private String zyyOrgDomainName;
	/*
	 * 限制登录用户类型（,分割的字符串）
	 */
	private String disabledLoginType;
	/*
	 * 备注
	 */
	private String remark;
	/*
	 * 状态（-1：失效；1：启用）
	 */
	private Integer state;
	/*
	 * 已删除（0,否；1,是）
	 */
	private Integer isDelete;
	/*
	 * 创建者
	 */
	private Long creatorId;
	/*
	 * 创建时间
	 */
	private Date createTime;
	/*
	 * 最后一次修改者
	 */
	private Long lastModifier;
	/*
	 * 最后一次修改时间
	 */
	private Date lastModifyTime;

	public ZyyDisabledLoginConfig() {
	}

	public String getDisabledLoginConfigId() {
		return disabledLoginConfigId;
	}

	public void setDisabledLoginConfigId(String disabledLoginConfigId) {
		this.disabledLoginConfigId = disabledLoginConfigId;
	}

	public Long getZyyUserProvinceId() {
		return zyyUserProvinceId;
	}

	public void setZyyUserProvinceId(Long zyyUserProvinceId) {
		this.zyyUserProvinceId = zyyUserProvinceId;
	}

	public Long getZyyOrgId() {
		return zyyOrgId;
	}

	public void setZyyOrgId(Long zyyOrgId) {
		this.zyyOrgId = zyyOrgId;
	}

	public String getZyyOrgDomainName() {
		return zyyOrgDomainName;
	}

	public void setZyyOrgDomainName(String zyyOrgDomainName) {
		this.zyyOrgDomainName = zyyOrgDomainName;
	}

	public String getDisabledLoginType() {
		return disabledLoginType;
	}

	public void setDisabledLoginType(String disabledLoginType) {
		this.disabledLoginType = disabledLoginType;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	public Integer getIsDelete() {
		return isDelete;
	}

	public void setIsDelete(Integer isDelete) {
		this.isDelete = isDelete;
	}

	public Long getCreatorId() {
		return creatorId;
	}

	public void setCreatorId(Long creatorId) {
		this.creatorId = creatorId;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Long getLastModifier() {
		return lastModifier;
	}

	public void setLastModifier(Long lastModifier) {
		this.lastModifier = lastModifier;
	}

	public Date getLastModifyTime() {
		return lastModifyTime;
	}

	public void setLastModifyTime(Date lastModifyTime) {
		this.lastModifyTime = lastModifyTime;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime
				* result
				+ ((disabledLoginConfigId == null) ? 0 : disabledLoginConfigId
						.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ZyyDisabledLoginConfig other = (ZyyDisabledLoginConfig) obj;
		if (disabledLoginConfigId == null) {
			if (other.disabledLoginConfigId != null)
				return false;
		} else if (!disabledLoginConfigId.equals(other.disabledLoginConfigId))
			return false;
		return true;
	}

	@Override
	public String toString() {
		return "ZyyDisabledLoginConfig [disabledLoginConfigId="
				+ disabledLoginConfigId + ", zyyUserProvinceId="
				+ zyyUserProvinceId + ", zyyOrgId=" + zyyOrgId
				+ ", zyyOrgDomainName=" + zyyOrgDomainName
				+ ", disabledLoginType=" + disabledLoginType + ", remark="
				+ remark + ", state=" + state + ", isDelete=" + isDelete
				+ ", creatorId=" + creatorId + ", createTime=" + createTime
				+ ", lastModifier=" + lastModifier + ", lastModifyTime="
				+ lastModifyTime + "]";
	}

}
