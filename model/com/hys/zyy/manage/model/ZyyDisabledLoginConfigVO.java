package com.hys.zyy.manage.model;

public class ZyyDisabledLoginConfigVO extends ZyyDisabledLoginConfig {
	private static final long serialVersionUID = 1L;
	private String zyyOrgName; // 机构名称 
	private String disabledLoginTypeStr; // 限制登录类型 
	private String stateStr; // 状态 
	private Integer __htp__; // 机构类型
	private String creator; // 创建者
	private String createTimeStr; // 创建时间
	private String lastModifierStr; // 最后一次修改者
	private String lastModifyTimeStr; // 最后一次修改时间

	public String getZyyOrgName() {
		return zyyOrgName;
	}

	public void setZyyOrgName(String zyyOrgName) {
		this.zyyOrgName = zyyOrgName;
	}

	public String getDisabledLoginTypeStr() {
		return disabledLoginTypeStr;
	}

	public void setDisabledLoginTypeStr(String disabledLoginTypeStr) {
		this.disabledLoginTypeStr = disabledLoginTypeStr;
	}

	public String getStateStr() {
		return stateStr;
	}

	public void setStateStr(String stateStr) {
		this.stateStr = stateStr;
	}

	public Integer get__htp__() {
		return __htp__;
	}

	public void set__htp__(Integer __htp__) {
		this.__htp__ = __htp__;
	}

	public String getCreator() {
		return creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public String getCreateTimeStr() {
		return createTimeStr;
	}

	public void setCreateTimeStr(String createTimeStr) {
		this.createTimeStr = createTimeStr;
	}

	public String getLastModifierStr() {
		return lastModifierStr;
	}

	public void setLastModifierStr(String lastModifierStr) {
		this.lastModifierStr = lastModifierStr;
	}

	public String getLastModifyTimeStr() {
		return lastModifyTimeStr;
	}

	public void setLastModifyTimeStr(String lastModifyTimeStr) {
		this.lastModifyTimeStr = lastModifyTimeStr;
	}

}
