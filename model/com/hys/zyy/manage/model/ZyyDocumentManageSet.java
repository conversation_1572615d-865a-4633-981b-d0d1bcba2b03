package com.hys.zyy.manage.model;

import java.io.Serializable;
import java.util.Date;

import com.hys.zyy.manage.util.BaseModel;

/**
 * 文档管理设置
 */
public class ZyyDocumentManageSet extends BaseModel implements Serializable {
	private static final long serialVersionUID = 1L;
	/*
	 * ID
	 */
	private Long id;
	/*
	 * 省ID
	 */
	private Long provinceId;
	/*
	 * 医院ID
	 */
	private Long hospitalId;
	/*
	 * 文件夹层级数（1：一层；2：二层；3：三层；4：四层；暂最多四层）
	 */
	private Integer folderLevelNumber;
	/*
	 * 存储空间，单位G
	 */
	private Float memorySize;
	/*
	 * 状态（-1：失效；1：有效）
	 */
	private Integer state;

	private Date createTime;
	private Date updateTime;

	public ZyyDocumentManageSet() {
		super();
	}

	public ZyyDocumentManageSet(Integer state, Long provinceId, Long hospitalId) {
		super();
		this.state = state;
		this.provinceId = provinceId;
		this.hospitalId = hospitalId;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getProvinceId() {
		return provinceId;
	}

	public void setProvinceId(Long provinceId) {
		this.provinceId = provinceId;
	}

	public Long getHospitalId() {
		return hospitalId;
	}

	public void setHospitalId(Long hospitalId) {
		this.hospitalId = hospitalId;
	}

	public Integer getFolderLevelNumber() {
		return folderLevelNumber;
	}

	public void setFolderLevelNumber(Integer folderLevelNumber) {
		this.folderLevelNumber = folderLevelNumber;
	}

	public Float getMemorySize() {
		return memorySize;
	}

	public void setMemorySize(Float memorySize) {
		this.memorySize = memorySize;
	}

	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ZyyDocumentManageSet other = (ZyyDocumentManageSet) obj;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		return true;
	}

	@Override
	public String toString() {
		return "ZyyDocumentManageSet [id=" + id + ", provinceId=" + provinceId
				+ ", hospitalId=" + hospitalId + ", folderLevelNumber="
				+ folderLevelNumber + ", memorySize=" + memorySize + ", state="
				+ state + ", createTime=" + createTime + ", updateTime="
				+ updateTime + "]";
	}

}