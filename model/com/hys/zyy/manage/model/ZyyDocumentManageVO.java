package com.hys.zyy.manage.model;

public class ZyyDocumentManageVO {

	private Long id;
	private Integer zyyUserType;
	private String documentName, documentType, fileType, updateTime,topTime, directoryName, stateUpdateTimeStr,realName,sizeStr;
	private Double documentSize;
	private Float activeTime;
	private Long createUserId ;
	private int isRead =2;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getDocumentName() {
		return documentName;
	}

	public void setDocumentName(String documentName) {
		this.documentName = documentName == null ? null : documentName.trim();
	}

	public String getDocumentType() {
		return documentType;
	}

	public void setDocumentType(String documentType) {
		this.documentType = documentType == null ? null : documentType.trim();
	}

	public String getFileType() {
		return fileType;
	}

	public void setFileType(String fileType) {
		this.fileType = fileType == null ? null : fileType.trim();
	}

	public String getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(String updateTime) {
		this.updateTime = updateTime == null ? null : updateTime.trim();
	}

	public String getTopTime() {
		return topTime;
	}

	public void setTopTime(String topTime) {
		this.topTime = topTime == null ? null : topTime.trim();
	}

	public Double getDocumentSize() {
		return documentSize;
	}

	public void setDocumentSize(Double documentSize) {
		this.documentSize = documentSize;
	}

	public String getDirectoryName() {
		return directoryName;
	}

	public void setDirectoryName(String directoryName) {
		this.directoryName = directoryName == null ? null : directoryName.trim();
	}

	public String getStateUpdateTimeStr() {
		return stateUpdateTimeStr;
	}

	public void setStateUpdateTimeStr(String stateUpdateTimeStr) {
		this.stateUpdateTimeStr = stateUpdateTimeStr == null ? null : stateUpdateTimeStr.trim();
	}

	public Float getActiveTime() {
		return activeTime;
	}

	public void setActiveTime(Float activeTime) {
		this.activeTime = activeTime;
	}

	public int getIsRead() {
		return isRead;
	}

	public void setIsRead(int isRead) {
		this.isRead = isRead;
	}

	public String getRealName() {
		return realName;
	}

	public void setRealName(String realName) {
		this.realName = realName;
	}

	public Integer getZyyUserType() {
		return zyyUserType;
	}

	public void setZyyUserType(Integer zyyUserType) {
		this.zyyUserType = zyyUserType;
	}

	public String getSizeStr() {
		return sizeStr;
	}

	public void setSizeStr(String sizeStr) {
		this.sizeStr = sizeStr;
	}

	public Long getCreateUserId() {
		return createUserId;
	}

	public void setCreateUserId(Long createUserId) {
		this.createUserId = createUserId;
	}
}
