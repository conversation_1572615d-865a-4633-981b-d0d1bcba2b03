package com.hys.zyy.manage.model;

import java.io.Serializable;
import java.util.Date;

import com.hys.zyy.manage.util.BaseModel;

/**
 * 操作技能直接观察评估量表
 * <AUTHOR>
 */
public class ZyyDopsForm extends BaseModel implements Serializable {
	private static final long serialVersionUID = 1L;
	/*
	 * ID
	 */
	private Long id;
	/*
	 * 学员ID
	 */
	private Long residencyId;
	/*
	 * 学员所在科室
	 */
	private Long deptId;
	/*
	 * 学员类型（1=第一年；2=第二年；3=第三年）
	 */
	private Integer residencyType;
	/*
	 * 学员满意度（1,2,3...）
	 */
	private Integer residencyJoyLevel;
	/*
	 * 考官姓名
	 */
	private String examinerName;
	/*
	 * 考官类型（1=主任医师；2=副主任医师；3=主治医师）
	 */
	private Integer examinerType;
	/*
	 * 考官满意程度（1,2,3...）
	 */
	private Integer examinerJoyLevel;
	/*
	 * 考官评语
	 */
	private String examinerComment;
	/*
	 * 考核时间
	 */
	private Date assessTime;
	/*
	 * 考核地点（1=病房；2=门诊；3=急诊；4=ICU；5=其他）
	 */
	private Integer assessLocale;
	/*
	 * 病人年龄
	 */
	private Integer patientAge;
	/*
	 * 病人性别（1=男；2=女）
	 */
	private Integer patientSex;
	/*
	 * 就诊类型（1=初诊；2=复诊）
	 */
	private Integer visitType;
	/*
	 * 操作名称
	 */
	private String operateName;
	/*
	 * 操作复杂程度（1=低；2=中；3=高）
	 */
	private Integer operateState;
	/*
	 * 操作适应证、相关解剖和操作技术的理解（0=未观察到；1=1分；2=2分；3=3分...）
	 */
	private Integer czjslj;
	/*
	 * 知情同意
	 */
	private Integer zqty;
	/*
	 * 操作前准备
	 */
	private Integer czqzb;
	/*
	 * 止痛镇静
	 */
	private Integer ztzj;
	/*
	 * 技术能力
	 */
	private Integer jsnl;
	/*
	 * 无菌技术
	 */
	private Integer wjjs;
	/*
	 * 根据需要寻求帮助
	 */
	private Integer xqbz;
	/*
	 * 操作后处理
	 */
	private Integer czhcl;
	/*
	 * 沟通技能
	 */
	private Integer gtjn;
	/*
	 * 人文关怀/职业素养
	 */
	private Integer rwgh;
	/*
	 * 整体表现
	 */
	private Integer ztbx;
	/*
	 * 直接观察时间（分钟）
	 */
	private String zjgcsj;
	/*
	 * 反馈时间（分钟）
	 */
	private String fksj;
	/*
	 * 状态（1=有效；-1=失效）
	 */
	private Integer state;
	private Date createTime;
	private Date updateTime;
	/*
	 * PDF路径
	 */
	private String pdfUrl;

	public ZyyDopsForm() {
		super();
	}

	public ZyyDopsForm(Long id) {
		super();
		this.id = id;
	}
	
	public ZyyDopsForm(Long id, String pdfUrl) {
		super();
		this.id = id;
		this.pdfUrl = pdfUrl;
	}

	public ZyyDopsForm(Long id, Integer residencyJoyLevel, Integer examinerJoyLevel) {
		super();
		this.id = id;
		this.residencyJoyLevel = residencyJoyLevel;
		this.examinerJoyLevel = examinerJoyLevel;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getResidencyId() {
		return residencyId;
	}

	public void setResidencyId(Long residencyId) {
		this.residencyId = residencyId;
	}

	public Long getDeptId() {
		return deptId;
	}

	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}

	public Integer getResidencyType() {
		return residencyType;
	}

	public void setResidencyType(Integer residencyType) {
		this.residencyType = residencyType;
	}

	public Integer getResidencyJoyLevel() {
		return residencyJoyLevel;
	}

	public void setResidencyJoyLevel(Integer residencyJoyLevel) {
		this.residencyJoyLevel = residencyJoyLevel;
	}

	public String getExaminerName() {
		return examinerName;
	}

	public void setExaminerName(String examinerName) {
		this.examinerName = examinerName == null ? null : examinerName.trim();
	}

	public Integer getExaminerType() {
		return examinerType;
	}

	public void setExaminerType(Integer examinerType) {
		this.examinerType = examinerType;
	}

	public Integer getExaminerJoyLevel() {
		return examinerJoyLevel;
	}

	public void setExaminerJoyLevel(Integer examinerJoyLevel) {
		this.examinerJoyLevel = examinerJoyLevel;
	}

	public String getExaminerComment() {
		return examinerComment;
	}

	public void setExaminerComment(String examinerComment) {
		this.examinerComment = examinerComment == null ? null : examinerComment.trim();
	}

	public Date getAssessTime() {
		return assessTime;
	}

	public void setAssessTime(Date assessTime) {
		this.assessTime = assessTime;
	}

	public Integer getAssessLocale() {
		return assessLocale;
	}

	public void setAssessLocale(Integer assessLocale) {
		this.assessLocale = assessLocale;
	}

	public Integer getPatientAge() {
		return patientAge;
	}

	public void setPatientAge(Integer patientAge) {
		this.patientAge = patientAge;
	}

	public Integer getPatientSex() {
		return patientSex;
	}

	public void setPatientSex(Integer patientSex) {
		this.patientSex = patientSex;
	}

	public Integer getVisitType() {
		return visitType;
	}

	public void setVisitType(Integer visitType) {
		this.visitType = visitType;
	}

	public String getOperateName() {
		return operateName;
	}

	public void setOperateName(String operateName) {
		this.operateName = operateName == null ? null : operateName.trim();
	}

	public Integer getOperateState() {
		return operateState;
	}

	public void setOperateState(Integer operateState) {
		this.operateState = operateState;
	}

	public Integer getCzjslj() {
		return czjslj;
	}

	public void setCzjslj(Integer czjslj) {
		this.czjslj = czjslj;
	}

	public Integer getZqty() {
		return zqty;
	}

	public void setZqty(Integer zqty) {
		this.zqty = zqty;
	}

	public Integer getCzqzb() {
		return czqzb;
	}

	public void setCzqzb(Integer czqzb) {
		this.czqzb = czqzb;
	}

	public Integer getZtzj() {
		return ztzj;
	}

	public void setZtzj(Integer ztzj) {
		this.ztzj = ztzj;
	}

	public Integer getJsnl() {
		return jsnl;
	}

	public void setJsnl(Integer jsnl) {
		this.jsnl = jsnl;
	}

	public Integer getWjjs() {
		return wjjs;
	}

	public void setWjjs(Integer wjjs) {
		this.wjjs = wjjs;
	}

	public Integer getXqbz() {
		return xqbz;
	}

	public void setXqbz(Integer xqbz) {
		this.xqbz = xqbz;
	}

	public Integer getCzhcl() {
		return czhcl;
	}

	public void setCzhcl(Integer czhcl) {
		this.czhcl = czhcl;
	}

	public Integer getGtjn() {
		return gtjn;
	}

	public void setGtjn(Integer gtjn) {
		this.gtjn = gtjn;
	}

	public Integer getRwgh() {
		return rwgh;
	}

	public void setRwgh(Integer rwgh) {
		this.rwgh = rwgh;
	}

	public Integer getZtbx() {
		return ztbx;
	}

	public void setZtbx(Integer ztbx) {
		this.ztbx = ztbx;
	}

	public String getZjgcsj() {
		return zjgcsj;
	}

	public void setZjgcsj(String zjgcsj) {
		this.zjgcsj = zjgcsj == null ? null : zjgcsj.trim();
	}

	public String getFksj() {
		return fksj;
	}

	public void setFksj(String fksj) {
		this.fksj = fksj == null ? null : fksj.trim();
	}

	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public String getPdfUrl() {
		return pdfUrl;
	}

	public void setPdfUrl(String pdfUrl) {
		this.pdfUrl = pdfUrl == null ? null : pdfUrl.trim();
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ZyyDopsForm other = (ZyyDopsForm) obj;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		return true;
	}

	@Override
	public String toString() {
		return "ZyyDopsForm [id=" + id + ", residencyId=" + residencyId + ", deptId=" + deptId + ", residencyType="
				+ residencyType + ", residencyJoyLevel=" + residencyJoyLevel + ", examinerName=" + examinerName
				+ ", examinerType=" + examinerType + ", examinerJoyLevel=" + examinerJoyLevel + ", examinerComment="
				+ examinerComment + ", assessTime=" + assessTime + ", assessLocale=" + assessLocale + ", patientAge="
				+ patientAge + ", patientSex=" + patientSex + ", visitType=" + visitType + ", operateName="
				+ operateName + ", operateState=" + operateState + ", czjslj=" + czjslj + ", zqty=" + zqty + ", czqzb="
				+ czqzb + ", ztzj=" + ztzj + ", jsnl=" + jsnl + ", wjjs=" + wjjs + ", xqbz=" + xqbz + ", czhcl=" + czhcl
				+ ", gtjn=" + gtjn + ", rwgh=" + rwgh + ", ztbx=" + ztbx + ", zjgcsj=" + zjgcsj + ", fksj=" + fksj
				+ ", state=" + state + ", createTime=" + createTime + ", updateTime=" + updateTime + ", pdfUrl=" + pdfUrl + "]";
	}

}