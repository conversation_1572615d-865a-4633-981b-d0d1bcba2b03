package com.hys.zyy.manage.model;

public class ZyyDopsFormVO extends ZyyDopsForm {

	private Long yearId, baseId, teacherId;
	private String realName, year, baseName, deptName, assessTimeStr, startDateStr, endDateStr;
	private Double czjsljAvgScore, zqtyAvgScore, czqzbAvgScore, ztzjAvgScore, jsnlAvgScore, wjjsAvgScore, xqbzAvgScore,
			czhclAvgScore, gtjnAvgScore, rwghAvgScore, ztbxAvgScore;

	public ZyyDopsFormVO() {
		super();
	}

	public ZyyDopsFormVO(Long id) {
		super(id);
	}

	public ZyyDopsFormVO(Long id, String pdfUrl) {
		super(id, pdfUrl);
	}

	public ZyyDopsFormVO(Long id, Integer residencyJoyLevel, Integer examinerJoyLevel) {
		super(id, residencyJoyLevel, examinerJoyLevel);
	}

	public Long getYearId() {
		return yearId;
	}

	public void setYearId(Long yearId) {
		this.yearId = yearId;
	}

	public Long getBaseId() {
		return baseId;
	}

	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}

	public Long getTeacherId() {
		return teacherId;
	}

	public void setTeacherId(Long teacherId) {
		this.teacherId = teacherId;
	}

	public String getRealName() {
		return realName;
	}

	public void setRealName(String realName) {
		this.realName = realName == null ? null : realName.trim();
	}

	public String getYear() {
		return year;
	}

	public void setYear(String year) {
		this.year = year == null ? null : year.trim();
	}

	public String getBaseName() {
		return baseName;
	}

	public void setBaseName(String baseName) {
		this.baseName = baseName == null ? null : baseName.trim();
	}

	public String getDeptName() {
		return deptName;
	}

	public void setDeptName(String deptName) {
		this.deptName = deptName == null ? null : deptName.trim();
	}

	public String getAssessTimeStr() {
		return assessTimeStr;
	}

	public void setAssessTimeStr(String assessTimeStr) {
		this.assessTimeStr = assessTimeStr == null ? null : assessTimeStr.trim();
	}

	public String getStartDateStr() {
		return startDateStr;
	}

	public void setStartDateStr(String startDateStr) {
		this.startDateStr = startDateStr == null ? null : startDateStr.trim();
	}

	public String getEndDateStr() {
		return endDateStr;
	}

	public void setEndDateStr(String endDateStr) {
		this.endDateStr = endDateStr == null ? null : endDateStr.trim();
	}

	public Double getCzjsljAvgScore() {
		return czjsljAvgScore;
	}

	public void setCzjsljAvgScore(Double czjsljAvgScore) {
		this.czjsljAvgScore = czjsljAvgScore;
	}

	public Double getZqtyAvgScore() {
		return zqtyAvgScore;
	}

	public void setZqtyAvgScore(Double zqtyAvgScore) {
		this.zqtyAvgScore = zqtyAvgScore;
	}

	public Double getCzqzbAvgScore() {
		return czqzbAvgScore;
	}

	public void setCzqzbAvgScore(Double czqzbAvgScore) {
		this.czqzbAvgScore = czqzbAvgScore;
	}

	public Double getZtzjAvgScore() {
		return ztzjAvgScore;
	}

	public void setZtzjAvgScore(Double ztzjAvgScore) {
		this.ztzjAvgScore = ztzjAvgScore;
	}

	public Double getJsnlAvgScore() {
		return jsnlAvgScore;
	}

	public void setJsnlAvgScore(Double jsnlAvgScore) {
		this.jsnlAvgScore = jsnlAvgScore;
	}

	public Double getWjjsAvgScore() {
		return wjjsAvgScore;
	}

	public void setWjjsAvgScore(Double wjjsAvgScore) {
		this.wjjsAvgScore = wjjsAvgScore;
	}

	public Double getXqbzAvgScore() {
		return xqbzAvgScore;
	}

	public void setXqbzAvgScore(Double xqbzAvgScore) {
		this.xqbzAvgScore = xqbzAvgScore;
	}

	public Double getCzhclAvgScore() {
		return czhclAvgScore;
	}

	public void setCzhclAvgScore(Double czhclAvgScore) {
		this.czhclAvgScore = czhclAvgScore;
	}

	public Double getGtjnAvgScore() {
		return gtjnAvgScore;
	}

	public void setGtjnAvgScore(Double gtjnAvgScore) {
		this.gtjnAvgScore = gtjnAvgScore;
	}

	public Double getRwghAvgScore() {
		return rwghAvgScore;
	}

	public void setRwghAvgScore(Double rwghAvgScore) {
		this.rwghAvgScore = rwghAvgScore;
	}

	public Double getZtbxAvgScore() {
		return ztbxAvgScore;
	}

	public void setZtbxAvgScore(Double ztbxAvgScore) {
		this.ztbxAvgScore = ztbxAvgScore;
	}

}