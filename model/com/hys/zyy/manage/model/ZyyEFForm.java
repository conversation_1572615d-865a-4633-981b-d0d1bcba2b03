package com.hys.zyy.manage.model;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.hys.zyy.manage.util.annotation.Column;
import com.hys.zyy.manage.util.annotation.Table;

/**
 * 评价信息表实体类
 * <AUTHOR>
 * @version 1.0  2012-08-21
 * 
 */
@Table("zyy_ef_form")
public class ZyyEFForm extends ZyyBaseObject{
	
	private static final long serialVersionUID = 2296108027485382030L;
	
	@Column("id")
	 private Long id;
	 @Column("catalog_id")
	 private Long catalogId; 
	 @Column("form_name")
	 private String formName;
	 @Column("memo") 
	 private String memo; 
	 @Column("form_struct")
	 private String formStruct; 
	 @Column("zyy_org_id")
	 private Long zyyOrgId; 
	 @Column("status")
	 private Integer status; 
	 @Column("create_by")
	 private Long createBy;
	 @Column("create_time")
	 private Date createTime; 
	 @Column("update_by")
	 private Long updateBy; 
	 @Column("update_time")
	 private Date updateTime; 
	 @Column("version")
	 private String version;
	 @Column("form_type")
	 private Long formType;   //1.他评  2.自评+他评  3.总评
	 @Column("store_type")
	 private String storeType;
	 @Column("time_limit_type")
	 private String timeLimitType;
	 @Column("time_limit")
	 private String timeLimit;
	 @Column("publish_type")
	 private String publishType;
	 @Column("view_permission")
	 private Integer viewPermission;
	 @Column("dispatch_type")
	 private Integer dispatchType = 2;					// 1、轮转相关   2、轮转无关
	 @Column("dispatch_start")
	 private Date dispatchStart;
	 @Column("dispatch_end")
	 private Date dispatchEnd;
	 @Column("is_lock")
	 private boolean isLock;
	 
	 private String createName;
	 
	 private Long evalutedBy;
	 
	 private Map<String ,ZyyEfCatalog> catalogs;
	 
	 private List<ZyyEfProcess>  zyyEfProcess;
	 
	 private ZyyUserExtendVO createUser = new ZyyUserExtendVO();
	 
	 public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getCatalogId() {
		return catalogId;
	}
	public void setCatalogId(Long catalogId) {
		this.catalogId = catalogId;
	}
	public String getFormName() {
		return formName;
	}
	public void setFormName(String formName) {
		this.formName = formName;
	}
	public String getMemo() {
		return memo;
	}
	public void setMemo(String memo) {
		this.memo = memo;
	}
	public String getFormStruct() {
		return formStruct;
	}
	public void setFormStruct(String formStruct) {
		this.formStruct = formStruct;
	}
	public Long getZyyOrgId() {
		return zyyOrgId;
	}
	public void setZyyOrgId(Long zyyOrgId) {
		this.zyyOrgId = zyyOrgId;
	}
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	public Long getCreateBy() {
		return createBy;
	}
	public void setCreateBy(Long createBy) {
		this.createBy = createBy;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	public Long getUpdateBy() {
		return updateBy;
	}
	public void setUpdateBy(Long updateBy) {
		this.updateBy = updateBy;
	}
	public Date getUpdateTime() {
		return updateTime;
	}
	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
	public String getVersion() {
		return version;
	}
	public void setVersion(String version) {
		this.version = version;
	}
	public void setCreateName(String createName) {
		this.createName = createName;
	}
	public String getCreateName() {
		return createName;
	}
	public void setCatalogs(Map<String ,ZyyEfCatalog> catalogs) {
		this.catalogs = catalogs;
	}
	public Map<String ,ZyyEfCatalog> getCatalogs() {
		return catalogs;
	}
	public void setFormType(Long formType) {
		this.formType = formType;
	}
	public Long getFormType() {
		return formType;
	}
	public void setStoreType(String storeType) {
		this.storeType = storeType;
	}
	public String getStoreType() {
		return storeType;
	}
	public void setTimeLimitType(String timeLimitType) {
		this.timeLimitType = timeLimitType;
	}
	public String getTimeLimitType() {
		return timeLimitType;
	}
	public void setTimeLimit(String timeLimit) {
		this.timeLimit = timeLimit;
	}
	public String getTimeLimit() {
		return timeLimit;
	}
	public void setPublishType(String publishType) {
		this.publishType = publishType;
	}
	public String getPublishType() {
		return publishType;
	}
	public void setViewPermission(Integer viewPermission) {
		this.viewPermission = viewPermission;
	}
	public Integer getViewPermission() {
		return viewPermission;
	}
	public void setZyyEfProcess(List<ZyyEfProcess> zyyEfProcess) {
		this.zyyEfProcess = zyyEfProcess;
	}
	public List<ZyyEfProcess> getZyyEfProcess() {
		return zyyEfProcess;
	}
	public ZyyUserExtendVO getCreateUser() {
		return createUser;
	}
	public void setCreateUser(ZyyUserExtendVO createUser) {
		this.createUser = createUser;
	}
	public Integer getDispatchType() {
		return dispatchType;
	}
	public void setDispatchType(Integer dispatchType) {
		this.dispatchType = dispatchType;
	}
	public Date getDispatchStart() {
		return dispatchStart;
	}
	public void setDispatchStart(Date dispatchStart) {
		this.dispatchStart = dispatchStart;
	}
	public Date getDispatchEnd() {
		return dispatchEnd;
	}
	public void setDispatchEnd(Date dispatchEnd) {
		this.dispatchEnd = dispatchEnd;
	}
	/**
	 * 轮转相关的评价表
	 * @return
	 */
	public boolean ifCycleRelated() {
		return this.dispatchType == 1;
	}
	
	/**
	 * 是否自评
	 * @return
	 */
	public boolean ifDiy() {
		if(this.formType == 2l)
			return true;
		return false;
	}
	
	public boolean getIsLock() {
		return isLock;
	}
	public void setIsLock(boolean isLock) {
		this.isLock = isLock;
	}
	public Long getEvalutedBy() {
		return evalutedBy;
	}
	public void setEvalutedBy(Long evalutedBy) {
		this.evalutedBy = evalutedBy;
	}
}
