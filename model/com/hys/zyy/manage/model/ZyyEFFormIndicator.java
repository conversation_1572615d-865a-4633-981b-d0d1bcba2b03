package com.hys.zyy.manage.model;

import com.hys.zyy.manage.util.annotation.Column;
import com.hys.zyy.manage.util.annotation.Table;
/**
 * 评价表指标
 * <AUTHOR>
 * @version 1.0  2012-08-30
 */
@Table("zyy_ef_form_indicator")
public class ZyyEFFormIndicator extends ZyyBaseObject {
	
	private static final long serialVersionUID = -4819964065501171493L;
	
	@Column("id")
	private Long id;
	@Column("form_id")
	private Long formId;
	@Column("indicator_id")
	private Long indicatorId;
	@Column("range_value")
	private String rangeValue;
	@Column("regular_value")
	private String regularValue;
	@Column("std_id")
	private Long stdId;
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getFormId() {
		return formId;
	}
	public void setFormId(Long formId) {
		this.formId = formId;
	}
	public Long getIndicatorId() {
		return indicatorId;
	}
	public void setIndicatorId(Long indicatorId) {
		this.indicatorId = indicatorId;
	}
	public String getRangeValue() {
		return rangeValue;
	}
	public void setRangeValue(String rangeValue) {
		this.rangeValue = rangeValue;
	}
	public String getRegularValue() {
		return regularValue;
	}
	public void setRegularValue(String regularValue) {
		this.regularValue = regularValue;
	}
	public Long getStdId() {
		return stdId;
	}
	public void setStdId(Long stdId) {
		this.stdId = stdId;
	}
}
