package com.hys.zyy.manage.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.hys.zyy.manage.util.annotation.Column;
import com.hys.zyy.manage.util.annotation.Table;

/**
 * 评价表分类实体类
 *     
 * <AUTHOR>      
 * @version 1.0    
 * @created 2012-8-21 下午05:11:14
 */
@Table("zyy_ef_catalog")
public class ZyyEfCatalog extends ZyyBaseObject {

	
	private static final long serialVersionUID = -6095279547582986534L;

	/**
	 * 主键ID
	 */
	@Column("id")
	private Long id ;
	
	/**
	 * 分类名称
	 */
	@Column("catalog_name")
	private String catalogName ;
	
	/**
	 * 树形编码
	 */
	@Column("tree_code")
	private String treeCode ;
	
	/**
	 * 父id
	 */
	@Column("parent_id")
	private Long parentId ;
	
	/**
	 * 顺序
	 */
	@Column("seq")
	private Long seq ;
	
	/**
	 * 状态   1:正常 , -1:删除
	 */
	@Column("status")
	private Integer status ;
	
	/**
	 * 机构ID
	 */
	@Column("zyy_org_id")
	private Long zyyOrgId;
	
	/**
	 * 科室ID
	 */
	@Column("zyy_dept_id")
	private Long zyyDeptId;
	/**
	 * 创建人
	 */
	@Column("create_by")
	private Long createBy ;
	
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private Date createTime ;
	
	/**
	 * 修改人
	 */
	@Column("update_by")
	private Long updateBy ;
	
	/**
	 * 创建时间
	 */
	@Column("update_time")
	private Date updateTime ;
	
	private int level;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getCatalogName() {
		return catalogName;
	}

	public void setCatalogName(String catalogName) {
		this.catalogName = catalogName;
	}

	public String getTreeCode() {
		return treeCode;
	}

	public void setTreeCode(String treeCode) {
		this.treeCode = treeCode;
	}

	public Long getParentId() {
		return parentId;
	}

	public void setParentId(Long parentId) {
		this.parentId = parentId;
	}

	public Long getSeq() {
		return seq;
	}

	public void setSeq(Long seq) {
		this.seq = seq;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Long getZyyOrgId() {
		return zyyOrgId;
	}

	public void setZyyOrgId(Long zyyOrgId) {
		this.zyyOrgId = zyyOrgId;
	}

	public Long getCreateBy() {
		return createBy;
	}

	public void setCreateBy(Long createBy) {
		this.createBy = createBy;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Long getUpdateBy() {
		return updateBy;
	}

	public void setUpdateBy(Long updateBy) {
		this.updateBy = updateBy;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public Long getZyyDeptId() {
		return zyyDeptId;
	}

	public void setZyyDeptId(Long zyyDeptId) {
		this.zyyDeptId = zyyDeptId;
	}

	public int getLevel() {
		return level;
	}

	public void setLevel(int level) {
		this.level = level;
	}

}
