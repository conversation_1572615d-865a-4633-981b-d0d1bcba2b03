package com.hys.zyy.manage.model;

import java.util.List;

/**
 * 
 * 评价表分类业务表单实体类    
 *    
 * <AUTHOR>      
 * @version 1.0    
 * @created 2012-8-22 下午02:09:27
 */
public class ZyyEfCatalogVO extends ZyyEfCatalog {
	
	private static final long serialVersionUID = -1955164822055190882L;
	
	//选择的上级分类Id
	private Long parentCatalogId;
	
	//操作结果
	private String operateResult;
	
	

	//子分类集合
	private List<ZyyEfCatalogVO> subZyyEfCatalogVOList;

	public List<ZyyEfCatalogVO> getSubZyyEfCatalogVOList() {
		return subZyyEfCatalogVOList;
	}

	public void setSubZyyEfCatalogVOList(List<ZyyEfCatalogVO> subZyyEfCatalogVOList) {
		this.subZyyEfCatalogVOList = subZyyEfCatalogVOList;
	} 


	public Long getParentCatalogId() {
		return parentCatalogId;
	}

	public void setParentCatalogId(Long parentCatalogId) {
		this.parentCatalogId = parentCatalogId;
	}

	public String getOperateResult() {
		return operateResult;
	}

	public void setOperateResult(String operateResult) {
		this.operateResult = operateResult;
	}
}
