package com.hys.zyy.manage.model;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;


public class ZyyEfData extends ZyyBaseObject implements java.io.Serializable {
	private static final long serialVersionUID = 5454155825314635342L;
	
	//alias
	public static final String TABLE_ALIAS = "ZyyEfData";
	public static final String ALIAS_ID = "id";
	public static final String ALIAS_TASK_ID = "taskId";
	public static final String ALIAS_INSTANCE_ID = "instanceId";
	public static final String ALIAS_EVALUATED_BY = "evaluatedBy";
	public static final String ALIAS_KEY_NAME = "keyName";
	public static final String ALIAS_KEY_VALUE = "keyValue";
	public static final String ALIAS_CREATE_DATE = "createDate";
	public static final String ALIAS_CREATE_BY = "createBy";
	public static final String ALIAS_LAST_UPDATE_DATE = "lastUpdateDate";
	public static final String ALIAS_LAST_UPDATE_BY = "lastUpdateBy";
	
	private Long id;
	
	private Long taskId;
	
	private Long instanceId;
	
	private Long evaluatedBy;
	
	private java.lang.String keyName;
	
	private java.lang.String keyValue;
	
	private java.util.Date createDate;
	
	private Long createBy;
	
	private java.util.Date lastUpdateDate;
	
	private Long lastUpdateBy;
	//columns END

	public ZyyEfData(){
	}

	public ZyyEfData(
		Long id
	){
		this.id = id;
	}

	public void setId(Long value) {
		this.id = value;
	}
	
	public Long getId() {
		return this.id;
	}
	public void setTaskId(Long value) {
		this.taskId = value;
	}
	
	public Long getTaskId() {
		return this.taskId;
	}
	public void setInstanceId(Long value) {
		this.instanceId = value;
	}
	
	public Long getInstanceId() {
		return this.instanceId;
	}
	public void setEvaluatedBy(Long value) {
		this.evaluatedBy = value;
	}
	
	public Long getEvaluatedBy() {
		return this.evaluatedBy;
	}
	public void setKeyName(java.lang.String value) {
		this.keyName = value;
	}
	
	public java.lang.String getKeyName() {
		return this.keyName;
	}
	public void setKeyValue(java.lang.String value) {
		this.keyValue = value;
	}
	
	public java.lang.String getKeyValue() {
		return this.keyValue;
	}
	
	public void setCreateDate(java.util.Date value) {
		this.createDate = value;
	}
	
	public java.util.Date getCreateDate() {
		return this.createDate;
	}
	public void setCreateBy(Long value) {
		this.createBy = value;
	}
	
	public Long getCreateBy() {
		return this.createBy;
	}
	
	public void setLastUpdateDate(java.util.Date value) {
		this.lastUpdateDate = value;
	}
	
	public java.util.Date getLastUpdateDate() {
		return this.lastUpdateDate;
	}
	public void setLastUpdateBy(Long value) {
		this.lastUpdateBy = value;
	}
	
	public Long getLastUpdateBy() {
		return this.lastUpdateBy;
	}

	public String toString() {
		return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
			.append("Id",getId())
			.append("TaskId",getTaskId())
			.append("InstanceId",getInstanceId())
			.append("EvaluatedBy",getEvaluatedBy())
			.append("KeyName",getKeyName())
			.append("KeyValue",getKeyValue())
			.append("CreateDate",getCreateDate())
			.append("CreateBy",getCreateBy())
			.append("LastUpdateDate",getLastUpdateDate())
			.append("LastUpdateBy",getLastUpdateBy())
			.toString();
	}
	
	public int hashCode() {
		return new HashCodeBuilder()
			.append(getId())
			.toHashCode();
	}
	
	public boolean equals(Object obj) {
		if(obj instanceof ZyyEfData == false) return false;
		if(this == obj) return true;
		ZyyEfData other = (ZyyEfData)obj;
		return new EqualsBuilder()
			.append(getId(),other.getId())
			.isEquals();
	}
}

