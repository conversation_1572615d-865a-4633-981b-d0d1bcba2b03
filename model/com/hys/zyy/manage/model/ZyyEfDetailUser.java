package com.hys.zyy.manage.model;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

public class ZyyEfDetailUser extends ZyyBaseObject implements java.io.Serializable {
	private static final long serialVersionUID = 5454155825314635342L;
	
	//alias
	public static final String TABLE_ALIAS = "ZyyEfDetailUser";
	public static final String ALIAS_DETAIL_ID = "detailId";
	public static final String ALIAS_USER_ID = "userId";
	
	private Long detailId;
	
	private Long userId;
	
	private String realName;
	
	private String accountName;
	//columns END

	public void setDetailId(Long value) {
		this.detailId = value;
	}
	
	public Long getDetailId() {
		return this.detailId;
	}
	public void setUserId(Long value) {
		this.userId = value;
	}
	
	public Long getUserId() {
		return this.userId;
	}
	
	private ZyyEfProcessDetail zyyEfProcessDetail;
	
	public void setZyyEfProcessDetail(ZyyEfProcessDetail zyyEfProcessDetail){
		this.zyyEfProcessDetail = zyyEfProcessDetail;
	}
	
	public ZyyEfProcessDetail getZyyEfProcessDetail() {
		return zyyEfProcessDetail;
	}

	public String toString() {
		return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
			.append("DetailId",getDetailId())
			.append("UserId",getUserId())
			.toString();
	}
	
	public int hashCode() {
		return new HashCodeBuilder()
			.toHashCode();
	}
	
	public boolean equals(Object obj) {
		if(obj instanceof ZyyEfDetailUser == false) return false;
		if(this == obj) return true;
		ZyyEfDetailUser other = (ZyyEfDetailUser)obj;
		return new EqualsBuilder()
			.isEquals();
	}

	public void setRealName(String realName) {
		this.realName = realName;
	}

	public String getRealName() {
		return realName;
	}

	public void setAccountName(String accountName) {
		this.accountName = accountName;
	}

	public String getAccountName() {
		return accountName;
	}
}

