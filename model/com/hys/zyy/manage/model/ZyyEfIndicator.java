package com.hys.zyy.manage.model;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import com.hys.zyy.manage.util.annotation.Column;
import com.hys.zyy.manage.util.annotation.Id;
import com.hys.zyy.manage.util.annotation.Table;

@Table("ZYY_EF_INDICATOR")
public class ZyyEfIndicator extends ZyyBaseObject implements IComboTreeJsonModel, IAutoCompleteJsonModel, java.io.Serializable {
	
	private static final long serialVersionUID = 5454155825314635342L;
	
	@Id("ZYY_EF_INDICATOR_SEQ.nextval")
	@Column("ID")
	private Long id;
	
	@Column("CATALOG_ID")
	private Long catalogId;
	
	@Column("ZYY_ORG_ID")
	private Long zyyOrgId;
	
	@Column("PARENT_ID")
	private Long parentId;
	
	@Column("TREE_CODE")
	private java.lang.String treeCode;
	
	@Column("INDICATOR_NAME")
	private java.lang.String indicatorName;
	
	@Column("STATUS")
	private Integer status;
	
	@Column("SEQ")
	private Long seq;								// 顺序
	
	@Column("CREATE_DATE")
	private java.util.Date createDate;
	
	private Long level;								// 级数
	
	private List<ZyyEfIndicator>  children = new ArrayList<ZyyEfIndicator>();			// 下级指标

	public ZyyEfIndicator(){
	}

	public ZyyEfIndicator(
		Long id
	){
		this.id = id;
	}

	public void setId(Long value) {
		this.id = value;
	}
	
	public Long getId() {
		return this.id;
	}
	public void setCatalogId(Long value) {
		this.catalogId = value;
	}
	
	public Long getCatalogId() {
		return this.catalogId;
	}
	public void setZyyOrgId(Long value) {
		this.zyyOrgId = value;
	}
	
	public Long getZyyOrgId() {
		return this.zyyOrgId;
	}
	public void setParentId(Long value) {
		this.parentId = value;
	}
	
	public Long getParentId() {
		return this.parentId;
	}
	public void setTreeCode(java.lang.String value) {
		this.treeCode = value;
	}
	
	public java.lang.String getTreeCode() {
		return this.treeCode;
	}
	public void setIndicatorName(java.lang.String value) {
		this.indicatorName = value;
	}
	
	public java.lang.String getIndicatorName() {
		return this.indicatorName;
	}
	public void setStatus(Integer value) {
		this.status = value;
	}
	
	public Integer getStatus() {
		return this.status;
	}
	
	public void setCreateDate(java.util.Date value) {
		this.createDate = value;
	}
	
	public java.util.Date getCreateDate() {
		return this.createDate;
	}
	
	private ZyyEfCatalog zyyEfCatalog;
	
	public void setZyyEfCatalog(ZyyEfCatalog zyyEfCatalog){
		this.zyyEfCatalog = zyyEfCatalog;
	}
	
	public ZyyEfCatalog getZyyEfCatalog() {
		return zyyEfCatalog;
	}

	public String toString() {
		return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
			.append("Id",getId())
			.append("CatalogId",getCatalogId())
			.append("ZyyOrgId",getZyyOrgId())
			.append("ParentId",getParentId())
			.append("TreeCode",getTreeCode())
			.append("IndicatorName",getIndicatorName())
			.append("Status",getStatus())
			.append("CreateDate",getCreateDate())
			.toString();
	}
	
	public int hashCode() {
		return new HashCodeBuilder()
			.append(getId())
			.toHashCode();
	}
	
	public boolean equals(Object obj) {
		if(obj instanceof ZyyEfIndicator == false) return false;
		if(this == obj) return true;
		ZyyEfIndicator other = (ZyyEfIndicator)obj;
		return new EqualsBuilder()
			.append(getId(),other.getId())
			.isEquals();
	}

	public Long getSeq() {
		return seq;
	}

	public void setSeq(Long seq) {
		this.seq = seq;
	}

	public Long getLevel() {
		return level;
	}

	public void setLevel(Long level) {
		this.level = level;
	}

	public List<ZyyEfIndicator> getChildren() {
		return children;
	}

	public void setChildren(List<ZyyEfIndicator> children) {
		this.children = children;
	}

	@Override
	public String getText() {
		return this.getIndicatorName();
	}

	@Override
	public String getLabel() {
		return this.getIndicatorName();
	}

	@Override
	public String getValue() {
		return String.valueOf(this.getId());
	}
}

