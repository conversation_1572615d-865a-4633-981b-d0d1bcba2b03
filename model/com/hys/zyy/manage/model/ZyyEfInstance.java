package com.hys.zyy.manage.model;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import com.google.common.collect.Lists;
import com.hys.zyy.manage.constants.Constants;
import com.hys.zyy.manage.util.annotation.Column;
import com.hys.zyy.manage.util.annotation.Id;
import com.hys.zyy.manage.util.annotation.Table;

@Table("ZYY_EF_INSTANCE")
public class ZyyEfInstance extends ZyyBaseObject implements java.io.Serializable {
	private static final long serialVersionUID = 5454155825314635342L;
	
	@Id("ZYY_EF_INSTANCE_SEQ.nextval")
	@Column("INSTANCE_ID")
	private Long instanceId;
	
	@Column("PARAM_VALUE")
	private java.lang.String paramValue;
	
	@Column("EVALUTED_BY")
	private Long evaluatedBy;
	
	@Column("FORM_ID")
	private Long formId;
	
	@Column("CREATE_DATE")
	private java.util.Date createDate;
	
	@Column("STATUS")
	private Integer status;
	
	@Column("START_DATE")
	private Date startDate;
	
	@Column("END_DATE")
	private Date endDate;
	
	private String evaluatedUserName;
	
	private List<ZyyEfInstanceEvaluation> valuators;
	
	private Long evaluteBy;
	
	public ZyyEfInstance(){
	}

	public ZyyEfInstance(
		Long instanceId
	){
		this.instanceId = instanceId;
	}

	public void setInstanceId(Long value) {
		this.instanceId = value;
	}
	
	public Long getInstanceId() {
		return this.instanceId;
	}
	public void setParamValue(java.lang.String value) {
		this.paramValue = value;
	}
	
	public java.lang.String getParamValue() {
		return this.paramValue;
	}
	public void setEvaluatedBy(Long value) {
		this.evaluatedBy = value;
	}
	
	public Long getEvaluatedBy() {
		return this.evaluatedBy;
	}
	public void setFormId(Long value) {
		this.formId = value;
	}
	
	public Long getFormId() {
		return this.formId;
	}
	
	public String toString() {
		return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
			.append("evaluateBy",getEvaluteBy())
			.append("EvaluatedBy",getEvaluatedBy())
			.append("FormId",getFormId())
			.toString();
	}
	
	public int hashCode() {
		return new HashCodeBuilder()
			.append(getInstanceId())
			.toHashCode();
	}
	
	public boolean equals(Object obj) {
		if(obj instanceof ZyyEfInstance == false) return false;
		if(this == obj) return true;
		ZyyEfInstance other = (ZyyEfInstance)obj;
		return new EqualsBuilder()
			.append(getInstanceId(),other.getInstanceId())
			.isEquals();
	}

	public java.util.Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(java.util.Date createDate) {
		this.createDate = createDate;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public String getEvaluatedUserName() {
		return evaluatedUserName;
	}

	public void setEvaluatedUserName(String evaluatedUserName) {
		this.evaluatedUserName = evaluatedUserName;
	}

	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}
	
	public List<ZyyEfInstanceEvaluation> getValuators() {
		return valuators;
	}

	public static ZyyEfInstance newInstance(Long to,
			long startTime, long endTime, Long formId) {
		ZyyEfInstance inst = new ZyyEfInstance();
		inst.setCreateDate(new Date());
		inst.setEvaluatedBy(to);
		inst.setFormId(formId);
		inst.setStatus(Constants.STATUS_NORMAL);
		inst.setStartDate(new Date(startTime));
		inst.setEndDate(new Date(endTime));
		return inst;
	}

	public void addValuator(Long from, long startTime, long endTime) {
		ZyyEfInstanceEvaluation zee = new ZyyEfInstanceEvaluation();
		zee.setEvaluteBy(from);
		zee.setHasPublish(1l);
		zee.setStatus(Constants.STATUS_NORMAL);
		zee.setStartDate(new Date(startTime));
		zee.setEndDate(new Date(endTime));
		
		if(this.valuators == null)
			this.valuators = Lists.newArrayList();
		
		this.valuators.add(zee);
	}

	public Long getEvaluteBy() {
		return evaluteBy;
	}

	public void setEvaluteBy(Long evaluteBy) {
		this.evaluteBy = evaluteBy;
	}

}

