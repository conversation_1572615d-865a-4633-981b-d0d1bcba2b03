package com.hys.zyy.manage.model;

import java.util.Date;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import com.hys.zyy.manage.util.annotation.Column;
import com.hys.zyy.manage.util.annotation.Table;

@Table("ZYY_EF_INSTANCE_EVALUATION")
public class ZyyEfInstanceEvaluation extends ZyyBaseObject implements java.io.Serializable {
	private static final long serialVersionUID = 5454155825314635342L;
	
	@Column("INSTANCE_ID")
	private Long instanceId;
	
	@Column("EVALUTE_BY")
	private Long evaluteBy;
	
	@Column("CREATE_DATE")
	private java.util.Date createDate;
	
	@Column("HAS_PUBLISH")
	private Long hasPublish;
	
	@Column("STATUS")
	private Integer status;
	
	@Column("PERMISSION")
	private Integer permission;
	
	@Column("START_DATE")
	private Date startDate;
	
	@Column("END_DATE")
	private Date endDate;
	//columns END
	
	private String userName;
	
	// 评价者所属医院
	private String hospName;
	
	// 评价者所属基地
	private String baseName;
	
	private String realName;

	public ZyyEfInstanceEvaluation(){
	}

	public ZyyEfInstanceEvaluation(
		Long instanceId
	){
		this.instanceId = instanceId;
	}

	public void setInstanceId(Long value) {
		this.instanceId = value;
	}
	
	public Long getInstanceId() {
		return this.instanceId;
	}
	public void setEvaluteBy(Long value) {
		this.evaluteBy = value;
	}
	
	public Long getEvaluteBy() {
		return this.evaluteBy;
	}
	
	public void setCreateDate(java.util.Date value) {
		this.createDate = value;
	}
	
	public java.util.Date getCreateDate() {
		return this.createDate;
	}
	public void setHasPublish(java.lang.Long value) {
		this.hasPublish = value;
	}
	
	public java.lang.Long getHasPublish() {
		return this.hasPublish;
	}
	public void setStatus(java.lang.Integer value) {
		this.status = value;
	}
	
	public java.lang.Integer getStatus() {
		return this.status;
	}
	
	public String toString() {
		return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
			.append("InstanceId",getInstanceId())
			.append("EvaluteBy",getEvaluteBy())
			.append("CreateDate",getCreateDate())
			.append("HasPublish",getHasPublish())
			.append("Status",getStatus())
			.toString();
	}
	
	public int hashCode() {
		return new HashCodeBuilder()
			.append(getInstanceId())
			.toHashCode();
	}
	
	public boolean equals(Object obj) {
		if(obj instanceof ZyyEfInstanceEvaluation == false) return false;
		if(this == obj) return true;
		ZyyEfInstanceEvaluation other = (ZyyEfInstanceEvaluation)obj;
		return new EqualsBuilder()
			.append(getInstanceId(),other.getInstanceId())
			.isEquals();
	}

	public Integer getPermission() {
		return permission;
	}

	public void setPermission(Integer permission) {
		this.permission = permission;
	}

	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getRealName() {
		return realName;
	}

	public void setRealName(String realName) {
		this.realName = realName;
	}

	public String getHospName() {
		return hospName;
	}

	public void setHospName(String hospName) {
		this.hospName = hospName;
	}

	public String getBaseName() {
		return baseName;
	}

	public void setBaseName(String baseName) {
		this.baseName = baseName;
	}
}

