package com.hys.zyy.manage.model;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;


public class ZyyEfInstanceViewer extends ZyyBaseObject implements java.io.Serializable {
	private static final long serialVersionUID = 5454155825314635342L;
	
	private Long instanceId;
	
	private Long userId;

	public void setInstanceId(Long value) {
		this.instanceId = value;
	}
	
	public Long getInstanceId() {
		return this.instanceId;
	}
	public void setUserId(Long value) {
		this.userId = value;
	}
	
	public Long getUserId() {
		return this.userId;
	}
	
	private ZyyEfInstance zyyEfInstance;
	
	public void setZyyEfInstance(ZyyEfInstance zyyEfInstance){
		this.zyyEfInstance = zyyEfInstance;
	}
	
	public ZyyEfInstance getZyyEfInstance() {
		return zyyEfInstance;
	}

	public String toString() {
		return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
			.append("InstanceId",getInstanceId())
			.append("UserId",getUserId())
			.toString();
	}
	
	public int hashCode() {
		return new HashCodeBuilder()
			.toHashCode();
	}
	
	public boolean equals(Object obj) {
		if(obj instanceof ZyyEfInstanceViewer == false) return false;
		if(this == obj) return true;
		ZyyEfInstanceViewer other = (ZyyEfInstanceViewer)obj;
		return new EqualsBuilder()
			.isEquals();
	}
}

