package com.hys.zyy.manage.model;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import com.hys.zyy.manage.util.annotation.Column;
import com.hys.zyy.manage.util.annotation.Id;
import com.hys.zyy.manage.util.annotation.Table;

@Table("ZYY_EF_JOB")
public class ZyyEfJob extends ZyyBaseObject implements java.io.Serializable {
	
	private static final long serialVersionUID = 5454155825314635342L;

	@Id("ZYY_EF_JOB_SEQ.nextval")
	@Column("ID")
	private Long id;
	
	@Column("FORM_ID")
	private Long formId;
	
	@Column("EVALUATE_BY")
	private Long evaluateBy;
	
	@Column("EVALUATED_BY")
	private Long evaluatedBy;
	
	@Column("CONFIG_CONDITION")
	private java.lang.String configCondition;
	
	@Column("JOB_CONFIG")
	private String jobConfig;
	
	@Column("CREATE_DATE")
	private java.util.Date createDate;
	
	@Column("STATUS")
	private java.lang.Integer status;
	
	@Column("START_TIME")
	private long startTime;
	
	@Column("END_TIME")
	private long endTime;
	
	private Long fromType;
	
	private Long toType;
	
	public ZyyEfJob() {
	}

	public ZyyEfJob(Long from, Long to, Date startDate, Date endDate, Long formId) {
		
		this.evaluateBy = from;
		this.evaluatedBy = to;
		
		if(startDate != null)
			this.startTime = startDate.getTime();
		if(endDate != null)
			this.endTime = endDate.getTime();
		this.formId = formId;
		
	}

	public void setFormId(Long value) {
		this.formId = value;
	}
	
	public Long getFormId() {
		return this.formId;
	}
	public void setEvaluatedBy(Long value) {
		this.evaluatedBy = value;
	}
	
	public Long getEvaluatedBy() {
		return this.evaluatedBy;
	}
	public void setConfigCondition(java.lang.String value) {
		this.configCondition = value;
	}
	
	public java.lang.String getConfigCondition() {
		return this.configCondition;
	}
	public void setCreateDate(java.util.Date value) {
		this.createDate = value;
	}
	
	public java.util.Date getCreateDate() {
		return this.createDate;
	}
	public void setStatus(java.lang.Integer value) {
		this.status = value;
	}
	
	public java.lang.Integer getStatus() {
		return this.status;
	}

	public String toString() {
		return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
			.append("FormId",getFormId())
			.append("EvaluatedBy",getEvaluatedBy())
			.append("ConfigCondition",getConfigCondition())
			.append("CreateDate",getCreateDate())
			.append("Status",getStatus())
			.toString();
	}
	
	public int hashCode() {
		return new HashCodeBuilder()
			.toHashCode();
	}
	
	public boolean equals(Object obj) {
		if(obj instanceof ZyyEfJob == false) return false;
		if(this == obj) return true;
		ZyyEfJob other = (ZyyEfJob)obj;
		return new EqualsBuilder()
			.isEquals();
	}

	public Long getEvaluateBy() {
		return evaluateBy;
	}

	public void setEvaluateBy(Long evaluateBy) {
		this.evaluateBy = evaluateBy;
	}

	public String getJobConfig() {
		return jobConfig;
	}

	public void setJobConfig(String jobConfig) {
		this.jobConfig = jobConfig;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getFromType() {
		return fromType;
	}

	public void setFromType(Long fromType) {
		this.fromType = fromType;
	}

	public Long getToType() {
		return toType;
	}

	public void setToType(Long toType) {
		this.toType = toType;
	}

	public long getStartTime() {
		return startTime;
	}

	public void setStartTime(long startTime) {
		this.startTime = startTime;
	}

	public long getEndTime() {
		return endTime;
	}

	public void setEndTime(long endTime) {
		this.endTime = endTime;
	}

	public static ZyyEfJob newJob(Long from, Long to, Date startDate,
			Date endDate, Long formId) {
		ZyyEfJob self = new ZyyEfJob(from, to, startDate, endDate, formId);
		self.setCreateDate(new Date());
		return self;
	}

	public ZyyEfJob join(List<ZyyEfJob> jobs) {
		if(jobs != null)
			jobs.add(this);
		return this;
	}
}

