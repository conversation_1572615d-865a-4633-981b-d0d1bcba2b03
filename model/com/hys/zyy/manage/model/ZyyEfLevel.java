package com.hys.zyy.manage.model;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import com.hys.zyy.manage.util.annotation.Column;
import com.hys.zyy.manage.util.annotation.Id;
import com.hys.zyy.manage.util.annotation.Table;

@Table("zyy_ef_level")
public class ZyyEfLevel extends ZyyBaseObject implements java.io.Serializable {
	
	private static final long serialVersionUID = 5454155825314635342L;
	
	//alias
	public static final String TABLE_ALIAS = "ZyyEfLevel";
	public static final String ALIAS_ID = "id";
	public static final String ALIAS_ZYY_ORG_ID = "zyyOrgId";
	public static final String ALIAS_CREATE_BY = "createBy";
	public static final String ALIAS_CREATE_DATE = "createDate";
	public static final String ALIAS_UPDATE_BY = "updateBy";
	public static final String ALIAS_UPDATE_DATE = "updateDate";
	public static final String ALIAS_LEVEL1 = "level1";
	public static final String ALIAS_LEVEL2 = "level2";
	public static final String ALIAS_LEVEL3 = "level3";
	public static final String ALIAS_LEVEL4 = "level4";
	public static final String ALIAS_LEVEL5 = "level5";
	public static final String ALIAS_LEVEL6 = "level6";
	public static final String ALIAS_LEVEL7 = "level7";
	
	@Column("id")
	@Id("zyy_ef_level_seq.nextval")
	private Long id;
	
	@Column("zyy_org_id")
	private Long zyyOrgId;
	
	@Column("create_by")
	private Long createBy;
	
	@Column("create_date")
	private java.util.Date createDate;
	
	@Column("update_by")
	private Long updateBy;
	
	@Column("update_date")
	private java.util.Date updateDate;
	
	@Column("level1")
	private String level1;

	@Column("level2")
	private String level2;
	
	@Column("level3")
	private String level3;
	
	@Column("level4")
	private String level4;
	
	@Column("level5")
	private String level5;
	
	@Column("level6")
	private String level6;
	
	@Column("level7")
	private String level7;
	//columns END

	public ZyyEfLevel(){
	}

	public ZyyEfLevel(
		Long id
	){
		this.id = id;
	}

	public void setId(Long value) {
		this.id = value;
	}
	
	public Long getId() {
		return this.id;
	}
	public void setZyyOrgId(Long value) {
		this.zyyOrgId = value;
	}
	
	public Long getZyyOrgId() {
		return this.zyyOrgId;
	}
	public void setCreateBy(Long value) {
		this.createBy = value;
	}
	
	public Long getCreateBy() {
		return this.createBy;
	}
	
	public void setCreateDate(java.util.Date value) {
		this.createDate = value;
	}
	
	public java.util.Date getCreateDate() {
		return this.createDate;
	}
	public void setUpdateBy(Long value) {
		this.updateBy = value;
	}
	
	public Long getUpdateBy() {
		return this.updateBy;
	}
	
	public void setUpdateDate(java.util.Date value) {
		this.updateDate = value;
	}
	
	public java.util.Date getUpdateDate() {
		return this.updateDate;
	}

	public String toString() {
		return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
			.append("Id",getId())
			.append("ZyyOrgId",getZyyOrgId())
			.append("CreateBy",getCreateBy())
			.append("CreateDate",getCreateDate())
			.append("UpdateBy",getUpdateBy())
			.append("UpdateDate",getUpdateDate())
			.toString();
	}
	
	public int hashCode() {
		return new HashCodeBuilder()
			.append(getId())
			.toHashCode();
	}
	
	public boolean equals(Object obj) {
		if(obj instanceof ZyyEfLevel == false) return false;
		if(this == obj) return true;
		ZyyEfLevel other = (ZyyEfLevel)obj;
		return new EqualsBuilder()
			.append(getId(),other.getId())
			.isEquals();
	}

	public String getLevel1() {
		return level1;
	}

	public void setLevel1(String level1) {
		this.level1 = level1;
	}

	public String getLevel2() {
		return level2;
	}

	public void setLevel2(String level2) {
		this.level2 = level2;
	}

	public String getLevel3() {
		return level3;
	}

	public void setLevel3(String level3) {
		this.level3 = level3;
	}

	public String getLevel4() {
		return level4;
	}

	public void setLevel4(String level4) {
		this.level4 = level4;
	}

	public String getLevel5() {
		return level5;
	}

	public void setLevel5(String level5) {
		this.level5 = level5;
	}

	public String getLevel6() {
		return level6;
	}

	public void setLevel6(String level6) {
		this.level6 = level6;
	}

	public String getLevel7() {
		return level7;
	}

	public void setLevel7(String level7) {
		this.level7 = level7;
	}
}

