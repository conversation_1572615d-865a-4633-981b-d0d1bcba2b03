/*
 * Powered By [rapid-framework]
 * Web Site: http://www.rapid-framework.org.cn
 * Google Code: http://code.google.com/p/rapid-framework/
 * Since 2008 - 2012
 */

package com.hys.zyy.manage.model;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;


public class ZyyEfProcUser extends ZyyBaseObject implements java.io.Serializable {
	private static final long serialVersionUID = 5454155825314635342L;
	
	//alias
	public static final String TABLE_ALIAS = "ZyyEfProcUser";
	public static final String ALIAS_PROCESS_ID = "processId";
	public static final String ALIAS_USER_ID = "userId";
	
	//date formats
	
	//可以直接使用: @Length(max=50,message="用户名长度不能大于50")显示错误消息
	//columns START
	
	private Long processId;
	
	private Long userId;
	
	private String realName;
	
	private String accountName;
	//columns END

	public void setProcessId(Long value) {
		this.processId = value;
	}
	
	public Long getProcessId() {
		return this.processId;
	}
	public void setUserId(Long value) {
		this.userId = value;
	}
	
	public Long getUserId() {
		return this.userId;
	}
	
	private ZyyEfProcess zyyEfProcess;
	
	public void setZyyEfProcess(ZyyEfProcess zyyEfProcess){
		this.zyyEfProcess = zyyEfProcess;
	}
	
	public ZyyEfProcess getZyyEfProcess() {
		return zyyEfProcess;
	}

	public String toString() {
		return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
			.append("ProcessId",getProcessId())
			.append("UserId",getUserId())
			.toString();
	}
	
	public int hashCode() {
		return new HashCodeBuilder()
			.toHashCode();
	}
	
	public boolean equals(Object obj) {
		if(obj instanceof ZyyEfProcUser == false) return false;
		if(this == obj) return true;
		ZyyEfProcUser other = (ZyyEfProcUser)obj;
		return new EqualsBuilder()
			.isEquals();
	}

	public void setRealName(String realName) {
		this.realName = realName;
	}

	public String getRealName() {
		return realName;
	}

	public void setAccountName(String accountName) {
		this.accountName = accountName;
	}

	public String getAccountName() {
		return accountName;
	}
}

