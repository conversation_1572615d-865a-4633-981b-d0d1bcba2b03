/*
 * Powered By [rapid-framework]
 * Web Site: http://www.rapid-framework.org.cn
 * Google Code: http://code.google.com/p/rapid-framework/
 * Since 2008 - 2012
 */

package com.hys.zyy.manage.model;

import java.util.Collections;
import java.util.List;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import ch.lambdaj.Lambda;


public class ZyyEfProcess extends ZyyBaseObject implements java.io.Serializable {
	private static final long serialVersionUID = 5454155825314635342L;
	
	//alias
	public static final String TABLE_ALIAS = "ZyyEfProcess";
	public static final String ALIAS_ID = "id";
	public static final String ALIAS_TEMPLATE_ID = "templateId";
	public static final String ALIAS_USER_TYPE = "userType";
	public static final String ALIAS_USER_PARAMETER = "userParameter";
	
	private Long id;
	
	private Long templateId;
	
	private Integer userType;
	
	private java.lang.String userParameter;
	
	private String configCondition;
	
	private List<ZyyEfProcessDetail> details;
	
	private List<ZyyEfProcUser> users;

	public ZyyEfProcess(){
	}

	public ZyyEfProcess(
		Long id
	){
		this.id = id;
	}

	public void setId(Long value) {
		this.id = value;
	}
	
	public Long getId() {
		return this.id;
	}
	public void setTemplateId(Long value) {
		this.templateId = value;
	}
	
	public Long getTemplateId() {
		return this.templateId;
	}
	public void setUserType(Integer value) {
		this.userType = value;
	}
	
	public Integer getUserType() {
		return this.userType;
	}
	public void setUserParameter(java.lang.String value) {
		this.userParameter = value;
	}
	
	public java.lang.String getUserParameter() {
		return this.userParameter;
	}
	
	public void setConfigCondition(String configCondition) {
		this.configCondition = configCondition;
	}

	public String getConfigCondition() {
		return configCondition;
	}

	private List<ZyyEfProcessDetail> zyyEfProcessDetails ;
	public void setZyyEfProcessDetails(List<ZyyEfProcessDetail> zyyEfProcessDetail){
		this.zyyEfProcessDetails = zyyEfProcessDetail;
	}
	
	public List<ZyyEfProcessDetail> getZyyEfProcessDetails() {
		return zyyEfProcessDetails;
	}
	
	private List<ZyyEfProcUser> zyyEfProcUsers ;
	public void setZyyEfProcUsers(List<ZyyEfProcUser> zyyEfProcUser){
		this.zyyEfProcUsers = zyyEfProcUser;
	}
	
	public List<ZyyEfProcUser> getZyyEfProcUsers() {
		return zyyEfProcUsers;
	}
	
	public String toString() {
		return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
			.append("Id",getId())
			.append("TemplateId",getTemplateId())
			.append("UserType",getUserType())
			.append("UserParameter",getUserParameter())
			.toString();
	}
	
	public int hashCode() {
		return new HashCodeBuilder()
			.append(getId())
			.toHashCode();
	}
	
	public boolean equals(Object obj) {
		if(obj instanceof ZyyEfProcess == false) return false;
		if(this == obj) return true;
		ZyyEfProcess other = (ZyyEfProcess)obj;
		return new EqualsBuilder()
			.append(getId(),other.getId())
			.isEquals();
	}

	public List<ZyyEfProcessDetail> getDetails() {
		return details;
	}

	public void setDetails(List<ZyyEfProcessDetail> details) {
		this.details = details;
	}

	public List<ZyyEfProcUser> getUsers() {
		return users;
	}
	
	public List<Long> getUserId() {
		if(this.users == null)
			return Collections.EMPTY_LIST;
		else
			return Lambda.extractProperty(this.users, "userId");
	}

	public void setUsers(List<ZyyEfProcUser> users) {
		this.users = users;
	}
}

