/*
 * Powered By [rapid-framework]
 * Web Site: http://www.rapid-framework.org.cn
 * Google Code: http://code.google.com/p/rapid-framework/
 * Since 2008 - 2012
 */

package com.hys.zyy.manage.model;

import java.util.Collections;
import java.util.List;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import ch.lambdaj.Lambda;

public class ZyyEfProcessDetail extends ZyyBaseObject implements java.io.Serializable {
	private static final long serialVersionUID = 5454155825314635342L;
	
	//alias
	public static final String TABLE_ALIAS = "ZyyEfProcessDetail";
	public static final String ALIAS_ID = "id";
	public static final String ALIAS_PROCESS_ID = "processId";
	public static final String ALIAS_USER_TYPE = "userType";
	public static final String ALIAS_USER_PARAMETER = "userParameter";
	public static final String ALIAS_USER_LEVEL = "userLevel";
	public static final String ALIAS_HAS_PUBLISHED = "hasPublished";
	public static final String ALIAS_CONFIG_CONDITION = "configCondition";
	
	private Long id;
	
	private Long processId;
	
	private Integer userType;
	
	private String userParameter;
	
	private Integer userLevel;
	
	private java.lang.Boolean hasPublished;
	
	private String configCondition;
	
	private List<ZyyEfDetailUser> users;

	public ZyyEfProcessDetail(){
	}

	public ZyyEfProcessDetail(
		Long id
	){
		this.id = id;
	}

	public void setId(Long value) {
		this.id = value;
	}
	
	public Long getId() {
		return this.id;
	}
	public void setProcessId(Long value) {
		this.processId = value;
	}
	
	public Long getProcessId() {
		return this.processId;
	}
	public void setUserType(Integer value) {
		this.userType = value;
	}
	
	public Integer getUserType() {
		return this.userType;
	}
	public void setUserParameter(String value) {
		this.userParameter = value;
	}
	
	public String getUserParameter() {
		return this.userParameter;
	}
	public void setUserLevel(Integer value) {
		this.userLevel = value;
	}
	
	public Integer getUserLevel() {
		return this.userLevel;
	}
	public void setHasPublished(java.lang.Boolean value) {
		this.hasPublished = value;
	}
	
	public java.lang.Boolean getHasPublished() {
		return this.hasPublished;
	}
	public void setConfigCondition(String value) {
		this.configCondition = value;
	}
	
	public String getConfigCondition() {
		return this.configCondition;
	}
	
	private List<ZyyEfDetailUser> zyyEfDetailUsers ;
	public void setZyyEfDetailUsers(List<ZyyEfDetailUser> zyyEfDetailUser){
		this.zyyEfDetailUsers = zyyEfDetailUser;
	}
	
	public List<ZyyEfDetailUser> getZyyEfDetailUsers() {
		return zyyEfDetailUsers;
	}
	
	private ZyyEfProcess zyyEfProcess;
	
	public void setZyyEfProcess(ZyyEfProcess zyyEfProcess){
		this.zyyEfProcess = zyyEfProcess;
	}
	
	public ZyyEfProcess getZyyEfProcess() {
		return zyyEfProcess;
	}

	public String toString() {
		return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
			.append("Id",getId())
			.append("ProcessId",getProcessId())
			.append("UserType",getUserType())
			.append("UserParameter",getUserParameter())
			.append("UserLevel",getUserLevel())
			.append("HasPublished",getHasPublished())
			.append("ConfigCondition",getConfigCondition())
			.toString();
	}
	
	public int hashCode() {
		return new HashCodeBuilder()
			.append(getId())
			.toHashCode();
	}
	
	public boolean equals(Object obj) {
		if(obj instanceof ZyyEfProcessDetail == false) return false;
		if(this == obj) return true;
		ZyyEfProcessDetail other = (ZyyEfProcessDetail)obj;
		return new EqualsBuilder()
			.append(getId(),other.getId())
			.isEquals();
	}

	public List<ZyyEfDetailUser> getUsers() {
		return users;
	}
	
	public List<Long> getUserId() {
		if(this.users == null)
			return Collections.EMPTY_LIST;
		else
			return Lambda.extractProperty(this.users, "userId");
	}

	public void setUsers(List<ZyyEfDetailUser> users) {
		this.users = users;
	}
}

