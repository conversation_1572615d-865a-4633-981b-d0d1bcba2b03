package com.hys.zyy.manage.model;

public class ZyyEnterDeptAttachment extends ZyyBaseObject {

	/**
	 * 
	 */
	private static final long serialVersionUID = -2598936535608277087L;

	private Long id;// ID标示
	private Long deptRecordId;// 入科记录ID
	private String attachmentName;// 附件名称
	private String attachmentUrl;// 附件路径
	private Integer attachmentStatus;//附件状态 0已删除  1未删除
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getDeptRecordId() {
		return deptRecordId;
	}
	public void setDeptRecordId(Long deptRecordId) {
		this.deptRecordId = deptRecordId;
	}
	public String getAttachmentName() {
		return attachmentName;
	}
	public void setAttachmentName(String attachmentName) {
		this.attachmentName = attachmentName;
	}
	public String getAttachmentUrl() {
		return attachmentUrl;
	}
	public void setAttachmentUrl(String attachmentUrl) {
		this.attachmentUrl = attachmentUrl;
	}
	public Integer getAttachmentStatus() {
		return attachmentStatus;
	}
	public void setAttachmentStatus(Integer attachmentStatus) {
		this.attachmentStatus = attachmentStatus;
	}
}
