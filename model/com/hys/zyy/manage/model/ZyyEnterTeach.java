package com.hys.zyy.manage.model;

import java.util.Date;

/**
 * @desc 入科教育实体类
 * <AUTHOR>
 *
 */
public class ZyyEnterTeach extends ZyyBaseObject {

	/**
	 * 
	 */
	private static final long serialVersionUID = -5736878014229538036L;

	private Long id;//ID
	private Long orgId;//医院ID
	private Long deptId;//科室ID
	private Date enterDate;//入科时间
	private Date startDate;//教育结束时间
	private Date endDate;//教育结束时间
	private Long speaker;//演讲人ID
	private String teachPlace;//演讲地点
	private Integer status;//活动状态  0未发布 1已发布
	private Long creater;//创建人ID
	private Date createTime;//创建时间
	private Date updateTime;//更新时间
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getOrgId() {
		return orgId;
	}
	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}
	public Long getDeptId() {
		return deptId;
	}
	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}
	public Date getStartDate() {
		return startDate;
	}
	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}
	public Date getEndDate() {
		return endDate;
	}
	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}
	public Long getSpeaker() {
		return speaker;
	}
	public void setSpeaker(Long speaker) {
		this.speaker = speaker;
	}
	public String getTeachPlace() {
		return teachPlace;
	}
	public void setTeachPlace(String teachPlace) {
		this.teachPlace = teachPlace;
	}
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	public Long getCreater() {
		return creater;
	}
	public void setCreater(Long creater) {
		this.creater = creater;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	public Date getUpdateTime() {
		return updateTime;
	}
	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
	public Date getEnterDate() {
		return enterDate;
	}
	public void setEnterDate(Date enterDate) {
		this.enterDate = enterDate;
	}
}
