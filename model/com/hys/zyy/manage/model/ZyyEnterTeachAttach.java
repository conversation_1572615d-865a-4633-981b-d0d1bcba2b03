package com.hys.zyy.manage.model;

import java.util.Date;

/**
 * Created by <PERSON><PERSON>
 * @desc 入科教育附件实体类
 */
public class ZyyEnterTeachAttach extends ZyyBaseObject {
	private static final long serialVersionUID = -2630976762093510949L;
	private Long id; // ID
	private Long teachId; // 入科教育活动ID
	private String attachmentName;// 附件名称
	private String attachmentUrl;// 附件地址
	private Integer attachmentStatus;// 附件状态 0已删除 1未删除
	private Date createDate;
	/*
	 * 文件存储在本地服务器
	 */
	private Boolean isLocalFile;
	/*
	 * 文件存储在七牛云
	 */
	private Boolean isQiNiuFile;
	private Boolean isImg;
	private Boolean isPdf;
	private String thumbnail;
	public Date getCreateDate() {
		return createDate;
	}
	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getTeachId() {
		return teachId;
	}
	public void setTeachId(Long teachId) {
		this.teachId = teachId;
	}
	public String getAttachmentName() {
		return attachmentName;
	}
	public void setAttachmentName(String attachmentName) {
		this.attachmentName = attachmentName;
	}
	public String getAttachmentUrl() {
		return attachmentUrl;
	}
	public void setAttachmentUrl(String attachmentUrl) {
		this.attachmentUrl = attachmentUrl;
	}
	public Integer getAttachmentStatus() {
		return attachmentStatus;
	}
	public void setAttachmentStatus(Integer attachmentStatus) {
		this.attachmentStatus = attachmentStatus;
	}

	public Boolean getIsLocalFile() {
		return isLocalFile;
	}

	public void setIsLocalFile(Boolean isLocalFile) {
		this.isLocalFile = isLocalFile;
	}

	public Boolean getIsQiNiuFile() {
		return isQiNiuFile;
	}

	public void setIsQiNiuFile(Boolean isQiNiuFile) {
		this.isQiNiuFile = isQiNiuFile;
	}
	
	public Boolean getIsImg() {
		return isImg;
	}

	public void setIsImg(Boolean img) {
		isImg = img;
	}

	public Boolean getIsPdf() {
		return isPdf;
	}

	public void setIsPdf(Boolean pdf) {
		isPdf = pdf;
	}

	public String getThumbnail() {
		return thumbnail;
	}

	public void setThumbnail(String thumbnail) {
		this.thumbnail = thumbnail;
	}
}