package com.hys.zyy.manage.model;

import java.util.Date;

/**
 * Created by LiLei
 *
 * @desc 入科教育人员表
 */
public class ZyyEnterTeachUser extends ZyyBaseObject {

	/**
	 *
	 */
	private static final long serialVersionUID = 6032850485740277927L;

	private Long id;// ID
	private Long teachId;// 入科教育ID
	private Long userId;// 参与人ID
	private Integer attendStatus;// 是否出勤 0 未上报 1 出勤 2未出勤
	private Integer attendStatusTwo;// 活动中是否出勤 0 未上报 1 出勤 2未出勤
	private Long emailFalg;// 是否发送邮件 0未发 1已发
	// 更新时间
	private Date updateDate;

	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getTeachId() {
		return teachId;
	}
	public void setTeachId(Long teachId) {
		this.teachId = teachId;
	}
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	public Integer getAttendStatus() {
		return attendStatus;
	}
	public void setAttendStatus(Integer attendStatus) {
		this.attendStatus = attendStatus;
	}
	public Long getEmailFalg() {
		return emailFalg;
	}
	public void setEmailFalg(Long emailFalg) {
		this.emailFalg = emailFalg;
	}
	public Date getUpdateDate() {
		return updateDate;
	}
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	public Integer getAttendStatusTwo() {
		return attendStatusTwo;
	}

	public void setAttendStatusTwo(Integer attendStatusTwo) {
		this.attendStatusTwo = attendStatusTwo;
	}
}
