package com.hys.zyy.manage.model;

import java.util.Date;


/**
 * 评价查询工具类
 */
public class ZyyEvaluateResidencyVO extends ZyyBaseObject {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	private Long id;
	
	private Date startDate;
	
	private Date endDate;
	
	private Long deptId;
	
	private String deptName;
	
	private Date cycleStartDate;
	
	private Date cycleEndDate;
	
	private Long cycleTimelineId;
	
	private Date enterDate;
	
	private Date leaveDate;
	
	
	private Integer fillinSdate;
	
	private Integer fillinEdate;
	
	/**
	 * 带教工号
	 */
	private String jobNumber;
	/**
	 * 用户评价表的ID
	 */
	private Long userEvaluateId;
	/**
	 * 是否可评价  1-可评价  2-未在评价时间段内,不可评价
	 */
	private Integer isEvaluate;
	/**
	 * 
	 */
	private String timeSection;
	/**
	 * 年份
	 */
	private String year;
	/**
	 * 学科名称
	 */
	private String baseAliasName;
	/**
	 * 学员姓名
	 */
	private String resiName;
	
	private Integer cycleStatus;
	/**
	 * 带教学员的时间段
	 */
	private String teachingTimeSection;
	
	
	//培训专业
	private String aliasName;
		
	//培训年限 1 一年制 2 二年制 3 三年制
	private Integer schoolSystem;
	
	//人员类型 1单位人 2社会人 3学位衔接
	private Integer residencySource;
	
	//姓名
	private String realName;
	
	//证件号码
	private String certificateNo;
	
	//手机号码
	private String mobilNumber;
	
	//评价时间
	private String evaluateDate;
	
	//评价分数
	private Integer itemScore;
	
	//职称
	private Integer userPostTitle;
	
	private String evaluateNam;
	
	private ZyyUserExtendVO zyyUser;//当前登陆人的信息
	private Integer qEvalStatus;//1 查询已评价   2查询未评价
	private String qStartTime;//评价时间段开始（与轮转相关）
	private String qEndTime;//评价时间段结束（与轮转无关）
	private Long orgId;//医院ID
	private String orgName;//医院名称
	private Long evalTableConfigId; //新版评价表ID
	private String evalLimitTime;//评价截至时间
	private Long baseId;//专业基地ID
	private Long teacherId;  //带教ID
	private Long tutorId;//导师ID
	private ZyyEvaluateTableConfig evalConfig;//评价表配置
	//入科时间
	private String startDateStr;
	//出科时间
	private String endDateStr;
	//是否仅查询总数 1 - 是 ， 空 - 否  app需要优化速度
	private Integer onlyTotalCount;
	
	public Integer getUserPostTitle() {
		return userPostTitle;
	}
	public void setUserPostTitle(Integer userPostTitle) {
		this.userPostTitle = userPostTitle;
	}
	public String getEvaluateNam() {
		return evaluateNam;
	}
	public void setEvaluateNam(String evaluateNam) {
		this.evaluateNam = evaluateNam;
	}
	private Long userId;
	
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	public String getAliasName() {
		return aliasName;
	}
	public void setAliasName(String aliasName) {
		this.aliasName = aliasName;
	}
	public Integer getSchoolSystem() {
		return schoolSystem;
	}
	public void setSchoolSystem(Integer schoolSystem) {
		this.schoolSystem = schoolSystem;
	}
	public Integer getResidencySource() {
		return residencySource;
	}
	public void setResidencySource(Integer residencySource) {
		this.residencySource = residencySource;
	}
	public String getRealName() {
		return realName;
	}
	public void setRealName(String realName) {
		this.realName = realName;
	}
	public String getCertificateNo() {
		return certificateNo;
	}
	public void setCertificateNo(String certificateNo) {
		this.certificateNo = certificateNo;
	}
	public String getMobilNumber() {
		return mobilNumber;
	}
	public void setMobilNumber(String mobilNumber) {
		this.mobilNumber = mobilNumber;
	}
	
	public String getEvaluateDate() {
		return evaluateDate;
	}
	public void setEvaluateDate(String evaluateDate) {
		this.evaluateDate = evaluateDate;
	}
	public Integer getItemScore() {
		return itemScore;
	}
	public void setItemScore(Integer itemScore) {
		this.itemScore = itemScore;
	}
	public String getTeachingTimeSection() {
		return teachingTimeSection;
	}
	public void setTeachingTimeSection(String teachingTimeSection) {
		this.teachingTimeSection = teachingTimeSection;
	}
	public Integer getCycleStatus() {
		return cycleStatus;
	}
	public void setCycleStatus(Integer cycleStatus) {
		this.cycleStatus = cycleStatus;
	}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Date getStartDate() {
		return startDate;
	}
	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}
	public Date getEndDate() {
		return endDate;
	}
	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}
	public Long getDeptId() {
		return deptId;
	}
	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}
	public String getDeptName() {
		return deptName;
	}
	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}
	public Date getCycleStartDate() {
		return cycleStartDate;
	}
	public void setCycleStartDate(Date cycleStartDate) {
		this.cycleStartDate = cycleStartDate;
	}
	public Date getCycleEndDate() {
		return cycleEndDate;
	}
	public void setCycleEndDate(Date cycleEndDate) {
		this.cycleEndDate = cycleEndDate;
	}
	public Long getCycleTimelineId() {
		return cycleTimelineId;
	}
	public void setCycleTimelineId(Long cycleTimelineId) {
		this.cycleTimelineId = cycleTimelineId;
	}
	public Date getEnterDate() {
		return enterDate;
	}
	public void setEnterDate(Date enterDate) {
		this.enterDate = enterDate;
	}
	public Date getLeaveDate() {
		return leaveDate;
	}
	public void setLeaveDate(Date leaveDate) {
		this.leaveDate = leaveDate;
	}
	public String getJobNumber() {
		return jobNumber;
	}
	public void setJobNumber(String jobNumber) {
		this.jobNumber = jobNumber;
	}
	public Long getUserEvaluateId() {
		return userEvaluateId;
	}
	public void setUserEvaluateId(Long userEvaluateId) {
		this.userEvaluateId = userEvaluateId;
	}
	public Integer getIsEvaluate() {
		return isEvaluate;
	}
	public void setIsEvaluate(Integer isEvaluate) {
		this.isEvaluate = isEvaluate;
	}
	public String getTimeSection() {
		return timeSection;
	}
	public void setTimeSection(String timeSection) {
		this.timeSection = timeSection;
	}
	public String getYear() {
		return year;
	}
	public void setYear(String year) {
		this.year = year;
	}
	public String getBaseAliasName() {
		return baseAliasName;
	}
	public void setBaseAliasName(String baseAliasName) {
		this.baseAliasName = baseAliasName;
	}
	public String getResiName() {
		return resiName;
	}
	public void setResiName(String resiName) {
		this.resiName = resiName;
	}
	public Integer getFillinSdate() {
		return fillinSdate;
	}
	public void setFillinSdate(Integer fillinSdate) {
		this.fillinSdate = fillinSdate;
	}
	public Integer getFillinEdate() {
		return fillinEdate;
	}
	public void setFillinEdate(Integer fillinEdate) {
		this.fillinEdate = fillinEdate;
	}
	public Long getOrgId() {
		return orgId;
	}
	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}
	public Long getEvalTableConfigId() {
		return evalTableConfigId;
	}
	public void setEvalTableConfigId(Long evalTableConfigId) {
		this.evalTableConfigId = evalTableConfigId;
	}
	public Integer getqEvalStatus() {
		return qEvalStatus;
	}
	public void setqEvalStatus(Integer qEvalStatus) {
		this.qEvalStatus = qEvalStatus;
	}
	public String getEvalLimitTime() {
		return evalLimitTime;
	}
	public void setEvalLimitTime(String evalLimitTime) {
		this.evalLimitTime = evalLimitTime;
	}
	public Long getBaseId() {
		return baseId;
	}
	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}
	public String getqStartTime() {
		return qStartTime;
	}
	public void setqStartTime(String qStartTime) {
		this.qStartTime = qStartTime;
	}
	public String getqEndTime() {
		return qEndTime;
	}
	public void setqEndTime(String qEndTime) {
		this.qEndTime = qEndTime;
	}
	public ZyyEvaluateTableConfig getEvalConfig() {
		return evalConfig;
	}
	public void setEvalConfig(ZyyEvaluateTableConfig evalConfig) {
		this.evalConfig = evalConfig;
	}
	public Long getTeacherId() {
		return teacherId;
	}
	public void setTeacherId(Long teacherId) {
		this.teacherId = teacherId;
	}
	public ZyyUserExtendVO getZyyUser() {
		return zyyUser;
	}
	public void setZyyUser(ZyyUserExtendVO zyyUser) {
		this.zyyUser = zyyUser;
	}
	public Long getTutorId() {
		return tutorId;
	}
	public void setTutorId(Long tutorId) {
		this.tutorId = tutorId;
	}
	public String getOrgName() {
		return orgName;
	}
	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}
	public String getStartDateStr() {
		return startDateStr;
	}
	public void setStartDateStr(String startDateStr) {
		this.startDateStr = startDateStr;
	}
	public String getEndDateStr() {
		return endDateStr;
	}
	public void setEndDateStr(String endDateStr) {
		this.endDateStr = endDateStr;
	}
	public Integer getOnlyTotalCount() {
		return onlyTotalCount;
	}
	public void setOnlyTotalCount(Integer onlyTotalCount) {
		this.onlyTotalCount = onlyTotalCount;
	}
}
