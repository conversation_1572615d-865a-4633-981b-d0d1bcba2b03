package com.hys.zyy.manage.model;

import java.util.ArrayList;
import java.util.List;

/**
 * 评价统计结果对象 通用
 * <AUTHOR>
 */

public class ZyyEvaluateResultVo {
	
	/**
	 * 以下字段为按学员统计 时候的字段
	 */
	private Long orgId;
	private Long userid;
	private Long deptId;
	private String realName;
	private String certificateNo;
	private String aliasName; 
	private Integer xypjdj;  
	private Integer djpjxy;
	private Integer xypjks;  
	private Integer kspjxy ;
	/*
	 * 护士评价学员
	 */
	private Integer hspjxy;
	/*
	 * 病人评价学员
	 */
	private Integer brpjxy;
	
	/**
	 * 以下字段是查看学员评价详情的
	 * 评价者
	 * 评价表
	 * 评价时间
	 * 满分得分
	 */
	private Integer id;//此ID是用户评价表的ID
	private String evaluateName;
	private String evaluateTableName;
	private String evaluateDate;
	private Double evaluateTotalScore;
	private Double evaluateGetScore;
	
	
	
	
	/**
	 * 以下字段为评价详情展示的时候使用  
	 */
	private String parentColumnName ;
	private String columnName ;
	private String scoreItem ;
	private Integer ScoreItemId ;//所得分的ID
	private Integer itemScore;//得分值
	
	private Integer itemTotalScore;//总分
	
	private Integer maxScore;//每个指标项下的最高分
	
	private List<String> scoreList = new ArrayList<String>();//分值范围
	private int ckIndex;//得分ID的索引值
	
	String otherTips;//其他意见和建议
	
	
	
	/**
	 * 以下字段是科室统计用的
	 */
	private String deptName;//科室名称
	private Long teacherId;//带教ID
	
	
	private Integer userType ;//用户类型 对应在Constans类中查看
	
	Integer tableType;
	Long stuId;
	
	/**
	 * @desc 评价表配置ID
	 */
	private Long configId;
	
	public Long getUserid() {
		return userid;
	}
	public void setUserid(Long userid) {
		this.userid = userid;
	}
	public String getRealName() {
		return realName;
	}
	public void setRealName(String realName) {
		this.realName = realName;
	}
	public String getCertificateNo() {
		return certificateNo;
	}
	public void setCertificateNo(String certificateNo) {
		this.certificateNo = certificateNo;
	}
	public String getAliasName() {
		return aliasName;
	}
	public void setAliasName(String aliasName) {
		this.aliasName = aliasName;
	}
	public Integer getXypjdj() {
		return xypjdj;
	}
	public void setXypjdj(Integer xypjdj) {
		this.xypjdj = xypjdj;
	}
	public Integer getDjpjxy() {
		return djpjxy;
	}
	public void setDjpjxy(Integer djpjxy) {
		this.djpjxy = djpjxy;
	}
	public Integer getXypjks() {
		return xypjks;
	}
	public void setXypjks(Integer xypjks) {
		this.xypjks = xypjks;
	}
	public Integer getKspjxy() {
		return kspjxy;
	}
	public void setKspjxy(Integer kspjxy) {
		this.kspjxy = kspjxy;
	}
	public Long getOrgId() {
		return orgId;
	}
	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}
	public Long getDeptId() {
		return deptId;
	}
	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}
	public String getEvaluateName() {
		return evaluateName;
	}
	public void setEvaluateName(String evaluateName) {
		this.evaluateName = evaluateName;
	}
	public String getEvaluateTableName() {
		return evaluateTableName;
	}
	public void setEvaluateTableName(String evaluateTableName) {
		this.evaluateTableName = evaluateTableName;
	}
	public String getEvaluateDate() {
		return evaluateDate;
	}
	public void setEvaluateDate(String evaluateDate) {
		this.evaluateDate = evaluateDate;
	}
	
	public Double getEvaluateTotalScore() {
		return evaluateTotalScore;
	}
	public void setEvaluateTotalScore(Double evaluateTotalScore) {
		this.evaluateTotalScore = evaluateTotalScore;
	}
	public Double getEvaluateGetScore() {
		return evaluateGetScore;
	}
	public void setEvaluateGetScore(Double evaluateGetScore) {
		this.evaluateGetScore = evaluateGetScore;
	}
	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	public String getDeptName() {
		return deptName;
	}
	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}
	public Integer getUserType() {
		return userType;
	}
	public void setUserType(Integer userType) {
		this.userType = userType;
	}
	public Long getTeacherId() {
		return teacherId;
	}
	public void setTeacherId(Long teacherId) {
		this.teacherId = teacherId;
	}
	public String getParentColumnName() {
		return parentColumnName;
	}
	public void setParentColumnName(String parentColumnName) {
		this.parentColumnName = parentColumnName;
	}
	public String getScoreItem() {
		return scoreItem;
	}
	public void setScoreItem(String scoreItem) {
		this.scoreItem = scoreItem;
	}
	public Integer getScoreItemId() {
		return ScoreItemId;
	}
	public void setScoreItemId(Integer scoreItemId) {
		ScoreItemId = scoreItemId;
	}
	public String getColumnName() {
		return columnName;
	}
	public void setColumnName(String columnName) {
		this.columnName = columnName;
	}
	public List<String> getScoreList() {
		return scoreList;
	}
	public void setScoreList(List<String> scoreList) {
		this.scoreList = scoreList;
	}
	public int getCkIndex() {
		return ckIndex;
	}
	public void setCkIndex(int ckIndex) {
		this.ckIndex = ckIndex;
	}
	public Integer getTableType() {
		return tableType;
	}
	public void setTableType(Integer tableType) {
		this.tableType = tableType;
	}
	public Long getStuId() {
		return stuId;
	}
	public void setStuId(Long stuId) {
		this.stuId = stuId;
	}
	public Integer getItemScore() {
		return itemScore;
	}
	public void setItemScore(Integer itemScore) {
		this.itemScore = itemScore;
	}
	public Integer getItemTotalScore() {
		return itemTotalScore;
	}
	public void setItemTotalScore(Integer itemTotalScore) {
		this.itemTotalScore = itemTotalScore;
	}
	public Integer getMaxScore() {
		return maxScore;
	}
	public void setMaxScore(Integer maxScore) {
		this.maxScore = maxScore;
	}
	public String getOtherTips() {
		return otherTips;
	}
	public void setOtherTips(String otherTips) {
		this.otherTips = otherTips;
	}
	public Integer getHspjxy() {
		return hspjxy;
	}
	public void setHspjxy(Integer hspjxy) {
		this.hspjxy = hspjxy;
	}
	public Integer getBrpjxy() {
		return brpjxy;
	}
	public void setBrpjxy(Integer brpjxy) {
		this.brpjxy = brpjxy;
	}
	public Long getConfigId() {
		return configId;
	}
	public void setConfigId(Long configId) {
		this.configId = configId;
	}
}
