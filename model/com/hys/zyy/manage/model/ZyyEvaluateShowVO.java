package com.hys.zyy.manage.model;

import java.util.Date;

/**
 * 医院查看评价表用的vo类
 * <AUTHOR>
 */
public class ZyyEvaluateShowVO extends ZyyBaseObject {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	/**
	 * zyy_cycle_teacher表的id
	 */
	private Long id;
	/**
	 * 带教老师名称
	 */
	private String teacherName;
	/**
	 * 工号
	 */
	private String jobNumber;
	/**
	 * 科室名称
	 */
	private String deptName;
	/**
	 * 合并带教的时间段
	 */
	private String teachingTimeSection;
	/**
	 * 
	 */
	private String userName;
	
	private Long deptId;
	
	private String certificateNo;
	/**
	 * 年份
	 */
	private String year;
	/**
	 * 学科名称
	 */
	private String baseName;
	/**
	 * 评价表名称
	 */
	private String tableName;
	/**
	 * zyy_cycle_timeline表中的时间段
	 */
	private String timeSection;
	/**
	 * 学员id
	 */
	private Long userId;
	/**
	 * 带教id
	 */
	private Long teacherId;
	/**
	 * 用户评价表的ID
	 */
	private Long userEvaluateId;
	/**
	 * 是否可评价  1-可评价(在评价时间段之内)  2-未在评价时间段内,不可评价
	 */
	private Integer isEvaluate;
	/**
	 * 评价表总分
	 */
	private Integer score;
	/**
	 * 评价时间
	 */
	private Date evaluateDate;
	
	private Date cycleStartDate;
	
	private Date cycleEndDate;
	/**
	 * 轮转状态  1,正在轮转 2,尚未轮转 3,结束轮转
	 */
	private Integer CycleStatus;
	/**
	 * 带教状态  1-正在带教  2-尚未带教开始 3-带教结束
	 */
	private Integer teachingStatus;
	
	public Integer getCycleStatus() {
		return CycleStatus;
	}
	public void setCycleStatus(Integer cycleStatus) {
		CycleStatus = cycleStatus;
	}
	public Integer getTeachingStatus() {
		return teachingStatus;
	}
	public void setTeachingStatus(Integer teachingStatus) {
		this.teachingStatus = teachingStatus;
	}
	public Date getCycleStartDate() {
		return cycleStartDate;
	}
	public void setCycleStartDate(Date cycleStartDate) {
		this.cycleStartDate = cycleStartDate;
	}
	public Date getCycleEndDate() {
		return cycleEndDate;
	}
	public void setCycleEndDate(Date cycleEndDate) {
		this.cycleEndDate = cycleEndDate;
	}
	public Date getEvaluateDate() {
		return evaluateDate;
	}
	public void setEvaluateDate(Date evaluateDate) {
		this.evaluateDate = evaluateDate;
	}
	public Integer getScore() {
		return score;
	}
	public void setScore(Integer score) {
		this.score = score;
	}
	public Long getUserEvaluateId() {
		return userEvaluateId;
	}
	public void setUserEvaluateId(Long userEvaluateId) {
		this.userEvaluateId = userEvaluateId;
	}
	public Integer getIsEvaluate() {
		return isEvaluate;
	}
	public void setIsEvaluate(Integer isEvaluate) {
		this.isEvaluate = isEvaluate;
	}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getTeacherName() {
		return teacherName;
	}
	public void setTeacherName(String teacherName) {
		this.teacherName = teacherName;
	}
	public String getJobNumber() {
		return jobNumber;
	}
	public void setJobNumber(String jobNumber) {
		this.jobNumber = jobNumber;
	}
	public String getDeptName() {
		return deptName;
	}
	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}
	public String getTeachingTimeSection() {
		return teachingTimeSection;
	}
	public void setTeachingTimeSection(String teachingTimeSection) {
		this.teachingTimeSection = teachingTimeSection;
	}
	public String getUserName() {
		return userName;
	}
	public void setUserName(String userName) {
		this.userName = userName;
	}
	public Long getDeptId() {
		return deptId;
	}
	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}
	public String getCertificateNo() {
		return certificateNo;
	}
	public void setCertificateNo(String certificateNo) {
		this.certificateNo = certificateNo;
	}
	public String getYear() {
		return year;
	}
	public void setYear(String year) {
		this.year = year;
	}
	public String getBaseName() {
		return baseName;
	}
	public void setBaseName(String baseName) {
		this.baseName = baseName;
	}
	public String getTableName() {
		return tableName;
	}
	public void setTableName(String tableName) {
		this.tableName = tableName;
	}
	public String getTimeSection() {
		return timeSection;
	}
	public void setTimeSection(String timeSection) {
		this.timeSection = timeSection;
	}
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	public Long getTeacherId() {
		return teacherId;
	}
	public void setTeacherId(Long teacherId) {
		this.teacherId = teacherId;
	}
	
}
