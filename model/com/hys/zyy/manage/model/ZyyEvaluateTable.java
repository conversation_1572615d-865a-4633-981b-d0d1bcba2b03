package com.hys.zyy.manage.model;

/**
 * 评价表
 * <AUTHOR>
 */
public class ZyyEvaluateTable extends ZyyBaseObject {

	private static final long serialVersionUID = 1287549989754950673L;

	private Long id;
	/**
	 * 表名称
	 */
	private String tableName;
	/**
	 * 评价表类型：1：学员评价带教  2：带教评价学员 3：学员评价科室 4：科室评价学员
	 */
	private Integer tableType;
	/**
	 * 表状态:1:启用 2:禁用
	 */
	private Integer tableStatus;
	/**
	 * 评价开始时间
	 */
	private Integer tableFillinStartDate;
	/**
	 * 评价结束时间
	 */
	private Integer tableFillinEndDate;
	/**
	 * 创建机构
	 */
	private Long createOrg;
	/**
	 * 创建用户ID
	 */
	private Long createUser;
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getTableName() {
		return tableName;
	}
	public void setTableName(String tableName) {
		this.tableName = tableName;
	}
	public Integer getTableType() {
		return tableType;
	}
	public void setTableType(Integer tableType) {
		this.tableType = tableType;
	}
	public Integer getTableStatus() {
		return tableStatus;
	}
	public void setTableStatus(Integer tableStatus) {
		this.tableStatus = tableStatus;
	}
	public Integer getTableFillinStartDate() {
		return tableFillinStartDate;
	}
	public void setTableFillinStartDate(Integer tableFillinStartDate) {
		this.tableFillinStartDate = tableFillinStartDate;
	}
	public Integer getTableFillinEndDate() {
		return tableFillinEndDate;
	}
	public void setTableFillinEndDate(Integer tableFillinEndDate) {
		this.tableFillinEndDate = tableFillinEndDate;
	}
	public Long getCreateOrg() {
		return createOrg;
	}
	public void setCreateOrg(Long createOrg) {
		this.createOrg = createOrg;
	}
	public Long getCreateUser() {
		return createUser;
	}
	public void setCreateUser(Long createUser) {
		this.createUser = createUser;
	}

}
