package com.hys.zyy.manage.model;

import java.util.Date;

/**
 * 评价表配置
 * <AUTHOR>
 * @date 2018-8-20下午4:41:08
 */
public class ZyyEvaluateTableConfig extends ZyyBaseObject {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private Long id;	
	//	评价表名称	
	private String tableName	;
	//  评价表类型       1 对评价学员 2 对带教评价 3 对责任导师评价 4 对科室评价 5 对专业基地评价，6培训基地
	private Integer tableType ;
	private String tableTypeStr;
	
	//	评价者类型	1带教老师 2 科室 3责任导师 4专业基地 5培训基地 6患者 7护士 8 住院医师
	private Integer evaluateType ;
	//	与轮转相关	1 是 2 否
	private Integer cycleType ;
	//	轮转时间开始	1 前 2 后
	private Integer cycleStart	; 
	//	轮转时间结束 1 前 2 后	
	private Integer cycleEnd;	
	//	轮转时间开始天数	
	private Integer cycleStartDays ;	
	//	轮转时间结束天数	
	private Integer cycleEndDays;	
	//	开始时间	
	private Date startDate	;
	private String startDateStr	;
	//	结束时间	
	private Date endDate;	
	private String endDateStr;
	//	是否重复	1 是 2 否
	private Integer repeatType	; 
	//	是否匿名	1 是 2 否
	private Integer anonymousType	;
	//	评价模板ID	
	private Long tableNewId ;
	//	评价表状态	1 开启 2 关闭
	private Integer status ;
	
	private String statusStr ;
	//	创建者省份ID	
	private Long createProvinceId	;
	//	创建者医院ID	
	private Long createOrgId	;
	//	创建者用户ID	
	private Long createUserId	;
	//	创建时间	
	private Date createDate ;
	//发布频率 1 ： 仅一次  2 ：每年发布
	private Integer publishFrequency;
	//频率开始日期
	private String startDateFrequency;
	//频率结束日期
	private String endDateFrequency;
	
	private String templetName;
	
	
	
	
	public String getTempletName() {
		return templetName;
	}
	public void setTempletName(String templetName) {
		this.templetName = templetName;
	}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getTableName() {
		return tableName;
	}
	public void setTableName(String tableName) {
		this.tableName = tableName;
	}
	public Integer getTableType() {
		return tableType;
	}
	public void setTableType(Integer tableType) {
		this.tableType = tableType;
	}
	public Integer getEvaluateType() {
		return evaluateType;
	}
	public void setEvaluateType(Integer evaluateType) {
		this.evaluateType = evaluateType;
	}
	public Integer getCycleType() {
		return cycleType;
	}
	public void setCycleType(Integer cycleType) {
		this.cycleType = cycleType;
	}
	public Integer getCycleStart() {
		return cycleStart;
	}
	public void setCycleStart(Integer cycleStart) {
		this.cycleStart = cycleStart;
	}
	public Integer getCycleEnd() {
		return cycleEnd;
	}
	public void setCycleEnd(Integer cycleEnd) {
		this.cycleEnd = cycleEnd;
	}
	public Integer getCycleStartDays() {
		return cycleStartDays;
	}
	public void setCycleStartDays(Integer cycleStartDays) {
		this.cycleStartDays = cycleStartDays;
	}
	public Integer getCycleEndDays() {
		return cycleEndDays;
	}
	public void setCycleEndDays(Integer cycleEndDays) {
		this.cycleEndDays = cycleEndDays;
	}
	public Date getStartDate() {
		return startDate;
	}
	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}
	public Date getEndDate() {
		return endDate;
	}
	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}
	public Integer getRepeatType() {
		return repeatType;
	}
	public void setRepeatType(Integer repeatType) {
		this.repeatType = repeatType;
	}
	public Integer getAnonymousType() {
		return anonymousType;
	}
	public void setAnonymousType(Integer anonymousType) {
		this.anonymousType = anonymousType;
	}
	public Long getTableNewId() {
		return tableNewId;
	}
	public void setTableNewId(Long tableNewId) {
		this.tableNewId = tableNewId;
	}
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
		if(status!=null){
			if(status==1){
				this.statusStr = "开启";
			}else if(status==2){
				this.statusStr = "禁用";
			}
		}
	}
	public Long getCreateProvinceId() {
		return createProvinceId;
	}
	public void setCreateProvinceId(Long createProvinceId) {
		this.createProvinceId = createProvinceId;
	}
	public Long getCreateOrgId() {
		return createOrgId;
	}
	public void setCreateOrgId(Long createOrgId) {
		this.createOrgId = createOrgId;
	}
	public Long getCreateUserId() {
		return createUserId;
	}
	public void setCreateUserId(Long createUserId) {
		this.createUserId = createUserId;
	}
	public Date getCreateDate() {
		return createDate;
	}
	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}
	public String getStartDateStr() {
		return startDateStr;
	}
	public void setStartDateStr(String startDateStr) {
		this.startDateStr = startDateStr;
	}
	public String getEndDateStr() {
		return endDateStr;
	}
	public void setEndDateStr(String endDateStr) {
		this.endDateStr = endDateStr;
	}
	public Integer getPublishFrequency() {
		return publishFrequency;
	}
	public void setPublishFrequency(Integer publishFrequency) {
		this.publishFrequency = publishFrequency;
	}
	public String getStartDateFrequency() {
		return startDateFrequency;
	}
	public void setStartDateFrequency(String startDateFrequency) {
		this.startDateFrequency = startDateFrequency;
	}
	public String getEndDateFrequency() {
		return endDateFrequency;
	}
	public void setEndDateFrequency(String endDateFrequency) {
		this.endDateFrequency = endDateFrequency;
	}
	public String getStatusStr() {
		return statusStr;
	}
	public String getTableTypeStr() {
		String str = "";
		//1 对评价学员 2 对带教评价 3 对责任导师评价 4 对科室评价 5 对专业基地评价，6培训基地
		switch (tableType) {
		case 1:
			str = "对学员评价";
			break;
		case 2:
			str = "对带教评价";		
			break;
		case 3:
			str = "对责任导师评价";
			break;
		case 4:
			str = "对科室评价";
			break;
		case 5:
			str = "对专业基地评价";
		break;
		case 6:
			str = "培训基地";
		break;
		default:
			str = "";
			break;
		}
		tableTypeStr = str;
		return tableTypeStr;
	}
	public void setTableTypeStr(String tableTypeStr) {
		this.tableTypeStr = tableTypeStr;
	}
	
	
}
