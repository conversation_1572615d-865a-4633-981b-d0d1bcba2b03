package com.hys.zyy.manage.model;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class ZyyEvaluateTableNewVO extends ZyyEvaluateTableNew {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 7142319687691096258L;
	
	/**
	 * 总分数
	 */
	private Integer totalScore;
	
	private String deptIdStr;
	
	private Long deptId;
	
	private Integer zyyUserType;//查询人员身份   5:医院身份  9:科室身份  11:带教身份
	
	private Long teacherId;//带教ID
	/**
	 * 评价表的列的集合
	 */
	private List<ZyyCustomizeColumnsNewVO> tableColumns;
	
	private String deptName;
	/**
	 * 评价数量
	 */
	private Integer evaluateNum;
	
	private String tableName;

	private String otherTips;//评价备注
	
	//被评论用户id
	private Long evaluatedUserId;
	//没用
	private Long tableId;
	//时间
	private String leaveTime;
	
	public String getTableName() {
		return tableName;
	}
	public void setTableName(String tableName) {
		this.tableName = tableName;
	}
	public Integer getEvaluateNum() {
		return evaluateNum;
	}
	public void setEvaluateNum(Integer evaluateNum) {
		this.evaluateNum = evaluateNum;
	}
	public List<ZyyCustomizeColumnsNewVO> getTableColumns() {
		return tableColumns;
	}
	public void setTableColumns(List<ZyyCustomizeColumnsNewVO> tableColumns) {
		this.tableColumns = tableColumns;
	}
	
	public Integer getTotalScore() {
		return totalScore;
	}
	public void setTotalScore(Integer totalScore) {
		this.totalScore = totalScore;
	}
	public Integer getDeptNum() {
		if (deptIdStr == null || "".equals(deptIdStr)) {
			return 0;
		}
		Set<Long> deptIds = new HashSet<Long>();
		String[] deptArr = deptIdStr.split(",");
		for (String deptIdStr : deptArr) {
			try {
				deptIds.add(Long.parseLong(deptIdStr));
			} catch (Exception ex) {
				ex.printStackTrace();
				continue;
			}
		}
		return deptIds.size();
	}
	public String getDeptIdStr() {
		return deptIdStr;
	}
	public void setDeptIdStr(String deptIdStr) {
		this.deptIdStr = deptIdStr;
	}
	public Long getDeptId() {
		return deptId;
	}
	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}
	public String getDeptName() {
		return deptName;
	}
	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}
	public Integer getZyyUserType() {
		return zyyUserType;
	}
	public void setZyyUserType(Integer zyyUserType) {
		this.zyyUserType = zyyUserType;
	}
	public Long getTeacherId() {
		return teacherId;
	}
	public void setTeacherId(Long teacherId) {
		this.teacherId = teacherId;
	}
	public String getOtherTips() {
		return otherTips;
	}
	public void setOtherTips(String otherTips) {
		this.otherTips = otherTips;
	}
	public Long getEvaluatedUserId() {
		return evaluatedUserId;
	}
	public void setEvaluatedUserId(Long evaluatedUserId) {
		this.evaluatedUserId = evaluatedUserId;
	}
	public Long getTableId() {
		return tableId;
	}
	public void setTableId(Long tableId) {
		this.tableId = tableId;
	}
	public String getLeaveTime() {
		return leaveTime;
	}
	public void setLeaveTime(String leaveTime) {
		this.leaveTime = leaveTime;
	}
}
