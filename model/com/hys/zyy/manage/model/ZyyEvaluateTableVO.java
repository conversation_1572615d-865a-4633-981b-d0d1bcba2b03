package com.hys.zyy.manage.model;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class ZyyEvaluateTableVO extends ZyyEvaluateTable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -4831863603304117953L;
	/**
	 * 总分数
	 */
	private Integer totalScore;
	/**
	 * 使用科室的数量
	 */
	private Integer deptNum;
	
	private String deptIdStr;
	
	private Long deptId;
	
	private Integer zyyUserType;//查询人员身份   5:医院身份  9:科室身份  11:带教身份
	
	private Long teacherId;//带教ID
	/**
	 * 评价表的列的集合
	 */
	private List<ZyyCustomizeColumnsVO> tableColumns;
	
	private String deptName;
	/**
	 * 评价数量
	 */
	private Integer evaluateNum;
	
	private String tableName;

	public String getTableName() {
		return tableName;
	}
	public void setTableName(String tableName) {
		this.tableName = tableName;
	}
	public Integer getEvaluateNum() {
		return evaluateNum;
	}
	public void setEvaluateNum(Integer evaluateNum) {
		this.evaluateNum = evaluateNum;
	}
	public List<ZyyCustomizeColumnsVO> getTableColumns() {
		return tableColumns;
	}
	public void setTableColumns(List<ZyyCustomizeColumnsVO> tableColumns) {
		this.tableColumns = tableColumns;
	}
	
	public Integer getTotalScore() {
		return totalScore;
	}
	public void setTotalScore(Integer totalScore) {
		this.totalScore = totalScore;
	}
	public Integer getDeptNum() {
		if (deptIdStr == null || "".equals(deptIdStr)) {
			return 0;
		}
		Set<Long> deptIds = new HashSet<Long>();
		String[] deptArr = deptIdStr.split(",");
		for (String deptIdStr : deptArr) {
			try {
				deptIds.add(Long.parseLong(deptIdStr));
			} catch (Exception ex) {
				ex.printStackTrace();
				continue;
			}
		}
		return deptIds.size();
	}
	public String getDeptIdStr() {
		return deptIdStr;
	}
	public void setDeptIdStr(String deptIdStr) {
		this.deptIdStr = deptIdStr;
	}
	public void setDeptNum(Integer deptNum) {
		this.deptNum = deptNum;
	}
	public Long getDeptId() {
		return deptId;
	}
	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}
	public String getDeptName() {
		return deptName;
	}
	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}
	public Integer getZyyUserType() {
		return zyyUserType;
	}
	public void setZyyUserType(Integer zyyUserType) {
		this.zyyUserType = zyyUserType;
	}
	public Long getTeacherId() {
		return teacherId;
	}
	public void setTeacherId(Long teacherId) {
		this.teacherId = teacherId;
	}
	
}
