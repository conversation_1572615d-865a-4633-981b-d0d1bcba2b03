package com.hys.zyy.manage.model;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang.StringUtils;

import com.hys.zyy.manage.util.DateUtil;
/**
 * 所有的考试  包括了 考试和单独的出科考试
 * <AUTHOR>
 * @date 2020-1-14下午3:58:28
 */
public class ZyyExam extends ZyyBaseObject {


    private static final long serialVersionUID=1L;

    //主键ID
    private Long id;
//    机构ID	
    private Long zyyUserOrgId;	
    //考试名称
    private String name;
    //考试类型编码
    private String typeCode;
    private Long code;
    //考试类型名称
    private String typeName;
    //考试开始日期
    private String startDate;
    //考试结束日期
    private String endDate;
    //创建人ID
    private Long createUserId;
    //创建人名称
    private String createUserName;
    //创建时间
    private Date createTime;
    //是否可以切屏（0：否  1：是）
    private Integer examOut;
    //切屏次数
    private Integer examOutNum;
//    发布状态	 0 未发布 1 已发布
    private Integer publishStatus;
//    是否网上打印准考证	 0 否  1 是
    private Integer onlinePrintTicket;	
//    是否需要网上报名	0 否  1 是
    private Integer onlineSignup;	
//    考试ID	考试系统的考试ID
    private Long examId;
    //准考证模板id
    private Long admissionTicketId;
    
//    更新时间	
    private Date updateDate;	
//    创建者类型	 	对应的用户类型
    private Integer createUserType;
//    创建者专业基地ID	
    private Long createBaseId;	
//    创建者医院ID	
    private Long createHospitalId;	
//    创建者科室ID	
    private Long createDeptId;	
//    创建考试的菜单	   1 考试列表    2  创建出科考试
    private Integer createMenu;	
    
//  准考证发布状态	 0 未发布 1 已发布
    private Integer ticketPubStatus;
    
//    考试报名表模板id	
    private Long examApplyFormId;	
//    网上报名开始时间	
    private String onlineStartTime;	
//    网上报名结束时间	
    private String onlineEndTime;	
//    是否回复		1 是    2  否
    private Integer replyReason;
    
    //不存库字段
    //考试状态  1 正在考试 2 尚未开始 3 考试结束
    private Integer examStatus;
    //考生人数	
    private Integer studentNum;
    //考试科目数
    private Integer courseNum;
    //创建者
    private Integer createType;
    
    //审核流程
    private List<ZyyApplyCheckFlow> flowList;
    //删除的审核级别
    private Integer checkLevel;
    
    //新增的学生id
    private String addStudentIds;
    //删除的学生id
    private String delStudentIds;
    
    // 1 报名   2  查看
    private Integer viewType;
    private boolean selectMyself;
    //是否可以报名  1 可以 2  不可以
    private Integer applyStatus;
    //报名审核状态
    private String applyCheckInfo;
    
    //省份的id
    private Long provinceId;
    //审核状态
    private Integer checkStatus;

	private Integer repeatExam;
	private Integer examIn;

	/**
	 * 根据考试时间转换状态
	 * @return 0=未初始化 1=尚未开始 2=正在考试 3=考试结束
	 */
	public Integer getExamDateStatus(){
		if (StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate) ){
			Date nowDate = DateUtil.now(DateUtil.FORMAT_MINUTES);
			Date start = DateUtil.parse(startDate,DateUtil.FORMAT_MINUTES);
			Date end = DateUtil.parse(endDate,DateUtil.FORMAT_MINUTES);
			if(nowDate.getTime()>end.getTime()){
				return 3;
			}else if(nowDate.getTime()>=start.getTime()&&nowDate.getTime()<=end.getTime()){
				return 2;
			}else if(nowDate.getTime()<start.getTime()){
				return 1;
			}
		}
		return 0;
	}

	public Integer getRepeatExam() {
		return repeatExam;
	}

	public void setRepeatExam(Integer repeatExam) {
		this.repeatExam = repeatExam;
	}

	public Integer getExamIn() {
		return examIn;
	}

	public void setExamIn(Integer examIn) {
		this.examIn = examIn;
	}

	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getZyyUserOrgId() {
		return zyyUserOrgId;
	}
	public void setZyyUserOrgId(Long zyyUserOrgId) {
		this.zyyUserOrgId = zyyUserOrgId;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getTypeCode() {
		return typeCode;
	}
	public void setTypeCode(String typeCode) {
		this.typeCode = typeCode;
	}
	public String getTypeName() {
		return typeName;
	}
	public void setTypeName(String typeName) {
		this.typeName = typeName;
	}
	public String getStartDate() {
		return startDate;
	}
	public void setStartDate(String startDate) {
		this.startDate = startDate;
	}
	public String getEndDate() {
		return endDate;
	}
	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}
	public Long getCreateUserId() {
		return createUserId;
	}
	public void setCreateUserId(Long createUserId) {
		this.createUserId = createUserId;
	}
	public String getCreateUserName() {
		return createUserName;
	}
	public void setCreateUserName(String createUserName) {
		this.createUserName = createUserName;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	public Integer getExamOut() {
		return examOut;
	}
	public void setExamOut(Integer examOut) {
		this.examOut = examOut;
	}
	public Integer getExamOutNum() {
		return examOutNum;
	}
	public void setExamOutNum(Integer examOutNum) {
		this.examOutNum = examOutNum;
	}
	public Integer getPublishStatus() {
		return publishStatus;
	}
	public void setPublishStatus(Integer publishStatus) {
		this.publishStatus = publishStatus;
	}
	public Integer getOnlinePrintTicket() {
		return onlinePrintTicket;
	}
	public void setOnlinePrintTicket(Integer onlinePrintTicket) {
		this.onlinePrintTicket = onlinePrintTicket;
	}
	public Integer getOnlineSignup() {
		return onlineSignup;
	}
	public void setOnlineSignup(Integer onlineSignup) {
		this.onlineSignup = onlineSignup;
	}
	public Long getExamId() {
		return examId;
	}
	public void setExamId(Long examId) {
		this.examId = examId;
	}
	public Long getAdmissionTicketId() {
		return admissionTicketId;
	}
	public void setAdmissionTicketId(Long admissionTicketId) {
		this.admissionTicketId = admissionTicketId;
	}
	public Long getExamApplyFormId() {
		return examApplyFormId;
	}
	public void setExamApplyFormId(Long examApplyFormId) {
		this.examApplyFormId = examApplyFormId;
	}
	public String getOnlineStartTime() {
		return onlineStartTime;
	}
	public void setOnlineStartTime(String onlineStartTime) {
		this.onlineStartTime = onlineStartTime;
	}
	public String getOnlineEndTime() {
		return onlineEndTime;
	}
	public void setOnlineEndTime(String onlineEndTime) {
		this.onlineEndTime = onlineEndTime;
	}
	public Integer getReplyReason() {
		return replyReason;
	}
	public void setReplyReason(Integer replyReason) {
		this.replyReason = replyReason;
	}
	public Integer getExamStatus() {
		return examStatus;
	}
	public void setExamStatus(Integer examStatus) {
		this.examStatus = examStatus;
	}
	public Integer getStudentNum() {
		return studentNum;
	}
	public void setStudentNum(Integer studentNum) {
		this.studentNum = studentNum;
	}
	public Integer getCourseNum() {
		return courseNum;
	}
	public void setCourseNum(Integer courseNum) {
		this.courseNum = courseNum;
	}
	public Date getUpdateDate() {
		return updateDate;
	}
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}
	public Integer getCreateUserType() {
		return createUserType;
	}
	public void setCreateUserType(Integer createUserType) {
		this.createUserType = createUserType;
	}
	public Long getCreateBaseId() {
		return createBaseId;
	}
	public void setCreateBaseId(Long createBaseId) {
		this.createBaseId = createBaseId;
	}
	public Long getCreateHospitalId() {
		return createHospitalId;
	}
	public void setCreateHospitalId(Long createHospitalId) {
		this.createHospitalId = createHospitalId;
	}
	public Long getCreateDeptId() {
		return createDeptId;
	}
	public void setCreateDeptId(Long createDeptId) {
		this.createDeptId = createDeptId;
	}
	public Integer getCreateMenu() {
		return createMenu;
	}
	public void setCreateMenu(Integer createMenu) {
		this.createMenu = createMenu;
	}
	public Integer getCreateType() {
		return createType;
	}
	public void setCreateType(Integer createType) {
		this.createType = createType;
	}
	public Integer getTicketPubStatus() {
		return ticketPubStatus;
	}
	public void setTicketPubStatus(Integer ticketPubStatus) {
		this.ticketPubStatus = ticketPubStatus;
	}
	public List<ZyyApplyCheckFlow> getFlowList() {
		return flowList;
	}
	public void setFlowList(List<ZyyApplyCheckFlow> flowList) {
		this.flowList = flowList;
	}
	public Integer getCheckLevel() {
		return checkLevel;
	}
	public void setCheckLevel(Integer checkLevel) {
		this.checkLevel = checkLevel;
	}
	public String getAddStudentIds() {
		return addStudentIds;
	}
	public void setAddStudentIds(String addStudentIds) {
		this.addStudentIds = addStudentIds;
	}
	public String getDelStudentIds() {
		return delStudentIds;
	}
	public void setDelStudentIds(String delStudentIds) {
		this.delStudentIds = delStudentIds;
	}
	public Integer getViewType() {
		return viewType;
	}
	public void setViewType(Integer viewType) {
		this.viewType = viewType;
	}
	public boolean isSelectMyself() {
		return selectMyself;
	}
	public void setSelectMyself(boolean selectMyself) {
		this.selectMyself = selectMyself;
	}
	public Integer getApplyStatus() {
		return applyStatus;
	}
	public void setApplyStatus(Integer applyStatus) {
		this.applyStatus = applyStatus;
	}
	public String getApplyCheckInfo() {
		return applyCheckInfo;
	}
	public void setApplyCheckInfo(String applyCheckInfo) {
		this.applyCheckInfo = applyCheckInfo;
	}
	public Long getProvinceId() {
		return provinceId;
	}
	public void setProvinceId(Long provinceId) {
		this.provinceId = provinceId;
	}
	public Integer getCheckStatus() {
		return checkStatus;
	}
	public void setCheckStatus(Integer checkStatus) {
		this.checkStatus = checkStatus;
	}
	public Long getCode() {
		return code;
	}
	public void setCode(Long code) {
		this.code = code;
	}
}
