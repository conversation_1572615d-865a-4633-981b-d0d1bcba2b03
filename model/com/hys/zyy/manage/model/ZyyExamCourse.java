package com.hys.zyy.manage.model;

import java.util.Date;

/**
 * 考试科目
 * <AUTHOR>
 * @date 2020-2-28上午9:59:27
 */
public class ZyyExamCourse extends ZyyBaseObject {
	/**
	 * 
	 */
	private static final long serialVersionUID = -8183313827061651003L;
	//	ID	
	private Long id;	
//	考试科目ID	 考试系统的考试科目ID
	private Long examCourseId;	
//	试卷ID	考试系统的试卷ID
	private Long paperId;	
//	考试科目开始日期	
	private String startDate;
//	考试科目结束日期	
	private String endDate; 
//	科目名称	
	private String courseName; 
//	考试科目开始时间	
	private String courseStartTime;
//	考试科目结束时间	
	private String courseEndTime;
//	考试时长	
	private String examDuration;
//	提交状态	0 未提交   1 已提交
	private Integer submitStatus;	
//	审核状态		0 未审核   1 审核通过  2 审核不通过
	private Integer checkStatus;
//	更新时间	
	private Date updateDate;	
//	创建时间	
	private Date createDate;	
//			
//	组卷方式	 	1  自动组卷   2 选择试卷
	private Integer paperType;
//	提交时间	
	private Date submitDate;	
//	住院医考试id	zyy_exam的id
	private Long zyyExamId;	
//	考试时间设置类型	1 有具体时间  2 不限
	private Integer examTimeType;
//	是否生成考试码  0 否  1 是
	private Integer isGenerateCode;
//	试卷是否乱序 0 否  1 是
	private Integer isRandom;
//	考试发布状态	0 未发布 1 已发布
	private Integer publishStatus;	
//	成绩发布状态	0 未发布 1 已发布
	private Integer scorePublishStatus;	

	//不存库字段
	//考试名称
	private String name;
	//创建机构id
	private Long zyyUserOrgId;
	//考试类型
	private String typeCode;
	//考试类型名称
    private String typeName;
    //考试状态  1 正在考试 2 尚未开始 3 考试结束
    private Integer examStatus;
    //考生人数	
    private Integer studentNum;
    //考试科目数
    private Integer courseNum;
    //考试系统的  考试id
    private Long examId;
    //试卷名称
    private String paperName;
    //选中状态  1 选中 
    private Integer chooseStatus;

	private Integer repeatSubmit;
	private Integer repeatSubmitNum;

	public Integer getRepeatSubmit() {
		return repeatSubmit;
	}

	public void setRepeatSubmit(Integer repeatSubmit) {
		this.repeatSubmit = repeatSubmit;
	}

	public Integer getRepeatSubmitNum() {
		return repeatSubmitNum;
	}

	public void setRepeatSubmitNum(Integer repeatSubmitNum) {
		this.repeatSubmitNum = repeatSubmitNum;
	}

	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getExamCourseId() {
		return examCourseId;
	}
	public void setExamCourseId(Long examCourseId) {
		this.examCourseId = examCourseId;
	}
	public Long getPaperId() {
		return paperId;
	}
	public void setPaperId(Long paperId) {
		this.paperId = paperId;
	}
	public String getStartDate() {
		return startDate;
	}
	public void setStartDate(String startDate) {
		this.startDate = startDate;
	}
	public String getEndDate() {
		return endDate;
	}
	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}
	public String getCourseName() {
		return courseName;
	}
	public void setCourseName(String courseName) {
		this.courseName = courseName;
	}
	public String getCourseStartTime() {
		return courseStartTime;
	}
	public void setCourseStartTime(String courseStartTime) {
		this.courseStartTime = courseStartTime;
	}
	public String getCourseEndTime() {
		return courseEndTime;
	}
	public void setCourseEndTime(String courseEndTime) {
		this.courseEndTime = courseEndTime;
	}
	public String getExamDuration() {
		return examDuration;
	}
	public void setExamDuration(String examDuration) {
		this.examDuration = examDuration;
	}
	public Integer getSubmitStatus() {
		return submitStatus;
	}
	public void setSubmitStatus(Integer submitStatus) {
		this.submitStatus = submitStatus;
	}
	public Integer getCheckStatus() {
		return checkStatus;
	}
	public void setCheckStatus(Integer checkStatus) {
		this.checkStatus = checkStatus;
	}
	public Date getUpdateDate() {
		return updateDate;
	}
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}
	public Date getCreateDate() {
		return createDate;
	}
	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}
	public Integer getPaperType() {
		return paperType;
	}
	public void setPaperType(Integer paperType) {
		this.paperType = paperType;
	}
	public Date getSubmitDate() {
		return submitDate;
	}
	public void setSubmitDate(Date submitDate) {
		this.submitDate = submitDate;
	}
	public Long getZyyExamId() {
		return zyyExamId;
	}
	public void setZyyExamId(Long zyyExamId) {
		this.zyyExamId = zyyExamId;
	}
	public Integer getExamTimeType() {
		return examTimeType;
	}
	public void setExamTimeType(Integer examTimeType) {
		this.examTimeType = examTimeType;
	}
	public Integer getIsGenerateCode() {
		return isGenerateCode;
	}
	public void setIsGenerateCode(Integer isGenerateCode) {
		this.isGenerateCode = isGenerateCode;
	}
	public Integer getIsRandom() {
		return isRandom;
	}
	public void setIsRandom(Integer isRandom) {
		this.isRandom = isRandom;
	}
	public Integer getPublishStatus() {
		return publishStatus;
	}
	public void setPublishStatus(Integer publishStatus) {
		this.publishStatus = publishStatus;
	}
	public Integer getScorePublishStatus() {
		return scorePublishStatus;
	}
	public void setScorePublishStatus(Integer scorePublishStatus) {
		this.scorePublishStatus = scorePublishStatus;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public Long getZyyUserOrgId() {
		return zyyUserOrgId;
	}
	public void setZyyUserOrgId(Long zyyUserOrgId) {
		this.zyyUserOrgId = zyyUserOrgId;
	}
	
	public String getTypeCode() {
		return typeCode;
	}
	public void setTypeCode(String typeCode) {
		this.typeCode = typeCode;
	}
	public String getTypeName() {
		return typeName;
	}
	public void setTypeName(String typeName) {
		this.typeName = typeName;
	}
	public Integer getExamStatus() {
		return examStatus;
	}
	public void setExamStatus(Integer examStatus) {
		this.examStatus = examStatus;
	}
	public Integer getStudentNum() {
		return studentNum;
	}
	public void setStudentNum(Integer studentNum) {
		this.studentNum = studentNum;
	}
	public Integer getCourseNum() {
		return courseNum;
	}
	public void setCourseNum(Integer courseNum) {
		this.courseNum = courseNum;
	}
	public Long getExamId() {
		return examId;
	}
	public void setExamId(Long examId) {
		this.examId = examId;
	}
	public String getPaperName() {
		return paperName;
	}
	public void setPaperName(String paperName) {
		this.paperName = paperName;
	}
	public Integer getChooseStatus() {
		return chooseStatus;
	}
	public void setChooseStatus(Integer chooseStatus) {
		this.chooseStatus = chooseStatus;
	}	
}
