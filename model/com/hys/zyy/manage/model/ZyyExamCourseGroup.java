package com.hys.zyy.manage.model;

import java.util.Date;

public class ZyyExamCourseGroup extends ZyyBaseObject {

	/**
	 * 
	 */
	private static final long serialVersionUID = 2673948821603809604L;
	
//	id	
	private Long id;	
//	考试id	
	private Long zyyExamId;	
	//分组名称	 
	private String groupName;
	// 一键生成状态		0 否  1 是
	private Integer onekeyStatus;
//	创建时间	
	private Date createDate;	
//	更新时间	
	private Date updateDate;
	
	//子表的字段
//	分组id	
	private Long groupId;
//	科目id	
	private Long courseId;
	
	//不存库字段
	//科目名称
	private String courseName;
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getZyyExamId() {
		return zyyExamId;
	}
	public void setZyyExamId(Long zyyExamId) {
		this.zyyExamId = zyyExamId;
	}
	public String getGroupName() {
		return groupName;
	}
	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}
	public Integer getOnekeyStatus() {
		return onekeyStatus;
	}
	public void setOnekeyStatus(Integer onekeyStatus) {
		this.onekeyStatus = onekeyStatus;
	}
	public Date getCreateDate() {
		return createDate;
	}
	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}
	public Date getUpdateDate() {
		return updateDate;
	}
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}
	public Long getGroupId() {
		return groupId;
	}
	public void setGroupId(Long groupId) {
		this.groupId = groupId;
	}
	public Long getCourseId() {
		return courseId;
	}
	public void setCourseId(Long courseId) {
		this.courseId = courseId;
	}
	public String getCourseName() {
		return courseName;
	}
	public void setCourseName(String courseName) {
		this.courseName = courseName;
	}	
	
}
