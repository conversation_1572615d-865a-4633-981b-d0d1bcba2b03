package com.hys.zyy.manage.model;

import java.util.Date;

/**
 * 考试扩展信息
 * <AUTHOR>
 * @date 2020-4-8上午10:05:52
 */
public class ZyyExamExtend extends ZyyBaseObject {

	/**
	 * 
	 */
	private static final long serialVersionUID = 3709907191668050980L;
	
//	考试ID	
	private Long zyyExamId;	
//	准考证发布状态	0 未发布 1 已发布
	private Integer ticketPubStatus;	
//	准考证号字段选择	
	private String ticketRules;
//	座位号位数	
	private Integer seatNumDigit;
//	固定文字	
	private String fixedText;
//	序列号位数	
	private Integer serialDigit;
	private Date createDate;
	private Date updateDate;
	
	//不存库字段
	//准考证号规则   汉字显示
	private String ticketRulesStr;
	//准考证示例
	private String tiketDemo;
	//选择的规则
	private String chooseRule;
	//选择的状态
	private Integer status;
	
	
	public Long getZyyExamId() {
		return zyyExamId;
	}
	public void setZyyExamId(Long zyyExamId) {
		this.zyyExamId = zyyExamId;
	}
	public Integer getTicketPubStatus() {
		return ticketPubStatus;
	}
	public void setTicketPubStatus(Integer ticketPubStatus) {
		this.ticketPubStatus = ticketPubStatus;
	}
	public String getTicketRules() {
		return ticketRules;
	}
	public void setTicketRules(String ticketRules) {
		this.ticketRules = ticketRules;
	}
	public Integer getSeatNumDigit() {
		return seatNumDigit;
	}
	public void setSeatNumDigit(Integer seatNumDigit) {
		this.seatNumDigit = seatNumDigit;
	}
	public String getFixedText() {
		return fixedText;
	}
	public void setFixedText(String fixedText) {
		this.fixedText = fixedText;
	}
	public Integer getSerialDigit() {
		return serialDigit;
	}
	public void setSerialDigit(Integer serialDigit) {
		this.serialDigit = serialDigit;
	}
	public Date getCreateDate() {
		return createDate;
	}
	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}
	public Date getUpdateDate() {
		return updateDate;
	}
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}
	public String getTicketRulesStr() {
		return ticketRulesStr;
	}
	public void setTicketRulesStr(String ticketRulesStr) {
		this.ticketRulesStr = ticketRulesStr;
	}
	public String getTiketDemo() {
		return tiketDemo;
	}
	public void setTiketDemo(String tiketDemo) {
		this.tiketDemo = tiketDemo;
	}
	public String getChooseRule() {
		return chooseRule;
	}
	public void setChooseRule(String chooseRule) {
		this.chooseRule = chooseRule;
	}
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}	
	
}
