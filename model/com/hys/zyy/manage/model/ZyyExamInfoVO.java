package com.hys.zyy.manage.model;

import java.io.Serializable;

public class ZyyExamInfoVO implements Serializable {
	private static final long serialVersionUID = 1L;
	/*
	 * 学员ID
	 */
	private Long residencyId;
	/*
	 * 考试时间
	 */
	private String examTimeSection;
	/*
	 * 创建者
	 */
	private String creator;
	/*
	 * 考试名称
	 */
	private String examName;
	/*
	 * 考试类型
	 */
	private String examTypeStr;
	/*
	 * 科目名称
	 */
	private String courseName;
	/*
	 * 考试分数
	 */
	private String examScore;
	/*
	 * 考试系统的考试科目ID
	 */
	private Long examCourseId;
	/*
	 * 学员证件号码
	 */
	private String certificateNo;
	/*
	 * 成绩发布状态
	 */
	public Integer scorePublishStatus;
	/*
	 * 是否老版考试
	 */
	private Integer isOldExam;

	public ZyyExamInfoVO() {
		super();
	}

	public ZyyExamInfoVO(Long residencyId) {
		super();
		this.residencyId = residencyId;
	}

	public Long getResidencyId() {
		return residencyId;
	}

	public void setResidencyId(Long residencyId) {
		this.residencyId = residencyId;
	}

	public String getExamTimeSection() {
		return examTimeSection;
	}

	public void setExamTimeSection(String examTimeSection) {
		this.examTimeSection = examTimeSection;
	}

	public String getCreator() {
		return creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public String getExamName() {
		return examName;
	}

	public void setExamName(String examName) {
		this.examName = examName;
	}

	public String getExamTypeStr() {
		return examTypeStr;
	}

	public void setExamTypeStr(String examTypeStr) {
		this.examTypeStr = examTypeStr;
	}

	public String getCourseName() {
		return courseName;
	}

	public void setCourseName(String courseName) {
		this.courseName = courseName;
	}

	public String getExamScore() {
		return examScore;
	}

	public void setExamScore(String examScore) {
		this.examScore = examScore;
	}

	public Integer getIsOldExam() {
		return isOldExam;
	}

	public void setIsOldExam(Integer isOldExam) {
		this.isOldExam = isOldExam;
	}

	public Long getExamCourseId() {
		return examCourseId;
	}

	public void setExamCourseId(Long examCourseId) {
		this.examCourseId = examCourseId;
	}

	public String getCertificateNo() {
		return certificateNo;
	}

	public void setCertificateNo(String certificateNo) {
		this.certificateNo = certificateNo;
	}

	public Integer getScorePublishStatus() {
		return scorePublishStatus;
	}

	public void setScorePublishStatus(Integer scorePublishStatus) {
		this.scorePublishStatus = scorePublishStatus;
	}

}
