package com.hys.zyy.manage.model;

import java.util.List;

import com.hys.beq.entities.vo.EmOrgGroupRelVO;

/**
 * ZYY_EXAM_ORG_AUTH 表对应的类
 * <AUTHOR>
 * @date 2019-11-11下午1:57:25
 */
public class ZyyExamOrgAuth extends ZyyBaseObject {

	/**
	 * 
	 */
	private static final long serialVersionUID = 784746145930335000L;
	
	private Long id;
	private String orgId;
	private String orgName;
	private String emSysOrgId;
	private List<EmOrgGroupRelVO> groupList;
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getOrgId() {
		return orgId;
	}
	public void setOrgId(String orgId) {
		this.orgId = orgId;
	}
	public String getOrgName() {
		return orgName;
	}
	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}
	public String getEmSysOrgId() {
		return emSysOrgId;
	}
	public void setEmSysOrgId(String emSysOrgId) {
		this.emSysOrgId = emSysOrgId;
	}
	public List<EmOrgGroupRelVO> getGroupList() {
		return groupList;
	}
	public void setGroupList(List<EmOrgGroupRelVO> groupList) {
		this.groupList = groupList;
	}
}
