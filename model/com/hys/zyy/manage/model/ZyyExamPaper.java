package com.hys.zyy.manage.model;

import java.util.Date;

/**
 * 考试试卷
 * <AUTHOR>
 * @date 2020-1-8下午2:21:48
 */
public class ZyyExamPaper extends ZyyBaseObject {
  /**
	 * 
	 */
	private static final long serialVersionUID = -3810829723267380297L;
	
	//	ID	
	private Long id;
//	机构ID	
	private Long zyyUserOrgId;
//	专业基地	
	private Long baseId;
//	科室ID	
	private Long deptId;
//	创建用户	
	private Long userId;
//	试卷ID	
	private Long paperId;
//	创建时间	
	private Date createDate;
	
//	试卷名称	
	private String name;
//	组卷方式 	1.手动组卷 2.卷中卷组卷 3.智能组卷  
	private Integer paperMode;	
//	难易程度	1.简单 2.中等 3.较难
	private Integer grade;	
//	适合考试分类	
	private Long examTypeId;	
	//用户姓名
	private String realName;
	//适合考试分类 名称
	private String examTypeName;
	//试题数量
	private String questionTotal;
    //试卷总分
	private String paperScore;
	//开始时间
	private String startDate;
	//结束时间
	private String endDate;
	
	//是否已选    1  已选
	private Integer setedFlag;
	
	//考试列表的 科目管理 
	//考试id
	private Long examId;
	//考试科目id
	private Long examCourseId;
	
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getZyyUserOrgId() {
		return zyyUserOrgId;
	}
	public void setZyyUserOrgId(Long zyyUserOrgId) {
		this.zyyUserOrgId = zyyUserOrgId;
	}
	public Long getBaseId() {
		return baseId;
	}
	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}
	public Long getDeptId() {
		return deptId;
	}
	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	public Long getPaperId() {
		return paperId;
	}
	public void setPaperId(Long paperId) {
		this.paperId = paperId;
	}
	public Date getCreateDate() {
		return createDate;
	}
	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public Integer getPaperMode() {
		return paperMode;
	}
	public void setPaperMode(Integer paperMode) {
		this.paperMode = paperMode;
	}
	public Integer getGrade() {
		return grade;
	}
	public void setGrade(Integer grade) {
		this.grade = grade;
	}
	public Long getExamTypeId() {
		return examTypeId;
	}
	public void setExamTypeId(Long examTypeId) {
		this.examTypeId = examTypeId;
	}
	public String getRealName() {
		return realName;
	}
	public void setRealName(String realName) {
		this.realName = realName;
	}
	public String getExamTypeName() {
		return examTypeName;
	}
	public void setExamTypeName(String examTypeName) {
		this.examTypeName = examTypeName;
	}
	public String getQuestionTotal() {
		return questionTotal;
	}
	public void setQuestionTotal(String questionTotal) {
		this.questionTotal = questionTotal;
	}
	public String getPaperScore() {
		return paperScore;
	}
	public void setPaperScore(String paperScore) {
		this.paperScore = paperScore;
	}
	public String getStartDate() {
		return startDate;
	}
	public void setStartDate(String startDate) {
		this.startDate = startDate;
	}
	public String getEndDate() {
		return endDate;
	}
	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}
	public Integer getSetedFlag() {
		return setedFlag;
	}
	public void setSetedFlag(Integer setedFlag) {
		this.setedFlag = setedFlag;
	}
	public Long getExamId() {
		return examId;
	}
	public void setExamId(Long examId) {
		this.examId = examId;
	}
	public Long getExamCourseId() {
		return examCourseId;
	}
	public void setExamCourseId(Long examCourseId) {
		this.examCourseId = examCourseId;
	}
}
