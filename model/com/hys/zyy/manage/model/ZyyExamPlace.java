package com.hys.zyy.manage.model;

import java.util.Date;
import java.util.List;
/**
 * 考点
 * <AUTHOR>
 * @date 2020-4-10上午11:07:23
 */
public class ZyyExamPlace extends ZyyBaseObject {

	/**
	 * 
	 */
	private static final long serialVersionUID = -5832187530118059142L;
	
//	id	
	private Long id;
//	科目组id	
	private Long groupId;
//	考点名称	
	private String placeName;
//	考点地址	
	private String address;
//	考点代码	
	private String placeCode;
//	创建时间	
	private Date createDate;
//	更新时间	
	private Date updateDate;
	
	//考场
	private List<ZyyExamRoom> roomList;
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getGroupId() {
		return groupId;
	}
	public void setGroupId(Long groupId) {
		this.groupId = groupId;
	}
	public String getPlaceName() {
		return placeName;
	}
	public void setPlaceName(String placeName) {
		this.placeName = placeName;
	}
	public String getAddress() {
		return address;
	}
	public void setAddress(String address) {
		this.address = address;
	}
	public String getPlaceCode() {
		return placeCode;
	}
	public void setPlaceCode(String placeCode) {
		this.placeCode = placeCode;
	}
	public Date getCreateDate() {
		return createDate;
	}
	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}
	public Date getUpdateDate() {
		return updateDate;
	}
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}
	public List<ZyyExamRoom> getRoomList() {
		return roomList;
	}
	public void setRoomList(List<ZyyExamRoom> roomList) {
		this.roomList = roomList;
	}
}
