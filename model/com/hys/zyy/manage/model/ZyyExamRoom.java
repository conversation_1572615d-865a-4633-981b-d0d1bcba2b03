package com.hys.zyy.manage.model;

import java.util.Date;
/**
 * 考场
 * <AUTHOR>
 * @date 2020-4-10上午11:08:02
 */
public class ZyyExamRoom extends ZyyBaseObject {

	/**
	 * 
	 */
	private static final long serialVersionUID = 4783026486337831358L;
	
//	id	
	private Long id;
//	考点id	
	private Long placeId;
//	考场名称	
	private String roomName;
//	考场人数	
	private Integer studentNumber;
//	考场代码	
	private String roomCode;
//	创建时间	
	private Date createDate;
//	更新时间	
	private Date updateDate;
	
	//不存库字段
	//考点名称
	private String placeName;
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getPlaceId() {
		return placeId;
	}
	public void setPlaceId(Long placeId) {
		this.placeId = placeId;
	}
	public String getRoomName() {
		return roomName;
	}
	public void setRoomName(String roomName) {
		this.roomName = roomName;
	}
	public Integer getStudentNumber() {
		return studentNumber;
	}
	public void setStudentNumber(Integer studentNumber) {
		this.studentNumber = studentNumber;
	}
	public String getRoomCode() {
		return roomCode;
	}
	public void setRoomCode(String roomCode) {
		this.roomCode = roomCode;
	}
	public Date getCreateDate() {
		return createDate;
	}
	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}
	public Date getUpdateDate() {
		return updateDate;
	}
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}
	public String getPlaceName() {
		return placeName;
	}
	public void setPlaceName(String placeName) {
		this.placeName = placeName;
	}
	
}
