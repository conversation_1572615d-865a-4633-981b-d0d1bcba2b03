package com.hys.zyy.manage.model;

/**
 * 考试或者科目对应的学生
 * <AUTHOR>
 * @date 2020-4-20上午9:13:59
 */
public class ZyyExamStudent extends ZyyBaseObject {

	/**
	 * 
	 */
	private static final long serialVersionUID = -5975312273642718607L;
	
//	考试科目的ID		考试或者考试科目的ID
	private Long zyyExamCourseId;
//	学生ID	
	private Long userId;
	private Long jdrId;
//	数据类型		1 考试   2 考试科目
	private Integer type;
	
	public Long getZyyExamCourseId() {
		return zyyExamCourseId;
	}
	public void setZyyExamCourseId(Long zyyExamCourseId) {
		this.zyyExamCourseId = zyyExamCourseId;
	}
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public Long getJdrId() {
		return jdrId;
	}

	public void setJdrId(Long jdrId) {
		this.jdrId = jdrId;
	}

	public Integer getType() {
		return type;
	}
	public void setType(Integer type) {
		this.type = type;
	}
}
