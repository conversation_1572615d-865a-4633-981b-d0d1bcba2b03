package com.hys.zyy.manage.model;

import java.util.Date;

/**
 * 考试类型设置       ZYY_EXAM_TYPE 
 * <AUTHOR>
 * @date 2019-12-24上午10:42:05
 */
public class ZyyExamType extends ZyyBaseObject{

	/**
	 * 
	 */
	private static final long serialVersionUID = 2084683680717749852L;
	
//	ID	
	private Long id;
//	机构ID	
	private Long zyyUserOrgId;
//	考试类型名称	
	private String name;
//	更新时间	
	private Date updateDate;
//	状态	
	private Integer status;
//	创建时间	
	private Date createDate;
	private ZyyOutDeptExamAuditSet zyyOutDeptExamAuditSet;
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getZyyUserOrgId() {
		return zyyUserOrgId;
	}
	public void setZyyUserOrgId(Long zyyUserOrgId) {
		this.zyyUserOrgId = zyyUserOrgId;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public Date getUpdateDate() {
		return updateDate;
	}
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	public Date getCreateDate() {
		return createDate;
	}
	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}
	public ZyyOutDeptExamAuditSet getZyyOutDeptExamAuditSet() {
		return zyyOutDeptExamAuditSet;
	}
	public void setZyyOutDeptExamAuditSet(ZyyOutDeptExamAuditSet zyyOutDeptExamAuditSet) {
		this.zyyOutDeptExamAuditSet = zyyOutDeptExamAuditSet;
	}
	
}
