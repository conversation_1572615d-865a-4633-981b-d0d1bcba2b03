package com.hys.zyy.manage.model;

import java.util.Date;
import java.util.List;

public class ZyyExamYearBaseAttr extends ZyyBaseObject {

	/**
	 * 
	 */
	private static final long serialVersionUID = -8367967929218436641L;
	
//	ID	
	private Long id;	
//	机构ID	zyy_user表的ZYY_USER_ORG_ID
	private Long zyyUserOrgId;
//	年度ID	
	private Long yearId;	
	private String yearName;
//	培训年限	
	private Integer schoolSystem;	
//	专业基地	
	private Long baseId;	
	private String baseName;
//	培训状态	
	private Integer zyyUserStatus;	
//	有效期至	
	private Date endDate;	
//	状态	STATUS	0 未启用 1 启用 2 禁用
	private Integer status;
	//题库属性名称
	private List<String> attrNameList;
	//所有题库的id ,号分隔
	private String allAttrId;
	//更新时间
	private Date updateDate;
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getZyyUserOrgId() {
		return zyyUserOrgId;
	}
	public void setZyyUserOrgId(Long zyyUserOrgId) {
		this.zyyUserOrgId = zyyUserOrgId;
	}
	public Long getYearId() {
		return yearId;
	}
	public void setYearId(Long yearId) {
		this.yearId = yearId;
	}
	public Integer getSchoolSystem() {
		return schoolSystem;
	}
	public void setSchoolSystem(Integer schoolSystem) {
		this.schoolSystem = schoolSystem;
	}
	public Long getBaseId() {
		return baseId;
	}
	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}
	public Integer getZyyUserStatus() {
		return zyyUserStatus;
	}
	public void setZyyUserStatus(Integer zyyUserStatus) {
		this.zyyUserStatus = zyyUserStatus;
	}
	public Date getEndDate() {
		return endDate;
	}
	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	public List<String> getAttrNameList() {
		return attrNameList;
	}
	public void setAttrNameList(List<String> attrNameList) {
		this.attrNameList = attrNameList;
	}
	public String getAllAttrId() {
		return allAttrId;
	}
	public void setAllAttrId(String allAttrId) {
		this.allAttrId = allAttrId;
	}
	public String getYearName() {
		return yearName;
	}
	public void setYearName(String yearName) {
		this.yearName = yearName;
	}
	public String getBaseName() {
		return baseName;
	}
	public void setBaseName(String baseName) {
		this.baseName = baseName;
	}
	public Date getUpdateDate() {
		return updateDate;
	}
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}
}
