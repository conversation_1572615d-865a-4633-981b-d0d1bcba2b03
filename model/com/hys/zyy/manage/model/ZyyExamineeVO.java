package com.hys.zyy.manage.model;

import java.util.Date;

public class ZyyExamineeVO extends ZyyGraduationExamResult {
	
	/**
	 * 培训基地
	 */
	private String orgName;
	
	/**
	 * 姓名
	 */
	private String userName;
	
	/**
	 * 考试批次（名称）
	 */
	private String examName;
		
	/**
	 * 年级
	 */
	private String year;
	
	/**
	 * 专业
	 */
	private String baseName;
	
	/**
	 * 身份证号
	 */
	private String certificateNo;
	
	//技能考站中用到的字段
	/**
	 * 技能考站编号
	 */
	private String stationName;
	/**
	 * 考核时间
	 */
	private Date examineDate;
	/**
	 * 考站考核内容
	 */
	private String examineContent;
	/**
	 * 技能考站是否可以删除  1可以  2不可以  
	 */
	private Integer stationIsDel;
	/**
	 * em_skill_station_score表的id键
	 */
	private Long scoreId;
	
	public Long getScoreId() {
		return scoreId;
	}

	public void setScoreId(Long scoreId) {
		this.scoreId = scoreId;
	}

	public Integer getStationIsDel() {
		return stationIsDel;
	}

	public void setStationIsDel(Integer stationIsDel) {
		this.stationIsDel = stationIsDel;
	}

	public String getStationName() {
		return stationName;
	}

	public void setStationName(String stationName) {
		this.stationName = stationName;
	}

	public Date getExamineDate() {
		return examineDate;
	}

	public void setExamineDate(Date examineDate) {
		this.examineDate = examineDate;
	}

	public String getExamineContent() {
		return examineContent;
	}

	public void setExamineContent(String examineContent) {
		this.examineContent = examineContent;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getExamName() {
		return examName;
	}

	public void setExamName(String examName) {
		this.examName = examName;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public String getYear() {
		return year;
	}

	public void setYear(String year) {
		this.year = year;
	}

	public String getBaseName() {
		return baseName;
	}

	public void setBaseName(String baseName) {
		this.baseName = baseName;
	}

	public String getCertificateNo() {
		return certificateNo;
	}

	public void setCertificateNo(String certificateNo) {
		this.certificateNo = certificateNo;
	}
	
}
