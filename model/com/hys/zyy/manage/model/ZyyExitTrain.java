package com.hys.zyy.manage.model;

import java.util.Date;

public class ZyyExitTrain {
	
	//用户ID
	private Long zyyUserId;

	//姓名
	private String name;
	
	//民族
	private String nation;
	
	//证件号码
	private String certificateNo;

	//手机号
	private String mobilNumber;
	
	//基地ID
	private Long orgId;
	
	//专业ID
	private Long baseId;
	
	//年级
	private Long yearId;

	//人员类型 1：单位人 2：社会人 3：学位衔接
	private Integer residencySource;
	
	//基地审核状态 0：未审核 1：同意退培
	private Integer baseCheckStatus;
	
	//省厅审核状态 0 ：未审核 1：同意退培
	private Integer provinceCheckStatus;
	
	//是否解除黑名单 0：未接触 1：已解除
	private Integer blackStatus;
	
	//申请时间
	private String applyDate;
	
	//省份ID
	private Long provinceId;
	
	private Integer directStu;//是否为订单定向生 1是0否
	
	private String orgName;
	private String baseStdName;
	private String yearName;
	
	private Date provinceCheckDate;
	/*
	 * 拒绝原因
	 */
	private String disagreeRemark;
	/*
	 * 拒绝时间
	 */
	private Date disagreeTime;
	
	private Integer sex;
	private Integer certificateType;
	
	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public String getBaseStdName() {
		return baseStdName;
	}

	public void setBaseStdName(String baseStdName) {
		this.baseStdName = baseStdName;
	}


	public String getYearName() {
		return yearName;
	}

	public void setYearName(String yearName) {
		this.yearName = yearName;
	}

	public Long getProvinceId() {
		return provinceId;
	}

	public void setProvinceId(Long provinceId) {
		this.provinceId = provinceId;
	}

	public Long getZyyUserId() {
		return zyyUserId;
	}

	public void setZyyUserId(Long zyyUserId) {
		this.zyyUserId = zyyUserId;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getNation() {
		return nation;
	}

	public void setNation(String nation) {
		this.nation = nation;
	}

	public String getCertificateNo() {
		return certificateNo;
	}

	public void setCertificateNo(String certificateNo) {
		this.certificateNo = certificateNo;
	}

	public String getMobilNumber() {
		return mobilNumber;
	}

	public void setMobilNumber(String mobilNumber) {
		this.mobilNumber = mobilNumber;
	}

	public Long getOrgId() {
		return orgId;
	}

	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}

	public Long getBaseId() {
		return baseId;
	}

	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}

	public Long getYearId() {
		return yearId;
	}

	public void setYearId(Long yearId) {
		this.yearId = yearId;
	}

	public Integer getResidencySource() {
		return residencySource;
	}

	public void setResidencySource(Integer residencySource) {
		this.residencySource = residencySource;
	}

	public Integer getBaseCheckStatus() {
		return baseCheckStatus;
	}

	public void setBaseCheckStatus(Integer baseCheckStatus) {
		this.baseCheckStatus = baseCheckStatus;
	}

	public Integer getProvinceCheckStatus() {
		return provinceCheckStatus;
	}

	public void setProvinceCheckStatus(Integer provinceCheckStatus) {
		this.provinceCheckStatus = provinceCheckStatus;
	}

	public Integer getBlackStatus() {
		return blackStatus;
	}

	public void setBlackStatus(Integer blackStatus) {
		this.blackStatus = blackStatus;
	}

	public String getApplyDate() {
		return applyDate;
	}

	public void setApplyDate(String applyDate) {
		this.applyDate = applyDate;
	}

	public Integer getDirectStu() {
		return directStu;
	}

	public void setDirectStu(Integer directStu) {
		this.directStu = directStu;
	}

	public Date getProvinceCheckDate() {
		return provinceCheckDate;
	}

	public void setProvinceCheckDate(Date provinceCheckDate) {
		this.provinceCheckDate = provinceCheckDate;
	}

	public Integer getSex() {
		return sex;
	}

	public void setSex(Integer sex) {
		this.sex = sex;
	}

	public Integer getCertificateType() {
		return certificateType;
	}

	public void setCertificateType(Integer certificateType) {
		this.certificateType = certificateType;
	}

	public String getDisagreeRemark() {
		return disagreeRemark;
	}

	public void setDisagreeRemark(String disagreeRemark) {
		this.disagreeRemark = disagreeRemark == null ? null : disagreeRemark.trim();
	}

	public Date getDisagreeTime() {
		return disagreeTime;
	}

	public void setDisagreeTime(Date disagreeTime) {
		this.disagreeTime = disagreeTime;
	}
	
}


