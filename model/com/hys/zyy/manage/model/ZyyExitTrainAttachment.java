package com.hys.zyy.manage.model;

/**
 * 退培附件
 * <AUTHOR>
 *
 */

public class ZyyExitTrainAttachment {

	private Long id;// ID标示
	private Long userId;// 用户ID
	private String attachmentName;// 附件名称
	private String attachmentUrl;// 附件路径
	private Integer attachmentStatus;//附件状态 0已删除  1未删除
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	public String getAttachmentName() {
		return attachmentName;
	}
	public void setAttachmentName(String attachmentName) {
		this.attachmentName = attachmentName;
	}
	public String getAttachmentUrl() {
		return attachmentUrl;
	}
	public void setAttachmentUrl(String attachmentUrl) {
		this.attachmentUrl = attachmentUrl;
	}
	public Integer getAttachmentStatus() {
		return attachmentStatus;
	}
	public void setAttachmentStatus(Integer attachmentStatus) {
		this.attachmentStatus = attachmentStatus;
	}
	
	
}
