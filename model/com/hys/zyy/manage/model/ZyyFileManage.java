package com.hys.zyy.manage.model;

import java.io.Serializable;
import java.util.Date;

import com.hys.zyy.manage.util.BaseModel;

/**
 * 文件管理
 */
public class ZyyFileManage extends BaseModel implements Serializable {
	private static final long serialVersionUID = 1L;
	/*
	 * ID
	 */
	private Long id;
	/*
	 * 文件名称
	 */
	private String fileName;
	/*
	 * 文件路径
	 */
	private String filePath;
	/*
	 * 文件大小（单位：KB）
	 */
	private Double fileSize;
	/*
	 * 所属文件夹ID
	 */
	private Long zyyFolderManageId;
	/*
	 * 省ID
	 */
	private Long provinceId;
	/*
	 * 医院ID
	 */
	private Long hospitalId;
	/*
	 * 状态（-1：失效；1：有效），文件从文档管理移动到回收站，状态由1改为-1
	 */
	private Integer state;
	/*
	 * 状态修改者ID
	 */
	private Long stateUpdateUserId;
	/*
	 * 状态修改时间
	 */
	private Date stateUpdateTime;
	/*
	 * 已删除（1：是；-1：否），从回收站删除的文件，已删除由-1改为1
	 */
	private Integer isDelete;
	/*
	 * 删除者ID
	 */
	private Long deleteUserId;
	/*
	 * 删除时间
	 */
	private Date deleteTime;
	/*
	 * 创建者ID
	 */
	private Long createUserId;
	/*
	 * 创建时间
	 */
	private Date createTime;
	/*
	 * 修改者ID
	 */
	private Long updateUserId;
	/*
	 * 修改时间
	 */
	private Date updateTime;
	/**
	 * 置顶排序时间
	 */
	private Date topTime;

	public ZyyFileManage() {
		super();
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	public String getFilePath() {
		return filePath;
	}

	public void setFilePath(String filePath) {
		this.filePath = filePath;
	}

	public Double getFileSize() {
		return fileSize;
	}

	public void setFileSize(Double fileSize) {
		this.fileSize = fileSize;
	}

	public Long getZyyFolderManageId() {
		return zyyFolderManageId;
	}

	public void setZyyFolderManageId(Long zyyFolderManageId) {
		this.zyyFolderManageId = zyyFolderManageId;
	}

	public Long getProvinceId() {
		return provinceId;
	}

	public void setProvinceId(Long provinceId) {
		this.provinceId = provinceId;
	}

	public Long getHospitalId() {
		return hospitalId;
	}

	public void setHospitalId(Long hospitalId) {
		this.hospitalId = hospitalId;
	}

	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	public Long getStateUpdateUserId() {
		return stateUpdateUserId;
	}

	public void setStateUpdateUserId(Long stateUpdateUserId) {
		this.stateUpdateUserId = stateUpdateUserId;
	}

	public Date getStateUpdateTime() {
		return stateUpdateTime;
	}

	public void setStateUpdateTime(Date stateUpdateTime) {
		this.stateUpdateTime = stateUpdateTime;
	}

	public Integer getIsDelete() {
		return isDelete;
	}

	public void setIsDelete(Integer isDelete) {
		this.isDelete = isDelete;
	}

	public Long getDeleteUserId() {
		return deleteUserId;
	}

	public void setDeleteUserId(Long deleteUserId) {
		this.deleteUserId = deleteUserId;
	}

	public Date getDeleteTime() {
		return deleteTime;
	}

	public void setDeleteTime(Date deleteTime) {
		this.deleteTime = deleteTime;
	}

	public Long getCreateUserId() {
		return createUserId;
	}

	public void setCreateUserId(Long createUserId) {
		this.createUserId = createUserId;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Long getUpdateUserId() {
		return updateUserId;
	}

	public void setUpdateUserId(Long updateUserId) {
		this.updateUserId = updateUserId;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public Date getTopTime() {
		return topTime;
	}

	public void setTopTime(Date topTime) {
		this.topTime = topTime;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ZyyFileManage other = (ZyyFileManage) obj;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		return true;
	}

	@Override
	public String toString() {
		return "ZyyFileManage [id=" + id + ", fileName=" + fileName
				+ ", filePath=" + filePath + ", fileSize=" + fileSize
				+ ", zyyFolderManageId=" + zyyFolderManageId + ", provinceId="
				+ provinceId + ", hospitalId=" + hospitalId + ", state="
				+ state + ", stateUpdateUserId=" + stateUpdateUserId
				+ ", stateUpdateTime=" + stateUpdateTime + ", isDelete="
				+ isDelete + ", deleteUserId=" + deleteUserId + ", deleteTime="
				+ deleteTime + ", createUserId=" + createUserId
				+ ", createTime=" + createTime + ", updateUserId="
				+ updateUserId + ", updateTime=" + updateTime + "]";
	}

}
