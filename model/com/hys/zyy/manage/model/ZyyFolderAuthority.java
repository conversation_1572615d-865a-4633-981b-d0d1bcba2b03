package com.hys.zyy.manage.model;

import java.io.Serializable;

import com.hys.zyy.manage.util.BaseModel;

/**
 * 文件夹权限
 */
public class ZyyFolderAuthority extends BaseModel implements Serializable {
	private static final long serialVersionUID = 1L;
	/*
	 * ID
	 */
	private Long id;
	/*
	 * 文件夹管理ID
	 */
	private Long zyyFolderManageId;
	/*
	 * 用户类型ID
	 */
	private Integer zyyUserTypeId;
	/*
	 * 权限类型（1：上传；2：查看）
	 */
	private Integer type;

	public ZyyFolderAuthority() {
		super();
	}

	public ZyyFolderAuthority(Long zyyFolderManageId) {
		super();
		this.zyyFolderManageId = zyyFolderManageId;
	}

	public ZyyFolderAuthority(Long zyyFolderManageId, Integer zyyUserTypeId, Integer type) {
		super();
		this.zyyFolderManageId = zyyFolderManageId;
		this.zyyUserTypeId = zyyUserTypeId;
		this.type = type;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getZyyFolderManageId() {
		return zyyFolderManageId;
	}

	public void setZyyFolderManageId(Long zyyFolderManageId) {
		this.zyyFolderManageId = zyyFolderManageId;
	}

	public Integer getZyyUserTypeId() {
		return zyyUserTypeId;
	}

	public void setZyyUserTypeId(Integer zyyUserTypeId) {
		this.zyyUserTypeId = zyyUserTypeId;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ZyyFolderAuthority other = (ZyyFolderAuthority) obj;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		return true;
	}

	@Override
	public String toString() {
		return "ZyyFolderAuthority [id=" + id + ", zyyFolderManageId="
				+ zyyFolderManageId + ", zyyUserTypeId=" + zyyUserTypeId
				+ ", type=" + type + "]";
	}

}
