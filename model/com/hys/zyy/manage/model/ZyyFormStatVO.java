package com.hys.zyy.manage.model;

import java.util.List;

public class ZyyFormStatVO extends ZyyFormStat {

	private Integer formType, dopsCount, miniCexCount, soapCount;
	private Long residencyId, yearId, baseId, teacherId;
	private String realName, certificateNo, year, baseName, startDateStr, endDateStr, assessTimeStr;
	private List<Long> residencyIdList;

	public ZyyFormStatVO() {
		super();
	}

	public Integer getFormType() {
		return formType;
	}

	public void setFormType(Integer formType) {
		this.formType = formType;
	}

	public Integer getDopsCount() {
		return dopsCount;
	}

	public void setDopsCount(Integer dopsCount) {
		this.dopsCount = dopsCount;
	}

	public Integer getMiniCexCount() {
		return miniCexCount;
	}

	public void setMiniCexCount(Integer miniCexCount) {
		this.miniCexCount = miniCexCount;
	}

	public Integer getSoapCount() {
		return soapCount;
	}

	public void setSoapCount(Integer soapCount) {
		this.soapCount = soapCount;
	}

	public Long getResidencyId() {
		return residencyId;
	}

	public void setResidencyId(Long residencyId) {
		this.residencyId = residencyId;
	}

	public Long getYearId() {
		return yearId;
	}

	public void setYearId(Long yearId) {
		this.yearId = yearId;
	}

	public Long getBaseId() {
		return baseId;
	}

	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}

	public Long getTeacherId() {
		return teacherId;
	}

	public void setTeacherId(Long teacherId) {
		this.teacherId = teacherId;
	}

	public String getRealName() {
		return realName;
	}

	public void setRealName(String realName) {
		this.realName = realName == null ? null : realName.trim();
	}

	public String getCertificateNo() {
		return certificateNo;
	}

	public void setCertificateNo(String certificateNo) {
		this.certificateNo = certificateNo == null ? null : certificateNo.trim();
	}

	public String getYear() {
		return year;
	}

	public void setYear(String year) {
		this.year = year == null ? null : year.trim();
	}

	public String getBaseName() {
		return baseName;
	}

	public void setBaseName(String baseName) {
		this.baseName = baseName == null ? null : baseName.trim();
	}

	public String getStartDateStr() {
		return startDateStr;
	}

	public void setStartDateStr(String startDateStr) {
		this.startDateStr = startDateStr == null ? null : startDateStr.trim();
	}

	public String getEndDateStr() {
		return endDateStr;
	}

	public void setEndDateStr(String endDateStr) {
		this.endDateStr = endDateStr == null ? null : endDateStr.trim();
	}

	public List<Long> getResidencyIdList() {
		return residencyIdList;
	}

	public void setResidencyIdList(List<Long> residencyIdList) {
		this.residencyIdList = residencyIdList;
	}

	public String getAssessTimeStr() {
		return assessTimeStr;
	}

	public void setAssessTimeStr(String assessTimeStr) {
		this.assessTimeStr = assessTimeStr == null ? null : assessTimeStr.trim();
	}

}