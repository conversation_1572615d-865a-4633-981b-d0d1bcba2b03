package com.hys.zyy.manage.model;

import java.io.Serializable;
import java.math.BigDecimal;

public class ZyyFractionalLineVO implements Serializable{

	private static final long serialVersionUID = -6744904643312222465L;
	
	private Long id;

	private String zyySyncId;
	
	/**
	 * 考核省份
	 */
	private String provinceCode;
	
	/**
	 * 考核年度
	 */
	private String examYear;
	
	/**
	 * 专业
	 */
	private String base;
	
	/**
	 * 理论考核分数线
	 */
	private BigDecimal theoryExamFractionalLine;
	
	/**
	 * 技能考核分数线
	 */
	private BigDecimal skillExamFractionalLine;
	
	
	private Integer status;

	public String getZyySyncId() {
		return zyySyncId;
	}

	public void setZyySyncId(String zyySyncId) {
		this.zyySyncId = zyySyncId;
	}

	public String getProvinceCode() {
		return provinceCode;
	}

	public void setProvinceCode(String provinceCode) {
		this.provinceCode = provinceCode;
	}

	public String getExamYear() {
		return examYear;
	}

	public void setExamYear(String examYear) {
		this.examYear = examYear;
	}

	public String getBase() {
		return base;
	}

	public void setBase(String base) {
		this.base = base;
	}

	public BigDecimal getTheoryExamFractionalLine() {
		return theoryExamFractionalLine;
	}

	public void setTheoryExamFractionalLine(BigDecimal theoryExamFractionalLine) {
		this.theoryExamFractionalLine = theoryExamFractionalLine;
	}

	public BigDecimal getSkillExamFractionalLine() {
		return skillExamFractionalLine;
	}

	public void setSkillExamFractionalLine(BigDecimal skillExamFractionalLine) {
		this.skillExamFractionalLine = skillExamFractionalLine;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}
		
	
}
