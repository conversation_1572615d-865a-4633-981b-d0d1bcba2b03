package com.hys.zyy.manage.model;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public class ZyyFundManage {

	private Long id;//主键
	private String name;//经费名称
	private Long manageTypeId;//经费来源，ZyyFundManageType表ID
	private Date manageDate;
	private String manageDateStr;//入账/出账/工资 时间
	private Integer type;//类型:1，入账。2，出账。3，工资。
	private Date createrDate;//创建时间
	private Long creater;//创建者
	private String pictureName;//图片名称
	private byte[] picture;//图片文件
	/*****************************************/
	private List<ZyyFundManageDetail> manageDetails;//经费明细
	private String typeName;//类型名称
	private String userName;//操作人名称
	private String orgName;//部门名称
	//private Float money;//总金额
	private BigDecimal money;//总金额
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public Integer getType() {
		return type;
	}
	public void setType(Integer type) {
		this.type = type;
	}
	public Date getCreaterDate() {
		return createrDate;
	}
	public void setCreaterDate(Date createrDate) {
		this.createrDate = createrDate;
	}
	public Long getCreater() {
		return creater;
	}
	public void setCreater(Long creater) {
		this.creater = creater;
	}
	public Long getManageTypeId() {
		return manageTypeId;
	}
	public void setManageTypeId(Long manageTypeId) {
		this.manageTypeId = manageTypeId;
	}
	public List<ZyyFundManageDetail> getManageDetails() {
		return manageDetails;
	}
	public void setManageDetails(List<ZyyFundManageDetail> manageDetails) {
		this.manageDetails = manageDetails;
	}
	public byte[] getPicture() {
		return picture;
	}
	public void setPicture(byte[] picture) {
		this.picture = picture;
	}
	public Date getManageDate() {
		return manageDate;
	}
	public void setManageDate(Date manageDate) {
		this.manageDate = manageDate;
	}
	public String getManageDateStr() {
		return manageDateStr;
	}
	public void setManageDateStr(String manageDateStr) {
		this.manageDateStr = manageDateStr;
	}
	public String getPictureName() {
		return pictureName;
	}
	public void setPictureName(String pictureName) {
		this.pictureName = pictureName;
	}
	public String getTypeName() {
		return typeName;
	}
	public void setTypeName(String typeName) {
		this.typeName = typeName;
	}
	public String getUserName() {
		return userName;
	}
	public void setUserName(String userName) {
		this.userName = userName;
	}
	public String getOrgName() {
		return orgName;
	}
	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}
	/*public Float getMoney() {
		return money;
	}
	public void setMoney(Float money) {
		this.money = money;
	}*/
	public BigDecimal getMoney() {
		return money;
	}
	public void setMoney(BigDecimal money) {
		this.money = money;
	}	
}
