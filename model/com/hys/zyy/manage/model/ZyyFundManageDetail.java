package com.hys.zyy.manage.model;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

import com.hys.framework.util.StringUtils;

/**
 * 经费明细
 * <AUTHOR>
 *
 */
public class ZyyFundManageDetail {

	private Long id;//主键
	private Long manageId;//经费管理ID
	private Long manageTypeId;//经费管理类型ID
	private String manageTypeIdStr;
	private Set<String> manageTypeIdSet = new HashSet<String>();
	/*
	 * 期初金额
	 */
	private BigDecimal qcje;
	//private Float money;//金额
	private BigDecimal money;//金额
	/*
	 * 结余金额
	 */
	private BigDecimal jyje;
	private String content;//备注
	/********************************/
	private String typeName;
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	/*public Float getMoney() {
		return money;
	}
	public void setMoney(Float money) {
		this.money = money;
	}*/
	
	
	public String getContent() {
		return content;
	}
	public BigDecimal getMoney() {
		return money;
	}
	public void setMoney(BigDecimal money) {
		this.money = money;
	}
	public void setContent(String content) {
		this.content = content;
	}
	public Long getManageTypeId() {
		return manageTypeId;
	}
	public void setManageTypeId(Long manageTypeId) {
		this.manageTypeId = manageTypeId;
	}
	public Long getManageId() {
		return manageId;
	}
	public void setManageId(Long manageId) {
		this.manageId = manageId;
	}
	public String getTypeName() {
		return typeName;
	}
	public void setTypeName(String typeName) {
		this.typeName = typeName;
	}
	public String getManageTypeIdStr() {
		return manageTypeIdStr;
	}
	public void setManageTypeIdStr(String manageTypeIdStr) {
		this.manageTypeIdStr = manageTypeIdStr == null ? null : manageTypeIdStr.trim();
	}
	public BigDecimal getQcje() {
		return qcje;
	}
	public void setQcje(BigDecimal qcje) {
		this.qcje = qcje;
	}
	public BigDecimal getJyje() {
		return jyje;
	}
	public void setJyje(BigDecimal jyje) {
		this.jyje = jyje;
	}

	public Set<String> getManageTypeIdSet() {
		if (StringUtils.isNotBlank(manageTypeIdStr))
			return new HashSet<String>(Arrays.asList(manageTypeIdStr.split(",")));
		return manageTypeIdSet;
	}

	public void setManageTypeIdSet(Set<String> manageTypeIdSet) {
		this.manageTypeIdSet = manageTypeIdSet;
	}
	
}
