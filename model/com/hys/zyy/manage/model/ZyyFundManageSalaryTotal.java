package com.hys.zyy.manage.model;

import java.util.ArrayList;
import java.util.List;

public class ZyyFundManageSalaryTotal {

	private String identityType;//身份类型
	private Long yearId;//年费ID
	private String year;//年份
	private Integer education;//学历；1 -大学专科 2 -大学本科 3 -硕士研究生 4 -博士研究生 5 -博士后
	private List<ZyyFundManageType> salaryTypes;//工资类型
	private List<ZyySalaryManageDetail> salaryDetails;//工资明细
	private Integer identityTypeId;//统计类型;1 -本院培训对象 2 -外单位委派培训对象 3 -面向社会招生对象
	List<ZyyFundManageSalaryTotal> children;
	
	public ZyyFundManageSalaryTotal(){init();}
	
	public ZyyFundManageSalaryTotal(String identityType,Integer identityTypeId){
		this.identityType=identityType;
		this.identityTypeId=identityTypeId;
		init();
	}
	
	private void init(){
		children=new ArrayList<ZyyFundManageSalaryTotal>();
		salaryTypes=new ArrayList<ZyyFundManageType>();
		salaryDetails=new ArrayList<ZyySalaryManageDetail>();
	}
	
	public void addSalaryDetail(List<ZyyFundManageType> salaryTypes){
		for(ZyyFundManageType type:salaryTypes){
			ZyySalaryManageDetail detail=new ZyySalaryManageDetail();
			detail.setSalaryTypeId(type.getId());
			salaryDetails.add(detail);
		}
	}
	
	public String getIdentityType() {
		return identityType;
	}
	public void setIdentityType(String identityType) {
		this.identityType = identityType;
	}
	public String getYear() {
		return year;
	}
	public void setYear(String year) {
		this.year = year;
	}
	public List<ZyyFundManageSalaryTotal> getChildren() {
		return children;
	}
	public void setChildren(List<ZyyFundManageSalaryTotal> children) {
		this.children = children;
	}

	public Long getYearId() {
		return yearId;
	}

	public void setYearId(Long yearId) {
		this.yearId = yearId;
	}

	public Integer getEducation() {
		return education;
	}

	public void setEducation(Integer education) {
		this.education = education;
	}

	public List<ZyyFundManageType> getSalaryTypes() {
		return salaryTypes;
	}

	public void setSalaryTypes(List<ZyyFundManageType> salaryTypes) {
		this.salaryTypes = salaryTypes;
	}

	public Integer getIdentityTypeId() {
		return identityTypeId;
	}

	public void setIdentityTypeId(Integer identityTypeId) {
		this.identityTypeId = identityTypeId;
	}

	public List<ZyySalaryManageDetail> getSalaryDetails() {
		return salaryDetails;
	}

	public void setSalaryDetails(List<ZyySalaryManageDetail> salaryDetails) {
		this.salaryDetails = salaryDetails;
	}
}
