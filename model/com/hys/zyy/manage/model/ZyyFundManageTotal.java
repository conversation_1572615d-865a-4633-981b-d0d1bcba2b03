package com.hys.zyy.manage.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import com.hys.framework.util.StringUtils;

public class ZyyFundManageTotal {

	private Long typeId;
	private String typeIdStr;
	private Set<String> typeIdSet = new HashSet<String>();
	private String typeName;
	//private Float money;
	private BigDecimal money;
	private List<ZyyFundManageTotal> totals;
	//private Float realMoney;
	private BigDecimal realMoney;
	//private Float diffMoney;
	private BigDecimal diffMoney;
	private String perCapita;//人均
	public ZyyFundManageTotal(){
		init();
	}
 
	public ZyyFundManageTotal(Long typeId, String typeName, BigDecimal money){
		init();
		this.typeId = typeId;
		this.typeName = typeName;
		this.money = money;
		
	}
	
	public ZyyFundManageTotal(String typeIdStr, String typeName, BigDecimal money){
		init();
		this.typeIdStr = typeIdStr;
		this.typeName = typeName;
		this.money = money;
		
	}
 	
	private void init(){
		this.money = new BigDecimal("0");
		this.realMoney = new BigDecimal("0");
		this.diffMoney = new BigDecimal("0");
		this.perCapita="0.00";
		totals=new ArrayList<ZyyFundManageTotal>();
	}
	
	public String getTypeName() {
		return typeName;
	}
	public void setTypeName(String typeName) {
		this.typeName = typeName;
	}
	/*public Float getMoney() {
		return money;
	}
	public void setMoney(Float money) {
		this.money = money;
	}*/
	public List<ZyyFundManageTotal> getTotals() {
		return totals;
	}
	public void setTotals(List<ZyyFundManageTotal> totals) {
		this.totals = totals;
	}
	/*public Float getRealMoney() {
		return realMoney;
	}
	public void setRealMoney(Float realMoney) {
		this.realMoney = realMoney;
	}
	public Float getDiffMoney() {
		return diffMoney;
	}
	public void setDiffMoney(Float diffMoney) {
		this.diffMoney = diffMoney;
	}*/

	public String getPerCapita() {
		return perCapita;
	}

	public void setPerCapita(String perCapita) {
		this.perCapita = perCapita;
	}

	public Long getTypeId() {
		return typeId;
	}

	public void setTypeId(Long typeId) {
		this.typeId = typeId;
	}

	public String getTypeIdStr() {
		return typeIdStr;
	}

	public void setTypeIdStr(String typeIdStr) {
		this.typeIdStr = typeIdStr == null ? null : typeIdStr.trim();
	}

	public BigDecimal getMoney() {
		return money;
	}

	public void setMoney(BigDecimal money) {
		this.money = money;
	}

	public BigDecimal getRealMoney() {
		return realMoney;
	}

	public void setRealMoney(BigDecimal realMoney) {
		this.realMoney = realMoney;
	}

	public BigDecimal getDiffMoney() {
		return diffMoney;
	}

	public void setDiffMoney(BigDecimal diffMoney) {
		this.diffMoney = diffMoney;
	}

	public Set<String> getTypeIdSet() {
		if (StringUtils.isNotBlank(typeIdStr)) 
			return new HashSet<String>(Arrays.asList(typeIdStr.split(",")));
		return typeIdSet;
	}

	public void setTypeIdSet(Set<String> typeIdSet) {
		this.typeIdSet = typeIdSet;
	}
	
	
}
