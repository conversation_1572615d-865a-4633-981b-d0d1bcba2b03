package com.hys.zyy.manage.model;

import java.util.Date;

/**
 * 经费管理类型
 * <AUTHOR>
 *
 */
public class ZyyFundManageType {

	public Boolean getIsDelete() {
		return isDelete;
	}
	public void setIsDelete(Boolean isDelete) {
		this.isDelete = isDelete;
	}
	private Long id;//主键
	private String typeName;//类型名称
	private Integer type;//种类：1，出账。2，入账。3，工资。
	private Boolean isDelete;//是否允许删除，
	private Date createDate;//创建时间，允许为空，为空表示固定不能删除的那几个
	private Long creater;//创建者,允许为空，为空表示固定不能删除的那几个
	
	private Long zyyOrgId;//机构ID
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getTypeName() {
		return typeName;
	}
	public void setTypeName(String typeName) {
		this.typeName = typeName;
	}
	public Integer getType() {
		return type;
	}
	public void setType(Integer type) {
		this.type = type;
	}
	public Date getCreateDate() {
		return createDate;
	}
	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}
	public Long getCreater() {
		return creater;
	}
	public void setCreater(Long creater) {
		this.creater = creater;
	}
	public Long getZyyOrgId() {
		return zyyOrgId;
	}
	public void setZyyOrgId(Long zyyOrgId) {
		this.zyyOrgId = zyyOrgId;
	}
}
