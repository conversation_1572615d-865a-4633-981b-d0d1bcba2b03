package com.hys.zyy.manage.model;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public class ZyyFundManageVO {

	private String userName;//操作人
	private String orgName;//部门
	private String fundName;//经费名称
	private Long fundType;//经费类型
	private Long fundType2;//计划投入经费。。
	private Date startDate;
	private Date endDate;
	private Long orgId;
	private Long provinceId;
	private Integer type;//类型。1：入账，2：出账。
	private Integer userType;//用户类型
	private Long curUserId;//当前登录用户的ID
	private Long deptId;//科室ID
	
	private BigDecimal sumInComeMoney;//总入账
	private BigDecimal sumExpendMoney;//总支出
	
	public String getUserName() {
		return userName;
	}
	public void setUserName(String userName) {
		this.userName = userName;
	}
	public String getFundName() {
		return fundName;
	}
	public void setFundName(String fundName) {
		this.fundName = fundName;
	}
	public Long getFundType() {
		return fundType;
	}
	public void setFundType(Long fundType) {
		this.fundType = fundType;
	}
	public Long getFundType2() {
		return fundType2;
	}
	public void setFundType2(Long fundType2) {
		this.fundType2 = fundType2;
	}
	public Date getStartDate() {
		return startDate;
	}
	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}
	public Date getEndDate() {
		return endDate;
	}
	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}
	public String getOrgName() {
		return orgName;
	}
	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}
	public Long getOrgId() {
		return orgId;
	}
	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}
	public Integer getType() {
		return type;
	}
	public void setType(Integer type) {
		this.type = type;
	}
	public Integer getUserType() {
		return userType;
	}
	public void setUserType(Integer userType) {
		this.userType = userType;
	}
	public Long getCurUserId() {
		return curUserId;
	}
	public void setCurUserId(Long curUserId) {
		this.curUserId = curUserId;
	}
	public Long getDeptId() {
		return deptId;
	}
	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}
	
	public BigDecimal getSumInComeMoney() {
		return sumInComeMoney;
	}
	public void setSumInComeMoney(BigDecimal sumInComeMoney) {
		this.sumInComeMoney = sumInComeMoney;
	}
	public BigDecimal getSumExpendMoney() {
		return sumExpendMoney;
	}
	public void setSumExpendMoney(BigDecimal sumExpendMoney) {
		this.sumExpendMoney = sumExpendMoney;
	}
	public Long getProvinceId() {
		return provinceId;
	}
	public void setProvinceId(Long provinceId) {
		this.provinceId = provinceId;
	}
}
