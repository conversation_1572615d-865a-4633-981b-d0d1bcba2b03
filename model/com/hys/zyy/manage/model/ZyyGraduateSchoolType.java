package com.hys.zyy.manage.model;

import java.util.List;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：李海龙 Apr 20, 2012
 * 
 * 描述：
 * 
 * 说明:
 */

public class ZyyGraduateSchoolType extends ZyyBaseObject {

	/**
	 * 
	 */
	private static final long serialVersionUID = -8334928158870395541L;

	/**
	 * 
	 */
	private Long id;

	/**
	 * 参考ZYY_USER_EXTEND中HIGHEST_GRADUATE_SCHOOL字段
	 */
	private String graduateSchoolName;

	/**
	 * 1 -一类 2 -二类 3 - 三类 4 -四类
	 */
	private Integer graduateSchoolType;

	/**
	 * 组织机构id
	 */
	private Long zyyOrgId;

	/**
	 * 批量提交
	 */
	private List<ZyyGraduateSchoolType> schoolTypeList;

	public String getGraduateSchoolName() {
		return graduateSchoolName;
	}

	public void setGraduateSchoolName(String graduateSchoolName) {
		this.graduateSchoolName = graduateSchoolName;
	}

	public Integer getGraduateSchoolType() {
		return graduateSchoolType;
	}

	public void setGraduateSchoolType(Integer graduateSchoolType) {
		this.graduateSchoolType = graduateSchoolType;
	}

	public Long getZyyOrgId() {
		return zyyOrgId;
	}

	public void setZyyOrgId(Long zyyOrgId) {
		this.zyyOrgId = zyyOrgId;
	}

	public List<ZyyGraduateSchoolType> getSchoolTypeList() {
		return schoolTypeList;
	}

	public void setSchoolTypeList(List<ZyyGraduateSchoolType> schoolTypeList) {
		this.schoolTypeList = schoolTypeList;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

}
