package com.hys.zyy.manage.model;

import java.util.Date;

import com.hys.zyy.manage.util.BaseModel;

/**
 * ZYY_GRADUATE_TARGET_STUDENT
 * <p>
 * Description:云南省专硕生和订单定向生 学生的信息表 excel导入
 * </p>
 * 
 * <AUTHOR>
 * @date 2018-6-1上午11:01:33
 */
public class ZyyGraduateTargetStudent extends BaseModel {

	private static final long serialVersionUID = 1L;
	/**
	 * 主键ID
	 */
	private Long id;
	// 姓名
	private String name;
	// 性别 1.男 2.女 0.未知
	private Integer sex;
	// 身份证号码
	private String certificateNo;
	// 手机号码
	private String mobileNumber;
	// 培养学校
	private String trainSchool;
	// 住培基地
	/**
	 * 培训医院(老学员注册) zyy_user_extend表字段
	 */
	private Long hospitalId;
	private String hospitalName;
	// 住培专业
	/**
	 * 培训学科(老学员注册) zyy_user_extend表字段
	 */
	private Long baseId;
	private String baseName;
	// 学生类型 1 专硕研究生 2 订单定向免费医学生
	private Integer studentType;
	// 生源地 - 省
	private String homeProvince;
	private Long homeProvinceId;
	// 生源地 - 市
	private String homeCity;
	private Long homeCityId;
	// 定向地
	private String targetPlace;
	// 创建时间
	private Date createTime;
	// 行号
	private String lineNo;
	// 邮箱 - 不存库
	private String email;
	// 培训年级
	private String year;
	private Long yearId;
	// 1 中医 2 西医 HOSPITAL_TYPE
	private Integer hospitalType;
	// 培训年限
	private Integer schoolSystem;
	/**
	 * 用户组织机构ID(省厅)
	 */
	private Long zyyUserProvinceId;
	/*
	 * 定向地市ID
	 */
	private Long targetPlaceProvinceId;
	/*
	 * 定向地县ID
	 */
	private Long targetPlaceCityId;
	/*
	 * 密码
	 */
	private String accountPassword;
	/*
	 * 标准专业ID
	 */
	private Long zyyBaseStdId;
	/*
	 * 学员报名状态
	 */
	private String recruitStatus;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Integer getSex() {
		return sex;
	}

	public void setSex(Integer sex) {
		this.sex = sex;
	}

	public String getCertificateNo() {
		return certificateNo;
	}

	public void setCertificateNo(String certificateNo) {
		this.certificateNo = certificateNo;
	}

	public String getMobileNumber() {
		return mobileNumber;
	}

	public void setMobileNumber(String mobileNumber) {
		this.mobileNumber = mobileNumber;
	}

	public String getTrainSchool() {
		return trainSchool;
	}

	public void setTrainSchool(String trainSchool) {
		this.trainSchool = trainSchool;
	}

	public Long getHospitalId() {
		return hospitalId;
	}

	public void setHospitalId(Long hospitalId) {
		this.hospitalId = hospitalId;
	}

	public String getHospitalName() {
		return hospitalName;
	}

	public void setHospitalName(String hospitalName) {
		this.hospitalName = hospitalName;
	}

	public Long getBaseId() {
		return baseId;
	}

	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}

	public String getBaseName() {
		return baseName;
	}

	public void setBaseName(String baseName) {
		this.baseName = baseName;
	}

	public Integer getStudentType() {
		return studentType;
	}

	public void setStudentType(Integer studentType) {
		this.studentType = studentType;
	}

	public String getHomeProvince() {
		return homeProvince;
	}

	public void setHomeProvince(String homeProvince) {
		this.homeProvince = homeProvince;
	}

	public String getHomeCity() {
		return homeCity;
	}

	public void setHomeCity(String homeCity) {
		this.homeCity = homeCity;
	}

	public String getTargetPlace() {
		return targetPlace;
	}

	public void setTargetPlace(String targetPlace) {
		this.targetPlace = targetPlace;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getLineNo() {
		return lineNo;
	}

	public void setLineNo(String lineNo) {
		this.lineNo = lineNo;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public Long getHomeProvinceId() {
		return homeProvinceId;
	}

	public void setHomeProvinceId(Long homeProvinceId) {
		this.homeProvinceId = homeProvinceId;
	}

	public Long getHomeCityId() {
		return homeCityId;
	}

	public void setHomeCityId(Long homeCityId) {
		this.homeCityId = homeCityId;
	}

	public String getYear() {
		return year;
	}

	public void setYear(String year) {
		this.year = year;
	}

	public Long getYearId() {
		return yearId;
	}

	public void setYearId(Long yearId) {
		this.yearId = yearId;
	}

	public Integer getHospitalType() {
		return hospitalType;
	}

	public void setHospitalType(Integer hospitalType) {
		this.hospitalType = hospitalType;
	}

	public Integer getSchoolSystem() {
		return schoolSystem;
	}

	public void setSchoolSystem(Integer schoolSystem) {
		this.schoolSystem = schoolSystem;
	}

	public Long getZyyUserProvinceId() {
		return zyyUserProvinceId;
	}

	public void setZyyUserProvinceId(Long zyyUserProvinceId) {
		this.zyyUserProvinceId = zyyUserProvinceId;
	}

	public Long getTargetPlaceProvinceId() {
		return targetPlaceProvinceId;
	}

	public void setTargetPlaceProvinceId(Long targetPlaceProvinceId) {
		this.targetPlaceProvinceId = targetPlaceProvinceId;
	}

	public Long getTargetPlaceCityId() {
		return targetPlaceCityId;
	}

	public void setTargetPlaceCityId(Long targetPlaceCityId) {
		this.targetPlaceCityId = targetPlaceCityId;
	}

	public String getAccountPassword() {
		return accountPassword;
	}

	public void setAccountPassword(String accountPassword) {
		this.accountPassword = accountPassword;
	}

	public Long getZyyBaseStdId() {
		return zyyBaseStdId;
	}

	public void setZyyBaseStdId(Long zyyBaseStdId) {
		this.zyyBaseStdId = zyyBaseStdId;
	}

	public String getRecruitStatus() {
		return recruitStatus;
	}

	public void setRecruitStatus(String recruitStatus) {
		this.recruitStatus = recruitStatus;
	}
	
}
