package com.hys.zyy.manage.model;

import java.io.Serializable;
import java.util.Date;

/**
 * @desc 结业证书表
 * <AUTHOR>
 * 
 */
public class ZyyGraduationCert extends ZyyBaseObject implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -4373546045049756033L;

	private Long certId;// 证书ID
	private Long groupId;// 批次
	private String serializableNo;//流水号
	private String stuBaseName;// 发证培训基地
	private String stuMajor;// 发证培训专业
	private String gradUserType; //证书人员类型-导入
	private String stuStartDate;// 培训开始时间
	private String stuEndDate;// 培训结束时间
	private String cerNo;// 证件号
	private Integer auditStatus;// 审核状态
	private String credentialNo;// 证书编号
	private Long provinceId;//省份ID
	private Date createTime;// 创建时间
	private Date updateTime;// 更新时间
	private String pubCertDate;//发证日期
	private String convergenceSchool;//学位衔接学校

	public Long getCertId() {
		return certId;
	}

	public void setCertId(Long certId) {
		this.certId = certId;
	}
	public String getStuBaseName() {
		return stuBaseName;
	}
	public void setStuBaseName(String stuBaseName) {
		this.stuBaseName = stuBaseName;
	}
	
	public String getStuMajor() {
		return stuMajor;
	}
	public void setStuMajor(String stuMajor) {
		this.stuMajor = stuMajor;
	}
	public String getStuStartDate() {
		return stuStartDate;
	}

	public void setStuStartDate(String stuStartDate) {
		this.stuStartDate = stuStartDate;
	}

	public String getStuEndDate() {
		return stuEndDate;
	}

	public void setStuEndDate(String stuEndDate) {
		this.stuEndDate = stuEndDate;
	}

	public String getCerNo() {
		return cerNo;
	}

	public void setCerNo(String cerNo) {
		this.cerNo = cerNo;
	}

	public Integer getAuditStatus() {
		return auditStatus;
	}

	public void setAuditStatus(Integer auditStatus) {
		this.auditStatus = auditStatus;
	}

	public String getCredentialNo() {
		return credentialNo;
	}

	public void setCredentialNo(String credentialNo) {
		this.credentialNo = credentialNo;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
	public Long getProvinceId() {
		return provinceId;
	}
	public void setProvinceId(Long provinceId) {
		this.provinceId = provinceId;
	}

	public Long getGroupId() {
		return groupId;
	}

	public void setGroupId(Long groupId) {
		this.groupId = groupId;
	}

	public String getSerializableNo() {
		return serializableNo;
	}

	public void setSerializableNo(String serializableNo) {
		this.serializableNo = serializableNo;
	}

	public String getGradUserType() {
		return gradUserType;
	}

	public void setGradUserType(String gradUserType) {
		this.gradUserType = gradUserType;
	}

	public String getPubCertDate() {
		return pubCertDate;
	}

	public void setPubCertDate(String pubCertDate) {
		this.pubCertDate = pubCertDate;
	}

	public String getConvergenceSchool() {
		return convergenceSchool;
	}

	public void setConvergenceSchool(String convergenceSchool) {
		this.convergenceSchool = convergenceSchool;
	}
	
}
