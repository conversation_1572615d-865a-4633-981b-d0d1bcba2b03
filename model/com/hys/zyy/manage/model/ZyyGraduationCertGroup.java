package com.hys.zyy.manage.model;

import java.io.Serializable;
import java.util.Date;

import com.hys.zyy.manage.util.annotation.Table;

/**
 * @desc 结业证书批次表
 * <AUTHOR>
 */
@Table("ZYY_GRADUATION_CERT_GROUP")
public class ZyyGraduationCertGroup extends ZyyBaseObject implements Serializable {

	private static final long serialVersionUID = -4687512148429878264L;

	private Long groupId;// 唯一标识
	private String groupName;// 批次名称
	private Date auditStartTime;// 审核开始时间,
	private Date auditEndTime;// 审核结束时间,
	private Integer status;// 批次状态 0正常 1已删除
	private Long provinceId;//省ID
	private Date createTime;// 创建时间
	private Date updateTime;// 修改时间
	
	public Long getGroupId() {
		return groupId;
	}

	public void setGroupId(Long groupId) {
		this.groupId = groupId;
	}
	public String getGroupName() {
		return groupName;
	}
	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}
	public Date getAuditStartTime() {
		return auditStartTime;
	}
	public void setAuditStartTime(Date auditStartTime) {
		this.auditStartTime = auditStartTime;
	}
	public Date getAuditEndTime() {
		return auditEndTime;
	}
	public void setAuditEndTime(Date auditEndTime) {
		this.auditEndTime = auditEndTime;
	}
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	public Date getUpdateTime() {
		return updateTime;
	}
	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
	public Long getProvinceId() {
		return provinceId;
	}
	public void setProvinceId(Long provinceId) {
		this.provinceId = provinceId;
	}
	
}
