package com.hys.zyy.manage.model;

public class ZyyGraduationCertVo extends ZyyGraduationCert {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1179986330837912457L;
	
	private Long qHospitalId;//培训基地
	private Long qGradeNo;//年级
	private Long qStuMajor;//专业
	private Long qStuYears;//培训年限
	private Long qGroupId;//批次，考试年限
	private String qRealName;//姓名
	private String qCerNo;//证件号
	private String qConvergenceSchool;
	private Integer hrs1;//学员身份-单位人
	private Integer hrs2;//学员身份-社会人
	private Integer hrs3;//学员身份-学位衔接
	
	private String impSeqNo;//序号
	private String groupName;  //年限，批次
	private String gradeNo; //年级
	private String stuRealBaseName;//培训基地
	private String stuRealMajor;//培训专业
	private String realName;//姓名
	private String sex; // 性别
	private String stuYears;//培训年限
	private Integer residencySource;//学员身份1 -单位人, 2 -社会人, 3-学位衔接
	private Integer degreeConvergence;//0否，1是
	private String photoPath;//用户头像
	private String identityType; // 学员身份：单位人，社会人，学位衔接
	
	
	/*** 导入证书使用 ***/
	private String graduOrgId;//机构ID
	private String graduOrgName;//机构名称
	private String year;// 年份
	private String regionCode;// 证书使用，区域列表
	private String seqNo;// 证书序列号
	private String credentialNo;// 证书编号
	
	public String getGroupName() {
		return groupName;
	}
	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}
	public String getGradeNo() {
		return gradeNo;
	}
	public void setGradeNo(String gradeNo) {
		this.gradeNo = gradeNo;
	}
	public String getStuRealBaseName() {
		return stuRealBaseName;
	}

	public void setStuRealBaseName(String stuRealBaseName) {
		this.stuRealBaseName = stuRealBaseName;
	}

	public String getStuRealMajor() {
		return stuRealMajor;
	}

	public void setStuRealMajor(String stuRealMajor) {
		this.stuRealMajor = stuRealMajor;
	}
	public String getRealName() {
		return realName;
	}
	public void setRealName(String realName) {
		this.realName = realName;
	}
	public String getStuYears() {
		return stuYears;
	}
	public void setStuYears(String stuYears) {
		this.stuYears = stuYears;
	}
	public Long getqHospitalId() {
		return qHospitalId;
	}
	public void setqHospitalId(Long qHospitalId) {
		this.qHospitalId = qHospitalId;
	}
	public Long getqGradeNo() {
		return qGradeNo;
	}
	public void setqGradeNo(Long qGradeNo) {
		this.qGradeNo = qGradeNo;
	}
	public Long getqStuMajor() {
		return qStuMajor;
	}
	public void setqStuMajor(Long qStuMajor) {
		this.qStuMajor = qStuMajor;
	}
	public Long getqStuYears() {
		return qStuYears;
	}
	public void setqStuYears(Long qStuYears) {
		this.qStuYears = qStuYears;
	}
	public Long getqGroupId() {
		return qGroupId;
	}
	public void setqGroupId(Long qGroupId) {
		this.qGroupId = qGroupId;
	}
	public String getqRealName() {
		return qRealName;
	}
	public void setqRealName(String qRealName) {
		this.qRealName = qRealName;
	}
	public String getqCerNo() {
		return qCerNo;
	}
	public void setqCerNo(String qCerNo) {
		this.qCerNo = qCerNo;
	}
	public Integer getHrs1() {
		return hrs1;
	}
	public void setHrs1(Integer hrs1) {
		this.hrs1 = hrs1;
	}
	public Integer getHrs2() {
		return hrs2;
	}
	public void setHrs2(Integer hrs2) {
		this.hrs2 = hrs2;
	}
	public Integer getHrs3() {
		return hrs3;
	}
	public void setHrs3(Integer hrs3) {
		this.hrs3 = hrs3;
	}
	public String getImpSeqNo() {
		return impSeqNo;
	}
	public void setImpSeqNo(String impSeqNo) {
		this.impSeqNo = impSeqNo;
	}
	public Integer getResidencySource() {
		return residencySource;
	}
	public void setResidencySource(Integer residencySource) {
		this.residencySource = residencySource;
	}
	public Integer getDegreeConvergence() {
		return degreeConvergence;
	}
	public void setDegreeConvergence(Integer degreeConvergence) {
		this.degreeConvergence = degreeConvergence;
	}
	public String getPhotoPath() {
		return photoPath;
	}
	public void setPhotoPath(String photoPath) {
		this.photoPath = photoPath;
	}
	public String getIdentityType() {
		return identityType;
	}
	public void setIdentityType(String identityType) {
		this.identityType = identityType;
	}
	public String getSex() {
		return sex;
	}
	public void setSex(String sex) {
		this.sex = sex == null ? null : sex.trim();
	}
	public String getGraduOrgId() {
		return graduOrgId;
	}
	public void setGraduOrgId(String graduOrgId) {
		this.graduOrgId = graduOrgId;
	}
	public String getGraduOrgName() {
		return graduOrgName;
	}
	public void setGraduOrgName(String graduOrgName) {
		this.graduOrgName = graduOrgName;
	}
	public String getYear() {
		return year;
	}
	public void setYear(String year) {
		this.year = year;
	}
	public String getRegionCode() {
		return regionCode;
	}
	public void setRegionCode(String regionCode) {
		this.regionCode = regionCode;
	}
	public String getSeqNo() {
		return seqNo;
	}
	public void setSeqNo(String seqNo) {
		this.seqNo = seqNo;
	}
	public String getCredentialNo() {
		return credentialNo;
	}
	public void setCredentialNo(String credentialNo) {
		this.credentialNo = credentialNo;
	}
	public String getqConvergenceSchool() {
		return qConvergenceSchool;
	}
	public void setqConvergenceSchool(String qConvergenceSchool) {
		this.qConvergenceSchool = qConvergenceSchool;
	}
	
}
