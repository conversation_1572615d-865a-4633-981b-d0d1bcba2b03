package com.hys.zyy.manage.model;

import java.math.BigDecimal;
import java.util.Date;

public class ZyyGraduationExamResult {

	private Long id;
	
	private Long zyyUserId;
	
	private Long examId;
	
	/**
	 * 理论考核时间
	 */
	private Date theoryExamDate;
	
	/**
	 * 理论考核成绩
	 */
	private BigDecimal theoryExamScore;
	
	/**
	 * 理论考核结果  1:通过;2不通过
	 */
	private Integer theoryExamResult;
	
	/**
	 * 技能考核成绩
	 */
	private BigDecimal skillExamScore;
	
	/**
	 * 技能考核结果 1:通过;2不通过
	 */
	private Integer skillExamResult;
	
	/**
	 * 结业考试综合结果 1:通过;2不通过
	 */
	private Integer examResult;
	
	/**
	 * 培训过程考核是否合格 1:通过;2不通过
	 */
	private Integer trainProcessResult;
	
	private Date createDate;
	
	private Date lastUpdateDate;
	
	/**
	 * 考核类型 1：理论 ;2：技能
	 */
	private Integer examType;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getZyyUserId() {
		return zyyUserId;
	}

	public void setZyyUserId(Long zyyUserId) {
		this.zyyUserId = zyyUserId;
	}

	public Long getExamId() {
		return examId;
	}

	public void setExamId(Long examId) {
		this.examId = examId;
	}

	public Date getTheoryExamDate() {
		return theoryExamDate;
	}

	public void setTheoryExamDate(Date theoryExamDate) {
		this.theoryExamDate = theoryExamDate;
	}

	public BigDecimal getTheoryExamScore() {
		return theoryExamScore;
	}

	public void setTheoryExamScore(BigDecimal theoryExamScore) {
		this.theoryExamScore = theoryExamScore;
	}

	public Integer getTheoryExamResult() {
		return theoryExamResult;
	}

	public void setTheoryExamResult(Integer theoryExamResult) {
		this.theoryExamResult = theoryExamResult;
	}

	public BigDecimal getSkillExamScore() {
		return skillExamScore;
	}

	public void setSkillExamScore(BigDecimal skillExamScore) {
		this.skillExamScore = skillExamScore;
	}

	public Integer getSkillExamResult() {
		return skillExamResult;
	}

	public void setSkillExamResult(Integer skillExamResult) {
		this.skillExamResult = skillExamResult;
	}

	public Integer getExamResult() {
		return examResult;
	}

	public void setExamResult(Integer examResult) {
		this.examResult = examResult;
	}

	public Integer getTrainProcessResult() {
		return trainProcessResult;
	}

	public void setTrainProcessResult(Integer trainProcessResult) {
		this.trainProcessResult = trainProcessResult;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Date getLastUpdateDate() {
		return lastUpdateDate;
	}

	public void setLastUpdateDate(Date lastUpdateDate) {
		this.lastUpdateDate = lastUpdateDate;
	}

	public Integer getExamType() {
		return examType;
	}

	public void setExamType(Integer examType) {
		this.examType = examType;
	}
	
}
