package com.hys.zyy.manage.model;

import java.util.Date;
import java.util.List;

/**
 * 
 * 标题：手册填写字段标准
 * 
 * 作者：ccj
 * 
 * 描述：
 * 
 * 说明:
 */
public class ZyyHandBookFieldStd extends ZyyBaseObject {

	/**
	 * 
	 */
	private static final long serialVersionUID = -5859051122782168228L;
	private String fieldName;//字段名称
	private Long handBookStdId;         //标准手册ID
	private int fieldType;//字段类型
	private String fieldDesc;//字段描述
	private Long fieldMaxLength;//最大长度
	private Long deptStdId;//标准科室ID
	private Long seq;//顺序
	private Long isRequired;//是否必填

	private String fieldValue;//字段序列值
	private String[] fields;

	public String getFieldName() {
		return fieldName;
	}

	public void setFieldName(String fieldName) {
		this.fieldName = fieldName;
	}

	public Long getHandBookStdId() {
		return handBookStdId;
	}

	public void setHandBookStdId(Long handBookStdId) {
		this.handBookStdId = handBookStdId;
	}


	public int getFieldType() {
		return fieldType;
	}

	public void setFieldType(int fieldType) {
		this.fieldType = fieldType;
	}

	public String getFieldDesc() {
		return fieldDesc;
	}

	public void setFieldDesc(String fieldDesc) {
		this.fieldDesc = fieldDesc;
	}

	public Long getFieldMaxLength() {
		return fieldMaxLength;
	}

	public void setFieldMaxLength(Long fieldMaxLength) {
		this.fieldMaxLength = fieldMaxLength;
	}

	public Long getDeptStdId() {
		return deptStdId;
	}

	public void setDeptStdId(Long deptStdId) {
		this.deptStdId = deptStdId;
	}

	public Long getSeq() {
		return seq;
	}

	public void setSeq(Long seq) {
		this.seq = seq;
	}

	public Long getIsRequired() {
		return isRequired;
	}

	public void setIsRequired(Long isRequired) {
		this.isRequired = isRequired;
	}

	public String getFieldValue() {
		return fieldValue;
	}

	public void setFieldValue(String fieldValue) {
		this.fieldValue = fieldValue;
	}

	public String[] getFields() {
		return fields;
	}

	public void setFields(String[] fields) {
		this.fields = fields;
	}

	

}
