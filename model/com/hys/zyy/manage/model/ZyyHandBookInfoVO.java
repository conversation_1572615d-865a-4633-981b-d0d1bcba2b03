package com.hys.zyy.manage.model;

import java.io.Serializable;

public class ZyyHandBookInfoVO implements Serializable {
	private static final long serialVersionUID = 1L;
	/*
	 * 学员ID
	 */
	private Long residencyId;
	/*
	 * 手册名称
	 */
	private String handBookName;
	/*
	 * 科室轮转时间段
	 */
	private String cycleDateSection;
	/*
	 * 轮转科室
	 */
	private String cycleDept;
	/*
	 * 带教时间段
	 */
	private String teachDateSection;
	/*
	 * 带教老师
	 */
	private String teacherName;
	/*
	 * 手册提交状态
	 */
	private String handBookSubmitStateStr;
	/*
	 * 带教审核状态
	 */
	private Integer teacherAuditState;
	/*
	 * 科室审核状态
	 */
	private Integer deptAuditState;
	/*
	 * 基地审核状态
	 */
	private Integer baseAuditState;
	/*
	 * 医院审核状态
	 */
	private Integer hospAuditState;
	/*
	 * 终审
	 */
	private Integer finalCheckStatus;

	private Integer auditState;
	private String auditStateStr;

	public ZyyHandBookInfoVO() {
		super();
	}

	public ZyyHandBookInfoVO(Long residencyId) {
		super();
		this.residencyId = residencyId;
	}

	public Long getResidencyId() {
		return residencyId;
	}

	public void setResidencyId(Long residencyId) {
		this.residencyId = residencyId;
	}

	public String getHandBookName() {
		return handBookName;
	}

	public void setHandBookName(String handBookName) {
		this.handBookName = handBookName;
	}

	public String getCycleDateSection() {
		return cycleDateSection;
	}

	public void setCycleDateSection(String cycleDateSection) {
		this.cycleDateSection = cycleDateSection;
	}

	public String getCycleDept() {
		return cycleDept;
	}

	public void setCycleDept(String cycleDept) {
		this.cycleDept = cycleDept;
	}

	public String getTeachDateSection() {
		return teachDateSection;
	}

	public void setTeachDateSection(String teachDateSection) {
		this.teachDateSection = teachDateSection;
	}

	public String getTeacherName() {
		return teacherName;
	}

	public void setTeacherName(String teacherName) {
		this.teacherName = teacherName;
	}

	public String getHandBookSubmitStateStr() {
		return handBookSubmitStateStr;
	}

	public void setHandBookSubmitStateStr(String handBookSubmitStateStr) {
		this.handBookSubmitStateStr = handBookSubmitStateStr;
	}

	public Integer getTeacherAuditState() {
		return teacherAuditState;
	}

	public void setTeacherAuditState(Integer teacherAuditState) {
		this.teacherAuditState = teacherAuditState;
	}

	public Integer getDeptAuditState() {
		return deptAuditState;
	}

	public void setDeptAuditState(Integer deptAuditState) {
		this.deptAuditState = deptAuditState;
	}

	public Integer getBaseAuditState() {
		return baseAuditState;
	}

	public void setBaseAuditState(Integer baseAuditState) {
		this.baseAuditState = baseAuditState;
	}

	public Integer getHospAuditState() {
		return hospAuditState;
	}

	public void setHospAuditState(Integer hospAuditState) {
		this.hospAuditState = hospAuditState;
	}

	public Integer getFinalCheckStatus() {
		return finalCheckStatus;
	}

	public void setFinalCheckStatus(Integer finalCheckStatus) {
		this.finalCheckStatus = finalCheckStatus;
	}
	
	public Integer getAuditState() {
		return auditState;
	}

	public void setAuditState(Integer auditState) {
		this.auditState = auditState;
	}

	public String getAuditStateStr() {
		return auditStateStr;
	}

	public void setAuditStateStr(String auditStateStr) {
		this.auditStateStr = auditStateStr;
	}

}
