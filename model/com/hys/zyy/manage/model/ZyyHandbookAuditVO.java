package com.hys.zyy.manage.model;

import java.util.Date;


/**
 * yjk 登记手册修改 添加的vo类
 */

public class ZyyHandbookAuditVO extends ZyyBaseObject {

	/**
	 * 
	 */
	private static final long serialVersionUID = 3825261477026089420L;
	private Long residencyId;
	private String userName;
	private Long deptId;
	private String deptName;
	private Integer finalCheckStatus;//终审审核状态
	private String timeSection;
	private Long dstdId;//标准科室id
	private String handbookName;//登记手册名称--标准科室名称
	private Date cycleStartDate;//轮转开始时间(分段)
	private Date cycleEndDate;//轮转结束时间(分段)
	private Date tStartDate;//带教开始时间
	private Date tEndDate;//带教结束时间
	private Long teacherId;//带教id
	private String teacherName;
	private Long teacherHdChkId;//zyy_cycle_resi_hd_check这个表的id
	private Long deptHdChkId;//zyy_cycle_resi_hd_check这个表的id
	private Long baseHdChkId;//zyy_cycle_resi_hd_check这个表的id
	private Long hospHdChkId;//zyy_cycle_resi_hd_check这个表的id
	private Integer teacherCycleStatus;//带教审核状态  轮转部分
	private Integer deptCycleStatus;//科室审核状态  轮转部分
	private Integer baseCycleStatus;//学科审核状态  轮转部分
	private Integer hospCycleStatus;//医院审核状态  轮转部分
	private Long otherChkId;//其它部分审核表的主表id
	private Integer ochkCommitTimes;//其它部分提交次数
	private Date ochkCommitDate;//其它部分提交时间
	private Integer teacherOtherStatus;//其它部分 带教审核状态
	private Integer deptOtherStatus;//其它部分 科室审核状态
	private Integer baseOtherStatus;//其它部分 学科审核状态
	private Integer hospOtherStatus;//其它部分 医院审核状态
	private Date enterDate; //入科时间
	private Date leaveDate;//出科时间
	private Long hdChKId;//zyy_residency_handbook_dept表的ID
	private Date residencyLastDate;//手册最后提交时间
	/*
	 * 手册标准ID
	 */
	private Long manualStdId;

	public Long getManualStdId() {
		return manualStdId;
	}

	public void setManualStdId(Long manualStdId) {
		this.manualStdId = manualStdId;
	}

	public Date getResidencyLastDate() {
		return residencyLastDate;
	}

	public void setResidencyLastDate(Date residencyLastDate) {
		this.residencyLastDate = residencyLastDate;
	}

	public Long getHdChKId() {
		return hdChKId;
	}

	public void setHdChKId(Long hdChKId) {
		this.hdChKId = hdChKId;
	}
	public Date getEnterDate() {
		return enterDate;
	}
	public void setEnterDate(Date enterDate) {
		this.enterDate = enterDate;
	}
	public Date getLeaveDate() {
		return leaveDate;
	}
	public void setLeaveDate(Date leaveDate) {
		this.leaveDate = leaveDate;
	}
	public Integer getOchkCommitTimes() {
		return ochkCommitTimes;
	}
	public void setOchkCommitTimes(Integer ochkCommitTimes) {
		this.ochkCommitTimes = ochkCommitTimes;
	}
	public Date getOchkCommitDate() {
		return ochkCommitDate;
	}
	public void setOchkCommitDate(Date ochkCommitDate) {
		this.ochkCommitDate = ochkCommitDate;
	}
	public Integer getFinalCheckStatus() {
		return finalCheckStatus;
	}
	public void setFinalCheckStatus(Integer finalCheckStatus) {
		this.finalCheckStatus = finalCheckStatus;
	}
	public Long getTeacherHdChkId() {
		return teacherHdChkId;
	}
	public void setTeacherHdChkId(Long teacherHdChkId) {
		this.teacherHdChkId = teacherHdChkId;
	}
	public Long getDeptHdChkId() {
		return deptHdChkId;
	}
	public void setDeptHdChkId(Long deptHdChkId) {
		this.deptHdChkId = deptHdChkId;
	}
	public Long getBaseHdChkId() {
		return baseHdChkId;
	}
	public void setBaseHdChkId(Long baseHdChkId) {
		this.baseHdChkId = baseHdChkId;
	}
	public Long getHospHdChkId() {
		return hospHdChkId;
	}
	public void setHospHdChkId(Long hospHdChkId) {
		this.hospHdChkId = hospHdChkId;
	}
	public Integer getTeacherCycleStatus() {
		return teacherCycleStatus;
	}
	public void setTeacherCycleStatus(Integer teacherCycleStatus) {
		this.teacherCycleStatus = teacherCycleStatus;
	}
	public Integer getDeptCycleStatus() {
		return deptCycleStatus;
	}
	public void setDeptCycleStatus(Integer deptCycleStatus) {
		this.deptCycleStatus = deptCycleStatus;
	}
	public Integer getBaseCycleStatus() {
		return baseCycleStatus;
	}
	public void setBaseCycleStatus(Integer baseCycleStatus) {
		this.baseCycleStatus = baseCycleStatus;
	}
	public Integer getHospCycleStatus() {
		return hospCycleStatus;
	}
	public void setHospCycleStatus(Integer hospCycleStatus) {
		this.hospCycleStatus = hospCycleStatus;
	}
	public Long getOtherChkId() {
		return otherChkId;
	}
	public void setOtherChkId(Long otherChkId) {
		this.otherChkId = otherChkId;
	}
	public Integer getTeacherOtherStatus() {
		return teacherOtherStatus;
	}
	public void setTeacherOtherStatus(Integer teacherOtherStatus) {
		this.teacherOtherStatus = teacherOtherStatus;
	}
	public Integer getDeptOtherStatus() {
		return deptOtherStatus;
	}
	public void setDeptOtherStatus(Integer deptOtherStatus) {
		this.deptOtherStatus = deptOtherStatus;
	}
	public Integer getBaseOtherStatus() {
		return baseOtherStatus;
	}
	public void setBaseOtherStatus(Integer baseOtherStatus) {
		this.baseOtherStatus = baseOtherStatus;
	}
	public Integer getHospOtherStatus() {
		return hospOtherStatus;
	}
	public void setHospOtherStatus(Integer hospOtherStatus) {
		this.hospOtherStatus = hospOtherStatus;
	}
	public Long getResidencyId() {
		return residencyId;
	}
	public void setResidencyId(Long residencyId) {
		this.residencyId = residencyId;
	}
	public String getUserName() {
		return userName;
	}
	public void setUserName(String userName) {
		this.userName = userName;
	}
	public Long getDeptId() {
		return deptId;
	}
	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}
	public String getDeptName() {
		return deptName;
	}
	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}
	public String getTimeSection() {
		return timeSection;
	}
	public void setTimeSection(String timeSection) {
		this.timeSection = timeSection;
	}
	public Long getDstdId() {
		return dstdId;
	}
	public void setDstdId(Long dstdId) {
		this.dstdId = dstdId;
	}
	public String getHandbookName() {
		return handbookName;
	}
	public void setHandbookName(String handbookName) {
		this.handbookName = handbookName;
	}
	public Date getCycleStartDate() {
		return cycleStartDate;
	}
	public void setCycleStartDate(Date cycleStartDate) {
		this.cycleStartDate = cycleStartDate;
	}
	public Date getCycleEndDate() {
		return cycleEndDate;
	}
	public void setCycleEndDate(Date cycleEndDate) {
		this.cycleEndDate = cycleEndDate;
	}
	public Date gettStartDate() {
		return tStartDate;
	}
	public void settStartDate(Date tStartDate) {
		this.tStartDate = tStartDate;
	}
	public Date gettEndDate() {
		return tEndDate;
	}
	public void settEndDate(Date tEndDate) {
		this.tEndDate = tEndDate;
	}
	public Long getTeacherId() {
		return teacherId;
	}
	public void setTeacherId(Long teacherId) {
		this.teacherId = teacherId;
	}
	public String getTeacherName() {
		return teacherName;
	}
	public void setTeacherName(String teacherName) {
		this.teacherName = teacherName;
	}
	
	
}
