package com.hys.zyy.manage.model;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：陈明凯 2012-7-2
 * 
 * 描述：
 * 
 * 说明:
 */
public class ZyyHandbookStd extends ZyyBaseObject implements Comparable<ZyyHandbookStd> {
	private static final long serialVersionUID = 5454155825314635342L;

	// alias
	public static final String TABLE_ALIAS = "ZyyHandbookStd";
	public static final String ALIAS_ID = "id";
	public static final String ALIAS_PARENT_BOOK_ID = "parentBookId";
	public static final String ALIAS_NAME = "name";
	public static final String ALIAS_ALIAS_NAME = "aliasName";

	private java.lang.Long id;
	private java.lang.Long parentBookId;
	private java.lang.String name;
	private java.lang.String aliasName;
	private java.lang.Long relId;		//	关联ID	基地ID或科室ID
	private java.lang.Long orgId;
	private Long seq;
	
	private List<ZyyHandbookStd> children = new ArrayList<ZyyHandbookStd>();

	public ZyyHandbookStd() {
	}
	
	public void addChild(ZyyHandbookStd entity) {
		this.children.add(entity);
	}

	public ZyyHandbookStd(java.lang.Long id) {
		this.id = id;
	}

	public void setId(java.lang.Long value) {
		this.id = value;
	}

	public java.lang.Long getId() {
		return this.id;
	}

	public void setParentBookId(java.lang.Long value) {
		this.parentBookId = value;
	}

	public java.lang.Long getParentBookId() {
		return this.parentBookId;
	}

	public void setName(java.lang.String value) {
		this.name = value;
	}

	public java.lang.String getName() {
		return this.name;
	}

	public void setAliasName(java.lang.String value) {
		this.aliasName = value;
	}

	public java.lang.String getAliasName() {
		return this.aliasName;
	}

	public String toString() {
		return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
				.append("Id", getId())
				.append("ParentBookId", getParentBookId())
				.append("Name", getName())
				.append("AliasName", getAliasName())
				.append("children", getChildren())
				.toString();
	}

	public List<ZyyHandbookStd> getChildren() {
		return children;
	}

	public void setChildren(List<ZyyHandbookStd> children) {
		this.children = children;
	}

	public java.lang.Long getOrgId() {
		return orgId;
	}

	public void setOrgId(java.lang.Long orgId) {
		this.orgId = orgId;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		result = prime * result + ((relId == null) ? 0 : relId.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ZyyHandbookStd other = (ZyyHandbookStd) obj;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		if (relId == null) {
			if (other.relId != null)
				return false;
		} else if (!relId.equals(other.relId))
			return false;
		return true;
	}

	public Long getSeq() {
		return seq;
	}

	public void setSeq(Long seq) {
		this.seq = seq;
	}

	public java.lang.Long getRelId() {
		return relId;
	}

	public void setRelId(java.lang.Long relId) {
		this.relId = relId;
	}

	@Override
	public int compareTo(ZyyHandbookStd o) {
		return this.seq.compareTo(o.getSeq());
	}

}