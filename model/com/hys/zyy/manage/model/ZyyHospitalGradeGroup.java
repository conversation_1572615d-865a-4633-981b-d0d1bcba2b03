package com.hys.zyy.manage.model;

import java.util.Date;

/**
 * 医院分班分组
 * <AUTHOR>
 * @date 2019-6-13上午10:41:26
 */
public class ZyyHospitalGradeGroup {
	//医院ID	
	private Long hospitalId;
	//年级	
	private Long yearId;
	//专业	
	private String profVal;
	//分班上限	   大于等于1，上限10个班
	private Integer gradeMax;
	//分组上限	  大于等于1，上限20个组
	private Integer groupMax;
	//创建时间	
	private Date createTime;
	//创建人	
	private Long createUser;
	
	//未存库字段
	//专业名称
	private String profName;
	//年级名称
	private String yearName;
	
	public Long getHospitalId() {
		return hospitalId;
	}
	public void setHospitalId(Long hospitalId) {
		this.hospitalId = hospitalId;
	}
	public Long getYearId() {
		return yearId;
	}
	public void setYearId(Long yearId) {
		this.yearId = yearId;
	}
	public String getProfVal() {
		return profVal;
	}
	public void setProfVal(String profVal) {
		this.profVal = profVal;
	}
	public Integer getGradeMax() {
		return gradeMax;
	}
	public void setGradeMax(Integer gradeMax) {
		this.gradeMax = gradeMax;
	}
	public Integer getGroupMax() {
		return groupMax;
	}
	public void setGroupMax(Integer groupMax) {
		this.groupMax = groupMax;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	public Long getCreateUser() {
		return createUser;
	}
	public void setCreateUser(Long createUser) {
		this.createUser = createUser;
	}
	public String getProfName() {
		return profName;
	}
	public void setProfName(String profName) {
		this.profName = profName;
	}
	public String getYearName() {
		return yearName;
	}
	public void setYearName(String yearName) {
		this.yearName = yearName;
	}
}
