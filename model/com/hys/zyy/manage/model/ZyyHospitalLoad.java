package com.hys.zyy.manage.model;

import java.io.Serializable;
import java.util.Date;

import com.hys.zyy.manage.util.BaseModel;

/**
 * 基地专硕生容量
 */
public class ZyyHospitalLoad extends BaseModel implements Serializable {
	private static final long serialVersionUID = 1L;
	/*
	 * ID
	 */
	private String id;
	/*
	 * 医院ID
	 */
	private Long hospitalId;
	/*
	 * 招录年度
	 */
	private Long zyyRecruitYearId;
	/*
	 * 中西医
	 */
	private Integer hospType;
	/*
	 * 专硕生最大容量
	 */
	private Integer maxNumber;
	/*
	 * 状态（1=有效；-1=失效）
	 */
	private Integer state;

	private Date createTime;

	private Date updateTime;

	public ZyyHospitalLoad() {
		super();
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public Long getHospitalId() {
		return hospitalId;
	}

	public void setHospitalId(Long hospitalId) {
		this.hospitalId = hospitalId;
	}

	public Long getZyyRecruitYearId() {
		return zyyRecruitYearId;
	}

	public void setZyyRecruitYearId(Long zyyRecruitYearId) {
		this.zyyRecruitYearId = zyyRecruitYearId;
	}

	public Integer getHospType() {
		return hospType;
	}

	public void setHospType(Integer hospType) {
		this.hospType = hospType;
	}

	public Integer getMaxNumber() {
		return maxNumber;
	}

	public void setMaxNumber(Integer maxNumber) {
		this.maxNumber = maxNumber;
	}

	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ZyyHospitalLoad other = (ZyyHospitalLoad) obj;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		return true;
	}

	@Override
	public String toString() {
		return "ZyyHospitalLoad [id=" + id + ", hospitalId=" + hospitalId
				+ ", zyyRecruitYearId=" + zyyRecruitYearId + ", hospType="
				+ hospType + ", maxNumber=" + maxNumber + ", state=" + state
				+ ", createTime=" + createTime + ", updateTime=" + updateTime
				+ "]";
	}

}
