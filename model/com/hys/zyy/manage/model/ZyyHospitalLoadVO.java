package com.hys.zyy.manage.model;

public class ZyyHospitalLoadVO extends ZyyHospitalLoad {

	private Long zyyUserProvinceId;
	private Integer tg, wsh, total, ye;
	private String hospitalName, hospTypeStr, orgTypeName;

	public Long getZyyUserProvinceId() {
		return zyyUserProvinceId;
	}

	public void setZyyUserProvinceId(Long zyyUserProvinceId) {
		this.zyyUserProvinceId = zyyUserProvinceId;
	}

	public Integer getTg() {
		return tg;
	}

	public void setTg(Integer tg) {
		this.tg = tg;
	}

	public Integer getWsh() {
		return wsh;
	}

	public void setWsh(Integer wsh) {
		this.wsh = wsh;
	}

	public Integer getTotal() {
		return total;
	}

	public void setTotal(Integer total) {
		this.total = total;
	}

	public Integer getYe() {
		return ye;
	}

	public void setYe(Integer ye) {
		this.ye = ye;
	}

	public String getHospitalName() {
		return hospitalName;
	}

	public void setHospitalName(String hospitalName) {
		this.hospitalName = hospitalName == null ? null : hospitalName.trim();
	}

	public String getHospTypeStr() {
		return hospTypeStr;
	}

	public void setHospTypeStr(String hospTypeStr) {
		this.hospTypeStr = hospTypeStr == null ? null : hospTypeStr.trim();
	}

	public String getOrgTypeName() {
		return orgTypeName;
	}

	public void setOrgTypeName(String orgTypeName) {
		this.orgTypeName = orgTypeName == null ? null : orgTypeName.trim();
	}

}
