package com.hys.zyy.manage.model;

import java.util.Date;

/**
 * 医院题型设置
 * <AUTHOR>
 * @date 2020-3-30下午3:28:56
 */
public class ZyyHospitalQuesSetting extends ZyyBaseObject {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = -7862461223040015749L;
	//	id	
	private Long id;	
//	医院id	
	private Long hospitalId;	
//	试卷分数	
	private Integer paperScore;	
//	试题数量	
	private Integer questionTotal;	
//	单选题a1	
	private Integer questionAone;	
//	单选题a1分数	
	private Integer questionAoneScore;	
//	单选题a2	
	private Integer questionAtwo;	
//	单选题a2分数	
	private Integer questionAtwoScore;	
//	难度		1.简单 2.中等 3.较难
	private Integer difficultyType;
//	创建时间	
	private Date createDate;	
//	更新时间	
	private Date updateDate;
	//考试那边存的机构id
	private Long emSysOrgId;
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getHospitalId() {
		return hospitalId;
	}
	public void setHospitalId(Long hospitalId) {
		this.hospitalId = hospitalId;
	}
	public Integer getPaperScore() {
		return paperScore;
	}
	public void setPaperScore(Integer paperScore) {
		this.paperScore = paperScore;
	}
	public Integer getQuestionTotal() {
		return questionTotal;
	}
	public void setQuestionTotal(Integer questionTotal) {
		this.questionTotal = questionTotal;
	}
	public Integer getQuestionAone() {
		return questionAone;
	}
	public void setQuestionAone(Integer questionAone) {
		this.questionAone = questionAone;
	}
	public Integer getQuestionAoneScore() {
		return questionAoneScore;
	}
	public void setQuestionAoneScore(Integer questionAoneScore) {
		this.questionAoneScore = questionAoneScore;
	}
	public Integer getQuestionAtwo() {
		return questionAtwo;
	}
	public void setQuestionAtwo(Integer questionAtwo) {
		this.questionAtwo = questionAtwo;
	}
	public Integer getQuestionAtwoScore() {
		return questionAtwoScore;
	}
	public void setQuestionAtwoScore(Integer questionAtwoScore) {
		this.questionAtwoScore = questionAtwoScore;
	}
	public Integer getDifficultyType() {
		return difficultyType;
	}
	public void setDifficultyType(Integer difficultyType) {
		this.difficultyType = difficultyType;
	}
	public Date getCreateDate() {
		return createDate;
	}
	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}
	public Date getUpdateDate() {
		return updateDate;
	}
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}
	public Long getEmSysOrgId() {
		return emSysOrgId;
	}
	public void setEmSysOrgId(Long emSysOrgId) {
		this.emSysOrgId = emSysOrgId;
	}
}
