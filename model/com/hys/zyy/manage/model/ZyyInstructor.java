package com.hys.zyy.manage.model;

/**
 * 带教教员信息
 * <AUTHOR>
 *
 */
public class ZyyInstructor extends ZyyBaseObject {

	private static final long serialVersionUID = 3342467185352338727L;

	private Long userId;				// 用户ID

	private String userName;			// 用户姓名

	private String deptName;			// 部门

	private String deptIdString;        //部门ID

	private Long deptLevel;				// 部门级别

	private Long sex;					// 性别

	private String jobNumber;			// 工号

	private String position;				// 职务

	private Long title;					// 职称

	private Long status;				// 目前状态

	private Integer allowTeachingNum;  // 允许带教人数

	private Integer teachingNum;  		// 当前带教人数

	private String telphone;			// 电话

	private String email;				// 邮件

	private String mobilNumber;         //联系电话

	private String accountName;          //账号

	private String accountPassword; // 密码
	/*
	 * 微信号
	 */
	private String wechatNumber;
	/*
	 * 教师资格证书号
	 */
	private String teacQualCerNum;
	/*
	 * 教学职称
	 */
	private String teachingTitle;

	/**
	 * 身份证号
	 */
	private String certificateNo;
	// 教师资格证书 数量  0 显示无； >0 显示有
	private Integer userCertCount;
	//专业名称
	private String aliasName;
	//主讲人   是否可以选择 0 可以  1 不可以
	private Integer speakerStatus = 0;
	private String zcpysjStr;
	private Integer highestRecordSchool;
	private String highestGraduateSchool;
	/*
	 * 人员分类名称
	 */
	private String userCategoryStr;
	
	public String getUserCategoryStr() {
		return userCategoryStr;
	}

	public void setUserCategoryStr(String userCategoryStr) {
		this.userCategoryStr = userCategoryStr == null ? null : userCategoryStr.trim();
	}

	public String getDeptIdString() {
		return deptIdString;
	}

	public void setDeptIdString(String deptIdString) {
		this.deptIdString = deptIdString;
	}

	public String getCertificateNo() {
		return certificateNo;
	}

	public void setCertificateNo(String certificateNo) {
		this.certificateNo = certificateNo;
	}

	public String getAccountPassword() {
		return accountPassword;
	}

	public void setAccountPassword(String accountPassword) {
		this.accountPassword = accountPassword;
	}

	public String getMobilNumber() {
		return mobilNumber;
	}

	public void setMobilNumber(String mobilNumber) {
		this.mobilNumber = mobilNumber;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getDeptName() {
		return deptName;
	}

	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}

	public Long getSex() {
		return sex;
	}

	public void setSex(Long sex) {
		this.sex = sex;
	}

	public Long getTitle() {
		return title;
	}

	public void setTitle(Long title) {
		this.title = title;
	}

	public Long getStatus() {
		return status;
	}

	public void setStatus(Long status) {
		this.status = status;
	}

	public String getTelphone() {
		return telphone;
	}

	public void setTelphone(String telphone) {
		this.telphone = telphone;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getJobNumber() {
		return jobNumber;
	}

	public void setJobNumber(String jobNumber) {
		this.jobNumber = jobNumber;
	}

	public String getPosition() {
		return position;
	}

	public void setPosition(String position) {
		this.position = position;
	}

	public Long getDeptLevel() {
		return deptLevel;
	}

	public void setDeptLevel(Long deptLevel) {
		this.deptLevel = deptLevel;
	}

	public String getAccountName() {
		return accountName;
	}

	public void setAccountName(String accountName) {
		this.accountName = accountName;
	}

	public String getWechatNumber() {
		return wechatNumber;
	}

	public void setWechatNumber(String wechatNumber) {
		this.wechatNumber = wechatNumber == null ? null : wechatNumber.trim();
	}

	public String getTeacQualCerNum() {
		return teacQualCerNum;
	}

	public void setTeacQualCerNum(String teacQualCerNum) {
		this.teacQualCerNum = teacQualCerNum == null ? null : teacQualCerNum.trim();
	}

	public String getTeachingTitle() {
		return teachingTitle;
	}

	public void setTeachingTitle(String teachingTitle) {
		this.teachingTitle = teachingTitle == null ? null : teachingTitle.trim();
	}

	public Integer getUserCertCount() {
		return userCertCount;
	}

	public void setUserCertCount(Integer userCertCount) {
		this.userCertCount = userCertCount;
	}

	public String getAliasName() {
		return aliasName;
	}

	public void setAliasName(String aliasName) {
		this.aliasName = aliasName;
	}

	public Integer getSpeakerStatus() {
		return speakerStatus;
	}

	public void setSpeakerStatus(Integer speakerStatus) {
		this.speakerStatus = speakerStatus;
	}

	public String getZcpysjStr() {
		return zcpysjStr;
	}

	public void setZcpysjStr(String zcpysjStr) {
		this.zcpysjStr = zcpysjStr == null ? null : zcpysjStr.trim();
	}
	
	public Integer getHighestRecordSchool() {
		return highestRecordSchool;
	}

	public void setHighestRecordSchool(Integer highestRecordSchool) {
		this.highestRecordSchool = highestRecordSchool;
	}

	public String getHighestGraduateSchool() {
		return highestGraduateSchool;
	}

	public void setHighestGraduateSchool(String highestGraduateSchool) {
		this.highestGraduateSchool = highestGraduateSchool == null ? null : highestGraduateSchool.trim();
	}

	public Integer getAllowTeachingNum() {
		return allowTeachingNum;
	}

	public void setAllowTeachingNum(Integer allowTeachingNum) {
		this.allowTeachingNum = allowTeachingNum;
	}

	public Integer getTeachingNum() {
		return teachingNum;
	}

	public void setTeachingNum(Integer teachingNum) {
		this.teachingNum = teachingNum;
	}
}
