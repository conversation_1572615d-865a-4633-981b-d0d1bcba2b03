package com.hys.zyy.manage.model;

import com.hys.zyy.manage.model.vo.ZyyTeacherEvaluateVO;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 出入科记录表
 */
public class ZyyJoinDeptRecord extends ZyyBaseObject {

	/**
	 * 
	 */
	private static final long serialVersionUID = -5567702492147530318L;
	/**
	 * 主键ID
	 */
	private Long id;
	/**
	 * 专业
	 */
	private String aliasName;
	/**
	 * 科室ID
	 */
	private Long deptId;
	/**
	 * 住院医师ID
	 */
	private Long residencyId;
	/**
	 * 开始时间
	 */
	private Date startDate;
	/**
	 * 结束时间
	 */
	private Date endDate;
	/**
	 * 学员状态：1，已经入科。0，未入科。
	 */
	private Integer status;
	/**
	 * 理论成绩
	 */
	private Float theoryGrade;
	/**
	 * 轮转管理成绩
	 */
	private Float cycleManageScore;
	/**
	 * 病例分析成绩
	 */
	private Float caseAnalyseScore;
	/**
	 * 轮转管理成绩Str
	 */
	private String cycleManageScoreStr;
	/**
	 * 病例分析成绩Str
	 */
	private String caseAnalyseScoreStr;
	/*
	 * 出科累计得分
	 */
	private String ckljdf;
	/*
	 * 综合评分
	 */
	private String zhpf;
	/*
	 * 带教老师评分（老师对学员评分）
	 */
	private String djlspf;
	/*
	 * 学员评分（学员对老师评分）
	 */
	private String xypf;
	/*
	 * 出科评语
	 */
	private String ckpy;
	/*
	 * 带教老师姓名
	 */
	private String teacherNames;
	/**
	 * 技能成绩
	 */
	private Float skillGrade;
	/**
	 * 技能考试说明
	 */
	private String skillGradeDesc;
	
	/**
	 * 对应ZyyCycleTime表的ID
	 */
	private Long ctId;
	
	/**
	 * 年级
	 */
	private String year;
	/**
	 * 科室名称
	 */
	private String deptName;
	/**
	 * 姓名
	 */
	private String realName;
	/**
	 * 起止日期字符串
	 */
	private String startAndEndTimeStr;
	/**
	 * 轮转状态：1，正在进行。0，结束了  2尚未轮转
	 */
	private Integer cycleState;
	/**
	 * 审核状态 0 未出科 1 合格出科  2 不合格出科
	 */
	private Integer auditState;
	/**
	 * zyy_cycle_time_line表ID
	 */
	private Long cycleTimeLineId;
	
	private String cycleTimes;
	
	//以下统计使用
	/**
	 * 理论和技能总和分数
	 */
	private String sumGrade;
	/**
	 * 未出科
	 */
	private Integer auditStateCount_0;
	/**
	 * 合格出科
	 */
	private Integer auditStateCount_1;
	/**
	 * 不合格出科
	 */
	private Integer auditStateCount_2;
	
	//以下属性在导入和导出的时候使用，因为int或float类型为空会抛出异常，因此用string类型替代
	/**
	 * 审核状态
	 */
	private String auditStateStr;
	/**
	 * 轮转状态字符串
	 */
	private String cycleStateStr;
	
	/**
	 * 理论成绩字符串
	 */
	private String theoryGradeStr;
	
	
	/**
	 * 技能成绩字符串
	 */
	private String skillGradeStr;
	//总成绩
	String totalGrade;
	private String totalScore;
	/**
	 * ID字符串
	 */
	private String idStr;
	/**
	 * 审核状态个数
	 */
	private Integer auditStateCount;
	
	/**
	 * 考试ID
	 */
	private Integer examId;
	
	private String examName;
	
	
	private String timeSection;
	
	private String certificateNo;
	
	//人员类型
	private String residencySource;
	private String residencySourceStr;
	private Integer userCategory;
	private String userCategoryStr;
	//出科审核的评论  || 不合格原因 lzq
	private	String auditReamrk;
		//入科上传的附件
	private	String enterDeptAtt;
	// 不合格项	lzq
	private Integer failType;
	
	private List<ZyyJoinDeptRecordSkill> skillList = new ArrayList<ZyyJoinDeptRecordSkill>();
	
	
	private Float  aduitPerformance;  //平时成绩
	private Float aduitCaseRecoed;   //病历书写
	private Float aduitExamination; 	 // 体格检查
	private Float aduitHistory; 		 //病史采集
	/*
	 * 技能成绩总和
	 */
	private Float skillGradeShow;
	
	private String highestGraduateSchool; // 学校
	
	private Float sum;  // 总和
	
	private String skillStr;//技能成绩
	
	
	private String ctpStartAndEndTime;//对应轮转的开始和结束的日期字符串  所有的阶段用~区分 eg：2020-02-01~2020-05-31,2019-01-01~2019-04-30,2021-03-01~2021-05-31
	
	//学员对带教评价结果
	private List<ZyyTeacherEvaluateVO> stuTeachEvalList;
	//带教对学员评价结果
	private List<ZyyTeacherEvaluateVO> teachStuEvalList;
	// 轮转结束时间 ,评价与轮转相关用
	private String cycleEndTime;
	
	Date createDate;
	Date leaveDeptDate;
	private String leaveDeptDateStr;
	Date joinDeptDate;
	
	private Integer verifyStatus;//审核状态0、未审核默认 1、审核通过2、审核不通过
	private Boolean isTimeOut;//是否逾期
	Long applyId;
	private Date leaveDate;
	private List<MultipartFile> files;
	private List<ZyyLeaveDeptFileVO> leaveDeptFiles;
	
	public String getSkillStr() {
		return skillStr;
	}
	public void setSkillStr(String skillStr) {
		this.skillStr = skillStr;
	}
	public String getHighestGraduateSchool() {
		return highestGraduateSchool;
	}
	public void setHighestGraduateSchool(String highestGraduateSchool) {
		this.highestGraduateSchool = highestGraduateSchool;
	}
	public Float getSum() {
		return sum;
	}
	public void setSum(Float sum) {
		this.sum = sum;
	}
	public Float getAduitPerformance() {
		return aduitPerformance;
	}
	public void setAduitPerformance(Float aduitPerformance) {
		this.aduitPerformance = aduitPerformance;
	}
	public Float getAduitCaseRecoed() {
		return aduitCaseRecoed;
	}
	public void setAduitCaseRecoed(Float aduitCaseRecoed) {
		this.aduitCaseRecoed = aduitCaseRecoed;
	}
	public Float getAduitExamination() {
		return aduitExamination;
	}
	public void setAduitExamination(Float aduitExamination) {
		this.aduitExamination = aduitExamination;
	}
	public Float getAduitHistory() {
		return aduitHistory;
	}
	public void setAduitHistory(Float aduitHistory) {
		this.aduitHistory = aduitHistory;
	}
	public static long getSerialversionuid() {
		return serialVersionUID;
	}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getDeptId() {
		return deptId;
	}
	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}
	public Long getResidencyId() {
		return residencyId;
	}
	public void setResidencyId(Long residencyId) {
		this.residencyId = residencyId;
	}
	public Date getStartDate() {
		return startDate;
	}
	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}
	public Date getEndDate() {
		return endDate;
	}
	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	public String getDeptName() {
		return deptName;
	}
	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}
	public String getRealName() {
		return realName;
	}
	public void setRealName(String realName) {
		this.realName = realName;
	}
	public String getStartAndEndTimeStr() {
		return startAndEndTimeStr;
	}
	public void setStartAndEndTimeStr(String startAndEndTimeStr) {
		this.startAndEndTimeStr = startAndEndTimeStr;
	}
	public Long getCtId() {
		return ctId;
		
	}
	public void setCtId(Long ctId) {
		this.ctId = ctId;
	}
	public Integer getCycleState() {
		return cycleState;
	}
	public void setCycleState(Integer cycleState) {
		this.cycleState = cycleState;
	}
	public String getYear() {
		return year;
	}
	public void setYear(String year) {
		this.year = year;
	}
	public Float getTheoryGrade() {
		return theoryGrade;
	}
	public void setTheoryGrade(Float theoryGrade) {
		this.theoryGrade = theoryGrade;
	}
	public Float getSkillGrade() {
		return skillGrade;
	}
	public void setSkillGrade(Float skillGrade) {
		this.skillGrade = skillGrade;
	}
	public String getSkillGradeDesc() {
		return skillGradeDesc;
	}
	public void setSkillGradeDesc(String skillGradeDesc) {
		this.skillGradeDesc = skillGradeDesc;
	}
	public String getCycleStateStr() {
		return cycleStateStr;
	}
	public void setCycleStateStr(String cycleStateStr) {
		this.cycleStateStr = cycleStateStr;
	}
	public String getTheoryGradeStr() {
		return theoryGradeStr;
	}
	public void setTheoryGradeStr(String theoryGradeStr) {
		this.theoryGradeStr = theoryGradeStr;
	}
	public String getSkillGradeStr() {
		return skillGradeStr;
	}
	public void setSkillGradeStr(String skillGradeStr) {
		this.skillGradeStr = skillGradeStr;
	}
	public String getIdStr() {
		return idStr;
	}
	public void setIdStr(String idStr) {
		this.idStr = idStr;
	}
	public Integer getAuditState() {
		return auditState;
	}
	public void setAuditState(Integer auditState) {
		this.auditState = auditState;
	}
	public void setSumGrade(String sumGrade) {
		this.sumGrade = sumGrade;
	}
	public String getSumGrade() {
		return sumGrade;
	}
	public String getAuditStateStr() {
		return auditStateStr;
	}
	public void setAuditStateStr(String auditStateStr) {
		this.auditStateStr = auditStateStr;
	}
	public Integer getAuditStateCount() {
		return auditStateCount;
	}
	public void setAuditStateCount(Integer auditStateCount) {
		this.auditStateCount = auditStateCount;
	}
	public Integer getAuditStateCount_0() {
		return auditStateCount_0;
	}
	public void setAuditStateCount_0(Integer auditStateCount_0) {
		this.auditStateCount_0 = auditStateCount_0;
	}
	public Integer getAuditStateCount_1() {
		return auditStateCount_1;
	}
	public void setAuditStateCount_1(Integer auditStateCount_1) {
		this.auditStateCount_1 = auditStateCount_1;
	}
	public Integer getAuditStateCount_2() {
		return auditStateCount_2;
	}
	public void setAuditStateCount_2(Integer auditStateCount_2) {
		this.auditStateCount_2 = auditStateCount_2;
	}
	public String getAliasName() {
		return aliasName;
	}
	public void setAliasName(String aliasName) {
		this.aliasName = aliasName;
	}
	public Long getCycleTimeLineId() {
		return cycleTimeLineId;
	}
	public void setCycleTimeLineId(Long cycleTimeLineId) {
		this.cycleTimeLineId = cycleTimeLineId;
	}
	public Integer getExamId() {
		return examId;
	}
	public void setExamId(Integer examId) {
		this.examId = examId;
	}
	public String getCertificateNo() {
		return certificateNo;
	}
	public void setCertificateNo(String certificateNo) {
		this.certificateNo = certificateNo;
	}
	public String getExamName() {
		return examName;
	}
	public void setExamName(String examName) {
		this.examName = examName;
	}
	public String getResidencySource() {
		return residencySource;
	}
	public void setResidencySource(String residencySource) {
		this.residencySource = residencySource;
	}
	public String getResidencySourceStr() {
		return residencySourceStr;
	}
	public void setResidencySourceStr(String residencySourceStr) {
		this.residencySourceStr = residencySourceStr == null ? null : residencySourceStr.trim();
	}
	public Integer getUserCategory() {
		return userCategory;
	}
	public void setUserCategory(Integer userCategory) {
		this.userCategory = userCategory;
	}
	public String getUserCategoryStr() {
		return userCategoryStr;
	}
	public void setUserCategoryStr(String userCategoryStr) {
		this.userCategoryStr = userCategoryStr == null ? null : userCategoryStr.trim();
	}
	public String getTimeSection() {
		return timeSection;
	}
	public void setTimeSection(String timeSection) {
		this.timeSection = timeSection;
	}
	public String getAuditReamrk() {
		return auditReamrk;
	}
	public void setAuditReamrk(String auditReamrk) {
		this.auditReamrk = auditReamrk;
	}
	public String getEnterDeptAtt() {
		return enterDeptAtt;
	}
	public void setEnterDeptAtt(String enterDeptAtt) {
		this.enterDeptAtt = enterDeptAtt;
	}
	public String getTotalGrade() {
		return totalGrade;
	}
	public void setTotalGrade(String totalGrade) {
		Double totalGradeDb = null;
		try{
			if(theoryGrade!=null&&skillGrade!=null){
				 totalGradeDb = BigDecimal.valueOf( theoryGrade+skillGrade ).setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue() ;
			}else if(theoryGrade!=null){
				totalGradeDb  = theoryGrade.doubleValue();
			}else if(skillGrade!=null){
				totalGradeDb  = skillGrade.doubleValue();
			}
		}catch (Exception e) {
			e.printStackTrace();
		}finally{
			this.totalGrade = (totalGradeDb==null?null:totalGradeDb+"");
		}
	}

	public String getTotalScore() {
		return totalScore;
	}

	public void setTotalScore(String totalScore) {
		this.totalScore = totalScore == null ? null : totalScore.trim();
	}

	public Integer getFailType() {
		return failType;
	}
	public void setFailType(Integer failType) {
		this.failType = failType;
	}
	public List<ZyyJoinDeptRecordSkill> getSkillList() {
		return skillList;
	}
	public void setSkillList(List<ZyyJoinDeptRecordSkill> skillList) {
		this.skillList = skillList;
	}
	public String getCycleTimes() {
		return cycleTimes;
	}
	public void setCycleTimes(String cycleTimes) {
		this.cycleTimes = cycleTimes;
	}
	public Float getSkillGradeShow() {
		return skillGradeShow;
	}
	public void setSkillGradeShow(Float skillGradeShow) {
		this.skillGradeShow = skillGradeShow;
	}
	public String getCtpStartAndEndTime() {
		return ctpStartAndEndTime;
	}
	public void setCtpStartAndEndTime(String ctpStartAndEndTime) {
		this.ctpStartAndEndTime = ctpStartAndEndTime;
	}
	public List<ZyyTeacherEvaluateVO> getStuTeachEvalList() {
		return stuTeachEvalList;
	}
	public void setStuTeachEvalList(List<ZyyTeacherEvaluateVO> stuTeachEvalList) {
		this.stuTeachEvalList = stuTeachEvalList;
	}
	public List<ZyyTeacherEvaluateVO> getTeachStuEvalList() {
		return teachStuEvalList;
	}
	public void setTeachStuEvalList(List<ZyyTeacherEvaluateVO> teachStuEvalList) {
		this.teachStuEvalList = teachStuEvalList;
	}
	public String getCycleEndTime() {
		return cycleEndTime;
	}
	public void setCycleEndTime(String cycleEndTime) {
		this.cycleEndTime = cycleEndTime;
	}
	public Integer getVerifyStatus() {
		return verifyStatus;
	}
	public void setVerifyStatus(Integer verifyStatus) {
		this.verifyStatus = verifyStatus;
	}
	public Boolean getIsTimeOut() {
		return isTimeOut;
	}
	public void setIsTimeOut(Boolean isTimeOut) {
		this.isTimeOut = isTimeOut;
	}
	public Long getApplyId() {
		return applyId;
	}
	public void setApplyId(Long applyId) {
		this.applyId = applyId;
	}
	public Date getCreateDate() {
		return createDate;
	}
	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}
	public Date getLeaveDeptDate() {
		return leaveDeptDate;
	}
	public void setLeaveDeptDate(Date leaveDeptDate) {
		this.leaveDeptDate = leaveDeptDate;
	}
	
	public String getLeaveDeptDateStr() {
		return leaveDeptDateStr;
	}
	public void setLeaveDeptDateStr(String leaveDeptDateStr) {
		this.leaveDeptDateStr = leaveDeptDateStr == null ? null : leaveDeptDateStr.trim();
	}
	public Date getJoinDeptDate() {
		return joinDeptDate;
	}
	public void setJoinDeptDate(Date joinDeptDate) {
		this.joinDeptDate = joinDeptDate;
	}
	public Date getLeaveDate() {
		return leaveDate;
	}
	public void setLeaveDate(Date leaveDate) {
		this.leaveDate = leaveDate;
	}
	public Float getCycleManageScore() {
		return cycleManageScore;
	}
	public void setCycleManageScore(Float cycleManageScore) {
		this.cycleManageScore = cycleManageScore;
	}
	public String getCycleManageScoreStr() {
		return cycleManageScoreStr;
	}
	public void setCycleManageScoreStr(String cycleManageScoreStr) {
		this.cycleManageScoreStr = cycleManageScoreStr;
	}
	public Float getCaseAnalyseScore() {
		return caseAnalyseScore;
	}
	public void setCaseAnalyseScore(Float caseAnalyseScore) {
		this.caseAnalyseScore = caseAnalyseScore;
	}
	public String getCaseAnalyseScoreStr() {
		return caseAnalyseScoreStr;
	}
	public void setCaseAnalyseScoreStr(String caseAnalyseScoreStr) {
		this.caseAnalyseScoreStr = caseAnalyseScoreStr == null ? null : caseAnalyseScoreStr.trim();
	}
	public String getCkljdf() {
		return ckljdf;
	}

	public void setCkljdf(String ckljdf) {
		this.ckljdf = ckljdf == null ? null : ckljdf.trim();
	}

	public String getZhpf() {
		return zhpf;
	}

	public void setZhpf(String zhpf) {
		this.zhpf = zhpf == null ? null : zhpf.trim();
	}

	public String getDjlspf() {
		return djlspf;
	}

	public void setDjlspf(String djlspf) {
		this.djlspf = djlspf == null ? null : djlspf.trim();
	}

	public String getXypf() {
		return xypf;
	}

	public void setXypf(String xypf) {
		this.xypf = xypf == null ? null : xypf.trim();
	}

	public String getCkpy() {
		return ckpy;
	}

	public void setCkpy(String ckpy) {
		this.ckpy = ckpy == null ? null : ckpy.trim();
	}

	public String getTeacherNames() {
		return teacherNames;
	}

	public void setTeacherNames(String teacherNames) {
		this.teacherNames = teacherNames == null ? null : teacherNames.trim();
	}
	public List<MultipartFile> getFiles() {
		return files;
	}
	public void setFiles(List<MultipartFile> files) {
		this.files = files;
	}
	public List<ZyyLeaveDeptFileVO> getLeaveDeptFiles() {
		return leaveDeptFiles;
	}
	public void setLeaveDeptFiles(List<ZyyLeaveDeptFileVO> leaveDeptFiles) {
		this.leaveDeptFiles = leaveDeptFiles;
	}
	
}