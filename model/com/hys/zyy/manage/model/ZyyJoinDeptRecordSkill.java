package com.hys.zyy.manage.model;

import java.util.Date;

/**
 * <p>Description: 出入科记录表-成绩表 </p>
 * <AUTHOR>
 * @date 2018-8-2下午2:18:26
 */
public class ZyyJoinDeptRecordSkill extends ZyyBaseObject {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	/**
	 * 主键ID
	 */
	private Long id;
	
	/**
	 * 出入科记录表ID  zyy_join_dept_record的id 
	 */
	private Long jdrId;
	
	/**
	 * 技能考核项
	 */
	private String skillGradeDesc;
	
	/**
	 * 技能成绩
	 */
	private Float skillGrade;
	
	private String skillGradeStr;
	
	/**
	 * 创建时间
	 */
	private Date startDate;
	
	private boolean validateSuccess;
	
	public ZyyJoinDeptRecordSkill() {
	}
	
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getJdrId() {
		return jdrId;
	}

	public void setJdrId(Long jdrId) {
		this.jdrId = jdrId;
	}

	public String getSkillGradeDesc() {
		return skillGradeDesc;
	}

	public void setSkillGradeDesc(String skillGradeDesc) {
		this.skillGradeDesc = skillGradeDesc;
	}

	public Float getSkillGrade() {
		return skillGrade;
	}

	public void setSkillGrade(Float skillGrade) {
		this.skillGrade = skillGrade;
	}

	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public String getSkillGradeStr() {
		return skillGradeStr;
	}

	public void setSkillGradeStr(String skillGradeStr) {
		this.skillGradeStr = skillGradeStr;
	}

	public boolean isValidateSuccess() {
		return validateSuccess;
	}

	public void setValidateSuccess(boolean validateSuccess) {
		this.validateSuccess = validateSuccess;
	}
	
}
