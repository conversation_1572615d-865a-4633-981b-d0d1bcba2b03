package com.hys.zyy.manage.model;

import java.io.Serializable;
import java.util.Date;

/**
 * 进修生扩展信息表
 */
public class ZyyJxjyExtend extends ZyyUserExtendVO implements Serializable {
	private static final long serialVersionUID = 1L;
	/*
	 * 年龄
	 */
	private String age;
	/*
	 * 培训批次（3月、6月、9月、12月）
	 */
	private String trainBatch;
	/*
	 * 进修对象（1=医师；2=护士；3=技师；4=药师；5=检验师）
	 */
	private Integer recruitType;
	/*
	 * 进修科目
	 */
	private String recruitCourse;

	private Date createTime;
	private Date updateTime;

	public ZyyJxjyExtend() {
		super();
	}

	public String getAge() {
		return age;
	}

	public void setAge(String age) {
		this.age = age == null ? null : age.trim();
	}

	public String getTrainBatch() {
		return trainBatch;
	}

	public void setTrainBatch(String trainBatch) {
		this.trainBatch = trainBatch == null ? null : trainBatch.trim();
	}

	public Integer getRecruitType() {
		return recruitType;
	}

	public void setRecruitType(Integer recruitType) {
		this.recruitType = recruitType;
	}

	public String getRecruitCourse() {
		return recruitCourse;
	}

	public void setRecruitCourse(String recruitCourse) {
		this.recruitCourse = recruitCourse == null ? null : recruitCourse.trim();
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
	
}