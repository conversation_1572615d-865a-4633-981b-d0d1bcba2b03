package com.hys.zyy.manage.model;

import java.util.Date;

public class ZyyLeaveAudit extends ZyyBaseObject{

	private static final long serialVersionUID = -1083603642157977253L;
	
	private Long id;
	
	private Long auditUserId;
	/**
	 * 审核状态
	 */
	private Integer auditStatus;
	
	private Long zyyResidencyLeaveId;
	
	private Date createDate;
	
	private Integer status;
	
	/**
	 * 审核用户类型：1为带教，2为科室，3为医院, 4导师 5 专业基地
	 */
	private Integer auditUserType;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getAuditUserId() {
		return auditUserId;
	}

	public void setAuditUserId(Long auditUserId) {
		this.auditUserId = auditUserId;
	}

	public Integer getAuditStatus() {
		return auditStatus;
	}

	public void setAuditStatus(Integer auditStatus) {
		this.auditStatus = auditStatus;
	}

	public Long getZyyResidencyLeaveId() {
		return zyyResidencyLeaveId;
	}

	public void setZyyResidencyLeaveId(Long zyyResidencyLeaveId) {
		this.zyyResidencyLeaveId = zyyResidencyLeaveId;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Integer getAuditUserType() {
		return auditUserType;
	}

	public void setAuditUserType(Integer auditUserType) {
		this.auditUserType = auditUserType;
	}
		
}
