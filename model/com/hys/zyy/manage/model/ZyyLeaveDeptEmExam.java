package com.hys.zyy.manage.model;

import java.util.ArrayList;
import java.util.List;



/**
 * 
 * 出科考试管理实体类--考试
 *
 */
public class ZyyLeaveDeptEmExam extends ZyyBaseObject{

	private static final long serialVersionUID = 1L;
	
	/**
	 * 考试id
	 */
	private Long examId;
	/**
	 * 考试名称
	 */
	private String examName;
	/**
	 * 开始日期
	 */
	private String examStartDate;
	/**
	 * 结束日期
	 */
	private String examEndDate;
	/**
	 * 时长
	 */
	private String examDuration;
	/**
	 * 试卷id
	 */
	private Long paperId;
	/**
	 * 考试的那个科室的ID
	 */
	private Long deptId;
	/**
	 * 试卷名称
	 */
	private String paperName;
	/**
	 * 总学员数
	 */
	private Integer totalCount;
	/**
	 * 完成人数
	 */
	private Integer doneCount;
	/**
	 * 未完成人数
	 */
	private Integer donotCount;
	/**
	 * 存储考试id的集合
	 */
	private List<Long> examIdList = new ArrayList<Long>();
	
	
	/**
	 * 逗号分隔的科室ID  医院查询使用
	 */
	private String deptIds;
	
	
	private Long orgId;//医院ID   医院查询使用
	
	/**
	 * 是否自动创建试卷 1是0否
	 */
	private Integer isAuto;
	/**
	 * 科室名称
	 */
	private String deptName;
	
	//状态 ： 0 关闭 /未启用 1 正常/启用
	private Integer status;
	
	
	
	public List<Long> getExamIdList() {
		return examIdList;
	}
	public void setExamIdList(List<Long> examIdList) {
		this.examIdList = examIdList;
	}
	public Long getExamId() {
		return examId;
	}
	public void setExamId(Long examId) {
		this.examId = examId;
	}
	public String getExamName() {
		return examName;
	}
	public void setExamName(String examName) {
		this.examName = examName;
	}
	public String getExamStartDate() {
		return examStartDate;
	}
	public void setExamStartDate(String examStartDate) {
		this.examStartDate = examStartDate;
	}
	public String getExamEndDate() {
		return examEndDate;
	}
	public void setExamEndDate(String examEndDate) {
		this.examEndDate = examEndDate;
	}
	public String getExamDuration() {
		return examDuration;
	}
	public void setExamDuration(String examDuration) {
		this.examDuration = examDuration;
	}
	public Long getPaperId() {
		return paperId;
	}
	public void setPaperId(Long paperId) {
		this.paperId = paperId;
	}
	public String getPaperName() {
		return paperName;
	}
	public void setPaperName(String paperName) {
		this.paperName = paperName;
	}
	public Integer getTotalCount() {
		return totalCount;
	}
	public void setTotalCount(Integer totalCount) {
		this.totalCount = totalCount;
	}
	public Integer getDoneCount() {
		return doneCount;
	}
	public void setDoneCount(Integer doneCount) {
		this.doneCount = doneCount;
	}
	public Integer getDonotCount() {
		return donotCount;
	}
	public void setDonotCount(Integer donotCount) {
		this.donotCount = donotCount;
	}
	public Long getDeptId() {
		return deptId;
	}
	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}
	public String getDeptIds() {
		return deptIds;
	}
	public void setDeptIds(String deptIds) {
		this.deptIds = deptIds;
	}
	public Long getOrgId() {
		return orgId;
	}
	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}
	public Integer getIsAuto() {
		return isAuto;
	}
	public void setIsAuto(Integer isAuto) {
		this.isAuto = isAuto;
	}
	public String getDeptName() {
		return deptName;
	}
	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
}
