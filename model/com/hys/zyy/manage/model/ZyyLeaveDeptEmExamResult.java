package com.hys.zyy.manage.model;
/**
 * 
 * 出科考试查询的各种报表返回的实体类 
 * <AUTHOR>
 *
 */
public class ZyyLeaveDeptEmExamResult {
	
	/**
	 * 以下字段是出入科管理>成绩统计>按科室分组的 出科考试 报表的展示字段
	 */
	private String deptName ;
	private Double totalScore ;
	private Integer subjectNum ;
	private Integer shouldEmPersonNum ;
	private Integer reallyEmPersonNum ;
	private Integer lackEmPersonNum ;
	private Double maxScore ;
	private Double minScore ;
	private Double avgScore ;
	private Integer qualifiedPersonNum ;
	private Integer notQualifiedPersonNum ;
	
	String qualifiedPersonScale ;
	String notQualifiedPersonScale ;
	
	/**
	 * 以下字段是出入科管理>成绩统计>按科室分组的 出科考试 报表的查询的字段
	 */
	private Double  qualifiedScore = 60d;//合格分数 默认60
	private Integer showType = 1 ;//展示方式 1表格2图表  默认1
	private Integer examId;//
	
	
	Integer skillOrThreoy;//1理论   2技能
	
	
	
	
	
	/**
	 * 是否自动创建试卷 1是0否
	 */
	private Integer isAuto;
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	public Integer getSkillOrThreoy() {
		return skillOrThreoy;
	}
	public void setSkillOrThreoy(Integer skillOrThreoy) {
		this.skillOrThreoy = skillOrThreoy;
	}
	public String getDeptName() {
		return deptName;
	}
	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}
	public Double getTotalScore() {
		return totalScore;
	}
	public void setTotalScore(Double totalScore) {
		this.totalScore = totalScore;
	}
	public Integer getSubjectNum() {
		return subjectNum;
	}
	public void setSubjectNum(Integer subjectNum) {
		this.subjectNum = subjectNum;
	}
	public Integer getShouldEmPersonNum() {
		return shouldEmPersonNum;
	}
	public void setShouldEmPersonNum(Integer shouldEmPersonNum) {
		this.shouldEmPersonNum = shouldEmPersonNum;
	}
	public Integer getReallyEmPersonNum() {
		return reallyEmPersonNum;
	}
	public void setReallyEmPersonNum(Integer reallyEmPersonNum) {
		this.reallyEmPersonNum = reallyEmPersonNum;
	}
	public Integer getLackEmPersonNum() {
		return lackEmPersonNum;
	}
	public void setLackEmPersonNum(Integer lackEmPersonNum) {
		this.lackEmPersonNum = lackEmPersonNum;
	}
	public Double getMaxScore() {
		return maxScore;
	}
	public void setMaxScore(Double maxScore) {
		this.maxScore = maxScore;
	}
	public Double getMinScore() {
		return minScore;
	}
	public void setMinScore(Double minScore) {
		this.minScore = minScore;
	}
	public Double getAvgScore() {
		return avgScore;
	}
	public void setAvgScore(Double avgScore) {
		this.avgScore = avgScore;
	}
	public Integer getQualifiedPersonNum() {
		return qualifiedPersonNum;
	}
	public void setQualifiedPersonNum(Integer qualifiedPersonNum) {
		this.qualifiedPersonNum = qualifiedPersonNum;
	}
	public Integer getNotQualifiedPersonNum() {
		return notQualifiedPersonNum;
	}
	public void setNotQualifiedPersonNum(Integer notQualifiedPersonNum) {
		this.notQualifiedPersonNum = notQualifiedPersonNum;
	}
	public Double getQualifiedScore() {
		return qualifiedScore;
	}
	public void setQualifiedScore(Double qualifiedScore) {
		this.qualifiedScore = qualifiedScore;
	}
	public Integer getShowType() {
		return showType;
	}
	public void setShowType(Integer showType) {
		this.showType = showType;
	}
	public Integer getExamId() {
		return examId;
	}
	public void setExamId(Integer examId) {
		this.examId = examId;
	}
	public String getQualifiedPersonScale() {
		return qualifiedPersonScale;
	}
	public void setQualifiedPersonScale(String qualifiedPersonScale) {
		this.qualifiedPersonScale = qualifiedPersonScale;
	}
	public String getNotQualifiedPersonScale() {
		return notQualifiedPersonScale;
	}
	public void setNotQualifiedPersonScale(String notQualifiedPersonScale) {
		this.notQualifiedPersonScale = notQualifiedPersonScale;
	}
	public Integer getIsAuto() {
		return isAuto;
	}
	public void setIsAuto(Integer isAuto) {
		this.isAuto = isAuto;
	}
	
	
	
	
	
	
	
	
	
	
}
