package com.hys.zyy.manage.model;

import java.util.Date;


/**
 * 
 * 出科考试管理实体类---学员
 *
 */
public class ZyyLeaveDeptExamResi extends ZyyUserExtendVO{

	private static final long serialVersionUID = 1L;
	/**
	 * 学员id
	 */
	private Long resiId;
	/**
	 * 入科时间
	 */
	private Date enterDate;
	/**
	 * 出科时间
	 */
	private Date leaveDate;
	/**
	 * 
	 */
	private String timeSection;
	/**
	 * 出科状态  1-出科  0-未出科
	 */
	private Integer leaveDeptStatus;
	/**
	 * 轮转状态
	 */
	private Integer cycleStatus;
	/**
	 * 最高成绩
	 */
	private Double higestScore;
	/**
	 * 考试批次 /考试名称
	 */
	private String examName;
	/**
	 * 考试时长
	 */
	private String examDuration;
	/**
	 * 考试时间
	 */
	private String examTime;
	/**
	 * 试卷名称
	 */
	private String paperName;
	/**
	 * 考试的id
	 */
	private Long examId;
	/**
	 * 科目id
	 */
	private Long examCourseId;
	/**
	 * 总分
	 */
	private String totalScore;
	/**
	 * 学员的考试状态  1-不区分   2-完成考试   3-未完成考试
	 */
	private Integer examStatus;
	/**
	 * 答题开始时间
	 */
	private Date answerStartTime;
	
	/**
	 * 逗号分隔的科室ID  医院查询使用
	 */
	private String deptIds;
	
	/**
	 * 入科ID
	 */
	private Integer drId;
	private Integer auditState;
	
	//当前人专业的某个科室对应的标准科室字符串
	private String deptStdStrs;
	
	//是否是编辑，编辑的时候不用管是否
	Boolean isEdit;
	
	
	//查询范围  thisMonthLeave  />本月出科学员（10人）   cycleIngStudent" />正在轮转学员（20人）
	private String checkArea;
	
	private Integer thisMonthLeave;
	private Integer cycleIngStudent;
	private Integer cycleEndStudent;
	
	public Long getExamCourseId() {
		return examCourseId;
	}
	public void setExamCourseId(Long examCourseId) {
		this.examCourseId = examCourseId;
	}
	public Date getAnswerStartTime() {
		return answerStartTime;
	}
	public void setAnswerStartTime(Date answerStartTime) {
		this.answerStartTime = answerStartTime;
	}
	public Integer getExamStatus() {
		return examStatus;
	}
	public void setExamStatus(Integer examStatus) {
		this.examStatus = examStatus;
	}
	public String getTotalScore() {
		return totalScore;
	}
	public void setTotalScore(String totalScore) {
		this.totalScore = totalScore;
	}
	public Long getExamId() {
		return examId;
	}
	public void setExamId(Long examId) {
		this.examId = examId;
	}
	public String getExamName() {
		return examName;
	}
	public void setExamName(String examName) {
		this.examName = examName;
	}
	public String getExamDuration() {
		return examDuration;
	}
	public void setExamDuration(String examDuration) {
		this.examDuration = examDuration;
	}
	public String getExamTime() {
		return examTime;
	}
	public void setExamTime(String examTime) {
		this.examTime = examTime;
	}
	public String getPaperName() {
		return paperName;
	}
	public void setPaperName(String paperName) {
		this.paperName = paperName;
	}
	public Long getResiId() {
		return resiId;
	}
	public void setResiId(Long resiId) {
		this.resiId = resiId;
	}
	public Date getEnterDate() {
		return enterDate;
	}
	public void setEnterDate(Date enterDate) {
		this.enterDate = enterDate;
	}
	public Date getLeaveDate() {
		return leaveDate;
	}
	public void setLeaveDate(Date leaveDate) {
		this.leaveDate = leaveDate;
	}
	public String getTimeSection() {
		return timeSection;
	}
	public void setTimeSection(String timeSection) {
		this.timeSection = timeSection;
	}
	public Integer getLeaveDeptStatus() {
		return leaveDeptStatus;
	}
	public void setLeaveDeptStatus(Integer leaveDeptStatus) {
		this.leaveDeptStatus = leaveDeptStatus;
	}
	public Integer getCycleStatus() {
		return cycleStatus;
	}
	public void setCycleStatus(Integer cycleStatus) {
		this.cycleStatus = cycleStatus;
	}
	public Double getHigestScore() {
		return higestScore;
	}
	public void setHigestScore(Double higestScore) {
		this.higestScore = higestScore;
	}
	public String getDeptIds() {
		return deptIds;
	}
	public void setDeptIds(String deptIds) {
		this.deptIds = deptIds;
	}
	public Integer getDrId() {
		return drId;
	}
	public void setDrId(Integer drId) {
		this.drId = drId;
	}
	public Boolean getIsEdit() {
		return isEdit;
	}
	public void setIsEdit(Boolean isEdit) {
		this.isEdit = isEdit;
	}
	public String getDeptStdStrs() {
		return deptStdStrs;
	}
	public void setDeptStdStrs(String deptStdStrs) {
		this.deptStdStrs = deptStdStrs;
	}
	public String getCheckArea() {
		return checkArea;
	}
	public void setCheckArea(String checkArea) {
		this.checkArea = checkArea;
	}
	public Integer getThisMonthLeave() {
		return thisMonthLeave;
	}
	public void setThisMonthLeave(Integer thisMonthLeave) {
		this.thisMonthLeave = thisMonthLeave;
	}
	public Integer getCycleIngStudent() {
		return cycleIngStudent;
	}
	public void setCycleIngStudent(Integer cycleIngStudent) {
		this.cycleIngStudent = cycleIngStudent;
	}
	public Integer getCycleEndStudent() {
		return cycleEndStudent;
	}
	public void setCycleEndStudent(Integer cycleEndStudent) {
		this.cycleEndStudent = cycleEndStudent;
	}
	public Integer getAuditState() {
		return auditState;
	}
	public void setAuditState(Integer auditState) {
		this.auditState = auditState;
	}
}
