package com.hys.zyy.manage.model;

import java.io.Serializable;
import java.util.Date;

/**
 * 出科审核附件
 */
public class ZyyLeaveDeptFile implements Serializable {
	private static final long serialVersionUID = 1L;
	/*
	 * ID
	 */
	private String id;
	/*
	 * ZYY_JOIN_DEPT_RECORD_ID
	 */
	private Long zyyJoinDeptRecordId;
	/*
	 * 文件名称
	 */
	private String fileName;
	/*
	 * 文件路径
	 */
	private String filePath;
	/*
	 * 状态（1=有效；-1=失效）
	 */
	private Integer state;
	private Date createTime;
	private Date updateTime;

	public ZyyLeaveDeptFile() {
		super();
	}

	public ZyyLeaveDeptFile(Long zyyJoinDeptRecordId) {
		super();
		this.zyyJoinDeptRecordId = zyyJoinDeptRecordId;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id == null ? null : id.trim();
	}

	public Long getZyyJoinDeptRecordId() {
		return zyyJoinDeptRecordId;
	}

	public void setZyyJoinDeptRecordId(Long zyyJoinDeptRecordId) {
		this.zyyJoinDeptRecordId = zyyJoinDeptRecordId;
	}

	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName == null ? null : fileName.trim();
	}

	public String getFilePath() {
		return filePath;
	}

	public void setFilePath(String filePath) {
		this.filePath = filePath == null ? null : filePath.trim();
	}

	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ZyyLeaveDeptFile other = (ZyyLeaveDeptFile) obj;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		return true;
	}

	@Override
	public String toString() {
		return "ZyyLeaveDeptFile [id=" + id + ", zyyJoinDeptRecordId="
				+ zyyJoinDeptRecordId + ", fileName=" + fileName
				+ ", filePath=" + filePath + ", state=" + state
				+ ", createTime=" + createTime + ", updateTime=" + updateTime
				+ "]";
	}

}
