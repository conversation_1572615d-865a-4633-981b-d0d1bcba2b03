package com.hys.zyy.manage.model;

import java.util.Date;

/**
 * 
 * 标题：zyy
 * 
 * 描述：
 * 
 * 说明:
 */
public class ZyyLeaveDeptPaper extends ZyyBaseObject {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	/**
	 * id
	 */
	private Long id;
	/**
	 * 科室id
	 */
	private Long deptId;
	/**
	 * 创建人的id，也是科室管理员的id
	 */
	private Long deptCreaterId;
	/**
	 * 考试的id
	 */
	private Long emExamId;
	/**
	 * 科目的id
	 */
	private Long examCourseId;
	/**
	 * 创建日期
	 */
	private Date createTime;
	/**
	 * 状态 1-正常
	 */
	private Integer status;
	/**
	 * 目录节点的id
	 */
	private Long catalogId;
	
	
	
	
	/**
	 * 科室ID集合  医院会批量创建  以逗号分隔
	 */
	private String deptIds;
	
	/**
	 * 人员ID集合  医院会批量创建  以逗号分隔
	 */
	private String users;
	
	/**
	 * 入科1出科2
	 */
	private Integer rkOrck ;
	
	
	/**
	 * 是否自动创建试卷 1是0否
	 */
	private Integer isAuto;
	
	//考试时长
	private String examDuration;
	
	public Long getCatalogId() {
		return catalogId;
	}
	public void setCatalogId(Long catalogId) {
		this.catalogId = catalogId;
	}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getDeptId() {
		return deptId;
	}
	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}
	public Long getDeptCreaterId() {
		return deptCreaterId;
	}
	public void setDeptCreaterId(Long deptCreaterId) {
		this.deptCreaterId = deptCreaterId;
	}
	public Long getEmExamId() {
		return emExamId;
	}
	public void setEmExamId(Long emExamId) {
		this.emExamId = emExamId;
	}
	public Long getExamCourseId() {
		return examCourseId;
	}
	public void setExamCourseId(Long examCourseId) {
		this.examCourseId = examCourseId;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	public String getDeptIds() {
		return deptIds;
	}
	public void setDeptIds(String deptIds) {
		this.deptIds = deptIds;
	}
	public String getUsers() {
		return users;
	}
	public void setUsers(String users) {
		this.users = users;
	}
	public Integer getRkOrck() {
		return rkOrck;
	}
	public void setRkOrck(Integer rkOrck) {
		this.rkOrck = rkOrck;
	}
	public Integer getIsAuto() {
		return isAuto;
	}
	public void setIsAuto(Integer isAuto) {
		this.isAuto = isAuto;
	}
	public String getExamDuration() {
		return examDuration;
	}
	public void setExamDuration(String examDuration) {
		this.examDuration = examDuration;
	}
	

	
	
}
