package com.hys.zyy.manage.model;

import java.util.Date;

public class <PERSON>yyLoginLog extends ZyyBaseObject {

	private static final long serialVersionUID = 558268873469380578L;

	private Long Id;
	/**
	 * 用户账号
	 */
	private String userAccount;
	/**
	 * 用户名称
	 */
	private String userName;
	private Date loginDate;
	/*
	 * IP地址
	 */
	private String ipAddress;

	public Long getId() {
		return Id;
	}

	public void setId(Long id) {
		Id = id;
	}

	public String getUserAccount() {
		return userAccount;
	}

	public void setUserAccount(String userAccount) {
		this.userAccount = userAccount;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public Date getLoginDate() {
		return loginDate;
	}

	public void setLoginDate(Date loginDate) {
		this.loginDate = loginDate;
	}

	public String getIpAddress() {
		return ipAddress;
	}

	public void setIpAddress(String ipAddress) {
		this.ipAddress = ipAddress == null ? null : ipAddress.trim();
	}

}