package com.hys.zyy.manage.model;

import java.io.Serializable;
import java.util.Date;

import com.hys.zyy.manage.util.BaseModel;

/**
 * 登录验证设置
 */
public class ZyyLoginValidateSet extends BaseModel implements Serializable {
	/*
	 * ID
	 */
	private String id;
	/*
	 * 用户类型
	 */
	private Integer zyyUserType;
	/*
	 * PC登录是否验证（-1：否；1：是）
	 */
	private Integer pcState;
	/*
	 * H5登录是否验证（-1：否；1：是）
	 */
	private Integer h5State;
	/*
	 * 备注
	 */
	private String remark;
	/*
	 * 创建者ID
	 */
	private Long createUserId;
	/*
	 * 创建时间
	 */
	private Date createTime;
	/*
	 * 修改者ID
	 */
	private Long updateUserId;
	/*
	 * 修改时间
	 */
	private Date updateTime;

	public ZyyLoginValidateSet() {
		super();
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public Integer getZyyUserType() {
		return zyyUserType;
	}

	public void setZyyUserType(Integer zyyUserType) {
		this.zyyUserType = zyyUserType;
	}

	public Integer getPcState() {
		return pcState;
	}

	public void setPcState(Integer pcState) {
		this.pcState = pcState;
	}

	public Integer getH5State() {
		return h5State;
	}

	public void setH5State(Integer h5State) {
		this.h5State = h5State;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public Long getCreateUserId() {
		return createUserId;
	}

	public void setCreateUserId(Long createUserId) {
		this.createUserId = createUserId;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Long getUpdateUserId() {
		return updateUserId;
	}

	public void setUpdateUserId(Long updateUserId) {
		this.updateUserId = updateUserId;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ZyyLoginValidateSet other = (ZyyLoginValidateSet) obj;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		return true;
	}

	@Override
	public String toString() {
		return "ZyyLoginValidateSet [id=" + id + ", zyyUserType=" + zyyUserType
				+ ", pcState=" + pcState + ", h5State=" + h5State + ", remark="
				+ remark + ", createUserId=" + createUserId + ", createTime="
				+ createTime + ", updateUserId=" + updateUserId
				+ ", updateTime=" + updateTime + "]";
	}

}
