package com.hys.zyy.manage.model;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang.builder.ReflectionToStringBuilder;

import com.hys.zyy.manage.constants.Constants;

/**
 * 轮转表科室概要信息（基本要求，高级要求，书刊。。。。。。）
 * <AUTHOR>
 *
 */
public class ZyyManualDeptSummaryVO extends ZyyBaseObject{
	
	private static final long serialVersionUID = 6204705202321445950L;

	private String name;
	
	private String cycleTime;
	
	private ZyyManualTitleContentVO memo = new ZyyManualTitleContentVO();					// 轮转备注
	
	private ZyyManualTitleContentVO targets = new ZyyManualTitleContentVO();				// 轮转目的
	
	private ZyyManualTitleContentVO basicText = new ZyyManualTitleContentVO();				// 基本要求
	
	private List<ZyyManualSickSummaryDetail> basic1s = new ArrayList<ZyyManualSickSummaryDetail>();			// 基本要求疾病
	
	private List<ZyyManualSkillSummaryDetail> basic2s = new ArrayList<ZyyManualSkillSummaryDetail>();			// 基本要求技能
	
	private ZyyManualTitleContentVO advanceText = new ZyyManualTitleContentVO();				// 高级要求
	
	private List<ZyyManualSickSummaryDetail> advance1s = new ArrayList<ZyyManualSickSummaryDetail>();			// 高级要求疾病
	
	private List<ZyyManualSkillSummaryDetail> advance2s = new ArrayList<ZyyManualSkillSummaryDetail>();			// 高级要求技能
	
	private ZyyManualTitleContentVO advance3s = new ZyyManualTitleContentVO();// 高级要求
	
	private ZyyManualTitleContentVO book = new ZyyManualTitleContentVO();					// 书刊啥的
	
	public static final ZyyManualDeptSummaryVO NULL = new ZyyManualDeptSummaryVO();

	public ZyyManualDeptSummaryVO() {
	}
	
	public ZyyManualTitleContentVO getMemo() {
		return memo;
	}

	public void setMemo(ZyyManualTitleContentVO memo) {
		this.memo = memo;
	}

	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this);
	}

	public void read(List<ZyyDeptStdDisease> list) {
		if(list == null || list.isEmpty()) {
			basic1s.add(new ZyyManualSickSummaryDetail("基本疾病要求"));
			basic2s.add(new ZyyManualSkillSummaryDetail("基本技能要求"));
			advance1s.add(new ZyyManualSickSummaryDetail("较高疾病要求"));
			advance2s.add(new ZyyManualSkillSummaryDetail("较高技能要求"));
			return;
		}
		
		for(ZyyDeptStdDisease entity : list) {
			if(Constants.DISEASE_TYPE_SICK == entity.getDiseaseType()) {			// 疾病
				ZyyManualSickSummaryDetail sickVO = new ZyyManualSickSummaryDetail();
				sickVO.read(entity);
				if(Constants.REQUIRE_TYPE_BASIC == entity.getRequireType())			// 基本
					basic1s.add(sickVO);
				if(Constants.REQUIRE_TYPE_ADVANCE == entity.getRequireType())		// 高级
					advance1s.add(sickVO);
			}
			
			if(Constants.DISEASE_TYPE_SKILL == entity.getDiseaseType()) {			// 技能
				ZyyManualSkillSummaryDetail skillVO = new ZyyManualSkillSummaryDetail();
				skillVO.read(entity);
				if(Constants.REQUIRE_TYPE_BASIC == entity.getRequireType())			// 基本
					basic2s.add(skillVO);
				if(Constants.REQUIRE_TYPE_ADVANCE == entity.getRequireType())		// 高级
					advance2s.add(skillVO);
			}
		}
	}
	
	public void write(List<ZyyDeptStdDisease> list) {
		// 基本疾病
		for(ZyyManualSickSummaryDetail vo : this.basic1s) {
			ZyyDeptStdDisease entity = new ZyyDeptStdDisease();
			entity.setDiseaseType(Constants.DISEASE_TYPE_SICK);
			entity.setRequireType(Constants.REQUIRE_TYPE_BASIC);
			if(vo.write(entity))
				list.add(entity);
		}
		// 基本技能
		for(ZyyManualSkillSummaryDetail vo : this.basic2s) {
			ZyyDeptStdDisease entity = new ZyyDeptStdDisease();
			entity.setDiseaseType(Constants.DISEASE_TYPE_SKILL);
			entity.setRequireType(Constants.REQUIRE_TYPE_BASIC);
			if(vo.write(entity))
				list.add(entity);
		}
		// 高级疾病
		for(ZyyManualSickSummaryDetail vo : this.advance1s) {
			ZyyDeptStdDisease entity = new ZyyDeptStdDisease();
			entity.setDiseaseType(Constants.DISEASE_TYPE_SICK);
			entity.setRequireType(Constants.REQUIRE_TYPE_ADVANCE);
			if(vo.write(entity))
				list.add(entity);
		}
		// 高级技能
		for(ZyyManualSkillSummaryDetail vo : this.advance2s) {
			ZyyDeptStdDisease entity = new ZyyDeptStdDisease();
			entity.setDiseaseType(Constants.DISEASE_TYPE_SKILL);
			entity.setRequireType(Constants.REQUIRE_TYPE_ADVANCE);
			if(vo.write(entity))
				list.add(entity);
		}
	}
	
	public void read(ZyyDeptStd std) {
		if(std == null)
			return;
		memo.setContent(std.getCycleRemark());
		// 轮转目的
		this.targets = new ZyyManualTitleContentVO(std.getCyclePurpose());
		// 基本要求文本
		this.basicText = new ZyyManualTitleContentVO(std.getBasicRequirementDesc());
		// 高级要求文本
		this.advanceText = new ZyyManualTitleContentVO(std.getHighRequirementDesc());
		// 外语、教学、科研等能力的要求描述
		this.advance3s =new ZyyManualTitleContentVO(std.getOtherRequirementDesc());
		// 参考书刊
		this.book = new ZyyManualTitleContentVO(std.getReferenceBook());
		
		this.name = std.getName();
		
		this.cycleTime = std.getCycleTime();
		
	}
	
	public void write(ZyyDeptStd std) {
		std.setCycleRemark(this.memo.getContent());
		std.setCyclePurpose(this.targets.getContent());
		std.setBasicRequirementDesc(this.basicText.getContent());
		std.setHighRequirementDesc(this.advanceText.getContent());
		std.setOtherRequirementDesc(this.advance3s.getContent());
		std.setReferenceBook(this.book.getContent());
	}

	public ZyyManualTitleContentVO getTargets() {
		return targets;
	}

	public void setTargets(ZyyManualTitleContentVO targets) {
		this.targets = targets;
	}

	public ZyyManualTitleContentVO getBasicText() {
		return basicText;
	}

	public void setBasicText(ZyyManualTitleContentVO basicText) {
		this.basicText = basicText;
	}

	public List<ZyyManualSickSummaryDetail> getBasic1s() {
		return basic1s;
	}

	public void setBasic1s(List<ZyyManualSickSummaryDetail> basic1s) {
		this.basic1s = basic1s;
	}

	public List<ZyyManualSkillSummaryDetail> getBasic2s() {
		return basic2s;
	}

	public void setBasic2s(List<ZyyManualSkillSummaryDetail> basic2s) {
		this.basic2s = basic2s;
	}

	public ZyyManualTitleContentVO getAdvanceText() {
		return advanceText;
	}

	public void setAdvanceText(ZyyManualTitleContentVO advanceText) {
		this.advanceText = advanceText;
	}

	public List<ZyyManualSickSummaryDetail> getAdvance1s() {
		return advance1s;
	}

	public void setAdvance1s(List<ZyyManualSickSummaryDetail> advance1s) {
		this.advance1s = advance1s;
	}

	public List<ZyyManualSkillSummaryDetail> getAdvance2s() {
		return advance2s;
	}

	public void setAdvance2s(List<ZyyManualSkillSummaryDetail> advance2s) {
		this.advance2s = advance2s;
	}

	public ZyyManualTitleContentVO getAdvance3s() {
		return advance3s;
	}

	public void setAdvance3s(ZyyManualTitleContentVO advance3s) {
		this.advance3s = advance3s;
	}

	public ZyyManualTitleContentVO getBook() {
		return book;
	}

	public void setBook(ZyyManualTitleContentVO book) {
		this.book = book;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getCycleTime() {
		return cycleTime;
	}

	public void setCycleTime(String cycleTime) {
		this.cycleTime = cycleTime;
	}

}