package com.hys.zyy.manage.model;

import org.apache.commons.lang.builder.ReflectionToStringBuilder;

public class ZyyManualDetailItem extends ZyyBaseObject{
	
	private static final long serialVersionUID = 6278698451628457252L;

	Long id;
	
	Long diseaseId;
	
	String name;
	
	String sign;
	
	String number;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getSign() {
		return sign;
	}

	public void setSign(String sign) {
		this.sign = sign;
	}

	public String getNumber() {
		return number;
	}

	public void setNumber(String number) {
		this.number = number;
	}
	
	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this);
	}

	public void read(ZyyDeptStdDiseaseDetail en) {
		if(en == null)
			return;
		this.setId(en.getId());
		this.setName(en.getDisease().getDiseaseName());
		this.setNumber(en.getRequireAmount());
		this.setSign(en.getRequireSymbol());
		this.setDiseaseId(en.getDiseaseId());
	}

	public void wirte(ZyyDeptStdDiseaseDetail model) {
		model.setId(this.getId());
		model.setRequireAmount(this.getNumber());
		model.setRequireSymbol(this.getSign());
		model.setDiseaseId(this.getDiseaseId());
	}

	public Long getDiseaseId() {
		return diseaseId;
	}

	public void setDiseaseId(Long diseaseId) {
		this.diseaseId = diseaseId;
	}
	
}
