package com.hys.zyy.manage.model;

import java.util.Date;

/**
 * 登记手册医院审核结果表
 * <AUTHOR>
 *
 */
public class ZyyManualHosVerifyResultVO extends ZyyBaseObject{
	
	private static final long serialVersionUID = -3735780956228284323L;

	private Long id;//ID
	
	private Long zyyOrgId;//医院ID
	
	private Long residencyId;//住院医师ID
	
	private Date residencyLastDate;//住院医师最后提交时间
	
	private Integer commitTimes;//住院医师提交次数
	
	private Integer residencyCommitStatus;//住院医师提交状态
	
	private Integer hospCheckStatus;//医院最终审核状态
	
	private Date hospCheckDate;//医院最终审核时间

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getZyyOrgId() {
		return zyyOrgId;
	}

	public void setZyyOrgId(Long zyyOrgId) {
		this.zyyOrgId = zyyOrgId;
	}

	public Long getResidencyId() {
		return residencyId;
	}

	public void setResidencyId(Long residencyId) {
		this.residencyId = residencyId;
	}

	public Date getResidencyLastDate() {
		return residencyLastDate;
	}

	public void setResidencyLastDate(Date residencyLastDate) {
		this.residencyLastDate = residencyLastDate;
	}

	public Integer getCommitTimes() {
		return commitTimes;
	}

	public void setCommitTimes(Integer commitTimes) {
		this.commitTimes = commitTimes;
	}

	public Integer getResidencyCommitStatus() {
		return residencyCommitStatus;
	}

	public void setResidencyCommitStatus(Integer residencyCommitStatus) {
		this.residencyCommitStatus = residencyCommitStatus;
	}

	public Integer getHospCheckStatus() {
		return hospCheckStatus;
	}

	public void setHospCheckStatus(Integer hospCheckStatus) {
		this.hospCheckStatus = hospCheckStatus;
	}

	public Date getHospCheckDate() {
		return hospCheckDate;
	}

	public void setHospCheckDate(Date hospCheckDate) {
		this.hospCheckDate = hospCheckDate;
	}

	

}