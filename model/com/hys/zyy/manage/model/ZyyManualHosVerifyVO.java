package com.hys.zyy.manage.model;

import java.util.Date;

/**
 * 登记手册医院审核表
 * <AUTHOR>
 *
 */
public class ZyyManualHosVerifyVO extends ZyyBaseObject{
	
	private static final long serialVersionUID = -2808329277864420542L;

	private Long id;//ID
	
	private Long residencyId;//住院医师ID
	
	private Long residencyHandbookHospId;//住院医师手册ID
	
	private Date commitDate;//提交时间
	
	private Long hospitalUserId;//审核用户id
	
	private Integer hospitalCheckStatus;//医院审核状态
	
	private Date hospitalCheckDate;//医院审核时间
	
	private String hospitalCheckRemark;//备注

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getResidencyId() {
		return residencyId;
	}

	public void setResidencyId(Long residencyId) {
		this.residencyId = residencyId;
	}

	public Long getResidencyHandbookHospId() {
		return residencyHandbookHospId;
	}

	public void setResidencyHandbookHospId(Long residencyHandbookHospId) {
		this.residencyHandbookHospId = residencyHandbookHospId;
	}

	public Date getCommitDate() {
		return commitDate;
	}

	public void setCommitDate(Date commitDate) {
		this.commitDate = commitDate;
	}

	public Long getHospitalUserId() {
		return hospitalUserId;
	}

	public void setHospitalUserId(Long hospitalUserId) {
		this.hospitalUserId = hospitalUserId;
	}

	public Integer getHospitalCheckStatus() {
		return hospitalCheckStatus;
	}

	public void setHospitalCheckStatus(Integer hospitalCheckStatus) {
		this.hospitalCheckStatus = hospitalCheckStatus;
	}

	public Date getHospitalCheckDate() {
		return hospitalCheckDate;
	}

	public void setHospitalCheckDate(Date hospitalCheckDate) {
		this.hospitalCheckDate = hospitalCheckDate;
	}

	public String getHospitalCheckRemark() {
		return hospitalCheckRemark;
	}

	public void setHospitalCheckRemark(String hospitalCheckRemark) {
		this.hospitalCheckRemark = hospitalCheckRemark;
	}
	

}