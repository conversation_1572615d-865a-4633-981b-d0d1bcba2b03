package com.hys.zyy.manage.model;

import java.util.List;

import com.hys.security.util.SecurityUtils;
import com.hys.zyy.manage.constants.Constants;

/**
 * 审核流程项
 * <AUTHOR>
 *
 */
public class ZyyManualProcessEntityItemVO extends ZyyBaseObject{
	
	private static final long serialVersionUID = 1932672357601491145L;

	public static final ZyyManualProcessEntityItemVO NULL = new ZyyManualProcessEntityItemVO();

	public static final ZyyManualProcessEntityItemVO QXZ = new ZyyManualProcessEntityItemVO(Constants.QXZ);
	
	public static final ZyyManualProcessEntityItemVO DAIJIAO = new ZyyManualProcessEntityItemVO(Constants.USER_TYPE_TEACHER);
	
	public static final ZyyManualProcessEntityItemVO JIDI = new ZyyManualProcessEntityItemVO(Constants.USER_TYPE_BASE);

	public transient ZyyManualProcessEntityItemVO parent = null;
	
	private Long id = 0l;
	
	private String mode = "new"; 		// new update remove
	
	private Integer reviewer = 1;
	
	private Integer level = 1;

	public ZyyManualProcessEntityItemVO(Integer reviewer) {
		this.reviewer = reviewer;
	}

	public ZyyManualProcessEntityItemVO() {
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getMode() {
		return mode;
	}

	public void setMode(String mode) {
		this.mode = mode;
	}

	public Integer getReviewer() {
		return reviewer;
	}

	public void setReviewer(Integer reviewer) {
		this.reviewer = reviewer;
	}

	public Integer getLevel() {
		return level;
	}

	public void setLevel(Integer level) {
		this.level = level;
	}

	public void read(ZyyProcessDetail entity) {
		if(entity == null)
			return;
		this.id = entity.getId();
		this.reviewer = entity.getVerifiers();
		this.level = entity.getProcessLevel();
		this.mode = "update";
	}

	public void writeToList(ZyyProcessDetail entity, List<ZyyProcessDetail> list) {
		if(this.id != 0)
			entity.setId(this.id);
		entity.setVerifiers(this.reviewer);
		list.add(entity);
	}
	
	public boolean ifNeedTeacherCheck() {
		return SecurityUtils.hasTeacherPrivilege(this.reviewer);
	}
	
	public boolean ifNeedDirectorCheck() {
		return SecurityUtils.hasDepartmentPrivilege(this.reviewer);
	}
	
	public boolean ifNeedBaseCheck() {
		return SecurityUtils.hasBasePrivilege(this.reviewer);
	}
	
	public boolean ifNeedHospitalCheck() {
		return SecurityUtils.hasHospitalPrivilege(this.reviewer);
	}
	
}
