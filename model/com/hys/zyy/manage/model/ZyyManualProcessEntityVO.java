package com.hys.zyy.manage.model;

import java.util.ArrayList;
import java.util.List;

import com.hys.zyy.manage.util.CollectionUtils;

/**
 * 审批流程实体VO
 * <AUTHOR>
 *
 */
public class ZyyManualProcessEntityVO extends ZyyBaseObject{

	private static final long serialVersionUID = 5513028646799522005L;

	private Long id = 0l;
	
	private String name;
	
	private boolean isCascade;
	
	private List<ZyyManualProcessEntityItemVO> items = new ArrayList<ZyyManualProcessEntityItemVO>();

	public void addItem(ZyyManualProcessEntityItemVO item) {
		if(item != null)
			items.add(item);
	}
	
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public List<ZyyManualProcessEntityItemVO> getItems() {
		return items;
	}

	public void setItems(List<ZyyManualProcessEntityItemVO> items) {
		this.items = items;
	}

	public void read(ZyyProcess entity) {
		if(entity == null)
			return;
		ZyyManualProcessEntityItemVO parent = null;
		this.id = entity.getId();
		this.name = entity.getProcessName();
		this.isCascade = entity.getIsCascade() == 0 ? false : true;
		List<ZyyProcessDetail> details = entity.getDetails();
		if(CollectionUtils.isNotEmpty(details)) {
			for(ZyyProcessDetail detail : details) {
				ZyyManualProcessEntityItemVO itemVO = new ZyyManualProcessEntityItemVO();
				itemVO.read(detail);
				// 构建链式结构，方便找到上一级
				itemVO.parent = parent;
				parent = itemVO;
				this.addItem(itemVO);
			}
		}
	}

	public boolean getIsCascade() {
		return isCascade;
	}

	public void setIsCascade(boolean isCascade) {
		this.isCascade = isCascade;
	}
	
	/**
	 * 是否轮转流程
	 * @return
	 */
	public boolean ifCycleProcess() {
		return id == 4;
	}
	
	/**
	 * 是否其他流程
	 * @return
	 */
	public boolean ifOtherProcess() {
		return id == 5;
	}
	
	/**
	 * 是否最终流程
	 * @return
	 */
	public boolean ifFinalProcess() {
		return id == 6;
	}
	
	/**
	 * 非逐级审核流程
	 * @return
	 */
	public boolean ifNotCascadeProcess() {
		return !this.isCascade;
	}
}
