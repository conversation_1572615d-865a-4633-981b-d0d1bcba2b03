package com.hys.zyy.manage.model;


/**
 * 审核流程设置VO
 * <AUTHOR>
 *
 */
public class ZyyManualProcessVO extends ZyyBaseObject{

	private static final long serialVersionUID = 2373314877896899679L;

	private ZyyManualProcessEntityVO proccess1 = new ZyyManualProcessEntityVO();
	
	private ZyyManualProcessEntityVO proccess2 = new ZyyManualProcessEntityVO();
	
	private ZyyManualProcessEntityVO proccess3 = new ZyyManualProcessEntityVO();

	public ZyyManualProcessEntityVO getProccess1() {
		return proccess1;
	}

	public void setProccess1(ZyyManualProcessEntityVO proccess1) {
		this.proccess1 = proccess1;
	}

	public ZyyManualProcessEntityVO getProccess2() {
		return proccess2;
	}

	public void setProccess2(ZyyManualProcessEntityVO proccess2) {
		this.proccess2 = proccess2;
	}

	public ZyyManualProcessEntityVO getProccess3() {
		return proccess3;
	}

	public void setProccess3(ZyyManualProcessEntityVO proccess3) {
		this.proccess3 = proccess3;
	}
	
	
	
}
