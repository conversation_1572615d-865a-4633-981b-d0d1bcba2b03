package com.hys.zyy.manage.model;

import java.util.ArrayList;
import java.util.List;

/**
 * 登记手册设置基地VO
 * <AUTHOR>
 *
 */
public class ZyyManualSettingBaseVO extends ZyyBaseObject  {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	private Long baseId;						// 基地ID
	
	private String baseName;					// 基地名称
	
	private List<ZyyManualSettingDeptVO> requireds = new ArrayList<ZyyManualSettingDeptVO>();	// 必须科室
	
	private List<ZyyManualSettingDeptVO> options = new ArrayList<ZyyManualSettingDeptVO>();		// 可选科室
	
	private List<ZyyManualSettingModuleVO> modules = new ArrayList<ZyyManualSettingModuleVO>();		// 模块
	
	private List<ZyyManualSettingDeptVO> depts = new ArrayList<ZyyManualSettingDeptVO>();
	
	public void addDeptVO(ZyyManualSettingDeptVO deptVO) {
		if(deptVO != null)
			this.depts.add(deptVO);
	}
	
	public void addModule(ZyyManualSettingModuleVO vo) {
		if(vo != null)
			this.modules.add(vo);
	}

	public Long getBaseId() {
		return baseId;
	}

	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}

	public String getBaseName() {
		return baseName;
	}

	public void setBaseName(String baseName) {
		this.baseName = baseName;
	}

	public List<ZyyManualSettingDeptVO> getDepts() {
		if(depts.isEmpty() && (!requireds.isEmpty() || !options.isEmpty())) {
			depts.addAll(requireds);
			depts.addAll(options);
		}
		return depts;
	}

	public void setDepts(List<ZyyManualSettingDeptVO> depts) {
		this.depts = depts;
	}

	public List<ZyyManualSettingModuleVO> getModules() {
		return modules;
	}

	public void setModules(List<ZyyManualSettingModuleVO> modules) {
		this.modules = modules;
	}

	public List<ZyyManualSettingDeptVO> getRequireds() {
		return requireds;
	}

	public void setRequireds(List<ZyyManualSettingDeptVO> requireds) {
		this.requireds = requireds;
	}

	public List<ZyyManualSettingDeptVO> getOptions() {
		return options;
	}

	public void setOptions(List<ZyyManualSettingDeptVO> options) {
		this.options = options;
	}

	public void read(ZyyBaseStd base) {
		if(base == null)
			return;
		this.setBaseId(base.getId());
		this.setBaseName(base.getAliasName());
	}

	public void addRequireDept(ZyyManualSettingDeptVO deptVO) {
		this.requireds.add(deptVO);
	}

	public void addOptionDept(ZyyManualSettingDeptVO deptVO) {
		this.options.add(deptVO);
	}
	
}
