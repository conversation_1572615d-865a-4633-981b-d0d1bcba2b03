package com.hys.zyy.manage.model;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang.builder.ReflectionToStringBuilder;

/**
 * 登记手册设置学科VO
 * <AUTHOR>
 *
 */
public class ZyyManualSettingDeptVO extends ZyyBaseObject  {
	



	/**
	 * 
	 */
	private static final long serialVersionUID = -6002893921802299377L;

	private Long id;					// ID
	
	private Long orgId;
	
	private String name;				// 名称
	
	private Boolean isRequired = false;			// 是否必选
	
	private String mode = "new";		// static, update, new, remove
	
	public String cycle = "0";				// 周期
	
	private List<ZyyManualSickVO> sick = new ArrayList<ZyyManualSickVO>();	// 疾病
	
	private List<ZyyManualSkillVO> skill = new ArrayList<ZyyManualSkillVO>();	// 技能
	
	private List<ZyyManualSettingModuleVO> modules = new ArrayList<ZyyManualSettingModuleVO>();		// 模块
	
	private List<ZyyDeptStd> hospDepts = new ArrayList<ZyyDeptStd>();		// 医院科室列表
	
	private List<ZyyDept> hospRealDepts = new ArrayList<ZyyDept>();		// 医院实际科室列表
        
    private Boolean isPro = false;			// 是否省厅
    //设置评分表 list       
    private List<ZyySetGradeTableVo> zyySetGradeTableList = new ArrayList<ZyySetGradeTableVo>();       
	

	public List<ZyySetGradeTableVo> getZyySetGradeTableList() {
		return zyySetGradeTableList;
	}

	public void setZyySetGradeTableList(
			List<ZyySetGradeTableVo> zyySetGradeTableList) {
		this.zyySetGradeTableList = zyySetGradeTableList;
	}
 
	public ZyyManualSettingDeptVO() {
	}
	
	public void addSick(ZyyManualSickVO vo) {
		if(vo != null)
			this.sick.add(vo);
	}
	
	public void addSkill(ZyyManualSkillVO vo) {
		if(vo != null)
			this.skill.add(vo);
	}
	
	public void addModule(ZyyManualSettingModuleVO vo) {
		if(vo != null)
			this.modules.add(vo);
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Boolean getIsRequired() {
		return isRequired;
	}

	public void setIsRequired(Boolean isRequired) {
		this.isRequired = isRequired;
	}

	public String getMode() {
		return mode;
	}

	public void setMode(String mode) {
		this.mode = mode;
	}

	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this);
	}

	public List<ZyyManualSettingModuleVO> getModules() {
		return modules;
	}

	public void setModules(List<ZyyManualSettingModuleVO> modules) {
		this.modules = modules;
	}

	public List<ZyyManualSickVO> getSick() {
		return sick;
	}

	public void setSick(List<ZyyManualSickVO> sick) {
		this.sick = sick;
	}

	public List<ZyyManualSkillVO> getSkill() {
		return skill;
	}

	public void setSkill(List<ZyyManualSkillVO> skill) {
		this.skill = skill;
	}

	public String getCycle() {
		return cycle;
	}

	public void setCycle(String cycle) {
		this.cycle = cycle;
	}
	
	public void read(ZyyDeptStd dept) {
		if(dept == null)
			return;
		this.mode = "update";
		this.setId(dept.getId());
		this.setName(dept.getName());
		if(dept.getIsRequired() != null)
			this.setIsRequired(dept.getIsRequired());
		if(dept.getCycleTime() != null)
			this.setCycle(dept.getCycleTime());
		this.setOrgId(dept.getZyyOrgId());
	}
	
	public void read(ZyyDept dept) {
		if(dept == null)
			return;
		this.setId(dept.getId());
		this.setName(dept.getDeptName());
		this.setOrgId(dept.getHospitalId());
	}

	public void write(ZyyDeptStd entity) {
		entity.setId(this.getId());
		entity.setIsRequired(this.getIsRequired());
		entity.setName(this.getName());
		entity.setCycleTime(this.getCycle());
		entity.setZyyOrgId(this.getOrgId());
	}

	public void writeToList(ZyyDeptStd entity, List<ZyyDeptStd> list) {
		write(entity);
		list.add(entity);
	}

	public Long getOrgId() {
		return orgId;
	}

	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}

	public List<ZyyDeptStd> getHospDepts() {
		return hospDepts;
	}

	public void setHospDepts(List<ZyyDeptStd> hospDepts) {
		this.hospDepts = hospDepts;
	}

	public List<ZyyDept> getHospRealDepts() {
		return hospRealDepts;
	}

	public void setHospRealDepts(List<ZyyDept> hospRealDepts) {
		this.hospRealDepts = hospRealDepts;
	}

}
