package com.hys.zyy.manage.model;

/**
 * 模块
 * <AUTHOR>
 *
 */
public class ZyyManualSettingModuleVO extends ZyyBaseObject  {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	private Long id;
	
	private String name;
	
	private Long orgId;
	
	private ZyyManualSettingModuleVO parent;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public ZyyManualSettingModuleVO getParent() {
		return parent;
	}

	public void setParent(ZyyManualSettingModuleVO parent) {
		this.parent = parent;
	}

	public void read(ZyyHandbookStd hb) {
		if(hb == null)
			return;
		this.id = hb.getId();
		this.name = hb.getName();
		this.orgId = hb.getOrgId();
	}

	public Long getOrgId() {
		return orgId;
	}

	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}
	
}
