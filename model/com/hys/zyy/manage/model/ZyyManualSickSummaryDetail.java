package com.hys.zyy.manage.model;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.builder.ReflectionToStringBuilder;

public class ZyyManualSickSummaryDetail extends ZyyBaseObject  {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	Long id;
	
	String title;
	
	Long orgId;
	
	List<ZyyManualDetailItem> sick = new ArrayList<ZyyManualDetailItem>();;
	
	String memo;
	
	public ZyyManualSickSummaryDetail() {
	}

	public ZyyManualSickSummaryDetail(String title) {
		this.title = title;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}
	
	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this);
	}

	public List<ZyyManualDetailItem> getSick() {
		return sick;
	}

	public void setSick(List<ZyyManualDetailItem> sick) {
		this.sick = sick;
	}

	public void read(ZyyDeptStdDisease entity) {
		if(entity == null)
			return;
		this.setTitle(entity.getTitleDesc());
		this.setId(entity.getId());
		this.setOrgId(entity.getZyyOrgId());
		this.setMemo(entity.getMemo());
		List<ZyyDeptStdDiseaseDetail> list = entity.getDetails();
		if(list == null || list.isEmpty())
			sick.add(new ZyyManualDetailItem());
		else {
			for(ZyyDeptStdDiseaseDetail en : list) {
				ZyyManualDetailItem item = new ZyyManualDetailItem();
				sick.add(item);
				item.read(en);
			}
		}
	}

	public boolean write(ZyyDeptStdDisease entity) {
		if((id == null || id.intValue() == 0) && StringUtils.isBlank(title))
			return false;
		entity.setTitleDesc(title);
		if(this.id != null)
			entity.setId(id);
		entity.setZyyOrgId(this.getOrgId());
		entity.setMemo(this.getMemo());
		for(ZyyManualDetailItem vo : sick) {
			if(vo.getDiseaseId() != null && vo.getDiseaseId().intValue() != 0) {
				ZyyDeptStdDiseaseDetail model = new ZyyDeptStdDiseaseDetail();
				vo.wirte(model);
				entity.getDetails().add(model);
			}
		}
		return true;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getOrgId() {
		return orgId;
	}

	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}

}
