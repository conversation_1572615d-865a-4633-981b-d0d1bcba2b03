package com.hys.zyy.manage.model;

import java.util.List;

import com.hys.zyy.manage.constants.Constants;
import com.hys.zyy.manage.util.StringPool;

/**
 * 疾病VO
 * <AUTHOR>
 *
 */
public class ZyyManualSickVO extends ZyyBaseObject  {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	private Long id = 0l;
	
	private String name = StringPool.BLANK;
	
	private String mode;				// static, update, new, remove
	
	private Long orgId = 0l;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}
	
	public String getMode() {
		return mode;
	}

	public void setMode(String mode) {
		this.mode = mode;
	}

	public void read(ZyyTrainDisease entity) {
		if(entity == null)
			return;
		this.setId(entity.getId());
		this.setName(entity.getDiseaseName());
		this.setOrgId(entity.getZyyOrgId());
	}

	public boolean write(ZyyTrainDisease entity) {
		if(this.name == null || "".equals(this.name.trim()))
			return false;
		entity.setId(this.id);
		entity.setDiseaseName(this.name);
		entity.setDiseaseType(Constants.DISEASE_TYPE_SICK);
		entity.setZyyOrgId(this.orgId);
		return true;
	}

	public void writeToList(ZyyTrainDisease entity, List<ZyyTrainDisease> list) {
		if(write(entity))
			list.add(entity);
	}

	public Long getOrgId() {
		return orgId;
	}

	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}
	
}
