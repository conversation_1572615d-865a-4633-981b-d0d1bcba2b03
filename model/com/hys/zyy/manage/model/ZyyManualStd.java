package com.hys.zyy.manage.model;

import java.io.Serializable;
import java.util.Date;

import com.hys.zyy.manage.util.BaseModel;

/**
 * 手册标准
 * <AUTHOR>
 */
public class ZyyManualStd extends BaseModel implements Serializable {
	private static final long serialVersionUID = 1L;
	/*
	 * ID
	 */
	private Long id;
	/*
	 * 省厅ID
	 */
	private Long zyyProvinceId;
	/*
	 * 手册标准名称
	 */
	private String manualStdName;
	/*
	 * 手册标准别名
	 */
	private String aliasName;
	/*
	 * 状态（1=有效；-1=失效）
	 */
	private Integer state;
	/*
	 * 创建者
	 */
	private Long creatorId;

	private Date createTime;
	private Date updateTime;

	public ZyyManualStd() {
		super();
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getZyyProvinceId() {
		return zyyProvinceId;
	}

	public void setZyyProvinceId(Long zyyProvinceId) {
		this.zyyProvinceId = zyyProvinceId;
	}

	public String getManualStdName() {
		return manualStdName;
	}

	public void setManualStdName(String manualStdName) {
		this.manualStdName = manualStdName == null ? null : manualStdName.trim();
	}

	public String getAliasName() {
		return aliasName;
	}

	public void setAliasName(String aliasName) {
		this.aliasName = aliasName == null ? null : aliasName.trim();
	}

	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	public Long getCreatorId() {
		return creatorId;
	}

	public void setCreatorId(Long creatorId) {
		this.creatorId = creatorId;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ZyyManualStd other = (ZyyManualStd) obj;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		return true;
	}

	@Override
	public String toString() {
		return "ZyyManualStd [id=" + id + ", zyyProvinceId=" + zyyProvinceId + ", manualStdName=" + manualStdName
				+ ", aliasName=" + aliasName + ", state=" + state + ", creatorId=" + creatorId + ", createTime="
				+ createTime + ", updateTime=" + updateTime + "]";
	}

}