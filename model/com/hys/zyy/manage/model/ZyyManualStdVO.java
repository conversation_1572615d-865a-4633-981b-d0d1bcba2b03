package com.hys.zyy.manage.model;

import java.util.ArrayList;
import java.util.List;

public class ZyyManualStdVO extends ZyyManualStd {

	private String creatorStr, createTimeStr, yearIds;
	private List<ZyyOrgYearManualStdVO> years = new ArrayList<>();

	public String getCreatorStr() {
		return creatorStr;
	}

	public void setCreatorStr(String creatorStr) {
		this.creatorStr = creatorStr == null ? null : creatorStr.trim();
	}

	public String getCreateTimeStr() {
		return createTimeStr;
	}

	public void setCreateTimeStr(String createTimeStr) {
		this.createTimeStr = createTimeStr == null ? null : createTimeStr.trim();
	}

	public List<ZyyOrgYearManualStdVO> getYears() {
		return years;
	}

	public void setYears(List<ZyyOrgYearManualStdVO> years) {
		this.years = years;
	}

	public String getYearIds() {
		return yearIds;
	}

	public void setYearIds(String yearIds) {
		this.yearIds = yearIds == null ? null : yearIds.trim();
	}
}