package com.hys.zyy.manage.model;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang.builder.ReflectionToStringBuilder;

/**
 * 登记手册基地概述信息
 * <AUTHOR>
 *
 */
public class ZyyManualSummaryVO extends ZyyBaseObject  {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	private Long baseId;				// 基地ID
	
	private String preface;				// 前言
	
	private String target;				// 目标
	
	private List<ZyyManualSettingDeptVO> requiredList = new ArrayList<ZyyManualSettingDeptVO>();	// 必选
	
	private List<ZyyManualSettingDeptVO> optionList = new ArrayList<ZyyManualSettingDeptVO>();	// 非必选
	
	private String memo;				// 备注
	
	public static final ZyyManualSummaryVO NULL = new ZyyManualSummaryVO();

	public void addRequiredItem(ZyyManualSettingDeptVO item) {
		if(item != null)
			this.requiredList.add(item);
	}
	
	public void addOptionItem(ZyyManualSettingDeptVO item) {
		if(item != null)
			this.optionList.add(item);
	}
	
	public Long getBaseId() {
		return baseId;
	}

	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}

	public String getPreface() {
		return preface;
	}

	public void setPreface(String preface) {
		this.preface = preface;
	}

	public String getTarget() {
		return target;
	}

	public void setTarget(String target) {
		this.target = target;
	}

	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this);
	}

	public List<ZyyManualSettingDeptVO> getRequiredList() {
		return requiredList;
	}

	public void setRequiredList(List<ZyyManualSettingDeptVO> requiredList) {
		this.requiredList = requiredList;
	}

	public List<ZyyManualSettingDeptVO> getOptionList() {
		return optionList;
	}

	public void setOptionList(List<ZyyManualSettingDeptVO> optionList) {
		this.optionList = optionList;
	}

	public void read(ZyyBaseStd stdbase) {
		if(stdbase == null)
			return;
		this.setBaseId(stdbase.getId());
		this.setPreface(stdbase.getPreface());
		this.setTarget(stdbase.getTarget());
		this.setMemo(stdbase.getMemo());
	}
	
}
