package com.hys.zyy.manage.model;

import java.util.Date;

/**
 * 登记手册带教、科室审核结果表
 * 
 * <AUTHOR>
 * 
 */
public class ZyyManualTeacherVerifyResultVO extends ZyyBaseObject  {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private Long id;//id
	private Long cycleTableId;//轮转表ID
	private Long residencyId;//住院医师ID
	private Long deptId;//科室ID
	private Integer cycleType;//轮转类别
	private Integer cycleTime;//轮转时间
	private Date startTime;//开始时间
	private Date endDate;//结束时间
	private Integer seq;//顺序
	private Integer isAuto;//是否自动轮转
	private Integer status;//状态
	private Integer cycleStatus;//轮转状态
	private String remark;//备注
	private Long scheduler;//排班人
	private Date schedulerDate;//排班时间
	private Long lastSheduler;//最后修改人
	private Date lastUpdateDate;//最后修改时间
	private Integer teacherCheckStatus;//带教老师最后审核状态
	private Integer directorCheckStatus;//科室主任最后审核状态
	private Date teacherCheckDate;//带教老师最后审核时间
	private Date directorCheckDate;//科室主任最后审核时间
	private Long commitTimes;//住院医师提交次数
	private Date residencyLastDate;//住院医师最后提交时间
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getCycleTableId() {
		return cycleTableId;
	}
	public void setCycleTableId(Long cycleTableId) {
		this.cycleTableId = cycleTableId;
	}
	public Long getResidencyId() {
		return residencyId;
	}
	public void setResidencyId(Long residencyId) {
		this.residencyId = residencyId;
	}
	public Long getDeptId() {
		return deptId;
	}
	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}
	public Integer getCycleType() {
		return cycleType;
	}
	public void setCycleType(Integer cycleType) {
		this.cycleType = cycleType;
	}
	public Integer getCycleTime() {
		return cycleTime;
	}
	public void setCycleTime(Integer cycleTime) {
		this.cycleTime = cycleTime;
	}
	public Date getStartTime() {
		return startTime;
	}
	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}
	public Date getEndDate() {
		return endDate;
	}
	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}
	public Integer getSeq() {
		return seq;
	}
	public void setSeq(Integer seq) {
		this.seq = seq;
	}
	public Integer getIsAuto() {
		return isAuto;
	}
	public void setIsAuto(Integer isAuto) {
		this.isAuto = isAuto;
	}
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	public Integer getCycleStatus() {
		return cycleStatus;
	}
	public void setCycleStatus(Integer cycleStatus) {
		this.cycleStatus = cycleStatus;
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	public Long getScheduler() {
		return scheduler;
	}
	public void setScheduler(Long scheduler) {
		this.scheduler = scheduler;
	}
	public Date getSchedulerDate() {
		return schedulerDate;
	}
	public void setSchedulerDate(Date schedulerDate) {
		this.schedulerDate = schedulerDate;
	}
	public Long getLastSheduler() {
		return lastSheduler;
	}
	public void setLastSheduler(Long lastSheduler) {
		this.lastSheduler = lastSheduler;
	}
	public Date getLastUpdateDate() {
		return lastUpdateDate;
	}
	public void setLastUpdateDate(Date lastUpdateDate) {
		this.lastUpdateDate = lastUpdateDate;
	}
	public Integer getTeacherCheckStatus() {
		return teacherCheckStatus;
	}
	public void setTeacherCheckStatus(Integer teacherCheckStatus) {
		this.teacherCheckStatus = teacherCheckStatus;
	}
	public Integer getDirectorCheckStatus() {
		return directorCheckStatus;
	}
	public void setDirectorCheckStatus(Integer directorCheckStatus) {
		this.directorCheckStatus = directorCheckStatus;
	}
	public Date getTeacherCheckDate() {
		return teacherCheckDate;
	}
	public void setTeacherCheckDate(Date teacherCheckDate) {
		this.teacherCheckDate = teacherCheckDate;
	}
	public Date getDirectorCheckDate() {
		return directorCheckDate;
	}
	public void setDirectorCheckDate(Date directorCheckDate) {
		this.directorCheckDate = directorCheckDate;
	}
	public Long getCommitTimes() {
		return commitTimes;
	}
	public void setCommitTimes(Long commitTimes) {
		this.commitTimes = commitTimes;
	}
	public Date getResidencyLastDate() {
		return residencyLastDate;
	}
	public void setResidencyLastDate(Date residencyLastDate) {
		this.residencyLastDate = residencyLastDate;
	}

}