package com.hys.zyy.manage.model;

import org.apache.commons.lang.builder.ReflectionToStringBuilder;

/**
 * ZyyManualDeptSummaryVO
 * <AUTHOR>
 *
 */
public class ZyyManualTitleContentVO  extends ZyyBaseObject  {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	private String title;
	
	private String content;
	
	public ZyyManualTitleContentVO() {
		super();
	}

	public ZyyManualTitleContentVO(String content) {
		super();
		this.content = content;
	}

	public ZyyManualTitleContentVO(String title, String content) {
		super();
		this.title = title;
		this.content = content;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}
	
	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this);
	}
	
}
