package com.hys.zyy.manage.model;

import java.util.Date;
import java.util.List;

import com.hys.zyy.manage.constants.ZyyMessageSendUserType;

/**
 * 
 * 标题：zyy
 * 
 * 作者：Tony Apr 5, 2012
 * 
 * 描述：
 * 
 * 说明:
 */
public class ZyyMessage extends ZyyBaseObject {

	/**
	 * 
	 */
	private static final long serialVersionUID = -5859051122782168228L;

	private Long id;
	
	/**
	 * 组织机构ID
	 */
	private Long zyyOrgId;
	
	/**
	 * 通知类别ID
	 */
	private Long messageTypeId;

	/**
	 * 通知类别
	 * 1 -通知 2 -草稿
	 */
	private Integer messageType;
	
	/**
	 * 通知时间
	 */
	private Date messageDate;
	
	
	/**
	 * 通知创建时间
	 */
	private Date messageCreateDate;
	
	
	
	/**
	 * 通知级别
	 * 1 -重要 2  -一般
	 */
	private Integer messageLevel = 1;
	
	/**
	 * 是否置顶
	 * 1：是
	 * 0：否
	 */
	private Integer isOntop;
	
	/**
	 * 置顶天数
	 */
	private Integer ontopDays = 0;
	
	
	
	
	/**
	 * 通知标题
	 */
	private String messageTitle;
	
	/**
	 * 通知内容
	 */
	private String messageContent;
	
	/**
	 * 发送者
	 */
	private Long messageSender;
	
	/**
	 * 发送者名称
	 */	
	private String messageSenderName;
	
	/**
	 * 收信人
	 */
	private String addressee;
	
	/**
	 * 是否加新
	 * 1：是
	 * 0：否
	 */
	private Integer isNew = 0;
	
	/**
	 * 通知地址 首页
	 * 1：发送 
	 * 0：不发送
	 */
	private Integer messageToIndex;
	
	/**
	 * 通知地址 通知栏
	 * 1：发送 
	 * 0：不发送
	 */
	private Integer messageToInner;
	
	/**
	 * 通知地址 邮箱
	 * 1：发送 
	 * 0：不发送
	 */
	private Integer messageToEmail;
	
	/**
	 * 通知地址 短信
	 * 1：发送 
	 * 0：不发送
	 */
	private Integer messageToSms;
	
	
	/**
	 * 通知对象
	 */
	private List<ZyyMessageUserType> zyyMessageUserTypeList;

	private List<ZyyMessageSendUserType> zyyMessageSendUserType;
	
	/**
	 * 通知用户
	 */
	private List<ZyyMessageUser> zyyMessageUserList;
	
	/**
	 * 通知栏目ID(网站首页)
	 */
	private Long messageCategoryId;
	
	/**
	 * 是否全员通知
	 * 1：是
	 * 0：否
	 */
	private Integer isFull = 0;
	
	private String users;
	
	private Integer status;//1表示已读，0表示未读
	
	
	/**
	 * 附件
	 */
	private List<ZyyMessageAttachment> zyyMessageAttachmentList;
	
	public Integer getIsFull() {
		return isFull;
	}

	public void setIsFull(Integer isFull) {
		this.isFull = isFull;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getMessageTypeId() {
		return messageTypeId;
	}

	public void setMessageTypeId(Long messageTypeId) {
		this.messageTypeId = messageTypeId;
	}

	public Date getMessageDate() {
		return messageDate;
	}

	public void setMessageDate(Date messageDate) {
		this.messageDate = messageDate;
	}

	public Integer getMessageLevel() {
		return messageLevel;
	}

	public void setMessageLevel(Integer messageLevel) {
		this.messageLevel = messageLevel;
	}



	public Integer getIsOntop() {
		return isOntop;
	}

	public void setIsOntop(Integer isOntop) {
		this.isOntop = isOntop;
	}

	public String getMessageTitle() {
		return messageTitle;
	}

	public void setMessageTitle(String messageTitle) {
		this.messageTitle = messageTitle;
	}

	public String getMessageContent() {
		return messageContent;
	}

	public void setMessageContent(String messageContent) {
		this.messageContent = messageContent;
	}

	public List<ZyyMessageUserType> getZyyMessageUserTypeList() {
		return zyyMessageUserTypeList;
	}

	public void setZyyMessageUserTypeList(
			List<ZyyMessageUserType> zyyMessageUserTypeList) {
		this.zyyMessageUserTypeList = zyyMessageUserTypeList;
	}

	public List<ZyyMessageUser> getZyyMessageUserList() {
		return zyyMessageUserList;
	}

	public void setZyyMessageUserList(List<ZyyMessageUser> zyyMessageUserList) {
		this.zyyMessageUserList = zyyMessageUserList;
	}

	public Long getMessageSender() {
		return messageSender;
	}

	public void setMessageSender(Long messageSender) {
		this.messageSender = messageSender;
	}

	public String getMessageSenderName() {
		return messageSenderName;
	}

	public void setMessageSenderName(String messageSenderName) {
		this.messageSenderName = messageSenderName;
	}

	public Integer getIsNew() {
		return isNew;
	}

	public void setIsNew(Integer isNew) {
		this.isNew = isNew;
	}

	public Integer getMessageToIndex() {
		return messageToIndex;
	}

	public void setMessageToIndex(Integer messageToIndex) {
		this.messageToIndex = messageToIndex;
	}

	public Integer getMessageToInner() {
		return messageToInner;
	}

	public void setMessageToInner(Integer messageToInner) {
		this.messageToInner = messageToInner;
	}

	public Integer getMessageToEmail() {
		return messageToEmail;
	}

	public void setMessageToEmail(Integer messageToEmail) {
		this.messageToEmail = messageToEmail;
	}

	public Integer getMessageToSms() {
		return messageToSms;
	}

	public void setMessageToSms(Integer messageToSms) {
		this.messageToSms = messageToSms;
	}

	public Date getMessageCreateDate() {
		return messageCreateDate;
	}

	public void setMessageCreateDate(Date messageCreateDate) {
		this.messageCreateDate = messageCreateDate;
	}

	public String getAddressee() {
		return addressee;
	}

	public void setAddressee(String addressee) {
		this.addressee = addressee;
	}

	public Integer getOntopDays() {
		return ontopDays;
	}

	public void setOntopDays(Integer ontopDays) {
		this.ontopDays = ontopDays;
	}

	public Long getZyyOrgId() {
		return zyyOrgId;
	}

	public void setZyyOrgId(Long zyyOrgId) {
		this.zyyOrgId = zyyOrgId;
	}

	public Integer getMessageType() {
		return messageType;
	}

	public void setMessageType(Integer messageType) {
		this.messageType = messageType;
	}

	public Long getMessageCategoryId() {
		return messageCategoryId;
	}

	public void setMessageCategoryId(Long messageCategoryId) {
		this.messageCategoryId = messageCategoryId;
	}

	public List<ZyyMessageAttachment> getZyyMessageAttachmentList() {
		return zyyMessageAttachmentList;
	}

	public void setZyyMessageAttachmentList(
			List<ZyyMessageAttachment> zyyMessageAttachmentList) {
		this.zyyMessageAttachmentList = zyyMessageAttachmentList;
	}

	public String getUsers() {
		return users;
	}

	public void setUsers(String users) {
		this.users = users;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public List<ZyyMessageSendUserType> getZyyMessageSendUserType() {
		return zyyMessageSendUserType;
	}

	public void setZyyMessageSendUserType(List<ZyyMessageSendUserType> zyyMessageSendUserType) {
		this.zyyMessageSendUserType = zyyMessageSendUserType;
	}

	
	
	
	
}
