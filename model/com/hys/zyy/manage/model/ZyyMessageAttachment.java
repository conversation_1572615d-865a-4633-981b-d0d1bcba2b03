package com.hys.zyy.manage.model;
/**
 * 
 * 标题：zyy
 * 
 * 作者：zdz Apr 5, 2012
 * 
 * 描述：
 * 
 * 说明:通知附件
 */
public class ZyyMessageAttachment extends ZyyBaseObject {
	
	
	
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 7268223684089810402L;

	private Long id;
	
	/**
	 * 通知ID
	 */
	private Long zyyMessageId;
	
	/**
	 * 附件地址
	 */
	private String attachmentPath;
	
	/**
	 * 附件名称
	 */
	private String attachmentName;

	public String getAttachmentName() {
		return attachmentName;
	}

	public void setAttachmentName(String attachmentName) {
		this.attachmentName = attachmentName;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getZyyMessageId() {
		return zyyMessageId;
	}

	public void setZyyMessageId(Long zyyMessageId) {
		this.zyyMessageId = zyyMessageId;
	}

	public String getAttachmentPath() {
		return attachmentPath;
	}

	public void setAttachmentPath(String attachmentPath) {
		this.attachmentPath = attachmentPath;
	}

	


	

}
