package com.hys.zyy.manage.model;

import java.util.Date;

/**
 * 
 * 标题：zyy
 * 
 * 作者：Tony Apr 5, 2012
 * 
 * 描述：
 * 
 * 说明:系统通知设置
 */
public class ZyyMessageSetting extends ZyyBaseObject {

	/**
	 * 
	 */
	private static final long serialVersionUID = -2426565867465459774L;

	private Long setId;// 设置ID
	private Long orgId;// 机构ID，省份管理员登录，为省ID
	private Integer setItemId;// 设置通知项ID
	private Integer setStatus;// 设置是否开启通知  1.开启  2 未开启
	private Integer beforeTime;// 通知提前时间，单位天
	private Date createTime;// 修改时间
	public Long getSetId() {
		return setId;
	}
	public void setSetId(Long setId) {
		this.setId = setId;
	}
	public Long getOrgId() {
		return orgId;
	}
	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}
	public Integer getSetItemId() {
		return setItemId;
	}
	public void setSetItemId(Integer setItemId) {
		this.setItemId = setItemId;
	}
	public Integer getSetStatus() {
		return setStatus;
	}
	public void setSetStatus(Integer setStatus) {
		this.setStatus = setStatus;
	}
	public Integer getBeforeTime() {
		return beforeTime;
	}
	public void setBeforeTime(Integer beforeTime) {
		this.beforeTime = beforeTime;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

}
