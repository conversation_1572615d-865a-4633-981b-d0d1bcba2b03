package com.hys.zyy.manage.model;
/**
 * 
 * 标题：zyy
 * 
 * 作者：Tony Apr 5, 2012
 * 
 * 描述：通知用户
 * 
 * 说明:
 */
public class ZyyMessageUser extends ZyyBaseObject {

	/**
	 * 
	 */
	private static final long serialVersionUID = -4234113109508611186L;
	
	/**
	 * 通知ID
	 */
	private Long zyyMessageId;
	
	/**
	 * 通知用户ID
	 */
	private Long zyyUserId;
	
	/**
	 * 
	 * 通知状态
	 * 
	 * 0: 未读
	 * 1: 已读
	 */
	private Integer status;

	public Long getZyyMessageId() {
		return zyyMessageId;
	}

	public void setZyyMessageId(Long zyyMessageId) {
		this.zyyMessageId = zyyMessageId;
	}

	public Long getZyyUserId() {
		return zyyUserId;
	}

	public void setZyyUserId(Long zyyUserId) {
		this.zyyUserId = zyyUserId;
	}


	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}
	
}
