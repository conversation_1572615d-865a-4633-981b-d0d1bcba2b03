package com.hys.zyy.manage.model;
/**
 * 
 * 标题：zyy
 * 
 * 作者：Tony Apr 5, 2012
 * 
 * 描述：通知用户类别
 * 
 * 说明:
 */
public class ZyyMessageUserType extends ZyyBaseObject {

	/**
	 * 
	 */
	private static final long serialVersionUID = -5079092490845014326L;
	
	/**
	 * 通知id
	 */
	private Long zyyMessageId;
	
	/**
	 * 用户类别id
	 */
	private Integer zyyUserTypeId;

	public Long getZyyMessageId() {
		return zyyMessageId;
	}

	public void setZyyMessageId(Long zyyMessageId) {
		this.zyyMessageId = zyyMessageId;
	}

	public Integer getZyyUserTypeId() {
		return zyyUserTypeId;
	}

	public void setZyyUserTypeId(Integer zyyUserTypeId) {
		this.zyyUserTypeId = zyyUserTypeId;
	}
	
	

}
