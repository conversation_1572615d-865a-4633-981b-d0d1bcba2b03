package com.hys.zyy.manage.model;

public class ZyyMiniCexFormVO extends ZyyMiniCexForm {

	private Long yearId, baseId, teacherId;
	private String realName, year, baseName, deptName, assessTimeStr, startDateStr, endDateStr;
	private Double ylmtAvgScore, tgjcAvgScore, gtjnAvgScore, lcpdAvgScore, rwghAvgScore, zzxnAvgScore, ztbxAvgScore;

	public Long getYearId() {
		return yearId;
	}

	public void setYearId(Long yearId) {
		this.yearId = yearId;
	}

	public Long getBaseId() {
		return baseId;
	}

	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}

	public Long getTeacherId() {
		return teacherId;
	}

	public void setTeacherId(Long teacherId) {
		this.teacherId = teacherId;
	}

	public String getRealName() {
		return realName;
	}

	public void setRealName(String realName) {
		this.realName = realName == null ? null : realName.trim();
	}

	public String getYear() {
		return year;
	}

	public void setYear(String year) {
		this.year = year == null ? null : year.trim();
	}

	public String getBaseName() {
		return baseName;
	}

	public void setBaseName(String baseName) {
		this.baseName = baseName == null ? null : baseName.trim();
	}

	public String getDeptName() {
		return deptName;
	}

	public void setDeptName(String deptName) {
		this.deptName = deptName == null ? null : deptName.trim();
	}

	public String getAssessTimeStr() {
		return assessTimeStr;
	}

	public void setAssessTimeStr(String assessTimeStr) {
		this.assessTimeStr = assessTimeStr == null ? null : assessTimeStr.trim();
	}

	public ZyyMiniCexFormVO() {
		super();
	}

	public ZyyMiniCexFormVO(Long id) {
		super(id);
	}

	public ZyyMiniCexFormVO(Long id, String pdfUrl) {
		super(id, pdfUrl);
	}

	public ZyyMiniCexFormVO(Long id, Integer residencyJoyLevel, Integer examinerJoyLevel) {
		super(id, residencyJoyLevel, examinerJoyLevel);
	}

	public String getStartDateStr() {
		return startDateStr;
	}

	public void setStartDateStr(String startDateStr) {
		this.startDateStr = startDateStr == null ? null : startDateStr.trim();
	}

	public String getEndDateStr() {
		return endDateStr;
	}

	public void setEndDateStr(String endDateStr) {
		this.endDateStr = endDateStr == null ? null : endDateStr.trim();
	}

	public Double getYlmtAvgScore() {
		return ylmtAvgScore;
	}

	public void setYlmtAvgScore(Double ylmtAvgScore) {
		this.ylmtAvgScore = ylmtAvgScore;
	}

	public Double getTgjcAvgScore() {
		return tgjcAvgScore;
	}

	public void setTgjcAvgScore(Double tgjcAvgScore) {
		this.tgjcAvgScore = tgjcAvgScore;
	}

	public Double getGtjnAvgScore() {
		return gtjnAvgScore;
	}

	public void setGtjnAvgScore(Double gtjnAvgScore) {
		this.gtjnAvgScore = gtjnAvgScore;
	}

	public Double getLcpdAvgScore() {
		return lcpdAvgScore;
	}

	public void setLcpdAvgScore(Double lcpdAvgScore) {
		this.lcpdAvgScore = lcpdAvgScore;
	}

	public Double getRwghAvgScore() {
		return rwghAvgScore;
	}

	public void setRwghAvgScore(Double rwghAvgScore) {
		this.rwghAvgScore = rwghAvgScore;
	}

	public Double getZzxnAvgScore() {
		return zzxnAvgScore;
	}

	public void setZzxnAvgScore(Double zzxnAvgScore) {
		this.zzxnAvgScore = zzxnAvgScore;
	}

	public Double getZtbxAvgScore() {
		return ztbxAvgScore;
	}

	public void setZtbxAvgScore(Double ztbxAvgScore) {
		this.ztbxAvgScore = ztbxAvgScore;
	}

}