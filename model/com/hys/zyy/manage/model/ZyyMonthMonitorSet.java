package com.hys.zyy.manage.model;

import java.io.Serializable;
import java.util.Date;

/**
 * 月度监测初始化设置
 */
public class ZyyMonthMonitorSet implements Serializable {
	private static final long serialVersionUID = 1L;
	/*
	 * ID
	 */
	private Long id;
	/*
	 * 医院ID
	 */
	private Long hospitalId;
	/*
	 * 上报后是否允许科室修改（-1：不允许；1：允许）
	 */
	private Integer allowModify;
	/*
	 * 创建者ID
	 */
	private Long createUserId;
	/*
	 * 提示语
	 */
	private String tipsInfo;
	/*
	 * 创建时间
	 */
	private Date createTime;
	/*
	 * 修改时间
	 */
	private Date updateTime;
	
	public ZyyMonthMonitorSet() {
		super();
	}

	public ZyyMonthMonitorSet(Long hospitalId) {
		super();
		this.hospitalId = hospitalId;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getHospitalId() {
		return hospitalId;
	}

	public void setHospitalId(Long hospitalId) {
		this.hospitalId = hospitalId;
	}

	public Integer getAllowModify() {
		return allowModify;
	}

	public void setAllowModify(Integer allowModify) {
		this.allowModify = allowModify;
	}

	public Long getCreateUserId() {
		return createUserId;
	}

	public void setCreateUserId(Long createUserId) {
		this.createUserId = createUserId;
	}

	public String getTipsInfo() {
		return tipsInfo;
	}

	public void setTipsInfo(String tipsInfo) {
		this.tipsInfo = tipsInfo;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ZyyMonthMonitorSet other = (ZyyMonthMonitorSet) obj;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		return true;
	}

	@Override
	public String toString() {
		return "ZyyMonthMonitorSet [id=" + id + ", hospitalId=" + hospitalId
				+ ", allowModify=" + allowModify + ", createUserId="
				+ createUserId + ", tipsInfo=" + tipsInfo + ", createTime="
				+ createTime + ", updateTime=" + updateTime + "]";
	}
	
	
}

























