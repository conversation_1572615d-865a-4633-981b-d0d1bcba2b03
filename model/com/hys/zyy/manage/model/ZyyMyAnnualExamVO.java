package com.hys.zyy.manage.model;

import java.util.List;



/**
 * 
 * 年度考核
 *
 */
public class ZyyMyAnnualExamVO extends ZyyBaseObject{

	private static final long serialVersionUID = 1L;
	
	/**
	 * 考试id
	 */
	private Long examId;
	/**
	 * 考试名称
	 */
	private String examName;
	/**
	 * 开始日期
	 */
	private String examStartDate;
	/**
	 * 结束日期
	 */
	private String examEndDate;
	/**
	 * 时长
	 */
	private String examDuration;
	/**
	 * 学员id
	 */
	private Long residencyId;
	/**
	 * 考试的科目的id
	 */
	private Long examCourseId;
	/**
	 * 存储标准科室名称的集合
	 */
	private List<String> deptStdNameList;
	public Long getExamId() {
		return examId;
	}
	public void setExamId(Long examId) {
		this.examId = examId;
	}
	public String getExamName() {
		return examName;
	}
	public void setExamName(String examName) {
		this.examName = examName;
	}
	public String getExamStartDate() {
		return examStartDate;
	}
	public void setExamStartDate(String examStartDate) {
		this.examStartDate = examStartDate;
	}
	public String getExamEndDate() {
		return examEndDate;
	}
	public void setExamEndDate(String examEndDate) {
		this.examEndDate = examEndDate;
	}
	public String getExamDuration() {
		return examDuration;
	}
	public void setExamDuration(String examDuration) {
		this.examDuration = examDuration;
	}
	public Long getResidencyId() {
		return residencyId;
	}
	public void setResidencyId(Long residencyId) {
		this.residencyId = residencyId;
	}
	public Long getExamCourseId() {
		return examCourseId;
	}
	public void setExamCourseId(Long examCourseId) {
		this.examCourseId = examCourseId;
	}
	public List<String> getDeptStdNameList() {
		return deptStdNameList;
	}
	public void setDeptStdNameList(List<String> deptStdNameList) {
		this.deptStdNameList = deptStdNameList;
	}
	
	
}
