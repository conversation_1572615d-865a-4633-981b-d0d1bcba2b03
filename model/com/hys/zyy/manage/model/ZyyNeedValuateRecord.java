package com.hys.zyy.manage.model;

import java.util.Date;

/**
 * 待评价内容记录表
 * <AUTHOR>
 * @date 2018-9-4上午10:18:31
 */
public class ZyyNeedValuateRecord extends ZyyBaseObject{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	//	评价表配置ID	
	private Long tableConfigId;
	//	用户ID	
	private Long userId;
	//	专业ID	
	private Long baseId;
	//	科室ID	
	private Long deptId;
	//	医院ID	
	private Long hospitalId;
	//	评论开始时间	
	private String startTime;
	//	评论截止时间	
	private String endTime;
	//	入科时间	
	private String enterTime;
	//	出科时间	
	private String leaveTime;
	//	被评价用户ID	
	private Long evaluatedUserId;
	//	被评价专业ID	
	private Long evaluatedBaseId;
	//	被评价科室ID	
	private Long evaluatedDeptId;
	//	被评价医院ID	
	private Long evaluatedHospitalId;
	//	创建时间	
	private Date createTime;
	//更新时间
	private Date updateTime;
	
	//组合key值 - 唯一值 = C+配置表id+U/B/D/H+评论者id+E+U/B/D/H+被评论者id+T+轮转结束时间 UNIQUE_KEY
	private String uniqueKey;
	
	public Long getTableConfigId() {
		return tableConfigId;
	}
	public void setTableConfigId(Long tableConfigId) {
		this.tableConfigId = tableConfigId;
	}
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	public Long getBaseId() {
		return baseId;
	}
	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}
	public Long getDeptId() {
		return deptId;
	}
	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}
	public Long getHospitalId() {
		return hospitalId;
	}
	public void setHospitalId(Long hospitalId) {
		this.hospitalId = hospitalId;
	}
	public String getStartTime() {
		return startTime;
	}
	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}
	public String getEndTime() {
		return endTime;
	}
	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}
	public Long getEvaluatedUserId() {
		return evaluatedUserId;
	}
	public void setEvaluatedUserId(Long evaluatedUserId) {
		this.evaluatedUserId = evaluatedUserId;
	}
	public Long getEvaluatedBaseId() {
		return evaluatedBaseId;
	}
	public void setEvaluatedBaseId(Long evaluatedBaseId) {
		this.evaluatedBaseId = evaluatedBaseId;
	}
	public Long getEvaluatedDeptId() {
		return evaluatedDeptId;
	}
	public void setEvaluatedDeptId(Long evaluatedDeptId) {
		this.evaluatedDeptId = evaluatedDeptId;
	}
	public Long getEvaluatedHospitalId() {
		return evaluatedHospitalId;
	}
	public void setEvaluatedHospitalId(Long evaluatedHospitalId) {
		this.evaluatedHospitalId = evaluatedHospitalId;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	public String getEnterTime() {
		return enterTime;
	}
	public void setEnterTime(String enterTime) {
		this.enterTime = enterTime;
	}
	public String getLeaveTime() {
		return leaveTime;
	}
	public void setLeaveTime(String leaveTime) {
		this.leaveTime = leaveTime;
	}
	public Date getUpdateTime() {
		return updateTime;
	}
	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
	public String getUniqueKey() {
		return uniqueKey;
	}
	public void setUniqueKey(String uniqueKey) {
		this.uniqueKey = uniqueKey;
	}
}
