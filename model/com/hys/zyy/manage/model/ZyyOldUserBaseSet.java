package com.hys.zyy.manage.model;

import java.io.Serializable;
import java.util.Date;

import com.hys.zyy.manage.util.BaseModel;

/**
 * 老学员注册专业基地限制
 */
public class ZyyOldUserBaseSet extends BaseModel implements Serializable {
	/*
	 * ID
	 */
	private Long id;
	/*
	 * 省厅ID
	 */
	private Long provinceId;
	/*
	 * 中西医
	 */
	private Integer hospType;
	/*
	 * 最高学历（1=大学专科；2=大学本科；3=硕士研究生；4=博士研究生）
	 */
	private Integer highestRecordSchool;
	/*
	 * 最高学历毕业专业
	 */
	private String highestRecordProf;
	/*
	 * 可选培训专科
	 */
	private Long zyyBaseStdId;
	/*
	 * 状态（1：有效；-1：失效）
	 */
	private Integer state;

	private Date createTime;

	private Date updateTime;

	public ZyyOldUserBaseSet() {
		super();
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getProvinceId() {
		return provinceId;
	}

	public void setProvinceId(Long provinceId) {
		this.provinceId = provinceId;
	}

	public Integer getHospType() {
		return hospType;
	}

	public void setHospType(Integer hospType) {
		this.hospType = hospType;
	}

	public Integer getHighestRecordSchool() {
		return highestRecordSchool;
	}

	public void setHighestRecordSchool(Integer highestRecordSchool) {
		this.highestRecordSchool = highestRecordSchool;
	}

	public String getHighestRecordProf() {
		return highestRecordProf;
	}

	public void setHighestRecordProf(String highestRecordProf) {
		this.highestRecordProf = highestRecordProf;
	}

	public Long getZyyBaseStdId() {
		return zyyBaseStdId;
	}

	public void setZyyBaseStdId(Long zyyBaseStdId) {
		this.zyyBaseStdId = zyyBaseStdId;
	}

	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ZyyOldUserBaseSet other = (ZyyOldUserBaseSet) obj;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		return true;
	}

	@Override
	public String toString() {
		return "ZyyOldUserBaseSet [id=" + id + ", provinceId=" + provinceId
				+ ", hospType=" + hospType + ", highestRecordSchool="
				+ highestRecordSchool + ", highestRecordProf="
				+ highestRecordProf + ", zyyBaseStdId=" + zyyBaseStdId
				+ ", state=" + state + ", createTime=" + createTime
				+ ", updateTime=" + updateTime + "]";
	}

}
