package com.hys.zyy.manage.model;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：张伟清 2010-03-16
 * 
 * 描述：组织机构
 * 
 * 说明:
 */
public class ZyyOrg extends ZyyBaseObject {

	private static final long serialVersionUID = 5743428215798651466L;

	/**
	 * 主键ID
	 */
	private Long id ;
	
	/**
	 * 组织机构类别
	 */
	private Long orgTypeId ;
	
	/**
	 * 父组织机构ID
	 */
	private Long parentOrgId ;
	
	/**
	 * 机构编码
	 */
	private String orgCode ;
	
	/**
	 * 机构名称
	 */
	private String orgName ;
	
	/**
	 * 组织机构标识
	 */
	private Integer orgTypeFlag ;
	
	/**
	 * 机构别名
	 */
	private String aliasName ;
	
	/**
	 * 机构排序-1	大学
	 */
	private Integer orgSeq ;
	
	/**
	 * 机构排序-2 省厅
	 */
	private Integer orgSeq2 ;
	
	/**
	 * 状态 1.正常  -1.删除
	 */
	private Integer status ;
	
	/**
	 * 机构缩写
	 */
	private String orgAbbr;
	
	/**
	 *	行政上级机构ID 
	 */
	private Long adminParentOrgId;
	
	/**
	 * 医院属性 1.中医  2.西医   3.中西医   省厅管理机构增加( 4.中医+中西医  5.西医+中西医  6. 全部(中医+西医+中西医) )
	 */
	private Integer hospType;
	/*
	 * 基地编码
	 */
	private String baseCode;
	/*
	 * BDP机构ID
	 */
	private Long bdpOrgId;
	
	public ZyyOrg() {
		super();
	}
	
	public ZyyOrg(Integer orgTypeFlag) {
		super();
		this.orgTypeFlag = orgTypeFlag;
	}

	public ZyyOrg(Long parentOrgId, Integer orgTypeFlag) {
		super();
		this.parentOrgId = parentOrgId;
		this.orgTypeFlag = orgTypeFlag;
	}

	public String getOrgAbbr() {
		return orgAbbr;
	}

	public void setOrgAbbr(String orgAbbr) {
		this.orgAbbr = orgAbbr;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getOrgTypeId() {
		return orgTypeId;
	}

	public void setOrgTypeId(Long orgTypeId) {
		this.orgTypeId = orgTypeId;
	}

	public Long getParentOrgId() {
		return parentOrgId;
	}

	public void setParentOrgId(Long parentOrgId) {
		this.parentOrgId = parentOrgId;
	}

	public String getOrgCode() {
		return orgCode;
	}

	public void setOrgCode(String orgCode) {
		this.orgCode = orgCode;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName == null ? null : orgName.trim();
	}

	public Integer getOrgTypeFlag() {
		return orgTypeFlag;
	}

	public void setOrgTypeFlag(Integer orgTypeFlag) {
		this.orgTypeFlag = orgTypeFlag;
	}

	public String getAliasName() {
		return aliasName;
	}

	public void setAliasName(String aliasName) {
		this.aliasName = aliasName;
	}

	public Integer getOrgSeq() {
		return orgSeq;
	}

	public void setOrgSeq(Integer orgSeq) {
		this.orgSeq = orgSeq;
	}

	public Integer getOrgSeq2() {
		return orgSeq2;
	}

	public void setOrgSeq2(Integer orgSeq2) {
		this.orgSeq2 = orgSeq2;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}
	public Long getAdminParentOrgId() {
		return adminParentOrgId;
	}

	public void setAdminParentOrgId(Long adminParentOrgId) {
		this.adminParentOrgId = adminParentOrgId;
	}

	public Integer getHospType() {
		return hospType;
	}

	public void setHospType(Integer hospType) {
		this.hospType = hospType;
	}

	public String getBaseCode() {
		return baseCode;
	}

	public void setBaseCode(String baseCode) {
		this.baseCode = baseCode == null ? null : baseCode.trim();
	}

	public Long getBdpOrgId() {
		return bdpOrgId;
	}

	public void setBdpOrgId(Long bdpOrgId) {
		this.bdpOrgId = bdpOrgId;
	}
	
}