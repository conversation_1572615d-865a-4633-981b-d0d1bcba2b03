package com.hys.zyy.manage.model;

/**
 * 标题：学员手册标准
 * 作者：ccj
 * 描述：
 * 说明:
 */
public class ZyyOrgBaseHandBook extends ZyyBaseObject {
	/**
	 * 机构学科学员手册
	 */
	private static final long serialVersionUID = -5859051122782168228L;
	private Long id; 			// ID
	private Long zyyOrgId; 		// 组织机构ID
	private Long zyyBaseId; 	// 标准学科ID
	private Long zyyHandBookId; // 学员手册ID
	private Long status; 		// 状态
	private Long manualStdId; 	// 手册标准ID

	public ZyyOrgBaseHandBook() {
		super();
	}

	public ZyyOrgBaseHandBook(Long manualStdId) {
		super();
		this.manualStdId = manualStdId;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getZyyOrgId() {
		return zyyOrgId;
	}

	public void setZyyOrgId(Long zyyOrgId) {
		this.zyyOrgId = zyyOrgId;
	}

	public Long getZyyBaseId() {
		return zyyBaseId;
	}

	public void setZyyBaseId(Long zyyBaseId) {
		this.zyyBaseId = zyyBaseId;
	}

	public Long getZyyHandBookId() {
		return zyyHandBookId;
	}

	public void setZyyHandBookId(Long zyyHandBookId) {
		this.zyyHandBookId = zyyHandBookId;
	}

	public Long getStatus() {
		return status;
	}

	public void setStatus(Long status) {
		this.status = status;
	}

	public Long getManualStdId() {
		return manualStdId;
	}

	public void setManualStdId(Long manualStdId) {
		this.manualStdId = manualStdId;
	}

}