package com.hys.zyy.manage.model;

import java.util.Date;
import java.util.List;

/**
 * 
 * 标题：学员手册标准
 * 
 * 作者：ccj
 * 
 * 描述：
 * 
 * 说明:
 */
public class ZyyOrgDeptHandBook extends ZyyBaseObject {

	/**
	 * 机构学科科室学员手册
	 */
	private static final long serialVersionUID = -5859051122782168228L;
	private Long id;//ID
	private Long zyyOrgId;         //组织机构ID
	private Long zyyDeptId;         //标准科室ID
	private Long zyyHandBookId;         //学员手册ID
	private Long status;         //状态
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getZyyOrgId() {
		return zyyOrgId;
	}
	public void setZyyOrgId(Long zyyOrgId) {
		this.zyyOrgId = zyyOrgId;
	}
	
	public Long getZyyHandBookId() {
		return zyyHandBookId;
	}
	public void setZyyHandBookId(Long zyyHandBookId) {
		this.zyyHandBookId = zyyHandBookId;
	}
	public Long getStatus() {
		return status;
	}
	public void setStatus(Long status) {
		this.status = status;
	}
	public Long getZyyDeptId() {
		return zyyDeptId;
	}
	public void setZyyDeptId(Long zyyDeptId) {
		this.zyyDeptId = zyyDeptId;
	}
	



}
