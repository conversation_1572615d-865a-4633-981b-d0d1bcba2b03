package com.hys.zyy.manage.model;

import com.hys.zyy.manage.util.BaseModel;

public class ZyyOrgQuery extends BaseModel {

	private static final long serialVersionUID = 1L;
	/**
	 * 组织机构标识
	 */
	private Integer orgTypeFlag;
	/**
	 * 父组织机构ID
	 */
	private Long parentOrgId;
	/**
	 * 医院属性 1.中医 2.西医 3.中西医 省厅管理机构增加( 4.中医+中西医 5.西医+中西医 6. 全部(中医+西医+中西医) )
	 */
	private Integer hospType;
	/**
	 * 机构名称
	 */
	private String orgName;
	/**
	 * SQL排序
	 */
	private String orderByDesc;

	public Integer getOrgTypeFlag() {
		return orgTypeFlag;
	}

	public void setOrgTypeFlag(Integer orgTypeFlag) {
		this.orgTypeFlag = orgTypeFlag;
	}

	public Long getParentOrgId() {
		return parentOrgId;
	}

	public void setParentOrgId(Long parentOrgId) {
		this.parentOrgId = parentOrgId;
	}

	public Integer getHospType() {
		return hospType;
	}

	public void setHospType(Integer hospType) {
		this.hospType = hospType;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName == null ? null : orgName.trim();
	}

	public String getOrderByDesc() {
		return orderByDesc;
	}

	public void setOrderByDesc(String orderByDesc) {
		this.orderByDesc = orderByDesc == null ? null : orderByDesc.trim();
	}
	
}