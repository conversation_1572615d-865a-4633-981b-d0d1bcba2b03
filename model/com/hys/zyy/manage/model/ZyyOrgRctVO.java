package com.hys.zyy.manage.model;

/**
 * Em_Org_Rct 视图的数据封装类
 * <AUTHOR>
 * @date 2019-11-8上午11:49:57
 */
public class ZyyOrgRctVO extends ZyyBaseObject {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1657510492756562308L;
	
	private String id;
	/**
     * 机构名称
     */
    private String orgName;

    /**
     * 父机构ID
     */
    private String parentOrgId;
    
    /**
     * 父机构类型
     */
    private Integer parentOrgType;

    /**
     * 机构属性编码
     */
    private String orgCode;

    /**
     * 机构类型
     */
    private Integer orgType;
    
    /**
     * 机构归属ID
     */
    private String orgId;
    
    /**
     * 机构排列顺序
     */
    private Integer orgSeq;
    
    /**
     * 状态
     */
    private Integer status;

    /**
     * 标记类型为  1:机构    2:科室   3:基地
     */
    private Integer markType;
    
    /**
     * 所属机构名称
     */
    private String parentOrgName;
    
    //是否已经选择
    private Integer checkStatus;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public String getParentOrgId() {
		return parentOrgId;
	}

	public void setParentOrgId(String parentOrgId) {
		this.parentOrgId = parentOrgId;
	}

	public Integer getParentOrgType() {
		return parentOrgType;
	}

	public void setParentOrgType(Integer parentOrgType) {
		this.parentOrgType = parentOrgType;
	}

	public String getOrgCode() {
		return orgCode;
	}

	public void setOrgCode(String orgCode) {
		this.orgCode = orgCode;
	}

	public Integer getOrgType() {
		return orgType;
	}

	public void setOrgType(Integer orgType) {
		this.orgType = orgType;
	}

	public String getOrgId() {
		return orgId;
	}

	public void setOrgId(String orgId) {
		this.orgId = orgId;
	}

	public Integer getOrgSeq() {
		return orgSeq;
	}

	public void setOrgSeq(Integer orgSeq) {
		this.orgSeq = orgSeq;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Integer getMarkType() {
		return markType;
	}

	public void setMarkType(Integer markType) {
		this.markType = markType;
	}

	public String getParentOrgName() {
		return parentOrgName;
	}

	public void setParentOrgName(String parentOrgName) {
		this.parentOrgName = parentOrgName;
	}

	public Integer getCheckStatus() {
		return checkStatus;
	}

	public void setCheckStatus(Integer checkStatus) {
		this.checkStatus = checkStatus;
	}
}
