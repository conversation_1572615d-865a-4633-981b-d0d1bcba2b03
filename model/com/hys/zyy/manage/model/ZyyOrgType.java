package com.hys.zyy.manage.model;

import java.util.Date;
import java.util.List;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：张伟清 2010-03-16
 * 
 * 描述：组织机构类别
 * 
 * 说明:
 */
public class ZyyOrgType extends ZyyBaseObject {

	private static final long serialVersionUID = -3289896446983016408L;

	/**
	 * 主键ID
	 */
	private Long id ;
	
	/**
	 * 组织机构ID
	 */
	private Long orgId ;
	
	/**
	 * 组织机构级别
	 */
	private Integer orgLevel ;
	
	/**
	 * 机构类别名称
	 */
	private String orgTypeName ;
	
	/**
	 * 最后更新时间
	 */
	private Date lastUpdateDate;
	
	/**
	 * 
	 * 状态 1正常，-1删除
	 * 
	 * */
	private Integer status;
	
	/**
	 * 
	 * 对应的级别下的医院 
	 * 
	 */
	private List<ZyyOrgVO> listZyyOrg;

	public List<ZyyOrgVO> getListZyyOrg() {
		return listZyyOrg;
	}

	public void setListZyyOrg(List<ZyyOrgVO> listZyyOrg) {
		this.listZyyOrg = listZyyOrg;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getOrgId() {
		return orgId;
	}

	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}

	public Integer getOrgLevel() {
		return orgLevel;
	}

	public void setOrgLevel(Integer orgLevel) {
		this.orgLevel = orgLevel;
	}

	public String getOrgTypeName() {
		return orgTypeName;
	}

	public void setOrgTypeName(String orgTypeName) {
		this.orgTypeName = orgTypeName;
	}
	
	public Date getLastUpdateDate() {
		return lastUpdateDate;
	}

	public void setLastUpdateDate(Date lastUpdateDate) {
		this.lastUpdateDate = lastUpdateDate;
	}
}
