package com.hys.zyy.manage.model;

import java.util.HashMap;
import java.util.List;

import com.hys.security.model.Resource;
import com.hys.zyy.manage.query.ZyyRecruitModifyDetailQuery;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：张伟清 2010-03-19
 * 
 * 描述：组织机构 VO
 * 
 * 说明:
 */
public class ZyyOrgVO extends ZyyOrg {

	private static final long serialVersionUID = -8660121925262165774L;

	/**
	 * 组织机构级别
	 */
	private Integer orgLevel;

	/**
	 * 机构类别名称
	 */
	private String orgTypeName;
	
	/**
	 * 志愿下学科数量 最大填报学科数量3个
	 */
	private Integer stageRecruit ;

	/**
	 * 委托培训小计
	 */
	private Integer consignTotal;

	/**
	 * 自主培训小计
	 */
	private Integer libertyTotal;
	
	/**
	 * 总计
	 */
	private Integer planTotalNum;
	
	/**
	 * 已招录总量
	 */
	private Integer recruitTotal;

	/**
	 * 旧的父组织机构ID
	 */
	private Long oldParentOrgId;

	/**
	 * 已选的联合医院id
	 */
	private String relationIdList;

	/**
	 * 机构缩写
	 */
	private String userTypeAbbr;

	/**
	 * 角色id
	 */
	private Long roleId;
	
	/**
	 * @desc 用户角色ID，区分账户角色ID
	 */
	private Long baseRoleId;

	/**
	 * 角色名称
	 */
	private String roleName;

	/**
	 * 用户类别
	 */
	private Integer zyyUserType;
	
	/**
	 * 年度ID
	 */
	private Long yearId ;
	
	/**
	 * 各基地总计
	 * @return
	 */
	private Integer allNum;

	/**
	 * 类别 1.单位人 2.社会人
	 */
	private Integer stageType ;
	
	/**
	 * 第几志愿
	 */
	private Integer will ;
	
	/**
	 * 各级区域信息
	 */
	private ZyyRegion region ;
	
	/**
	 * 组织机构列表
	 */
	private List<ZyyOrgVO> zyyOrgVOList;
	
	/**
	 * 基地列表
	 */
	private List<ZyyBaseVO> zyyBaseStdList;
	
	/**
	 * 资源列表
	 */
	private List<Resource> resourceList;
	
	/**
	 * 志愿信息
	 */
	private List<ZyyRecruitResidencyWillVO> willList;

	/**
	 * 基地扩展信息表
	 */
	private List<ZyyRecruitBaseExtendVO> baseExtendList;
	
	/**
	 * 计划招生人数详细
	 */
	private List<ZyyRecruitModifyDetail> detailList;

	/**
	 * 医院修改计划招生人数详细
	 */
	private List<ZyyRecruitModifyDetailQuery> detailListQuery;
	
	/**
	 * 医院计划招生人数
	 */
	private List<ZyyRecruitModify> modifyList ;
	
	/**
	 * 机构功能
	 */
	private String resourcesString;
	
	/**
	 * 医院基地统计列表
	 */
	List<ZyyRecruitStatis> baseStatis ;
	
	/**
	 * 阶段ID
	 */
	private Long stageId;
	/**
	 * 签约单位属性
	 */
	private String hospPro;
	/**
	 * 签约单位
	 */
	private String hospCon;
	/**
	 * 报名招录考试的成绩
	 */
	private Float score;
	
	private Long zyyBaseId;
	private String zyyBaseStdName;
	/**
	 * BDP机构等级编码
	 */
	private String bdpOrgTypeCode;
	
	public ZyyOrgVO() {
		super();
	}

	public ZyyOrgVO(Integer orgTypeFlag) {
		super(orgTypeFlag);
	}

	public ZyyOrgVO(Long parentOrgId, Integer orgTypeFlag) {
		super(parentOrgId, orgTypeFlag);
	}

	public Float getScore() {
		return score;
	}

	public void setScore(Float score) {
		this.score = score;
	}

	private HashMap<String,Integer> signUpNumber; 
	

	private HashMap<String,Integer> admitNumber; 
	
	
	public String getHospPro() {
		return hospPro;
	}

	public void setHospPro(String hospPro) {
		this.hospPro = hospPro;
	}

	public String getHospCon() {
		return hospCon;
	}

	public void setHospCon(String hospCon) {
		this.hospCon = hospCon;
	}

	public String getResourcesString() {
		return resourcesString;
	}

	public void setResourcesString(String resourcesString) {
		this.resourcesString = resourcesString;
	}

	public Integer getOrgLevel() {
		return orgLevel;
	}

	public void setOrgLevel(Integer orgLevel) {
		this.orgLevel = orgLevel;
	}

	public String getOrgTypeName() {
		return orgTypeName;
	}

	public void setOrgTypeName(String orgTypeName) {
		this.orgTypeName = orgTypeName;
	}

	public Integer getStageRecruit() {
		return stageRecruit;
	}

	public void setStageRecruit(Integer stageRecruit) {
		this.stageRecruit = stageRecruit;
	}

	public Integer getConsignTotal() {
		return consignTotal;
	}

	public void setConsignTotal(Integer consignTotal) {
		this.consignTotal = consignTotal;
	}

	public Integer getLibertyTotal() {
		return libertyTotal;
	}

	public Long getBaseRoleId() {
		return baseRoleId;
	}

	public void setBaseRoleId(Long baseRoleId) {
		this.baseRoleId = baseRoleId;
	}

	public void setLibertyTotal(Integer libertyTotal) {
		this.libertyTotal = libertyTotal;
	}

	public Integer getPlanTotalNum() {
		return planTotalNum;
	}

	public void setPlanTotalNum(Integer planTotalNum) {
		this.planTotalNum = planTotalNum;
	}

	public Integer getRecruitTotal() {
		return recruitTotal;
	}

	public void setRecruitTotal(Integer recruitTotal) {
		this.recruitTotal = recruitTotal;
	}

	public Long getOldParentOrgId() {
		return oldParentOrgId;
	}

	public void setOldParentOrgId(Long oldParentOrgId) {
		this.oldParentOrgId = oldParentOrgId;
	}

	public String getRelationIdList() {
		return relationIdList;
	}

	public void setRelationIdList(String relationIdList) {
		this.relationIdList = relationIdList;
	}

	public String getUserTypeAbbr() {
		return userTypeAbbr;
	}

	public void setUserTypeAbbr(String userTypeAbbr) {
		this.userTypeAbbr = userTypeAbbr;
	}

	public Long getRoleId() {
		return roleId;
	}

	public void setRoleId(Long roleId) {
		this.roleId = roleId;
	}

	public String getRoleName() {
		return roleName;
	}

	public void setRoleName(String roleName) {
		this.roleName = roleName;
	}

	public Integer getZyyUserType() {
		return zyyUserType;
	}

	public void setZyyUserType(Integer zyyUserType) {
		this.zyyUserType = zyyUserType;
	}

	public Long getYearId() {
		return yearId;
	}

	public void setYearId(Long yearId) {
		this.yearId = yearId;
	}

	public Integer getAllNum() {
		return allNum;
	}

	public void setAllNum(Integer allNum) {
		this.allNum = allNum;
	}

	public ZyyRegion getRegion() {
		return region;
	}

	public void setRegion(ZyyRegion region) {
		this.region = region;
	}

	public List<ZyyOrgVO> getZyyOrgVOList() {
		return zyyOrgVOList;
	}

	public void setZyyOrgVOList(List<ZyyOrgVO> zyyOrgVOList) {
		this.zyyOrgVOList = zyyOrgVOList;
	}

	public List<ZyyBaseVO> getZyyBaseStdList() {
		return zyyBaseStdList;
	}

	public void setZyyBaseStdList(List<ZyyBaseVO> zyyBaseStdList) {
		this.zyyBaseStdList = zyyBaseStdList;
	}

	public List<Resource> getResourceList() {
		return resourceList;
	}

	public void setResourceList(List<Resource> resourceList) {
		this.resourceList = resourceList;
	}

	public List<ZyyRecruitResidencyWillVO> getWillList() {
		return willList;
	}

	public void setWillList(List<ZyyRecruitResidencyWillVO> willList) {
		this.willList = willList;
	}

	public List<ZyyRecruitBaseExtendVO> getBaseExtendList() {
		return baseExtendList;
	}

	public void setBaseExtendList(List<ZyyRecruitBaseExtendVO> baseExtendList) {
		this.baseExtendList = baseExtendList;
	}

	public List<ZyyRecruitModifyDetail> getDetailList() {
		return detailList;
	}

	public void setDetailList(List<ZyyRecruitModifyDetail> detailList) {
		this.detailList = detailList;
	}

	public List<ZyyRecruitModifyDetailQuery> getDetailListQuery() {
		return detailListQuery;
	}

	public void setDetailListQuery(List<ZyyRecruitModifyDetailQuery> detailListQuery) {
		this.detailListQuery = detailListQuery;
	}

	public List<ZyyRecruitModify> getModifyList() {
		return modifyList;
	}

	public void setModifyList(List<ZyyRecruitModify> modifyList) {
		this.modifyList = modifyList;
	}

	public Integer getStageType() {
		return stageType;
	}

	public void setStageType(Integer stageType) {
		this.stageType = stageType;
	}

	public Integer getWill() {
		return will;
	}

	public void setWill(Integer will) {
		this.will = will;
	}

	public List<ZyyRecruitStatis> getBaseStatis() {
		return baseStatis;
	}

	public void setBaseStatis(List<ZyyRecruitStatis> baseStatis) {
		this.baseStatis = baseStatis;
	}

	public Long getStageId() {
		return stageId;
	}

	public void setStageId(Long stageId) {
		this.stageId = stageId;
	}

	public HashMap<String,Integer> getSignUpNumber() {
		return signUpNumber;
	}

	public void setSignUpNumber(HashMap<String,Integer> signUpNumber) {
		this.signUpNumber = signUpNumber;
	}

	public HashMap<String,Integer> getAdmitNumber() {
		return admitNumber;
	}

	public void setAdmitNumber(HashMap<String,Integer> admitNumber) {
		this.admitNumber = admitNumber;
	}

	public Long getZyyBaseId() {
		return zyyBaseId;
	}

	public void setZyyBaseId(Long zyyBaseId) {
		this.zyyBaseId = zyyBaseId;
	}

	public String getZyyBaseStdName() {
		return zyyBaseStdName;
	}

	public void setZyyBaseStdName(String zyyBaseStdName) {
		this.zyyBaseStdName = zyyBaseStdName;
	}

	public String getBdpOrgTypeCode() {
		return bdpOrgTypeCode;
	}

	public void setBdpOrgTypeCode(String bdpOrgTypeCode) {
		this.bdpOrgTypeCode = bdpOrgTypeCode == null ? null : bdpOrgTypeCode.trim();
	}
	
}