package com.hys.zyy.manage.model;

import java.io.Serializable;
import java.util.Date;

/**
 * 手册标准和医院培训年级对应关系
 * <AUTHOR>
 */
public class ZyyOrgYearManualStd implements Serializable {
	private static final long serialVersionUID = 1L;
	/*
	 * ID
	 */
	private Long id;
	/*
	 * 医院ID
	 */
	private Long hospitalId;
	/*
	 * 手册标准ID
	 */
	private Long manualStdId;
	/*
	 * 培训年级
	 */
	private Long recruitYearId;
	/*
	 * 状态（1=有效；-1=失效）
	 */
	private Integer state;

	private Date createTime;
	private Date updateTime;

	public ZyyOrgYearManualStd() {
		super();
	}

	public ZyyOrgYearManualStd(Long hospitalId) {
		super();
		this.hospitalId = hospitalId;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getHospitalId() {
		return hospitalId;
	}

	public void setHospitalId(Long hospitalId) {
		this.hospitalId = hospitalId;
	}

	public Long getManualStdId() {
		return manualStdId;
	}

	public void setManualStdId(Long manualStdId) {
		this.manualStdId = manualStdId;
	}

	public Long getRecruitYearId() {
		return recruitYearId;
	}

	public void setRecruitYearId(Long recruitYearId) {
		this.recruitYearId = recruitYearId;
	}

	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ZyyOrgYearManualStd other = (ZyyOrgYearManualStd) obj;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		return true;
	}

	@Override
	public String toString() {
		return "ZyyOrgYearManualStd [id=" + id + ", hospitalId=" + hospitalId + ", manualStdId=" + manualStdId
				+ ", recruitYearId=" + recruitYearId + ", state=" + state + ", createTime=" + createTime
				+ ", updateTime=" + updateTime + "]";
	}

}