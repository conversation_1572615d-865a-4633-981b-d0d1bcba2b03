package com.hys.zyy.manage.model;

import java.io.Serializable;
import java.util.Date;

/**
 * 出科考试审核设置
 * <AUTHOR>
 */
public class ZyyOutDeptExamAuditSet implements Serializable {
	private static final long serialVersionUID = 1L;
	/*
	 * ID
	 */
	private String id;
	/*
	 * 医院ID
	 */
	private Long hospitalId;
	/*
	 * 是否需要审核（0=否；1=是）
	 */
	private Integer requiredAudit;
	/*
	 * 审核者（5=培训基地；7=专业基地）
	 */
	private Integer auditUserType;
	/*
	 * 创建者ID
	 */
	private Long createUserId;

	private Date createTime;
	/*
	 * 修改者ID
	 */
	private Long updateUserId;

	private Date updateTime;

	public ZyyOutDeptExamAuditSet() {
		super();
	}

	public ZyyOutDeptExamAuditSet(Integer requiredAudit, Integer auditUserType) {
		super();
		this.requiredAudit = requiredAudit;
		this.auditUserType = auditUserType;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id == null ? null : id.trim();
	}

	public Long getHospitalId() {
		return hospitalId;
	}

	public void setHospitalId(Long hospitalId) {
		this.hospitalId = hospitalId;
	}

	public Integer getRequiredAudit() {
		return requiredAudit;
	}

	public void setRequiredAudit(Integer requiredAudit) {
		this.requiredAudit = requiredAudit;
	}

	public Integer getAuditUserType() {
		return auditUserType;
	}

	public void setAuditUserType(Integer auditUserType) {
		this.auditUserType = auditUserType;
	}

	public Long getCreateUserId() {
		return createUserId;
	}

	public void setCreateUserId(Long createUserId) {
		this.createUserId = createUserId;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Long getUpdateUserId() {
		return updateUserId;
	}

	public void setUpdateUserId(Long updateUserId) {
		this.updateUserId = updateUserId;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ZyyOutDeptExamAuditSet other = (ZyyOutDeptExamAuditSet) obj;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		return true;
	}

	@Override
	public String toString() {
		return "ZyyOutDeptExamAuditSet [id=" + id + ", hospitalId="
				+ hospitalId + ", requiredAudit=" + requiredAudit
				+ ", auditUserType=" + auditUserType + ", createUserId="
				+ createUserId + ", createTime=" + createTime
				+ ", updateUserId=" + updateUserId + ", updateTime="
				+ updateTime + "]";
	}

}
