package com.hys.zyy.manage.model;

import java.io.Serializable;
import java.util.Date;

/**
 * 出科考试审核流程记录
 * <AUTHOR>
 */
public class ZyyOutDeptExamHistory implements Serializable {
	private static final long serialVersionUID = 1L;
	/*
	 * 考试ID
	 */
	private Long zyyExamId;
	/*
	 * 是否需要审核（0=否；1=是）
	 */
	private Integer requiredAudit;
	/*
	 * 审核者（5=培训基地；7=专业基地）
	 */
	private Integer auditUserType;

	private Date createTime;
	private Date updateTime;

	public ZyyOutDeptExamHistory() {
		super();
	}

	public ZyyOutDeptExamHistory(Long zyyExamId) {
		super();
		this.zyyExamId = zyyExamId;
	}

	public Long getZyyExamId() {
		return zyyExamId;
	}

	public void setZyyExamId(Long zyyExamId) {
		this.zyyExamId = zyyExamId;
	}

	public Integer getRequiredAudit() {
		return requiredAudit;
	}

	public void setRequiredAudit(Integer requiredAudit) {
		this.requiredAudit = requiredAudit;
	}

	public Integer getAuditUserType() {
		return auditUserType;
	}

	public void setAuditUserType(Integer auditUserType) {
		this.auditUserType = auditUserType;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	@Override
	public String toString() {
		return "ZyyOutDeptExamHistory [zyyExamId=" + zyyExamId
				+ ", requiredAudit=" + requiredAudit + ", auditUserType="
				+ auditUserType + ", createTime=" + createTime
				+ ", updateTime=" + updateTime + "]";
	}

}