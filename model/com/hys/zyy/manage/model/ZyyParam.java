package com.hys.zyy.manage.model;

import java.io.Serializable;
import java.util.List;

/**
 * 住院医参数模型
 * <AUTHOR>
 */
public class ZyyParam implements Serializable {
	private static final long serialVersionUID = 1L;
	/*
	 * 签名
	 */
	private String sign;
	/*
	 * 时间戳
	 */
	private String lon;
	/*
	 * 人员推送记录
	 */
	private List<ZyyUserInfo> records;

	public ZyyParam() {
		super();
	}

	public String getSign() {
		return sign;
	}

	public void setSign(String sign) {
		this.sign = sign;
	}

	public String getLon() {
		return lon;
	}

	public void setLon(String lon) {
		this.lon = lon;
	}

	public List<ZyyUserInfo> getRecords() {
		return records;
	}

	public void setRecords(List<ZyyUserInfo> records) {
		this.records = records;
	}

	@Override
	public String toString() {
		return "ZyyParam [sign=" + sign + ", lon=" + lon + ", records=" + records + "]";
	}

}