package com.hys.zyy.manage.model;

import java.util.Date;

/**
 * 播放记录
 * <AUTHOR>
 */
public class ZyyPlayRecordVo {

	private Long playRecordId;

	private Long courseId;

	private Long coursewareId;

	private Long zyyUserId;

	// 视频播放状态 0：未开始 1：进行中 2：已结束
	private Integer playStatue;

	private Date createTime;

	private Date playTime;

	// 1公共科目 2 专业课
	private Integer coursewareType;
	/*
	 * 专业课已完成学时数
	 */
	private Integer professionalCompleteHoursNum;
	/*
	 * 专业课待完成学时数
	 */
	private Integer professionalWaitHoursNum;
	/*
	 * 公共课已完成学时数
	 */
	private Integer publicCompleteHoursNum;
	/*
	 * 公共课待完成学时数
	 */
	private Integer publicWaitHoursNum;
	/*
	 * 是否达标
	 */
	private String isComplete;
	
	public ZyyPlayRecordVo() {
	}
	
	public ZyyPlayRecordVo(Integer professionalWaitHoursNum, Integer publicWaitHoursNum, String isComplete) {
		super();
		this.professionalWaitHoursNum = professionalWaitHoursNum;
		this.publicWaitHoursNum = publicWaitHoursNum;
		this.professionalCompleteHoursNum = 0;
		this.publicCompleteHoursNum = 0;
		this.isComplete = isComplete;
	}

	public Integer getCoursewareType() {
		return coursewareType;
	}

	public void setCoursewareType(Integer coursewareType) {
		this.coursewareType = coursewareType;
	}

	public Long getPlayRecordId() {
		return playRecordId;
	}

	public void setPlayRecordId(Long playRecordId) {
		this.playRecordId = playRecordId;
	}

	public Long getCourseId() {
		return courseId;
	}

	public void setCourseId(Long courseId) {
		this.courseId = courseId;
	}

	public Long getCoursewareId() {
		return coursewareId;
	}

	public void setCoursewareId(Long coursewareId) {
		this.coursewareId = coursewareId;
	}

	public Long getZyyUserId() {
		return zyyUserId;
	}

	public void setZyyUserId(Long zyyUserId) {
		this.zyyUserId = zyyUserId;
	}

	public Integer getPlayStatue() {
		return playStatue;
	}

	public void setPlayStatue(Integer playStatue) {
		this.playStatue = playStatue;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getPlayTime() {
		return playTime;
	}

	public void setPlayTime(Date playTime) {
		this.playTime = playTime;
	}

	public Integer getProfessionalCompleteHoursNum() {
		return professionalCompleteHoursNum;
	}

	public void setProfessionalCompleteHoursNum(Integer professionalCompleteHoursNum) {
		this.professionalCompleteHoursNum = professionalCompleteHoursNum;
	}

	public Integer getProfessionalWaitHoursNum() {
		return professionalWaitHoursNum;
	}

	public void setProfessionalWaitHoursNum(Integer professionalWaitHoursNum) {
		this.professionalWaitHoursNum = professionalWaitHoursNum;
	}

	public Integer getPublicCompleteHoursNum() {
		return publicCompleteHoursNum;
	}

	public void setPublicCompleteHoursNum(Integer publicCompleteHoursNum) {
		this.publicCompleteHoursNum = publicCompleteHoursNum;
	}

	public Integer getPublicWaitHoursNum() {
		return publicWaitHoursNum;
	}

	public void setPublicWaitHoursNum(Integer publicWaitHoursNum) {
		this.publicWaitHoursNum = publicWaitHoursNum;
	}

	public String getIsComplete() {
		return isComplete;
	}

	public void setIsComplete(String isComplete) {
		this.isComplete = isComplete;
	}
	
}