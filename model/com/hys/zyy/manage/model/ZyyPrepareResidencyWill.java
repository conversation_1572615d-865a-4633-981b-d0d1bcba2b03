package com.hys.zyy.manage.model;

import java.io.Serializable;
import java.util.Date;

import com.hys.zyy.manage.util.BaseModel;

/**
 * 预报名志愿表
 */
public class ZyyPrepareResidencyWill extends BaseModel implements Serializable {
	private static final long serialVersionUID = 1L;
	/*
	 * ID
	 */
	private String id;
	/*
	 * 学员ID
	 */
	private Long residencyId;
	/*
	 * 招录年度
	 */
	private Long zyyRecruitYearId;
	/*
	 * 意向培训医院ID
	 */
	private Long hospitalId;
	/*
	 * 第1志愿意向专业ID
	 */
	private Long firstBaseId;
	/*
	 * 第二志愿意向专业ID
	 */
	private Long secondBaseId;

	private Date createTime;
	private Date updateTime;

	public ZyyPrepareResidencyWill() {
		super();
	}

	public ZyyPrepareResidencyWill(Long residencyId, Long zyyRecruitYearId) {
		super();
		this.residencyId = residencyId;
		this.zyyRecruitYearId = zyyRecruitYearId;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public Long getResidencyId() {
		return residencyId;
	}

	public void setResidencyId(Long residencyId) {
		this.residencyId = residencyId;
	}

	public Long getZyyRecruitYearId() {
		return zyyRecruitYearId;
	}

	public void setZyyRecruitYearId(Long zyyRecruitYearId) {
		this.zyyRecruitYearId = zyyRecruitYearId;
	}

	public Long getHospitalId() {
		return hospitalId;
	}

	public void setHospitalId(Long hospitalId) {
		this.hospitalId = hospitalId;
	}

	public Long getFirstBaseId() {
		return firstBaseId;
	}

	public void setFirstBaseId(Long firstBaseId) {
		this.firstBaseId = firstBaseId;
	}

	public Long getSecondBaseId() {
		return secondBaseId;
	}

	public void setSecondBaseId(Long secondBaseId) {
		this.secondBaseId = secondBaseId;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ZyyPrepareResidencyWill other = (ZyyPrepareResidencyWill) obj;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		return true;
	}

	@Override
	public String toString() {
		return "ZyyPrepareResidencyWill [id=" + id + ", residencyId="
				+ residencyId + ", zyyRecruitYearId=" + zyyRecruitYearId
				+ ", hospitalId=" + hospitalId + ", firstBaseId=" + firstBaseId
				+ ", secondBaseId=" + secondBaseId + ", createTime="
				+ createTime + ", updateTime=" + updateTime + "]";
	}

}
