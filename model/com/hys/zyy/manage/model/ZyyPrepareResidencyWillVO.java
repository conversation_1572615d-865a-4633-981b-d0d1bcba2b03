package com.hys.zyy.manage.model;

public class ZyyPrepareResidencyWillVO extends ZyyPrepareResidencyWill {
	
	private Integer hospType;
	
	private Long zyyBaseStdId;
	
	private String realName, sexStr, certificateTypeStr, certificateNo,
			mobilNumber, highestRecordSchoolStr, hospitalName, firstBaseName, secondBaseName, residencyWill;

	public ZyyPrepareResidencyWillVO() {
		super();
	}

	public ZyyPrepareResidencyWillVO(Long residencyId, Long zyyRecruitYearId) {
		super(residencyId, zyyRecruitYearId);
	}

	public String getRealName() {
		return realName;
	}

	public void setRealName(String realName) {
		this.realName = realName == null ? null : realName.trim();
	}

	public String getSexStr() {
		return sexStr;
	}

	public void setSexStr(String sexStr) {
		this.sexStr = sexStr == null ? null : sexStr.trim();
	}

	public String getCertificateTypeStr() {
		return certificateTypeStr;
	}

	public void setCertificateTypeStr(String certificateTypeStr) {
		this.certificateTypeStr = certificateTypeStr == null ? null : certificateTypeStr.trim();
	}

	public String getCertificateNo() {
		return certificateNo;
	}

	public void setCertificateNo(String certificateNo) {
		this.certificateNo = certificateNo == null ? null : certificateNo.trim();
	}

	public String getMobilNumber() {
		return mobilNumber;
	}

	public void setMobilNumber(String mobilNumber) {
		this.mobilNumber = mobilNumber == null ? null : mobilNumber.trim();
	}

	public String getHighestRecordSchoolStr() {
		return highestRecordSchoolStr;
	}

	public void setHighestRecordSchoolStr(String highestRecordSchoolStr) {
		this.highestRecordSchoolStr = highestRecordSchoolStr == null ? null : highestRecordSchoolStr.trim();
	}

	public String getHospitalName() {
		return hospitalName;
	}

	public void setHospitalName(String hospitalName) {
		this.hospitalName = hospitalName == null ? null : hospitalName.trim();
	}

	public String getFirstBaseName() {
		return firstBaseName;
	}

	public void setFirstBaseName(String firstBaseName) {
		this.firstBaseName = firstBaseName == null ? null : firstBaseName.trim();
	}

	public String getSecondBaseName() {
		return secondBaseName;
	}

	public void setSecondBaseName(String secondBaseName) {
		this.secondBaseName = secondBaseName == null ? null : secondBaseName.trim();
	}

	public String getResidencyWill() {
		return residencyWill;
	}

	public void setResidencyWill(String residencyWill) {
		this.residencyWill = residencyWill == null ? null : residencyWill.trim();
	}

	public Long getZyyBaseStdId() {
		return zyyBaseStdId;
	}

	public void setZyyBaseStdId(Long zyyBaseStdId) {
		this.zyyBaseStdId = zyyBaseStdId;
	}

	public Integer getHospType() {
		return hospType;
	}

	public void setHospType(Integer hospType) {
		this.hospType = hospType;
	}

}