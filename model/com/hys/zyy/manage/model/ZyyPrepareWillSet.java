package com.hys.zyy.manage.model;

import java.io.Serializable;
import java.util.Date;

import com.hys.zyy.manage.util.BaseModel;

/**
 * 预报名设置表
 */
public class ZyyPrepareWillSet extends BaseModel implements Serializable {
	private static final long serialVersionUID = 1L;
	/*
	 * ID
	 */
	private String id;
	/*
	 * 省厅ID
	 */
	private Long zyyProvinceId;
	/*
	 * 类别属性（1：中医；2：西医）
	 */
	private Integer hospType;
	/*
	 * 招录年度
	 */
	private Long zyyRecruitYearId;
	/*
	 * 是否开启规培意向调查（-1：失效；0：否；1：是）
	 */
	private Integer isOpen;
	/*
	 * 提示语
	 */
	private String tip;

	private Date createTime;
	private Date updateTime;

	public ZyyPrepareWillSet() {
		super();
	}

	public ZyyPrepareWillSet(Long zyyProvinceId, Integer hospType, Long zyyRecruitYearId, Integer isOpen) {
		super();
		this.zyyProvinceId = zyyProvinceId;
		this.hospType = hospType;
		this.zyyRecruitYearId = zyyRecruitYearId;
		this.isOpen = isOpen;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public Long getZyyProvinceId() {
		return zyyProvinceId;
	}

	public void setZyyProvinceId(Long zyyProvinceId) {
		this.zyyProvinceId = zyyProvinceId;
	}

	public Integer getHospType() {
		return hospType;
	}

	public void setHospType(Integer hospType) {
		this.hospType = hospType;
	}

	public Long getZyyRecruitYearId() {
		return zyyRecruitYearId;
	}

	public void setZyyRecruitYearId(Long zyyRecruitYearId) {
		this.zyyRecruitYearId = zyyRecruitYearId;
	}

	public Integer getIsOpen() {
		return isOpen;
	}

	public void setIsOpen(Integer isOpen) {
		this.isOpen = isOpen;
	}

	public String getTip() {
		return tip;
	}

	public void setTip(String tip) {
		this.tip = tip;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ZyyPrepareWillSet other = (ZyyPrepareWillSet) obj;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		return true;
	}

	@Override
	public String toString() {
		return "ZyyPrepareWillSet [id=" + id + ", zyyProvinceId="
				+ zyyProvinceId + ", hospType=" + hospType
				+ ", zyyRecruitYearId=" + zyyRecruitYearId + ", isOpen="
				+ isOpen + ", tip=" + tip + ", createTime=" + createTime
				+ ", updateTime=" + updateTime + "]";
	}

}
