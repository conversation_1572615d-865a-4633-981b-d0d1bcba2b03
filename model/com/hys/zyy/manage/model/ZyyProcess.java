package com.hys.zyy.manage.model;

import java.util.List;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import com.hys.zyy.manage.util.annotation.Column;
import com.hys.zyy.manage.util.annotation.Id;
import com.hys.zyy.manage.util.annotation.Table;

@Table("ZYY_PROCESS")
public class ZyyProcess extends ZyyBaseObject implements java.io.Serializable {
	
	private static final long serialVersionUID = 5454155825314635342L;
	
	//alias
	public static final String TABLE_ALIAS = "ZyyProcess";
	public static final String ALIAS_ID = "id";
	public static final String ALIAS_PROCESS_NAME = "processName";
	public static final String ALIAS_PROCESS_TYPE = "1 -招生计划制定及审核流程 2 -报名资格审核流程 3 -录取资格审核流程 4 -学员手册审核流程(科室轮转记录) 5 -学员手册审核流程(其他部分) 6 -学员手册审核流程(终审)";
	
	@Id("ZYY_PROCESS_SEQ.nextval")
	@Column("id")
	private java.lang.Long id;
	
	@Column("PROCESS_NAME")
	private java.lang.String processName;
	
	@Column("PROCESS_TYPE")
	private Integer processType;
	
	@Column("IS_CASCADE")
	private Integer isCascade;

	private List<ZyyProcessDetail> details;
	
	public ZyyProcess(){
	}

	public ZyyProcess(
		java.lang.Long id
	){
		this.id = id;
	}

	public void setId(java.lang.Long value) {
		this.id = value;
	}
	
	public java.lang.Long getId() {
		return this.id;
	}
	public void setProcessName(java.lang.String value) {
		this.processName = value;
	}
	
	public java.lang.String getProcessName() {
		return this.processName;
	}
	public void setProcessType(Integer value) {
		this.processType = value;
	}
	
	public Integer getProcessType() {
		return this.processType;
	}
	
	public String toString() {
		return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
			.append("Id",getId())
			.append("ProcessName",getProcessName())
			.append("ProcessType",getProcessType())
			.toString();
	}
	
	public int hashCode() {
		return new HashCodeBuilder()
			.append(getId())
			.toHashCode();
	}
	
	public boolean equals(Object obj) {
		if(obj instanceof ZyyProcess == false) return false;
		if(this == obj) return true;
		ZyyProcess other = (ZyyProcess)obj;
		return new EqualsBuilder()
			.append(getId(),other.getId())
			.isEquals();
	}

	public List<ZyyProcessDetail> getDetails() {
		return details;
	}

	public void setDetails(List<ZyyProcessDetail> details) {
		this.details = details;
	}

	public Integer getIsCascade() {
		return isCascade;
	}

	public void setIsCascade(Integer isCascade) {
		this.isCascade = isCascade;
	}
}

