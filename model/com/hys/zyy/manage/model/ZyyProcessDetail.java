package com.hys.zyy.manage.model;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import com.hys.zyy.manage.util.annotation.Column;
import com.hys.zyy.manage.util.annotation.Id;
import com.hys.zyy.manage.util.annotation.Table;

@Table("ZYY_PROCESS_DETAIL")
public class ZyyProcessDetail extends ZyyBaseObject implements java.io.Serializable {
	
	private static final long serialVersionUID = 5454155825314635342L;
	
	//alias
	public static final String TABLE_ALIAS = "ZyyProcessDetail";
	public static final String ALIAS_ID = "id";
	public static final String ALIAS_PROCESS_ID = "processId";
	public static final String ALIAS_ZYY_ORG_ID = "zyyOrgId";
	public static final String ALIAS_PLANNERS = "0 -无";
	public static final String ALIAS_VERIFIERS = "0 -无";
	public static final String ALIAS_PROCESS_LEVEL = "1 -一级 2 -二级 3 -三级 4 -四级";
	public static final String ALIAS_IS_FINAL = "0 -否 1 -是";
	public static final String ALIAS_RESIDENCY_SOURCE = "1 -单位人 2 -社会人";
	
	@Id("ZYY_PROCESS_DETAIL_SEQ.nextval")
	@Column("id")
	private java.lang.Long id;
	
	@Column("PROCESS_ID")
	private java.lang.Long processId;
	
	@Column("ZYY_ORG_ID")
	private java.lang.Long zyyOrgId;
	
	@Column("PLANNERS")
	private Integer planners;
	
	@Column("VERIFIERS")
	private Integer verifiers;
	
	@Column("PROCESS_LEVEL")
	private Integer processLevel;
	
	@Column("IS_FINAL")
	private Integer isFinal;
	
	@Column("RESIDENCY_SOURCE")
	private Integer residencySource;
	
	@Column("VERIFIERS_ORG")
	private Long verifiersOrg;
	
	private String orgName;
	
	@Column("PLANNERS_ORG")
	private Long plannersOrg;
	
	@Column("HOSP_TYPE")
	private Integer hospType;
	
	@Column("ADJUST_TYPE")
	private Integer adjustType;

	public ZyyProcessDetail(){
	}

	public ZyyProcessDetail(
		java.lang.Long id
	){
		this.id = id;
	}

	public void setId(java.lang.Long value) {
		this.id = value;
	}
	
	public java.lang.Long getId() {
		return this.id;
	}
	public void setProcessId(java.lang.Long value) {
		this.processId = value;
	}
	
	public java.lang.Long getProcessId() {
		return this.processId;
	}
	public void setZyyOrgId(java.lang.Long value) {
		this.zyyOrgId = value;
	}
	
	public java.lang.Long getZyyOrgId() {
		return this.zyyOrgId;
	}
	public void setPlanners(Integer value) {
		this.planners = value;
	}
	
	public Integer getPlanners() {
		return this.planners;
	}
	public void setVerifiers(Integer value) {
		this.verifiers = value;
	}
	
	public Integer getVerifiers() {
		return this.verifiers;
	}
	public void setProcessLevel(Integer value) {
		this.processLevel = value;
	}
	
	public Integer getProcessLevel() {
		return this.processLevel;
	}
	public void setIsFinal(Integer value) {
		this.isFinal = value;
	}
	
	public Integer getIsFinal() {
		return this.isFinal;
	}
	public void setResidencySource(Integer value) {
		this.residencySource = value;
	}
	
	public Integer getResidencySource() {
		return this.residencySource;
	}
	
	private ZyyProcess zyyProcess;
	
	public void setZyyProcess(ZyyProcess zyyProcess){
		this.zyyProcess = zyyProcess;
	}
	
	public ZyyProcess getZyyProcess() {
		return zyyProcess;
	}
	
	private ZyyOrg zyyOrg;
	
	public void setZyyOrg(ZyyOrg zyyOrg){
		this.zyyOrg = zyyOrg;
	}
	
	public ZyyOrg getZyyOrg() {
		return zyyOrg;
	}

	public Long getVerifiersOrg() {
		return verifiersOrg;
	}

	public void setVerifiersOrg(Long verifiersOrg) {
		this.verifiersOrg = verifiersOrg;
	}
	
	public Long getPlannersOrg() {
		return plannersOrg;
	}

	public void setPlannersOrg(Long plannersOrg) {
		this.plannersOrg = plannersOrg;
	}

	public String toString() {
		return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
			.append("Id",getId())
			.append("ProcessId",getProcessId())
			.append("ZyyOrgId",getZyyOrgId())
			.append("Planners",getPlanners())
			.append("Verifiers",getVerifiers())
			.append("ProcessLevel",getProcessLevel())
			.append("IsFinal",getIsFinal())
			.append("ResidencySource",getResidencySource())
			.append("VerifierOrg",getVerifiersOrg())
			.append("PlannersOrg",getPlannersOrg())
			.toString();
	}
	
	public int hashCode() {
		return new HashCodeBuilder()
			.append(getId())
			.toHashCode();
	}
	
	public boolean equals(Object obj) {
		if(obj instanceof ZyyProcessDetail == false) return false;
		if(this == obj) return true;
		ZyyProcessDetail other = (ZyyProcessDetail)obj;
		return new EqualsBuilder()
			.append(getId(),other.getId())
			.isEquals();
	}

	public Integer getHospType() {
		return hospType;
	}

	public void setHospType(Integer hospType) {
		this.hospType = hospType;
	}

	public Integer getAdjustType() {
		return adjustType;
	}

	public void setAdjustType(Integer adjustType) {
		this.adjustType = adjustType;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}
}

