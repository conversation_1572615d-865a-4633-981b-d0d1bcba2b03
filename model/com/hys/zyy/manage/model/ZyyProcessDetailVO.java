package com.hys.zyy.manage.model;

import java.util.List;

public class ZyyProcessDetailVO extends ZyyBaseObject  {
	private static final long serialVersionUID = 6221714165666146789L;
	
	private String title;
	
	private String code;
	
	private String processLevelStr;

	private List<ZyyProcessDetail> listChlid;
	
	private List<ZyyProcessDetail> listParent;

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title == null ? null : title.trim();
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getProcessLevelStr() {
		return processLevelStr;
	}

	public void setProcessLevelStr(String processLevelStr) {
		this.processLevelStr = processLevelStr == null ? null : processLevelStr.trim();
	}

	public List<ZyyProcessDetail> getListChlid() {
		return listChlid;
	}

	public void setListChlid(List<ZyyProcessDetail> listChlid) {
		this.listChlid = listChlid;
	}

	public List<ZyyProcessDetail> getListParent() {
		return listParent;
	}

	public void setListParent(List<ZyyProcessDetail> listParent) {
		this.listParent = listParent;
	}

}
