package com.hys.zyy.manage.model;
/**
 * 省对应的专业
 * <AUTHOR>
 * @date 2019-3-18下午1:49:20
 */
public class ZyyProfProvince extends ZyyBaseObject {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	//省份ID
	private Long provinceId;
	//学历级别
	private Integer graduateLevel;
	//学历类型
	private Integer graduateType;
	//专业ID
	private Long profId;
	//排序标识
	private Integer sortFlag;
	//数据状态
	private Integer dataStatus;
	
	public Long getProvinceId() {
		return provinceId;
	}
	public void setProvinceId(Long provinceId) {
		this.provinceId = provinceId;
	}
	public Integer getGraduateLevel() {
		return graduateLevel;
	}
	public void setGraduateLevel(Integer graduateLevel) {
		this.graduateLevel = graduateLevel;
	}
	public Integer getGraduateType() {
		return graduateType;
	}
	public void setGraduateType(Integer graduateType) {
		this.graduateType = graduateType;
	}
	public Long getProfId() {
		return profId;
	}
	public void setProfId(Long profId) {
		this.profId = profId;
	}
	public Integer getSortFlag() {
		return sortFlag;
	}
	public void setSortFlag(Integer sortFlag) {
		this.sortFlag = sortFlag;
	}
	public Integer getDataStatus() {
		return dataStatus;
	}
	public void setDataStatus(Integer dataStatus) {
		this.dataStatus = dataStatus;
	}
}
