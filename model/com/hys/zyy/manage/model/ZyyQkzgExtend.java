package com.hys.zyy.manage.model;

import java.io.Serializable;
import java.util.Date;

/**
 * 全科转岗生扩展信息表
 */
public class ZyyQkzgExtend extends ZyyUserExtendVO implements Serializable {
	private static final long serialVersionUID = 1L;
	/*
	 * 培训批次（第一批次、第二批次、第三批次）
	 */
	private String trainBatch;
	/*
	 * 学历（1=中专；2=大专；3=本科；4=硕士研究生；5=博士研究生）
	 */
	private Integer education;
	/*
	 * 所属市
	 */
	private String belongCity;
	/*
	 * 单位所属级别（1=基层机构；2=二级机构；3=三级机构；4=其他）
	 */
	private Integer unitGrade;
	/*
	 * 推荐单位
	 */
	private String recommendUnit;
	/*
	 * 专业工作年限
	 */
	private String majorWorkYear;
	/*
	 * 职称
	 */
	private String title;

	private Date createTime;
	private Date updateTime;

	public ZyyQkzgExtend() {
		super();
	}

	public String getTrainBatch() {
		return trainBatch;
	}

	public void setTrainBatch(String trainBatch) {
		this.trainBatch = trainBatch == null ? null : trainBatch.trim();
	}

	public Integer getEducation() {
		return education;
	}

	public void setEducation(Integer education) {
		this.education = education;
	}

	public String getBelongCity() {
		return belongCity;
	}

	public void setBelongCity(String belongCity) {
		this.belongCity = belongCity == null ? null : belongCity.trim();
	}

	public Integer getUnitGrade() {
		return unitGrade;
	}

	public void setUnitGrade(Integer unitGrade) {
		this.unitGrade = unitGrade;
	}

	public String getRecommendUnit() {
		return recommendUnit;
	}

	public void setRecommendUnit(String recommendUnit) {
		this.recommendUnit = recommendUnit == null ? null : recommendUnit.trim();
	}

	public String getMajorWorkYear() {
		return majorWorkYear;
	}

	public void setMajorWorkYear(String majorWorkYear) {
		this.majorWorkYear = majorWorkYear == null ? null : majorWorkYear.trim();
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title == null ? null : title.trim();
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

}