package com.hys.zyy.manage.model;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：张伟清 2012-07-17
 * 
 * 描述：基地扩展信息表
 * 
 * 说明:
 */
public class ZyyRecruitBaseExtend extends ZyyBaseObject{

	private static final long serialVersionUID = 4910279460273525959L;
	
	/**
	 * 主键ID
	 */
	private Long id ;
	
	/**
	 * 阶段ID
	 */
	private Integer stageId;
	
	/**
	 * 基地ID
	 */
	private Long baseId ;
	
	/**
	 * 标准基地ID
	 */
	private Long baseStdId ;
	
	/**
	 * 年度ID
	 */
	private Long yearId ;
	
	/**
	 * 省厅ID
	 */
	private Long orgId ;
	
	/**
	 * 医院ID
	 */
	private Long hospitalId ;
	
	/**
	 * 本科招收人数（提交数据）
	 */
	private Integer undergraduateNum ;
	
	/**
	 * 硕士招收人数（提交数据）
	 */
	private Integer masterNum ;
	
	/**
	 * 博士招收人数（提交数据）
	 */
	private Integer doctorNum ;
	
	/**
	 * 本科招收人数(专业学位研究生结合项目)（提交数据）
	 */
	private Integer undergraduateNumPro ;
	
	/**
	 * 本科招收人数(其他)（提交数据）
	 */
	private Integer undergraduateNumOther ;
	
	/**
	 * 本科招收人数（保存数据）
	 */
	private Integer undergraduateNum2 ;
	
	/**
	 * 硕士招收人数（保存数据）
	 */
	private Integer masterNum2 ;
	
	/**
	 * 博士招收人数（保存数据）
	 */
	private Integer doctorNum2 ;
	
	/**
	 * 本科招收人数(专业学位研究生结合项目)（保存数据）
	 */
	private Integer undergraduateNumPro2 ;
	
	/**
	 * 本科招收人数(其他)（保存数据）
	 */
	private Integer undergraduateNumOther2 ;
	
	/**
	 * 本院委托本科招收人数（提交数据）
	 */
	private Integer insideUndergraduateNum ;
	
	/**
	 * 本院委托硕士招收人数（提交数据）
	 */
	private Integer insideMasterNum ;
	
	/**
	 * 本院委托博士招收人数（提交数据）
	 */
	private Integer insideDoctorNum ;
	
	/**
	 * 本院委托本科招收人数(专业学位研究生结合项目)（提交数据）
	 */
	private Integer insideUndergraduateNumPro ;
	
	/**
	 * 本院委托本科招收人数(其他)（提交数据）
	 */
	private Integer insideUndergraduateNumOther ;
	
	/**
	 * 本院委托本科招收人数（保存数据）
	 */
	private Integer insideUndergraduateNum2 ;
	
	/**
	 * 本院委托硕士招收人数（保存数据）
	 */
	private Integer insideMasterNum2 ;
	
	/**
	 * 本院委托博士招收人数（保存数据）
	 */
	private Integer insideDoctorNum2 ;
	
	/**
	 * 本院委托本科招收人数(专业学位研究生结合项目)（保存数据）
	 */
	private Integer insideUndergraduateNumPro2 ;
	
	/**
	 * 本院委托本科招收人数(其他)（保存数据）
	 */
	private Integer insideUndergraduateNumOthe2 ;
	
	/**
	 * 外院委托本科招收人数（提交数据）
	 */
	private Integer outsideUndergraduateNum ;
	
	/**
	 * 外院委托硕士招收人数（提交数据）
	 */
	private Integer outsideMasterNum ;
	
	/**
	 * 外院委托博士招收人数（提交数据）
	 */
	private Integer outsideDoctorNum ;
	
	/**
	 * 外院委托本科招收人数(专业学位研究生结合项目)（提交数据）
	 */
	private Integer outsideUndergraduateNumPro ;
	
	/**
	 * 外院委托本科招收人数(其他)（提交数据）
	 */
	private Integer outsideUndergraduateNumOth ;
	
	/**
	 * 外院委托本科招收人数（保存数据）
	 */
	private Integer outsideUndergraduateNum2 ;
	
	/**
	 * 外院委托硕士招收人数（保存数据）
	 */
	private Integer outsideMasterNum2 ;
	
	/**
	 * 外院委托博士招收人数（保存数据）
	 */
	private Integer outsideDoctorNum2 ;
	
	/**
	 * 外院委托本科招收人数(专业学位研究生结合项目)（保存数据）
	 */
	private Integer outsideUndergraduateNumPro2 ;
	
	/**
	 * 外院委托本科招收人数(其他)（保存数据）
	 */
	private Integer outsideUndergraduateNumOth2 ;
	
	/**
	 * 是否提交
	 */
	private Integer isSubmit ;
	
	/**
	 * 单位人招收总数
	 * @return
	 */
	private Integer entrustTotal;
	/**
	 * 社会人招收总数
	 */
	private Integer autonomousTotal;
	private Integer autonomousTotal2;
	
	public ZyyRecruitBaseExtend() {
		super();
	}

	public ZyyRecruitBaseExtend(Long id, Integer autonomousTotal) {
		super();
		this.id = id;
		this.autonomousTotal = autonomousTotal;
	}
	
	public ZyyRecruitBaseExtend(Long id, Integer autonomousTotal, Integer autonomousTotal2) {
		super();
		this.id = id;
		this.autonomousTotal = autonomousTotal;
		this.autonomousTotal2 = autonomousTotal2;
	}

	public Integer getEntrustTotal() {
		return entrustTotal;
	}

	public void setEntrustTotal(Integer entrustTotal) {
		this.entrustTotal = entrustTotal;
	}

	public Integer getAutonomousTotal() {
		return autonomousTotal;
	}

	public void setAutonomousTotal(Integer autonomousTotal) {
		this.autonomousTotal = autonomousTotal;
	}
	
	public Integer getAutonomousTotal2() {
		return autonomousTotal2;
	}

	public void setAutonomousTotal2(Integer autonomousTotal2) {
		this.autonomousTotal2 = autonomousTotal2;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getBaseId() {
		return baseId;
	}

	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}

	public Long getBaseStdId() {
		return baseStdId;
	}

	public void setBaseStdId(Long baseStdId) {
		this.baseStdId = baseStdId;
	}

	public Long getYearId() {
		return yearId;
	}

	public void setYearId(Long yearId) {
		this.yearId = yearId;
	}

	public Long getOrgId() {
		return orgId;
	}

	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}

	public Long getHospitalId() {
		return hospitalId;
	}

	public void setHospitalId(Long hospitalId) {
		this.hospitalId = hospitalId;
	}

	public Integer getUndergraduateNum() {
		return undergraduateNum;
	}

	public void setUndergraduateNum(Integer undergraduateNum) {
		this.undergraduateNum = undergraduateNum;
	}

	public Integer getMasterNum() {
		return masterNum;
	}

	public void setMasterNum(Integer masterNum) {
		this.masterNum = masterNum;
	}

	public Integer getDoctorNum() {
		return doctorNum;
	}

	public void setDoctorNum(Integer doctorNum) {
		this.doctorNum = doctorNum;
	}

	public Integer getUndergraduateNumPro() {
		return undergraduateNumPro;
	}

	public void setUndergraduateNumPro(Integer undergraduateNumPro) {
		this.undergraduateNumPro = undergraduateNumPro;
	}

	public Integer getUndergraduateNumOther() {
		return undergraduateNumOther;
	}

	public void setUndergraduateNumOther(Integer undergraduateNumOther) {
		this.undergraduateNumOther = undergraduateNumOther;
	}

	public Integer getUndergraduateNum2() {
		return undergraduateNum2;
	}

	public void setUndergraduateNum2(Integer undergraduateNum2) {
		this.undergraduateNum2 = undergraduateNum2;
	}

	public Integer getMasterNum2() {
		return masterNum2;
	}

	public void setMasterNum2(Integer masterNum2) {
		this.masterNum2 = masterNum2;
	}

	public Integer getDoctorNum2() {
		return doctorNum2;
	}

	public void setDoctorNum2(Integer doctorNum2) {
		this.doctorNum2 = doctorNum2;
	}

	public Integer getUndergraduateNumPro2() {
		return undergraduateNumPro2;
	}

	public void setUndergraduateNumPro2(Integer undergraduateNumPro2) {
		this.undergraduateNumPro2 = undergraduateNumPro2;
	}

	public Integer getUndergraduateNumOther2() {
		return undergraduateNumOther2;
	}

	public void setUndergraduateNumOther2(Integer undergraduateNumOther2) {
		this.undergraduateNumOther2 = undergraduateNumOther2;
	}

	public Integer getInsideUndergraduateNum() {
		return insideUndergraduateNum;
	}

	public void setInsideUndergraduateNum(Integer insideUndergraduateNum) {
		this.insideUndergraduateNum = insideUndergraduateNum;
	}

	public Integer getInsideMasterNum() {
		return insideMasterNum;
	}

	public void setInsideMasterNum(Integer insideMasterNum) {
		this.insideMasterNum = insideMasterNum;
	}

	public Integer getInsideDoctorNum() {
		return insideDoctorNum;
	}

	public void setInsideDoctorNum(Integer insideDoctorNum) {
		this.insideDoctorNum = insideDoctorNum;
	}

	public Integer getInsideUndergraduateNumPro() {
		return insideUndergraduateNumPro;
	}

	public void setInsideUndergraduateNumPro(Integer insideUndergraduateNumPro) {
		this.insideUndergraduateNumPro = insideUndergraduateNumPro;
	}

	public Integer getInsideUndergraduateNumOther() {
		return insideUndergraduateNumOther;
	}

	public void setInsideUndergraduateNumOther(Integer insideUndergraduateNumOther) {
		this.insideUndergraduateNumOther = insideUndergraduateNumOther;
	}

	public Integer getInsideUndergraduateNum2() {
		return insideUndergraduateNum2;
	}

	public void setInsideUndergraduateNum2(Integer insideUndergraduateNum2) {
		this.insideUndergraduateNum2 = insideUndergraduateNum2;
	}

	public Integer getInsideMasterNum2() {
		return insideMasterNum2;
	}

	public void setInsideMasterNum2(Integer insideMasterNum2) {
		this.insideMasterNum2 = insideMasterNum2;
	}

	public Integer getInsideDoctorNum2() {
		return insideDoctorNum2;
	}

	public void setInsideDoctorNum2(Integer insideDoctorNum2) {
		this.insideDoctorNum2 = insideDoctorNum2;
	}

	public Integer getInsideUndergraduateNumPro2() {
		return insideUndergraduateNumPro2;
	}

	public void setInsideUndergraduateNumPro2(Integer insideUndergraduateNumPro2) {
		this.insideUndergraduateNumPro2 = insideUndergraduateNumPro2;
	}

	public Integer getInsideUndergraduateNumOthe2() {
		return insideUndergraduateNumOthe2;
	}

	public void setInsideUndergraduateNumOthe2(Integer insideUndergraduateNumOthe2) {
		this.insideUndergraduateNumOthe2 = insideUndergraduateNumOthe2;
	}

	public Integer getOutsideUndergraduateNum() {
		return outsideUndergraduateNum;
	}

	public void setOutsideUndergraduateNum(Integer outsideUndergraduateNum) {
		this.outsideUndergraduateNum = outsideUndergraduateNum;
	}

	public Integer getOutsideMasterNum() {
		return outsideMasterNum;
	}

	public void setOutsideMasterNum(Integer outsideMasterNum) {
		this.outsideMasterNum = outsideMasterNum;
	}

	public Integer getOutsideDoctorNum() {
		return outsideDoctorNum;
	}

	public void setOutsideDoctorNum(Integer outsideDoctorNum) {
		this.outsideDoctorNum = outsideDoctorNum;
	}

	public Integer getOutsideUndergraduateNumPro() {
		return outsideUndergraduateNumPro;
	}

	public void setOutsideUndergraduateNumPro(Integer outsideUndergraduateNumPro) {
		this.outsideUndergraduateNumPro = outsideUndergraduateNumPro;
	}

	public Integer getOutsideUndergraduateNumOth() {
		return outsideUndergraduateNumOth;
	}

	public void setOutsideUndergraduateNumOth(Integer outsideUndergraduateNumOth) {
		this.outsideUndergraduateNumOth = outsideUndergraduateNumOth;
	}

	public Integer getOutsideUndergraduateNum2() {
		return outsideUndergraduateNum2;
	}

	public void setOutsideUndergraduateNum2(Integer outsideUndergraduateNum2) {
		this.outsideUndergraduateNum2 = outsideUndergraduateNum2;
	}

	public Integer getOutsideMasterNum2() {
		return outsideMasterNum2;
	}

	public void setOutsideMasterNum2(Integer outsideMasterNum2) {
		this.outsideMasterNum2 = outsideMasterNum2;
	}

	public Integer getOutsideDoctorNum2() {
		return outsideDoctorNum2;
	}

	public void setOutsideDoctorNum2(Integer outsideDoctorNum2) {
		this.outsideDoctorNum2 = outsideDoctorNum2;
	}

	public Integer getOutsideUndergraduateNumPro2() {
		return outsideUndergraduateNumPro2;
	}

	public void setOutsideUndergraduateNumPro2(Integer outsideUndergraduateNumPro2) {
		this.outsideUndergraduateNumPro2 = outsideUndergraduateNumPro2;
	}

	public Integer getOutsideUndergraduateNumOth2() {
		return outsideUndergraduateNumOth2;
	}

	public void setOutsideUndergraduateNumOth2(Integer outsideUndergraduateNumOth2) {
		this.outsideUndergraduateNumOth2 = outsideUndergraduateNumOth2;
	}

	public Integer getIsSubmit() {
		return isSubmit;
	}

	public void setIsSubmit(Integer isSubmit) {
		this.isSubmit = isSubmit;
	}

	public Integer getStageId() {
		return stageId;
	}

	public void setStageId(Integer stageId) {
		this.stageId = stageId;
	}
}