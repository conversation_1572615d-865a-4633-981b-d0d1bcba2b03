package com.hys.zyy.manage.model;

import java.util.List;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：张伟清 2012-07-17
 * 
 * 描述：基地扩展信息表
 * 
 * 说明:
 */
public class ZyyRecruitBaseExtendVO extends ZyyRecruitBaseExtend {
	
	private static final long serialVersionUID = -5016187985554584193L;

	/**
	 * 标准基地名称
	 */
	private String baseName;
	
	/**
	 * 标准基地别名
	 */
	private String baseAliasName;
	
	/**
	 * 本科缺额人数
	 */
	private Integer deficitUnder;

	/**
	 * 本科缺额人数(专业学位研究生结合项目)
	 */
	private Integer deficitUnderPro;
	
	/**
	 * 本科缺额人数(其他)
	 */
	private Integer deficitUnderOther;
	
	/**
	 *硕士缺额人数 
	 */
	private Integer deficitMaster;
	
	/**
	 * 博士缺额人数
	 */
	private Integer deficitDoctor;
	
	/**
	 *自主缺额人数(不区分本硕博及本院和外院) 
	 */
	
	private	Integer	deficitAutonomously;
	
	/**
	 * 单位缺额人数(不区分本硕博及本院和外院)
	 */
	private Integer	deficitEntpuset;
	
	/**
	 * 本院本科缺额人数
	 */
	private Integer insideDeficitUnder;
	
	/**
	 * 本院博士缺额人数
	 */
	private Integer insideDeficitDoctor;
	
	/**
	 * 本院硕士缺额人数
	 */
	private Integer insideDeficitMaster;
	
	/**
	 * 外院本科缺额人数
	 */
	private Integer outsideDeficitUnder;
	
	/**
	 * 外院博士缺额人数
	 */
	private Integer outsideDeficitDoctor;
	
	/**
	 * 外院硕士缺额人数
	 */
	private Integer outsideDeficitMaster;
	
	/**
	 * 委托培训小计 单位人
	 */
	private Integer consignTotal;

	/**
	 * 自主培训小计 社会人
	 */
	private Integer libertyTotal;
	
	/**
	 * 基地已招录人数
	 */
	private Integer recruitTotal ;
	
	/**
	 * 提交小计
	 */
	private Integer totalNum;

	/**
	 * @desc 医院名称
	 */
	private String hospName;
	
	/**
	 * @desc 跨出数据行
	 */
	private int rowSpan = 0;
	
	/**
	 * 推荐录取的志愿
	 */
	private List<ZyyRecruitResidencyWillVO> willsRecommend;
	/**
	 * 存在争议的志愿
	 */
	private List<ZyyRecruitResidencyWillVO> willsDisputed;
	
	/**
	 * @desc 查看基地招录计划
	 */
	private List<ZyyRecruitBaseExtendVO> subExtendVoList;
	
	public ZyyRecruitBaseExtendVO() {
		super();
	}

	public ZyyRecruitBaseExtendVO(Long id, Integer autonomousTotal) {
		super(id, autonomousTotal);
	}

	public ZyyRecruitBaseExtendVO(Long id, Integer autonomousTotal, Integer autonomousTotal2) {
		super(id, autonomousTotal, autonomousTotal2);
	}

	public List<ZyyRecruitResidencyWillVO> getWillsRecommend() {
		return willsRecommend;
	}

	public void setWillsRecommend(List<ZyyRecruitResidencyWillVO> willsRecommend) {
		this.willsRecommend = willsRecommend;
	}

	public List<ZyyRecruitResidencyWillVO> getWillsDisputed() {
		return willsDisputed;
	}

	public void setWillsDisputed(List<ZyyRecruitResidencyWillVO> willsDisputed) {
		this.willsDisputed = willsDisputed;
	}

	public Integer getDeficitAutonomously() {
		return deficitAutonomously;
	}

	public void setDeficitAutonomously(Integer deficitAutonomously) {
		this.deficitAutonomously = deficitAutonomously;
	}

	public Integer getDeficitEntpuset() {
		return deficitEntpuset;
	}

	public void setDeficitEntpuset(Integer deficitEntpuset) {
		this.deficitEntpuset = deficitEntpuset;
	}
	
	public String getBaseName() {
		return baseName;
	}

	public void setBaseName(String baseName) {
		this.baseName = baseName;
	}

	public String getBaseAliasName() {
		return baseAliasName;
	}

	public void setBaseAliasName(String baseAliasName) {
		this.baseAliasName = baseAliasName;
	}

	public Integer getDeficitUnder() {
		return deficitUnder;
	}

	public void setDeficitUnder(Integer deficitUnder) {
		this.deficitUnder = deficitUnder;
	}

	public Integer getDeficitUnderPro() {
		return deficitUnderPro;
	}

	public void setDeficitUnderPro(Integer deficitUnderPro) {
		this.deficitUnderPro = deficitUnderPro;
	}

	public Integer getDeficitUnderOther() {
		return deficitUnderOther;
	}

	public void setDeficitUnderOther(Integer deficitUnderOther) {
		this.deficitUnderOther = deficitUnderOther;
	}

	public Integer getDeficitMaster() {
		return deficitMaster;
	}

	public void setDeficitMaster(Integer deficitMaster) {
		this.deficitMaster = deficitMaster;
	}

	public Integer getDeficitDoctor() {
		return deficitDoctor;
	}

	public void setDeficitDoctor(Integer deficitDoctor) {
		this.deficitDoctor = deficitDoctor;
	}

	public Integer getInsideDeficitUnder() {
		return insideDeficitUnder;
	}

	public void setInsideDeficitUnder(Integer insideDeficitUnder) {
		this.insideDeficitUnder = insideDeficitUnder;
	}

	public Integer getInsideDeficitDoctor() {
		return insideDeficitDoctor;
	}

	public void setInsideDeficitDoctor(Integer insideDeficitDoctor) {
		this.insideDeficitDoctor = insideDeficitDoctor;
	}

	public Integer getInsideDeficitMaster() {
		return insideDeficitMaster;
	}

	public void setInsideDeficitMaster(Integer insideDeficitMaster) {
		this.insideDeficitMaster = insideDeficitMaster;
	}

	public Integer getOutsideDeficitUnder() {
		return outsideDeficitUnder;
	}

	public void setOutsideDeficitUnder(Integer outsideDeficitUnder) {
		this.outsideDeficitUnder = outsideDeficitUnder;
	}

	public Integer getOutsideDeficitDoctor() {
		return outsideDeficitDoctor;
	}

	public void setOutsideDeficitDoctor(Integer outsideDeficitDoctor) {
		this.outsideDeficitDoctor = outsideDeficitDoctor;
	}

	public Integer getOutsideDeficitMaster() {
		return outsideDeficitMaster;
	}

	public void setOutsideDeficitMaster(Integer outsideDeficitMaster) {
		this.outsideDeficitMaster = outsideDeficitMaster;
	}

	public Integer getConsignTotal() {
		return consignTotal;
	}

	public void setConsignTotal(Integer consignTotal) {
		this.consignTotal = consignTotal;
	}

	public Integer getLibertyTotal() {
		return libertyTotal;
	}

	public void setLibertyTotal(Integer libertyTotal) {
		this.libertyTotal = libertyTotal;
	}

	public Integer getRecruitTotal() {
		return recruitTotal;
	}

	public void setRecruitTotal(Integer recruitTotal) {
		this.recruitTotal = recruitTotal;
	}

	public Integer getTotalNum() {
		return totalNum;
	}

	public void setTotalNum(Integer totalNum) {
		this.totalNum = totalNum;
	}

	public String getHospName() {
		return hospName;
	}

	public void setHospName(String hospName) {
		this.hospName = hospName;
	}

	public int getRowSpan() {
		return rowSpan;
	}

	public void setRowSpan(int rowSpan) {
		this.rowSpan = rowSpan;
	}

	public List<ZyyRecruitBaseExtendVO> getSubExtendVoList() {
		return subExtendVoList;
	}

	public void setSubExtendVoList(List<ZyyRecruitBaseExtendVO> subExtendVoList) {
		this.subExtendVoList = subExtendVoList;
	}

}