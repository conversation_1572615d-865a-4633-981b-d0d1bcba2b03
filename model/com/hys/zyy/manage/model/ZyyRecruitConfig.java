package com.hys.zyy.manage.model;

import com.hys.zyy.manage.util.annotation.Column;
import com.hys.zyy.manage.util.annotation.Id;
import com.hys.zyy.manage.util.annotation.Table;

@Table("ZYY_RECRUITR_CONFIG")
public class ZyyRecruitConfig extends ZyyBaseObject {
	
	

	/**
	 * 
	 */
	private static final long serialVersionUID = -5445771025027175886L;

	@Id("ZYY_RECRUITR_CONFIG_SEQ.nextval")
	@Column("ID")
	private Long id;
	
	@Column("ORG_ID")
	private Long orgId;
	
	@Column("YEAR_ID")
	private Long yearId;
	
	@Column("HOSP_TYPE")
	private Integer hospType;					// 中西医
	
	@Column("RESIDENCY_SOURCE")
	private Integer residencySource;			// 单位人社会人 1、2
	
	@Column("IS_REG_AUDITS")
	private Integer isRegAudits;				// 逐级报名审核
	
	@Column("IS_ENROLL_AUDITS")
	private Integer isEnrollAudits;				// 逐级招录操作
	
	@Column("IS_ENROLL_APPLY_AUDITS")
	private Integer isEnrollApplyAudits;		// 逐级录取审核
	
	@Column("IS_SWAP_AUDITS")
	private Integer isSwapAudits;				// 逐级调剂
	
	@Column("ALLOW_REPEAT")
	private Integer allowRepeat;				// 是否允许重复报名

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getOrgId() {
		return orgId;
	}

	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}

	public Integer getHospType() {
		return hospType;
	}

	public void setHospType(Integer hospType) {
		this.hospType = hospType;
	}

	public Integer getResidencySource() {
		return residencySource;
	}

	public void setResidencySource(Integer residencySource) {
		this.residencySource = residencySource;
	}

	public Integer getIsRegAudits() {
		return isRegAudits;
	}

	public void setIsRegAudits(Integer isRegAudits) {
		this.isRegAudits = isRegAudits;
	}

	public Integer getIsEnrollAudits() {
		return isEnrollAudits;
	}

	public void setIsEnrollAudits(Integer isEnrollAudits) {
		this.isEnrollAudits = isEnrollAudits;
	}

	public Integer getIsEnrollApplyAudits() {
		return isEnrollApplyAudits;
	}

	public void setIsEnrollApplyAudits(Integer isEnrollApplyAudits) {
		this.isEnrollApplyAudits = isEnrollApplyAudits;
	}

	public Integer getIsSwapAudits() {
		return isSwapAudits;
	}

	public void setIsSwapAudits(Integer isSwapAudits) {
		this.isSwapAudits = isSwapAudits;
	}

	public Integer getAllowRepeat() {
		return allowRepeat;
	}

	public void setAllowRepeat(Integer allowRepeat) {
		this.allowRepeat = allowRepeat;
	}

	public Long getYearId() {
		return yearId;
	}

	public void setYearId(Long yearId) {
		this.yearId = yearId;
	}				
	
}
