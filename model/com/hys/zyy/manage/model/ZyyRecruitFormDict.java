package com.hys.zyy.manage.model;

import java.util.Date;

/**
 * 报名表学历专业字典
 * <AUTHOR>
 *
 */
public class ZyyRecruitFormDict {
	
	//主键ID
	private Long id;
	//编码
	private String code;
	//名称
	private String name;
	//分类编码
	private String categoryCode;
	// 页面
	private String ym;
	
	private Integer sort;
	private Integer status;
	private Date createTime;
	private Date updateTime;
	
	public ZyyRecruitFormDict() {
		super();
	}
	
	public ZyyRecruitFormDict(String categoryCode) {
		super();
		this.categoryCode = categoryCode;
	}

	public ZyyRecruitFormDict(String categoryCode, String ym) {
		super();
		this.categoryCode = categoryCode;
		this.ym = ym;
	}

	public ZyyRecruitFormDict(String categoryCode, String ym, String code) {
		super();
		this.code = code;
		this.categoryCode = categoryCode;
		this.ym = ym;
	}

	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getCategoryCode() {
		return categoryCode;
	}
	public void setCategoryCode(String categoryCode) {
		this.categoryCode = categoryCode;
	}
	public Integer getSort() {
		return sort;
	}
	public void setSort(Integer sort) {
		this.sort = sort;
	}
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	public Date getUpdateTime() {
		return updateTime;
	}
	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
	public String getYm() {
		return ym;
	}
	public void setYm(String ym) {
		this.ym = ym == null ? null : ym.trim();
	}
	
}
