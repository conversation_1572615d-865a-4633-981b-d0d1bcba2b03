package com.hys.zyy.manage.model;

import java.util.Date;

import com.hys.zyy.manage.util.BaseModel;

public class ZyyRecruitFormDictVO extends BaseModel {
	// 主键ID
	private Long id;
	// 编码
	private String code;
	// 名称
	private String name;
	// 分类编码
	private String categoryCode;
	private Integer status;
	private Date createTime;
	private Date updateTime;
	private Integer hospType;
	private String baseStdNames;
	private String baseStdIds;
	private Integer highestRecordSchool;

	public ZyyRecruitFormDictVO() {
		super();
	}

	public ZyyRecruitFormDictVO(Integer highestRecordSchool, String categoryCode) {
		super();
		this.highestRecordSchool = highestRecordSchool;
		this.categoryCode = categoryCode;
	}
	
	public void init(Integer highestRecordSchool, String categoryCode) {
		setHighestRecordSchool(highestRecordSchool);
		setCategoryCode(categoryCode);
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getCategoryCode() {
		return categoryCode;
	}

	public void setCategoryCode(String categoryCode) {
		this.categoryCode = categoryCode;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public Integer getHospType() {
		return hospType;
	}

	public void setHospType(Integer hospType) {
		this.hospType = hospType;
	}

	public String getBaseStdNames() {
		return baseStdNames;
	}

	public void setBaseStdNames(String baseStdNames) {
		this.baseStdNames = baseStdNames;
	}

	public Integer getHighestRecordSchool() {
		return highestRecordSchool;
	}

	public void setHighestRecordSchool(Integer highestRecordSchool) {
		this.highestRecordSchool = highestRecordSchool;
	}

	public String getBaseStdIds() {
		return baseStdIds;
	}

	public void setBaseStdIds(String baseStdIds) {
		this.baseStdIds = baseStdIds == null ? null : baseStdIds.trim();
	}

}
