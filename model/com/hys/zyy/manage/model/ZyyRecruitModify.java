package com.hys.zyy.manage.model;

import java.util.Date;
import java.util.List;

/**
 * 
 * 标题：zyy
 * 
 * 作者：Tony Mar 21, 2012
 * 
 * 描述：医院修改计划招生人数
 * 
 * 说明:
 */
public class ZyyRecruitModify extends ZyyBaseObject {

	private static final long serialVersionUID = 1792675470103146684L;
	
	/**
	 * 主键ID
	 */
	private Long id ;
	
	/**
	 * 省厅ID
	 */
	private Long provinceOrgId ;
	
	/**
	 * 计划制定者机构ID
	 */
	private Long zyyOrgId ;
	
	/**
	 * 医院id
	 */
	private Long hospId;
	
	/**
	 * 制定者ID
	 */
	private Long zyyUserId ;
	
	/**
	 * 年度ID
	 */
	private Long recruitYearId ;
	
	/**
	 * 申请时间
	 */
	private Date applyDate ;
	
	/**
	 * 申请状态 0.申请提交 1.申请通过 2.申请不通过 3.保存状态 -1.已删除
	 */
	private Integer applyStatus ;
	
	/**
	 * 最后更新时间
	 */
	private Date lastUpdateDate ;
	
	/**
	 * 申请原因
	 */
	private String applyReason ;
	
	/**
	 * 不通过原因
	 */
	private String noReason ;
	
	/**
	 * 提交计划类别 1.社会人计划 2.单位人计划 3.全部
	 */
	private Integer recruitModifyType ;
	
	/**
	 * 真实姓名
	 */
	private String realName ;
	
	/**
	 * 第几次提交
	 */
	private Integer subNumber;
	
	/**
	 * 组织机构名称
	 */
	private String orgName ;
	
	/**
	 * 组织机构别名
	 */
	private String orgAliasName ;
	
	/**
	 * 是否最终级别
	 */
	private Integer isFinal ;
	
	/**
	 * 计划招生审核记录
	 */
	private ZyyRecruitModifyCheck modifyCheck ;
	
	/**
	 * 医院修改计划招生人数详细列表
	 */
	private List<ZyyRecruitModifyDetail> detailList;
	
	/**
	 * 扩展基地列表
	 */
	private List<ZyyRecruitBaseExtend> baseExtList ;
	
	/**
	 * 审核记录列表
	 */
	private List<ZyyRecruitModifyCheck> checkList ;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getProvinceOrgId() {
		return provinceOrgId;
	}

	public void setProvinceOrgId(Long provinceOrgId) {
		this.provinceOrgId = provinceOrgId;
	}

	public Long getZyyOrgId() {
		return zyyOrgId;
	}

	public void setZyyOrgId(Long zyyOrgId) {
		this.zyyOrgId = zyyOrgId;
	}

	public Long getHospId() {
		return hospId;
	}

	public void setHospId(Long hospId) {
		this.hospId = hospId;
	}

	public Long getZyyUserId() {
		return zyyUserId;
	}

	public void setZyyUserId(Long zyyUserId) {
		this.zyyUserId = zyyUserId;
	}

	public Long getRecruitYearId() {
		return recruitYearId;
	}

	public void setRecruitYearId(Long recruitYearId) {
		this.recruitYearId = recruitYearId;
	}

	public Date getApplyDate() {
		return applyDate;
	}

	public void setApplyDate(Date applyDate) {
		this.applyDate = applyDate;
	}

	public Integer getApplyStatus() {
		return applyStatus;
	}

	public void setApplyStatus(Integer applyStatus) {
		this.applyStatus = applyStatus;
	}

	public Date getLastUpdateDate() {
		return lastUpdateDate;
	}

	public void setLastUpdateDate(Date lastUpdateDate) {
		this.lastUpdateDate = lastUpdateDate;
	}

	public String getApplyReason() {
		return applyReason;
	}

	public void setApplyReason(String applyReason) {
		this.applyReason = applyReason;
	}

	public String getNoReason() {
		return noReason;
	}

	public void setNoReason(String noReason) {
		this.noReason = noReason;
	}

	public Integer getRecruitModifyType() {
		return recruitModifyType;
	}

	public void setRecruitModifyType(Integer recruitModifyType) {
		this.recruitModifyType = recruitModifyType;
	}

	public String getRealName() {
		return realName;
	}

	public void setRealName(String realName) {
		this.realName = realName;
	}

	public Integer getSubNumber() {
		return subNumber;
	}

	public void setSubNumber(Integer subNumber) {
		this.subNumber = subNumber;
	}

	public List<ZyyRecruitModifyDetail> getDetailList() {
		return detailList;
	}

	public void setDetailList(List<ZyyRecruitModifyDetail> detailList) {
		this.detailList = detailList;
	}

	public List<ZyyRecruitModifyCheck> getCheckList() {
		return checkList;
	}

	public void setCheckList(List<ZyyRecruitModifyCheck> checkList) {
		this.checkList = checkList;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public String getOrgAliasName() {
		return orgAliasName;
	}

	public void setOrgAliasName(String orgAliasName) {
		this.orgAliasName = orgAliasName;
	}

	public ZyyRecruitModifyCheck getModifyCheck() {
		return modifyCheck;
	}

	public void setModifyCheck(ZyyRecruitModifyCheck modifyCheck) {
		this.modifyCheck = modifyCheck;
	}

	public Integer getIsFinal() {
		return isFinal;
	}

	public void setIsFinal(Integer isFinal) {
		this.isFinal = isFinal;
	}

	public List<ZyyRecruitBaseExtend> getBaseExtList() {
		return baseExtList;
	}

	public void setBaseExtList(List<ZyyRecruitBaseExtend> baseExtList) {
		this.baseExtList = baseExtList;
	}
}