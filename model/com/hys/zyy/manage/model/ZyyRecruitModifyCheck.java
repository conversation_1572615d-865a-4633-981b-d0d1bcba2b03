package com.hys.zyy.manage.model;

import java.util.Date;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：张伟清 2012-07-19
 * 
 * 描述：修改计划招生审核记录
 * 
 * 说明:
 */
public class ZyyRecruitModifyCheck extends ZyyBaseObject {

	private static final long serialVersionUID = -5910047761186694171L;

	/**
	 * 主键ID
	 */
	private Long id ;
	
	/**
	 * 修改计划招生ID
	 */
	private Long recruitModifyId ;
	
	/**
	 * 审核人ID
	 */
	private Long checkUserId ;
	
	/**
	 * 审核状态
	 */
	private Integer checkStatus ;
	
	/**
	 * 审核时间
	 */
	private Date checkDate ;
	
	/**
	 * 审核备注
	 */
	private String checkRemark ;
	
	/**
	 * 审核级别
	 */
	private Integer checkProcessLevel ;
	
	/**
	 * 提交计划类别
	 */
	private Integer recruitModifyType ;
	
	/**
	 * 用户类别
	 */
	private Integer zyyUserType ;
	
	/**
	 * 真实姓名
	 */
	private String realName ;
	
	/**
	 * 用户组织机构ID  审核人组织机构id.新增的
	 */
	private Long zyyUserOrgId ;

	/**
	 * 审核人机构ID
	 */
	private Long checkUserOrgId ;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getRecruitModifyId() {
		return recruitModifyId;
	}

	public void setRecruitModifyId(Long recruitModifyId) {
		this.recruitModifyId = recruitModifyId;
	}

	public Long getCheckUserId() {
		return checkUserId;
	}

	public void setCheckUserId(Long checkUserId) {
		this.checkUserId = checkUserId;
	}

	public Integer getCheckStatus() {
		return checkStatus;
	}

	public void setCheckStatus(Integer checkStatus) {
		this.checkStatus = checkStatus;
	}

	public Date getCheckDate() {
		return checkDate;
	}

	public void setCheckDate(Date checkDate) {
		this.checkDate = checkDate;
	}

	public String getCheckRemark() {
		return checkRemark;
	}

	public void setCheckRemark(String checkRemark) {
		this.checkRemark = checkRemark;
	}

	public Integer getCheckProcessLevel() {
		return checkProcessLevel;
	}

	public void setCheckProcessLevel(Integer checkProcessLevel) {
		this.checkProcessLevel = checkProcessLevel;
	}

	public Integer getRecruitModifyType() {
		return recruitModifyType;
	}

	public void setRecruitModifyType(Integer recruitModifyType) {
		this.recruitModifyType = recruitModifyType;
	}

	public Integer getZyyUserType() {
		return zyyUserType;
	}

	public void setZyyUserType(Integer zyyUserType) {
		this.zyyUserType = zyyUserType;
	}

	public String getRealName() {
		return realName;
	}

	public void setRealName(String realName) {
		this.realName = realName;
	}

	public Long getZyyUserOrgId() {
		return zyyUserOrgId;
	}

	public void setZyyUserOrgId(Long zyyUserOrgId) {
		this.zyyUserOrgId = zyyUserOrgId;
	}

	public Long getCheckUserOrgId() {
		return checkUserOrgId;
	}

	public void setCheckUserOrgId(Long checkUserOrgId) {
		this.checkUserOrgId = checkUserOrgId;
	}
}