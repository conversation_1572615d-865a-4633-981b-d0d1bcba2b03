package com.hys.zyy.manage.model;

import java.util.ArrayList;
import java.util.List;

/**
 * 招录流程配置信息
 * 注：该类只读
 * <AUTHOR>
 *
 */
public class ZyyRecruitProcessConfigVO extends ZyyBaseObject {

	

	/**
	 * 
	 */
	private static final long serialVersionUID = 1601858521230629716L;

	// 报名资格审核流程
	private ZyyRecruitProcessGroup process1;
	
	// 录取操作流程
	private ZyyRecruitProcessGroup process2;
	
	// 录取资格审核流程
	private ZyyRecruitProcessGroup process3;
	
	// 志愿调剂工作流程
	private ZyyRecruitProcessGroup process4;
	
	public ZyyRecruitProcessConfigVO() {
		this.process1 = new ZyyRecruitProcessGroup();
		this.process2 = new ZyyRecruitProcessGroup();
		this.process3 = new ZyyRecruitProcessGroup();
		this.process4 = new ZyyRecruitProcessGroup();
	}

	public void read(List<ZyyProcessDetail> flows) {
		for(ZyyProcessDetail detail : flows) {
			ZyyRecruitProcessGroup entity = findOrCreate(detail.getProcessId());
			entity.read(detail);
		}
	}
	
	public void read(ZyyRecruitConfig config) {
		if(config == null)
			return;
		this.process1.stepByStep = (config.getIsRegAudits() == null) ? false : (config.getIsRegAudits() == 1 ? true : false);
		this.process2.stepByStep = (config.getIsEnrollAudits() == null) ? false : (config.getIsEnrollAudits() == 1 ? true : false);
		this.process3.stepByStep = (config.getIsEnrollApplyAudits() == null) ? false : (config.getIsEnrollApplyAudits() == 1 ? true : false);
		this.process4.stepByStep = (config.getIsSwapAudits() == null) ? false : (config.getIsSwapAudits() == 1 ? true : false);
	}
	
	public ZyyRecruitProcessGroup findOrCreate(Long pid) {
		ZyyRecruitProcessGroup reference = null;
		if(pid == 2) {
			reference = this.process1;
		}
		else if(pid == 7) {
			reference = this.process2;
		}
		else if(pid == 3) {
			reference = this.process3;
		}
		else if(pid == 8) {
			reference = this.process4;
		}
		return reference;
	}
	
	/**
	 * 报名审核流程
	 * @return
	 */
	public ZyyRecruitProcessGroup getProcess1() {
		return process1;
	}

	/**
	 * 招录操作流程
	 * @return
	 */
	public ZyyRecruitProcessGroup getProcess2() {
		return process2;
	}

	/**
	 * 录取审核流程
	 * @return
	 */
	public ZyyRecruitProcessGroup getProcess3() {
		return process3;
	}

	/**
	 * 调剂流程
	 * @return
	 */
	public ZyyRecruitProcessGroup getProcess4() {
		return process4;
	}

}

/**
 * 流程组包含流程关系信息
 * <AUTHOR>
 *
 */
class ZyyRecruitProcessGroup {
	
	// 是否逐级审核
	protected boolean stepByStep;
	
	// 流程列表
	private List<ZyyRecruitProcess> processes;
	
	// 是否存在流程
	public boolean hasProcess() {
		return this.processes != null;
	}

	public void read(ZyyProcessDetail detail) {
		if(detail == null)
			return;
		
		if(this.processes == null)
			this.processes = new ArrayList<ZyyRecruitProcess>();
		
		ZyyRecruitProcess vo = new ZyyRecruitProcess();
		vo.read(detail);
		
		this.processes.add(vo);
	}

	public boolean isStepByStep() {
		return stepByStep;
	}

	public List<ZyyRecruitProcess> getProcesses() {
		return processes;
	}
	
}

/**
 * 流程项
 * <AUTHOR>
 *
 */
class ZyyRecruitProcess {
	
	// 审核级别
	private int level;
	
	// 组织机构ID
	private Long orgId;
	
	// 用户类型
	private Integer userType;
	
	// 调剂方式
	private Integer swapType;

	public void read(ZyyProcessDetail detail) {
		this.level = detail.getProcessLevel();
		this.orgId = detail.getVerifiersOrg();
		this.userType = detail.getVerifiers();
		this.swapType = detail.getAdjustType();
	}

	public int getLevel() {
		return level;
	}

	public Long getOrgId() {
		return orgId;
	}

	public Integer getUserType() {
		return userType;
	}

	public Integer getSwapType() {
		return swapType;
	}

}