package com.hys.zyy.manage.model;


/**
 * yjk  2016.12.7
 * 报名招录中自动批量录取对应的推荐录取学员表
 */

public class ZyyRecruitResiRecommend extends ZyyBaseObject {

	private static final long serialVersionUID = 1L;
	/**
	 * 主键ID
	 */
	private Long id;
	/**
	 * 阶段
	 */
	private Long stageId;
	/**
	 * 第一志愿，第二志愿，第三志愿
	 */
	private Integer will;
	/**
	 * 医院id
	 */
	private Long hospitalId;
	/**
	 * 学科id
	 */
	private Long baseId;
	/**
	 * 学员id
	 */
	private Long residencyId;
	/**
	 * 1-推荐录取
     * 2-存在争议(分数相同)
     * 3-不推荐录取
	 */
	private Integer recommendStatus;
	/**
	 * 1-分数优先
     * 2-志愿优先
	 */
	private Integer category;
	/**
	 * 1-此学员已推荐操作
     * 0-此学员未参与推荐操作
     * -1-删除
	 */
	private Integer status;
	/**
	 * 志愿表的id
	 */
	private Long willId;
	/**
	 * 省厅的id
	 */
	private Long provinceId;
	/**
	 * 年度的id
	 */
	private Long yearId;
	/**
	 * 中西医
	 */
	private Integer hospType;
	/**
	 * 学员属性
	 */
	private Integer residencySource;
	
	
	
	public Integer getHospType() {
		return hospType;
	}
	public void setHospType(Integer hospType) {
		this.hospType = hospType;
	}
	public Integer getResidencySource() {
		return residencySource;
	}
	public void setResidencySource(Integer residencySource) {
		this.residencySource = residencySource;
	}
	public Long getYearId() {
		return yearId;
	}
	public void setYearId(Long yearId) {
		this.yearId = yearId;
	}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getStageId() {
		return stageId;
	}
	public void setStageId(Long stageId) {
		this.stageId = stageId;
	}
	public Integer getWill() {
		return will;
	}
	public void setWill(Integer will) {
		this.will = will;
	}
	public Long getHospitalId() {
		return hospitalId;
	}
	public void setHospitalId(Long hospitalId) {
		this.hospitalId = hospitalId;
	}
	public Long getBaseId() {
		return baseId;
	}
	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}
	public Long getResidencyId() {
		return residencyId;
	}
	public void setResidencyId(Long residencyId) {
		this.residencyId = residencyId;
	}
	public Integer getRecommendStatus() {
		return recommendStatus;
	}
	public void setRecommendStatus(Integer recommendStatus) {
		this.recommendStatus = recommendStatus;
	}
	public Integer getCategory() {
		return category;
	}
	public void setCategory(Integer category) {
		this.category = category;
	}
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	public Long getWillId() {
		return willId;
	}
	public void setWillId(Long willId) {
		this.willId = willId;
	}
	public Long getProvinceId() {
		return provinceId;
	}
	public void setProvinceId(Long provinceId) {
		this.provinceId = provinceId;
	}
	
	
}