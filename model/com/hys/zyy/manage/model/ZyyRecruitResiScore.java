package com.hys.zyy.manage.model;

import java.util.Date;


/**
 * 
 */

public class ZyyRecruitResiScore extends ZyyBaseObject {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	/**
	 * 主键id
	 */
	private Long id;
	/**
	 * 年度
	 */
	private Long yearId;
	/**
	 * 阶段
	 */
	private Long stageId;
	/**
	 * 医院Id
	 */
	private Long hospitalId;
	/**
	 * 医院名称
	 */
	private String hospitalName;
	/**
	 * 学员Id
	 */
	private Long residencyId;
	/**
	 * 真实姓名
	 */
	private String realName;
	/**
	 * 学员分数
	 */
	private Float score;
	/**
	 * 创建者Id
	 */
	private Long createrId;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 状态信息 1正常    -1删除
	 */
	private Long status;
	
	
	
	public String getHospitalName() {
		return hospitalName;
	}
	public void setHospitalName(String hospitalName) {
		this.hospitalName = hospitalName;
	}
	public String getRealName() {
		return realName;
	}
	public void setRealName(String realName) {
		this.realName = realName;
	}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getYearId() {
		return yearId;
	}
	public void setYearId(Long yearId) {
		this.yearId = yearId;
	}
	public Long getStageId() {
		return stageId;
	}
	public void setStageId(Long stageId) {
		this.stageId = stageId;
	}
	public Long getHospitalId() {
		return hospitalId;
	}
	public void setHospitalId(Long hospitalId) {
		this.hospitalId = hospitalId;
	}
	public Long getResidencyId() {
		return residencyId;
	}
	public void setResidencyId(Long residencyId) {
		this.residencyId = residencyId;
	}
	public Float getScore() {
		return score;
	}
	public void setScore(Float score) {
		this.score = score;
	}
	public Long getCreaterId() {
		return createrId;
	}
	public void setCreaterId(Long createrId) {
		this.createrId = createrId;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	public Long getStatus() {
		return status;
	}
	public void setStatus(Long status) {
		this.status = status;
	}
	
	
}
