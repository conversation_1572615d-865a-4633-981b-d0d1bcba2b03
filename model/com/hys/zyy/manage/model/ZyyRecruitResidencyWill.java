package com.hys.zyy.manage.model;

import java.util.Date;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：张伟清 2012-03-20
 * 
 * 描述：住院医师志愿信息
 * 
 * 说明:
 */
public class ZyyRecruitResidencyWill extends ZyyBaseObject {

	private static final long serialVersionUID = 4932408566461532145L;

	/**
	 * 主键ID
	 */
	private Long id ;
	
	/**
	 * 招录阶段ID 
	 */
	private Long recruitStageId ;
	
	/**
	 * 招录基地ID  记录的是招录计划的id （zyy_recruit_base_extend的id）
	 */
	private Long recruitBaseId ;
	
	/**
	 * 住院医师ID
	 */
	private Long residencyId ;
	
	/**
	 * 基地ID
	 */
	private Long baseId ;
	
	/**
	 * 医院ID
	 */
	private Long hospitalId ;
	
	/**
	 * 最后修改人ID
	 */
	private Long lastUpdateUserId ;
	
	/**
	 * 是否服从调剂
	 */
	private Integer isAdjust ;
	
	/**
	 * 是否服从调剂  增加“是否服从调剂培训基地” 是、否；  isAdjust -- “是否服从调剂专业”  是、否；。
	 */
	private Integer isAdjustHospital ;
	
	/**
	 * 状态
	 */
	private Integer status ;
	private Integer state;
	
	/**
	 * 住院医师查看状态
	 */
	private Integer residencyViewStatus ;
	
	/**
	 * 住院医师填写时间
	 */
	private Date recruitDate ;
	
	/**
	 * 最后修改时间
	 */
	private Date lastUpdateDate ;
	
	/**
	 * 是否调剂自愿
	 */
	private Integer isAdjusted ;
	
	/**
	 * 毁约原因
	 */
	private String breachContract ;
	
	/**
	 * 志愿
	 */
	private Integer will ;
	
	/**
	 * 志愿顺序
	 */
	private Integer willSeq ;
	
	/**
	 * 是否隐藏
	 */
	private Integer isHidden ;
	
	/**
	 * 最后更新类别
	 */
	private Integer lastUpdateType ;
	
	/**
	 * 录取级别
	 */
	private Integer admitLevel;
	
	/**
	 * 单位人社会人
	 * @return
	 */
	private Integer residencySource;
	
	/**
	 * 年度
	 * @return
	 */
	private Long yearId;
	
	public ZyyRecruitResidencyWill() {
		super();
	}

	public ZyyRecruitResidencyWill(Long residencyId, Integer status, Long yearId) {
		super();
		this.residencyId = residencyId;
		this.status = status;
		this.yearId = yearId;
	}
	
	public ZyyRecruitResidencyWill(Long residencyId, Long recruitStageId, Integer isAdjust) {
		super();
		this.residencyId = residencyId;
		this.recruitStageId = recruitStageId;
		this.isAdjust = isAdjust;
	}

	public Long getYearId() {
		return yearId;
	}

	public void setYearId(Long yearId) {
		this.yearId = yearId;
	}
	
	public Integer getResidencySource() {
		return residencySource;
	}

	public void setResidencySource(Integer residencySource) {
		this.residencySource = residencySource;
	}

	public Integer getAdmitLevel() {
		return admitLevel;
	}

	public void setAdmitLevel(Integer admitLevel) {
		this.admitLevel = admitLevel;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getRecruitStageId() {
		return recruitStageId;
	}

	public void setRecruitStageId(Long recruitStageId) {
		this.recruitStageId = recruitStageId;
	}

	public Long getRecruitBaseId() {
		return recruitBaseId;
	}

	public void setRecruitBaseId(Long recruitBaseId) {
		this.recruitBaseId = recruitBaseId;
	}

	public Long getResidencyId() {
		return residencyId;
	}

	public void setResidencyId(Long residencyId) {
		this.residencyId = residencyId;
	}

	public Long getBaseId() {
		return baseId;
	}

	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}

	public Long getHospitalId() {
		return hospitalId;
	}

	public void setHospitalId(Long hospitalId) {
		this.hospitalId = hospitalId;
	}

	public Long getLastUpdateUserId() {
		return lastUpdateUserId;
	}

	public void setLastUpdateUserId(Long lastUpdateUserId) {
		this.lastUpdateUserId = lastUpdateUserId;
	}

	public Integer getIsAdjust() {
		return isAdjust;
	}

	public void setIsAdjust(Integer isAdjust) {
		this.isAdjust = isAdjust;
	}

	public Integer getIsAdjustHospital() {
		return isAdjustHospital;
	}

	public void setIsAdjustHospital(Integer isAdjustHospital) {
		this.isAdjustHospital = isAdjustHospital;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}
	
	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	public Integer getResidencyViewStatus() {
		return residencyViewStatus;
	}

	public void setResidencyViewStatus(Integer residencyViewStatus) {
		this.residencyViewStatus = residencyViewStatus;
	}

	public Date getRecruitDate() {
		return recruitDate;
	}

	public void setRecruitDate(Date recruitDate) {
		this.recruitDate = recruitDate;
	}

	public Date getLastUpdateDate() {
		return lastUpdateDate;
	}

	public void setLastUpdateDate(Date lastUpdateDate) {
		this.lastUpdateDate = lastUpdateDate;
	}

	public Integer getIsAdjusted() {
		return isAdjusted;
	}

	public void setIsAdjusted(Integer isAdjusted) {
		this.isAdjusted = isAdjusted;
	}

	public String getBreachContract() {
		return breachContract;
	}

	public void setBreachContract(String breachContract) {
		this.breachContract = breachContract;
	}

	public Integer getWill() {
		return will;
	}

	public void setWill(Integer will) {
		this.will = will;
	}

	public Integer getWillSeq() {
		return willSeq;
	}

	public void setWillSeq(Integer willSeq) {
		this.willSeq = willSeq;
	}

	public Integer getIsHidden() {
		return isHidden;
	}

	public void setIsHidden(Integer isHidden) {
		this.isHidden = isHidden;
	}

	public Integer getLastUpdateType() {
		return lastUpdateType;
	}

	public void setLastUpdateType(Integer lastUpdateType) {
		this.lastUpdateType = lastUpdateType;
	}
}