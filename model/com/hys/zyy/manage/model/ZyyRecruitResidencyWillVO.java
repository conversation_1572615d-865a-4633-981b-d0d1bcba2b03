package com.hys.zyy.manage.model;

import java.util.Date;
import java.util.List;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：张伟清 2012-03-20
 * 
 * 描述：住院医师志愿信息
 * 
 * 说明:
 */
public class ZyyRecruitResidencyWillVO extends ZyyRecruitResidencyWill {

	private static final long serialVersionUID = 1982073000728251189L;

	/**
	 * 组织机构ID
	 */
	private Long orgId ;
	
	/**
	 * 组织机构名称
	 */
	private String orgName ;
	/*
	 * 基地编码
	 */
	private String baseCode;
	/**
	 * 组织机构别名
	 */
	private String orgAliasName ;
	
	/**
	 * 基地名称
	 */
	private String baseName ;
	
	/**
	 * 基地别名
	 */
	private String baseAliasName ;
	/*
	 * 专业代码
	 */
	private String professionalSubjectId;
	/**
	 * 阶段ID
	 */
	private Long stageId ;
	
	/**
	 * 阶段名称
	 */
	private String stageName ;
	
	/**
	 * 年度ID
	 */
	private Long yearId ;
	
	/**
	 * 年度信息
	 */
	private String year ;
	
	/**
	 * 委托培训小计 单位人
	 */
	private Integer consignTotal;

	/**
	 * 自主培训小计 社会人
	 */
	private Integer libertyTotal;
	
	/**
	 * 吉林 - 单位人招收总数
	 * @return
	 */
	private Integer entrustTotal;
	
	/**
	 * 吉林 - 社会人招收总数
	 */
	private Integer autonomousTotal;
	
	/**
	 * 计划招生人数
	 */
	private Integer planNum ;
	
	/**
	 * 已报名人数
	 */
	private Integer recruitNum ;
	
	/**
	 * 人员类型 1.单位人 2.社会人
	 */
	private Integer residencySource ;
	
	/**
	 * 住院医师报名录取资格ID
	 */
	private Long residentQualificationId ;
	
	/**
	 * 最终报名资格
	 */
	private Integer finalSignupQualification ;
	
	/**
	 * 最终录取资格
	 */
	private Integer finalAdmissionQualification ;
	
	/**
	 * 住院医师志愿列表
	 */
	private List<ZyyRecruitResidencyWillVO> willList ;
	
	/**
	 *	报名开始时间 
	 */
	private Date signStartDate;

	/**
	 *	报名结束日期
	 */
	private Date signEndDate;
	
	/**
	 * 是否在录取操作时间内
	 */
	private Integer isBetweenAdmitDate;
	
	/**
	 * 志源审核记录
	 */
	private List<ZyyResiQualificationLog> zyyResiQualificationLog;
	
	/**
	 * 志愿是否可以被操作  2不可操作
	 */
	private Integer willIsUpdate;
	/**
	 * 学员真实姓名
	 */
	private String resiName;
	/**
	 * 学员成绩
	 */
	private Float score;
	private String recruitDateStr;
	private Integer hospType;
	private String recruitCode;
	private String certificateNo;
	
	public Integer getHospType() {
		return hospType;
	}

	public void setHospType(Integer hospType) {
		this.hospType = hospType;
	}

	public ZyyRecruitResidencyWillVO() {
		super();
	}

	public ZyyRecruitResidencyWillVO(Long residencyId, Integer status, Long yearId) {
		super(residencyId, status, yearId);
	}

	public ZyyRecruitResidencyWillVO(Long residencyId, Long recruitStageId, Integer isAdjust) {
		super(residencyId, recruitStageId, isAdjust);
	}

	public String getResiName() {
		return resiName;
	}

	public void setResiName(String resiName) {
		this.resiName = resiName;
	}

	public Float getScore() {
		return score;
	}

	public void setScore(Float score) {
		this.score = score;
	}

	public Integer getWillIsUpdate() {
		return willIsUpdate;
	}

	public void setWillIsUpdate(Integer willIsUpdate) {
		this.willIsUpdate = willIsUpdate;
	}

	public Integer getIsBetweenAdmitDate() {
		return isBetweenAdmitDate;
	}

	public void setIsBetweenAdmitDate(Integer isBetweenAdmitDate) {
		this.isBetweenAdmitDate = isBetweenAdmitDate;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public String getOrgAliasName() {
		return orgAliasName;
	}

	public void setOrgAliasName(String orgAliasName) {
		this.orgAliasName = orgAliasName;
	}

	public String getBaseName() {
		return baseName;
	}

	public void setBaseName(String baseName) {
		this.baseName = baseName;
	}

	public String getBaseAliasName() {
		return baseAliasName;
	}

	public void setBaseAliasName(String baseAliasName) {
		this.baseAliasName = baseAliasName;
	}

	public Long getStageId() {
		return stageId;
	}

	public void setStageId(Long stageId) {
		this.stageId = stageId;
	}

	public String getStageName() {
		return stageName;
	}

	public void setStageName(String stageName) {
		this.stageName = stageName;
	}
	
	public Long getSuperYearId() {
		return super.getYearId();
	}

	public Long getYearId() {
		return yearId;
	}

	public void setYearId(Long yearId) {
		this.yearId = yearId;
	}

	public String getYear() {
		return year;
	}

	public void setYear(String year) {
		this.year = year;
	}

	public Integer getPlanNum() {
		return planNum;
	}

	public void setPlanNum(Integer planNum) {
		this.planNum = planNum;
	}

	public Integer getRecruitNum() {
		return recruitNum;
	}

	public void setRecruitNum(Integer recruitNum) {
		this.recruitNum = recruitNum;
	}

	public List<ZyyRecruitResidencyWillVO> getWillList() {
		return willList;
	}

	public void setWillList(List<ZyyRecruitResidencyWillVO> willList) {
		this.willList = willList;
	}

	public Long getOrgId() {
		return orgId;
	}

	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}

	public Integer getResidencySource() {
		return residencySource;
	}

	public void setResidencySource(Integer residencySource) {
		this.residencySource = residencySource;
	}

	public Long getResidentQualificationId() {
		return residentQualificationId;
	}

	public void setResidentQualificationId(Long residentQualificationId) {
		this.residentQualificationId = residentQualificationId;
	}

	public Date getSignStartDate() {
		return signStartDate;
	}

	public void setSignStartDate(Date signStartDate) {
		this.signStartDate = signStartDate;
	}

	public Date getSignEndDate() {
		return signEndDate;
	}

	public void setSignEndDate(Date signEndDate) {
		this.signEndDate = signEndDate;
	}

	public Integer getConsignTotal() {
		return consignTotal;
	}

	public void setConsignTotal(Integer consignTotal) {
		this.consignTotal = consignTotal;
	}

	public Integer getLibertyTotal() {
		return libertyTotal;
	}

	public void setLibertyTotal(Integer libertyTotal) {
		this.libertyTotal = libertyTotal;
	}

	public Integer getEntrustTotal() {
		return entrustTotal;
	}

	public void setEntrustTotal(Integer entrustTotal) {
		this.entrustTotal = entrustTotal;
	}

	public Integer getAutonomousTotal() {
		return autonomousTotal;
	}

	public void setAutonomousTotal(Integer autonomousTotal) {
		this.autonomousTotal = autonomousTotal;
	}

	public Integer getFinalSignupQualification() {
		return finalSignupQualification;
	}

	public void setFinalSignupQualification(Integer finalSignupQualification) {
		this.finalSignupQualification = finalSignupQualification;
	}

	public Integer getFinalAdmissionQualification() {
		return finalAdmissionQualification;
	}

	public void setFinalAdmissionQualification(Integer finalAdmissionQualification) {
		this.finalAdmissionQualification = finalAdmissionQualification;
	}

	public List<ZyyResiQualificationLog> getZyyResiQualificationLog() {
		return zyyResiQualificationLog;
	}

	public void setZyyResiQualificationLog(List<ZyyResiQualificationLog> zyyResiQualificationLog) {
		this.zyyResiQualificationLog = zyyResiQualificationLog;
	}

	public String getRecruitDateStr() {
		return recruitDateStr;
	}

	public void setRecruitDateStr(String recruitDateStr) {
		this.recruitDateStr = recruitDateStr == null ? null : recruitDateStr.trim();
	}

	public String getBaseCode() {
		return baseCode;
	}

	public void setBaseCode(String baseCode) {
		this.baseCode = baseCode == null ? null : baseCode.trim();
	}

	public String getProfessionalSubjectId() {
		return professionalSubjectId;
	}

	public void setProfessionalSubjectId(String professionalSubjectId) {
		this.professionalSubjectId = professionalSubjectId == null ? null : professionalSubjectId.trim();
	}

	public String getRecruitCode() {
		return recruitCode;
	}

	public void setRecruitCode(String recruitCode) {
		this.recruitCode = recruitCode == null ? null : recruitCode.trim();
	}

	public String getCertificateNo() {
		return certificateNo;
	}

	public void setCertificateNo(String certificateNo) {
		this.certificateNo = certificateNo == null ? null : certificateNo.trim();
	}
	
}