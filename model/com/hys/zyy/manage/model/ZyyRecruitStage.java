package com.hys.zyy.manage.model;

import java.util.Date;

import com.hys.zyy.manage.util.annotation.Column;
import com.hys.zyy.manage.util.annotation.Id;
import com.hys.zyy.manage.util.annotation.Table;
/**
 * 
 * 标题：zyy
 * 
 * 作者：Tony Mar 16, 2012
 * 
 * 描述：招录阶段
 * 
 * 说明:
 */
@Table("ZYY_RECRUIT_STAGE")
public class ZyyRecruitStage extends ZyyBaseObject {

	private static final long serialVersionUID = 3517525073323514803L;
	
	/**
	 * 主键ID
	 */
	@Id("ZYY_RECRUIT_STAGE_SEQ.nextval")
	@Column("ID")
	private Long id;
	
	/**
	 * 阶段id
	 */
	@Column("STAGE_ID")
	private Long stageId;
	
	/**
	 * 年度id
	 */
	@Column("YEAR_ID")
	private Long yearId;
	
	/**
	 * 报名开始时间
	 */
	@Column("SIGN_START_DATE")
	private Date signStartDate;
	
	/**
	 * 报名始束时间
	 */
	@Column("SIGN_END_DATE")
	private Date signEndDate;
	
	/**
	 * 招录开始时间
	 */
	@Column("RECRUIT_START_DATE")
	private Date recruitStartDate;
	
	/**
	 * 招录结束时间
	 */
	@Column("RECRUIT_END_DATE")
	private Date recruitEndDate;
	
	/**
	 * 报名开始时间-保存
	 */
	private Date signStartDateSave;
	
	/**
	 * 报名始束时间-保存
	 */
	private Date signEndDateSave;
	
	/**
	 * 招录开始时间-保存
	 */
	private Date recruitStartDateSave;
	
	/**
	 * 招录结束时间-保存
	 */
	private Date recruitEndDateSave;	
	
	/**
	 * 住院医师提交后是否能修改
	 * 1：可以
	 * 2：不可以
	 */
	@Column("IS_MODIFY")
	private Integer isModify;
	
	/**
	 * 住院医师提交后是否能修改
	 * 1：可以
	 * 2：不可以
	 */
	private Integer isModifySave;
	
	/**
	 * 状态
	 * 0 -保存状态 
	 * 1 -提交状态
	 */
	@Column("STATUS")
	private Integer status;
	
	/**
	 * 招录类别 1.单位人 2.社会人
	 */
	@Column("RECRUIT_STAGE_TYPE")
	private Integer recruitStageType ;
	
	/**
	 * 北京第二志愿录取开始时间
	 */
	@Column("RECRUIT_START_DATE_TWO")
	private Date recruitStartDateTwo ;
	
	/**
	 * 北京第二志愿录取结束时间
	 */
	@Column("RECRUIT_END_DATE_TWO")
	private Date recruitEndDateTwo ;

	/**
	 * 北京第三志愿录取开始时间
	 */
	@Column("RECRUIT_START_DATE_THREE")
	private Date recruitStartDateThree ;
	
	/**
	 * 北京第三志愿录取结束时间
	 */
	@Column("RECRUIT_END_DATE_THREE")
	private Date recruitEndDateThree ;

	/**
	 * 北京第四志愿录取开始时间
	 */
	@Column("RECRUIT_START_DATE_FOUR")
	private Date recruitStartDateFour ;
	
	/**
	 * 北京第四志愿录取结束时间
	 */
	@Column("RECRUIT_END_DATE_FOUR")
	private Date recruitEndDateFour ;

	/**
	 * 北京第五志愿录取开始时间
	 */
	@Column("RECRUIT_START_DATE_FIVE")
	private Date recruitStartDateFive ;
	
	/**
	 * 北京第五志愿录取结束时间
	 */
	@Column("RECRUIT_END_DATE_FIVE")
	private Date recruitEndDateFive ;
	
	@Column("HOSP_TYPE")
	private Integer hospType;
	
	@Column("SIGN_VIEW_OPEN")
	private Boolean signViewOpen = false;
	
	@Column("RECRUIT_VIEW_OPEN")
	private Boolean recruitViewOpen = false;
	
	@Column("RECRUIT_VIEW_TWO_OPEN")
	private Boolean recruitViewTwoOpen = false;
	
	@Column("RECRUIT_VIEW_THREE_OPEN")
	private Boolean recruitViewThreeOpen = false;
	
	@Column("RECRUIT_VIEW_FOUR_OPEN")
	private Boolean recruitViewFourOpen = false;
	
	@Column("RECRUIT_VIEW_FIVE_OPEN")
	private Boolean recruitViewFiveOpen = false;
	
	/**
	 * 附加配置信息
	 */
	@Column("ADDITION_CONFIG_XML")
	private String additionConfigXml;
	/*
	 * 学员报到开始时间
	 */
	@Column("RESIDENCY_REPORT_START_DATE")
	private Date residencyReportStartDate;
	/*
	 * 学员报到结束时间
	 */
	@Column("RESIDENCY_REPORT_END_DATE")
	private Date residencyReportEndDate;
	/*
	 * 其他用户是否可查看学员报到时间
	 */
	@Column("RESI_REP_VIEW_OPEN")
	private Boolean resiRepViewOpen = false;
	/*
	 * 录取时间
	 */
	@Column("RECRUIT_DATE")
	private Date recruitDate;
	
	public ZyyRecruitStage() {
		super();
	}

	public ZyyRecruitStage(Long yearId, Integer hospType) {
		super();
		this.yearId = yearId;
		this.hospType = hospType;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getStageId() {
		return stageId;
	}

	public void setStageId(Long stageId) {
		this.stageId = stageId;
	}

	public Long getYearId() {
		return yearId;
	}

	public void setYearId(Long yearId) {
		this.yearId = yearId;
	}

	public Date getSignStartDate() {
		return signStartDate;
	}

	public void setSignStartDate(Date signStartDate) {
		this.signStartDate = signStartDate;
	}

	public Date getSignEndDate() {
		return signEndDate;
	}

	public void setSignEndDate(Date signEndDate) {
		this.signEndDate = signEndDate;
	}

	public Date getRecruitStartDate() {
		return recruitStartDate;
	}

	public void setRecruitStartDate(Date recruitStartDate) {
		this.recruitStartDate = recruitStartDate;
	}

	public Date getRecruitEndDate() {
		return recruitEndDate;
	}

	public void setRecruitEndDate(Date recruitEndDate) {
		this.recruitEndDate = recruitEndDate;
	}

	public Integer getIsModify() {
		return isModify;
	}

	public void setIsModify(Integer isModify) {
		this.isModify = isModify;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Date getSignStartDateSave() {
		return signStartDateSave;
	}

	public void setSignStartDateSave(Date signStartDateSave) {
		this.signStartDateSave = signStartDateSave;
	}

	public Date getSignEndDateSave() {
		return signEndDateSave;
	}

	public void setSignEndDateSave(Date signEndDateSave) {
		this.signEndDateSave = signEndDateSave;
	}

	public Date getRecruitStartDateSave() {
		return recruitStartDateSave;
	}

	public void setRecruitStartDateSave(Date recruitStartDateSave) {
		this.recruitStartDateSave = recruitStartDateSave;
	}

	public Date getRecruitEndDateSave() {
		return recruitEndDateSave;
	}

	public void setRecruitEndDateSave(Date recruitEndDateSave) {
		this.recruitEndDateSave = recruitEndDateSave;
	}

	public Integer getIsModifySave() {
		return isModifySave;
	}

	public void setIsModifySave(Integer isModifySave) {
		this.isModifySave = isModifySave;
	}

	public Integer getRecruitStageType() {
		return recruitStageType;
	}

	public void setRecruitStageType(Integer recruitStageType) {
		this.recruitStageType = recruitStageType;
	}

	public Date getRecruitStartDateTwo() {
		return recruitStartDateTwo;
	}

	public void setRecruitStartDateTwo(Date recruitStartDateTwo) {
		this.recruitStartDateTwo = recruitStartDateTwo;
	}

	public Date getRecruitEndDateTwo() {
		return recruitEndDateTwo;
	}

	public void setRecruitEndDateTwo(Date recruitEndDateTwo) {
		this.recruitEndDateTwo = recruitEndDateTwo;
	}

	public Date getRecruitStartDateThree() {
		return recruitStartDateThree;
	}

	public void setRecruitStartDateThree(Date recruitStartDateThree) {
		this.recruitStartDateThree = recruitStartDateThree;
	}

	public Date getRecruitEndDateThree() {
		return recruitEndDateThree;
	}

	public void setRecruitEndDateThree(Date recruitEndDateThree) {
		this.recruitEndDateThree = recruitEndDateThree;
	}

	public Date getRecruitStartDateFour() {
		return recruitStartDateFour;
	}

	public void setRecruitStartDateFour(Date recruitStartDateFour) {
		this.recruitStartDateFour = recruitStartDateFour;
	}

	public Date getRecruitEndDateFour() {
		return recruitEndDateFour;
	}

	public void setRecruitEndDateFour(Date recruitEndDateFour) {
		this.recruitEndDateFour = recruitEndDateFour;
	}

	public Date getRecruitStartDateFive() {
		return recruitStartDateFive;
	}

	public void setRecruitStartDateFive(Date recruitStartDateFive) {
		this.recruitStartDateFive = recruitStartDateFive;
	}

	public Date getRecruitEndDateFive() {
		return recruitEndDateFive;
	}

	public void setRecruitEndDateFive(Date recruitEndDateFive) {
		this.recruitEndDateFive = recruitEndDateFive;
	}

	public Integer getHospType() {
		return hospType;
	}

	public void setHospType(Integer hospType) {
		this.hospType = hospType;
	}

	public Boolean getSignViewOpen() {
		return signViewOpen;
	}

	public void setSignViewOpen(Boolean signViewOpen) {
		this.signViewOpen = signViewOpen;
	}

	public Boolean getRecruitViewOpen() {
		return recruitViewOpen;
	}

	public void setRecruitViewOpen(Boolean recruitViewOpen) {
		this.recruitViewOpen = recruitViewOpen;
	}

	public Boolean getRecruitViewTwoOpen() {
		return recruitViewTwoOpen;
	}

	public void setRecruitViewTwoOpen(Boolean recruitViewTwoOpen) {
		this.recruitViewTwoOpen = recruitViewTwoOpen;
	}

	public Boolean getRecruitViewThreeOpen() {
		return recruitViewThreeOpen;
	}

	public void setRecruitViewThreeOpen(Boolean recruitViewThreeOpen) {
		this.recruitViewThreeOpen = recruitViewThreeOpen;
	}

	public Boolean getRecruitViewFourOpen() {
		return recruitViewFourOpen;
	}

	public void setRecruitViewFourOpen(Boolean recruitViewFourOpen) {
		this.recruitViewFourOpen = recruitViewFourOpen;
	}

	public Boolean getRecruitViewFiveOpen() {
		return recruitViewFiveOpen;
	}

	public void setRecruitViewFiveOpen(Boolean recruitViewFiveOpen) {
		this.recruitViewFiveOpen = recruitViewFiveOpen;
	}

	public String getAdditionConfigXml() {
		return additionConfigXml;
	}

	public void setAdditionConfigXml(String additionConfigXml) {
		this.additionConfigXml = additionConfigXml;
	}

	public Date getResidencyReportStartDate() {
		return residencyReportStartDate;
	}

	public void setResidencyReportStartDate(Date residencyReportStartDate) {
		this.residencyReportStartDate = residencyReportStartDate;
	}

	public Date getResidencyReportEndDate() {
		return residencyReportEndDate;
	}

	public void setResidencyReportEndDate(Date residencyReportEndDate) {
		this.residencyReportEndDate = residencyReportEndDate;
	}

	public Boolean getResiRepViewOpen() {
		return resiRepViewOpen;
	}

	public void setResiRepViewOpen(Boolean resiRepViewOpen) {
		this.resiRepViewOpen = resiRepViewOpen;
	}

	public Date getRecruitDate() {
		return recruitDate;
	}

	public void setRecruitDate(Date recruitDate) {
		this.recruitDate = recruitDate;
	}
	
}