package com.hys.zyy.manage.model;

import java.util.ArrayList;
import java.util.List;

import org.hamcrest.core.IsEqual;

import ch.lambdaj.Lambda;

import com.hys.zyy.manage.service.impl.ApplicationConfigurationManager;
import com.hys.zyy.manage.service.util.ZyyRecruitStageParser;
import com.hys.zyy.manage.util.DateUtil;


/**
 * 各阶段起止时间设置
 * <AUTHOR>
 *
 */
public class ZyyRecruitStageTimeConfigVO extends ZyyBaseObject {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	private Long id;
	
	private Long stage;
	
	private Integer hospCount;				// 可填报医院数量
	
	private Integer willCount;				// 可填报志愿数量
	
	private Boolean duplicate;				// 允许学员重复填报医院
	private Integer allowRepeat;
	
	private ZyyTimeConfigVO cycle1;			// 学员报名
	private ZyyTimeConfigVO cycle7; // 学员报到时间
	private ZyyTimeConfigVO cycle8; // 录取通知书生成时间设置
	
	private List<ZyyTimeConfigVO> cycle2;	// 报名资格审核
	
	private List<ZyyTimeConfigVO> cycle3;	// 录取操作（注意：无流程信息，录取阶段时间）
	
	private List<ZyyTimeConfigVO> cycle6;	// 录取操作（注意：有流程，无录取阶段时间）;
	
	private List<ZyyTimeConfigVO> cycle4;	// 录取资格审核
	
	private List<ZyyTimeConfigVO> cycle5;	// 志愿调剂
	
	public ZyyRecruitStageTimeConfigVO() {
		this.cycle2 = new ArrayList<ZyyTimeConfigVO>();
		this.cycle3 = new ArrayList<ZyyTimeConfigVO>();
		this.cycle4 = new ArrayList<ZyyTimeConfigVO>();
		this.cycle5 = new ArrayList<ZyyTimeConfigVO>();
		this.cycle6 = new ArrayList<ZyyTimeConfigVO>();
	}
	
	public ZyyRecruitStageTimeConfigVO(Long id) {
		this();
		this.id = id;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public ZyyTimeConfigVO getCycle1() {
		return cycle1;
	}

	public void setCycle1(ZyyTimeConfigVO cycle1) {
		this.cycle1 = cycle1;
	}

	public ZyyTimeConfigVO getCycle7() {
		return cycle7;
	}

	public void setCycle7(ZyyTimeConfigVO cycle7) {
		this.cycle7 = cycle7;
	}

	public ZyyTimeConfigVO getCycle8() {
		return cycle8;
	}

	public void setCycle8(ZyyTimeConfigVO cycle8) {
		this.cycle8 = cycle8;
	}

	public List<ZyyTimeConfigVO> getCycle2() {
		return cycle2;
	}

	public void setCycle2(List<ZyyTimeConfigVO> cycle2) {
		this.cycle2 = cycle2;
	}

	public List<ZyyTimeConfigVO> getCycle3() {
		return cycle3;
	}

	public void setCycle3(List<ZyyTimeConfigVO> cycle3) {
		this.cycle3 = cycle3;
	}

	public List<ZyyTimeConfigVO> getCycle4() {
		return cycle4;
	}

	public void setCycle4(List<ZyyTimeConfigVO> cycle4) {
		this.cycle4 = cycle4;
	}

	public List<ZyyTimeConfigVO> getCycle5() {
		return cycle5;
	}

	public void setCycle5(List<ZyyTimeConfigVO> cycle5) {
		this.cycle5 = cycle5;
	}
	
	public void init(List<ZyyProcessDetail> flows) {
		
		// 报名资格审核流程
		List<ZyyProcessDetail> subflows = Lambda.select(flows, Lambda.having(Lambda.on(ZyyProcessDetail.class).getProcessId(), IsEqual.equalTo(2l)));
		this.cycle2 = new ArrayList<ZyyTimeConfigVO>(subflows.size());
		for(ZyyProcessDetail entity : subflows) {
			ZyyTimeConfigVO vo = new ZyyTimeConfigVO();
			vo.setOperator(entity.getOrgName() != null ? entity.getOrgName() : ApplicationConfigurationManager.getMessage(ZyyProcessDetail.class.getCanonicalName() + ".verifiers." + entity.getVerifiers()));
			vo.setOrgId(entity.getVerifiersOrg());
			vo.setUserType(entity.getVerifiers());
			this.cycle2.add(vo);
		}
		// 录取操作流程
		subflows = Lambda.select(flows, Lambda.having(Lambda.on(ZyyProcessDetail.class).getProcessId(), IsEqual.equalTo(7l)));
		this.cycle6 = new ArrayList<ZyyTimeConfigVO>(subflows.size());
		for(ZyyProcessDetail entity : subflows) {
			ZyyTimeConfigVO vo = new ZyyTimeConfigVO();
			vo.setOrgId(entity.getVerifiersOrg());
			vo.setUserType(entity.getVerifiers());
			this.cycle6.add(vo);
		}
		
		// 录取资格审核流程
		subflows = Lambda.select(flows, Lambda.having(Lambda.on(ZyyProcessDetail.class).getProcessId(), IsEqual.equalTo(3l)));
		this.cycle4 = new ArrayList<ZyyTimeConfigVO>(subflows.size());
		for(ZyyProcessDetail entity : subflows) {
			ZyyTimeConfigVO vo = new ZyyTimeConfigVO();
			vo.setOperator(entity.getOrgName() != null ? entity.getOrgName() : ApplicationConfigurationManager.getMessage(ZyyProcessDetail.class.getCanonicalName() + ".verifiers." + entity.getVerifiers()));
			vo.setOrgId(entity.getVerifiersOrg());
			vo.setUserType(entity.getVerifiers());
			this.cycle4.add(vo);
		}
		// 调剂流程
		subflows = Lambda.select(flows, Lambda.having(Lambda.on(ZyyProcessDetail.class).getProcessId(), IsEqual.equalTo(8l)));
		this.cycle5 = new ArrayList<ZyyTimeConfigVO>(subflows.size());
		for(ZyyProcessDetail entity : subflows) {
			ZyyTimeConfigVO vo = new ZyyTimeConfigVO();
			vo.setOperator(entity.getOrgName() != null ? entity.getOrgName() : ApplicationConfigurationManager.getMessage(ZyyProcessDetail.class.getCanonicalName() + ".verifiers." + entity.getVerifiers()));
			vo.setOrgId(entity.getVerifiersOrg());
			vo.setUserType(entity.getVerifiers());
			this.cycle5.add(vo);
		}
		
	}

	public void read(ZyyRecruitStageVO entity) {
		
		this.stage = entity.getStageId();
		
		// 本阶段填报医院数量
		this.hospCount = entity.getStageHospital();
		
		// 本阶段填报志愿数量
		this.willCount = entity.getStageRecruit();
		
		// 解析阶段附加信息
		ZyyRecruitStageParser parser = new ZyyRecruitStageParser(entity);
		
		// 学员报名时间
		cycle1 = new ZyyTimeConfigVO();
		cycle1.setStartTime(DateUtil.format(entity.getSignStartDate(), DateUtil.FORMAT_MINUTES));
		cycle1.setEndTime(DateUtil.format(entity.getSignEndDate(), DateUtil.FORMAT_MINUTES));
		cycle1.setIsPublish(entity.getSignViewOpen() == null ? false : entity.getSignViewOpen());
		
		// 学员报到时间
		cycle7 = new ZyyTimeConfigVO();
		cycle7.setStartTime(DateUtil.format(entity.getResidencyReportStartDate(), DateUtil.FORMAT_MINUTES));
		cycle7.setEndTime(DateUtil.format(entity.getResidencyReportEndDate(), DateUtil.FORMAT_MINUTES));
		cycle7.setIsPublish(entity.getResiRepViewOpen() == null ? false : entity.getResiRepViewOpen());
		
		// 录取通知书生成时间设置
		cycle8 = new ZyyTimeConfigVO();
		cycle8.setStartTime(DateUtil.format(entity.getRecruitDate(), DateUtil.FORMAT_MINUTES));
		cycle8.setEndTime(null);
		cycle8.setIsPublish(false);
		
		// 报名资格审核 （这里的数据来自附加字段）
		for(int i = 1; i <= this.cycle2.size(); i++) {
			ZyyTimeConfigVO vo = this.cycle2.get(i - 1);
			vo.setStartTime(parser.getRegStartTime(i));
			vo.setEndTime(parser.getRegEndTime(i));
			vo.setIsPublish(parser.getRegViewOpen(i));
		}
		
		// 录取操作 (志愿数量) 这里的数据来自附加字段
		this.cycle3 = new ArrayList<ZyyTimeConfigVO>(entity.getStageHospital());
		for(int i = 1; i <= entity.getStageHospital(); i++) {
			ZyyTimeConfigVO vo = new ZyyTimeConfigVO();
			this.cycle3.add(vo);
			
			vo.setStartTime(parser.getEnrollStartTime(i));
			vo.setEndTime(parser.getEnrollEndTime(i));
			vo.setIsPublish(parser.getEnrollViewOpen(i));
		}
		
		// 录取资格审核 
		for(int i = 1; i <= this.cycle4.size(); i++) {
			ZyyTimeConfigVO vo = this.cycle4.get(i - 1);
			if(i == 1) {
				vo.setStartTime(DateUtil.format(entity.getRecruitStartDate(), DateUtil.FORMAT_MINUTES));
				vo.setEndTime(DateUtil.format(entity.getRecruitEndDate(), DateUtil.FORMAT_MINUTES));
				vo.setIsPublish(entity.getRecruitViewOpen() == null ? false : entity.getRecruitViewOpen());
			}
			else if(i == 2) {
				vo.setStartTime(DateUtil.format(entity.getRecruitStartDateTwo(), DateUtil.FORMAT_MINUTES));
				vo.setEndTime(DateUtil.format(entity.getRecruitEndDateTwo(), DateUtil.FORMAT_MINUTES));
				vo.setIsPublish(entity.getRecruitViewTwoOpen() == null ? false : entity.getRecruitViewTwoOpen());
			}
			else if(i == 3) {
				vo.setStartTime(DateUtil.format(entity.getRecruitStartDateThree(), DateUtil.FORMAT_MINUTES));
				vo.setEndTime(DateUtil.format(entity.getRecruitEndDateThree(), DateUtil.FORMAT_MINUTES));
				vo.setIsPublish(entity.getRecruitViewThreeOpen() == null ? false : entity.getRecruitViewThreeOpen());
			}
			else if(i == 4) {
				vo.setStartTime(DateUtil.format(entity.getRecruitStartDateFour(), DateUtil.FORMAT_MINUTES));
				vo.setEndTime(DateUtil.format(entity.getRecruitEndDateFour(), DateUtil.FORMAT_MINUTES));
				vo.setIsPublish(entity.getRecruitViewFourOpen() == null ? false : entity.getRecruitViewFourOpen());
			}
			else if(i == 5) {
				vo.setStartTime(DateUtil.format(entity.getRecruitStartDateFive(), DateUtil.FORMAT_MINUTES));
				vo.setEndTime(DateUtil.format(entity.getRecruitEndDateFive(), DateUtil.FORMAT_MINUTES));
				vo.setIsPublish(entity.getRecruitViewFiveOpen() == null ? false : entity.getRecruitViewFiveOpen());
			}
		}
		
		// 调剂（这里的数据来自附加字段）
		for(int i = 1; i <= this.cycle5.size(); i++) {
			ZyyTimeConfigVO vo = this.cycle5.get(i - 1);
			vo.setStartTime(parser.getSwapStartTime(i));
			vo.setEndTime(parser.getSwapEndTime(i));
			vo.setIsPublish(parser.getSwapViewOpen(i));
		}
	}
	
	public void read(ZyyRecruitConfig config) {
		if (config != null) {
			this.allowRepeat = config.getAllowRepeat();
			this.duplicate = (config.getAllowRepeat() == null ? false : (config.getAllowRepeat() == 0 ? false : true));
		} else
			this.duplicate = false;
	}

	public void write(ZyyRecruitStageVO stage) {
		
		// 主键
		if(id != null)
			stage.setId(this.id);
		
		stage.setStageId(this.stage);
		
		// 读入学员报到时间到持久
		if(cycle7!=null){
			stage.setResidencyReportStartDate(DateUtil.parse(cycle7.getStartTime(), DateUtil.FORMAT_MINUTES));
			stage.setResidencyReportEndDate(DateUtil.parse(cycle7.getEndTime(), DateUtil.FORMAT_MINUTES));
			stage.setResiRepViewOpen(cycle7.getIsPublish());
		}
		
		// 读入录取通知书生成时间设置到持久
		if (cycle8 != null)
			stage.setRecruitDate(DateUtil.parse(cycle8.getStartTime(), DateUtil.FORMAT_MINUTES));
		
		// 读入报名时间和录取审核时间信息到持久
		stage.setSignStartDate(DateUtil.parse(cycle1.getStartTime(), DateUtil.FORMAT_MINUTES));
		stage.setSignEndDate(DateUtil.parse(cycle1.getEndTime(), DateUtil.FORMAT_MINUTES)) ;
		stage.setSignViewOpen(cycle1.getIsPublish());
		for(int i = 0; i < cycle4.size(); i++) {
			ZyyTimeConfigVO vo = cycle4.get(i);
			if(i == 0) {
				stage.setRecruitStartDate(DateUtil.parse(vo.getStartTime(), DateUtil.FORMAT_MINUTES)) ;
				stage.setRecruitEndDate(DateUtil.parse(vo.getEndTime(), DateUtil.FORMAT_MINUTES)) ;
				stage.setRecruitViewOpen(vo.getIsPublish());
			}
			else if(i == 1) {
				stage.setRecruitStartDateTwo(DateUtil.parse(vo.getStartTime(), DateUtil.FORMAT_MINUTES)) ;
				stage.setRecruitEndDateTwo(DateUtil.parse(vo.getEndTime(), DateUtil.FORMAT_MINUTES)) ;
				stage.setRecruitViewTwoOpen(vo.getIsPublish());
			}
			else if(i == 2) {
				stage.setRecruitStartDateThree(DateUtil.parse(vo.getStartTime(), DateUtil.FORMAT_MINUTES)) ;
				stage.setRecruitEndDateThree(DateUtil.parse(vo.getEndTime(), DateUtil.FORMAT_MINUTES)) ;
				stage.setRecruitViewThreeOpen(vo.getIsPublish());
			}
			else if(i == 3) {
				stage.setRecruitStartDateFour(DateUtil.parse(vo.getStartTime(), DateUtil.FORMAT_MINUTES)) ;
				stage.setRecruitEndDateFour(DateUtil.parse(vo.getEndTime(), DateUtil.FORMAT_MINUTES)) ;
				stage.setRecruitViewFourOpen(vo.getIsPublish());
			}
			else if(i == 4) {
				stage.setRecruitStartDateFive(DateUtil.parse(vo.getStartTime(), DateUtil.FORMAT_MINUTES)) ;
				stage.setRecruitEndDateFive(DateUtil.parse(vo.getEndTime(), DateUtil.FORMAT_MINUTES)) ;
				stage.setRecruitViewFiveOpen(vo.getIsPublish());
			}
		}
		
		// 其他配置报名招录，录取操作，调剂
		ZyyRecruitStageParser parser = new ZyyRecruitStageParser(stage);
		parser.begin();
		
		// 报名审核
		int level = 1;
		for(ZyyTimeConfigVO vo : cycle2)
			parser.setRegTime(level++, vo.getStartTime(), vo.getEndTime(), vo.getIsPublish());
		
		// 录取操作
		level = 1;
		for(ZyyTimeConfigVO vo : cycle3)
			parser.setEnrollTime(level++, vo.getStartTime(), vo.getEndTime(), vo.getIsPublish());
		
		// 调剂操作
		level = 1;
		for(ZyyTimeConfigVO vo : cycle5)
			parser.setSwapStartTime(level++, vo.getStartTime(), vo.getEndTime(), vo.getIsPublish());
		
		parser.end();
		
		stage.setIsModify(1);
	}

	public Long getStage() {
		return stage;
	}

	public void setStage(Long stage) {
		this.stage = stage;
	}

	public Integer getHospCount() {
		return hospCount;
	}

	public void setHospCount(Integer hospCount) {
		this.hospCount = hospCount;
	}

	public Integer getWillCount() {
		return willCount;
	}

	public void setWillCount(Integer willCount) {
		this.willCount = willCount;
	}

	public Boolean getDuplicate() {
		return duplicate;
	}

	public void setDuplicate(Boolean duplicate) {
		this.duplicate = duplicate;
	}

	public Integer getAllowRepeat() {
		return allowRepeat;
	}

	public void setAllowRepeat(Integer allowRepeat) {
		this.allowRepeat = allowRepeat;
	}

	public List<ZyyTimeConfigVO> getCycle6() {
		return cycle6;
	}

}
