package com.hys.zyy.manage.model;

import java.util.Date;
import java.util.List;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：张伟清 2012-03-19
 * 
 * 描述：招录阶段 VO
 * 
 * 说明:
 */
public class ZyyRecruitStageVO extends ZyyRecruitStage {

	private static final long serialVersionUID = -6203231620819758635L;

	/**
	 * 志愿信息
	 */
	private Integer will ;
	
	/**
	 * 阶段名称
	 */
	private String stageName ;
	
	/**
	 * 组织机构ID
	 */
	private Long orgId ;
	
	/**
	 * 志愿修改状态
	 */
	private Integer alertStatus ;
	
	/**
	 * 医院ID
	 */
	private Long hospitalId ;
	
	/**
	 * 医院名称
	 */
	private Long orgAliasName ;
	
	/**
	 * 科室名称
	 */
	private Long baseAliasName ;
	
	/**
	 * 组织机构信息
	 */
	private List<ZyyOrgVO> orgList ;
	
	/**
	 * 横跨行数
	 */
	private Integer rowSpan ;
	
	/**
	 * 志愿数量 最大填报医院数量5个
	 */
	private Integer stageHospital ;
	
	/**
	 * 志愿下学科数量 最大填报学科数量3个
	 */
	private Integer stageRecruit ;
	
	/**
	 * 是否允许学员重复填报医院
	 */
	private Boolean duplicate;
	private Integer allowRepeat;
	
	/**
	 * 住院医师志愿列表
	 */
	private List<ZyyRecruitResidencyWillVO> willList ;
	
	/**
	 * 扩展基地列表
	 */
	private List<ZyyRecruitBaseExtendVO> extendList ;
	
	/**
	 * 各阶段志愿列表
	 */
	private List<ZyyRecruitStageVO> stageList ;
	/**
	 * 志愿是否可以被操作  2不可操作
	 */
	private Integer willIsUpdate;
	private Date startRecruitDate;
	private Date endRecruitDate;
	private ZyyTimeConfigVO timeConfig;

	public ZyyRecruitStageVO() {
		super();
	}

	public ZyyRecruitStageVO(Long yearId, Integer hospType) {
		super(yearId, hospType);
	}

	public Integer getWillIsUpdate() {
		return willIsUpdate;
	}

	public void setWillIsUpdate(Integer willIsUpdate) {
		this.willIsUpdate = willIsUpdate;
	}

	public Long getHospitalId() {
		return hospitalId;
	}

	public void setHospitalId(Long hospitalId) {
		this.hospitalId = hospitalId;
	}

	public Integer getWill() {
		return will;
	}

	public void setWill(Integer will) {
		this.will = will;
	}

	public String getStageName() {
		return stageName;
	}

	public void setStageName(String stageName) {
		this.stageName = stageName;
	}

	public Integer getAlertStatus() {
		return alertStatus;
	}

	public void setAlertStatus(Integer alertStatus) {
		this.alertStatus = alertStatus;
	}
	
	public List<ZyyOrgVO> getOrgList() {
		return orgList;
	}

	public void setOrgList(List<ZyyOrgVO> orgList) {
		this.orgList = orgList;
	}

	public List<ZyyRecruitResidencyWillVO> getWillList() {
		return willList;
	}

	public void setWillList(List<ZyyRecruitResidencyWillVO> willList) {
		this.willList = willList;
	}

	public List<ZyyRecruitBaseExtendVO> getExtendList() {
		return extendList;
	}

	public void setExtendList(List<ZyyRecruitBaseExtendVO> extendList) {
		this.extendList = extendList;
	}

	public Integer getRowSpan() {
		return rowSpan;
	}

	public void setRowSpan(Integer rowSpan) {
		this.rowSpan = rowSpan;
	}

	public Integer getStageHospital() {
		return stageHospital;
	}

	public void setStageHospital(Integer stageHospital) {
		this.stageHospital = stageHospital;
	}

	public Integer getStageRecruit() {
		return stageRecruit;
	}

	public void setStageRecruit(Integer stageRecruit) {
		this.stageRecruit = stageRecruit;
	}

	public Long getOrgId() {
		return orgId;
	}

	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}

	public List<ZyyRecruitStageVO> getStageList() {
		return stageList;
	}

	public void setStageList(List<ZyyRecruitStageVO> stageList) {
		this.stageList = stageList;
	}

	public void setOrgAliasName(Long orgAliasName) {
		this.orgAliasName = orgAliasName;
	}

	public Long getOrgAliasName() {
		return orgAliasName;
	}

	public void setBaseAliasName(Long baseAliasName) {
		this.baseAliasName = baseAliasName;
	}

	public Long getBaseAliasName() {
		return baseAliasName;
	}

	public Boolean getDuplicate() {
		return duplicate;
	}

	public void setDuplicate(Boolean duplicate) {
		this.duplicate = duplicate;
	}

	public Integer getAllowRepeat() {
		return allowRepeat;
	}

	public void setAllowRepeat(Integer allowRepeat) {
		this.allowRepeat = allowRepeat;
	}

	public Date getStartRecruitDate() {
		return startRecruitDate;
	}

	public void setStartRecruitDate(Date startRecruitDate) {
		this.startRecruitDate = startRecruitDate;
	}

	public Date getEndRecruitDate() {
		return endRecruitDate;
	}

	public void setEndRecruitDate(Date endRecruitDate) {
		this.endRecruitDate = endRecruitDate;
	}

	public ZyyTimeConfigVO getTimeConfig() {
		return timeConfig;
	}

	public void setTimeConfig(ZyyTimeConfigVO timeConfig) {
		this.timeConfig = timeConfig;
	}

}
