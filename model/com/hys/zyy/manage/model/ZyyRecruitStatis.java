package com.hys.zyy.manage.model;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：张伟清 2012-12-07
 * 
 * 描述：招录统计 1.报名统计 2.录取统计 3.缺额统计 4.报到统计 5.综合分析
 * 
 * 说明:
 */
public class ZyyRecruitStatis extends ZyyBaseObject {

	private static final long serialVersionUID = 4198575598395330218L;

	/**
	 * 医院ID 简称：培训基地ID 
	 */
	private Long orgId;

	/**
	 * 医院名称 简称：培训基地名称 
	 */
	private String orgName;

	/**
	 * 医院别名 简称：培训基地别名 
	 */
	private String orgAliasName;

	/**
	 * 医院基地ID 简称：培训专科ID 
	 */
	private Long baseId;

	/**
	 * 标准基地ID 简称：标准培训专科ID
	 */
	private Long baseStdId;

	/**
	 * 标准基地名称 简称：标准培训专科名称
	 */
	private String baseName;

	/**
	 * 标准基地别名 简称：标准培训专科别名
	 */
	private String baseAliasName;

	/**
	 * 本科专业学位人数
	 */
	private Integer undergraduateNumPro ;
	
	/**
	 * 普通本科生
	 */
	private Integer undergraduateNum ;
	
	/**
	 * 硕士
	 */
	private Integer masterNum ; 
	
	/**
	 * 博士
	 */
	private Integer doctorNum ;
	
	/**
	 * 单位人招收总数
	 */
	private Integer entrustTotal ;

	/**
	 * 社会人招收总数
	 */
	private Integer autonomousTotal ;
	
	/**
	 * 计划招生人数-暂定上海
	 */
	private Integer planRecrOne;
	
	/**
	 * 计划招生人数-暂定吉林-单位
	 */
	private Integer planRecrTwo;
	
	/**
	 * 计划招生人数-暂定吉林-社会
	 */
	private Integer planRecrThr;
	
	/**
	 * 招录人数不限
	 */
	private String notLimited ;

	/**
	 * 实际报名人数
	 */
	private Integer practicalRegister;

	/**
	 * 实际录取人数 
	 */
	private Integer practicalAdmission;

	/**
	 * 实际报到人数 
	 */
	private Integer practicalReport;

	/**
	 * 资格审核通过人数
	 */
	private Integer reportQualification;
	/**
	 * 录取资格审核通过人数
	 */
	private Integer reportQualification2ChickIn;

	/**
	 * 本科-推免
	 */
	private Integer graduateTuiMian;

	/**
	 * 本科-统招
	 */
	private Integer graduateTongZhao;

	/**
	 * 本科-其他
	 */
	private Integer graduateOther;

	/**
	 * 硕士-专业
	 */
	private Integer masterSpecialty;

	/**
	 * 硕士-科学
	 */
	private Integer masterScience;

	/**
	 * 博士-专业
	 */
	private Integer doctorSpecialty;

	/**
	 * 博士-科学
	 */
	private Integer doctorScience;

	/**
	 * 毕业情况-应届 
	 */
	private Integer freshGraduate;

	/**
	 * 毕业情况-往届
	 */
	private Integer pastGraduate;

	/**
	 * 生源地-本地 
	 */
	private Integer homePlaceLocal;

	/**
	 * 生源地-外地 
	 */
	private Integer homePlaceOther;

	/**
	 * 毕业院校-本地 
	 */
	private Integer schoolPlaceLocal;

	/**
	 * 毕业院校-外地
	 */
	private Integer schoolPlaceOther;
	
	/**
	 * 调剂
	 */
	private Integer isAdjusted;
	
	/**
	 * 非调剂
	 */
	private Integer isNotAdjusted;
	/**
	 * 本地院校本地生源
	 */
	private Integer schoolLocalHomeLocal;
	/**
	 * 外地院校本地生源
	 */
	private Integer schoolOtherHomeLocal;
	/**
	 * 本地院校外地生源
	 */
	private Integer schoolLocalHomeOther;
	/**
	 * 外地院校外地生源
	 */
	private Integer schoolOtherHomeOther;
	/**
	 * 最高学历 本科
	 */
	private Integer undergraduateCon;
	/**
	 * 最高学历 本科
	 */
	private Integer masterCon;
	/**
	 * 最高学历 本科
	 */
	private Integer doctorCon;
	/**
	 * 缺额统计 最高学历 本科专业
	 */
	private Integer lackUnderCon;
	/**
	 * 缺额统计 最高学历 其他
	 */
	private Integer lackUnderOtherCon;
	/**
	 * 签约单位属性
	 */
	private String hospPro;
	/**
	 * 签约单位
	 */
	private String hospCon;
	/**
	 * 本院委培
	 */
	private Integer isThisOrg;
	/**
	 * 外院委培
	 */
	private Integer isNotThisOrg;
	
	private Integer id;
	/**
	 * 河北用字段  是订单定向生数量
	 */
	private Integer isDirectStu;
	/**
	 * 不是订单定向生 数量
	 */
	private Integer notDirectStu;
	
	public Integer getIsDirectStu() {
		return isDirectStu;
	}

	public void setIsDirectStu(Integer isDirectStu) {
		this.isDirectStu = isDirectStu;
	}

	public Integer getNotDirectStu() {
		return notDirectStu;
	}

	public void setNotDirectStu(Integer notDirectStu) {
		this.notDirectStu = notDirectStu;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getIsThisOrg() {
		return isThisOrg;
	}

	public void setIsThisOrg(Integer isThisOrg) {
		this.isThisOrg = isThisOrg;
	}

	public Integer getIsNotThisOrg() {
		return isNotThisOrg;
	}

	public void setIsNotThisOrg(Integer isNotThisOrg) {
		this.isNotThisOrg = isNotThisOrg;
	}

	public Integer getReportQualification2ChickIn() {
		return reportQualification2ChickIn;
	}

	public void setReportQualification2ChickIn(Integer reportQualification2ChickIn) {
		this.reportQualification2ChickIn = reportQualification2ChickIn;
	}

	public String getHospPro() {
		return hospPro;
	}

	public void setHospPro(String hospPro) {
		this.hospPro = hospPro;
	}

	public String getHospCon() {
		return hospCon;
	}

	public void setHospCon(String hospCon) {
		this.hospCon = hospCon;
	}

	public Integer getLackUnderCon() {
		return lackUnderCon;
	}

	public void setLackUnderCon(Integer lackUnderCon) {
		this.lackUnderCon = lackUnderCon;
	}

	public Integer getLackUnderOtherCon() {
		return lackUnderOtherCon;
	}

	public void setLackUnderOtherCon(Integer lackUnderOtherCon) {
		this.lackUnderOtherCon = lackUnderOtherCon;
	}

	public Integer getUndergraduateCon() {
		return undergraduateCon;
	}

	public void setUndergraduateCon(Integer undergraduateCon) {
		this.undergraduateCon = undergraduateCon;
	}

	public Integer getMasterCon() {
		return masterCon;
	}

	public void setMasterCon(Integer masterCon) {
		this.masterCon = masterCon;
	}

	public Integer getDoctorCon() {
		return doctorCon;
	}

	public void setDoctorCon(Integer doctorCon) {
		this.doctorCon = doctorCon;
	}

	public Integer getSchoolLocalHomeLocal() {
		return schoolLocalHomeLocal;
	}

	public void setSchoolLocalHomeLocal(Integer schoolLocalHomeLocal) {
		this.schoolLocalHomeLocal = schoolLocalHomeLocal;
	}

	public Integer getSchoolOtherHomeLocal() {
		return schoolOtherHomeLocal;
	}

	public void setSchoolOtherHomeLocal(Integer schoolOtherHomeLocal) {
		this.schoolOtherHomeLocal = schoolOtherHomeLocal;
	}

	public Integer getSchoolLocalHomeOther() {
		return schoolLocalHomeOther;
	}

	public void setSchoolLocalHomeOther(Integer schoolLocalHomeOther) {
		this.schoolLocalHomeOther = schoolLocalHomeOther;
	}

	public Integer getSchoolOtherHomeOther() {
		return schoolOtherHomeOther;
	}

	public void setSchoolOtherHomeOther(Integer schoolOtherHomeOther) {
		this.schoolOtherHomeOther = schoolOtherHomeOther;
	}

	public Integer getIsAdjusted() {
		return isAdjusted;
	}

	public void setIsAdjusted(Integer isAdjusted) {
		this.isAdjusted = isAdjusted;
	}

	public Integer getIsNotAdjusted() {
		return isNotAdjusted;
	}

	public void setIsNotAdjusted(Integer isNotAdjusted) {
		this.isNotAdjusted = isNotAdjusted;
	}

	public Long getOrgId() {
		return orgId;
	}

	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public String getOrgAliasName() {
		return orgAliasName;
	}

	public void setOrgAliasName(String orgAliasName) {
		this.orgAliasName = orgAliasName;
	}

	public Long getBaseId() {
		return baseId;
	}

	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}

	public Long getBaseStdId() {
		return baseStdId;
	}

	public void setBaseStdId(Long baseStdId) {
		this.baseStdId = baseStdId;
	}

	public String getBaseName() {
		return baseName;
	}

	public void setBaseName(String baseName) {
		this.baseName = baseName;
	}

	public String getBaseAliasName() {
		return baseAliasName;
	}

	public void setBaseAliasName(String baseAliasName) {
		this.baseAliasName = baseAliasName;
	}

	public Integer getPlanRecrOne() {
		return planRecrOne;
	}

	public void setPlanRecrOne(Integer planRecrOne) {
		this.planRecrOne = planRecrOne;
	}

	public Integer getPlanRecrTwo() {
		return planRecrTwo;
	}

	public void setPlanRecrTwo(Integer planRecrTwo) {
		this.planRecrTwo = planRecrTwo;
	}
	
	public Integer getPracticalRegister() {
		return practicalRegister;
	}

	public void setPracticalRegister(Integer practicalRegister) {
		this.practicalRegister = practicalRegister;
	}

	public Integer getPracticalAdmission() {
		return practicalAdmission;
	}

	public void setPracticalAdmission(Integer practicalAdmission) {
		this.practicalAdmission = practicalAdmission;
	}

	public Integer getPracticalReport() {
		return practicalReport;
	}

	public void setPracticalReport(Integer practicalReport) {
		this.practicalReport = practicalReport;
	}

	public Integer getReportQualification() {
		return reportQualification;
	}

	public void setReportQualification(Integer reportQualification) {
		this.reportQualification = reportQualification;
	}

	public Integer getGraduateTuiMian() {
		return graduateTuiMian;
	}

	public void setGraduateTuiMian(Integer graduateTuiMian) {
		this.graduateTuiMian = graduateTuiMian;
	}

	public Integer getGraduateTongZhao() {
		return graduateTongZhao;
	}

	public void setGraduateTongZhao(Integer graduateTongZhao) {
		this.graduateTongZhao = graduateTongZhao;
	}

	public Integer getGraduateOther() {
		return graduateOther;
	}

	public void setGraduateOther(Integer graduateOther) {
		this.graduateOther = graduateOther;
	}

	public Integer getMasterSpecialty() {
		return masterSpecialty;
	}

	public void setMasterSpecialty(Integer masterSpecialty) {
		this.masterSpecialty = masterSpecialty;
	}

	public Integer getMasterScience() {
		return masterScience;
	}

	public void setMasterScience(Integer masterScience) {
		this.masterScience = masterScience;
	}

	public Integer getDoctorSpecialty() {
		return doctorSpecialty;
	}

	public void setDoctorSpecialty(Integer doctorSpecialty) {
		this.doctorSpecialty = doctorSpecialty;
	}

	public Integer getDoctorScience() {
		return doctorScience;
	}

	public void setDoctorScience(Integer doctorScience) {
		this.doctorScience = doctorScience;
	}

	public Integer getFreshGraduate() {
		return freshGraduate;
	}

	public void setFreshGraduate(Integer freshGraduate) {
		this.freshGraduate = freshGraduate;
	}

	public Integer getPastGraduate() {
		return pastGraduate;
	}

	public void setPastGraduate(Integer pastGraduate) {
		this.pastGraduate = pastGraduate;
	}

	public Integer getHomePlaceLocal() {
		return homePlaceLocal;
	}

	public void setHomePlaceLocal(Integer homePlaceLocal) {
		this.homePlaceLocal = homePlaceLocal;
	}

	public Integer getHomePlaceOther() {
		return homePlaceOther;
	}

	public void setHomePlaceOther(Integer homePlaceOther) {
		this.homePlaceOther = homePlaceOther;
	}

	public Integer getSchoolPlaceLocal() {
		return schoolPlaceLocal;
	}

	public void setSchoolPlaceLocal(Integer schoolPlaceLocal) {
		this.schoolPlaceLocal = schoolPlaceLocal;
	}

	public Integer getSchoolPlaceOther() {
		return schoolPlaceOther;
	}

	public void setSchoolPlaceOther(Integer schoolPlaceOther) {
		this.schoolPlaceOther = schoolPlaceOther;
	}

	public static long getSerialVersionUID() {
		return serialVersionUID;
	}

	public Integer getPlanRecrThr() {
		return planRecrThr;
	}

	public void setPlanRecrThr(Integer planRecrThr) {
		this.planRecrThr = planRecrThr;
	}

	public Integer getUndergraduateNumPro() {
		return undergraduateNumPro;
	}

	public void setUndergraduateNumPro(Integer undergraduateNumPro) {
		this.undergraduateNumPro = undergraduateNumPro;
	}

	public Integer getUndergraduateNum() {
		return undergraduateNum;
	}

	public void setUndergraduateNum(Integer undergraduateNum) {
		this.undergraduateNum = undergraduateNum;
	}

	public Integer getMasterNum() {
		return masterNum;
	}

	public void setMasterNum(Integer masterNum) {
		this.masterNum = masterNum;
	}

	public Integer getDoctorNum() {
		return doctorNum;
	}

	public void setDoctorNum(Integer doctorNum) {
		this.doctorNum = doctorNum;
	}

	public Integer getEntrustTotal() {
		return entrustTotal;
	}

	public void setEntrustTotal(Integer entrustTotal) {
		this.entrustTotal = entrustTotal;
	}

	public Integer getAutonomousTotal() {
		return autonomousTotal;
	}

	public void setAutonomousTotal(Integer autonomousTotal) {
		this.autonomousTotal = autonomousTotal;
	}

	public String getNotLimited() {
		return notLimited;
	}

	public void setNotLimited(String notLimited) {
		this.notLimited = notLimited;
	}
}
