package com.hys.zyy.manage.model;

public class ZyyRecruitStatistics extends ZyyBaseObject {

	/**
	 * 
	 */
	private static final long serialVersionUID = 600501911770625675L;
	
	private Long orgId; // 培训基地ID
	private Integer hospType; //中西医类型 1中医 2西医 3中西医
	private Long baseId; // 专业基地ID
	private Long baseStdId; // 专业ID
	private Long yearId; // 招录年份
	private Long zyyUserProvinceId;//年份
	private Integer planCount = 0;// 计划招录人数
	private Integer stageId;// 招录阶段
	private String signupPersonEntrust;// 报名人ID
	private String signupPersonSocial;// 报名人ID
	private String signupPersonTotal;// 报名人ID
	private Integer signupTimesEntrust = 0;// 报名次数（单位人）
	private Integer signupTimesSocial = 0;// 报名次数（社会人）
	private Integer signupTimesTotal = 0;// 报名次数（合计）
	private Integer recruitCountEntrust = 0;// 录取人数（单位人）
	private Integer recruitCountSocial = 0;// 录取人数（社会人）
	private Integer recruitCountTotal = 0;// 录取人数（合计）
	private Integer signedCountEntrust = 0;// 报到人数（单位人）
	private Integer signedCountSocial = 0;// 报到人数（社会人）
	private Integer signedCountTotal = 0;// 报到人数（合计）
	
	public Long getOrgId() {
		return orgId;
	}
	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}
	public Long getBaseId() {
		return baseId;
	}
	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}
	public Long getBaseStdId() {
		return baseStdId;
	}
	public void setBaseStdId(Long baseStdId) {
		this.baseStdId = baseStdId;
	}
	public Long getYearId() {
		return yearId;
	}
	public void setYearId(Long yearId) {
		this.yearId = yearId;
	}
	public Integer getPlanCount() {
		return planCount;
	}
	public void setPlanCount(Integer planCount) {
		this.planCount = planCount;
	}
	public Integer getStageId() {
		return stageId;
	}
	public void setStageId(Integer stageId) {
		this.stageId = stageId;
	}
	public Integer getSignupTimesEntrust() {
		return signupTimesEntrust;
	}
	public void setSignupTimesEntrust(Integer signupTimesEntrust) {
		this.signupTimesEntrust = signupTimesEntrust;
	}
	public Integer getSignupTimesSocial() {
		return signupTimesSocial;
	}
	public void setSignupTimesSocial(Integer signupTimesSocial) {
		this.signupTimesSocial = signupTimesSocial;
	}
	public Integer getSignupTimesTotal() {
		return signupTimesTotal;
	}
	public void setSignupTimesTotal(Integer signupTimesTotal) {
		this.signupTimesTotal = signupTimesTotal;
	}
	public Integer getRecruitCountEntrust() {
		return recruitCountEntrust;
	}
	public void setRecruitCountEntrust(Integer recruitCountEntrust) {
		this.recruitCountEntrust = recruitCountEntrust;
	}
	public Integer getRecruitCountSocial() {
		return recruitCountSocial;
	}
	public void setRecruitCountSocial(Integer recruitCountSocial) {
		this.recruitCountSocial = recruitCountSocial;
	}
	public Integer getRecruitCountTotal() {
		return recruitCountTotal;
	}
	public void setRecruitCountTotal(Integer recruitCountTotal) {
		this.recruitCountTotal = recruitCountTotal;
	}
	public Integer getSignedCountEntrust() {
		return signedCountEntrust;
	}
	public void setSignedCountEntrust(Integer signedCountEntrust) {
		this.signedCountEntrust = signedCountEntrust;
	}
	public Integer getSignedCountSocial() {
		return signedCountSocial;
	}
	public void setSignedCountSocial(Integer signedCountSocial) {
		this.signedCountSocial = signedCountSocial;
	}
	public Integer getSignedCountTotal() {
		return signedCountTotal;
	}
	public void setSignedCountTotal(Integer signedCountTotal) {
		this.signedCountTotal = signedCountTotal;
	}
	public Long getZyyUserProvinceId() {
		return zyyUserProvinceId;
	}
	public void setZyyUserProvinceId(Long zyyUserProvinceId) {
		this.zyyUserProvinceId = zyyUserProvinceId;
	}
	public Integer getHospType() {
		return hospType;
	}
	public void setHospType(Integer hospType) {
		this.hospType = hospType;
	}
	public String getSignupPersonEntrust() {
		return signupPersonEntrust;
	}
	public void setSignupPersonEntrust(String signupPersonEntrust) {
		this.signupPersonEntrust = signupPersonEntrust;
	}
	public String getSignupPersonSocial() {
		return signupPersonSocial;
	}
	public void setSignupPersonSocial(String signupPersonSocial) {
		this.signupPersonSocial = signupPersonSocial;
	}
	public String getSignupPersonTotal() {
		return signupPersonTotal;
	}
	public void setSignupPersonTotal(String signupPersonTotal) {
		this.signupPersonTotal = signupPersonTotal;
	}
}
