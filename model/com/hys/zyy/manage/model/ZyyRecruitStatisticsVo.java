package com.hys.zyy.manage.model;

import java.util.ArrayList;
import java.util.List;

public class ZyyRecruitStatisticsVo extends ZyyRecruitStatistics {

	/**
	 * 
	 */
	private static final long serialVersionUID = -7965961404999538122L;
	
	private String orgName;//培训基地ID
	private String baseName;//专业基地ID
	
	private Integer signupCountEntrust = 0;
	private Integer signupCountSocial = 0;
	private Integer signupCountTotal = 0;
	
	private Integer viewBy;//查看方式 1培训基地方式 2专业基地方式
	
	private Integer rowSpan=0;
	private Integer stdRowSpan = 0;
	
	public String getOrgName() {
		return orgName;
	}
	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}
	public String getBaseName() {
		return baseName;
	}
	public void setBaseName(String baseName) {
		this.baseName = baseName;
	}
	public Integer getViewBy() {
		return viewBy;
	}
	public void setViewBy(Integer viewBy) {
		this.viewBy = viewBy;
	}
	public Integer getRowSpan() {
		return rowSpan;
	}
	public void setRowSpan(Integer rowSpan) {
		this.rowSpan = rowSpan;
	}
	public Integer getSignupCountEntrust() {
		return signupCountEntrust;
	}
	public void setSignupCountEntrust(Integer signupCountEntrust) {
		this.signupCountEntrust = signupCountEntrust;
	}
	public Integer getSignupCountSocial() {
		return signupCountSocial;
	}
	public void setSignupCountSocial(Integer signupCountSocial) {
		this.signupCountSocial = signupCountSocial;
	}
	public Integer getSignupCountTotal() {
		return signupCountTotal;
	}
	public void setSignupCountTotal(Integer signupCountTotal) {
		this.signupCountTotal = signupCountTotal;
	}
	public Integer getStdRowSpan() {
		return stdRowSpan;
	}
	public void setStdRowSpan(Integer stdRowSpan) {
		this.stdRowSpan = stdRowSpan;
	}
	
}
