package com.hys.zyy.manage.model;

import java.io.Serializable;
import java.util.Date;

/**
 * 志愿调剂同意书
 */
public class ZyyRecruitWillAdjustFile implements Serializable {
	private static final long serialVersionUID = 1L;
	/*
	 * ID
	 */
	private String id;
	/*
	 * 学员ID
	 */
	private Long residencyId;
	/*
	 * 招录阶段ID
	 */
	private Long zyyRecruitStageId;
	/*
	 * 文件名称
	 */
	private String fileName;
	/*
	 * 状态（-1：失效；1：有效）
	 */
	private Integer state;
	/*
	 * 文件路径
	 */
	private String filePath;

	private Date createTime;
	private Date updateTime;

	public ZyyRecruitWillAdjustFile() {
		super();
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public Long getResidencyId() {
		return residencyId;
	}

	public void setResidencyId(Long residencyId) {
		this.residencyId = residencyId;
	}

	public Long getZyyRecruitStageId() {
		return zyyRecruitStageId;
	}

	public void setZyyRecruitStageId(Long zyyRecruitStageId) {
		this.zyyRecruitStageId = zyyRecruitStageId;
	}

	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	public String getFilePath() {
		return filePath;
	}

	public void setFilePath(String filePath) {
		this.filePath = filePath;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ZyyRecruitWillAdjustFile other = (ZyyRecruitWillAdjustFile) obj;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		return true;
	}

	@Override
	public String toString() {
		return "ZyyRecruitWillAdjustFile [id=" + id + ", residencyId="
				+ residencyId + ", zyyRecruitStageId=" + zyyRecruitStageId
				+ ", fileName=" + fileName + ", state=" + state + ", filePath="
				+ filePath + ", createTime=" + createTime + ", updateTime="
				+ updateTime + "]";
	}

}
