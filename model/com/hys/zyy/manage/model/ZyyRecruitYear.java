package com.hys.zyy.manage.model;

/**
 * 标题：zyy
 * 
 * 作者：Tony Mar 19, 2012
 * 
 * 描述：
 * 
 * 说明:
 */
public class ZyyRecruitYear extends ZyyBaseObject {

	private static final long serialVersionUID = 1086639810689555503L;

	private Long id;

	private Long orgId;

	private String year;

	private Integer status;

	public ZyyRecruitYear() {
	}

	public ZyyRecruitYear(Long orgId, Integer status) {
		super();
		this.orgId = orgId;
		this.status = status;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getOrgId() {
		return orgId;
	}

	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}

	public String getYear() {
		return year;
	}

	public void setYear(String year) {
		this.year = year;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	@Override
	public String toString() {
		return "ZyyRecruitYear [id=" + id + ", orgId=" + orgId + ", year=" + year + ", status=" + status + "]";
	}

}
