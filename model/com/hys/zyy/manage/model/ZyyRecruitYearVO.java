package com.hys.zyy.manage.model;

import java.util.List;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：张伟清 2012-04-18
 * 
 * 描述：
 * 
 * 说明:
 */
public class ZyyRecruitYearVO extends ZyyRecruitYear {

	private static final long serialVersionUID = 5003414993472694300L;

	/**
	 * 志愿列表
	 */
	private List<ZyyRecruitResidencyWillVO> willList ;
	
	/**
	 * 阶段列表
	 */
	private List<ZyyRecruitStageVO> stageList ;
	
	/**
	 * 横跨行数
	 */
	private Integer rowSpan ;
	
	/**
	 * 住院医师报名录取资格
	 */
	private ZyyResidentQualification resiQual ; 

	public ZyyResidentQualification getResiQual() {
		return resiQual;
	}

	public void setResiQual(ZyyResidentQualification resiQual) {
		this.resiQual = resiQual;
	}

	public List<ZyyRecruitResidencyWillVO> getWillList() {
		return willList;
	}

	public void setWillList(List<ZyyRecruitResidencyWillVO> willList) {
		this.willList = willList;
	}

	public List<ZyyRecruitStageVO> getStageList() {
		return stageList;
	}

	public void setStageList(List<ZyyRecruitStageVO> stageList) {
		this.stageList = stageList;
	}

	public Integer getRowSpan() {
		return rowSpan;
	}

	public void setRowSpan(Integer rowSpan) {
		this.rowSpan = rowSpan;
	}
}
