package com.hys.zyy.manage.model;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：陈明凯 2012-7-11
 * 
 * 描述：
 * 
 * 说明:
 */
public class ZyyRegion extends ZyyBaseObject {

	private static final long serialVersionUID = -4185278043278228915L;

	/**
	 * 域名信息
	 */
	private String domainName;

	/**
	 * 省厅组织机构ID
	 */
	private Long orgId;

	/**
	 * 组织机构名称
	 */
	private String orgName;

	/**
	 * 招录类别 1.上海 2.北京 3.吉林
	 */
	private Integer recruitType;
	
	/**
	 * 审核类别 1.逐级审核 2.无限制审核
	 */
	private Integer checkType ;
	
	/**
	 * 2013/03/12
	 * 王云龙暂时添加,区分吉林中医属性 0.非吉林中医  1.吉林中医
	 */
	private Integer hospType;

	public ZyyRegion() {
		super();
	}

	public ZyyRegion(Long orgId) {
		super();
		this.orgId = orgId;
	}

	public Integer getHospType() {
		return hospType;
	}

	public void setHospType(Integer hospType) {
		this.hospType = hospType;
	}

	public Integer getRecruitType() {
		return recruitType;
	}

	public void setRecruitType(Integer recruitType) {
		this.recruitType = recruitType;
	}

	public String getDomainName() {
		return domainName;
	}

	public void setDomainName(String domainName) {
		this.domainName = domainName;
	}

	public Long getOrgId() {
		return orgId;
	}

	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public Integer getCheckType() {
		return checkType;
	}

	public void setCheckType(Integer checkType) {
		this.checkType = checkType;
	}
}
