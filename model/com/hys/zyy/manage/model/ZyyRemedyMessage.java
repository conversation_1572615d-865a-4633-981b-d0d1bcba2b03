package com.hys.zyy.manage.model;

import java.util.Date;
import java.util.List;

/**
 * 
 * 标题：zyy
 * 
 * 作者：Tony Apr 5, 2012
 * 
 * 描述：
 * 
 * 说明:
 */
public class ZyyRemedyMessage extends ZyyBaseObject {

	/**
	 * 
	 */
	private static final long serialVersionUID = -5859051122782168228L;

	private Long id;
	private Long residencyId;//住院医师ID_报名表修改
	private Long remedyType;//1 -报名表修改 2 -无法注册
	private Date applyDate;
	private String applyName;//真实姓名
	private String applyContactNumber;//联系电话
	private String applyIdCard;//身份证号
	private String applyEmail;//电子邮箱
	private String applyReason;//申请原由
	private Long status=0L;//0 -提交状态 1 -审核通过 2 -审核不通过
	private Long lastUpdateUserId;//更新人
	private Date lastUpdateDate;//更新时间
	private String applyNoReason;//审核意见
	private Long zyyOrgId;//审核机构省机构

	public Long getZyyOrgId() {
		return zyyOrgId;
	}
	public void setZyyOrgId(Long zyyOrgId) {
		this.zyyOrgId = zyyOrgId;
	}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getResidencyId() {
		return residencyId;
	}
	public void setResidencyId(Long residencyId) {
		this.residencyId = residencyId;
	}
	public Long getLastUpdateUserId() {
		return lastUpdateUserId;
	}
	public void setLastUpdateUserId(Long lastUpdateUserId) {
		this.lastUpdateUserId = lastUpdateUserId;
	}
	public Long getRemedyType() {
		return remedyType;
	}
	public void setRemedyType(Long remedyType) {
		this.remedyType = remedyType;
	}
	public Date getApplyDate() {
		return applyDate;
	}
	public void setApplyDate(Date applyDate) {
		this.applyDate = applyDate;
	}
	public String getApplyName() {
		return applyName;
	}
	public void setApplyName(String applyName) {
		this.applyName = applyName;
	}
	public String getApplyContactNumber() {
		return applyContactNumber;
	}
	public void setApplyContactNumber(String applyContactNumber) {
		this.applyContactNumber = applyContactNumber;
	}
	public String getApplyIdCard() {
		return applyIdCard;
	}
	public void setApplyIdCard(String applyIdCard) {
		this.applyIdCard = applyIdCard;
	}
	public String getApplyEmail() {
		return applyEmail;
	}
	public void setApplyEmail(String applyEmail) {
		this.applyEmail = applyEmail;
	}
	public String getApplyReason() {
		return applyReason;
	}
	public void setApplyReason(String applyReason) {
		this.applyReason = applyReason;
	}
	public Long getStatus() {
		return status;
	}
	public void setStatus(Long status) {
		this.status = status;
	}
	public Date getLastUpdateDate() {
		return lastUpdateDate;
	}
	public void setLastUpdateDate(Date lastUpdateDate) {
		this.lastUpdateDate = lastUpdateDate;
	}
	public String getApplyNoReason() {
		return applyNoReason;
	}
	public void setApplyNoReason(String applyNoReason) {
		this.applyNoReason = applyNoReason;
	}
	
}
