package com.hys.zyy.manage.model;

import com.hys.zyy.manage.action.reg.viewmodel.ManualVerifyPageQuery;

public class ZyyResiHbAuditRecordVO extends ManualVerifyPageQuery {
	private static final long serialVersionUID = 1L;
	/*
	 * 培训基地
	 */
	private String hospitalName;
	/*
	 * 培训年级
	 */
	private String yearName;
	/*
	 * 人员类型
	 */
	private String residencySourceStr;
	/*
	 * 培训年限
	 */
	private String schoolSystemStr;
	/*
	 * 学员姓名
	 */
	private String realName;
	/*
	 * 证件号码
	 */
	private String certificateNo;
	/*
	 * 科室名称
	 */
	private String deptName;
	/*
	 * 登记手册名称
	 */
	private String handBookName;
	/*
	 * 实际轮转科室
	 */
	private String factCycleDept;
	/*
	 * 轮转时间
	 */
	private String cycleTimeStr;
	/*
	 * 带教时间
	 */
	private String teacherTimeStr;
	/*
	 * 轮转部分提交状态
	 */
	private String cycleModuleSubmitStateStr;
	/*
	 * 轮转部分带教审核
	 */
	private String cycleModuleTeacherAuditStr;
	/*
	 * 轮转部分科室审核
	 */
	private String cycleModuleDeptAuditStr;
	/*
	 * 轮转部分基地审核
	 */
	private String cycleModuleBaseAuditStr;
	/*
	 * 轮转部分医院审核
	 */
	private String cycleModuleHospitalAuditStr;
	/*
	 * 其它部分提交状态
	 */
	private String otherModuleSubmitStateStr;
	/*
	 * 其它部分带教审核
	 */
	private String otherModuleTeacherAuditStr;
	/*
	 * 其它部分科室审核
	 */
	private String otherModuleDeptAuditStr;
	/*
	 * 其它部分基地审核
	 */
	private String otherModuleBaseAuditStr;
	/*
	 * 其它部分医院审核
	 */
	private String otherModuleHospitalAuditStr;
	/*
	 * 终审
	 */
	private String finalAuditStr;

	public ZyyResiHbAuditRecordVO() {
		super();
	}

	public String getHospitalName() {
		return hospitalName;
	}

	public void setHospitalName(String hospitalName) {
		this.hospitalName = hospitalName == null ? null : hospitalName.trim();
	}

	public String getResidencySourceStr() {
		return residencySourceStr;
	}

	public void setResidencySourceStr(String residencySourceStr) {
		this.residencySourceStr = residencySourceStr == null ? null : residencySourceStr.trim();
	}

	public String getYearName() {
		return yearName;
	}

	public void setYearName(String yearName) {
		this.yearName = yearName == null ? null : yearName.trim();
	}

	public String getSchoolSystemStr() {
		return schoolSystemStr;
	}

	public void setSchoolSystemStr(String schoolSystemStr) {
		this.schoolSystemStr = schoolSystemStr == null ? null : schoolSystemStr.trim();
	}

	public String getRealName() {
		return realName;
	}

	public void setRealName(String realName) {
		this.realName = realName == null ? null : realName.trim();
	}

	public String getCertificateNo() {
		return certificateNo;
	}

	public void setCertificateNo(String certificateNo) {
		this.certificateNo = certificateNo == null ? null : certificateNo.trim();
	}

	public String getDeptName() {
		return deptName;
	}

	public void setDeptName(String deptName) {
		this.deptName = deptName == null ? null : deptName.trim();
	}

	public String getHandBookName() {
		return handBookName;
	}

	public void setHandBookName(String handBookName) {
		this.handBookName = handBookName == null ? null : handBookName.trim();
	}

	public String getFactCycleDept() {
		return factCycleDept;
	}

	public void setFactCycleDept(String factCycleDept) {
		this.factCycleDept = factCycleDept == null ? null : factCycleDept.trim();
	}

	public String getCycleTimeStr() {
		return cycleTimeStr;
	}

	public void setCycleTimeStr(String cycleTimeStr) {
		this.cycleTimeStr = cycleTimeStr == null ? null : cycleTimeStr.trim();
	}

	public String getTeacherTimeStr() {
		return teacherTimeStr;
	}

	public void setTeacherTimeStr(String teacherTimeStr) {
		this.teacherTimeStr = teacherTimeStr == null ? null : teacherTimeStr.trim();
	}

	public String getCycleModuleSubmitStateStr() {
		return cycleModuleSubmitStateStr;
	}

	public void setCycleModuleSubmitStateStr(String cycleModuleSubmitStateStr) {
		this.cycleModuleSubmitStateStr = cycleModuleSubmitStateStr == null ? null : cycleModuleSubmitStateStr.trim();
	}

	public String getCycleModuleTeacherAuditStr() {
		return cycleModuleTeacherAuditStr;
	}

	public void setCycleModuleTeacherAuditStr(String cycleModuleTeacherAuditStr) {
		this.cycleModuleTeacherAuditStr = cycleModuleTeacherAuditStr == null ? null : cycleModuleTeacherAuditStr.trim();
	}

	public String getCycleModuleDeptAuditStr() {
		return cycleModuleDeptAuditStr;
	}

	public void setCycleModuleDeptAuditStr(String cycleModuleDeptAuditStr) {
		this.cycleModuleDeptAuditStr = cycleModuleDeptAuditStr == null ? null : cycleModuleDeptAuditStr.trim();
	}

	public String getCycleModuleBaseAuditStr() {
		return cycleModuleBaseAuditStr;
	}

	public void setCycleModuleBaseAuditStr(String cycleModuleBaseAuditStr) {
		this.cycleModuleBaseAuditStr = cycleModuleBaseAuditStr == null ? null : cycleModuleBaseAuditStr.trim();
	}

	public String getCycleModuleHospitalAuditStr() {
		return cycleModuleHospitalAuditStr;
	}

	public void setCycleModuleHospitalAuditStr(String cycleModuleHospitalAuditStr) {
		this.cycleModuleHospitalAuditStr = cycleModuleHospitalAuditStr == null ? null : cycleModuleHospitalAuditStr.trim();
	}

	public String getOtherModuleSubmitStateStr() {
		return otherModuleSubmitStateStr;
	}

	public void setOtherModuleSubmitStateStr(String otherModuleSubmitStateStr) {
		this.otherModuleSubmitStateStr = otherModuleSubmitStateStr == null ? null : otherModuleSubmitStateStr.trim();
	}

	public String getOtherModuleTeacherAuditStr() {
		return otherModuleTeacherAuditStr;
	}

	public void setOtherModuleTeacherAuditStr(String otherModuleTeacherAuditStr) {
		this.otherModuleTeacherAuditStr = otherModuleTeacherAuditStr == null ? null : otherModuleTeacherAuditStr.trim();
	}

	public String getOtherModuleDeptAuditStr() {
		return otherModuleDeptAuditStr;
	}

	public void setOtherModuleDeptAuditStr(String otherModuleDeptAuditStr) {
		this.otherModuleDeptAuditStr = otherModuleDeptAuditStr == null ? null : otherModuleDeptAuditStr.trim();
	}

	public String getOtherModuleBaseAuditStr() {
		return otherModuleBaseAuditStr;
	}

	public void setOtherModuleBaseAuditStr(String otherModuleBaseAuditStr) {
		this.otherModuleBaseAuditStr = otherModuleBaseAuditStr == null ? null : otherModuleBaseAuditStr.trim();
	}

	public String getOtherModuleHospitalAuditStr() {
		return otherModuleHospitalAuditStr;
	}

	public void setOtherModuleHospitalAuditStr(String otherModuleHospitalAuditStr) {
		this.otherModuleHospitalAuditStr = otherModuleHospitalAuditStr == null ? null : otherModuleHospitalAuditStr.trim();
	}

	public String getFinalAuditStr() {
		return finalAuditStr;
	}

	public void setFinalAuditStr(String finalAuditStr) {
		this.finalAuditStr = finalAuditStr == null ? null : finalAuditStr.trim();
	}

}
