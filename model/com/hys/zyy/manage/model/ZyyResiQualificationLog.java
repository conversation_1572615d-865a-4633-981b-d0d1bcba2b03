package com.hys.zyy.manage.model;

import java.util.Date;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：张伟清 2010-07-10
 * 
 * 描述：住院医师报名录取资格记录
 * 
 * 说明:
 */
public class ZyyResiQualificationLog extends ZyyBaseObject {

	private static final long serialVersionUID = 5683183475816529104L;

	/**
	 * 主键ID
	 */
	private Long id ;
	
	/**
	 * 住院医师报名录取资格ID
	 */
	private Long residentQualificationId ;
	
	/**
	 * 审核类别 1.报名资格审核 2.录取资格审核
	 */
	private Integer checkType ;
	
	/**
	 * 审核人ID
	 */
	private Long checkUserId ;
	
	/**
	 * 审核时间
	 */
	private Date checkDate ;
	
	/**
	 * 审核状态
	 */
	private Integer checkStatus ;
	
	/**
	 * 审核备注
	 */
	private String checkRemark ;
	
	/**
	 * 用户类别
	 */
	private Integer zyyUserType ;
	
	/**
	 * 用户组织机构ID
	 */
	private Long zyyUserOrgId ;
	
	/**
	 * 用户ID
	 */
	private Long zyyUserId ;
	
	/**
	 * 阶段ID
	 */
	private Long stageId ;
	
	/**
	 * 审核级别
	 */
	private Integer checkLevel ;
	
	/**
	 * 是否同一审核者 0.不相同 1.相同
	 */
	private Integer sameAuditUser ;
	
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getResidentQualificationId() {
		return residentQualificationId;
	}

	public void setResidentQualificationId(Long residentQualificationId) {
		this.residentQualificationId = residentQualificationId;
	}

	public Integer getCheckType() {
		return checkType;
	}

	public void setCheckType(Integer checkType) {
		this.checkType = checkType;
	}

	public Long getCheckUserId() {
		return checkUserId;
	}

	public void setCheckUserId(Long checkUserId) {
		this.checkUserId = checkUserId;
	}

	public Date getCheckDate() {
		return checkDate;
	}

	public void setCheckDate(Date checkDate) {
		this.checkDate = checkDate;
	}

	public Integer getCheckStatus() {
		return checkStatus;
	}

	public void setCheckStatus(Integer checkStatus) {
		this.checkStatus = checkStatus;
	}

	public String getCheckRemark() {
		return checkRemark;
	}

	public void setCheckRemark(String checkRemark) {
		this.checkRemark = checkRemark;
	}

	public Integer getZyyUserType() {
		return zyyUserType;
	}

	public void setZyyUserType(Integer zyyUserType) {
		this.zyyUserType = zyyUserType;
	}

	public Long getZyyUserOrgId() {
		return zyyUserOrgId;
	}

	public void setZyyUserOrgId(Long zyyUserOrgId) {
		this.zyyUserOrgId = zyyUserOrgId;
	}

	public Long getZyyUserId() {
		return zyyUserId;
	}

	public void setZyyUserId(Long zyyUserId) {
		this.zyyUserId = zyyUserId;
	}

	public Long getStageId() {
		return stageId;
	}

	public void setStageId(Long stageId) {
		this.stageId = stageId;
	}

	public Integer getCheckLevel() {
		return checkLevel;
	}

	public void setCheckLevel(Integer checkLevel) {
		this.checkLevel = checkLevel;
	}

	public Integer getSameAuditUser() {
		return sameAuditUser;
	}

	public void setSameAuditUser(Integer sameAuditUser) {
		this.sameAuditUser = sameAuditUser;
	}
}
