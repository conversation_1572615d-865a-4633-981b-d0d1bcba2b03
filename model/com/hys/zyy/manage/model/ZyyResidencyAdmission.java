package com.hys.zyy.manage.model;

import java.util.Date;

import com.hys.zyy.manage.constants.Constants;
import com.hys.zyy.manage.util.annotation.Column;
import com.hys.zyy.manage.util.annotation.Table;

/**
 * 
 *	住院医师准考证信息实体类     
 *    
 * <AUTHOR>      
 * @version 1.0    
 * @created 2012-9-7 下午05:10:10
 */
public class ZyyResidencyAdmission extends ZyyBaseObject {



	/**  描述  */    
	
	private static final long serialVersionUID = -4673021333780321299L;

	/**
	 * 主键ID
	 */
	private Long id ;
	
	/**
	 * 用户ID
	 */
	private Long zyyUserId ;
	
	/**
	 * 年度ID
	 */
	private Long recruitYearId ;
	
	/**
	 * 机构ID
	 */
	private Long zyyOrgId ;

	/**
	 * 准考证号
	 */
	private String admissionNumber ;
	
	/**
	 * 考点
	 */
	private String examSite	 ;
	
	/**
	 * 时间
	 */
	private Date examDate ;
	
	/**
	 * 考试结束时间
	 */
	private Date examEndDate;

	/**
	 * 地点
	 */
	private String examLocation ;
	
	/**
	 * 创建人
	 */
	private String examSubject ;
	
	/**
	 * 状态:0-未发布  1-发布
	 */
	private Integer status;
	
	/**
	 * 考试结束时间
	 */
	private Date pubDate;
	
	/**
	 * 考场
	 */
	private String examRoom;
	
	/**
	 * 座位号
	 */
	private String seatNumber;
	
	/**
	 * 科目ID
	 */
	private Long examSubjectId;

	/**
	 * 关联ZYY_RECRUIT_STAGE表，表示报名阶段
	 */
	private Long recruitStageId;
	
	private String statusString;

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getZyyUserId() {
		return zyyUserId;
	}

	public void setZyyUserId(Long zyyUserId) {
		this.zyyUserId = zyyUserId;
	}

	public Long getRecruitYearId() {
		return recruitYearId;
	}

	public void setRecruitYearId(Long recruitYearId) {
		this.recruitYearId = recruitYearId;
	}

	public String getAdmissionNumber() {
		return admissionNumber;
	}

	public void setAdmissionNumber(String admissionNumber) {
		this.admissionNumber = admissionNumber;
	}

	public String getExamSite() {
		return examSite;
	}

	public void setExamSite(String examSite) {
		this.examSite = examSite;
	}

	public Long getZyyOrgId() {
		return zyyOrgId;
	}

	public void setZyyOrgId(Long zyyOrgId) {
		this.zyyOrgId = zyyOrgId;
	}

	public Date getExamDate() {
		return examDate;
	}

	public void setExamDate(Date examDate) {
		this.examDate = examDate;
	}

	public String getExamLocation() {
		return examLocation;
	}

	public void setExamLocation(String examLocation) {
		this.examLocation = examLocation;
	}

	public String getExamSubject() {
		return examSubject;
	}

	public void setExamSubject(String examSubject) {
		this.examSubject = examSubject;
	}
	

	public String getStatusString() {
		if(status != null ){
			switch(getStatus()){
				case Constants.ZYY_RESIDENCY_ADMISSION_STATUS_UNRELEASE:
					this.statusString = "未发布";
					break;
				case Constants.ZYY_RESIDENCY_ADMISSION_STATUS_RELEASE:
					this.statusString = "已发布";
					break;
				default:this.statusString="未知";
			}
			return this.statusString;
		}
		return "";
	}

	public void setStatusString(String statusString) {
		this.statusString = statusString;
	}
	

	public Date getExamEndDate() {
		return examEndDate;
	}

	public void setExamEndDate(Date examEndDate) {
		this.examEndDate = examEndDate;
	}
	
	public Date getPubDate() {
		return pubDate;
	}

	public void setPubDate(Date pubDate) {
		this.pubDate = pubDate;
	}

	public String getExamRoom() {
		return examRoom;
	}

	public void setExamRoom(String examRoom) {
		this.examRoom = examRoom;
	}

	public String getSeatNumber() {
		return seatNumber;
	}

	public void setSeatNumber(String seatNumber) {
		this.seatNumber = seatNumber;
	}

	public Long getExamSubjectId() {
		return examSubjectId;
	}

	public void setExamSubjectId(Long examSubjectId) {
		this.examSubjectId = examSubjectId;
	}

	public Long getRecruitStageId() {
		return recruitStageId;
	}

	public void setRecruitStageId(Long recruitStageId) {
		this.recruitStageId = recruitStageId;
	}
	
	
}
