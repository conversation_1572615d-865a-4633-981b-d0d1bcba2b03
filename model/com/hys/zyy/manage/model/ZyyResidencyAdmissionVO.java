package com.hys.zyy.manage.model;

import java.util.List;

import com.hys.zyy.manage.constants.Constants;

/**
 * 住院医师准考证信息表单实体对象
 *     
 *    
 * <AUTHOR>      
 * @version 1.0    
 * @created 2012-9-10 下午02:26:09
 */
public class ZyyResidencyAdmissionVO {

	/**  描述  */    
	
	private static final long serialVersionUID = -4732652885857830754L;
	
	/**
	 * 主键ID
	 */
	private Long id ;
	
	/**
	 * 用户ID
	 */
	private Long zyyUserId ;
	
	/**
	 * 年度ID
	 */
	private Long recruitYearId ;
	
	/**
	 * 机构ID
	 */
	private Long zyyOrgId ;
	
	/**
	 * 姓名
	 */
	private String realName;
	
	/**
	 * 性别 1-男,2-女
	 */
	private Integer sex;
	
	private String sexString;
	
	/**
	 * 人员属性 1-单位人,2-社会人
	 */
	private Integer residencySource;
	
	private String residencySourceString;
	
	/**
	 * 毕业院校
	 */
	private String highestGraduateSchool;
	

	/**
	 * 最高学历 1-大学专科,2-大学本科,3-硕士研究生,4-博士研究生,5-博士后
	 */
	private Integer highestRecordSchool;
	
	private String highestRecordSchoolString;
	
	/**
	 * 证件类型 1-身份证,2-军官证,0-其他,3-护照
	 */
	private Integer certificateType;
	
	private String certificateTypeString;


	/**
	 * 证件号码
	 */
	private String certificateNo;
	
	/**
	 * @desc 考试阶段
	 */
	private Long recruitStageId;
	
	/**
	 * 照片
	 */
	private String photoPath;
	
	private String orgName;
	
	private List<ZyyResidencyAdmission> admissionList;
	
	private List<ZyyRecruitResidencyWillVO> willList;
	
	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public String getPhotoPath() {
		return photoPath;
	}

	public void setPhotoPath(String photoPath) {
		this.photoPath = photoPath;
	}

	public String getCertificateNo() {
		return certificateNo;
	}

	public void setCertificateNo(String certificateNo) {
		this.certificateNo = certificateNo;
	}

	public String getRealName() {
		return realName;
	}

	public void setRealName(String realName) {
		this.realName = realName;
	}


	public String getHighestGraduateSchool() {
		return highestGraduateSchool;
	}

	public void setHighestGraduateSchool(String highestGraduateSchool) {
		this.highestGraduateSchool = highestGraduateSchool;
	}
	
	public Integer getSex() {
		return sex;
	}

	public void setSex(Integer sex) {
		this.sex = sex;
	}

	public String getSexString() {
		int sexInt = 0;
		Integer sex = getSex();
		if (sex != null){
			sexInt = sex.intValue();
		}
		switch(sexInt){
			case Constants.SEX_MAN:
				this.sexString = "男";
				break;
			case Constants.SEX_WOMAN:
				this.sexString = "女";
				break;
			default:this.sexString="未知";
		}
		return this.sexString;
	}

	public void setSexString(String sexString) {
		this.sexString = sexString;
	}

	public Integer getResidencySource() {
		return residencySource;
	}

	public void setResidencySource(Integer residencySource) {
		this.residencySource = residencySource;
	}

	public String getResidencySourceString() {
		int rs = 0;
		Integer residencySource = getResidencySource();
		if (residencySource != null){
			rs = residencySource.intValue();
		}
		switch(rs){
			case Constants.RESIDENCY_SOURCE_1:
				this.residencySourceString = "单位人";
				break;
			case Constants.RESIDENCY_SOURCE_2:
				this.residencySourceString = "社会人";
				break;
			default:this.residencySourceString="未知";
		}
		return this.residencySourceString;
	}

	public void setResidencySourceString(String residencySourceString) {
		this.residencySourceString = residencySourceString;
	}

	public Integer getHighestRecordSchool() {
		return highestRecordSchool;
	}

	public void setHighestRecordSchool(Integer highestRecordSchool) {
		this.highestRecordSchool = highestRecordSchool;
	}

	public String getHighestRecordSchoolString() {
		int hrs = 0;
		Integer highestRecordSchool = getHighestRecordSchool();
		if (highestRecordSchool != null){
			hrs = highestRecordSchool.intValue();
		}
		switch(hrs){
			case Constants.HIGHEST_RECORD_SCHOOL_1:
				this.highestRecordSchoolString = "专科";
				break;
			case Constants.HIGHEST_RECORD_SCHOOL_2:
				this.highestRecordSchoolString = "本科";
				break;
			case Constants.HIGHEST_RECORD_SCHOOL_3:
				this.highestRecordSchoolString = "硕士";
				break;
			case Constants.HIGHEST_RECORD_SCHOOL_4:
				this.highestRecordSchoolString = "博士";
				break;
			case Constants.HIGHEST_RECORD_SCHOOL_5:
				this.highestRecordSchoolString = "博士后";
				break;
			default:this.highestRecordSchoolString="未知";
		}
		return this.highestRecordSchoolString;
	}

	public void setHighestRecordSchoolString(String highestRecordSchoolString) {
		this.highestRecordSchoolString = highestRecordSchoolString;
	}

	public Integer getCertificateType() {
		return certificateType;
	}

	public void setCertificateType(Integer certificateType) {
		this.certificateType = certificateType;
	}
	
	public String getCertificateTypeString() {
		int ct = -1;
		Integer certificateType = getCertificateType();
		if (certificateType != null){
			ct = certificateType.intValue();
		}
		switch(ct){
			case Constants.ZYY_CERTIFICATE_TYPE_IDENTITY:
				this.residencySourceString = "身份证";
				break;
			case Constants.ZYY_CERTIFICATE_TYPE_CADRE:
				this.residencySourceString = "干部证";
				break;
			case Constants.ZYY_CERTIFICATE_TYPE_OFFICER:
				this.residencySourceString = "军官证";
				break;
			case Constants.ZYY_CERTIFICATE_TYPE_STUDENT:
				this.residencySourceString = "学员证";
				break;
			case Constants.ZYY_CERTIFICATE_TYPE_PASSPORT:
				this.residencySourceString = "护照";
				break;
			case Constants.ZYY_CERTIFICATE_TYPE_RETURN:
				this.residencySourceString = "回乡证";
				break;
			case Constants.ZYY_CERTIFICATE_TYPE_TAIWAN:
				this.residencySourceString = "台胞证";
				break;
			case Constants.ZYY_CERTIFICATE_TYPE_OTHER:
				this.residencySourceString = "其他";
				break;
			default:this.residencySourceString="未知";
		}
		return this.residencySourceString;
	}

	public void setCertificateTypeString(String certificateTypeString) {
		this.certificateTypeString = certificateTypeString;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getZyyUserId() {
		return zyyUserId;
	}

	public void setZyyUserId(Long zyyUserId) {
		this.zyyUserId = zyyUserId;
	}

	public Long getRecruitYearId() {
		return recruitYearId;
	}

	public void setRecruitYearId(Long recruitYearId) {
		this.recruitYearId = recruitYearId;
	}

	public Long getZyyOrgId() {
		return zyyOrgId;
	}

	public void setZyyOrgId(Long zyyOrgId) {
		this.zyyOrgId = zyyOrgId;
	}

	public List<ZyyResidencyAdmission> getAdmissionList() {
		return admissionList;
	}

	public void setAdmissionList(List<ZyyResidencyAdmission> admissionList) {
		this.admissionList = admissionList;
	}

	public List<ZyyRecruitResidencyWillVO> getWillList() {
		return willList;
	}

	public void setWillList(List<ZyyRecruitResidencyWillVO> willList) {
		this.willList = willList;
	}

	public Long getRecruitStageId() {
		return recruitStageId;
	}

	public void setRecruitStageId(Long recruitStageId) {
		this.recruitStageId = recruitStageId;
	}
		
}
