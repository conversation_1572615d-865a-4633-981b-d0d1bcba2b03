package com.hys.zyy.manage.model;

import java.util.Date;

public class ZyyResidencyAttendance implements java.io.Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	/**
	 * ID
	 */
	private Long id;
	
	/**
	 * ID
	 */
	private Long userId;
	
	/**
	 * 医院ID
	 */
	private Long zyyOrgId;
	
	/**
	 * 科室ID
	 */
	private Long zyyDeptId;
	
	/**
	 * 科室用户ID
	 */
	private Long zyyUserId;
	
	/**
	 * 住院医师ID
	 */
	private Long zyyUserId2;
	
	/**
	 * 考勤时间
	 */
	private Date attendanceDate;
	
	/**
	 * 考勤类别
	 */
	private Integer attendanceType;
	
	/**
	 * 考勤数量
	 */
	private Integer attendanceAmount;
	
	/**
	 * 填写时间
	 */
	private Date writeDate;
	
	/**
	 * 最后更新时间
	 */
	private Date lastUpdateDate;
	
	/**
	 * 状态
	 */
	private Integer status;
	
	/**
	 * 转换 zyyUserId2
	 */
	private Long newUserId;
	
	private Integer personNum;
	
	public ZyyResidencyAttendance() {
		super();
	}

	public ZyyResidencyAttendance(Long zyyDeptId, Integer attendanceType) {
		super();
		this.zyyDeptId = zyyDeptId;
		this.attendanceType = attendanceType;
	}

	public Integer getPersonNum() {
		return personNum;
	}

	public void setPersonNum(Integer personNum) {
		this.personNum = personNum;
	}

	public Long getNewUserId() {
		return newUserId;
	}

	public void setNewUserId(Long newUserId) {
		this.newUserId = newUserId;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public Long getZyyOrgId() {
		return zyyOrgId;
	}

	public void setZyyOrgId(Long zyyOrgId) {
		this.zyyOrgId = zyyOrgId;
	}

	public Long getZyyDeptId() {
		return zyyDeptId;
	}

	public void setZyyDeptId(Long zyyDeptId) {
		this.zyyDeptId = zyyDeptId;
	}

	public Long getZyyUserId() {
		return zyyUserId;
	}

	public void setZyyUserId(Long zyyUserId) {
		this.zyyUserId = zyyUserId;
	}

	public Long getZyyUserId2() {
		return zyyUserId2;
	}

	public void setZyyUserId2(Long zyyUserId2) {
		this.zyyUserId2 = zyyUserId2;
	}

	public Date getAttendanceDate() {
		return attendanceDate;
	}

	public void setAttendanceDate(Date attendanceDate) {
		this.attendanceDate = attendanceDate;
	}

	public Integer getAttendanceType() {
		return attendanceType;
	}

	public void setAttendanceType(Integer attendanceType) {
		this.attendanceType = attendanceType;
	}

	public Integer getAttendanceAmount() {
		return attendanceAmount;
	}

	public void setAttendanceAmount(Integer attendanceAmount) {
		this.attendanceAmount = attendanceAmount;
	}

	public Date getWriteDate() {
		return writeDate;
	}

	public void setWriteDate(Date writeDate) {
		this.writeDate = writeDate;
	}

	public Date getLastUpdateDate() {
		return lastUpdateDate;
	}

	public void setLastUpdateDate(Date lastUpdateDate) {
		this.lastUpdateDate = lastUpdateDate;
	}
}
