package com.hys.zyy.manage.model;

import java.util.Date;
import java.util.List;

public class ZyyResidencyAttendanceVO extends ZyyResidencyAttendance {
	/**
	 * 
	 */
	private static final long serialVersionUID = -5407009492447996644L;
	/**
	 * 机构名称
	 */
	private String orgName;
	
	/**
	 * 学科名称
	 */
	private String baseName;

	/**
	 * 考勤类别
	 */
	private Integer attendanceType;

	/**
	 * 真是姓名
	 */
	private String realName;
	
	/**
	 * 科室名称
	 */
	private String deptName;
	
	/**
	 * 科室级别
	 */
	private Integer deptLevel;
	
	/**
	 * 
	 */
	private Integer year;
	
	/**
	 * 用户状态
	 */
	private Integer zyyUserStatus;

	/**
	 * 最后更新状态时间
	 */
	private Date lastUpdateStatusDate ; 
	
	
	private Integer lateNumber;
	
	private Integer leaveEarlyNumber;
	
	private Integer absenteeismNumber;
	
	private Integer leaveNumber;
	
	private Integer leaveDateNumber;
	
	private String mobilNumber;//手机号
	
	private List<ZyyResidencyLeaveVO> resiLeaveVos;
	/*
	 * 考勤年月
	 */
	private String attDateStr;
	/*
	 * 上报时间
	 */
	private String reportTimeStr;
	
	public ZyyResidencyAttendanceVO() {
		super();
	}

	public ZyyResidencyAttendanceVO(Long zyyDeptId, Integer attendanceType, String attDateStr) {
		super(zyyDeptId, attendanceType);
		this.attDateStr = attDateStr;
	}

	public String getOrgName() {
		return orgName;
	}

	public String getBaseName() {
		return baseName;
	}

	public void setBaseName(String baseName) {
		this.baseName = baseName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public String getRealName() {
		return realName;
	}

	public void setRealName(String realName) {
		this.realName = realName;
	}

	public String getDeptName() {
		return deptName;
	}

	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}

	public Integer getDeptLevel() {
		return deptLevel;
	}

	public void setDeptLevel(Integer deptLevel) {
		this.deptLevel = deptLevel;
	}

	public Integer getYear() {
		return year;
	}

	public void setYear(Integer year) {
		this.year = year;
	}

	public Integer getZyyUserStatus() {
		return zyyUserStatus;
	}

	public void setZyyUserStatus(Integer zyyUserStatus) {
		this.zyyUserStatus = zyyUserStatus;
	}

	public Date getLastUpdateStatusDate() {
		return lastUpdateStatusDate;
	}

	public void setLastUpdateStatusDate(Date lastUpdateStatusDate) {
		this.lastUpdateStatusDate = lastUpdateStatusDate;
	}

	public Integer getLateNumber() {
		return lateNumber;
	}

	public void setLateNumber(Integer lateNumber) {
		this.lateNumber = lateNumber;
	}

	public Integer getLeaveEarlyNumber() {
		return leaveEarlyNumber;
	}

	public void setLeaveEarlyNumber(Integer leaveEarlyNumber) {
		this.leaveEarlyNumber = leaveEarlyNumber;
	}

	public Integer getAbsenteeismNumber() {
		return absenteeismNumber;
	}

	public void setAbsenteeismNumber(Integer absenteeismNumber) {
		this.absenteeismNumber = absenteeismNumber;
	}

	public Integer getLeaveNumber() {
		return leaveNumber;
	}

	public void setLeaveNumber(Integer leaveNumber) {
		this.leaveNumber = leaveNumber;
	}

	public Integer getLeaveDateNumber() {
		return leaveDateNumber;
	}

	public void setLeaveDateNumber(Integer leaveDateNumber) {
		this.leaveDateNumber = leaveDateNumber;
	}
	
	public Integer getAttendanceType() {
		return attendanceType;
	}
	public void setAttendanceType(Integer attendanceType) {
		this.attendanceType = attendanceType;
	}
	public String getMobilNumber() {
		return mobilNumber;
	}
	public void setMobilNumber(String mobilNumber) {
		this.mobilNumber = mobilNumber;
	}
	public List<ZyyResidencyLeaveVO> getResiLeaveVos() {
		return resiLeaveVos;
	}
	public void setResiLeaveVos(List<ZyyResidencyLeaveVO> resiLeaveVos) {
		this.resiLeaveVos = resiLeaveVos;
	}

	public String getAttDateStr() {
		return attDateStr;
	}

	public void setAttDateStr(String attDateStr) {
		this.attDateStr = attDateStr == null ? null : attDateStr.trim();
	}

	public String getReportTimeStr() {
		return reportTimeStr;
	}

	public void setReportTimeStr(String reportTimeStr) {
		this.reportTimeStr = reportTimeStr == null ? null : reportTimeStr.trim();
	}
	
}
