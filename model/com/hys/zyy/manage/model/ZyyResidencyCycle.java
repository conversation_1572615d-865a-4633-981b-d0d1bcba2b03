package com.hys.zyy.manage.model;

import java.util.Date;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：张伟清 2012-04-24
 * 
 * 描述：住院医师轮转表
 * 
 * 说明:
 */
public class ZyyResidencyCycle extends ZyyBaseObject {

	private static final long serialVersionUID = 2791679736194565365L;

	/**
	 * 主键ID
	 */
	private Long id ;
	
	/**
	 * 培训阶段ID
	 */
	private Integer stageId ;
	
	/**
	 * 轮转科室ID
	 */
	private Long deptId ;
	
	/**
	 * 住院医师ID
	 */
	private Long residencyId ;
	
	/**
	 * 实际轮转时间
	 */
	private Long cycleTime ;
	
	/**
	 * 轮转类别
	 */
	private Integer cycleType ;
	
	/**
	 * 开始时间
	 */
	private Date startDate ;
	
	/**
	 * 结束时间
	 */
	private Date endDate ;
	
	/**
	 * 状态
	 */
	private Integer status ;
	
	/**
	 * 顺序
	 */
	private Integer seq ;
	
	/**
	 * 备注
	 */
	private Long remark ;
	
	/**
	 * 排班人
	 */
	private Long scheduler ;
	
	/**
	 * 排班时间
	 */
	private Date scheduleDate ;
	
	/**
	 * 最后修改人
	 */
	private Long lastScheduler ;
	
	/**
	 * 最后修改时间
	 */
	private Date lastUpdateDate ;
	
	/**
	 * 轮转历史表Id
	 */
	private Long cycleTableHistoryId;
	
	/**
	 * 是否自动轮转
	 */
	private Integer isAuto ;
	
	/**
	 * 轮转状态
	 */
	private Integer cycleStatus ;
	
	/**
	 * 住院医师轮转ID
	 */
	private Long residencyCycleId; 

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Integer getStageId() {
		return stageId;
	}

	public void setStageId(Integer stageId) {
		this.stageId = stageId;
	}

	public Long getDeptId() {
		return deptId;
	}

	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}

	public Long getResidencyId() {
		return residencyId;
	}

	public void setResidencyId(Long residencyId) {
		this.residencyId = residencyId;
	}

	public Long getCycleTime() {
		return cycleTime;
	}

	public void setCycleTime(Long cycleTime) {
		this.cycleTime = cycleTime;
	}

	public Integer getCycleType() {
		return cycleType;
	}

	public void setCycleType(Integer cycleType) {
		this.cycleType = cycleType;
	}

	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Integer getSeq() {
		return seq;
	}

	public void setSeq(Integer seq) {
		this.seq = seq;
	}

	public Long getRemark() {
		return remark;
	}

	public void setRemark(Long remark) {
		this.remark = remark;
	}

	public Long getScheduler() {
		return scheduler;
	}

	public void setScheduler(Long scheduler) {
		this.scheduler = scheduler;
	}

	public Date getScheduleDate() {
		return scheduleDate;
	}

	public void setScheduleDate(Date scheduleDate) {
		this.scheduleDate = scheduleDate;
	}

	public Long getLastScheduler() {
		return lastScheduler;
	}

	public void setLastScheduler(Long lastScheduler) {
		this.lastScheduler = lastScheduler;
	}

	public Date getLastUpdateDate() {
		return lastUpdateDate;
	}

	public void setLastUpdateDate(Date lastUpdateDate) {
		this.lastUpdateDate = lastUpdateDate;
	}

	public Long getCycleTableHistoryId() {
		return cycleTableHistoryId;
	}

	public void setCycleTableHistoryId(Long cycleTableHistoryId) {
		this.cycleTableHistoryId = cycleTableHistoryId;
	}

	public Integer getIsAuto() {
		return isAuto;
	}

	public void setIsAuto(Integer isAuto) {
		this.isAuto = isAuto;
	}

	public Integer getCycleStatus() {
		return cycleStatus;
	}

	public void setCycleStatus(Integer cycleStatus) {
		this.cycleStatus = cycleStatus;
	}

	public Long getResidencyCycleId() {
		return residencyCycleId;
	}

	public void setResidencyCycleId(Long residencyCycleId) {
		this.residencyCycleId = residencyCycleId;
	}
}