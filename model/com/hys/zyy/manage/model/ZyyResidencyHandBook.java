package com.hys.zyy.manage.model;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 
 * 标题：住院医师学员手册
 * 
 * 作者：ccj
 * 
 * 描述：
 * 
 * 说明:
 * 
 * 修改人：  王宝昌
 * 
 * 修改时间：2012-08-03
 * 
 * 修改说明：添加 医院轮转科室ID 与 标准科室ID 属性
 */
public class ZyyResidencyHandBook extends ZyyBaseObject {

	/**
	 * 
	 */
	private static final long serialVersionUID = -5859051122782168228L;
	private Long id;   
	private Long handBookStdId;         //标准手册ID
	private Long residencyId;         //住院医师ID
	private Long zyyOrgId;         //医院ID
	private Long residencyCycleId;         //住院医师轮转ID
	private Long zyyDeptId;         //轮转科室ID
	private Long zyyDeptStdId;         //标准科室ID
	private int diseaseType;         //疾病技能ID
	private Date writeDate;         //写入时间
	private Date lastUpdateDate;//最后修改时间
	private int status;        // 状态
	private List<ZyyResidencyHbkExt> extList;
	private List<ZyyResidencyHbkDisease> diseaseList;
	private Map<String, String> fieldMap;
	private Map<String,List<ZyyTrainDisease>> diseaseMap;
	private Map<String,List<ZyyDeptStdDiseaseDetail>> zyyDeptStdDiseaseDetails;
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getHandBookStdId() {
		return handBookStdId;
	}
	public void setHandBookStdId(Long handBookStdId) {
		this.handBookStdId = handBookStdId;
	}
	public Long getResidencyId() {
		return residencyId;
	}
	public void setResidencyId(Long residencyId) {
		this.residencyId = residencyId;
	}
	public Long getZyyOrgId() {
		return zyyOrgId;
	}
	public void setZyyOrgId(Long zyyOrgId) {
		this.zyyOrgId = zyyOrgId;
	}
	public Long getResidencyCycleId() {
		return residencyCycleId;
	}
	public void setResidencyCycleId(Long residencyCycleId) {
		this.residencyCycleId = residencyCycleId;
	}


	public void setZyyDeptId(Long zyyDeptId) {
		this.zyyDeptId = zyyDeptId;
	}
	public Long getZyyDeptId() {
		return zyyDeptId;
	}
	public void setZyyDeptStdId(Long zyyDeptStdId) {
		this.zyyDeptStdId = zyyDeptStdId;
	}
	public Long getZyyDeptStdId() {
		return zyyDeptStdId;
	}
	public int getDiseaseType() {
		return diseaseType;
	}
	public void setDiseaseType(int diseaseType) {
		this.diseaseType = diseaseType;
	}
	public Date getWriteDate() {
		return writeDate;
	}
	public void setWriteDate(Date writeDate) {
		this.writeDate = writeDate;
	}
	public Date getLastUpdateDate() {
		return lastUpdateDate;
	}
	public void setLastUpdateDate(Date lastUpdateDate) {
		this.lastUpdateDate = lastUpdateDate;
	}

	public int getStatus() {
		return status;
	}
	public void setStatus(int status) {
		this.status = status;
	}
	public List<ZyyResidencyHbkExt> getExtList() {
		return extList;
	}
	public void setExtList(List<ZyyResidencyHbkExt> extList) {
		this.extList = extList;
	}
	public Map<String, String> getFieldMap() {
		return fieldMap;
	}
	public void setFieldMap(Map<String, String> fieldMap) {
		this.fieldMap = fieldMap;
	}
	public List<ZyyResidencyHbkDisease> getDiseaseList() {
		return diseaseList;
	}
	public void setDiseaseList(List<ZyyResidencyHbkDisease> diseaseList) {
		this.diseaseList = diseaseList;
	}
	public Map<String, List<ZyyTrainDisease>> getDiseaseMap() {
		return diseaseMap;
	}
	public void setDiseaseMap(Map<String, List<ZyyTrainDisease>> diseaseMap) {
		this.diseaseMap = diseaseMap;
	}
	public void setZyyDeptStdDiseaseDetails(Map<String,List<ZyyDeptStdDiseaseDetail>> zyyDeptStdDiseaseDetails) {
		this.zyyDeptStdDiseaseDetails = zyyDeptStdDiseaseDetails;
	}
	public Map<String,List<ZyyDeptStdDiseaseDetail>> getZyyDeptStdDiseaseDetails() {
		return zyyDeptStdDiseaseDetails;
	}



	

}
