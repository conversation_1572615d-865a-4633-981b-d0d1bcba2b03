package com.hys.zyy.manage.model;

import java.util.Date;
/**
 * 
 * 标题：住院医师
 * 
 * 作者：王宝昌 2012-08-07
 * 
 * 描述：住院医师审核记录
 * 
 * 说明:
 */
public class ZyyResidencyHandBookDept extends ZyyBaseObject {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	private Long id; 
	private Integer  cycleStatus;
	private Long zyyDeptId; 
	private Long zyyDeptStdId; 
	private Integer  teacherCheckStatus;
	private Integer  directorCheckStatus; 
	private Integer  baseCheckStatus; 
	private Integer  hospCheckStatus; 
	private Date teacherCheckDate; 
	private Date directorCheckDate;
	private Date baseCheckDate; 
	private Date hospCheckDate; 
	private Long commitTimes; 
	private Date residencyLastDate;
	private Long residencyId;
	private Long zyyUserOrgId;
	
	public Long getZyyUserOrgId() {
		return zyyUserOrgId;
	}
	public void setZyyUserOrgId(Long zyyUserOrgId) {
		this.zyyUserOrgId = zyyUserOrgId;
	}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	
	public Integer getCycleStatus() {
		return cycleStatus;
	}
	public void setCycleStatus(Integer cycleStatus) {
		this.cycleStatus = cycleStatus;
	}
	public Long getZyyDeptId() {
		return zyyDeptId;
	}
	public void setZyyDeptId(Long zyyDeptId) {
		this.zyyDeptId = zyyDeptId;
	}
	public Long getZyyDeptStdId() {
		return zyyDeptStdId;
	}
	public void setZyyDeptStdId(Long zyyDeptStdId) {
		this.zyyDeptStdId = zyyDeptStdId;
	}
	public Integer getTeacherCheckStatus() {
		return teacherCheckStatus;
	}
	public void setTeacherCheckStatus(Integer teacherCheckStatus) {
		this.teacherCheckStatus = teacherCheckStatus;
	}
	public Integer getDirectorCheckStatus() {
		return directorCheckStatus;
	}
	public void setDirectorCheckStatus(Integer directorCheckStatus) {
		this.directorCheckStatus = directorCheckStatus;
	}
	public Integer getBaseCheckStatus() {
		return baseCheckStatus;
	}
	public void setBaseCheckStatus(Integer baseCheckStatus) {
		this.baseCheckStatus = baseCheckStatus;
	}
	public Integer getHospCheckStatus() {
		return hospCheckStatus;
	}
	public void setHospCheckStatus(Integer hospCheckStatus) {
		this.hospCheckStatus = hospCheckStatus;
	}
	public Date getTeacherCheckDate() {
		return teacherCheckDate;
	}
	public void setTeacherCheckDate(Date teacherCheckDate) {
		this.teacherCheckDate = teacherCheckDate;
	}
	public Date getDirectorCheckDate() {
		return directorCheckDate;
	}
	public void setDirectorCheckDate(Date directorCheckDate) {
		this.directorCheckDate = directorCheckDate;
	}
	public Date getBaseCheckDate() {
		return baseCheckDate;
	}
	public void setBaseCheckDate(Date baseCheckDate) {
		this.baseCheckDate = baseCheckDate;
	}
	public Date getHospCheckDate() {
		return hospCheckDate;
	}
	public void setHospCheckDate(Date hospCheckDate) {
		this.hospCheckDate = hospCheckDate;
	}
	public Long getCommitTimes() {
		return commitTimes;
	}
	public void setCommitTimes(Long commitTimes) {
		this.commitTimes = commitTimes;
	}
	public Date getResidencyLastDate() {
		return residencyLastDate;
	}
	public void setResidencyLastDate(Date residencyLastDate) {
		this.residencyLastDate = residencyLastDate;
	}
	public Long getResidencyId() {
		return residencyId;
	}
	public void setResidencyId(Long residencyId) {
		this.residencyId = residencyId;
	}
}
