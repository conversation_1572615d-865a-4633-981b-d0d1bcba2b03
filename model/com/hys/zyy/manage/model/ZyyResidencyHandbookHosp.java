package com.hys.zyy.manage.model;

// default package

import java.util.Date;

/**
 * ZyyResidencyHandbookHosp entity. <AUTHOR> Persistence Tools
 */

public class ZyyResidencyHandbookHosp implements java.io.Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	private Long id;
	
	private Long residencyId;
	
	private Long zyyOrgId;
	
	private Date residencyLastDate;
	
	private Long commitTimes;
	
	private Integer residencyCommitStatus;
	
	private Integer hospCheckStatus;
	
	private Date hospCheckDate;
	
	private Integer hospFinalCheckStatus;
	
	private Date hospFinalCheckDate;
	
	private Integer baseCheckStatus;
	
	private Date baseCheckDate;
	
	private Integer baseFinalCheckStatus;
	
	private Date baseFinalCheckDate;
	
	private Integer teacherCheckStatus;
	
	private Date teacherCheckDate;
	
	private Integer directorCheckStatus;
	
	private Date directorCheckDate;
	
	private Long zyyDeptId;
	
	private Long zyyDeptStdId;
	
	private Integer finalCheckStatus;
	
	private Date finalCheckDate;
	
	private Long finalCheckUser;

	public Integer getBaseFinalCheckStatus() {
		return baseFinalCheckStatus;
	}

	public void setBaseFinalCheckStatus(Integer baseFinalCheckStatus) {
		this.baseFinalCheckStatus = baseFinalCheckStatus;
	}

	public Date getBaseFinalCheckDate() {
		return baseFinalCheckDate;
	}

	public void setBaseFinalCheckDate(Date baseFinalCheckDate) {
		this.baseFinalCheckDate = baseFinalCheckDate;
	}

	public Integer getHospFinalCheckStatus() {
		return hospFinalCheckStatus;
	}

	public void setHospFinalCheckStatus(Integer hospFinalCheckStatus) {
		this.hospFinalCheckStatus = hospFinalCheckStatus;
	}

	public Date getHospFinalCheckDate() {
		return hospFinalCheckDate;
	}

	public void setHospFinalCheckDate(Date hospFinalCheckDate) {
		this.hospFinalCheckDate = hospFinalCheckDate;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Date getResidencyLastDate() {
		return residencyLastDate;
	}

	public void setResidencyLastDate(Date residencyLastDate) {
		this.residencyLastDate = residencyLastDate;
	}

	public Long getCommitTimes() {
		return commitTimes;
	}

	public void setCommitTimes(Long commitTimes) {
		this.commitTimes = commitTimes;
	}

	public Integer getResidencyCommitStatus() {
		return residencyCommitStatus;
	}

	public void setResidencyCommitStatus(Integer residencyCommitStatus) {
		this.residencyCommitStatus = residencyCommitStatus;
	}

	public Integer getHospCheckStatus() {
		return hospCheckStatus;
	}

	public void setHospCheckStatus(Integer hospCheckStatus) {
		this.hospCheckStatus = hospCheckStatus;
	}

	public Date getHospCheckDate() {
		return hospCheckDate;
	}

	public void setHospCheckDate(Date hospCheckDate) {
		this.hospCheckDate = hospCheckDate;
	}

	public Long getResidencyId() {
		return residencyId;
	}

	public void setResidencyId(Long residencyId) {
		this.residencyId = residencyId;
	}

	public Long getZyyOrgId() {
		return zyyOrgId;
	}

	public void setZyyOrgId(Long zyyOrgId) {
		this.zyyOrgId = zyyOrgId;
	}

	public Integer getBaseCheckStatus() {
		return baseCheckStatus;
	}

	public void setBaseCheckStatus(Integer baseCheckStatus) {
		this.baseCheckStatus = baseCheckStatus;
	}

	public Date getBaseCheckDate() {
		return baseCheckDate;
	}

	public void setBaseCheckDate(Date baseCheckDate) {
		this.baseCheckDate = baseCheckDate;
	}

	public Integer getTeacherCheckStatus() {
		return teacherCheckStatus;
	}

	public void setTeacherCheckStatus(Integer teacherCheckStatus) {
		this.teacherCheckStatus = teacherCheckStatus;
	}

	public Date getTeacherCheckDate() {
		return teacherCheckDate;
	}

	public void setTeacherCheckDate(Date teacherCheckDate) {
		this.teacherCheckDate = teacherCheckDate;
	}

	public Integer getDirectorCheckStatus() {
		return directorCheckStatus;
	}

	public void setDirectorCheckStatus(Integer directorCheckStatus) {
		this.directorCheckStatus = directorCheckStatus;
	}

	public Date getDirectorCheckDate() {
		return directorCheckDate;
	}

	public void setDirectorCheckDate(Date directorCheckDate) {
		this.directorCheckDate = directorCheckDate;
	}

	public Long getZyyDeptId() {
		return zyyDeptId;
	}

	public void setZyyDeptId(Long zyyDeptId) {
		this.zyyDeptId = zyyDeptId;
	}

	public Long getZyyDeptStdId() {
		return zyyDeptStdId;
	}

	public void setZyyDeptStdId(Long zyyDeptStdId) {
		this.zyyDeptStdId = zyyDeptStdId;
	}

	public Integer getFinalCheckStatus() {
		return finalCheckStatus == null ? 4 : finalCheckStatus;
	}

	public void setFinalCheckStatus(Integer finalCheckStatus) {
		this.finalCheckStatus = finalCheckStatus;
	}

	public Date getFinalCheckDate() {
		return finalCheckDate;
	}

	public void setFinalCheckDate(Date finalCheckDate) {
		this.finalCheckDate = finalCheckDate;
	}

	public Long getFinalCheckUser() {
		return finalCheckUser;
	}

	public void setFinalCheckUser(Long finalCheckUser) {
		this.finalCheckUser = finalCheckUser;
	}

}