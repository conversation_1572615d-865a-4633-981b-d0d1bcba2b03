package com.hys.zyy.manage.model;
// default package

import java.util.Date;

import org.codehaus.jackson.map.annotate.JsonSerialize;

/**
 * ZyyResidencyHandbookHosp entity. <AUTHOR>
 */
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class ZyyResidencyHandbookHospVO implements java.io.Serializable {

	private static final long serialVersionUID = 1L;
	//ID
	private Long id;
	
	//住院医师ID
	private Long residencyId;
	
	//医院ID
	private Long zyyOrgId;
	
	//最后提交时间
	private Date residencyLastDate;
	
	//提交次数
	private Long commitTimes;
	
	//住院医师提交状态
	private Integer residencyCommitStatus;
	
	//医院审核状态
	private Integer hospCheckStatus;
	
	//医院审核时间
	private Date hospCheckDate;
	
	//真实姓名
	private String realName;
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Date getResidencyLastDate() {
		return residencyLastDate;
	}
	public void setResidencyLastDate(Date residencyLastDate) {
		this.residencyLastDate = residencyLastDate;
	}
	public Long getCommitTimes() {
		return commitTimes;
	}
	public void setCommitTimes(Long commitTimes) {
		this.commitTimes = commitTimes;
	}
	public Integer getResidencyCommitStatus() {
		return residencyCommitStatus;
	}
	public void setResidencyCommitStatus(Integer residencyCommitStatus) {
		this.residencyCommitStatus = residencyCommitStatus;
	}
	public Integer getHospCheckStatus() {
		return hospCheckStatus;
	}
	public void setHospCheckStatus(Integer hospCheckStatus) {
		this.hospCheckStatus = hospCheckStatus;
	}
	public Date getHospCheckDate() {
		return hospCheckDate;
	}
	public void setHospCheckDate(Date hospCheckDate) {
		this.hospCheckDate = hospCheckDate;
	}
	public Long getResidencyId() {
		return residencyId;
	}
	public void setResidencyId(Long residencyId) {
		this.residencyId = residencyId;
	}
	public Long getZyyOrgId() {
		return zyyOrgId;
	}
	public void setZyyOrgId(Long zyyOrgId) {
		this.zyyOrgId = zyyOrgId;
	}
	public String getRealName() {
		return realName;
	}
	public void setRealName(String realName) {
		this.realName = realName;
	}

}