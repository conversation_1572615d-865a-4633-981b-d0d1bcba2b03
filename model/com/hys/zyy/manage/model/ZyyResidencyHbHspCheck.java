package com.hys.zyy.manage.model;
// default package

import java.util.Date;

/**
 * ZyyResidencyHbHspCheck entity. <AUTHOR> Persistence Tools
 */

public class ZyyResidencyHbHspCheck implements java.io.Serializable {

	private static final long serialVersionUID = 1L;

	private Long id;
	
	private Long deptId;
	
	private Long hospitalUserId;
	
	private Long ResidencyId;
	
	private Long residencyHandbookHospId;
	
	private Date commitDate;
	
	private Integer hospitalCheckStatus;
	
	private Date hospitalCheckDate;
	
	private String hosp;
	
	private String hospitalCheckRemark;
	
	private Long baseUserId;
	
	private Date baseCheckDate;
	
	private String base;
	
	private Integer baseCheckStatus;
	
	private String baseCheckRemark;
	
	private Long teacherUserId;
	
	private Date teacherCheckDate;
	
	private String teacher;
	
	private Integer teacherCheckStatus;
	
	private String teacherCheckRemark;
	
	private Long directorUserId;
	
	private Date directorCheckDate;
	
	private String director;
	
	private Integer directorCheckStatus;
	
	private String directorCheckRemark;
	
	private Long zyyDeptId;
	
	private Long zyyDeptStdId;
	
	private Long checkUserId;
	
	private Integer status;
	
	private String remark;
	
	private Date date;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getHospitalUserId() {
		return hospitalUserId;
	}

	public void setHospitalUserId(Long hospitalUserId) {
		this.hospitalUserId = hospitalUserId;
	}

	public Long getResidencyId() {
		return ResidencyId;
	}

	public void setResidencyId(Long residencyId) {
		ResidencyId = residencyId;
	}

	public Long getResidencyHandbookHospId() {
		return residencyHandbookHospId;
	}

	public void setResidencyHandbookHospId(Long residencyHandbookHospId) {
		this.residencyHandbookHospId = residencyHandbookHospId;
	}

	public Date getCommitDate() {
		return commitDate;
	}

	public void setCommitDate(Date commitDate) {
		this.commitDate = commitDate;
	}

	public Integer getHospitalCheckStatus() {
		return hospitalCheckStatus;
	}

	public void setHospitalCheckStatus(Integer hospitalCheckStatus) {
		this.hospitalCheckStatus = hospitalCheckStatus;
	}

	public Date getHospitalCheckDate() {
		return hospitalCheckDate;
	}

	public void setHospitalCheckDate(Date hospitalCheckDate) {
		this.hospitalCheckDate = hospitalCheckDate;
	}

	public String getHospitalCheckRemark() {
		return hospitalCheckRemark;
	}

	public void setHospitalCheckRemark(String hospitalCheckRemark) {
		this.hospitalCheckRemark = hospitalCheckRemark;
	}

	public Long getBaseUserId() {
		return baseUserId;
	}

	public void setBaseUserId(Long baseUserId) {
		this.baseUserId = baseUserId;
	}

	public Date getBaseCheckDate() {
		return baseCheckDate;
	}

	public void setBaseCheckDate(Date baseCheckDate) {
		this.baseCheckDate = baseCheckDate;
	}

	public String getBaseCheckRemark() {
		return baseCheckRemark;
	}

	public void setBaseCheckRemark(String baseCheckRemark) {
		this.baseCheckRemark = baseCheckRemark;
	}

	public Integer getBaseCheckStatus() {
		return baseCheckStatus;
	}

	public void setBaseCheckStatus(Integer baseCheckStatus) {
		this.baseCheckStatus = baseCheckStatus;
	}

	public String getHosp() {
		return hosp;
	}

	public void setHosp(String hosp) {
		this.hosp = hosp;
	}

	public String getBase() {
		return base;
	}

	public void setBase(String base) {
		this.base = base;
	}

	public Long getTeacherUserId() {
		return teacherUserId;
	}

	public void setTeacherUserId(Long teacherUserId) {
		this.teacherUserId = teacherUserId;
	}

	public Date getTeacherCheckDate() {
		return teacherCheckDate;
	}

	public void setTeacherCheckDate(Date teacherCheckDate) {
		this.teacherCheckDate = teacherCheckDate;
	}

	public String getTeacher() {
		return teacher;
	}

	public void setTeacher(String teacher) {
		this.teacher = teacher;
	}

	public Integer getTeacherCheckStatus() {
		return teacherCheckStatus;
	}

	public void setTeacherCheckStatus(Integer teacherCheckStatus) {
		this.teacherCheckStatus = teacherCheckStatus;
	}

	public String getTeacherCheckRemark() {
		return teacherCheckRemark;
	}

	public void setTeacherCheckRemark(String teacherCheckRemark) {
		this.teacherCheckRemark = teacherCheckRemark;
	}

	public Long getDirectorUserId() {
		return directorUserId;
	}

	public void setDirectorUserId(Long directorUserId) {
		this.directorUserId = directorUserId;
	}

	public Date getDirectorCheckDate() {
		return directorCheckDate;
	}

	public void setDirectorCheckDate(Date directorCheckDate) {
		this.directorCheckDate = directorCheckDate;
	}

	public String getDirector() {
		return director;
	}

	public void setDirector(String director) {
		this.director = director;
	}

	public Integer getDirectorCheckStatus() {
		return directorCheckStatus;
	}

	public void setDirectorCheckStatus(Integer directorCheckStatus) {
		this.directorCheckStatus = directorCheckStatus;
	}

	public String getDirectorCheckRemark() {
		return directorCheckRemark;
	}

	public void setDirectorCheckRemark(String directorCheckRemark) {
		this.directorCheckRemark = directorCheckRemark;
	}

	public Long getZyyDeptId() {
		return zyyDeptId;
	}

	public void setZyyDeptId(Long zyyDeptId) {
		this.zyyDeptId = zyyDeptId;
	}

	public Long getZyyDeptStdId() {
		return zyyDeptStdId;
	}

	public void setZyyDeptStdId(Long zyyDeptStdId) {
		this.zyyDeptStdId = zyyDeptStdId;
	}

	public Long getDeptId() {
		return deptId;
	}

	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}

	public Long getCheckUserId() {
		return checkUserId;
	}

	public void setCheckUserId(Long checkUserId) {
		this.checkUserId = checkUserId;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public Date getDate() {
		return date;
	}

	public void setDate(Date date) {
		this.date = date;
	}

}