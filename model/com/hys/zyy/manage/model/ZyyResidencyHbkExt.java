package com.hys.zyy.manage.model;

import java.util.Date;

/**
 * 
 * 标题：用户填写手册
 * 
 * 作者：ccj  
 * 
 * 描述：
 * 
 * 说明:
 */

public class ZyyResidencyHbkExt extends ZyyBaseObject {

	private static final long serialVersionUID = 1839135588653098898L;

	/**
	 * 主键ID
	 */
	private Long id;
	
	/**
	 * 学员手册ID
	 */
	private Long handBookId;
	
	/**
	 * 字段名称
	 */
	private String fieldName;
	
	/**
	 * 字段值
	 */
	private String fieldValue;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getHandBookId() {
		return handBookId;
	}

	public void setHandBookId(Long handBookId) {
		this.handBookId = handBookId;
	}

	public String getFieldName() {
		return fieldName;
	}

	public void setFieldName(String fieldName) {
		this.fieldName = fieldName;
	}

	public String getFieldValue() {
		return fieldValue;
	}

	public void setFieldValue(String fieldValue) {
		this.fieldValue = fieldValue;
	}



}