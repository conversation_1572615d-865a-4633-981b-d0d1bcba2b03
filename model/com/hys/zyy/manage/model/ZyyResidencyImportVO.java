package com.hys.zyy.manage.model;

public class ZyyResidencyImportVO extends ZyyResidencyImport {
	private String birthdayStr, isMarriedStr, joinPartyDateStr, homePlaceStr, firstRecordSchoolStr, trainingPeriodStr, 
		hasFirstRecordCertificateStr, isFreshStr, firstDegreeStr, firstSchoolSystemStr, firstDegreeTypeStr, hasFirstDegreeCertificateStr,
		schoolPlaceStr, highestRecordSchoolStr, hasHighestRecordCertificateStr, graduationDateStr, highestDegreeStr, highestSchoolSystemStr,
		highestDegreeTypeStr, hasHighestDegreeCertificateStr, graduateStudentStatusStr, physiciansPracticingCertStr, getPracticeDateStr,
		hasCertificateStr, physiciansQualificationLevelStr, physiciansQualificationTypeStr, getDateStr, workDateStr, workUnitLevelStr,
		workUnitNatureStr, getTitleDateStr, schoolTypeStr;

	public String getBirthdayStr() {
		return birthdayStr;
	}

	public void setBirthdayStr(String birthdayStr) {
		this.birthdayStr = birthdayStr == null ? null : birthdayStr.trim();
	}

	public String getIsMarriedStr() {
		return isMarriedStr;
	}

	public void setIsMarriedStr(String isMarriedStr) {
		this.isMarriedStr = isMarriedStr == null ? null : isMarriedStr.trim();
	}

	public String getJoinPartyDateStr() {
		return joinPartyDateStr;
	}

	public void setJoinPartyDateStr(String joinPartyDateStr) {
		this.joinPartyDateStr = joinPartyDateStr == null ? null : joinPartyDateStr.trim();
	}

	public String getHomePlaceStr() {
		return homePlaceStr;
	}

	public void setHomePlaceStr(String homePlaceStr) {
		this.homePlaceStr = homePlaceStr == null ? null : homePlaceStr.trim();
	}

	public String getFirstRecordSchoolStr() {
		return firstRecordSchoolStr;
	}

	public void setFirstRecordSchoolStr(String firstRecordSchoolStr) {
		this.firstRecordSchoolStr = firstRecordSchoolStr == null ? null : firstRecordSchoolStr.trim();
	}

	public String getTrainingPeriodStr() {
		return trainingPeriodStr;
	}

	public void setTrainingPeriodStr(String trainingPeriodStr) {
		this.trainingPeriodStr = trainingPeriodStr == null ? null : trainingPeriodStr.trim();
	}

	public String getHasFirstRecordCertificateStr() {
		return hasFirstRecordCertificateStr;
	}

	public void setHasFirstRecordCertificateStr(String hasFirstRecordCertificateStr) {
		this.hasFirstRecordCertificateStr = hasFirstRecordCertificateStr == null ? null : hasFirstRecordCertificateStr.trim();
	}

	public String getIsFreshStr() {
		return isFreshStr;
	}

	public void setIsFreshStr(String isFreshStr) {
		this.isFreshStr = isFreshStr == null ? null : isFreshStr.trim();
	}

	public String getFirstDegreeStr() {
		return firstDegreeStr;
	}

	public void setFirstDegreeStr(String firstDegreeStr) {
		this.firstDegreeStr = firstDegreeStr == null ? null : firstDegreeStr.trim();
	}

	public String getFirstSchoolSystemStr() {
		return firstSchoolSystemStr;
	}

	public void setFirstSchoolSystemStr(String firstSchoolSystemStr) {
		this.firstSchoolSystemStr = firstSchoolSystemStr == null ? null : firstSchoolSystemStr.trim();
	}

	public String getFirstDegreeTypeStr() {
		return firstDegreeTypeStr;
	}

	public void setFirstDegreeTypeStr(String firstDegreeTypeStr) {
		this.firstDegreeTypeStr = firstDegreeTypeStr == null ? null : firstDegreeTypeStr.trim();
	}

	public String getHasFirstDegreeCertificateStr() {
		return hasFirstDegreeCertificateStr;
	}

	public void setHasFirstDegreeCertificateStr(String hasFirstDegreeCertificateStr) {
		this.hasFirstDegreeCertificateStr = hasFirstDegreeCertificateStr == null ? null : hasFirstDegreeCertificateStr.trim();
	}

	public String getSchoolPlaceStr() {
		return schoolPlaceStr;
	}

	public void setSchoolPlaceStr(String schoolPlaceStr) {
		this.schoolPlaceStr = schoolPlaceStr == null ? null : schoolPlaceStr.trim();
	}

	public String getHighestRecordSchoolStr() {
		return highestRecordSchoolStr;
	}

	public void setHighestRecordSchoolStr(String highestRecordSchoolStr) {
		this.highestRecordSchoolStr = highestRecordSchoolStr == null ? null : highestRecordSchoolStr.trim();
	}

	public String getHasHighestRecordCertificateStr() {
		return hasHighestRecordCertificateStr;
	}

	public void setHasHighestRecordCertificateStr(String hasHighestRecordCertificateStr) {
		this.hasHighestRecordCertificateStr = hasHighestRecordCertificateStr == null ? null : hasHighestRecordCertificateStr.trim();
	}

	public String getGraduationDateStr() {
		return graduationDateStr;
	}

	public void setGraduationDateStr(String graduationDateStr) {
		this.graduationDateStr = graduationDateStr == null ? null : graduationDateStr.trim();
	}

	public String getHighestDegreeStr() {
		return highestDegreeStr;
	}

	public void setHighestDegreeStr(String highestDegreeStr) {
		this.highestDegreeStr = highestDegreeStr == null ? null : highestDegreeStr.trim();
	}

	public String getHighestSchoolSystemStr() {
		return highestSchoolSystemStr;
	}

	public void setHighestSchoolSystemStr(String highestSchoolSystemStr) {
		this.highestSchoolSystemStr = highestSchoolSystemStr == null ? null : highestSchoolSystemStr.trim();
	}

	public String getHighestDegreeTypeStr() {
		return highestDegreeTypeStr;
	}

	public void setHighestDegreeTypeStr(String highestDegreeTypeStr) {
		this.highestDegreeTypeStr = highestDegreeTypeStr == null ? null : highestDegreeTypeStr.trim();
	}

	public String getHasHighestDegreeCertificateStr() {
		return hasHighestDegreeCertificateStr;
	}

	public void setHasHighestDegreeCertificateStr(String hasHighestDegreeCertificateStr) {
		this.hasHighestDegreeCertificateStr = hasHighestDegreeCertificateStr == null ? null : hasHighestDegreeCertificateStr.trim();
	}

	public String getGraduateStudentStatusStr() {
		return graduateStudentStatusStr;
	}

	public void setGraduateStudentStatusStr(String graduateStudentStatusStr) {
		this.graduateStudentStatusStr = graduateStudentStatusStr == null ? null : graduateStudentStatusStr.trim();
	}

	public String getPhysiciansPracticingCertStr() {
		return physiciansPracticingCertStr;
	}

	public void setPhysiciansPracticingCertStr(String physiciansPracticingCertStr) {
		this.physiciansPracticingCertStr = physiciansPracticingCertStr == null ? null : physiciansPracticingCertStr.trim();
	}

	public String getGetPracticeDateStr() {
		return getPracticeDateStr;
	}

	public void setGetPracticeDateStr(String getPracticeDateStr) {
		this.getPracticeDateStr = getPracticeDateStr == null ? null : getPracticeDateStr.trim();
	}

	public String getHasCertificateStr() {
		return hasCertificateStr;
	}

	public void setHasCertificateStr(String hasCertificateStr) {
		this.hasCertificateStr = hasCertificateStr == null ? null : hasCertificateStr.trim();
	}

	public String getPhysiciansQualificationLevelStr() {
		return physiciansQualificationLevelStr;
	}

	public void setPhysiciansQualificationLevelStr(String physiciansQualificationLevelStr) {
		this.physiciansQualificationLevelStr = physiciansQualificationLevelStr == null ? null : physiciansQualificationLevelStr.trim();
	}

	public String getPhysiciansQualificationTypeStr() {
		return physiciansQualificationTypeStr;
	}

	public void setPhysiciansQualificationTypeStr(String physiciansQualificationTypeStr) {
		this.physiciansQualificationTypeStr = physiciansQualificationTypeStr == null ? null : physiciansQualificationTypeStr.trim();
	}

	public String getGetDateStr() {
		return getDateStr;
	}

	public void setGetDateStr(String getDateStr) {
		this.getDateStr = getDateStr == null ? null : getDateStr.trim();
	}

	public String getWorkDateStr() {
		return workDateStr;
	}

	public void setWorkDateStr(String workDateStr) {
		this.workDateStr = workDateStr == null ? null : workDateStr.trim();
	}

	public String getWorkUnitLevelStr() {
		return workUnitLevelStr;
	}

	public void setWorkUnitLevelStr(String workUnitLevelStr) {
		this.workUnitLevelStr = workUnitLevelStr == null ? null : workUnitLevelStr.trim();
	}

	public String getWorkUnitNatureStr() {
		return workUnitNatureStr;
	}

	public void setWorkUnitNatureStr(String workUnitNatureStr) {
		this.workUnitNatureStr = workUnitNatureStr == null ? null : workUnitNatureStr.trim();
	}

	public String getGetTitleDateStr() {
		return getTitleDateStr;
	}

	public void setGetTitleDateStr(String getTitleDateStr) {
		this.getTitleDateStr = getTitleDateStr == null ? null : getTitleDateStr.trim();
	}

	public String getSchoolTypeStr() {
		return schoolTypeStr;
	}

	public void setSchoolTypeStr(String schoolTypeStr) {
		this.schoolTypeStr = schoolTypeStr == null ? null : schoolTypeStr.trim();
	}

}
