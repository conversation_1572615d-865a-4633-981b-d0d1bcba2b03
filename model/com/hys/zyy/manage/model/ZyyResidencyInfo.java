package com.hys.zyy.manage.model;

import java.io.Serializable;

/**
 * 住院医师学员信息
 * <AUTHOR>
 */
public class ZyyResidencyInfo implements Serializable {
	private static final long serialVersionUID = 1L;
	/*
	 * 学员ID
	 */
	private Long id;
	/*
	 * 学员姓名
	 */
	private String realName;
	/*
	 * 性别 ：1，男； 2，女； 0，未知
	 */
	private Integer sex;
	/*
	 * 证件号码
	 */
	private String certificateNo;
	/*
	 * 人员类型：1，单位人；2：社会人；3：学位衔接
	 */
	private Integer residencySource;
	/*
	 * 学员来源：1，住院医；2，实习生；3，进修生；
	 */
	private Integer userOrigin;
	/*
	 * 年级
	 */
	private String year;
	/*
	 * 学员状态
	 */
	private Integer zyyUserStatus;
	/*
	 * 手机号码
	 */
	private String mobileNumber;

	public ZyyResidencyInfo() {
		super();
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getRealName() {
		return realName;
	}

	public void setRealName(String realName) {
		this.realName = realName;
	}

	public Integer getSex() {
		return sex;
	}

	public void setSex(Integer sex) {
		this.sex = sex;
	}

	public String getCertificateNo() {
		return certificateNo;
	}

	public void setCertificateNo(String certificateNo) {
		this.certificateNo = certificateNo;
	}

	public Integer getResidencySource() {
		return residencySource;
	}

	public void setResidencySource(Integer residencySource) {
		this.residencySource = residencySource;
	}

	public Integer getUserOrigin() {
		return userOrigin;
	}

	public void setUserOrigin(Integer userOrigin) {
		this.userOrigin = userOrigin;
	}

	public String getYear() {
		return year;
	}

	public void setYear(String year) {
		this.year = year;
	}

	public Integer getZyyUserStatus() {
		return zyyUserStatus;
	}

	public void setZyyUserStatus(Integer zyyUserStatus) {
		this.zyyUserStatus = zyyUserStatus;
	}

	public String getMobileNumber() {
		return mobileNumber;
	}

	public void setMobileNumber(String mobileNumber) {
		this.mobileNumber = mobileNumber;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ZyyResidencyInfo other = (ZyyResidencyInfo) obj;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		return true;
	}

	@Override
	public String toString() {
		return "ZyyResidencyInfo [id=" + id + ", realName=" + realName
				+ ", sex=" + sex + ", certificateNo=" + certificateNo
				+ ", residencySource=" + residencySource + ", userOrigin="
				+ userOrigin + ", year=" + year + ", zyyUserStatus="
				+ zyyUserStatus + ", mobileNumber=" + mobileNumber + "]";
	}
	
}
