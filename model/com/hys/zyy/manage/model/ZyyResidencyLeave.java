package com.hys.zyy.manage.model;

import java.util.Date;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：李海龙 May 4, 2012
 * 
 * 描述：
 * 
 * 说明:
 */
public class ZyyResidencyLeave extends ZyyBaseObject {

	private static final long serialVersionUID = 5439107249570578292L;
	
	/**
	 * 主键ID
	 */
	private Long id;
	
	/**
	 * 医院ID
	 */
	private Long zyyOrgId;
	
	/**
	 * 医院用户ID
	 */
	private Long zyyUserId;
	/**
	 * 科室用户ID
	 */
	private Long zyyUserId1;

	/**
	 * 住院医师ID
	 */
	private Long zyyUserId2;
	
	/**
	 * 开始时间
	 */
	private Date startDate;
	
	/**
	 * 结束时间
	 */
	private Date endDate;
	
	/**
	 * 请假事由 1 -病假 2 -事假 3 -婚假 4 -产假 5 -丧假 6 -其他
	 */
	private Integer reason;
	
	/**
	 * 请假类型ID
	 */
	private Long leaveTypeId;
	
	/**
	 * 轮转处理  1 -轮转不变 2 -补轮转 3 -退出轮转
	 */
	private Integer cycleDeal;
	
	/**
	 * 填写时间
	 */
	private Date writeDate;
	
	/**
	 * 最后更新时间
	 */
	private Date lastUpdateDate;

	/**
	 * 
	 * 是否按时收假
	 * 
	 */
	private Integer isReceiveLeave;
	
	/**
	 * 
	 * 收假时间
	 * 
	 */
	private Date receiveLeaveDate;
	
	private Integer status;
	
	/**
	 * 考勤请假规则ID
	 */
	private Long zyyAttendanceRuleId;
	
	/**
	 * 请假天数
	 */
	private Float leaveNum;
	
	/**
	 * 请假原因
	 */
	private String leaveReason;
	
	/**
	 * 审核状态：1为审核中 2为审核通过 3为审核未通过
	 */
	private Integer auditStatus;
	
	/**
	 * 请假申请提出时所在科室
	 */
	private Long deptId;
	
	/**
	 * 请假开始时间上午下午标识 ：1为上午 2为下午
	 */
	private Integer startTime;
	
	/**
	 * 请假结束时间上午下午标识 ：1为上午 2为下午
	 */
	private Integer endTime;
	/*
	 * 是否销假（1：已销假；其它：未销假）
	 */
	private Integer isDestroy;
	
	public ZyyResidencyLeave() {
		super();
	}

	public ZyyResidencyLeave(Long id, Integer isDestroy) {
		super();
		this.id = id;
		this.isDestroy = isDestroy;
	}

	public Integer getIsDestroy() {
		return isDestroy;
	}

	public void setIsDestroy(Integer isDestroy) {
		this.isDestroy = isDestroy;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getZyyOrgId() {
		return zyyOrgId;
	}

	public void setZyyOrgId(Long zyyOrgId) {
		this.zyyOrgId = zyyOrgId;
	}

	public Long getZyyUserId() {
		return zyyUserId;
	}

	public void setZyyUserId(Long zyyUserId) {
		this.zyyUserId = zyyUserId;
	}

	public Long getZyyUserId1() {
		return zyyUserId1;
	}

	public void setZyyUserId1(Long zyyUserId1) {
		this.zyyUserId1 = zyyUserId1;
	}

	public Long getZyyUserId2() {
		return zyyUserId2;
	}

	public void setZyyUserId2(Long zyyUserId2) {
		this.zyyUserId2 = zyyUserId2;
	}

	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	public Integer getReason() {
		return reason;
	}

	public void setReason(Integer reason) {
		this.reason = reason;
	}

	public Integer getCycleDeal() {
		return cycleDeal;
	}

	public void setCycleDeal(Integer cycleDeal) {
		this.cycleDeal = cycleDeal;
	}

	public Date getWriteDate() {
		return writeDate;
	}

	public void setWriteDate(Date writeDate) {
		this.writeDate = writeDate;
	}

	public Date getLastUpdateDate() {
		return lastUpdateDate;
	}

	public void setLastUpdateDate(Date lastUpdateDate) {
		this.lastUpdateDate = lastUpdateDate;
	}
	public Integer getIsReceiveLeave() {
		return isReceiveLeave;
	}

	public void setIsReceiveLeave(Integer isReceiveLeave) {
		this.isReceiveLeave = isReceiveLeave;
	}

	public Date getReceiveLeaveDate() {
		return receiveLeaveDate;
	}

	public void setReceiveLeaveDate(Date receiveLeaveDate) {
		this.receiveLeaveDate = receiveLeaveDate;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Long getZyyAttendanceRuleId() {
		return zyyAttendanceRuleId;
	}

	public void setZyyAttendanceRuleId(Long zyyAttendanceRuleId) {
		this.zyyAttendanceRuleId = zyyAttendanceRuleId;
	}

	public Float getLeaveNum() {
		return leaveNum;
	}

	public void setLeaveNum(Float leaveNum) {
		this.leaveNum = leaveNum;
	}

	public String getLeaveReason() {
		return leaveReason;
	}

	public void setLeaveReason(String leaveReason) {
		this.leaveReason = leaveReason;
	}

	public Integer getAuditStatus() {
		return auditStatus;
	}

	public void setAuditStatus(Integer auditStatus) {
		this.auditStatus = auditStatus;
	}

	public Long getDeptId() {
		return deptId;
	}

	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}

	public Integer getStartTime() {
		return startTime;
	}

	public void setStartTime(Integer startTime) {
		this.startTime = startTime;
	}

	public Integer getEndTime() {
		return endTime;
	}

	public void setEndTime(Integer endTime) {
		this.endTime = endTime;
	}

	public Long getLeaveTypeId() {
		return leaveTypeId;
	}

	public void setLeaveTypeId(Long leaveTypeId) {
		this.leaveTypeId = leaveTypeId;
	}
}
