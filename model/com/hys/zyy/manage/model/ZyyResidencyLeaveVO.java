package com.hys.zyy.manage.model;

import java.util.Date;
import java.util.List;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：李海龙 May 4, 2012
 * 
 * 描述：
 * 
 * 说明:
 */
public class ZyyResidencyLeaveVO extends ZyyResidencyLeave {

	private static final long serialVersionUID = -4591887750788017304L;
	
	private String orgName;

	/**
	 * 基地名称
	 */
	private String baseName ;
	
	/**
	 * 基地别名
	 */
	private String aliasName ;
	
	/**
	 * 真实姓名
	 */
	private String realName ;
	/**
	 * 手机号
	 */
	private String mobilNumber;
	
	/**
	 * 医院提交用户姓名
	 */
	private String userName1;
	
	/**
	 * 科室提交用户姓名
	 */
	private String userName2;
	
	/**
	 * 学员轮转信息
	 */
	private List<ZyyCycleTableResiCycleVO> cycleList ;
	
	/**
	 * 用户id
	 */
	private Long newUserId;
	
	/**
	 * 
	 * 年份id
	 * @return
	 */
	private Long recruitYearId;
	
	/**
	 * 年份
	 */
	private String year;
	
	/**
	 * 
	 * 基地id  
	 */
	private Long baseId;
	
	
	/**
	 * 科室名称 
	 */
	private String deptName;
	
	/**
	 * 用户状态
	 */
	private Integer zyyUserStatus;

	/**
	 * 最后更新状态时间
	 */
	private Date lastUpdateStatusDate ; 
	
	private Integer cycleType;
	
	/**
	 * 请假类型
	 */
	private String leaveType;
	
	/**
	 * 请假流程
	 */
	private Integer leaveProcess;
	
	private Integer teacherAuditStatus;
	
	private Integer deptAuditStatus;
	
	private Integer hospAuditStatus;
	
	private Integer tutorAuditStatus;
	
	private Integer baseAuditStatus;
	
	private Long zyyAttendanceLeaveType;
	
	//请假时间字符串 
	private String startDateStr;
	private String endDateStr;
	
	private String showStartTime;//开始时间
	private String showEndTime;//结束时间
	
	//是否有审核记录    1 有  2 没有
	private Integer recordStatus;
	//是否末级审核  0 否， 1是末级
	private Integer lastLeavel;
	private Integer leaveDays;//学员总计请假天数
	
	private String auditUserTypeSeq;//审核顺序
	
	private String isDestroyStr;
	
	/**
	 * 1、通过2不通过 空为 未审核
	 */
	private Integer currAuditStatus;
	
	// 附件
	private List<zyyResidencyLeaveAttachment> attaList;
	// 附件数量
	private Integer attachmentNumber;
	private String finalAutitRole;
	private List<ZyyResidencyLeaveVO> records;
	
	
	/**
	 * 证件号码
	 */
	private String certificateNo ;

	/**
	 * 人员类型
	 */
	private Integer residencySource;

	public Integer getResidencySource() {
		return residencySource;
	}

	public void setResidencySource(Integer residencySource) {
		this.residencySource = residencySource;
	}

	public String getCertificateNo() {
		return certificateNo;
	}

	public void setCertificateNo(String certificateNo) {
		this.certificateNo = certificateNo;
	}

	public ZyyResidencyLeaveVO() {
		super();
	}

	public ZyyResidencyLeaveVO(Long id, Integer isDestroy) {
		super(id, isDestroy);
	}

	public List<ZyyResidencyLeaveVO> getRecords() {
		return records;
	}

	public void setRecords(List<ZyyResidencyLeaveVO> records) {
		this.records = records;
	}

	public String getIsDestroyStr() {
		return isDestroyStr;
	}

	public void setIsDestroyStr(String isDestroyStr) {
		this.isDestroyStr = isDestroyStr == null ? null : isDestroyStr.trim();
	}

	public String getFinalAutitRole() {
		return finalAutitRole;
	}

	public void setFinalAutitRole(String finalAutitRole) {
		this.finalAutitRole = finalAutitRole;
	}

	public Integer getAttachmentNumber() {
		return attachmentNumber;
	}

	public void setAttachmentNumber(Integer attachmentNumber) {
		this.attachmentNumber = attachmentNumber;
	}

	public List<zyyResidencyLeaveAttachment> getAttaList() {
		return attaList;
	}

	public void setAttaList(List<zyyResidencyLeaveAttachment> attaList) {
		this.attaList = attaList;
	}

	public Integer getRecordStatus() {
		return recordStatus;
	}

	public void setRecordStatus(Integer recordStatus) {
		this.recordStatus = recordStatus;
	}

	public String getStartDateStr() {
		return startDateStr;
	}

	public void setStartDateStr(String startDateStr) {
		this.startDateStr = startDateStr;
	}

	public String getEndDateStr() {
		return endDateStr;
	}

	public void setEndDateStr(String endDateStr) {
		this.endDateStr = endDateStr;
	}

	public Long getZyyAttendanceLeaveType() {
		return zyyAttendanceLeaveType;
	}

	public void setZyyAttendanceLeaveType(Long zyyAttendanceLeaveType) {
		this.zyyAttendanceLeaveType = zyyAttendanceLeaveType;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public String getDeptName() {
		return deptName;
	}

	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}

	public Long getBaseId() {
		return baseId;
	}

	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}

	public Long getRecruitYearId() {
		return recruitYearId;
	}

	public void setRecruitYearId(Long recruitYearId) {
		this.recruitYearId = recruitYearId;
	}

	public String getYear() {
		return year;
	}

	public void setYear(String year) {
		this.year = year;
	}

	public Long getNewUserId() {
		return newUserId;
	}

	public void setNewUserId(Long newUserId) {
		this.newUserId = newUserId;
	}

	public String getBaseName() {
		return baseName;
	}

	public void setBaseName(String baseName) {
		this.baseName = baseName;
	}

	public String getAliasName() {
		return aliasName;
	}

	public void setAliasName(String aliasName) {
		this.aliasName = aliasName;
	}

	public String getRealName() {
		return realName;
	}

	public void setRealName(String realName) {
		this.realName = realName;
	}

	public String getUserName1() {
		return userName1;
	}

	public void setUserName1(String userName1) {
		this.userName1 = userName1;
	}

	public String getUserName2() {
		return userName2;
	}

	public void setUserName2(String userName2) {
		this.userName2 = userName2;
	}

	public List<ZyyCycleTableResiCycleVO> getCycleList() {
		return cycleList;
	}

	public void setCycleList(List<ZyyCycleTableResiCycleVO> cycleList) {
		this.cycleList = cycleList;
	}

	public Integer getZyyUserStatus() {
		return zyyUserStatus;
	}

	public void setZyyUserStatus(Integer zyyUserStatus) {
		this.zyyUserStatus = zyyUserStatus;
	}

	public Date getLastUpdateStatusDate() {
		return lastUpdateStatusDate;
	}

	public void setLastUpdateStatusDate(Date lastUpdateStatusDate) {
		this.lastUpdateStatusDate = lastUpdateStatusDate;
	}

	public Integer getCycleType() {
		return cycleType;
	}

	public void setCycleType(Integer cycleType) {
		this.cycleType = cycleType;
	}

	public String getLeaveType() {
		return leaveType;
	}

	public void setLeaveType(String leaveType) {
		this.leaveType = leaveType;
	}

	public Integer getLeaveProcess() {
		return leaveProcess;
	}

	public void setLeaveProcess(Integer leaveProcess) {
		this.leaveProcess = leaveProcess;
	}

	public Integer getTeacherAuditStatus() {
		return teacherAuditStatus;
	}

	public void setTeacherAuditStatus(Integer teacherAuditStatus) {
		this.teacherAuditStatus = teacherAuditStatus;
	}

	public Integer getDeptAuditStatus() {
		return deptAuditStatus;
	}

	public void setDeptAuditStatus(Integer deptAuditStatus) {
		this.deptAuditStatus = deptAuditStatus;
	}

	public String getShowStartTime() {
		return showStartTime;
	}

	public void setShowStartTime(String showStartTime) {
		this.showStartTime = showStartTime;
	}

	public String getShowEndTime() {
		return showEndTime;
	}

	public void setShowEndTime(String showEndTime) {
		this.showEndTime = showEndTime;
	}

	public String getAuditUserTypeSeq() {
		return auditUserTypeSeq;
	}

	public void setAuditUserTypeSeq(String auditUserTypeSeq) {
		this.auditUserTypeSeq = auditUserTypeSeq;
	}

	public Integer getHospAuditStatus() {
		return hospAuditStatus;
	}

	public void setHospAuditStatus(Integer hospAuditStatus) {
		this.hospAuditStatus = hospAuditStatus;
	}

	public Integer getTutorAuditStatus() {
		return tutorAuditStatus;
	}

	public void setTutorAuditStatus(Integer tutorAuditStatus) {
		this.tutorAuditStatus = tutorAuditStatus;
	}
	public Integer getBaseAuditStatus() {
		return baseAuditStatus;
	}
	public void setBaseAuditStatus(Integer baseAuditStatus) {
		this.baseAuditStatus = baseAuditStatus;
	}
	public Integer getLastLeavel() {
		return lastLeavel;
	}
	public void setLastLeavel(Integer lastLeavel) {
		this.lastLeavel = lastLeavel;
	}

	public Integer getLeaveDays() {
		return leaveDays;
	}
	public void setLeaveDays(Integer leaveDays) {
		this.leaveDays = leaveDays;
	}
	public String getMobilNumber() {
		return mobilNumber;
	}
	public void setMobilNumber(String mobilNumber) {
		this.mobilNumber = mobilNumber;
	}

	public Integer getCurrAuditStatus() {
		return currAuditStatus;
	}

	public void setCurrAuditStatus(Integer currAuditStatus) {
		this.currAuditStatus = currAuditStatus;
	} 
	
	
	
	
}