package com.hys.zyy.manage.model;

import java.io.Serializable;
import java.util.Date;

/**
 * 学员通知
 */
public class ZyyResidencyNotice implements Serializable {
	private static final long serialVersionUID = 1L;
	/*
	 * ID
	 */
	private String id;
	/*
	 * 省ID
	 */
	private Long zyyProvinceId;
	/*
	 * 年度ID
	 */
	private Long zyyRecruitYearId;
	/*
	 * 是否显示（1：是；0：否）
	 */
	private Integer isShow;
	/*
	 * 通知标题
	 */
	private String title;
	/*
	 * 通知内容
	 */
	private String content;
	/*
	 * 状态（1：启用；-1：失效）
	 */
	private Integer state;
	/*
	 * 创建者ID
	 */
	private Long createUserId;
	/*
	 * 创建时间
	 */
	private Date createTime;
	/*
	 * 修改时间
	 */
	private Date updateTime;

	public ZyyResidencyNotice() {
		super();
	}

	public ZyyResidencyNotice(Long zyyProvinceId, Long zyyRecruitYearId, Integer state) {
		super();
		this.zyyProvinceId = zyyProvinceId;
		this.zyyRecruitYearId = zyyRecruitYearId;
		this.state = state;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public Long getZyyProvinceId() {
		return zyyProvinceId;
	}

	public void setZyyProvinceId(Long zyyProvinceId) {
		this.zyyProvinceId = zyyProvinceId;
	}

	public Long getZyyRecruitYearId() {
		return zyyRecruitYearId;
	}

	public void setZyyRecruitYearId(Long zyyRecruitYearId) {
		this.zyyRecruitYearId = zyyRecruitYearId;
	}

	public Integer getIsShow() {
		return isShow;
	}

	public void setIsShow(Integer isShow) {
		this.isShow = isShow;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	public Long getCreateUserId() {
		return createUserId;
	}

	public void setCreateUserId(Long createUserId) {
		this.createUserId = createUserId;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ZyyResidencyNotice other = (ZyyResidencyNotice) obj;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		return true;
	}

	@Override
	public String toString() {
		return "ZyyResidencyNotice [id=" + id + ", zyyProvinceId="
				+ zyyProvinceId + ", zyyRecruitYearId=" + zyyRecruitYearId
				+ ", isShow=" + isShow + ", title=" + title + ", content="
				+ content + ", state=" + state + ", createUserId="
				+ createUserId + ", createTime=" + createTime + ", updateTime="
				+ updateTime + "]";
	}

}
