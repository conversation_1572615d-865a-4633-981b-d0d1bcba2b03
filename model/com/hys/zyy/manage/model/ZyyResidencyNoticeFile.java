package com.hys.zyy.manage.model;

import java.io.Serializable;

/**
 * 学员通知附件
 */
public class ZyyResidencyNoticeFile implements Serializable {
	private static final long serialVersionUID = 1L;
	/*
	 * ID
	 */
	private String id;
	/*
	 * 通知ID
	 */
	private String ZyyResidencyNoticeId;
	/*
	 * 附件名称
	 */
	private String fileName;
	/*
	 * 附件路径
	 */
	private String filePath;
	/*
	 * 状态（1：有效；-1：失效）
	 */
	private Integer state;

	public ZyyResidencyNoticeFile() {
		super();
	}

	public ZyyResidencyNoticeFile(String zyyResidencyNoticeId, Integer state) {
		super();
		ZyyResidencyNoticeId = zyyResidencyNoticeId;
		this.state = state;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getZyyResidencyNoticeId() {
		return ZyyResidencyNoticeId;
	}

	public void setZyyResidencyNoticeId(String zyyResidencyNoticeId) {
		ZyyResidencyNoticeId = zyyResidencyNoticeId;
	}

	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	public String getFilePath() {
		return filePath;
	}

	public void setFilePath(String filePath) {
		this.filePath = filePath;
	}

	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ZyyResidencyNoticeFile other = (ZyyResidencyNoticeFile) obj;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		return true;
	}

	@Override
	public String toString() {
		return "ZyyResidencyNoticeFile [id=" + id + ", ZyyResidencyNoticeId="
				+ ZyyResidencyNoticeId + ", fileName=" + fileName
				+ ", filePath=" + filePath + ", state=" + state + "]";
	}

}
