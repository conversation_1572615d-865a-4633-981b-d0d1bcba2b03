package com.hys.zyy.manage.model;

import java.util.List;

public class ZyyResidencyNoticeVO extends ZyyResidencyNotice {

	private String multiName;
	private String deleteFileIdStr;
	private List<ZyyResidencyNoticeFileVO> fileList;

	public ZyyResidencyNoticeVO() {
		super();
	}

	public ZyyResidencyNoticeVO(Long zyyProvinceId, Long zyyRecruitYearId, Integer state) {
		super(zyyProvinceId, zyyRecruitYearId, state);
	}

	public String getMultiName() {
		return multiName;
	}

	public void setMultiName(String multiName) {
		this.multiName = multiName;
	}

	public String getDeleteFileIdStr() {
		return deleteFileIdStr;
	}

	public void setDeleteFileIdStr(String deleteFileIdStr) {
		this.deleteFileIdStr = deleteFileIdStr == null ? null : deleteFileIdStr.trim();
	}

	public List<ZyyResidencyNoticeFileVO> getFileList() {
		return fileList;
	}

	public void setFileList(List<ZyyResidencyNoticeFileVO> fileList) {
		this.fileList = fileList;
	}
	
}
