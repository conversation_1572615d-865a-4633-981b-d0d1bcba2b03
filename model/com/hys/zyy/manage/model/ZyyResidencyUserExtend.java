package com.hys.zyy.manage.model;

import java.util.Date;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：李海龙 2012-06-05
 * 
 * 描述：
 * 
 * 说明:
 */
public class ZyyResidencyUserExtend extends ZyyBaseObject {

	private static final long serialVersionUID = 1858460153426914391L;

	/**
	 * 用户ID
	 */
	private Long zyyUserId ;
	
	/**
	 * 学制(住院医师需要轮转时间) 1.一年制 2.二年制 3.三年制
	 */
	private Integer schoolSystem ;
	
	/**
	 * 阶段 1.一阶段 2.二阶段
	 */
	private Integer stage ;
	
	/**
	 * 入院考试是否通过
	 */
	private Integer isExamPassed ;
	
	/**
	 * 延长毕业年数
	 */
	private Integer graduationYear ;
	
	/**
	 * 有无医师执业证书 1.有 0.无
	 */
	private Integer hasCertificate ;
	
	/**
	 * 医师执业证书编码
	 */
	private String certificateNumber ;
	
	/**
	 * 取得时间
	 */
	private Date certificateDate ;
	
	/**
	 * 证书执业范围
	 */
	private String certificateScope ;
		
	/**
	 * 状态 0.保存 1.提交
	 */
	private Integer status ;

	public ZyyResidencyUserExtend() {
		super();
	}

	public ZyyResidencyUserExtend(Long zyyUserId, Integer schoolSystem) {
		super();
		this.zyyUserId = zyyUserId;
		this.schoolSystem = schoolSystem;
	}

	public Long getZyyUserId() {
		return zyyUserId;
	}

	public void setZyyUserId(Long zyyUserId) {
		this.zyyUserId = zyyUserId;
	}

	public Integer getSchoolSystem() {
		return schoolSystem;
	}

	public void setSchoolSystem(Integer schoolSystem) {
		this.schoolSystem = schoolSystem;
	}

	public Integer getStage() {
		return stage;
	}

	public void setStage(Integer stage) {
		this.stage = stage;
	}

	public Integer getIsExamPassed() {
		return isExamPassed;
	}

	public void setIsExamPassed(Integer isExamPassed) {
		this.isExamPassed = isExamPassed;
	}

	public Integer getGraduationYear() {
		return graduationYear;
	}

	public void setGraduationYear(Integer graduationYear) {
		this.graduationYear = graduationYear;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Integer getHasCertificate() {
		return hasCertificate;
	}

	public void setHasCertificate(Integer hasCertificate) {
		this.hasCertificate = hasCertificate;
	}

	public String getCertificateNumber() {
		return certificateNumber;
	}

	public void setCertificateNumber(String certificateNumber) {
		this.certificateNumber = certificateNumber;
	}

	public Date getCertificateDate() {
		return certificateDate;
	}

	public void setCertificateDate(Date certificateDate) {
		this.certificateDate = certificateDate;
	}

	public String getCertificateScope() {
		return certificateScope;
	}

	public void setCertificateScope(String certificateScope) {
		this.certificateScope = certificateScope;
	}
}