package com.hys.zyy.manage.model;

import java.util.Date;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：张伟清 2010-07-10
 * 
 * 描述：住院医师报名录取资格
 * 
 * 说明:
 */
public class ZyyResidentQualification extends ZyyBaseObject {

	private static final long serialVersionUID = -9060574947998296415L;

	/**
	 * 主键ID
	 */
	private Long id;

	/**
	 * 住院医师ID
	 */
	private Long residencyId;

	/**
	 * 年度ID
	 */
	private Long recruitYearId;

	/**
	 * 最终报名资格 1.-通过  0.不通过
	 */
	private Integer finalSignupQualification ;
	
	/**
	 * 最终录取资格 1.-通过  0.不通过
	 */
	private Integer finalAdmissionQualification ;
	
	/**
	 * 学员志愿ID
	 */
	private Long recruitResidencyWill ;
	
	/**
	 * 审核状态 1.一级用户可查看 2.二级用户可查看 3.三级用户可查看
	 */
	private Integer qualificationStatus ;
	
	/**
	 * 最后修改时间
	 */
	private Date lastUpdateDate ;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getResidencyId() {
		return residencyId;
	}

	public void setResidencyId(Long residencyId) {
		this.residencyId = residencyId;
	}

	public Long getRecruitYearId() {
		return recruitYearId;
	}

	public void setRecruitYearId(Long recruitYearId) {
		this.recruitYearId = recruitYearId;
	}

	public Integer getFinalSignupQualification() {
		return finalSignupQualification;
	}

	public void setFinalSignupQualification(Integer finalSignupQualification) {
		this.finalSignupQualification = finalSignupQualification;
	}

	public Integer getFinalAdmissionQualification() {
		return finalAdmissionQualification;
	}

	public void setFinalAdmissionQualification(Integer finalAdmissionQualification) {
		this.finalAdmissionQualification = finalAdmissionQualification;
	}

	public Long getRecruitResidencyWill() {
		return recruitResidencyWill;
	}

	public void setRecruitResidencyWill(Long recruitResidencyWill) {
		this.recruitResidencyWill = recruitResidencyWill;
	}

	public Date getLastUpdateDate() {
		return lastUpdateDate;
	}

	public void setLastUpdateDate(Date lastUpdateDate) {
		this.lastUpdateDate = lastUpdateDate;
	}

	public Integer getQualificationStatus() {
		return qualificationStatus;
	}

	public void setQualificationStatus(Integer qualificationStatus) {
		this.qualificationStatus = qualificationStatus;
	}
}