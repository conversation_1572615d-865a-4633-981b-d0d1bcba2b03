	package com.hys.zyy.manage.model;

import java.util.Date;

import com.google.common.primitives.Ints;
import com.hys.security.util.SecurityUtils;
import com.hys.zyy.manage.constants.Constants;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：陈明凯 2012-03-19
 * 
 * 描述：菜单管理中前端页面用的是EasyUI中的TreeGrid插件，此插件需要的数据格式 我们在这里定义
 * 
 * 说明:
 */
public class ZyyResourceManageModel extends ZyyBaseObject {

	/**
	 * 
	 */
	private static final long serialVersionUID = -4990867447341268130L;
	/***
	 * 节点id
	 */
	private Long id ;
	/**
	 * 父节点id
	 */
	//fastjson 分不清楚 _parentId 和 parentId lzq 2020-01-08
//	private Long _parentId;
	private Long parentId;
	/**
	 * 资源名称
	 */
	private String resourceName;
	/**
	 * 资源备注
	 */
	private String resourceRemark;
	/**
	 * admin分配的
	 */
	private String adminChecks;
	/**
	 * 省超级管理员分配的/省厅
	 */
	private String proChecks;
	
	
	public String getResourceRemark() {
		return resourceRemark;
	}
	public void setResourceRemark(String resourceRemark) {
		this.resourceRemark = resourceRemark;
	}
	
//	public Long get_parentId() {
//		return _parentId;
//	}
//	public void set_parentId(Long _parentId) {
//		this._parentId = _parentId;
//	}
	public Long getParentId() {
		return parentId;
	}
	public void setParentId(Long parentId) {
		this.parentId = parentId;
	}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getResourceName() {
		return resourceName;
	}
	public void setResourceName(String resourceName) {
		this.resourceName = resourceName;
	}
	public String getAdminChecks() {
		return adminChecks;
	}
	public void setAdminChecks(String adminChecks) {
		this.adminChecks = adminChecks;
	}
	public String getProChecks() {
		return proChecks;
	}
	public void setProChecks(String proChecks) {
		this.proChecks = proChecks;
	}
	
}