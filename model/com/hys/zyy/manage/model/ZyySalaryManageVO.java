package com.hys.zyy.manage.model;

import java.util.List;
import java.util.Map;

public class ZyySalaryManageVO extends ZyySalaryManage {

	private static final long serialVersionUID = -5224704976436415540L;
	
	private String realName;
	
	private Integer residencySource;
			
	private String year;
	
	private String baseAliasName;
	
	private Integer highestRecordSchool;//毕业学历
	
	private Integer degreeConvergence; //residencySource为1且degreeConvergence为1 表示学位衔接
	
	private Map<Long, ZyySalaryManageDetail> salaryDetail;
	
	private List<ZyySalaryManageDetail> salaryDetailList;

	public Map<Long, ZyySalaryManageDetail> getSalaryDetail() {
		return salaryDetail;
	}

	public void setSalaryDetail(Map<Long, ZyySalaryManageDetail> salaryDetail) {
		this.salaryDetail = salaryDetail;
	}

	public String getRealName() {
		return realName;
	}

	public void setRealName(String realName) {
		this.realName = realName;
	}

	public Integer getResidencySource() {
		return residencySource;
	}

	public void setResidencySource(Integer residencySource) {
		this.residencySource = residencySource;
	}

	public String getYear() {
		return year;
	}

	public void setYear(String year) {
		this.year = year;
	}

	public List<ZyySalaryManageDetail> getSalaryDetailList() {
		return salaryDetailList;
	}

	public void setSalaryDetailList(List<ZyySalaryManageDetail> salaryDetailList) {
		this.salaryDetailList = salaryDetailList;
	}

	public String getBaseAliasName() {
		return baseAliasName;
	}

	public void setBaseAliasName(String baseAliasName) {
		this.baseAliasName = baseAliasName;
	}

	public Integer getHighestRecordSchool() {
		return highestRecordSchool;
	}

	public void setHighestRecordSchool(Integer highestRecordSchool) {
		this.highestRecordSchool = highestRecordSchool;
	}

	public Integer getDegreeConvergence() {
		return degreeConvergence;
	}

	public void setDegreeConvergence(Integer degreeConvergence) {
		this.degreeConvergence = degreeConvergence;
	}
		
	
}
