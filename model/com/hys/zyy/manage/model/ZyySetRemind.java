package com.hys.zyy.manage.model;

public class ZyySetRemind extends ZyyBaseObject{
	
	private static final long serialVersionUID = 12651321321225522L;
	/**
	 * id
	 */
	private Long id;
	/**
	 * 医院ID
	 */
	private Long orgId;
	/**
	 * 用户类型
	 */
	private Integer userType;
	/**
	 * 提醒时间 /天
	 */
	private Integer remindTime;
	/**
	 * 提醒方式 1 邮件 2微信 3其他
	 */
	private Long remindType;
	/**
	 * 提醒内容
	 */
	private String remindContent;
	/**
	 * 状态 1有效 -1删除
	 */
	private Integer status;
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getOrgId() {
		return orgId;
	}
	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}
	public Integer getUserType() {
		return userType;
	}
	public void setUserType(Integer userType) {
		this.userType = userType;
	}
	public Integer getRemindTime() {
		return remindTime;
	}
	public void setRemindTime(Integer remindTime) {
		this.remindTime = remindTime;
	}
	public Long getRemindType() {
		return remindType;
	}
	public void setRemindType(Long remindType) {
		this.remindType = remindType;
	}
	public String getRemindContent() {
		return remindContent;
	}
	public void setRemindContent(String remindContent) {
		this.remindContent = remindContent;
	}
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	
	
	

}
