package com.hys.zyy.manage.model;

import java.util.List;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：张伟清 2012-03-27
 * 
 * 描述：报名表字段设置表
 * 
 * 说明:
 */
public class ZyySignFieldSet extends ZyyBaseObject {

	private static final long serialVersionUID = -5513956276306546735L;

	/**
	 * 主键ID
	 */
	private Long id ;
	
	/**
	 * 组织机构
	 */
	private Long zyyOrgId ;
	
	/**
	 * 字段描述
	 */
	private String fieldDescription ;
	
	/**
	 * 字段名称 
	 */
	private String fieldName ;
	
	/**
	 * 是否必填 
	 */
	private Integer isRequired ;
	
	/**
	 * 是否允许修改 0-正常 1-无效(disabled在设置页面)
	 */
	private Integer isUpdated ;
	
	/**
	 * list 
	 */
	private List<ZyySignFieldSet> zyySignFieldSetList;

	public List<ZyySignFieldSet> getZyySignFieldSetList() {
		return zyySignFieldSetList;
	}

	public void setZyySignFieldSetList(List<ZyySignFieldSet> zyySignFieldSetList) {
		this.zyySignFieldSetList = zyySignFieldSetList;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getZyyOrgId() {
		return zyyOrgId;
	}

	public void setZyyOrgId(Long zyyOrgId) {
		this.zyyOrgId = zyyOrgId;
	}

	public String getFieldDescription() {
		return fieldDescription;
	}

	public void setFieldDescription(String fieldDescription) {
		this.fieldDescription = fieldDescription;
	}

	public String getFieldName() {
		return fieldName;
	}

	public void setFieldName(String fieldName) {
		this.fieldName = fieldName;
	}

	public Integer getIsRequired() {
		return isRequired;
	}

	public void setIsRequired(Integer isRequired) {
		this.isRequired = isRequired;
	}
	
	public Integer getIsUpdated() {
		return isUpdated;
	}

	public void setIsUpdated(Integer isUpdated) {
		this.isUpdated = isUpdated;
	}
}