package com.hys.zyy.manage.model;

import java.io.Serializable;

public class ZyySignIn implements Serializable {
	private static final long serialVersionUID = 1L;
	/*
	 * 用户名
	 */
	private String accountName;
	/*
	 * 密码
	 */
	private String accountPassword;
	/*
	 * 使用密码模式
	 */
	private boolean passwordMode = false;
	/*
	 * AES加密key
	 */
	private String aesKey = "HYSZYY2000060223";
	/*
	 * 目标地址（自动登录后跳转到哪个菜单，默认为首页）
	 */
	private String targetUrl = "/index";
	/*
	 * 客户端IP
	 */
	private String clientIp;
	/*
	 * 时间戳
	 */
	private Long timeSpan;
	/*
	 * 签名
	 */
	private String sign;

	public ZyySignIn() {
		super();
	}

	public String getAccountName() {
		return accountName;
	}

	public void setAccountName(String accountName) {
		this.accountName = accountName == null ? null : accountName.trim();
	}

	public String getAccountPassword() {
		return accountPassword;
	}

	public void setAccountPassword(String accountPassword) {
		this.accountPassword = accountPassword == null ? null : accountPassword.trim();
	}

	public boolean getPasswordMode() {
		return passwordMode;
	}

	public void setPasswordMode(boolean passwordMode) {
		this.passwordMode = passwordMode;
	}

	public String getAesKey() {
		return aesKey;
	}

	public void setAesKey(String aesKey) {
		this.aesKey = aesKey == null ? null : aesKey.trim();
	}

	public Long getTimeSpan() {
		return timeSpan;
	}

	public void setTimeSpan(Long timeSpan) {
		this.timeSpan = timeSpan;
	}

	public String getTargetUrl() {
		return targetUrl;
	}

	public void setTargetUrl(String targetUrl) {
		this.targetUrl = targetUrl == null ? null : targetUrl.trim();
	}

	public String getClientIp() {
		return clientIp;
	}

	public void setClientIp(String clientIp) {
		this.clientIp = clientIp == null ? null : clientIp.trim();
	}

	public String getSign() {
		return sign;
	}

	public void setSign(String sign) {
		this.sign = sign == null ? null : sign.trim();
	}

	@Override
	public String toString() {
		return "ZyySignIn [accountName=" + accountName + ", accountPassword="
				+ accountPassword + ", passwordMode=" + passwordMode
				+ ", aesKey=" + aesKey + ", targetUrl=" + targetUrl
				+ ", clientIp=" + clientIp + ", timeSpan=" + timeSpan
				+ ", sign=" + sign + "]";
	}
	
}