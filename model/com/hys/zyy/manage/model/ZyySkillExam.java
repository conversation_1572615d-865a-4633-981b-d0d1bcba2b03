package com.hys.zyy.manage.model;

import java.util.Date;
import com.hys.zyy.manage.util.annotation.Column;
import com.hys.zyy.manage.util.annotation.Id;
import com.hys.zyy.manage.util.annotation.Table;

/**
 * 技术考核  考场
 * <AUTHOR>	
 * @date 2021-09-08
 */
@Table("ZYY_SKILL_EXAM")
public class ZyySkillExam extends ZyyBaseObject {

	private static final long serialVersionUID = -7399901902073916206L;

	//主键ID
	@Id("ZYY_SKILL_EXAM_SEQ.nextval")
	@Column("id")
    private Long id;
    
	@Column("TABLE_ID")
    private Long tableId;
	
	@Column("NAME")
    private String name;
	
	@Column("ADDRESS")
    private String address;
	
	@Column("REMARK")
    private String remark;

	@Column("ID_REMARK")
	private String idRemark;
	
	@Column("REMARK_SHOW")
    private Long remarkShow;
    
	@Column("CREATE_DATE")
    private Date createDate;
	
	@Column("UPDATE_DATE")	
    private Date updateDate;
	
	@Column("EXAM_START_DATE")
    private Date examStartDate;
	
	@Column("EXAM_END_DATE")
    private Date examEndDate;
	
	@Column("EXAM_END_FINAL_DATE")
    private Date examEndFinalDate;
	
	@Column("CREATE_USER_ID")
    private Long createUserId;
	
	@Column("UPDATE_USER_ID")
    private Long updateUserId;
	
	@Column("PUBLISH_FEEDBACK_STUDENT_SHOW")
    private Long publishFeedbackStudentShow;
	
	@Column("TECHER_LIMIT_MINUTE")
    private Long techerLimitMinute;
	
	@Column("PUBLISH_FEEDBACK_TECHER_SHOW")
    private Long publishFeedbackTecherShow;
	
	@Column("FLAG")
    private Long flag;
	
	@Column("PUBLISH_STATUS")
    private Long publishStatus;
	
	@Column("STUDENT_NUM")
    private Long studentNum;
	
	@Column("TECHER_NUM")
    private Long techerNum;
	
	@Column("TECHER_LIMIT_STATUS")
    private Long techerLimitStatus;

	public String getIdRemark() {
		return idRemark;
	}

	public void setIdRemark(String idRemark) {
		this.idRemark = idRemark;
	}

	public Date getExamEndFinalDate() {
		return examEndFinalDate;
	}

	public void setExamEndFinalDate(Date examEndFinalDate) {
		this.examEndFinalDate = examEndFinalDate;
	}

	public Long getTecherLimitStatus() {
		return techerLimitStatus;
	}

	public void setTecherLimitStatus(Long techerLimitStatus) {
		this.techerLimitStatus = techerLimitStatus;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getTableId() {
		return tableId;
	}

	public void setTableId(Long tableId) {
		this.tableId = tableId;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Date getUpdateDate() {
		return updateDate;
	}

	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	public Date getExamStartDate() {
		return examStartDate;
	}

	public void setExamStartDate(Date examStartDate) {
		this.examStartDate = examStartDate;
	}

	public Date getExamEndDate() {
		return examEndDate;
	}

	public void setExamEndDate(Date examEndDate) {
		this.examEndDate = examEndDate;
	}

	public Long getCreateUserId() {
		return createUserId;
	}

	public void setCreateUserId(Long createUserId) {
		this.createUserId = createUserId;
	}

	public Long getUpdateUserId() {
		return updateUserId;
	}

	public void setUpdateUserId(Long updateUserId) {
		this.updateUserId = updateUserId;
	}

	public Long getPublishFeedbackStudentShow() {
		return publishFeedbackStudentShow;
	}

	public void setPublishFeedbackStudentShow(Long publishFeedbackStudentShow) {
		this.publishFeedbackStudentShow = publishFeedbackStudentShow;
	}

	public Long getTecherLimitMinute() {
		return techerLimitMinute;
	}

	public void setTecherLimitMinute(Long techerLimitMinute) {
		this.techerLimitMinute = techerLimitMinute;
	}

	public Long getPublishFeedbackTecherShow() {
		return publishFeedbackTecherShow;
	}

	public void setPublishFeedbackTecherShow(Long publishFeedbackTecherShow) {
		this.publishFeedbackTecherShow = publishFeedbackTecherShow;
	}

	public Long getFlag() {
		return flag;
	}

	public void setFlag(Long flag) {
		this.flag = flag;
	}

	public Long getPublishStatus() {
		return publishStatus;
	}

	public void setPublishStatus(Long publishStatus) {
		this.publishStatus = publishStatus;
	}

	public Long getStudentNum() {
		return studentNum;
	}

	public void setStudentNum(Long studentNum) {
		this.studentNum = studentNum;
	}

	public Long getTecherNum() {
		return techerNum;
	}

	public void setTecherNum(Long techerNum) {
		this.techerNum = techerNum;
	}

	public Long getRemarkShow() {
		return remarkShow;
	}

	public void setRemarkShow(Long remarkShow) {
		this.remarkShow = remarkShow;
	}
    
}
