package com.hys.zyy.manage.model;

import java.util.Date;
import com.hys.zyy.manage.util.annotation.Column;
import com.hys.zyy.manage.util.annotation.Id;
import com.hys.zyy.manage.util.annotation.Table;

@Table("ZYY_SKILL_EXAM_FEEDBACK")
public class ZyySkillExamFeedback extends ZyyBaseObject {

	private static final long serialVersionUID = -1674903135085165204L;

	//主键ID
	@Id("ZYY_SKILL_EXAM_FEEDBACK_SEQ.nextval")
	@Column("id")
    private Long id;
    
	@Column("EXAM_ID")
    private Long examId;
	
	@Column("USER_ID")
    private Long userId;
	
	@Column("CREATE_DATE")
    private Date createDate;
	
	@Column("UPDATE_DATE")	
    private Date updateDate;

	@Column("SCORE")
    private Long score;
	
	@Column("FLAG")
    private Long flag;
	
	@Column("NOTE")
    private String note;

	public void setDefault(){
		this.setCreateDate(new Date());
		this.setUpdateDate(new Date());
		this.setFlag(1L);
	}
	
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getExamId() {
		return examId;
	}

	public void setExamId(Long examId) {
		this.examId = examId;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Long getScore() {
		return score;
	}

	public void setScore(Long score) {
		this.score = score;
	}

	public Long getFlag() {
		return flag;
	}

	public void setFlag(Long flag) {
		this.flag = flag;
	}

	public String getNote() {
		return note;
	}

	public void setNote(String note) {
		this.note = note;
	}

	public Date getUpdateDate() {
		return updateDate;
	}

	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	
}
