package com.hys.zyy.manage.model;

import java.util.Date;

import com.hys.zyy.manage.util.annotation.Column;
import com.hys.zyy.manage.util.annotation.Id;
import com.hys.zyy.manage.util.annotation.Table;

@Table("ZYY_SKILL_EXAM_Invigilate")
public class ZyySkillExamInvigilate extends ZyyBaseObject {

	private static final long serialVersionUID = -4449327408457505075L;

	//主键ID
	@Id("ZYY_SKILL_EXAM_INVIGILATE_SEQ.nextval")
	@Column("id")
    private Long id;
    
	@Column("exam_id")
    private Long examId;
	
	@Column("user_id")
    private Long userId;
	
	@Column("create_date")
    private Date createDate;

	@Column("FLAG")
	private Long flag;

	@Column("UPDATE_DATE")
	private Date updateDate;

	public Long getFlag() {
		return flag;
	}

	public void setFlag(Long flag) {
		this.flag = flag;
	}

	public Date getUpdateDate() {
		return updateDate;
	}

	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getExamId() {
		return examId;
	}

	public void setExamId(Long examId) {
		this.examId = examId;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}
	
	
}
