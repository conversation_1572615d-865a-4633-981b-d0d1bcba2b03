package com.hys.zyy.manage.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class ZyySkillExamResultVO implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 369749129452372771L;

	private String zyySyncId;
	
	private Long zyyUserId;
	
	/**
	 * 结业考核结果信息标识符
	 */
	private String syncGraduationId;
	
	/**
	 * 技能考站编号
	 */
	private String stationName;
	
	/**
	 * 考核时间
	 */
	private Date examDate;
	
	/**
	 * 考站考核内容
	 */
	private String examContent;
	
	/**
	 * 技能考核考站成绩
	 */
	private BigDecimal skillExamStationScore;
		
	/**
	 * 技能考核考站结果
	 */
	private Integer skillExamStationResult;
		
	private Integer status;
	
	private Long skillStationId;

	public String getZyySyncId() {
		return zyySyncId;
	}

	public void setZyySyncId(String zyySyncId) {
		this.zyySyncId = zyySyncId;
	}

	public String getSyncGraduationId() {
		return syncGraduationId;
	}

	public void setSyncGraduationId(String syncGraduationId) {
		this.syncGraduationId = syncGraduationId;
	}

	public String getStationName() {
		return stationName;
	}

	public void setStationName(String stationName) {
		this.stationName = stationName;
	}

	public Date getExamDate() {
		return examDate;
	}

	public void setExamDate(Date examDate) {
		this.examDate = examDate;
	}

	public String getExamContent() {
		return examContent;
	}

	public void setExamContent(String examContent) {
		this.examContent = examContent;
	}

	public BigDecimal getSkillExamStationScore() {
		return skillExamStationScore;
	}

	public void setSkillExamStationScore(BigDecimal skillExamStationScore) {
		this.skillExamStationScore = skillExamStationScore;
	}

	public Integer getSkillExamStationResult() {
		return skillExamStationResult;
	}

	public void setSkillExamStationResult(Integer skillExamStationResult) {
		this.skillExamStationResult = skillExamStationResult;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Long getZyyUserId() {
		return zyyUserId;
	}

	public void setZyyUserId(Long zyyUserId) {
		this.zyyUserId = zyyUserId;
	}

	public Long getSkillStationId() {
		return skillStationId;
	}

	public void setSkillStationId(Long skillStationId) {
		this.skillStationId = skillStationId;
	}
		
}
