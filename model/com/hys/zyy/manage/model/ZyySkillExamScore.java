package com.hys.zyy.manage.model;

import java.util.Date;
import com.hys.zyy.manage.util.annotation.Column;
import com.hys.zyy.manage.util.annotation.Id;
import com.hys.zyy.manage.util.annotation.Table;

@Table("ZYY_SKILL_EXAM_SCORE")
public class ZyySkillExamScore extends ZyyBaseObject {

	private static final long serialVersionUID = -7399901902073916206L;

	//主键ID
	@Id("ZYY_SKILL_EXAM_SCORE_SEQ.nextval")
	@Column("id")
    private Long id;
    
	@Column("EXAM_ID")
    private Long examId;
	
	@Column("STUDENT_ID")
    private Long studentId;
	
	@Column("INVIGILATE_ID")
    private Long invigilateId;
    
    @Column("TABLE_ID")
    private Long tableId;
	
	@Column("CREATE_DATE")
    private Date createDate;
	
	@Column("UPDATE_DATE")	
    private Date updateDate;
	
	@Column("TOTAL_SCORE")
    private Double totalScore;
	
	@Column("CASE_SHOW")
    private Long caseShow;
	
	@Column("RESULT_SHOW")
    private Long resultShow;
	
	@Column("RESULT_OK")
    private String resultOk;
	
	@Column("RESULT_FAIL")
    private String resultFail;
	
	@Column("RESULT_ADVISE")
    private String resultAdvise;
	
	@Column("PASS")
    private Long pass;
	
	@Column("NOTE_SHOW")
    private Long noteShow;
	
	@Column("NOTE")
    private String note;
	
	@Column("FLAG")
    private Long flag;
	
	public void setDefault(){
		this.setCreateDate(new Date());
		this.setUpdateDate(new Date());
		this.setFlag(1L);
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getExamId() {
		return examId;
	}

	public void setExamId(Long examId) {
		this.examId = examId;
	}

	public Long getStudentId() {
		return studentId;
	}

	public void setStudentId(Long studentId) {
		this.studentId = studentId;
	}

	public Long getInvigilateId() {
		return invigilateId;
	}

	public void setInvigilateId(Long invigilateId) {
		this.invigilateId = invigilateId;
	}

	public Long getTableId() {
		return tableId;
	}

	public void setTableId(Long tableId) {
		this.tableId = tableId;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Date getUpdateDate() {
		return updateDate;
	}

	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	public Double getTotalScore() {
		return totalScore;
	}

	public void setTotalScore(Double totalScore) {
		this.totalScore = totalScore;
	}

	public Long getCaseShow() {
		return caseShow;
	}

	public void setCaseShow(Long caseShow) {
		this.caseShow = caseShow;
	}

	public Long getResultShow() {
		return resultShow;
	}

	public void setResultShow(Long resultShow) {
		this.resultShow = resultShow;
	}

	public String getResultOk() {
		return resultOk;
	}

	public void setResultOk(String resultOk) {
		this.resultOk = resultOk;
	}

	public String getResultFail() {
		return resultFail;
	}

	public void setResultFail(String resultFail) {
		this.resultFail = resultFail;
	}

	public String getResultAdvise() {
		return resultAdvise;
	}

	public void setResultAdvise(String resultAdvise) {
		this.resultAdvise = resultAdvise;
	}

	public Long getPass() {
		return pass;
	}

	public void setPass(Long pass) {
		this.pass = pass;
	}

	public Long getNoteShow() {
		return noteShow;
	}

	public void setNoteShow(Long noteShow) {
		this.noteShow = noteShow;
	}

	public String getNote() {
		return note;
	}

	public void setNote(String note) {
		this.note = note;
	}

	public Long getFlag() {
		return flag;
	}

	public void setFlag(Long flag) {
		this.flag = flag;
	}
	
}
