package com.hys.zyy.manage.model;

import java.util.Date;
import com.hys.zyy.manage.util.annotation.Column;
import com.hys.zyy.manage.util.annotation.Id;
import com.hys.zyy.manage.util.annotation.Table;

@Table("ZYY_SKILL_EXAM_SCORE_CASE")
public class ZyySkillExamScoreCase extends ZyyBaseObject {

	private static final long serialVersionUID = 7402891787117105751L;

	//主键ID
	@Id("ZYY_SKILL_EXAM_SCORE_C_SEQ.nextval")
	@Column("id")
    private Long id;
    
	@Column("EXAM_SCORE_ID")
    private Long examScoreId;
	
	@Column("CREATE_DATE")
    private Date createDate;
	
	@Column("UPDATE_DATE")	
    private Date updateDate;
	
	@Column("HOSPITAL_NUMBER")
    private String hospitalNumber;
	
	@Column("AGE")
    private String age;
	
	@Column("VISIT_RESULT")
    private String visitResult;
	
	@Column("SEX")
    private Long sex;
	
	@Column("VISIT_TYPE")
    private Long visitType;
	
	@Column("COMPLEX_TYPE")
    private Long complexType;
	
	@Column("ADAPTABILITY_TYPE")
    private Long adaptabilityType;
	
	public void setDefault(){
		this.setCreateDate(new Date());
		this.setUpdateDate(new Date());
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getExamScoreId() {
		return examScoreId;
	}

	public void setExamScoreId(Long examScoreId) {
		this.examScoreId = examScoreId;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Date getUpdateDate() {
		return updateDate;
	}

	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	public String getHospitalNumber() {
		return hospitalNumber;
	}

	public void setHospitalNumber(String hospitalNumber) {
		this.hospitalNumber = hospitalNumber;
	}

	public String getAge() {
		return age;
	}

	public void setAge(String age) {
		this.age = age;
	}

	public String getVisitResult() {
		return visitResult;
	}

	public void setVisitResult(String visitResult) {
		this.visitResult = visitResult;
	}

	public Long getSex() {
		return sex;
	}

	public void setSex(Long sex) {
		this.sex = sex;
	}

	public Long getVisitType() {
		return visitType;
	}

	public void setVisitType(Long visitType) {
		this.visitType = visitType;
	}

	public Long getComplexType() {
		return complexType;
	}

	public void setComplexType(Long complexType) {
		this.complexType = complexType;
	}

	public Long getAdaptabilityType() {
		return adaptabilityType;
	}

	public void setAdaptabilityType(Long adaptabilityType) {
		this.adaptabilityType = adaptabilityType;
	}

}
