package com.hys.zyy.manage.model;

import java.util.Date;
import com.hys.zyy.manage.util.annotation.Column;
import com.hys.zyy.manage.util.annotation.Id;
import com.hys.zyy.manage.util.annotation.Table;

@Table("ZYY_SKILL_EXAM_SCORE_ITEM")
public class ZyySkillExamScoreItem extends ZyyBaseObject {

	private static final long serialVersionUID = -1674903135085165204L;

	//主键ID
	@Id("ZYY_SKILL_EXAM_SCORE_I_SEQ.nextval")
	@Column("id")
    private Long id;
    
	@Column("EXAM_SCORE_ID")
    private Long examScoreId;
	
	@Column("CREATE_DATE")
    private Date createDate;
	
	@Column("UPDATE_DATE")	
    private Date updateDate;
	
	@Column("TABLE_ITEM_ID")
    private Long tableItemId;
	
	@Column("FLAG")
    private Long falg;
	
	@Column("TOTAL_SCORE")
    private Double totalScore;

	public void setDefault(){
		this.setCreateDate(new Date());
		this.setUpdateDate(new Date());
		this.setFalg(1L);
	}
	
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getExamScoreId() {
		return examScoreId;
	}

	public void setExamScoreId(Long examScoreId) {
		this.examScoreId = examScoreId;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Date getUpdateDate() {
		return updateDate;
	}

	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	public Long getTableItemId() {
		return tableItemId;
	}

	public void setTableItemId(Long tableItemId) {
		this.tableItemId = tableItemId;
	}

	public Long getFalg() {
		return falg;
	}

	public void setFalg(Long falg) {
		this.falg = falg;
	}

	public Double getTotalScore() {
		return totalScore;
	}

	public void setTotalScore(Double totalScore) {
		this.totalScore = totalScore;
	}

}
