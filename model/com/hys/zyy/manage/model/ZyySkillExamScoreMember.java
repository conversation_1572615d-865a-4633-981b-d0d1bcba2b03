package com.hys.zyy.manage.model;

import java.util.Date;
import com.hys.zyy.manage.util.annotation.Column;
import com.hys.zyy.manage.util.annotation.Id;
import com.hys.zyy.manage.util.annotation.Table;

@Table("ZYY_SKILL_EXAM_SCORE_MEMBER")
public class ZyySkillExamScoreMember extends ZyyBaseObject {

	private static final long serialVersionUID = -4616916219152454828L;

	//主键ID
	@Id("ZYY_SKILL_EXAM_SCORE_M_SEQ.nextval")
	@Column("id")
    private Long id;
    
	@Column("EXAM_SCORE_ID")
    private Long examScoreId;
	
	@Column("CREATE_DATE")
    private Date createDate;
	
	@Column("UPDATE_DATE")	
    private Date updateDate;
	
	@Column("STUDENT_NAME")
    private String studentName;
	
	@Column("STUDENT_IDCARD")
    private String studentIdcard;
	
	@Column("TECHER_NAME")
    private String techerName;
	
	@Column("TECHER_IDCARD")
    private String techerIdcard;
	
	@Column("TEST_ADDRESS")
    private String testAddress;
	
	@Column("TEST_START_DATE")	
    private Date testStartDate;
	
	@Column("TEST_END_DATE")	
    private Date testEndDate;

	public void setDefault(){
		this.setCreateDate(new Date());
		this.setUpdateDate(new Date());
	}
	
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getExamScoreId() {
		return examScoreId;
	}

	public void setExamScoreId(Long examScoreId) {
		this.examScoreId = examScoreId;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Date getUpdateDate() {
		return updateDate;
	}

	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	public String getStudentName() {
		return studentName;
	}

	public void setStudentName(String studentName) {
		this.studentName = studentName;
	}

	public String getStudentIdcard() {
		return studentIdcard;
	}

	public void setStudentIdcard(String studentIdcard) {
		this.studentIdcard = studentIdcard;
	}

	public String getTecherName() {
		return techerName;
	}

	public void setTecherName(String techerName) {
		this.techerName = techerName;
	}

	public String getTecherIdcard() {
		return techerIdcard;
	}

	public void setTecherIdcard(String techerIdcard) {
		this.techerIdcard = techerIdcard;
	}

	public String getTestAddress() {
		return testAddress;
	}

	public void setTestAddress(String testAddress) {
		this.testAddress = testAddress;
	}

	public Date getTestStartDate() {
		return testStartDate;
	}

	public void setTestStartDate(Date testStartDate) {
		this.testStartDate = testStartDate;
	}

	public Date getTestEndDate() {
		return testEndDate;
	}

	public void setTestEndDate(Date testEndDate) {
		this.testEndDate = testEndDate;
	}
	
}
