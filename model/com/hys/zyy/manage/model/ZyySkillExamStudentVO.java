package com.hys.zyy.manage.model;

public class ZyySkillExamStudentVO extends ZyySkillExamStudent {
	
	private Long skillExamScoreId;
	private Double totalScore, passScore;
	// 老师选择的通过不通过
	private Long pass;
	// 测试是否通过 1=通过 2=不通过
	private Long testPass;
	private Integer scoreState, skillExamState;
	private String realName, certificateNo, skillExamName, skillExamStartDateStr, skillExamEndDateStr;
	/*
	 * 1=修改评分；2=查看评分
	 */
	private Integer type;
	
	public Long getSkillExamScoreId() {
		return skillExamScoreId;
	}

	public void setSkillExamScoreId(Long skillExamScoreId) {
		this.skillExamScoreId = skillExamScoreId;
	}

	public Double getTotalScore() {
		return totalScore;
	}

	public void setTotalScore(Double totalScore) {
		this.totalScore = totalScore;
	}

	public Integer getScoreState() {
		return scoreState;
	}

	public void setScoreState(Integer scoreState) {
		this.scoreState = scoreState;
	}

	public Integer getSkillExamState() {
		return skillExamState;
	}

	public void setSkillExamState(Integer skillExamState) {
		this.skillExamState = skillExamState;
	}

	public String getRealName() {
		return realName;
	}

	public void setRealName(String realName) {
		this.realName = realName == null ? null : realName.trim();
	}

	public String getCertificateNo() {
		return certificateNo;
	}

	public void setCertificateNo(String certificateNo) {
		this.certificateNo = certificateNo == null ? null : certificateNo.trim();
	}

	public String getSkillExamName() {
		return skillExamName;
	}

	public void setSkillExamName(String skillExamName) {
		this.skillExamName = skillExamName == null ? null : skillExamName.trim();
	}

	public String getSkillExamStartDateStr() {
		return skillExamStartDateStr;
	}

	public void setSkillExamStartDateStr(String skillExamStartDateStr) {
		this.skillExamStartDateStr = skillExamStartDateStr == null ? null : skillExamStartDateStr.trim();
	}

	public String getSkillExamEndDateStr() {
		return skillExamEndDateStr;
	}

	public void setSkillExamEndDateStr(String skillExamEndDateStr) {
		this.skillExamEndDateStr = skillExamEndDateStr == null ? null : skillExamEndDateStr.trim();
	}

	public Double getPassScore() {
		return passScore;
	}

	public void setPassScore(Double passScore) {
		this.passScore = passScore;
	}

	public Long getPass() {
		return pass;
	}

	public void setPass(Long pass) {
		this.pass = pass;
	}

	public Long getTestPass() {

		if (totalScore != null && passScore != null && passScore > 0) {
			if (totalScore >= passScore) {
				return 1L;
			}
		} else {
			if (pass != null) {
				return pass;
			}
		}
		return 2L;
	}

	public void setTestPass(Long testPass) {
		this.testPass = testPass;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}
	
}