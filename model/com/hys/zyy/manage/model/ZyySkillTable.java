package com.hys.zyy.manage.model;

import java.util.Date;
import com.hys.zyy.manage.util.annotation.Column;
import com.hys.zyy.manage.util.annotation.Id;
import com.hys.zyy.manage.util.annotation.Table;

/**
 * 技术考核  量表
 * <AUTHOR>	
 * @date 2021-08-30
 */
@Table("ZYY_SKILL_TABLE")
public class ZyySkillTable extends ZyyBaseObject {

	private static final long serialVersionUID = -2657812485709207148L;

	//主键ID
	@Id("ZYY_SKILL_TABLE_SEQ.nextval")
	@Column("id")
    private Long id;
    
    //量表类型ID
	@Column("SKILL_TYPE_ID")
    private Long skillTypeId;
    
    //删除状态
	@Column("flag")
    private Long flag;
    
    //量表状态
	@Column("ENABLED")
    private Long enabled;
    
    //更新时间
	@Column("UPDATE_DATE")
    private Date updateDate;
    
    //创建时间
	@Column("create_Date")
    private Date createDate;
        
    //量表名称
	@Column("NAME")
    private String name;
	
    //总分
	@Column("total_Score")
    private Double totalScore;
    
    //及格分
	@Column("pass_Score")
    private Double passScore;
    
    //注意事项显示
	@Column("note_Show")
    private Long noteShow;
	
	//注意事项显示
	@Column("note")
	private String note;
    
    //考核评语显示
	@Column("test_Result_Show")
    private Long testResultShow;
    
    //病例显示
	@Column("case_Show")
    private Long caseShow;
    
    public ZyySkillTable(){}
    
	public ZyySkillTable(Long id, Long skillTypeId, Long flag, Long enabled,
			Date updateDate, Date createDate, String name, Double totalScore,
			Double passScore, Long noteShow, Long testResultShow, Long caseShow,String note) {
		super();
		this.id = id;
		this.skillTypeId = skillTypeId;
		this.flag = flag;
		this.enabled = enabled;
		this.updateDate = updateDate;
		this.createDate = createDate;
		this.name = name;
		this.totalScore = totalScore;
		this.passScore = passScore;
		this.noteShow = noteShow;
		this.testResultShow = testResultShow;
		this.caseShow = caseShow;
		this.note=note;
	}

	public String getNote() {
		return note;
	}

	public void setNote(String note) {
		this.note = note;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getSkillTypeId() {
		return skillTypeId;
	}

	public void setSkillTypeId(Long skillTypeId) {
		this.skillTypeId = skillTypeId;
	}

	public Long getFlag() {
		return flag;
	}

	public void setFlag(Long flag) {
		this.flag = flag;
	}

	public Long getEnabled() {
		return enabled;
	}

	public void setEnabled(Long enabled) {
		this.enabled = enabled;
	}

	public Date getUpdateDate() {
		return updateDate;
	}

	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Double getTotalScore() {
		return totalScore;
	}

	public void setTotalScore(Double totalScore) {
		this.totalScore = totalScore;
	}

	public Double getPassScore() {
		return passScore;
	}

	public void setPassScore(Double passScore) {
		this.passScore = passScore;
	}

	public Long getNoteShow() {
		return noteShow;
	}

	public void setNoteShow(Long noteShow) {
		this.noteShow = noteShow;
	}

	public Long getTestResultShow() {
		return testResultShow;
	}

	public void setTestResultShow(Long testResultShow) {
		this.testResultShow = testResultShow;
	}

	public Long getCaseShow() {
		return caseShow;
	}

	public void setCaseShow(Long caseShow) {
		this.caseShow = caseShow;
	}
    
}
