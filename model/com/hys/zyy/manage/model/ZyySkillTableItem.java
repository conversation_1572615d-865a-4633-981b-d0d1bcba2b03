package com.hys.zyy.manage.model;

import java.util.Date;

import com.hys.zyy.manage.util.annotation.Column;
import com.hys.zyy.manage.util.annotation.Id;
import com.hys.zyy.manage.util.annotation.Table;

/**
 * 技术考核  量表-评分细则
 * <AUTHOR>	
 * @date 2021-08-30
 */
@Table("ZYY_SKILL_TABLE_ITEM")
public class ZyySkillTableItem extends ZyyBaseObject {

	private static final long serialVersionUID = -7996444965976310149L;
    
	//主键ID
	@Id("ZYY_SKILL_TABLE_ITEM_SEQ.nextval")
	@Column("id")
    private Long id;
    
    //量表ID
	@Column("SKILL_TABLE_ID")
    private Long skillTableId;
    
    //考核项类型ID
	@Column("item_Type_Id")
    private Long itemTypeId;

    //名称
	@Column("name")
    private String name;

    //总分
	@Column("total_Score")
    private Double totalScore;

    //删除状态
	@Column("flag")
    private Long flag;
    
    //创建时间
	@Column("create_Date")
    private Date createDate;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getSkillTableId() {
		return skillTableId;
	}

	public void setSkillTableId(Long skillTableId) {
		this.skillTableId = skillTableId;
	}

	public Long getItemTypeId() {
		return itemTypeId;
	}

	public void setItemTypeId(Long itemTypeId) {
		this.itemTypeId = itemTypeId;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Double getTotalScore() {
		return totalScore;
	}

	public void setTotalScore(Double totalScore) {
		this.totalScore = totalScore;
	}

	public Long getFlag() {
		return flag;
	}

	public void setFlag(Long flag) {
		this.flag = flag;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

}
