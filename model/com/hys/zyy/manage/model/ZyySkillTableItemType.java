package com.hys.zyy.manage.model;

import java.util.Date;

import com.hys.zyy.manage.util.annotation.Column;
import com.hys.zyy.manage.util.annotation.Id;
import com.hys.zyy.manage.util.annotation.Table;

/**
 * 技术考核  量表-评分细则类型
 * <AUTHOR>	
 * @date 2021-08-30
 */
@Table("ZYY_SKILL_TABLE_ITEM_TYPE")
public class ZyySkillTableItemType extends ZyyBaseObject {

	private static final long serialVersionUID = -5759506861170444335L;
	
	//主键ID
	@Id("ZYY_SKILL_TABLE_ITEM_TYPE_SEQ.nextval")
	@Column("id")
    private Long id;
    
    //量表ID
	@Column("SKILL_TABLE_ID")
    private Long skillTableId;

    //名称
	@Column("name")
    private String name;

    //删除状态
	@Column("flag")
    private Long flag;
    
    //创建时间
	@Column("create_Date")
    private Date createDate;
    
    //总分
	@Column("total_Score")
    private Double totalScore;
    
    public ZyySkillTableItemType(){}
    
    public ZyySkillTableItemType(Long id, String name, Double totalScore) {
		super();
		this.id = id;
		this.name = name;
		this.totalScore = totalScore;
	}

	public ZyySkillTableItemType(Long id, Long skillTableId, String name,
			Long flag, Date createDate, Double totalScore) {
		super();
		this.id = id;
		this.skillTableId = skillTableId;
		this.name = name;
		this.flag = flag;
		this.createDate = createDate;
		this.totalScore = totalScore;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getSkillTableId() {
		return skillTableId;
	}

	public void setSkillTableId(Long skillTableId) {
		this.skillTableId = skillTableId;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Long getFlag() {
		return flag;
	}

	public void setFlag(Long flag) {
		this.flag = flag;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Double getTotalScore() {
		return totalScore;
	}

	public void setTotalScore(Double totalScore) {
		this.totalScore = totalScore;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ZyySkillTableItemType other = (ZyySkillTableItemType) obj;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		return true;
	}
	
}
