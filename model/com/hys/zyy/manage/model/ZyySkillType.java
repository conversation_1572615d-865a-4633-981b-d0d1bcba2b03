package com.hys.zyy.manage.model;

import java.util.Date;

import com.hys.zyy.manage.util.annotation.Column;
import com.hys.zyy.manage.util.annotation.Id;
import com.hys.zyy.manage.util.annotation.Table;

/**
 * 技术考核  量表类型
 * <AUTHOR>	
 * @date 2021-08-26 上午 08:55:00
 */
@Table("zyy_skill_type")
public class ZyySkillType extends ZyyBaseObject {

	private static final long serialVersionUID = 8939371247716117551L;

	//主键ID
	@Id("zyy_skill_type_SEQ.nextval")
	@Column("id")
    private Long id;

    //量表类型名称
	@Column("type_name")
    private String typeName;

    //更新时间
	@Column("update_date")
    private Date updateDate;
	
	@Column("create_date")
    private Date createDate;
    
	@Column("flag")
    private Long flag;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getTypeName() {
		return typeName;
	}

	public void setTypeName(String typeName) {
		this.typeName = typeName;
	}

	public Date getUpdateDate() {
		return updateDate;
	}

	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	public Long getFlag() {
		return flag;
	}

	public void setFlag(Long flag) {
		this.flag = flag;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}
	
}
