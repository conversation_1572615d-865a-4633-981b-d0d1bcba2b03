package com.hys.zyy.manage.model;

import java.io.Serializable;
import java.util.Date;

import com.hys.zyy.manage.util.BaseModel;

/**
 * SOAP病例汇报评估表
 * <AUTHOR>
 */
public class ZyySoapForm extends BaseModel implements Serializable{
	private static final long serialVersionUID = 1L;
	/*
	 * ID
	 */
	private Long id;
	/*
	 * 学员ID
	 */
	private Long residencyId;
	/*
	 * 学员所在科室
	 */
	private Long deptId;
	/*
	 * 学员类型（1=第一年；2=第二年；3=第三年）
	 */
	private Integer residencyType;
	/*
	 * 学员满意度（1,2,3...）
	 */
	private Integer residencyJoyLevel;
	/*
	 * 考官姓名
	 */
	private String examinerName;
	/*
	 * 考官类型（1=主任医师；2=副主任医师；3=主治医师）
	 */
	private Integer examinerType;
	/*
	 * 考官满意程度（1,2,3...）
	 */
	private Integer examinerJoyLevel;
	/*
	 * 考官评语
	 */
	private String examinerComment;
	/*
	 * 考核时间
	 */
	private Date assessTime;
	/*
	 * 考核地点（1=病房；2=门诊；3=急诊；4=ICU；5=其他）
	 */
	private Integer assessLocale;
	/*
	 * 病人年龄
	 */
	private Integer patientAge;
	/*
	 * 病人性别（1=男；2=女）
	 */
	private Integer patientSex;
	/*
	 * 就诊类型（1=初诊；2=复诊）
	 */
	private Integer visitType;
	/*
	 * 诊断
	 */
	private String diacrisis;
	/*
	 * 病情复杂程度（1=低；2=中；3=高）
	 */
	private Integer diseaseState;
	/*
	 * 基本情况（年龄、性别等）（0=不适用评价；1=内容完全遗漏；2=内容部分遗漏；3=内容完整）
	 */
	private Integer jbqk;
	/*
	 * 主要症状描述
	 */
	private Integer zyzzms;
	/*
	 * 主要伴随阳性/阴性症状
	 */
	private Integer zybs;
	/*
	 * 相关处理及反应
	 */
	private Integer xgcl;
	/*
	 * 其他相关情况说明
	 */
	private Integer qtxgqksm;
	/*
	 * 生命体征
	 */
	private Integer smtz;
	/*
	 * 心肺基本查体
	 */
	private Integer cfjbct;
	/*
	 * 重要阳性和阴性体征
	 */
	private Integer zyyxhyxtz;
	/*
	 * 重要辅助检查
	 */
	private Integer zyfzjc;
	/*
	 * 其他情况说明
	 */
	private Integer qtqksm;
	/*
	 * 简单总结
	 */
	private Integer jdzj;
	/*
	 * 列举问题
	 */
	private Integer ljwt;
	/*
	 * 分析问题的原因/依据
	 */
	private Integer fxwtdyyyj;
	/*
	 * 安排辅助检查
	 */
	private Integer apfzjc;
	/*
	 * 安排治疗和健康指导
	 */
	private Integer apzlhjkzd;
	/*
	 * 随访时间与项目
	 */
	private Integer sfjsyxm;
	/*
	 * 资料收集（1、2、3、4、5）
	 */
	private Integer zlsj;
	/*
	 * 列举问题（总体评估）
	 */
	private Integer ljwtZtpg;
	/*
	 * 诊疗计划
	 */
	private Integer zljh;
	/*
	 * 组织效能
	 */
	private Integer zzxn;
	/*
	 * 沟通表达
	 */
	private Integer gtbd;
	/*
	 * 职业素养
	 */
	private Integer zysy;
	/*
	 * 整体表现
	 */
	private Integer ztbx;
	/*
	 * 直接观察时间（分钟）
	 */
	private String zjgcsj;
	/*
	 * 反馈时间（分钟）
	 */
	private String fksj;
	/*
	 * 状态（1=有效；-1=失效）
	 */
	private Integer state;
	private Date createTime;
	private Date updateTime;
	/*
	 * PDF路径
	 */
	private String pdfUrl;

	public ZyySoapForm() {
		super();
	}

	public ZyySoapForm(Long id) {
		super();
		this.id = id;
	}
	
	public ZyySoapForm(Long id, String pdfUrl) {
		super();
		this.id = id;
		this.pdfUrl = pdfUrl;
	}

	public ZyySoapForm(Long id, Integer residencyJoyLevel, Integer examinerJoyLevel) {
		super();
		this.id = id;
		this.residencyJoyLevel = residencyJoyLevel;
		this.examinerJoyLevel = examinerJoyLevel;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getResidencyId() {
		return residencyId;
	}

	public void setResidencyId(Long residencyId) {
		this.residencyId = residencyId;
	}

	public Long getDeptId() {
		return deptId;
	}

	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}

	public Integer getResidencyType() {
		return residencyType;
	}

	public void setResidencyType(Integer residencyType) {
		this.residencyType = residencyType;
	}

	public Integer getResidencyJoyLevel() {
		return residencyJoyLevel;
	}

	public void setResidencyJoyLevel(Integer residencyJoyLevel) {
		this.residencyJoyLevel = residencyJoyLevel;
	}

	public String getExaminerName() {
		return examinerName;
	}

	public void setExaminerName(String examinerName) {
		this.examinerName = examinerName;
	}

	public Integer getExaminerType() {
		return examinerType;
	}

	public void setExaminerType(Integer examinerType) {
		this.examinerType = examinerType;
	}

	public Integer getExaminerJoyLevel() {
		return examinerJoyLevel;
	}

	public void setExaminerJoyLevel(Integer examinerJoyLevel) {
		this.examinerJoyLevel = examinerJoyLevel;
	}

	public String getExaminerComment() {
		return examinerComment;
	}

	public void setExaminerComment(String examinerComment) {
		this.examinerComment = examinerComment;
	}

	public Date getAssessTime() {
		return assessTime;
	}

	public void setAssessTime(Date assessTime) {
		this.assessTime = assessTime;
	}

	public Integer getAssessLocale() {
		return assessLocale;
	}

	public void setAssessLocale(Integer assessLocale) {
		this.assessLocale = assessLocale;
	}

	public Integer getPatientAge() {
		return patientAge;
	}

	public void setPatientAge(Integer patientAge) {
		this.patientAge = patientAge;
	}

	public Integer getPatientSex() {
		return patientSex;
	}

	public void setPatientSex(Integer patientSex) {
		this.patientSex = patientSex;
	}

	public Integer getVisitType() {
		return visitType;
	}

	public void setVisitType(Integer visitType) {
		this.visitType = visitType;
	}

	public String getDiacrisis() {
		return diacrisis;
	}

	public void setDiacrisis(String diacrisis) {
		this.diacrisis = diacrisis == null ? null : diacrisis.trim();
	}

	public Integer getDiseaseState() {
		return diseaseState;
	}

	public void setDiseaseState(Integer diseaseState) {
		this.diseaseState = diseaseState;
	}

	public Integer getJbqk() {
		return jbqk;
	}

	public void setJbqk(Integer jbqk) {
		this.jbqk = jbqk;
	}

	public Integer getZyzzms() {
		return zyzzms;
	}

	public void setZyzzms(Integer zyzzms) {
		this.zyzzms = zyzzms;
	}

	public Integer getZybs() {
		return zybs;
	}

	public void setZybs(Integer zybs) {
		this.zybs = zybs;
	}

	public Integer getXgcl() {
		return xgcl;
	}

	public void setXgcl(Integer xgcl) {
		this.xgcl = xgcl;
	}

	public Integer getQtxgqksm() {
		return qtxgqksm;
	}

	public void setQtxgqksm(Integer qtxgqksm) {
		this.qtxgqksm = qtxgqksm;
	}

	public Integer getSmtz() {
		return smtz;
	}

	public void setSmtz(Integer smtz) {
		this.smtz = smtz;
	}

	public Integer getCfjbct() {
		return cfjbct;
	}

	public void setCfjbct(Integer cfjbct) {
		this.cfjbct = cfjbct;
	}

	public Integer getZyyxhyxtz() {
		return zyyxhyxtz;
	}

	public void setZyyxhyxtz(Integer zyyxhyxtz) {
		this.zyyxhyxtz = zyyxhyxtz;
	}

	public Integer getZyfzjc() {
		return zyfzjc;
	}

	public void setZyfzjc(Integer zyfzjc) {
		this.zyfzjc = zyfzjc;
	}

	public Integer getQtqksm() {
		return qtqksm;
	}

	public void setQtqksm(Integer qtqksm) {
		this.qtqksm = qtqksm;
	}

	public Integer getJdzj() {
		return jdzj;
	}

	public void setJdzj(Integer jdzj) {
		this.jdzj = jdzj;
	}

	public Integer getLjwt() {
		return ljwt;
	}

	public void setLjwt(Integer ljwt) {
		this.ljwt = ljwt;
	}

	public Integer getFxwtdyyyj() {
		return fxwtdyyyj;
	}

	public void setFxwtdyyyj(Integer fxwtdyyyj) {
		this.fxwtdyyyj = fxwtdyyyj;
	}

	public Integer getApfzjc() {
		return apfzjc;
	}

	public void setApfzjc(Integer apfzjc) {
		this.apfzjc = apfzjc;
	}

	public Integer getApzlhjkzd() {
		return apzlhjkzd;
	}

	public void setApzlhjkzd(Integer apzlhjkzd) {
		this.apzlhjkzd = apzlhjkzd;
	}

	public Integer getSfjsyxm() {
		return sfjsyxm;
	}

	public void setSfjsyxm(Integer sfjsyxm) {
		this.sfjsyxm = sfjsyxm;
	}

	public Integer getZlsj() {
		return zlsj;
	}

	public void setZlsj(Integer zlsj) {
		this.zlsj = zlsj;
	}

	public Integer getLjwtZtpg() {
		return ljwtZtpg;
	}

	public void setLjwtZtpg(Integer ljwtZtpg) {
		this.ljwtZtpg = ljwtZtpg;
	}

	public Integer getZljh() {
		return zljh;
	}

	public void setZljh(Integer zljh) {
		this.zljh = zljh;
	}

	public Integer getZzxn() {
		return zzxn;
	}

	public void setZzxn(Integer zzxn) {
		this.zzxn = zzxn;
	}

	public Integer getGtbd() {
		return gtbd;
	}

	public void setGtbd(Integer gtbd) {
		this.gtbd = gtbd;
	}

	public Integer getZysy() {
		return zysy;
	}

	public void setZysy(Integer zysy) {
		this.zysy = zysy;
	}

	public Integer getZtbx() {
		return ztbx;
	}

	public void setZtbx(Integer ztbx) {
		this.ztbx = ztbx;
	}

	public String getZjgcsj() {
		return zjgcsj;
	}

	public void setZjgcsj(String zjgcsj) {
		this.zjgcsj = zjgcsj == null ? null : zjgcsj.trim();
	}

	public String getFksj() {
		return fksj;
	}

	public void setFksj(String fksj) {
		this.fksj = fksj == null ? null : fksj.trim();
	}

	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public String getPdfUrl() {
		return pdfUrl;
	}

	public void setPdfUrl(String pdfUrl) {
		this.pdfUrl = pdfUrl == null ? null : pdfUrl.trim();
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ZyySoapForm other = (ZyySoapForm) obj;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		return true;
	}

	@Override
	public String toString() {
		return "ZyySoapForm [id=" + id + ", residencyId=" + residencyId + ", deptId=" + deptId + ", residencyType="
				+ residencyType + ", residencyJoyLevel=" + residencyJoyLevel + ", examinerName=" + examinerName
				+ ", examinerType=" + examinerType + ", examinerJoyLevel=" + examinerJoyLevel + ", examinerComment="
				+ examinerComment + ", assessTime=" + assessTime + ", assessLocale=" + assessLocale + ", patientAge="
				+ patientAge + ", patientSex=" + patientSex + ", visitType=" + visitType + ", diacrisis=" + diacrisis
				+ ", diseaseState=" + diseaseState + ", jbqk=" + jbqk + ", zyzzms=" + zyzzms + ", zybs=" + zybs
				+ ", xgcl=" + xgcl + ", qtxgqksm=" + qtxgqksm + ", smtz=" + smtz + ", cfjbct=" + cfjbct + ", zyyxhyxtz="
				+ zyyxhyxtz + ", zyfzjc=" + zyfzjc + ", qtqksm=" + qtqksm + ", jdzj=" + jdzj + ", ljwt=" + ljwt
				+ ", fxwtdyyyj=" + fxwtdyyyj + ", apfzjc=" + apfzjc + ", apzlhjkzd=" + apzlhjkzd + ", sfjsyxm="
				+ sfjsyxm + ", zlsj=" + zlsj + ", ljwtZtpg=" + ljwtZtpg + ", zljh=" + zljh + ", zzxn=" + zzxn
				+ ", gtbd=" + gtbd + ", zysy=" + zysy + ", ztbx=" + ztbx + ", zjgcsj=" + zjgcsj + ", fksj=" + fksj
				+ ", state=" + state + ", createTime=" + createTime + ", updateTime=" + updateTime + ", pdfUrl="
				+ pdfUrl + "]";
	}
	
}