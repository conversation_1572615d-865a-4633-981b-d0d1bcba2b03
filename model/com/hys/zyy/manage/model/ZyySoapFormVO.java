package com.hys.zyy.manage.model;

public class ZyySoapFormVO extends ZyySoapForm {
	
	private Long yearId, baseId, teacherId;
	private String realName, year, baseName, deptName, assessTimeStr, startDateStr, endDateStr;
	private Double jbqkAvgScore, zyzzmsAvgScore, zybsAvgScore, xgclAvgScore, qtxgqksmAvgScore, smtzAvgScore,
			cfjbctAvgScore, zyyxhyxtzAvgScore, zyfzjcAvgScore, qtqksmAvgScore, jdzjAvgScore, ljwtAvgScore,
			fxwtdyyyjAvgScore, apfzjcAvgScore, apzlhjkzdAvgScore, sfjsyxmAvgScore, zlsjAvgScore, ljwtZtpgAvgScore,
			zljhAvgScore, zzxnAvgScore, gtbdAvgScore, zysyAvgScore, ztbxAvgScore;

	public ZyySoapFormVO() {
		super();
	}

	public ZyySoapFormVO(Long id) {
		super(id);
	}

	public ZyySoapFormVO(Long id, String pdfUrl) {
		super(id, pdfUrl);
	}

	public ZyySoapFormVO(Long id, Integer residencyJoyLevel, Integer examinerJoyLevel) {
		super(id, residencyJoyLevel, examinerJoyLevel);
	}

	public Long getYearId() {
		return yearId;
	}

	public void setYearId(Long yearId) {
		this.yearId = yearId;
	}

	public Long getBaseId() {
		return baseId;
	}

	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}

	public Long getTeacherId() {
		return teacherId;
	}

	public void setTeacherId(Long teacherId) {
		this.teacherId = teacherId;
	}

	public String getRealName() {
		return realName;
	}

	public void setRealName(String realName) {
		this.realName = realName == null ? null : realName.trim();
	}

	public String getYear() {
		return year;
	}

	public void setYear(String year) {
		this.year = year == null ? null : year.trim();
	}

	public String getBaseName() {
		return baseName;
	}

	public void setBaseName(String baseName) {
		this.baseName = baseName == null ? null : baseName.trim();
	}

	public String getDeptName() {
		return deptName;
	}

	public void setDeptName(String deptName) {
		this.deptName = deptName == null ? null : deptName.trim();
	}

	public String getAssessTimeStr() {
		return assessTimeStr;
	}

	public void setAssessTimeStr(String assessTimeStr) {
		this.assessTimeStr = assessTimeStr == null ? null : assessTimeStr.trim();
	}

	public String getStartDateStr() {
		return startDateStr;
	}

	public void setStartDateStr(String startDateStr) {
		this.startDateStr = startDateStr;
	}

	public String getEndDateStr() {
		return endDateStr;
	}

	public void setEndDateStr(String endDateStr) {
		this.endDateStr = endDateStr;
	}

	public Double getJbqkAvgScore() {
		return jbqkAvgScore;
	}

	public void setJbqkAvgScore(Double jbqkAvgScore) {
		this.jbqkAvgScore = jbqkAvgScore;
	}

	public Double getZyzzmsAvgScore() {
		return zyzzmsAvgScore;
	}

	public void setZyzzmsAvgScore(Double zyzzmsAvgScore) {
		this.zyzzmsAvgScore = zyzzmsAvgScore;
	}

	public Double getZybsAvgScore() {
		return zybsAvgScore;
	}

	public void setZybsAvgScore(Double zybsAvgScore) {
		this.zybsAvgScore = zybsAvgScore;
	}

	public Double getXgclAvgScore() {
		return xgclAvgScore;
	}

	public void setXgclAvgScore(Double xgclAvgScore) {
		this.xgclAvgScore = xgclAvgScore;
	}

	public Double getQtxgqksmAvgScore() {
		return qtxgqksmAvgScore;
	}

	public void setQtxgqksmAvgScore(Double qtxgqksmAvgScore) {
		this.qtxgqksmAvgScore = qtxgqksmAvgScore;
	}

	public Double getSmtzAvgScore() {
		return smtzAvgScore;
	}

	public void setSmtzAvgScore(Double smtzAvgScore) {
		this.smtzAvgScore = smtzAvgScore;
	}

	public Double getCfjbctAvgScore() {
		return cfjbctAvgScore;
	}

	public void setCfjbctAvgScore(Double cfjbctAvgScore) {
		this.cfjbctAvgScore = cfjbctAvgScore;
	}

	public Double getZyyxhyxtzAvgScore() {
		return zyyxhyxtzAvgScore;
	}

	public void setZyyxhyxtzAvgScore(Double zyyxhyxtzAvgScore) {
		this.zyyxhyxtzAvgScore = zyyxhyxtzAvgScore;
	}

	public Double getZyfzjcAvgScore() {
		return zyfzjcAvgScore;
	}

	public void setZyfzjcAvgScore(Double zyfzjcAvgScore) {
		this.zyfzjcAvgScore = zyfzjcAvgScore;
	}

	public Double getQtqksmAvgScore() {
		return qtqksmAvgScore;
	}

	public void setQtqksmAvgScore(Double qtqksmAvgScore) {
		this.qtqksmAvgScore = qtqksmAvgScore;
	}

	public Double getJdzjAvgScore() {
		return jdzjAvgScore;
	}

	public void setJdzjAvgScore(Double jdzjAvgScore) {
		this.jdzjAvgScore = jdzjAvgScore;
	}

	public Double getLjwtAvgScore() {
		return ljwtAvgScore;
	}

	public void setLjwtAvgScore(Double ljwtAvgScore) {
		this.ljwtAvgScore = ljwtAvgScore;
	}

	public Double getFxwtdyyyjAvgScore() {
		return fxwtdyyyjAvgScore;
	}

	public void setFxwtdyyyjAvgScore(Double fxwtdyyyjAvgScore) {
		this.fxwtdyyyjAvgScore = fxwtdyyyjAvgScore;
	}

	public Double getApfzjcAvgScore() {
		return apfzjcAvgScore;
	}

	public void setApfzjcAvgScore(Double apfzjcAvgScore) {
		this.apfzjcAvgScore = apfzjcAvgScore;
	}

	public Double getApzlhjkzdAvgScore() {
		return apzlhjkzdAvgScore;
	}

	public void setApzlhjkzdAvgScore(Double apzlhjkzdAvgScore) {
		this.apzlhjkzdAvgScore = apzlhjkzdAvgScore;
	}

	public Double getSfjsyxmAvgScore() {
		return sfjsyxmAvgScore;
	}

	public void setSfjsyxmAvgScore(Double sfjsyxmAvgScore) {
		this.sfjsyxmAvgScore = sfjsyxmAvgScore;
	}

	public Double getZlsjAvgScore() {
		return zlsjAvgScore;
	}

	public void setZlsjAvgScore(Double zlsjAvgScore) {
		this.zlsjAvgScore = zlsjAvgScore;
	}

	public Double getLjwtZtpgAvgScore() {
		return ljwtZtpgAvgScore;
	}

	public void setLjwtZtpgAvgScore(Double ljwtZtpgAvgScore) {
		this.ljwtZtpgAvgScore = ljwtZtpgAvgScore;
	}

	public Double getZljhAvgScore() {
		return zljhAvgScore;
	}

	public void setZljhAvgScore(Double zljhAvgScore) {
		this.zljhAvgScore = zljhAvgScore;
	}

	public Double getZzxnAvgScore() {
		return zzxnAvgScore;
	}

	public void setZzxnAvgScore(Double zzxnAvgScore) {
		this.zzxnAvgScore = zzxnAvgScore;
	}

	public Double getGtbdAvgScore() {
		return gtbdAvgScore;
	}

	public void setGtbdAvgScore(Double gtbdAvgScore) {
		this.gtbdAvgScore = gtbdAvgScore;
	}

	public Double getZysyAvgScore() {
		return zysyAvgScore;
	}

	public void setZysyAvgScore(Double zysyAvgScore) {
		this.zysyAvgScore = zysyAvgScore;
	}

	public Double getZtbxAvgScore() {
		return ztbxAvgScore;
	}

	public void setZtbxAvgScore(Double ztbxAvgScore) {
		this.ztbxAvgScore = ztbxAvgScore;
	}
	
}