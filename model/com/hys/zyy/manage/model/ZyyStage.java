package com.hys.zyy.manage.model;
/**
 * 
 * 标题：zyy
 * 
 * 作者：Tony Mar 19, 2012
 * 
 * 描述：阶段
 * 
 * 说明:
 */
public class ZyyStage extends ZyyBaseObject {

	/**
	 * 
	 */
	private static final long serialVersionUID = -2536068308077870073L;
	
	/**
	 * 阶段id
	 */
	private Long id;
	
	/**
	 * 阶段名称
	 */
	private String name;
	
	/**
	 * 阶段描述
	 */
	private String describe;
	
	/**
	 * 招录阶段
	 */
	private ZyyRecruitStage recruitStage;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getDescribe() {
		return describe;
	}

	public void setDescribe(String describe) {
		this.describe = describe;
	}

	public ZyyRecruitStage getRecruitStage() {
		return recruitStage;
	}

	public void setRecruitStage(ZyyRecruitStage recruitStage) {
		this.recruitStage = recruitStage;
	}

}
