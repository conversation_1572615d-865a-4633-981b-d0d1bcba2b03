package com.hys.zyy.manage.model;

import java.util.List;

/**
 * 
 * 标题：zyy
 * 
 * 作者：Tony Apr 17, 2012
 * 
 * 描述：
 * 
 * 说明:
 */
public class ZyyStatistics extends ZyyBaseObject {

	/**
	 * 
	 */
	private static final long serialVersionUID = 8493118355653732790L;
	
	/**
	 * 标准基地id
	 */
	private Long baseStdId;
	
	/**
	 * 标准基地名称
	 */
	private String baseStdName;
	
	/**
	 * 医院id
	 */
	private Long hospitalId;
	
	/**
	 * 医院名称
	 */
	private String hospitalName;
	
	/**
	 * 计划人数
	 */
	private Integer planNum;

	/**
	 * 计划人数
	 */
	private Integer planNum2;
	
	/**
	 * 实际人数
	 */
	private Integer factNum;
	
	/**
	 * 缺额
	 */
	private Integer deficiencyNum;
	
	/**
	 * 完成率
	 */
	private Double achieveNum;
	
	/**
	 *
	 * 社会人总数(计划招生)
	 * 
	 */
	private Double outsideCountPlan;
	
	/**
	 *
	 * 本科招生人数/其他(计划招生)
	 * 
	 */
	private Double undergraduateNum;
	/**
	 *
	 * 本科招生人数/专业学位(计划招生)
	 * 
	 */
	private Double undergraduateNumPro;
	/**
	 *
	 * 研究生招生人数(计划招生)
	 * 
	 */
	private Double masterNum;
	/**
	 *
	 * 单位人总数(计划招生)
	 * 
	 */
	private Double insideCountPlan;
	
	/**
	 *
	 * 社会人总数(实际人数)
	 * 
	 */
	private Double outsideCountReal;
	/**
	 *
	 * 专业学位(实际人数)
	 * 
	 */
	private Double outsideCountRealPro;
	/**
	 *
	 * 本科其他(实际人数)
	 * 
	 */
	private Double outsideCountRealOther;
	/**
	 *
	 * 研究生(实际人数)
	 * 
	 */
	private Double outsideCountRealMuster;
	/**
	 *
	 * 单位人总数(实际人数)
	 * 
	 */
	private Double insideCountReal;
	
	/**
	 *	子项目  
	 */
	private List<ZyyStatistics> list;
	/**
	 * 社会人总数缺额
	 */
	private Integer outsideDeficiencyNum;
	
	/**
	 * 单位人总数缺额
	 */
	private Integer insideDeficiencyNum;
	/**
	 * 社会人完成率
	 */
	private Double outsideAchieveNum;
	
	/**
	 * 单位人完成率
	 */
	private Double insideAchieveNum;
	/*
	 * 录取人ID
	 */
	private Long zyyUserId;
	/*
	 * 录取人类型
	 */
	private Long undergraduateType;
	/*
	 * 录取人最高学历
	 */
	private Long highestRecordSchool;
	
	public Double getOutsideCountRealPro() {
		return outsideCountRealPro;
	}

	public void setOutsideCountRealPro(Double outsideCountRealPro) {
		this.outsideCountRealPro = outsideCountRealPro;
	}

	public Double getOutsideCountRealOther() {
		return outsideCountRealOther;
	}

	public void setOutsideCountRealOther(Double outsideCountRealOther) {
		this.outsideCountRealOther = outsideCountRealOther;
	}

	public Double getOutsideCountRealMuster() {
		return outsideCountRealMuster;
	}

	public void setOutsideCountRealMuster(Double outsideCountRealMuster) {
		this.outsideCountRealMuster = outsideCountRealMuster;
	}

	public Long getHighestRecordSchool() {
		return highestRecordSchool;
	}

	public void setHighestRecordSchool(Long highestRecordSchool) {
		this.highestRecordSchool = highestRecordSchool;
	}

	public Long getZyyUserId() {
		return zyyUserId;
	}

	public void setZyyUserId(Long zyyUserId) {
		this.zyyUserId = zyyUserId;
	}

	public Long getUndergraduateType() {
		return undergraduateType;
	}

	public void setUndergraduateType(Long undergraduateType) {
		this.undergraduateType = undergraduateType;
	}

	public Double getOutsideAchieveNum() {
		return outsideAchieveNum;
	}

	public void setOutsideAchieveNum(Double outsideAchieveNum) {
		this.outsideAchieveNum = outsideAchieveNum;
	}

	public Double getInsideAchieveNum() {
		return insideAchieveNum;
	}

	public void setInsideAchieveNum(Double insideAchieveNum) {
		this.insideAchieveNum = insideAchieveNum;
	}

	public Integer getOutsideDeficiencyNum() {
		return outsideDeficiencyNum;
	}

	public void setOutsideDeficiencyNum(Integer outsideDeficiencyNum) {
		this.outsideDeficiencyNum = outsideDeficiencyNum;
	}

	public Integer getInsideDeficiencyNum() {
		return insideDeficiencyNum;
	}

	public void setInsideDeficiencyNum(Integer insideDeficiencyNum) {
		this.insideDeficiencyNum = insideDeficiencyNum;
	}

	public List<ZyyStatistics> getList() {
		return list;
	}

	public void setList(List<ZyyStatistics> list) {
		this.list = list;
	}

	public Double getOutsideCountPlan() {
		return outsideCountPlan;
	}

	public void setOutsideCountPlan(Double outsideCountPlan) {
		this.outsideCountPlan = outsideCountPlan;
	}

	public Double getInsideCountPlan() {
		return insideCountPlan;
	}

	public void setInsideCountPlan(Double insideCountPlan) {
		this.insideCountPlan = insideCountPlan;
	}

	public Double getOutsideCountReal() {
		return outsideCountReal;
	}

	public void setOutsideCountReal(Double outsideCountReal) {
		this.outsideCountReal = outsideCountReal;
	}

	public Double getInsideCountReal() {
		return insideCountReal;
	}

	public void setInsideCountReal(Double insideCountReal) {
		this.insideCountReal = insideCountReal;
	}

	public Integer getPlanNum2() {
		return planNum2;
	}

	public void setPlanNum2(Integer planNum2) {
		this.planNum2 = planNum2;
	}

	public Long getBaseStdId() {
		return baseStdId;
	}

	public void setBaseStdId(Long baseStdId) {
		this.baseStdId = baseStdId;
	}

	public String getBaseStdName() {
		return baseStdName;
	}

	public void setBaseStdName(String baseStdName) {
		this.baseStdName = baseStdName;
	}

	public Long getHospitalId() {
		return hospitalId;
	}

	public void setHospitalId(Long hospitalId) {
		this.hospitalId = hospitalId;
	}

	public String getHospitalName() {
		return hospitalName;
	}

	public void setHospitalName(String hospitalName) {
		this.hospitalName = hospitalName;
	}

	public Integer getPlanNum() {
		return planNum;
	}

	public void setPlanNum(Integer planNum) {
		this.planNum = planNum;
	}

	public Integer getFactNum() {
		return factNum;
	}

	public void setFactNum(Integer factNum) {
		this.factNum = factNum;
	}

	public Integer getDeficiencyNum() {
		return deficiencyNum;
	}

	public void setDeficiencyNum(Integer deficiencyNum) {
		this.deficiencyNum = deficiencyNum;
	}

	public Double getAchieveNum() {
		return achieveNum;
	}

	public void setAchieveNum(Double achieveNum) {
		this.achieveNum = achieveNum;
	}

	public Double getUndergraduateNum() {
		return undergraduateNum;
	}

	public void setUndergraduateNum(Double undergraduateNum) {
		this.undergraduateNum = undergraduateNum;
	}

	public Double getUndergraduateNumPro() {
		return undergraduateNumPro;
	}

	public void setUndergraduateNumPro(Double undergraduateNumPro) {
		this.undergraduateNumPro = undergraduateNumPro;
	}

	public Double getMasterNum() {
		return masterNum;
	}

	public void setMasterNum(Double masterNum) {
		this.masterNum = masterNum;
	}
	
}
