package com.hys.zyy.manage.model;

import java.io.Serializable;

public class ZyyStudentApplyCourse implements Serializable {
    /**
     * 学员考试报名表ID
     */
    private Long studentExamApplyId;

    /**
     * 科目ID  -- 住院医这边的
     */
    private Long courseId;
    //科目名称
    private String courseName;
    //考试科目id  -- 考试那边的
    private Long examCourseId;

    private static final long serialVersionUID = 1L;

    public Long getStudentExamApplyId() {
        return studentExamApplyId;
    }

    public void setStudentExamApplyId(Long studentExamApplyId) {
        this.studentExamApplyId = studentExamApplyId;
    }

    public Long getCourseId() {
        return courseId;
    }

    public void setCourseId(Long courseId) {
        this.courseId = courseId;
    }

	public String getCourseName() {
		return courseName;
	}

	public void setCourseName(String courseName) {
		this.courseName = courseName;
	}

	public Long getExamCourseId() {
		return examCourseId;
	}

	public void setExamCourseId(Long examCourseId) {
		this.examCourseId = examCourseId;
	}
}