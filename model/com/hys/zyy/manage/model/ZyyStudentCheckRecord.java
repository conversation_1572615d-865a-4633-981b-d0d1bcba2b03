package com.hys.zyy.manage.model;

import java.io.Serializable;
import java.util.Date;

public class ZyyStudentCheckRecord implements Serializable {
    /**
     * ID
     */
    private Long id;

    /**
     * 学员考试报名表ID
     */
    private Long studentExamApplyId;
    /**
     * 多个学员考试报名表ID
     */
    private String studentExamApplyIds;

    /**
     * 网上报名信息审核流程ID
     */
    private Long applyCheckFlowId;

    /**
     * 审核人
     */
    private Long checkUserId;

    /**
     * 审核时间
     */
    private Date checkTime;

    /**
     * 审核状态   1 通过 2 不通过 3 退回
     */
    private Integer checkStatus;

    /**
     * 失败原因
     */
    private String failReason;
    
    private static final long serialVersionUID = 1L;
    
    //不存库字段
    /**
     * 审核级别  1-7  一级审核  ~ 七级审核
     */
    private Integer checkLevel;
    //审核类型
    private Integer checkType;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getStudentExamApplyId() {
        return studentExamApplyId;
    }

    public void setStudentExamApplyId(Long studentExamApplyId) {
        this.studentExamApplyId = studentExamApplyId;
    }
    
    public String getStudentExamApplyIds() {
		return studentExamApplyIds;
	}

	public void setStudentExamApplyIds(String studentExamApplyIds) {
		this.studentExamApplyIds = studentExamApplyIds;
	}

	public Long getApplyCheckFlowId() {
        return applyCheckFlowId;
    }

    public void setApplyCheckFlowId(Long applyCheckFlowId) {
        this.applyCheckFlowId = applyCheckFlowId;
    }

    public Long getCheckUserId() {
        return checkUserId;
    }

    public void setCheckUserId(Long checkUserId) {
        this.checkUserId = checkUserId;
    }

    public Date getCheckTime() {
        return checkTime;
    }

    public void setCheckTime(Date checkTime) {
        this.checkTime = checkTime;
    }

    public Integer getCheckStatus() {
        return checkStatus;
    }

    public void setCheckStatus(Integer checkStatus) {
        this.checkStatus = checkStatus;
    }

    public String getFailReason() {
        return failReason;
    }

    public void setFailReason(String failReason) {
        this.failReason = failReason == null ? null : failReason.trim();
    }

	public Integer getCheckLevel() {
		return checkLevel;
	}

	public void setCheckLevel(Integer checkLevel) {
		this.checkLevel = checkLevel;
	}

	public Integer getCheckType() {
		return checkType;
	}

	public void setCheckType(Integer checkType) {
		this.checkType = checkType;
	}
}