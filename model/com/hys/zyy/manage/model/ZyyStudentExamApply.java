package com.hys.zyy.manage.model;

import java.io.Serializable;
import java.util.Date;

public class ZyyStudentExamApply implements Serializable {
    /**
     * ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;
    
//    考试ID	
    private Long zyyExamId;
    
//    人员类型		1 本单位住院医师 2 外单位委托培养住院医师 3 面向社会招收住院医师 4全日制硕士专业研究生
    private Integer doctorUserType;

    /**
     * 是否为军队人员  1 现役军人  2 否  3 军队文职人员
     */
    private Integer armyType;

    /**
     * 本次考试是否为初次报考 0 否  1 是
     */
    private Integer firstApplyExam;

    /**
     * 毕业专业备注
     */
    private String graduateProfRemark;

    /**
     * 是否为西部支援住院医师  0 否  1 是
     */
    private Integer westSupportDoctor;

    /**
     * 工作年限
     */
    private String workYear;

    /**
     * 进入培训基地时间
     */
    private Date workStartDate;

    /**
     * 培训专业备注
     */
    private String baseRemark;

    /**
     * 是否在协同单位培训 0 否  1 是
     */
    private Integer cooperatUnit;

    /**
     * 所在协同单位
     */
    private String cooperatUnitName;

    /**
     * 执业地点
     */
    private String practicePlace;

    /**
     * 是否为补考学员 0 否  1 是
     */
    private Integer makeupExam;

    /**
     * 报名表状态 0 未报名 1 已报名
     */
    private Integer applyStatus;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;
    
    private static final long serialVersionUID = 1L;
    
    // 不存库字段
    //科目id
    private String courseIds;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getZyyExamId() {
		return zyyExamId;
	}

	public void setZyyExamId(Long zyyExamId) {
		this.zyyExamId = zyyExamId;
	}
	
	public Integer getDoctorUserType() {
		return doctorUserType;
	}

	public void setDoctorUserType(Integer doctorUserType) {
		this.doctorUserType = doctorUserType;
	}

	public Integer getArmyType() {
        return armyType;
    }

    public void setArmyType(Integer armyType) {
        this.armyType = armyType;
    }

    public Integer getFirstApplyExam() {
        return firstApplyExam;
    }

    public void setFirstApplyExam(Integer firstApplyExam) {
        this.firstApplyExam = firstApplyExam;
    }

    public String getGraduateProfRemark() {
        return graduateProfRemark;
    }

    public void setGraduateProfRemark(String graduateProfRemark) {
        this.graduateProfRemark = graduateProfRemark == null ? null : graduateProfRemark.trim();
    }

    public Integer getWestSupportDoctor() {
        return westSupportDoctor;
    }

    public void setWestSupportDoctor(Integer westSupportDoctor) {
        this.westSupportDoctor = westSupportDoctor;
    }

    public String getWorkYear() {
        return workYear;
    }

    public void setWorkYear(String workYear) {
        this.workYear = workYear == null ? null : workYear.trim();
    }

    public Date getWorkStartDate() {
        return workStartDate;
    }

    public void setWorkStartDate(Date workStartDate) {
        this.workStartDate = workStartDate;
    }

    public String getBaseRemark() {
        return baseRemark;
    }

    public void setBaseRemark(String baseRemark) {
        this.baseRemark = baseRemark == null ? null : baseRemark.trim();
    }

    public Integer getCooperatUnit() {
        return cooperatUnit;
    }

    public void setCooperatUnit(Integer cooperatUnit) {
        this.cooperatUnit = cooperatUnit;
    }

    public String getCooperatUnitName() {
        return cooperatUnitName;
    }

    public void setCooperatUnitName(String cooperatUnitName) {
        this.cooperatUnitName = cooperatUnitName == null ? null : cooperatUnitName.trim();
    }

    public String getPracticePlace() {
        return practicePlace;
    }

    public void setPracticePlace(String practicePlace) {
        this.practicePlace = practicePlace == null ? null : practicePlace.trim();
    }

    public Integer getMakeupExam() {
        return makeupExam;
    }

    public void setMakeupExam(Integer makeupExam) {
        this.makeupExam = makeupExam;
    }

    public Integer getApplyStatus() {
        return applyStatus;
    }

    public void setApplyStatus(Integer applyStatus) {
        this.applyStatus = applyStatus;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

	public String getCourseIds() {
		return courseIds;
	}

	public void setCourseIds(String courseIds) {
		this.courseIds = courseIds;
	}
    
}