package com.hys.zyy.manage.model;

import java.util.Date;

public class ZyyStudentExaminee extends ZyyBaseObject{
//	ID	
	private Long id;	
//	考试科目ID	 考试那边的考试科目的id
	private Long examCourseId;	
//	身份证号	
	private String certificateNo;	
//	考试状态		1 正在考试 2 考试结束
	private Integer status;
//	创建时间	
	private Date createDate;	
//	更新时间	
	private Date updateDate;
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getExamCourseId() {
		return examCourseId;
	}
	public void setExamCourseId(Long examCourseId) {
		this.examCourseId = examCourseId;
	}
	public String getCertificateNo() {
		return certificateNo;
	}
	public void setCertificateNo(String certificateNo) {
		this.certificateNo = certificateNo;
	}
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	public Date getCreateDate() {
		return createDate;
	}
	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}
	public Date getUpdateDate() {
		return updateDate;
	}
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}	
}
