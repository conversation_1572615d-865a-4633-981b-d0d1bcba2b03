package com.hys.zyy.manage.model;

public class ZyyStudentStatistics extends ZyyBaseObject {
	/**
	 * 
	 */
	private static final long serialVersionUID = -7747008753260688542L;

	private Long orgId;// 培训基地ID
	private Integer hospType;//中医西医
	private Long baseId;// 专业基地ID
	private Long baseStdId;// 专业ID
	private Long yearId;// 学年ID
	private Long zyyUserProvinceId;//身份ID
	private Integer entrustCount = 0;// 单位人人数
	private Integer socialCount = 0;// 社会人人数
	private Integer degreeCount = 0;// 学位衔接人数
	private Integer totalCount = 0;// 合计人数
	private Integer hasCertCount = 0;// 有医师资格证人数
	
	public Long getOrgId() {
		return orgId;
	}
	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}
	public Long getBaseId() {
		return baseId;
	}
	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}
	public Long getBaseStdId() {
		return baseStdId;
	}
	public void setBaseStdId(Long baseStdId) {
		this.baseStdId = baseStdId;
	}
	public Long getYearId() {
		return yearId;
	}
	public void setYearId(Long yearId) {
		this.yearId = yearId;
	}
	public Integer getEntrustCount() {
		return entrustCount;
	}
	public void setEntrustCount(Integer entrustCount) {
		this.entrustCount = entrustCount;
	}
	public Integer getSocialCount() {
		return socialCount;
	}
	public void setSocialCount(Integer socialCount) {
		this.socialCount = socialCount;
	}
	public Integer getDegreeCount() {
		return degreeCount;
	}
	public void setDegreeCount(Integer degreeCount) {
		this.degreeCount = degreeCount;
	}
	public Integer getTotalCount() {
		return totalCount;
	}
	public void setTotalCount(Integer totalCount) {
		this.totalCount = totalCount;
	}
	public Integer getHasCertCount() {
		return hasCertCount;
	}
	public void setHasCertCount(Integer hasCertCount) {
		this.hasCertCount = hasCertCount;
	}
	public Long getZyyUserProvinceId() {
		return zyyUserProvinceId;
	}
	public void setZyyUserProvinceId(Long zyyUserProvinceId) {
		this.zyyUserProvinceId = zyyUserProvinceId;
	}
	public Integer getHospType() {
		return hospType;
	}
	public void setHospType(Integer hospType) {
		this.hospType = hospType;
	}
}
