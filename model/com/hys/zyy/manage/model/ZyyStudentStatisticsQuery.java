package com.hys.zyy.manage.model;

import java.util.ArrayList;
import java.util.List;

/**
 * @desc 
 * <AUTHOR>
 *
 */
public class ZyyStudentStatisticsQuery extends ZyyStudentStatistics {

	/**
	 * 
	 */
	private static final long serialVersionUID = 5986723604339476653L;
	
	private String orgName;//培训基地名称
	private String baseName;//专业基地名称
	private String yearName;//年份ID
	
	private Long hospitalId;//医院ID
	private Integer residencySource;//身份类型
	
	private Integer stuCount;//人员数量
	private Integer certCount; //持有证书人员数量
	
	private Integer viewBy;//查看方式 1培训基地方式 2专业基地方式
	
	private Integer rowSpan=0;
	
	private List<ZyyStudentStatisticsQuery> subList = new ArrayList<ZyyStudentStatisticsQuery>();//子集，年份排序
	
	public String getOrgName() {
		return orgName;
	}
	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}
	public String getBaseName() {
		return baseName;
	}
	public void setBaseName(String baseName) {
		this.baseName = baseName;
	}
	public Long getHospitalId() {
		return hospitalId;
	}
	public void setHospitalId(Long hospitalId) {
		this.hospitalId = hospitalId;
	}
	public Integer getResidencySource() {
		return residencySource;
	}
	public void setResidencySource(Integer residencySource) {
		this.residencySource = residencySource;
	}
	public Integer getStuCount() {
		return stuCount;
	}
	public void setStuCount(Integer stuCount) {
		this.stuCount = stuCount;
	}
	public Integer getCertCount() {
		return certCount;
	}
	public void setCertCount(Integer certCount) {
		this.certCount = certCount;
	}
	public String getYearName() {
		return yearName;
	}
	public void setYearName(String yearName) {
		this.yearName = yearName;
	}
	public Integer getViewBy() {
		return viewBy;
	}
	public void setViewBy(Integer viewBy) {
		this.viewBy = viewBy;
	}
	public Integer getRowSpan() {
		return rowSpan;
	}
	public void setRowSpan(Integer rowSpan) {
		this.rowSpan = rowSpan;
	}
	public List<ZyyStudentStatisticsQuery> getSubList() {
		return subList;
	}
	public void setSubList(List<ZyyStudentStatisticsQuery> subList) {
		this.subList = subList;
	}
	
}
