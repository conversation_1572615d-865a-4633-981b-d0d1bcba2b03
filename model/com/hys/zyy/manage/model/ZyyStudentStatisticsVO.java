package com.hys.zyy.manage.model;

import java.util.List;



public class ZyyStudentStatisticsVO extends ZyyUserExtend{
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	//培训年级
	private String year;
	//学科
	private Long baseId;
	//科室
	private Long deptId;
	//科室名字
	private String deptName;
	//培训专科
	private Long baseStdId;
	//培训专科
	private String baseStdName;
    //ID编号
	private String residencyId;
    //姓名
	private String userName;
    //轮转数量
	private Integer cycleNum;
    //手册填写数量
	private Integer editmanualNum;
    //出科数量
	private Integer finishNum;
	//登录次数
	private Integer loginNum;
	//评价数量
	private Integer efNum;
	//评价数量
	private List<Integer> yearNum;
	
	public Integer getEfNum() {
		return efNum;
	}
	public void setEfNum(Integer efNum) {
		this.efNum = efNum;
	}
	public String getYear() {
		return year;
	}
	public void setYear(String year) {
		this.year = year;
	}
	public Long getBaseId() {
		return baseId;
	}
	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}
	public Long getBaseStdId() {
		return baseStdId;
	}
	public void setBaseStdId(Long baseStdId) {
		this.baseStdId = baseStdId;
	}
	public String getBaseStdName() {
		return baseStdName;
	}
	public void setBaseStdName(String baseStdName) {
		this.baseStdName = baseStdName;
	}
	public String getResidencyId() {
		return residencyId;
	}
	public void setResidencyId(String residencyId) {
		this.residencyId = residencyId;
	}
	public String getUserName() {
		return userName;
	}
	public void setUserName(String userName) {
		this.userName = userName;
	}
	public Integer getCycleNum() {
		return cycleNum;
	}
	public void setCycleNum(Integer cycleNum) {
		this.cycleNum = cycleNum;
	}
	public Integer getEditmanualNum() {
		return editmanualNum;
	}
	public void setEditmanualNum(Integer editmanualNum) {
		this.editmanualNum = editmanualNum;
	}
	public Integer getFinishNum() {
		return finishNum;
	}
	public void setFinishNum(Integer finishNum) {
		this.finishNum = finishNum;
	}
	public Integer getLoginNum() {
		return loginNum;
	}
	public void setLoginNum(Integer loginNum) {
		this.loginNum = loginNum;
	}
	public Long getDeptId() {
		return deptId;
	}
	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}
	public String getDeptName() {
		return deptName;
	}
	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}
	public List<Integer> getYearNum() {
		return yearNum;
	}
	public void setYearNum(List<Integer> yearNum) {
		this.yearNum = yearNum;
	}
	
	
	
	
	
	
}