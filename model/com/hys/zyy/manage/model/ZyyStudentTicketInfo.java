package com.hys.zyy.manage.model;

import java.util.Date;

/**
 * 学生准考证信息 
 * <AUTHOR>
 * @date 2020-4-8上午9:53:47
 */
public class ZyyStudentTicketInfo extends ZyyBaseObject {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 7798357257436666412L;
	
	//	ID	
	private Long id;
//	考试id	
	private Long zyyExamId;
//	分组id	
	private Long groupId;
//	分组名称	
	private String groupName;
//	用户id	
	private Long userId;
//	开始日期	
	private String startDate;
//	结束日期	
	private String endDate;
//	开始时间	
	private String startTime;
//	结束时间	
	private String endTime;
//	考点	
	private String examPlace;
//	考场	
	private String examRoom;
//	座位号	
	private String examSeat;
//	创建时间	
	private Date createDate;
//	更新时间	
	private Date updateDate;
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getZyyExamId() {
		return zyyExamId;
	}
	public void setZyyExamId(Long zyyExamId) {
		this.zyyExamId = zyyExamId;
	}
	public Long getGroupId() {
		return groupId;
	}
	public void setGroupId(Long groupId) {
		this.groupId = groupId;
	}
	public String getGroupName() {
		return groupName;
	}
	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	public String getStartDate() {
		return startDate;
	}
	public void setStartDate(String startDate) {
		this.startDate = startDate;
	}
	public String getEndDate() {
		return endDate;
	}
	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}
	public String getStartTime() {
		return startTime;
	}
	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}
	public String getEndTime() {
		return endTime;
	}
	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}
	public String getExamPlace() {
		return examPlace;
	}
	public void setExamPlace(String examPlace) {
		this.examPlace = examPlace;
	}
	public String getExamRoom() {
		return examRoom;
	}
	public void setExamRoom(String examRoom) {
		this.examRoom = examRoom;
	}
	public String getExamSeat() {
		return examSeat;
	}
	public void setExamSeat(String examSeat) {
		this.examSeat = examSeat;
	}
	public Date getCreateDate() {
		return createDate;
	}
	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}
	public Date getUpdateDate() {
		return updateDate;
	}
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}
}
