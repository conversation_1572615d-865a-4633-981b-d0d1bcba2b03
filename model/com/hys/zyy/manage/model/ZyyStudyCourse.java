package com.hys.zyy.manage.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import oracle.sql.CLOB;

/**
 * 
 * 标题：住院医
 * 
 * 作者：陈来宾 2013-1-15
 * 
 * 描述：课程表主表
 * 
 * 说明:
 */
public class ZyyStudyCourse extends ZyyBaseObject{

	
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	/**
	 * 主键ID
	 */
	private Long id ;
	
	/**
	 * 课程名
	 */
	private String courseName;
	
	/**
	 * 课程别名
	 */
	private String courseAnotherName;

	private Integer courseType;
	
	/**
	 * -1：禁用   1 ：正常
	 */
	private Long courseStatus;
	
	/**
	 * 秒
	 */
	private Long courseTimes;
	
	/**
	 * 小时
	 */
	private Long courseHours;
	
	/**
	 * 课程章节
	 */
	private String courseSummary;
	
	/**
	 * 音频地址
	 */
	private String coursePath;
	/**
	 * 文字内容
	 */
	private String courseClob;
	
	private Long normId;
	
	private String iptv;
	
	private Long type;

	private Long status;
	


	public String getIptv() {
		return iptv;
	}

	public void setIptv(String iptv) {
		this.iptv = iptv;
	}

	public Long getType() {
		return type;
	}

	public void setType(Long type) {
		this.type = type;
	}

	public Long getStatus() {
		return status;
	}

	public void setStatus(Long status) {
		this.status = status;
	}

	public String getCourseClob() {
		return courseClob;
	}

	public void setCourseClob(String courseClob) {
		this.courseClob = courseClob;
	}

	public String getCoursePathPpt() {
		return coursePathPpt;
	}

	public void setCoursePathPpt(String coursePathPpt) {
		this.coursePathPpt = coursePathPpt;
	}

	/**
	 * 视频地址
	 */
	private String coursePathPpt;
	
	/**
	 * 讲课老师
	 */
	private String teacherName;
	
	/**
	 * 老师单位
	 */
	private String teacherUint;
	
	/**
	 * 创建时间
	 */
	private Date createDate;
	
	/**
	 * 排序
	 */
	private Long seq;
	
	/**
	 * 属性名称
	 */
	private String normName;
	
	private Float period;
	
	private Float credit;
	
	//已选择列表
	
	public List<ZyyBase> baseList = new ArrayList<ZyyBase>(); 	//基地列表
	
	public List<ZyyDeptStd> deptList = new ArrayList<ZyyDeptStd>();	//科室列表
	
	public List<ZyyTrainDisease> sickList = new ArrayList<ZyyTrainDisease>();	//疾病列表
	
	public List<ZyyTrainDisease> skillList = new ArrayList<ZyyTrainDisease>();	//技能列表
	
	public List<ZyyDictArea> propertyList = new ArrayList<ZyyDictArea>();	//属性列表
	
	
	public String getCourseSummary() {
		return courseSummary;
	}

	public void setCourseSummary(String courseSummary) {
		this.courseSummary = courseSummary;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getCourseName() {
		return courseName;
	}

	public void setCourseName(String courseName) {
		this.courseName = courseName;
	}

	public String getCourseAnotherName() {
		return courseAnotherName;
	}

	public void setCourseAnotherName(String courseAnotherName) {
		this.courseAnotherName = courseAnotherName;
	}

	public Long getCourseStatus() {
		return courseStatus;
	}

	public void setCourseStatus(Long courseStatus) {
		this.courseStatus = courseStatus;
	}

	public Long getCourseTimes() {
		return courseTimes;
	}

	public void setCourseTimes(Long courseTimes) {
		this.courseTimes = courseTimes;
	}

	public Long getCourseHours() {
		return courseHours;
	}

	public void setCourseHours(Long courseHours) {
		this.courseHours = courseHours;
	}

	public String getCoursePath() {
		return coursePath;
	}

	public void setCoursePath(String coursePath) {
		this.coursePath = coursePath;
	}


	public String getTeacherName() {
		return teacherName;
	}

	public void setTeacherName(String teacherName) {
		this.teacherName = teacherName;
	}

	public String getTeacherUint() {
		return teacherUint;
	}

	public void setTeacherUint(String teacherUint) {
		this.teacherUint = teacherUint;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public List<ZyyBase> getBaseList() {
		return baseList;
	}

	public void setBaseList(List<ZyyBase> baseList) {
		this.baseList = baseList;
	}

	public List<ZyyDeptStd> getDeptList() {
		return deptList;
	}

	public void setDeptList(List<ZyyDeptStd> deptList) {
		this.deptList = deptList;
	}

	public List<ZyyTrainDisease> getSickList() {
		return sickList;
	}

	public void setSickList(List<ZyyTrainDisease> sickList) {
		this.sickList = sickList;
	}

	public List<ZyyTrainDisease> getSkillList() {
		return skillList;
	}

	public void setSkillList(List<ZyyTrainDisease> skillList) {
		this.skillList = skillList;
	}

	public List<ZyyDictArea> getPropertyList() {
		return propertyList;
	}

	public void setPropertyList(List<ZyyDictArea> propertyList) {
		this.propertyList = propertyList;
	}

	public Long getSeq() {
		return seq;
	}

	public void setSeq(Long seq) {
		this.seq = seq;
	}

	public String getNormName() {
		return normName;
	}

	public void setNormName(String normName) {
		this.normName = normName;
	}
	
	public Integer getCourseType() {
		return courseType;
	}

	public void setCourseType(Integer courseType) {
		this.courseType = courseType;
	}

	public Float getPeriod() {
		return period;
	}

	public void setPeriod(Float period) {
		this.period = period;
	}

	public Float getCredit() {
		return credit;
	}

	public void setCredit(Float credit) {
		this.credit = credit;
	}

	public Long getNormId() {
		return normId;
	}

	public void setNormId(Long normId) {
		this.normId = normId;
	}

}


