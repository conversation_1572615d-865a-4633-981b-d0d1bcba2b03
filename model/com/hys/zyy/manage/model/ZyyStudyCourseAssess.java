package com.hys.zyy.manage.model;

import java.util.Date;

/**
 * 
 * 标题：住院医
 * 
 * 作者：
 * 
 * 描述：课程评价
 * 
 * 说明:
 */
public class ZyyStudyCourseAssess extends ZyyBaseObject{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	/**
	 * 主键ID
	 */
	private Long id ;
	
	/**
	 * 课程ID
	 */
	private Long courseId ;
	/**
	 * 课程名称
	 */
	private String courseName ;
	/**
	 * 评价用户ID
	 */
	private Long zyyUserId ;
	/**
	 * 评价用户姓名
	 */
	private String zyyUserName ;
	/**
	 * 评价时间
	 */
	private Date assessDate ;
	/**
	 * 评价分数
	 */
	private Long praiseIndex ;
	/**
	 * 评价
	 */
	private String remark ;
	/**
	 * 评价一星
	 */
	private Long assessNum2 ;
	/**
	 * 评价二星
	 */
	private Long assessNum4 ;
	/**
	 * 评价三星
	 */
	private Long assessNum6 ;
	/**
	 * 评价四星
	 */
	private Long assessNum8 ;
	/**
	 * 评价五星
	 */
	private Long assessNum10 ;
	/**
	 * 累计学习
	 */
	private Long logsNum ;
	/**
	 * 评价指数
	 */
	private Long coursePro ;
	/**
	 * 所属组别
	 */
	private Long groupId ;
	/**
	 * 课程所属基地
	 */
	private Long baseId ;
	/**
	 * 医院学员
	 */
	private Long isNotUser ;
	/**
	 * 其他人员
	 */
	private Long isNotOtherUser ;
	/**
	 * 开放形式
	 */
	private Long openForm ;
	
	public Long getIsNotUser() {
		return isNotUser;
	}
	public void setIsNotUser(Long isNotUser) {
		this.isNotUser = isNotUser;
	}
	public Long getIsNotOtherUser() {
		return isNotOtherUser;
	}
	public void setIsNotOtherUser(Long isNotOtherUser) {
		this.isNotOtherUser = isNotOtherUser;
	}
	public Long getOpenForm() {
		return openForm;
	}
	public void setOpenForm(Long openForm) {
		this.openForm = openForm;
	}
	public Long getBaseId() {
		return baseId;
	}
	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}
	public Long getGroupId() {
		return groupId;
	}
	public void setGroupId(Long groupId) {
		this.groupId = groupId;
	}
	public Long getAssessNum2() {
		return assessNum2;
	}
	public void setAssessNum2(Long assessNum2) {
		this.assessNum2 = assessNum2;
	}
	public Long getAssessNum4() {
		return assessNum4;
	}
	public void setAssessNum4(Long assessNum4) {
		this.assessNum4 = assessNum4;
	}
	public Long getAssessNum6() {
		return assessNum6;
	}
	public void setAssessNum6(Long assessNum6) {
		this.assessNum6 = assessNum6;
	}
	public Long getAssessNum8() {
		return assessNum8;
	}
	public void setAssessNum8(Long assessNum8) {
		this.assessNum8 = assessNum8;
	}
	public Long getAssessNum10() {
		return assessNum10;
	}
	public void setAssessNum10(Long assessNum10) {
		this.assessNum10 = assessNum10;
	}
	public Long getLogsNum() {
		return logsNum;
	}
	public void setLogsNum(Long logsNum) {
		this.logsNum = logsNum;
	}
	public Long getCoursePro() {
		return coursePro;
	}
	public void setCoursePro(Long coursePro) {
		this.coursePro = coursePro;
	}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getCourseId() {
		return courseId;
	}
	public void setCourseId(Long courseId) {
		this.courseId = courseId;
	}
	public String getCourseName() {
		return courseName;
	}
	public void setCourseName(String courseName) {
		this.courseName = courseName;
	}
	public Long getZyyUserId() {
		return zyyUserId;
	}
	public void setZyyUserId(Long zyyUserId) {
		this.zyyUserId = zyyUserId;
	}
	public String getZyyUserName() {
		return zyyUserName;
	}
	public void setZyyUserName(String zyyUserName) {
		this.zyyUserName = zyyUserName;
	}
	public Date getAssessDate() {
		return assessDate;
	}
	public void setAssessDate(Date assessDate) {
		this.assessDate = assessDate;
	}
	public Long getPraiseIndex() {
		return praiseIndex;
	}
	public void setPraiseIndex(Long praiseIndex) {
		this.praiseIndex = praiseIndex;
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	
	
}


