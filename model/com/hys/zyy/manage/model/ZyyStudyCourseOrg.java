/**
 * 
 * 标题：住院医
 * 
 * 作者：陈来宾 2012-12-03
 * 
 * 描述：学习机构课件中间表
 * 
 * 说明:
 */

package com.hys.zyy.manage.model;

public class ZyyStudyCourseOrg extends ZyyBaseObject{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	private Long id;
	
	private Long orgTypeId;			//机构类别	
	
	private Long orgId;				//机构ID
	
	private Long isNotUser;			//住院医师选择  1：选择 0：不选
	
	private Long isNotOrtherUser;	//其他人员选择   1：选择 0：不选
	
	private Long courseId;			//课件ID
	
	private Long groupId;			//所属组别
	
	private Long groupCourseSeq;	//所属组中课件排序
	
	private String orgName;			//机构名称
	
	private String courseName;		//课程名称
	
	private String courseSummary;
	

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getOrgTypeId() {
		return orgTypeId;
	}

	public void setOrgTypeId(Long orgTypeId) {
		this.orgTypeId = orgTypeId;
	}

	public Long getOrgId() {
		return orgId;
	}

	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}

	public Long getIsNotUser() {
		return isNotUser;
	}

	public void setIsNotUser(Long isNotUser) {
		this.isNotUser = isNotUser;
	}

	public Long getIsNotOrtherUser() {
		return isNotOrtherUser;
	}

	public void setIsNotOrtherUser(Long isNotOrtherUser) {
		this.isNotOrtherUser = isNotOrtherUser;
	}

	public Long getCourseId() {
		return courseId;
	}

	public void setCourseId(Long courseId) {
		this.courseId = courseId;
	}

	public Long getGroupId() {
		return groupId;
	}

	public void setGroupId(Long groupId) {
		this.groupId = groupId;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public String getCourseName() {
		return courseName;
	}

	public void setCourseName(String courseName) {
		this.courseName = courseName;
	}

	public String getCourseSummary() {
		return courseSummary;
	}

	public void setCourseSummary(String courseSummary) {
		this.courseSummary = courseSummary;
	}

	public Long getGroupCourseSeq() {
		return groupCourseSeq;
	}

	public void setGroupCourseSeq(Long groupCourseSeq) {
		this.groupCourseSeq = groupCourseSeq;
	}

}


