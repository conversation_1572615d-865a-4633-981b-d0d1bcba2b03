/**
 * 
 * 标题：住院医
 * 
 * 作者：陈来宾 2012-12-03
 * 
 * 描述：学习机构课件组
 * 
 * 说明:
 */

package com.hys.zyy.manage.model;

import java.util.List;

public class ZyyStudyCourseOrgGroup extends ZyyBaseObject{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	private Long id;
	
	private String groupName;	//组名
	
	private Long openForm;	//开放形式 1: 无限制开放 2: 按照住院医师培训专科对应开放
	
	private Long courseType; //1：好医生课程  2：公共课程
	
	private List<ZyyStudyCourseOrg> courseOrgList;	//组织列表
	
	private List<ZyyStudyCourseOrg> courseList;	//课程列表
	
	private List<ZyyStudyCourseProperty> coursePropertyList;	//课程详细
	
	private List<ZyyStudyCourse> hysCourseList;	//好医生列表,为了读取其下的基地专科列表
	

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getGroupName() {
		return groupName;
	}

	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}

	public Long getOpenForm() {
		return openForm;
	}

	public void setOpenForm(Long openForm) {
		this.openForm = openForm;
	}

	public List<ZyyStudyCourseOrg> getCourseOrgList() {
		return courseOrgList;
	}

	public void setCourseOrgList(List<ZyyStudyCourseOrg> courseOrgList) {
		this.courseOrgList = courseOrgList;
	}

	public List<ZyyStudyCourseOrg> getCourseList() {
		return courseList;
	}

	public void setCourseList(List<ZyyStudyCourseOrg> courseList) {
		this.courseList = courseList;
	}

	public List<ZyyStudyCourseProperty> getCoursePropertyList() {
		return coursePropertyList;
	}

	public void setCoursePropertyList(
			List<ZyyStudyCourseProperty> coursePropertyList) {
		this.coursePropertyList = coursePropertyList;
	}

	public Long getCourseType() {
		return courseType;
	}

	public void setCourseType(Long courseType) {
		this.courseType = courseType;
	}

	public List<ZyyStudyCourse> getHysCourseList() {
		return hysCourseList;
	}

	public void setHysCourseList(List<ZyyStudyCourse> hysCourseList) {
		this.hysCourseList = hysCourseList;
	}

	
}


