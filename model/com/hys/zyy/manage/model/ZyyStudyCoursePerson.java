package com.hys.zyy.manage.model;

import java.util.Date;

/**
 * 
 * 标题：住院医
 * 
 * 作者：
 * 
 * 描述：课程状态
 * 
 * 说明:
 */
public class ZyyStudyCoursePerson extends ZyyBaseObject{
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	/*
	 * 人员ID
	 */
	private Long id;
	/*
	 * 姓名
	 */
	private String realName;
	/*
	 * 所属基地
	 */
	private Long baseId;
	/*
	 * 所属年份
	 */
	private Long year;
	/*
	 * 所属基地
	 */
	private String aliasName;
	
	/*
	 * 是否学习
	 */
	private int isStudy;
	
	public String getAliasName() {
		return aliasName;
	}
	public void setAliasName(String aliasName) {
		this.aliasName = aliasName;
	}
	public int getIsStudy() {
		return isStudy;
	}
	public void setIsStudy(int isStudy) {
		this.isStudy = isStudy;
	}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getRealName() {
		return realName;
	}
	public void setRealName(String realName) {
		this.realName = realName;
	}
	public Long getBaseId() {
		return baseId;
	}
	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}
	public Long getYear() {
		return year;
	}
	public void setYear(Long year) {
		this.year = year;
	}
	
	
}


