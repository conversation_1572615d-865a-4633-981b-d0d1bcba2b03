package com.hys.zyy.manage.model;
/**
 * 
 * 标题：住院医
 * 
 * 作者：陈来宾 2012-11-21
 * 
 * 描述：课程子表属性
 * 
 * 说明:
 */
public class ZyyStudyCourseProperty extends ZyyBaseObject{

	
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	/**
	 * 主键ID
	 */
	private Long id ;
	
	/**
	 * 主表ID
	 */
	private Long courseId;
	
	private String courseName;
	
	/**
	 * 课程别名
	 */
	private String anotherName;
	
	/**
	 * 课程章节
	 */
	private String courseSummary;
	
	/**
	 * 年
	 */
	private String year;
	
	/**
	 * 课程一级分类ID
	 */
	private Long courseTypeId1;
	
	private String courseTypeName1;

	/**
	 * 课程二级分类ID
	 */
	private Long courseTypeId2;
	
	private String courseTypeName2;

	/**
	 * 课程三级分类ID
	 */
	private Long courseTypeId3;
	
	private String courseTypeName3;

	/**
	 * 课程四级分类ID
	 */
	private Long courseTypeId4;

	/**
	 * 序列
	 */
	private Long courseSeq;

	/**
	 * 拍摄地区ID
	 */
	private Long areaId;
	
	private String areaName;

	/**
	 * 拍摄年份
	 */
	private String createYear;

	/**
	 * 评价指数
	 */
	private Long assess;
	
	/**
	 * 属性
	 */
	private Long normId;
	
	/**
	 * 属性名称
	 */
	private String normName;
	
	private Float period;
	
	private Float credit;
	
	private Long zyyOrgId;

	/**
	 * 备注
	 */
	private String remark;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getCourseId() {
		return courseId;
	}

	public void setCourseId(Long courseId) {
		this.courseId = courseId;
	}

	public String getYear() {
		return year;
	}

	public void setYear(String year) {
		this.year = year;
	}

	public Long getCourseTypeId1() {
		return courseTypeId1;
	}

	public void setCourseTypeId1(Long courseTypeId1) {
		this.courseTypeId1 = courseTypeId1;
	}

	public String getCourseTypeName1() {
		return courseTypeName1;
	}

	public void setCourseTypeName1(String courseTypeName1) {
		this.courseTypeName1 = courseTypeName1;
	}

	public Long getCourseTypeId2() {
		return courseTypeId2;
	}

	public void setCourseTypeId2(Long courseTypeId2) {
		this.courseTypeId2 = courseTypeId2;
	}

	public String getCourseTypeName2() {
		return courseTypeName2;
	}

	public void setCourseTypeName2(String courseTypeName2) {
		this.courseTypeName2 = courseTypeName2;
	}

	public Long getCourseTypeId3() {
		return courseTypeId3;
	}

	public void setCourseTypeId3(Long courseTypeId3) {
		this.courseTypeId3 = courseTypeId3;
	}

	public String getCourseTypeName3() {
		return courseTypeName3;
	}

	public void setCourseTypeName3(String courseTypeName3) {
		this.courseTypeName3 = courseTypeName3;
	}

	public Long getCourseTypeId4() {
		return courseTypeId4;
	}

	public void setCourseTypeId4(Long courseTypeId4) {
		this.courseTypeId4 = courseTypeId4;
	}

	public Long getCourseSeq() {
		return courseSeq;
	}

	public void setCourseSeq(Long courseSeq) {
		this.courseSeq = courseSeq;
	}

	public Long getAreaId() {
		return areaId;
	}

	public void setAreaId(Long areaId) {
		this.areaId = areaId;
	}

	public String getAreaName() {
		return areaName;
	}

	public void setAreaName(String areaName) {
		this.areaName = areaName;
	}

	public String getCreateYear() {
		return createYear;
	}

	public void setCreateYear(String createYear) {
		this.createYear = createYear;
	}

	public Long getAssess() {
		return assess;
	}

	public void setAssess(Long assess) {
		this.assess = assess;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getCourseName() {
		return courseName;
	}

	public void setCourseName(String courseName) {
		this.courseName = courseName;
	}

	public String getCourseSummary() {
		return courseSummary;
	}

	public void setCourseSummary(String courseSummary) {
		this.courseSummary = courseSummary;
	}

	public Long getNormId() {
		return normId;
	}

	public void setNormId(Long normId) {
		this.normId = normId;
	}

	public String getNormName() {
		return normName;
	}

	public void setNormName(String normName) {
		this.normName = normName;
	}

	public Float getPeriod() {
		return period;
	}

	public void setPeriod(Float period) {
		this.period = period;
	}

	public Float getCredit() {
		return credit;
	}

	public void setCredit(Float credit) {
		this.credit = credit;
	}

	public Long getZyyOrgId() {
		return zyyOrgId;
	}

	public void setZyyOrgId(Long zyyOrgId) {
		this.zyyOrgId = zyyOrgId;
	}

	public String getAnotherName() {
		return anotherName;
	}

	public void setAnotherName(String anotherName) {
		this.anotherName = anotherName;
	}

}


