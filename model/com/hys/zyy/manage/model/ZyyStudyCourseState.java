package com.hys.zyy.manage.model;

import java.util.Date;

/**
 * 
 * 标题：住院医
 * 
 * 作者：
 * 
 * 描述：课程状态
 * 
 * 说明:
 */
public class ZyyStudyCourseState extends ZyyBaseObject{

	

	/**
	 * 
	 */
	private static final long serialVersionUID = 357424299084410951L;

	/**
	 * 主键ID
	 */
	private Long id ;
	
	/**
	 * 一级分类
	 */
	private String courseTypeFirst;
	/**
	 * 所属科室
	 */
	private String aliasName;
	
	/**
	 * 课程名称
	 */
	private String courseName;
	/**
	 * 课程别名
	 */
	private String courseAnotherName;
	
	/**
	 * 学时数
	 */
	private Long courseTime;
	/**
	 * 课时数
	 */
	private Long courseHours;
	/**
	 * 上线时间
	 */
	private Date addCourseTime;

	/**
	 * 点击次数
	 */
	private Long clickSum;
	
	/**
	 * 评价指数
	 */
	private Long assessIndex;
	/**
	 * 学习状态
	 */
	private Long studyCon;
	/**
	 * 课程分组
	 */
	private Long groupId;
	/**
	 * 住院医师
	 */
	private Long isNotUser;
	/**
	 * 其他学员
	 */
	private Long isNotOtherUser;
	/**
	 * 开放形式
	 */
	private Long openForm;
	/**
	 * 所属基地
	 * @return
	 */
	private Long baseId;
	
	public Long getBaseId() {
		return baseId;
	}
	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}
	public String getCourseAnotherName() {
		return courseAnotherName;
	}
	public void setCourseAnotherName(String courseAnotherName) {
		this.courseAnotherName = courseAnotherName;
	}
	public String getAliasName() {
		return aliasName;
	}
	public void setAliasName(String aliasName) {
		this.aliasName = aliasName;
	}
	public Long getCourseHours() {
		return courseHours;
	}
	public void setCourseHours(Long courseHours) {
		this.courseHours = courseHours;
	}
	public Long getIsNotUser() {
		return isNotUser;
	}
	public void setIsNotUser(Long isNotUser) {
		this.isNotUser = isNotUser;
	}
	public Long getIsNotOtherUser() {
		return isNotOtherUser;
	}
	public void setIsNotOtherUser(Long isNotOtherUser) {
		this.isNotOtherUser = isNotOtherUser;
	}
	public Long getOpenForm() {
		return openForm;
	}
	public void setOpenForm(Long openForm) {
		this.openForm = openForm;
	}
	public Long getGroupId() {
		return groupId;
	}
	public void setGroupId(Long groupId) {
		this.groupId = groupId;
	}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getCourseTypeFirst() {
		return courseTypeFirst;
	}
	public void setCourseTypeFirst(String courseTypeFirst) {
		this.courseTypeFirst = courseTypeFirst;
	}
	public String getCourseName() {
		return courseName;
	}
	public void setCourseName(String courseName) {
		this.courseName = courseName;
	}
	
	public Long getCourseTime() {
		return courseTime;
	}
	public void setCourseTime(Long courseTime) {
		this.courseTime = courseTime;
	}
	public Date getAddCourseTime() {
		return addCourseTime;
	}
	public void setAddCourseTime(Date addCourseTime) {
		this.addCourseTime = addCourseTime;
	}
	public Long getClickSum() {
		return clickSum;
	}
	public void setClickSum(Long clickSum) {
		this.clickSum = clickSum;
	}
	public Long getAssessIndex() {
		return assessIndex;
	}
	public void setAssessIndex(Long assessIndex) {
		this.assessIndex = assessIndex;
	}
	public Long getStudyCon() {
		return studyCon;
	}
	public void setStudyCon(Long studyCon) {
		this.studyCon = studyCon;
	}
	
	
}


