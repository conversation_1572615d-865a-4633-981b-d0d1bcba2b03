package com.hys.zyy.manage.model;
/**
 * 
 * 标题：住院医
 * 
 * 作者：陈来宾 2012-11-21
 * 
 * 描述：课程类型
 * 
 * 说明:
 */
public class ZyyStudyCourseType extends ZyyBaseObject{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	/**
	 * 主键ID
	 */
	private Long id ;
	
	/**
	 * 课程类型名
	 */
	private String courseTypeName;
	
	/**
	 * 课程类型级别
	 * 1 -一级 2 -二级 3:三级 4：四级
	 */
	private Long courseTypeLevel;

	/**
	 * 上级ID
	 */
	private Long parentId;

	/**
	 * 所属医院ID
	 */
	private Long zyyOrgId;

	/**
	 * 属性
	 * 1;公共科目 2:好医生课程
	 */
	private Long property;
	
	

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getCourseTypeName() {
		return courseTypeName;
	}

	public void setCourseTypeName(String courseTypeName) {
		this.courseTypeName = courseTypeName;
	}

	public Long getCourseTypeLevel() {
		return courseTypeLevel;
	}

	public void setCourseTypeLevel(Long courseTypeLevel) {
		this.courseTypeLevel = courseTypeLevel;
	}

	public Long getParentId() {
		return parentId;
	}

	public void setParentId(Long parentId) {
		this.parentId = parentId;
	}

	public Long getZyyOrgId() {
		return zyyOrgId;
	}

	public void setZyyOrgId(Long zyyOrgId) {
		this.zyyOrgId = zyyOrgId;
	}

	public Long getProperty() {
		return property;
	}

	public void setProperty(Long property) {
		this.property = property;
	}

}


