package com.hys.zyy.manage.model;
/**
 * 
 * 标题：zyy
 * 
 * 作者：Tony Apr 23, 2012
 * 
 * 描述：
 * 
 * 说明:
 */
public class ZyySummarizeStatistics extends ZyyBaseObject {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = -7684522389411811095L;

	/**
	 * 标准基地id
	 */
	private Long baseStdId;
	
	/**
	 * 标准基地名称
	 */
	private String baseStdName;
	
	/**
	 * 医院id
	 */
	private Long hospitalId;
	
	/**
	 * 医院名称
	 */
	private String hospitalName;

	/**
	 * 报名
	 */
	private Integer baoMingStatusNum;
	
	/**
	 * 面试
	 */
	private Integer mianShiStatusNum;
	
	/**
	 * 签约
	 */
	private Integer qianYueStatusNum;
	
	/**
	 * 报到 
	 */
	private Integer baoDaoStatusNum;
	
	/**
	 * 毁约 
	 */
	private Integer huiYueStatusNum;
	
	/**
	 * 生源地 本地
	 */
	private Integer homePlaceNativeNum;
	
	/**
	 * 生源地 外地
	 */
	private Integer homePlaceNotNativeNum;
	
	/**
	 *毕业时间  应届
	 */
	private Integer freshNum;
	
	/**
	 * 毕业时间  非应届
	 */
	private Integer notFreshNum;
	
	/**
	 * 学历 本科
	 */
	private Integer undergraduateNum;
	
	/**
	 * 学历 硕士
	 */
	private Integer masterNum;
	
	/**
	 * 学历 博士
	 */
	private Integer doctorNum;
	
	/**
	 * 毕业院校 一类
	 */
	private Integer graduateSchoolType1Num;
	
	/**
	 * 毕业院校 二类
	 */
	private Integer graduateSchoolType2Num;
	
	/**
	 * 毕业院校 三类
	 */
	private Integer graduateSchoolType3Num;
	
	/**
	 * 毕业院校 四类
	 */
	private Integer graduateSchoolType4Num;

	public Long getBaseStdId() {
		return baseStdId;
	}

	public void setBaseStdId(Long baseStdId) {
		this.baseStdId = baseStdId;
	}

	public String getBaseStdName() {
		return baseStdName;
	}

	public void setBaseStdName(String baseStdName) {
		this.baseStdName = baseStdName;
	}

	public Long getHospitalId() {
		return hospitalId;
	}

	public void setHospitalId(Long hospitalId) {
		this.hospitalId = hospitalId;
	}

	public String getHospitalName() {
		return hospitalName;
	}

	public void setHospitalName(String hospitalName) {
		this.hospitalName = hospitalName;
	}

	public Integer getHomePlaceNativeNum() {
		return homePlaceNativeNum;
	}

	public void setHomePlaceNativeNum(Integer homePlaceNativeNum) {
		this.homePlaceNativeNum = homePlaceNativeNum;
	}

	public Integer getHomePlaceNotNativeNum() {
		return homePlaceNotNativeNum;
	}

	public void setHomePlaceNotNativeNum(Integer homePlaceNotNativeNum) {
		this.homePlaceNotNativeNum = homePlaceNotNativeNum;
	}

	public Integer getFreshNum() {
		return freshNum;
	}

	public void setFreshNum(Integer freshNum) {
		this.freshNum = freshNum;
	}

	public Integer getNotFreshNum() {
		return notFreshNum;
	}

	public void setNotFreshNum(Integer notFreshNum) {
		this.notFreshNum = notFreshNum;
	}

	public Integer getUndergraduateNum() {
		return undergraduateNum;
	}

	public void setUndergraduateNum(Integer undergraduateNum) {
		this.undergraduateNum = undergraduateNum;
	}

	public Integer getMasterNum() {
		return masterNum;
	}

	public void setMasterNum(Integer masterNum) {
		this.masterNum = masterNum;
	}

	public Integer getDoctorNum() {
		return doctorNum;
	}

	public void setDoctorNum(Integer doctorNum) {
		this.doctorNum = doctorNum;
	}

	public Integer getGraduateSchoolType1Num() {
		return graduateSchoolType1Num;
	}

	public void setGraduateSchoolType1Num(Integer graduateSchoolType1Num) {
		this.graduateSchoolType1Num = graduateSchoolType1Num;
	}

	public Integer getGraduateSchoolType2Num() {
		return graduateSchoolType2Num;
	}

	public void setGraduateSchoolType2Num(Integer graduateSchoolType2Num) {
		this.graduateSchoolType2Num = graduateSchoolType2Num;
	}

	public Integer getGraduateSchoolType3Num() {
		return graduateSchoolType3Num;
	}

	public void setGraduateSchoolType3Num(Integer graduateSchoolType3Num) {
		this.graduateSchoolType3Num = graduateSchoolType3Num;
	}

	public Integer getGraduateSchoolType4Num() {
		return graduateSchoolType4Num;
	}

	public void setGraduateSchoolType4Num(Integer graduateSchoolType4Num) {
		this.graduateSchoolType4Num = graduateSchoolType4Num;
	}

	public Integer getBaoMingStatusNum() {
		return baoMingStatusNum;
	}

	public void setBaoMingStatusNum(Integer baoMingStatusNum) {
		this.baoMingStatusNum = baoMingStatusNum;
	}

	public Integer getMianShiStatusNum() {
		return mianShiStatusNum;
	}

	public void setMianShiStatusNum(Integer mianShiStatusNum) {
		this.mianShiStatusNum = mianShiStatusNum;
	}

	public Integer getQianYueStatusNum() {
		return qianYueStatusNum;
	}

	public void setQianYueStatusNum(Integer qianYueStatusNum) {
		this.qianYueStatusNum = qianYueStatusNum;
	}

	public Integer getBaoDaoStatusNum() {
		return baoDaoStatusNum;
	}

	public void setBaoDaoStatusNum(Integer baoDaoStatusNum) {
		this.baoDaoStatusNum = baoDaoStatusNum;
	}

	public Integer getHuiYueStatusNum() {
		return huiYueStatusNum;
	}

	public void setHuiYueStatusNum(Integer huiYueStatusNum) {
		this.huiYueStatusNum = huiYueStatusNum;
	}
	
	
	

	
	
	
}
