package com.hys.zyy.manage.model;

import java.io.Serializable;

public class ZyySyncUpdateInfo implements Serializable{
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	private String token;
	
	private String batch_id;
	
	private String update_type;
	
	private String version;
	
	private String trade_code;
	
	private ZyySyncInfo info;

	public String getToken() {
		return token;
	}

	public void setToken(String token) {
		this.token = token;
	}

	public String getBatch_id() {
		return batch_id;
	}

	public void setBatch_id(String batch_id) {
		this.batch_id = batch_id;
	}

	public String getUpdate_type() {
		return update_type;
	}

	public void setUpdate_type(String update_type) {
		this.update_type = update_type;
	}

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public String getTrade_code() {
		return trade_code;
	}

	public void setTrade_code(String trade_code) {
		this.trade_code = trade_code;
	}

	public ZyySyncInfo getInfo() {
		return info;
	}

	public void setInfo(ZyySyncInfo info) {
		this.info = info;
	}
	
	
	
	
}
