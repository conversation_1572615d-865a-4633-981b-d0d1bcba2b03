package com.hys.zyy.manage.model;

/**
 * 教学活动评价统计
 * <AUTHOR>
 *
 */
public class ZyyTaeStatistics {
	
	//教学活动ID
	private Long activityId;

	//教学活动名称
	private String activityName;

	//创建者ID
	private Long creater;
	
	//活动类型名称
	private String typeName;
	
	//活动开始时间
	private String startTime;
	
	//活动结束时间
	private String endTime;
	
	//创建者类型
	private Integer createrType;
	
	//创建者名称
	private String createrName;
	
	//主讲人
	private String speaker;
	
	//总人数
	private Integer totalNum;
	
	//评价人数
	private Integer evaluateNum;
	
	//评价总分
	private Integer average;
	
	//态度评价总分
	private Integer tdAverage;
	
	//内容评价总分
	private Integer nrAverage;
	
	//教学评价总分
	private Integer jxAverage;
	
	//水平评价总分
	private Integer spAverage;
	
	//效果评价总分
	private Integer xgAverage;
	
	public Integer getAverage() {
		return average;
	}

	public void setAverage(Integer average) {
		this.average = average;
	}

	public Long getActivityId() {
		return activityId;
	}

	public void setActivityId(Long activityId) {
		this.activityId = activityId;
	}

	public String getActivityName() {
		return activityName;
	}

	public void setActivityName(String activityName) {
		this.activityName = activityName;
	}

	public Long getCreater() {
		return creater;
	}

	public void setCreater(Long creater) {
		this.creater = creater;
	}

	public String getTypeName() {
		return typeName;
	}

	public void setTypeName(String typeName) {
		this.typeName = typeName;
	}

	public String getStartTime() {
		return startTime;
	}

	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}

	public String getEndTime() {
		return endTime;
	}

	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}

	public Integer getCreaterType() {
		return createrType;
	}

	public void setCreaterType(Integer createrType) {
		this.createrType = createrType;
	}

	public String getCreaterName() {
		return createrName;
	}

	public void setCreaterName(String createrName) {
		this.createrName = createrName;
	}

	public String getSpeaker() {
		return speaker;
	}

	public void setSpeaker(String speaker) {
		this.speaker = speaker;
	}

	public Integer getTotalNum() {
		return totalNum;
	}

	public void setTotalNum(Integer totalNum) {
		this.totalNum = totalNum;
	}

	public Integer getEvaluateNum() {
		return evaluateNum;
	}

	public void setEvaluateNum(Integer evaluateNum) {
		this.evaluateNum = evaluateNum;
	}

	public Integer getTdAverage() {
		return tdAverage;
	}

	public void setTdAverage(Integer tdAverage) {
		this.tdAverage = tdAverage;
	}

	public Integer getNrAverage() {
		return nrAverage;
	}

	public void setNrAverage(Integer nrAverage) {
		this.nrAverage = nrAverage;
	}

	public Integer getJxAverage() {
		return jxAverage;
	}

	public void setJxAverage(Integer jxAverage) {
		this.jxAverage = jxAverage;
	}

	public Integer getSpAverage() {
		return spAverage;
	}

	public void setSpAverage(Integer spAverage) {
		this.spAverage = spAverage;
	}

	public Integer getXgAverage() {
		return xgAverage;
	}

	public void setXgAverage(Integer xgAverage) {
		this.xgAverage = xgAverage;
	}
}
