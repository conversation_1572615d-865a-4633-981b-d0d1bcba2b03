package com.hys.zyy.manage.model;

import java.io.Serializable;

public class ZyyTeachActInfoVO implements Serializable {
	private static final long serialVersionUID = 1L;
	/*
	 * 学员ID
	 */
	private Long residencyId;
	/*
	 * 教学活动名称
	 */
	private String activityName;
	/*
	 * 教学活动开始时间
	 */
	private String teachStartDateStr;
	/*
	 * 活动时间
	 */
	private String teachDateSection;
	/*
	 * 教学活动创建者
	 */
	private String createrName;
	/*
	 * 出勤情况
	 */
	private String attendStatusStr;
	/*
	 * 轮转时间
	 */
	private String dateSection;
	/*
	 * 轮转科室
	 */
	private String cycleDept;

	public ZyyTeachActInfoVO() {
		super();
	}

	public ZyyTeachActInfoVO(Long residencyId) {
		super();
		this.residencyId = residencyId;
	}

	public Long getResidencyId() {
		return residencyId;
	}

	public void setResidencyId(Long residencyId) {
		this.residencyId = residencyId;
	}

	public String getActivityName() {
		return activityName;
	}

	public void setActivityName(String activityName) {
		this.activityName = activityName;
	}

	public String getTeachStartDateStr() {
		return teachStartDateStr;
	}

	public void setTeachStartDateStr(String teachStartDateStr) {
		this.teachStartDateStr = teachStartDateStr;
	}

	public String getCreaterName() {
		return createrName;
	}

	public void setCreaterName(String createrName) {
		this.createrName = createrName;
	}

	public String getAttendStatusStr() {
		return attendStatusStr;
	}

	public void setAttendStatusStr(String attendStatusStr) {
		this.attendStatusStr = attendStatusStr;
	}

	public String getDateSection() {
		return dateSection;
	}

	public void setDateSection(String dateSection) {
		this.dateSection = dateSection;
	}

	public String getCycleDept() {
		return cycleDept;
	}

	public void setCycleDept(String cycleDept) {
		this.cycleDept = cycleDept;
	}

	public String getTeachDateSection() {
		return teachDateSection;
	}

	public void setTeachDateSection(String teachDateSection) {
		this.teachDateSection = teachDateSection;
	}

}
