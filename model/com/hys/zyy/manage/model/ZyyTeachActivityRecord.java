package com.hys.zyy.manage.model;

import java.io.Serializable;
import java.util.Date;

/**
 * 教学活动登记
 */
public class ZyyTeachActivityRecord implements Serializable {
	private static final long serialVersionUID = 1L;
	/*
	 * ID
	 */
	private Long id;
	/*
	 * 学员ID
	 */
	private Long residencyId;
	/*
	 * 科室ID
	 */
	private Long deptId;
	/*
	 * 科室轮转开始时间
	 */
	private Date startDate;
	/*
	 * 科室轮转结束时间
	 */
	private Date endDate;
	/*
	 * 教学活动日期
	 */
	private Date teachActivityDate;
	/*
	 * 教学活动标题
	 */
	private String teachActivityTitle;
	/*
	 * 教学活动内容
	 */
	private String teachActivityContent;
	/*
	 * 活动形式（1=入科教育；2=病例讨论；3=学术活动；4=小讲课；5=主任教学查房；6=其他形式学习；7=技能操作）
	 */
	private Integer teachActivityType;
	/*
	 * 学时
	 */
	private String teachActivityHour;
	/*
	 * 主讲人
	 */
	private String teachActivitySpeaker;
	/*
	 * 备注
	 */
	private String remark;
	/*
	 * 状态（1=有效；-1=失效）
	 */
	private Integer state;
	private Date createTime;
	private Date updateTime;

	public ZyyTeachActivityRecord() {
		super();
	}

	public ZyyTeachActivityRecord(Long residencyId, Long deptId) {
		super();
		this.residencyId = residencyId;
		this.deptId = deptId;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getResidencyId() {
		return residencyId;
	}

	public void setResidencyId(Long residencyId) {
		this.residencyId = residencyId;
	}

	public Long getDeptId() {
		return deptId;
	}

	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}

	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	public Date getTeachActivityDate() {
		return teachActivityDate;
	}

	public void setTeachActivityDate(Date teachActivityDate) {
		this.teachActivityDate = teachActivityDate;
	}

	public String getTeachActivityTitle() {
		return teachActivityTitle;
	}

	public void setTeachActivityTitle(String teachActivityTitle) {
		this.teachActivityTitle = teachActivityTitle == null ? null : teachActivityTitle.trim();
	}

	public String getTeachActivityContent() {
		return teachActivityContent;
	}

	public void setTeachActivityContent(String teachActivityContent) {
		this.teachActivityContent = teachActivityContent == null ? null : teachActivityContent.trim();
	}

	public Integer getTeachActivityType() {
		return teachActivityType;
	}

	public void setTeachActivityType(Integer teachActivityType) {
		this.teachActivityType = teachActivityType;
	}

	public String getTeachActivityHour() {
		return teachActivityHour;
	}

	public void setTeachActivityHour(String teachActivityHour) {
		this.teachActivityHour = teachActivityHour == null ? null : teachActivityHour.trim();
	}

	public String getTeachActivitySpeaker() {
		return teachActivitySpeaker;
	}

	public void setTeachActivitySpeaker(String teachActivitySpeaker) {
		this.teachActivitySpeaker = teachActivitySpeaker == null ? null : teachActivitySpeaker.trim();
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark == null ? null : remark.trim();
	}

	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ZyyTeachActivityRecord other = (ZyyTeachActivityRecord) obj;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		return true;
	}

	@Override
	public String toString() {
		return "ZyyTeachActivityRecord [id=" + id + ", residencyId="
				+ residencyId + ", deptId=" + deptId + ", startDate="
				+ startDate + ", endDate=" + endDate + ", teachActivityDate="
				+ teachActivityDate + ", teachActivityTitle="
				+ teachActivityTitle + ", teachActivityContent="
				+ teachActivityContent + ", teachActivityType="
				+ teachActivityType + ", teachActivityHour="
				+ teachActivityHour + ", teachActivitySpeaker="
				+ teachActivitySpeaker + ", remark=" + remark + ", state="
				+ state + ", createTime=" + createTime + ", updateTime="
				+ updateTime + "]";
	}

}
