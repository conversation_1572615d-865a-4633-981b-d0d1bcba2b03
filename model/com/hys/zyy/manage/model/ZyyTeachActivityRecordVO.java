package com.hys.zyy.manage.model;

import java.util.Date;
import java.util.List;

import org.springframework.web.multipart.MultipartFile;

public class ZyyTeachActivityRecordVO extends ZyyTeachActivityRecord {

	private Integer bookId, booksize;
	private Long deptStdId, manualStdId;
	private Date sDate, eDate;
	private String deptName, timeSection, s, e, other, teachActivityDateStr, teachActivityTypeStr, deleteFileIdStr;
	private List<List<ZyyTeachActivityRecordFileVO>> teachActivityRecordFiles;
	private List<ZyyTeachActivityRecordFileVO> atts;
	private List<MultipartFile> files;

	public ZyyTeachActivityRecordVO() {
		super();
	}

	public ZyyTeachActivityRecordVO(Long residencyId, Long deptId) {
		super(residencyId, deptId);
	}

	public Integer getBookId() {
		return bookId;
	}

	public void setBookId(Integer bookId) {
		this.bookId = bookId;
	}

	public Integer getBooksize() {
		return booksize;
	}

	public void setBooksize(Integer booksize) {
		this.booksize = booksize;
	}

	public Long getDeptStdId() {
		return deptStdId;
	}

	public void setDeptStdId(Long deptStdId) {
		this.deptStdId = deptStdId;
	}

	public Long getManualStdId() {
		return manualStdId;
	}

	public void setManualStdId(Long manualStdId) {
		this.manualStdId = manualStdId;
	}

	public Date getsDate() {
		return sDate;
	}

	public void setsDate(Date sDate) {
		this.sDate = sDate;
	}

	public Date geteDate() {
		return eDate;
	}

	public void seteDate(Date eDate) {
		this.eDate = eDate;
	}

	public String getDeptName() {
		return deptName;
	}

	public void setDeptName(String deptName) {
		this.deptName = deptName == null ? null : deptName.trim();
	}

	public String getTimeSection() {
		return timeSection;
	}

	public void setTimeSection(String timeSection) {
		this.timeSection = timeSection == null ? null : timeSection.trim();
	}

	public String getS() {
		return s;
	}

	public void setS(String s) {
		this.s = s == null ? null : s.trim();
	}

	public String getE() {
		return e;
	}

	public void setE(String e) {
		this.e = e == null ? null : e.trim();
	}

	public String getOther() {
		return other;
	}

	public void setOther(String other) {
		this.other = other == null ? null : other.trim();
	}

	public String getTeachActivityDateStr() {
		return teachActivityDateStr;
	}

	public void setTeachActivityDateStr(String teachActivityDateStr) {
		this.teachActivityDateStr = teachActivityDateStr == null ? null : teachActivityDateStr.trim();
	}

	public String getTeachActivityTypeStr() {
		return teachActivityTypeStr;
	}

	public void setTeachActivityTypeStr(String teachActivityTypeStr) {
		this.teachActivityTypeStr = teachActivityTypeStr == null ? null : teachActivityTypeStr.trim();
	}
	
	public List<ZyyTeachActivityRecordFileVO> getAtts() {
		return atts;
	}

	public void setAtts(List<ZyyTeachActivityRecordFileVO> atts) {
		this.atts = atts;
	}

	public List<List<ZyyTeachActivityRecordFileVO>> getTeachActivityRecordFiles() {
		return teachActivityRecordFiles;
	}

	public void setTeachActivityRecordFiles(List<List<ZyyTeachActivityRecordFileVO>> teachActivityRecordFiles) {
		this.teachActivityRecordFiles = teachActivityRecordFiles;
	}

	public List<MultipartFile> getFiles() {
		return files;
	}

	public void setFiles(List<MultipartFile> files) {
		this.files = files;
	}

	public String getDeleteFileIdStr() {
		return deleteFileIdStr;
	}

	public void setDeleteFileIdStr(String deleteFileIdStr) {
		this.deleteFileIdStr = deleteFileIdStr == null ? null : deleteFileIdStr.trim();
	}

}
