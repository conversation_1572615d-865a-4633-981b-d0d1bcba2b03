package com.hys.zyy.manage.model;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

public class ZyyTeacherCombineLog implements Serializable {
    /**
     * ID
     */
    private Long id;
    //主账户id
    private Long userId;

    /**
     * 账号
     */
    private String accountName;

    /**
     * 创建人
     */
    private Long createUserId;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;
    
    //子表数据
    private List<ZyyTeacherCombineLogSub> childList;
    
    //待合并用户id
    private String childUserId;

    private static final long serialVersionUID = 1L;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public String getAccountName() {
		return accountName;
	}

	public void setAccountName(String accountName) {
		this.accountName = accountName;
	}

	public Long getCreateUserId() {
		return createUserId;
	}

	public void setCreateUserId(Long createUserId) {
		this.createUserId = createUserId;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Date getUpdateDate() {
		return updateDate;
	}

	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	public List<ZyyTeacherCombineLogSub> getChildList() {
		return childList;
	}

	public void setChildList(List<ZyyTeacherCombineLogSub> childList) {
		this.childList = childList;
	}

	public String getChildUserId() {
		return childUserId;
	}

	public void setChildUserId(String childUserId) {
		this.childUserId = childUserId;
	}
}