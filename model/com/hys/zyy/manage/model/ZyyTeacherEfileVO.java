package com.hys.zyy.manage.model;

import java.util.List;

/**
 * 用户的相应表格的集合Vo类
 * <AUTHOR>
 *
 */
public class ZyyTeacherEfileVO extends ZyyBaseObject {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	//
	private ZyyUserAward zyyUserAward;
	
	//
	private List<ZyyUserAward> awards;  //带教老师的获得的奖励的集合
	
	//
	private  List<ZyyUserExtendStudy> educationList;  //带教教育背景的集合
	
	public List<ZyyUserExtendStudy> getEducationList() {
		return educationList;
	}

	public void setEducationList(List<ZyyUserExtendStudy> educationList) {
		this.educationList = educationList;
	}

	//
	private ZyyTutorWorkExperience zyyTutorWorkExperience; 
	
	//
	private List<ZyyTutorWorkExperience> experience;  //带教的工作经历的集合
	
	//
	private ZyyUserCertificate zyyUserCertificate; 
	
	//
	private List<ZyyUserCertificate> certificates;  //个人证件的集合
	
	public ZyyUserCertificate getZyyUserCertificate() {
		return zyyUserCertificate;
	}

	public void setZyyUserCertificate(ZyyUserCertificate zyyUserCertificate) {
		this.zyyUserCertificate = zyyUserCertificate;
	}

	public List<ZyyUserCertificate> getCertificates() {
		return certificates;
	}

	public void setCertificates(List<ZyyUserCertificate> certificates) {
		this.certificates = certificates;
	}

	public List<ZyyTutorWorkExperience> getExperience() {
		return experience;
	}

	public void setExperience(List<ZyyTutorWorkExperience> experience) {
		this.experience = experience;
	}

	public ZyyUserAward getZyyUserAward() {
		return zyyUserAward;
	}

	public void setZyyUserAward(ZyyUserAward zyyUserAward) {
		this.zyyUserAward = zyyUserAward;
	}

	public List<ZyyUserAward> getAwards() {
		return awards;
	}

	public void setAwards(List<ZyyUserAward> awards) {
		this.awards = awards;
	}
	public ZyyTutorWorkExperience getZyyTutorWorkExperience() {
		return zyyTutorWorkExperience;
	}

	public void setZyyTutorWorkExperience(
			ZyyTutorWorkExperience zyyTutorWorkExperience) {
		this.zyyTutorWorkExperience = zyyTutorWorkExperience;
	}

	

}
