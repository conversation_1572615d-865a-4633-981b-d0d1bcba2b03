package com.hys.zyy.manage.model;

import java.io.Serializable;
import java.util.Date;

/**
 * 带教扩展信息表
 */
public class ZyyTeacherExtend implements Serializable {
	private static final long serialVersionUID = 1L;
	/*
	 * 用户ID
	 */
	private Long zyyUserId;
	/*
	 * 国籍及地区
	 */
	private String gjjdq;
	/*
	 * 现任职务开始时间
	 */
	private Date xrzwkssj;
	/*
	 * 职称聘用时间
	 */
	private Date zcpysj;
	/*
	 * 是否获得教师职称（0：否；1：是）
	 */
	private Integer sfhdjszc;
	/*
	 * 教师职称聘用时间
	 */
	private Date jszcpysj;
	/*
	 * 医师资格证书编码
	 */
	private String yszgzsbm;
	/*
	 * 医师执业范围
	 */
	private String yszyfw;
	/*
	 * 从事本专业临床工作年限（年）
	 */
	private Float csbzylcgznx;
	/*
	 * 是否取得住院医师规范化培训合格证书（0：否；1：是）
	 */
	private Integer qdzyyhgzs;
	/*
	 * 住院医师规范化培训合格证书发证地区
	 */
	private String zyyhgzsfzdq;
	/*
	 * 住院医师规范化培训合格证书发放时间
	 */
	private Date zyyhgzsfzsj;
	/*
	 * 累计带教实习生年限
	 */
	private Integer ljdjsxsnx;
	/*
	 * 累计带教实习生人数
	 */
	private Integer ljdjsxsrs;
	/*
	 * 累计带教住院医师（本专业）年限
	 */
	private Integer ljdjzyybzynx;
	/*
	 * 累计带教住院医师（本专业）人数
	 */
	private Integer ljdjzyybzyrs;
	/*
	 * 累计带教进修医师年限
	 */
	private Integer ljdjjxysnx;
	/*
	 * 累计带教进修医师人数
	 */
	private Integer ljdjjxysrs;
	/*
	 * 累计带教专科医师（本专科）年限
	 */
	private Integer ljdjzkysbzknx;
	/*
	 * 累计带教专科医师（本专科）人数
	 */
	private Integer ljdjzkysbzkrs;
	/*
	 * 带教资格变动时间
	 */
	private Date djzgbdsj;
	/*
	 * 创建时间
	 */
	private Date createTime;
	/*
	 * 修改时间
	 */
	private Date update_time;
	/**
	 * 允许带教带人人数
	 */
	private String allowTeachingNum;
	/**
	 * 当前带人人数
	 */
	private String teachingNum;

	public ZyyTeacherExtend() {
		super();
	}

	public ZyyTeacherExtend(Date djzgbdsj) {
		super();
		this.djzgbdsj = djzgbdsj;
	}

	public ZyyTeacherExtend(Long zyyUserId, Date djzgbdsj) {
		super();
		this.zyyUserId = zyyUserId;
		this.djzgbdsj = djzgbdsj;
	}

	public Long getZyyUserId() {
		return zyyUserId;
	}

	public void setZyyUserId(Long zyyUserId) {
		this.zyyUserId = zyyUserId;
	}

	public String getGjjdq() {
		return gjjdq;
	}

	public void setGjjdq(String gjjdq) {
		this.gjjdq = gjjdq;
	}

	public Date getXrzwkssj() {
		return xrzwkssj;
	}

	public void setXrzwkssj(Date xrzwkssj) {
		this.xrzwkssj = xrzwkssj;
	}

	public Date getZcpysj() {
		return zcpysj;
	}

	public void setZcpysj(Date zcpysj) {
		this.zcpysj = zcpysj;
	}

	public Integer getSfhdjszc() {
		return sfhdjszc;
	}

	public void setSfhdjszc(Integer sfhdjszc) {
		this.sfhdjszc = sfhdjszc;
	}

	public Date getJszcpysj() {
		return jszcpysj;
	}

	public void setJszcpysj(Date jszcpysj) {
		this.jszcpysj = jszcpysj;
	}

	public String getYszgzsbm() {
		return yszgzsbm;
	}

	public void setYszgzsbm(String yszgzsbm) {
		this.yszgzsbm = yszgzsbm;
	}

	public String getYszyfw() {
		return yszyfw;
	}

	public void setYszyfw(String yszyfw) {
		this.yszyfw = yszyfw;
	}

	public Float getCsbzylcgznx() {
		return csbzylcgznx;
	}

	public void setCsbzylcgznx(Float csbzylcgznx) {
		this.csbzylcgznx = csbzylcgznx;
	}

	public Integer getQdzyyhgzs() {
		return qdzyyhgzs;
	}

	public void setQdzyyhgzs(Integer qdzyyhgzs) {
		this.qdzyyhgzs = qdzyyhgzs;
	}

	public String getZyyhgzsfzdq() {
		return zyyhgzsfzdq;
	}

	public void setZyyhgzsfzdq(String zyyhgzsfzdq) {
		this.zyyhgzsfzdq = zyyhgzsfzdq;
	}

	public Date getZyyhgzsfzsj() {
		return zyyhgzsfzsj;
	}

	public void setZyyhgzsfzsj(Date zyyhgzsfzsj) {
		this.zyyhgzsfzsj = zyyhgzsfzsj;
	}

	public Integer getLjdjsxsnx() {
		return ljdjsxsnx;
	}

	public void setLjdjsxsnx(Integer ljdjsxsnx) {
		this.ljdjsxsnx = ljdjsxsnx;
	}

	public Integer getLjdjsxsrs() {
		return ljdjsxsrs;
	}

	public void setLjdjsxsrs(Integer ljdjsxsrs) {
		this.ljdjsxsrs = ljdjsxsrs;
	}

	public Integer getLjdjzyybzynx() {
		return ljdjzyybzynx;
	}

	public void setLjdjzyybzynx(Integer ljdjzyybzynx) {
		this.ljdjzyybzynx = ljdjzyybzynx;
	}

	public Integer getLjdjzyybzyrs() {
		return ljdjzyybzyrs;
	}

	public void setLjdjzyybzyrs(Integer ljdjzyybzyrs) {
		this.ljdjzyybzyrs = ljdjzyybzyrs;
	}

	public Integer getLjdjjxysnx() {
		return ljdjjxysnx;
	}

	public void setLjdjjxysnx(Integer ljdjjxysnx) {
		this.ljdjjxysnx = ljdjjxysnx;
	}

	public Integer getLjdjjxysrs() {
		return ljdjjxysrs;
	}

	public void setLjdjjxysrs(Integer ljdjjxysrs) {
		this.ljdjjxysrs = ljdjjxysrs;
	}

	public Integer getLjdjzkysbzknx() {
		return ljdjzkysbzknx;
	}

	public void setLjdjzkysbzknx(Integer ljdjzkysbzknx) {
		this.ljdjzkysbzknx = ljdjzkysbzknx;
	}

	public Integer getLjdjzkysbzkrs() {
		return ljdjzkysbzkrs;
	}

	public void setLjdjzkysbzkrs(Integer ljdjzkysbzkrs) {
		this.ljdjzkysbzkrs = ljdjzkysbzkrs;
	}

	public Date getDjzgbdsj() {
		return djzgbdsj;
	}

	public void setDjzgbdsj(Date djzgbdsj) {
		this.djzgbdsj = djzgbdsj;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getUpdate_time() {
		return update_time;
	}

	public void setUpdate_time(Date update_time) {
		this.update_time = update_time;
	}

	public String getAllowTeachingNum() {
		return allowTeachingNum;
	}

	public void setAllowTeachingNum(String allowTeachingNum) {
		this.allowTeachingNum = allowTeachingNum;
	}

	public String getTeachingNum() {
		return teachingNum;
	}

	public void setTeachingNum(String teachingNum) {
		this.teachingNum = teachingNum;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result
				+ ((zyyUserId == null) ? 0 : zyyUserId.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ZyyTeacherExtend other = (ZyyTeacherExtend) obj;
		if (zyyUserId == null) {
			if (other.zyyUserId != null)
				return false;
		} else if (!zyyUserId.equals(other.zyyUserId))
			return false;
		return true;
	}

	@Override
	public String toString() {
		return "ZyyTeacherExtend [zyyUserId=" + zyyUserId + ", gjjdq=" + gjjdq
				+ ", xrzwkssj=" + xrzwkssj + ", zcpysj=" + zcpysj
				+ ", sfhdjszc=" + sfhdjszc + ", jszcpysj=" + jszcpysj
				+ ", yszgzsbm=" + yszgzsbm + ", yszyfw=" + yszyfw
				+ ", csbzylcgznx=" + csbzylcgznx + ", qdzyyhgzs=" + qdzyyhgzs
				+ ", zyyhgzsfzdq=" + zyyhgzsfzdq + ", zyyhgzsfzsj="
				+ zyyhgzsfzsj + ", ljdjsxsnx=" + ljdjsxsnx + ", ljdjsxsrs="
				+ ljdjsxsrs + ", ljdjzyybzynx=" + ljdjzyybzynx
				+ ", ljdjzyybzyrs=" + ljdjzyybzyrs + ", ljdjjxysnx="
				+ ljdjjxysnx + ", ljdjjxysrs=" + ljdjjxysrs
				+ ", ljdjzkysbzknx=" + ljdjzkysbzknx + ", ljdjzkysbzkrs="
				+ ljdjzkysbzkrs + ", djzgbdsj=" + djzgbdsj + ", createTime="
				+ createTime + ", update_time=" + update_time + "]";
	}

}
