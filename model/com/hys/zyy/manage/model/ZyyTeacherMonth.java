package com.hys.zyy.manage.model;

import java.util.Date;

/**
 * 带教按月拆分
 * <AUTHOR>
 * @date 2019-7-3下午5:13:43
 */
public class ZyyTeacherMonth extends ZyyBaseObject {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	//id
	private Long id;
	//科室id
	private Long deptId;
	//手册id zyy_dept_std 的id
	private Long dstdId;
	//带教id
	private Long teacherId;
	//带教开始时间  
	private String teacherStartTime;
	//带教结束时间  
	private String teacherEndTime;
	//开始时间  
	private String startTime;
	//结束时间  
	private String endTime;
	//审核状态  1 未审核 2 审核通过 3审核未通过
	private Integer status;
	//创建时间
	private Date createTime;
	//更新时间
	private Date updateTime;
	//审核备注
	private String checkRemark;
	//审核人ID
	private Long checkUserId;
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getDeptId() {
		return deptId;
	}
	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}
	public Long getDstdId() {
		return dstdId;
	}
	public void setDstdId(Long dstdId) {
		this.dstdId = dstdId;
	}
	public Long getTeacherId() {
		return teacherId;
	}
	public void setTeacherId(Long teacherId) {
		this.teacherId = teacherId;
	}
	public String getTeacherStartTime() {
		return teacherStartTime;
	}
	public void setTeacherStartTime(String teacherStartTime) {
		this.teacherStartTime = teacherStartTime;
	}
	public String getTeacherEndTime() {
		return teacherEndTime;
	}
	public void setTeacherEndTime(String teacherEndTime) {
		this.teacherEndTime = teacherEndTime;
	}
	public String getStartTime() {
		return startTime;
	}
	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}
	public String getEndTime() {
		return endTime;
	}
	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	public Date getUpdateTime() {
		return updateTime;
	}
	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
	public String getCheckRemark() {
		return checkRemark;
	}
	public void setCheckRemark(String checkRemark) {
		this.checkRemark = checkRemark;
	}
	public Long getCheckUserId() {
		return checkUserId;
	}
	public void setCheckUserId(Long checkUserId) {
		this.checkUserId = checkUserId;
	}
}
