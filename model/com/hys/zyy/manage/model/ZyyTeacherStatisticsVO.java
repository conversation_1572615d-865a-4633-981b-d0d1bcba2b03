package com.hys.zyy.manage.model;



public class ZyyTeacherStatisticsVO extends ZyyUserExtend{
	/**
	 * 
	 */
	private static final long serialVersionUID = -884436155146414164L;
	//培训年级
	private String year;
    //培训专科
	private Long deptId;
	//科室Name
	private String deptName;
    //ID编号
	private String residencyId;
    //姓名
	private String userName;
	
    //培训学员数量
	private Integer studentNum;
    //审核手册人员数量
	private Integer checkmanualNum;
    //出科人员数量
	private Integer finishNum;
	//工作量汇总中的培训年纪
	private String workYear;
	
	//带教时长
	private Integer days;
	
	
	public String getWorkYear() {
		return workYear;
	}
	public void setWorkYear(String workYear) {
		this.workYear = workYear;
	}
	public String getYear() {
		return year;
	}
	public void setYear(String year) {
		this.year = year;
	}
	public Long getDeptId() {
		return deptId;
	}
	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}
	public String getDeptName() {
		return deptName;
	}
	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}
	public String getResidencyId() {
		return residencyId;
	}
	public void setResidencyId(String residencyId) {
		this.residencyId = residencyId;
	}
	public String getUserName() {
		return userName;
	}
	public void setUserName(String userName) {
		this.userName = userName;
	}
	public Integer getStudentNum() {
		return studentNum;
	}
	public void setStudentNum(Integer studentNum) {
		this.studentNum = studentNum;
	}
	public Integer getCheckmanualNum() {
		return checkmanualNum;
	}
	public void setCheckmanualNum(Integer checkmanualNum) {
		this.checkmanualNum = checkmanualNum;
	}
	public Integer getFinishNum() {
		return finishNum;
	}
	public void setFinishNum(Integer finishNum) {
		this.finishNum = finishNum;
	}
	public Integer getDays() {
		return days;
	}
	public void setDays(Integer days) {
		this.days = days;
	}
	
	
	
}