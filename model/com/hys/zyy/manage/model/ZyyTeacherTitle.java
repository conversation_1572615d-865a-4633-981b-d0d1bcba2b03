package com.hys.zyy.manage.model;

import java.io.Serializable;
import java.util.Date;

import com.hys.zyy.manage.util.BaseModel;

/**
 * 带教职称管理
 */
public class ZyyTeacherTitle extends BaseModel implements Serializable {
	private static final long serialVersionUID = 1L;
	/*
	 * ID
	 */
	private Long id;
	/*
	 * 职称名称
	 */
	private String titleName;
	/*
	 * 职称备注
	 */
	private String remark;
	/*
	 * 职称排序
	 */
	private Integer sortIndex;
	/*
	 * 状态（-1：失效；1：启用）
	 */
	private Integer state;
	/*
	 * 创建者
	 */
	private Long createUserId;
	/*
	 * 创建时间
	 */
	private Date createTime;
	/*
	 * 最后一次修改者
	 */
	private Long lastUpdateUserId;
	/*
	 * 最后一次修改时间
	 */
	private Date lastUpdateTime;
	/*
	 * BDP字典值CODE
	 */
	private String bdpDicCode;

	public ZyyTeacherTitle() {
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getTitleName() {
		return titleName;
	}

	public void setTitleName(String titleName) {
		this.titleName = titleName == null ? null : titleName.trim();
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public Integer getSortIndex() {
		return sortIndex;
	}

	public void setSortIndex(Integer sortIndex) {
		this.sortIndex = sortIndex;
	}

	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	public Long getCreateUserId() {
		return createUserId;
	}

	public void setCreateUserId(Long createUserId) {
		this.createUserId = createUserId;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Long getLastUpdateUserId() {
		return lastUpdateUserId;
	}

	public void setLastUpdateUserId(Long lastUpdateUserId) {
		this.lastUpdateUserId = lastUpdateUserId;
	}

	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}

	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}

	public String getBdpDicCode() {
		return bdpDicCode;
	}

	public void setBdpDicCode(String bdpDicCode) {
		this.bdpDicCode = bdpDicCode == null ? null : bdpDicCode.trim();
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ZyyTeacherTitle other = (ZyyTeacherTitle) obj;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		return true;
	}

	@Override
	public String toString() {
		return "ZyyTeacherTitle [id=" + id + ", titleName=" + titleName + ", remark=" + remark + ", sortIndex="
				+ sortIndex + ", state=" + state + ", createUserId=" + createUserId + ", createTime=" + createTime
				+ ", lastUpdateUserId=" + lastUpdateUserId + ", lastUpdateTime=" + lastUpdateTime + ", bdpDicCode="
				+ bdpDicCode + "]";
	}
	
}
