package com.hys.zyy.manage.model;

import java.io.Serializable;
import java.util.Date;

/**
 * 带教师资培训
 */
public class ZyyTeacherTrain implements Serializable {
	private static final long serialVersionUID = 1L;
	/*
	 * ID
	 */
	private Long id;
	/*
	 * 用户ID
	 */
	private Long zyyUserId;
	/*
	 * 培训开始时间
	 */
	private Date pxkssj;
	/*
	 * 培训结束时间
	 */
	private Date pxjssj;
	/*
	 * 培训班名称
	 */
	private String pxbmc;
	/*
	 * 培训专业
	 */
	private String pxzy;
	/*
	 * 培训班级别（1：国家级；2：省级；3：市/县级；4：院级）
	 */
	private Integer pxbjb;
	/*
	 * 培训形式（1：集中培训；2：短期进修：3：线上培训）
	 */
	private Integer pxxs;
	/*
	 * 是否获得培训证书（0：否；1：是）
	 */
	private Integer sfhdpxzs;
	/*
	 * 创建时间
	 */
	private Date createTime;
	/*
	 * 修改时间
	 */
	private Date update_time;
	/*
	 * 状态（0：失效；1：有效）
	 */
	private Integer state;

	public ZyyTeacherTrain() {
	}
	
	public ZyyTeacherTrain(Long zyyUserId) {
		super();
		this.zyyUserId = zyyUserId;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getZyyUserId() {
		return zyyUserId;
	}

	public void setZyyUserId(Long zyyUserId) {
		this.zyyUserId = zyyUserId;
	}

	public Date getPxkssj() {
		return pxkssj;
	}

	public void setPxkssj(Date pxkssj) {
		this.pxkssj = pxkssj;
	}

	public Date getPxjssj() {
		return pxjssj;
	}

	public void setPxjssj(Date pxjssj) {
		this.pxjssj = pxjssj;
	}

	public String getPxbmc() {
		return pxbmc;
	}

	public void setPxbmc(String pxbmc) {
		this.pxbmc = pxbmc;
	}

	public String getPxzy() {
		return pxzy;
	}

	public void setPxzy(String pxzy) {
		this.pxzy = pxzy;
	}

	public Integer getPxbjb() {
		return pxbjb;
	}

	public void setPxbjb(Integer pxbjb) {
		this.pxbjb = pxbjb;
	}

	public Integer getPxxs() {
		return pxxs;
	}

	public void setPxxs(Integer pxxs) {
		this.pxxs = pxxs;
	}

	public Integer getSfhdpxzs() {
		return sfhdpxzs;
	}

	public void setSfhdpxzs(Integer sfhdpxzs) {
		this.sfhdpxzs = sfhdpxzs;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getUpdate_time() {
		return update_time;
	}

	public void setUpdate_time(Date update_time) {
		this.update_time = update_time;
	}

	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ZyyTeacherTrain other = (ZyyTeacherTrain) obj;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		return true;
	}

	@Override
	public String toString() {
		return "ZyyTeacherTrain [id=" + id + ", zyyUserId=" + zyyUserId
				+ ", pxkssj=" + pxkssj + ", pxjssj=" + pxjssj + ", pxbmc="
				+ pxbmc + ", pxzy=" + pxzy + ", pxbjb=" + pxbjb + ", pxxs="
				+ pxxs + ", sfhdpxzs=" + sfhdpxzs + ", createTime="
				+ createTime + ", update_time=" + update_time + ", state="
				+ state + "]";
	}

}