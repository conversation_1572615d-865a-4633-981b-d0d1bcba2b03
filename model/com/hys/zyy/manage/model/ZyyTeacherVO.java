package com.hys.zyy.manage.model;

public class ZyyTeacherVO extends ZyyCycleTeacher {
	/*
	 * 带教姓名
	 */
	private String teacherName;
	/*
	 * 带教证件号码
	 */
	private String certificateNo;
	/*
	 * 带教状态（带教 / 非带教）
	 */
	private String teacherStatusStr;
	private Integer teacherStatus;
	/*
	 * 带教账号
	 */
	private String teacherAccountName;
	/*
	 * 带教所在科室
	 */
	private String teacherDeptNames;
	/*
	 * 学员姓名
	 */
	private String studentName;
	private Integer userCategory;
	/*
	 * 学员分类
	 */
	private String userCategoryStr;
	/*
	 * 人员状态
	 */
	private Integer zyyUserStatus;
	/*
	 * 学员证件号码
	 */
	private String stuCertificateNo;
	/*
	 * 学制
	 */
	private String schoolSystemStr;
	/*
	 * 年级
	 */
	private String year;
	/*
	 * 人员类型
	 */
	private Integer residencySource;
	private String residencySourceStr;
	/*
	 * 带教时间
	 */
	private String teacherTimeSection;
	/*
	 * 带教时长
	 */
	private String teacherDays;
	/*
	 * 带教开始时间 / 带教结束时间
	 */
	private String teacherStartDateStr, teacherEndDateStr;
	/*
	 * 带教开始时间 / 带教结束时间（年月日格式）
	 */
	private String teacherStartTimeStr, teacherEndTimeStr;
	/*
	 * 科室轮转时间
	 */
	private String deptCycleTime;
	/*
	 * 带教指定状态（1：已指定；2：未指定；3：指定不全）
	 */
	private Integer teacherPointState;
	private String winTitle;
	public ZyyTeacherVO() {
	}

	public String getTeacherName() {
		return teacherName;
	}

	public void setTeacherName(String teacherName) {
		this.teacherName = teacherName == null ? null : teacherName.trim();
	}

	public String getCertificateNo() {
		return certificateNo;
	}

	public void setCertificateNo(String certificateNo) {
		this.certificateNo = certificateNo == null ? null : certificateNo.trim();
	}

	public String getStuCertificateNo() {
		return stuCertificateNo;
	}

	public void setStuCertificateNo(String stuCertificateNo) {
		this.stuCertificateNo = stuCertificateNo == null ? null : stuCertificateNo.trim();
	}

	public String getTeacherStatusStr() {
		return teacherStatusStr;
	}

	public void setTeacherStatusStr(String teacherStatusStr) {
		this.teacherStatusStr = teacherStatusStr == null ? null : teacherStatusStr.trim();
	}

	public Integer getTeacherStatus() {
		return teacherStatus;
	}

	public void setTeacherStatus(Integer teacherStatus) {
		this.teacherStatus = teacherStatus;
	}

	public String getTeacherAccountName() {
		return teacherAccountName;
	}

	public void setTeacherAccountName(String teacherAccountName) {
		this.teacherAccountName = teacherAccountName == null ? null : teacherAccountName.trim();
	}

	public String getTeacherDeptNames() {
		return teacherDeptNames;
	}

	public void setTeacherDeptNames(String teacherDeptNames) {
		this.teacherDeptNames = teacherDeptNames == null ? null : teacherDeptNames.trim();
	}

	public String getStudentName() {
		return studentName;
	}

	public void setStudentName(String studentName) {
		this.studentName = studentName == null ? null : studentName.trim();
	}

	public Integer getUserCategory() {
		return userCategory;
	}

	public void setUserCategory(Integer userCategory) {
		this.userCategory = userCategory;
	}

	public String getUserCategoryStr() {
		return userCategoryStr;
	}

	public void setUserCategoryStr(String userCategoryStr) {
		this.userCategoryStr = userCategoryStr == null ? null : userCategoryStr.trim();
	}

	public Integer getZyyUserStatus() {
		return zyyUserStatus;
	}

	public void setZyyUserStatus(Integer zyyUserStatus) {
		this.zyyUserStatus = zyyUserStatus;
	}

	public String getSchoolSystemStr() {
		return schoolSystemStr;
	}

	public void setSchoolSystemStr(String schoolSystemStr) {
		this.schoolSystemStr = schoolSystemStr == null ? null : schoolSystemStr.trim();
	}

	public String getYear() {
		return year;
	}

	public void setYear(String year) {
		this.year = year == null ? null : year.trim();
	}

	public Integer getResidencySource() {
		return residencySource;
	}

	public void setResidencySource(Integer residencySource) {
		this.residencySource = residencySource;
	}

	public String getResidencySourceStr() {
		return residencySourceStr;
	}

	public void setResidencySourceStr(String residencySourceStr) {
		this.residencySourceStr = residencySourceStr == null ? null : residencySourceStr.trim();
	}

	public String getTeacherTimeSection() {
		return teacherTimeSection;
	}

	public void setTeacherTimeSection(String teacherTimeSection) {
		this.teacherTimeSection = teacherTimeSection == null ? null : teacherTimeSection.trim();
	}

	public String getTeacherDays() {
		return teacherDays;
	}

	public void setTeacherDays(String teacherDays) {
		this.teacherDays = teacherDays == null ? null : teacherDays.trim();
	}

	public String getTeacherStartDateStr() {
		return teacherStartDateStr;
	}

	public void setTeacherStartDateStr(String teacherStartDateStr) {
		this.teacherStartDateStr = teacherStartDateStr == null ? null : teacherStartDateStr.trim();
	}

	public String getTeacherStartTimeStr() {
		return teacherStartTimeStr;
	}

	public void setTeacherStartTimeStr(String teacherStartTimeStr) {
		this.teacherStartTimeStr = teacherStartTimeStr == null ? null : teacherStartTimeStr.trim();
	}

	public String getTeacherEndTimeStr() {
		return teacherEndTimeStr;
	}

	public void setTeacherEndTimeStr(String teacherEndTimeStr) {
		this.teacherEndTimeStr = teacherEndTimeStr == null ? null : teacherEndTimeStr.trim();
	}

	public String getTeacherEndDateStr() {
		return teacherEndDateStr;
	}

	public void setTeacherEndDateStr(String teacherEndDateStr) {
		this.teacherEndDateStr = teacherEndDateStr == null ? null : teacherEndDateStr.trim();
	}

	public String getDeptCycleTime() {
		return deptCycleTime;
	}

	public void setDeptCycleTime(String deptCycleTime) {
		this.deptCycleTime = deptCycleTime == null ? null : deptCycleTime.trim();
	}

	public Integer getTeacherPointState() {
		return teacherPointState;
	}

	public void setTeacherPointState(Integer teacherPointState) {
		this.teacherPointState = teacherPointState;
	}

	public String getWinTitle() {
		return winTitle;
	}

	public void setWinTitle(String winTitle) {
		this.winTitle = winTitle == null ? null : winTitle.trim();
	}

}