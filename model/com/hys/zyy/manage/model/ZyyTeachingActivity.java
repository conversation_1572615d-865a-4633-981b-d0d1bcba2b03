package com.hys.zyy.manage.model;

import java.util.Date;

public class ZyyTeachingActivity extends ZyyBaseObject{
	
	private static final long serialVersionUID = 12651321321225522L;
	/**
	 * id
	 */
	private Long id;
	/**
	 * 医院ID
	 */
	private Long orgId;
	/**
	 * 学科ID
	 */
	private Long baseId;
	/**
	 * 科室ID
	 */
	private Long deptId;
	/**
	 * 活动名称
	 */
	private String activityName;
	/**
	 * 活动类型
	 */
	private Long activityType;
	/**
	 * 开始时间
	 */
	private Date startDate;
	/**
	 * 结束时间
	 */
	private Date endDate;
	/**
	 * 主讲人
	 */
	private String speaker;
	/**
	 * 创建人
	 */
	private Long creater;
	/**
	 * 创建者类型
	 */
	private Long createrType;
	/**
	 * 状态 1有效 -1删除
	 */
	private Long status;
	/**
	 * 活动地点
	 */
	private String activityPlace;
	
	
	//活动上传的附件  一个压缩包
	String activityAtt;
	
	//主讲人 带教id
	private Long speakerUserId; 
	
	private ZyyUserExtendVO zyyUser;
	
	public ZyyTeachingActivity() {
		super();
	}
	public ZyyTeachingActivity(Long id) {
		super();
		this.id = id;
	}
	public String getActivityPlace() {
		return activityPlace;
	}
	public void setActivityPlace(String activityPlace) {
		this.activityPlace = activityPlace;
	}
	public Long getCreaterType() {
		return createrType;
	}
	public void setCreaterType(Long createrType) {
		this.createrType = createrType;
	}
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getOrgId() {
		return orgId;
	}
	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}
	public Long getBaseId() {
		return baseId;
	}
	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}
	public Long getDeptId() {
		return deptId;
	}
	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}
	public String getActivityName() {
		return activityName;
	}
	public void setActivityName(String activityName) {
		this.activityName = activityName;
	}
	public Long getActivityType() {
		return activityType;
	}
	public void setActivityType(Long activityType) {
		this.activityType = activityType;
	}
	public Date getStartDate() {
		return startDate;
	}
	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}
	public Date getEndDate() {
		return endDate;
	}
	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}
	public String getSpeaker() {
		return speaker;
	}
	public void setSpeaker(String speaker) {
		this.speaker = speaker == null ? null : speaker.trim();
	}
	public Long getCreater() {
		return creater;
	}
	public void setCreater(Long creater) {
		this.creater = creater;
	}
	public Long getStatus() {
		return status;
	}
	public void setStatus(Long status) {
		this.status = status;
	}
	public String getActivityAtt() {
		return activityAtt;
	}
	public void setActivityAtt(String activityAtt) {
		this.activityAtt = activityAtt;
	}
	public Long getSpeakerUserId() {
		return speakerUserId;
	}
	public void setSpeakerUserId(Long speakerUserId) {
		this.speakerUserId = speakerUserId;
	}
	public ZyyUserExtendVO getZyyUser() {
		return zyyUser;
	}
	public void setZyyUser(ZyyUserExtendVO zyyUser) {
		this.zyyUser = zyyUser;
	}
	
}
