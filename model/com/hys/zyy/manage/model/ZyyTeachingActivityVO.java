package com.hys.zyy.manage.model;

import java.util.Date;
import java.util.List;

import com.hys.zyy.manage.util.DateUtil;

public class ZyyTeachingActivityVO extends ZyyTeachingActivity {

	/**
	 *
	 */
	private static final long serialVersionUID = 1L;

	private String showSeq;

	/**
	 * 参加学员的数量
	 */
	private Long userCount;
	/**
	 * 参加学员的集合
	 */
	private List<ZyyActivityUserVO> activityUserList;
	/*
	 * 教学活动附件
	 */
	private List<ZyyActivityAttachment> atts;
	/**
	 * @desc 附件数量
	 */
	private Integer attCount;
	/**
	 * 创建者的姓名
	 */
	private String createrName;
	/**
	 * 活动类型名称
	 */
	private String activityTypeName;
	/**
	 * 这两个字符串类型的日期是为了接收页面时传递过来的开始结束时间,因为只精确到分，所以Date类型的变量会报错
	 */
	private String startDateStr;
	private String endDateStr;
	/**
	 * 导入用
	 */
	private String startDateStrDr;
	private String endDateStrDr;
	/**
	 * startDate是否晚于当前时间
	 */
	private Boolean isStartDateAfterCurrentDate;

	/**
	 * 用来接收参加教学活动的学员的id
	 */
	private String studentsIds;
	/**
	 * 创建者类型名称
	 */
	private String createrTypeName;

	private String createrTypeStr;

	private String deleteAttIdStr;

	// ////////////////////////////////////
	// ///////以下是发送邮件用到的字段/////////
	/**
	 * user的email
	 */
	private String userEmail;
	/**
	 * 用户的类型
	 */
	private Integer userType;
	/**
	 * 拼接提示信息
	 */
	private String remindMessage;
	/**
	 * 用户名称
	 */
	private String realName;
	/**
	 * 用户登录名
	 */
	private String accountName;
	/**
	 * 用户绑定微信号后对应的用户唯一标识
	 */
	private String openId;
	/**
	 * 活动人员ID
	 */
	private Long activityUserId;

	private String deptIds;

	// 活动上传的附件 一个压缩包
	String activityAtt;

	/**
	 * 活动状态 1-正在进行 2-已结束 3- 未开始
	 */
	private Integer activityStatus;

	private Long evaluateId;

	private String attendance;

	private String activitySignTwo;

	public String getAttendance() {
		return attendance;
	}

	public void setAttendance(String attendance) {
		this.attendance = attendance;
	}

	public String getDeptIds() {
		return deptIds;
	}

	public void setDeptIds(String deptIds) {
		this.deptIds = deptIds;
	}

	public Long getEvaluateId() {
		return evaluateId;
	}

	public void setEvaluateId(Long evaluateId) {
		this.evaluateId = evaluateId;
	}

	public Integer getActivityStatus() {
		return activityStatus;
	}

	public void setActivityStatus(Integer activityStatus) {
		this.activityStatus = activityStatus;
	}

	public Long getActivityUserId() {
		return activityUserId;
	}

	public void setActivityUserId(Long activityUserId) {
		this.activityUserId = activityUserId;
	}

	public String getRealName() {
		return realName;
	}

	public void setRealName(String realName) {
		this.realName = realName;
	}

	public Integer getUserType() {
		return userType;
	}

	public void setUserType(Integer userType) {
		this.userType = userType;
	}

	public String getRemindMessage() {
		return remindMessage;
	}

	public void setRemindMessage(String remindMessage) {
		this.remindMessage = remindMessage;
	}

	public String getUserEmail() {
		return userEmail;
	}

	public void setUserEmail(String userEmail) {
		this.userEmail = userEmail;
	}

	public String getStartDateStrDr() {
		return startDateStrDr;
	}

	public void setStartDateStrDr(String startDateStrDr) {
		this.startDateStrDr = startDateStrDr == null ? null : startDateStrDr.trim();
	}

	public String getEndDateStrDr() {
		return endDateStrDr;
	}

	public void setEndDateStrDr(String endDateStrDr) {
		this.endDateStrDr = endDateStrDr;
	}

	public String getCreaterTypeName() {
		return createrTypeName;
	}

	public void setCreaterTypeName(String createrTypeName) {
		this.createrTypeName = createrTypeName;
	}

	public String getStudentsIds() {
		return studentsIds;
	}

	public void setStudentsIds(String studentsIds) {
		this.studentsIds = studentsIds;
	}

	public String getStartDateStr() {
		return startDateStr;
	}

	public void setStartDateStr(String startDateStr) {
		Date sDate = DateUtil.parse(startDateStr, DateUtil.FORMAT_MINUTES);
		this.setStartDate(sDate);
		this.startDateStr = startDateStr;
	}

	public String getEndDateStr() {
		return endDateStr;
	}

	public void setEndDateStr(String endDateStr) {
		Date eDate = DateUtil.parse(endDateStr, DateUtil.FORMAT_MINUTES);
		this.setEndDate(eDate);
		this.endDateStr = endDateStr;
	}

	public Long getUserCount() {
		return userCount;
	}

	public void setUserCount(Long userCount) {
		this.userCount = userCount;
	}

	public List<ZyyActivityUserVO> getActivityUserList() {
		return activityUserList;
	}

	public void setActivityUserList(List<ZyyActivityUserVO> activityUserList) {
		this.activityUserList = activityUserList;
	}

	public String getCreaterName() {
		return createrName;
	}

	public void setCreaterName(String createrName) {
		this.createrName = createrName;
	}

	public String getActivityTypeName() {
		return activityTypeName;
	}

	public void setActivityTypeName(String activityTypeName) {
		this.activityTypeName = activityTypeName;
	}

	public Boolean getIsStartDateAfterCurrentDate() {
		return isStartDateAfterCurrentDate;
	}

	public void setIsStartDateAfterCurrentDate(
			Boolean isStartDateAfterCurrentDate) {
		this.isStartDateAfterCurrentDate = isStartDateAfterCurrentDate;
	}

	public String getAccountName() {
		return accountName;
	}

	public void setAccountName(String accountName) {
		this.accountName = accountName;
	}

	public String getOpenId() {
		return openId;
	}

	public void setOpenId(String openId) {
		this.openId = openId;
	}

	public String getActivityAtt() {
		return activityAtt;
	}

	public void setActivityAtt(String activityAtt) {
		this.activityAtt = activityAtt;
	}

	public String getShowSeq() {
		return showSeq;
	}

	public void setShowSeq(String showSeq) {
		this.showSeq = showSeq;
	}

	public String getCreaterTypeStr() {
		return createrTypeStr;
	}

	public void setCreaterTypeStr(String createrTypeStr) {
		this.createrTypeStr = createrTypeStr;
	}

	public List<ZyyActivityAttachment> getAtts() {
		return atts;
	}

	public void setAtts(List<ZyyActivityAttachment> atts) {
		this.atts = atts;
	}

	public String getDeleteAttIdStr() {
		return deleteAttIdStr;
	}

	public void setDeleteAttIdStr(String deleteAttIdStr) {
		this.deleteAttIdStr = deleteAttIdStr;
	}

	public Integer getAttCount() {
		return attCount;
	}

	public void setAttCount(Integer attCount) {
		this.attCount = attCount;
	}

	public String getActivitySignTwo() {
		return activitySignTwo;
	}

	public void setActivitySignTwo(String activitySignTwo) {
		this.activitySignTwo = activitySignTwo;
	}
}
