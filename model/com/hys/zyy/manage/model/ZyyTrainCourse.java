package com.hys.zyy.manage.model;

import java.util.Date;

public class ZyyTrainCourse extends ZyyBaseObject {



	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getCourseName() {
		return courseName;
	}

	public void setCourseName(String courseName) {
		this.courseName = courseName;
	}

	public int getCourseHours() {
		return courseHours;
	}

	public void setCourseHours(int courseHours) {
		this.courseHours = courseHours;
	}

	public Date getOnlineDate() {
		return onlineDate;
	}

	public void setOnlineDate(Date onlineDate) {
		this.onlineDate = onlineDate;
	}

	public Date getOfflineDate() {
		return offlineDate;
	}

	public void setOfflineDate(Date offlineDate) {
		this.offlineDate = offlineDate;
	}

	public Date getCheckDate() {
		return checkDate;
	}

	public void setCheckDate(Date checkDate) {
		this.checkDate = checkDate;
	}

	public ZyyTrainCourseCategory getZyyTrainCourseCategory() {
		return zyyTrainCourseCategory;
	}

	public void setZyyTrainCourseCategory(
			ZyyTrainCourseCategory zyyTrainCourseCategory) {
		this.zyyTrainCourseCategory = zyyTrainCourseCategory;
	}
	public int getPraiseIndex() {
		return praiseIndex;
	}

	public void setPraiseIndex(int praiseIndex) {
		this.praiseIndex = praiseIndex;
	}
	
	private Long id;			//课程ID
	
	private String[] courseId;
 	
	public String[] getCourseId() {
		return courseId;
	}

	public void setCourseId(String[] courseId) {
		this.courseId = courseId;
	}

	private String courseName;	//课程名称
	
	private Long courseCategoryId;	//课程分类Id
	
	private int type;
	
	public int getType() {
		return type;
	}

	public void setType(int type) {
		this.type = type;
	}

	private  int whoStudy;			//学习对象
	

	public Long getCourseCategoryId() {
		return courseCategoryId;
	}

	public void setCourseCategoryId(Long courseCategoryId) {
		this.courseCategoryId = courseCategoryId;
	}

	public int getWhoStudy() {
		return whoStudy;
	}

	public void setWhoStudy(int whoStudy) {
		this.whoStudy = whoStudy;
	}

	private int courseLevel;		//课程级别
	
	public int getCourseLevel() {
		return courseLevel;
	}

	public void setCourseLevel(int courseLevel) {
		this.courseLevel = courseLevel;
	}

	private int courseFormat;		//课程格式
	
	private int isDownload;		//是否允许下载
	
	public int getIsDownload() {
		return isDownload;
	}

	public void setIsDownload(int isDownload) {
		this.isDownload = isDownload;
	}

	private Date downloadStartDate;		//允许下载开始时间
	
	private Date downloadEndDate;		//允许下载结束时间
	
	public Date getDownloadStartDate() {
		return downloadStartDate;
	}

	public void setDownloadStartDate(Date downloadStartDate) {
		this.downloadStartDate = downloadStartDate;
	}

	public Date getDownloadEndDate() {
		return downloadEndDate;
	}

	public void setDownloadEndDate(Date downloadEndDate) {
		this.downloadEndDate = downloadEndDate;
	}

	public int getCourseFormat() {
		return courseFormat;
	}

	public void setCourseFormat(int courseFormat) {
		this.courseFormat = courseFormat;
	}

	private int courseHours;		//课时数
	
	private int courseCredit;		//学分
	
	public int getCourseCredit() {
		return courseCredit;
	}

	public void setCourseCredit(int courseCredit) {
		this.courseCredit = courseCredit;
	}

	private Date onlineDate;	//上线时间
	
	private Date offlineDate;	//下线时间
	
	private String courseRecommend;	//课程推荐
	
	public String getCourseRecommend() {
		return courseRecommend;
	}

	public void setCourseRecommend(String courseRecommend) {
		this.courseRecommend = courseRecommend;
	}

	private Long uploadUserId;		//上传者Id
	
	private ZyyUser user;
	
	public ZyyUser getUser() {
		return user;
	}

	public void setUser(ZyyUser user) {
		this.user = user;
	}

	public Long getUploadUserId() {
		return uploadUserId;
	}

	public void setUploadUserId(Long uploadUserId) {
		this.uploadUserId = uploadUserId;
	}

	private int clicks;		//点击次数
	
	public void setClicks(int clicks) {
		this.clicks = clicks;
	}

	private int praiseIndex;	//好评指数
	

	private int courseStatus;		//课程状态
	
	private int checkStatus;		//审核状态
	
	private Integer[] files;		//文件上传
	
	private int hiddenType;			//区分线上和线下
	
	public int getHiddenType() {
		return hiddenType;
	}

	public void setHiddenType(int hiddenType) {
		this.hiddenType = hiddenType;
	}

	public Integer[] getFiles() {
		return files;
	}

	public void setFiles(Integer[] files) {
		this.files = files;
	}

	public int getCourseStatus() {
		return courseStatus;
	}

	public void setCourseStatus(int courseStatus) {
		this.courseStatus = courseStatus;
	}

	public int getClicks() {
		return clicks;
	}

	public int getCheckStatus() {
		return checkStatus;
	}

	public void setCheckStatus(int checkStatus) {
		this.checkStatus = checkStatus;
	}

	private Date checkDate;		//审核时间
	
	private ZyyTrainCourseCategory zyyTrainCourseCategory; //类别对象

	
}
