package com.hys.zyy.manage.model;

import java.util.Date;
import java.util.List;

/**
 * 标题：住院医师
 * 
 * 作者： 崔伟达 2012-7-24 上午09:50:00
 * 
 * 描述：培训课程分类表
 * 
 * 说明：实体类
 * 
 */

public class ZyyTrainCourseCategory extends ZyyBaseObject {
	/**
	 * 
	 */
	private static final long serialVersionUID = 471331952828213309L;
	//主键id 自动增长唯一标识符
	private Long id;
	//课程分类名称
	private String courseCategoryName;
	//课程分类级别1代表一级 2 代表2级
	private int courseCategoryLevel;
	//课程分类父类id 一级为0
	private Long parentId;
	//状态 标识是否已删除 1正常 2 删除
	private int status;
	//课程分类类型：1.卫生厅 2.卫生局 3.大学 4.医院 5.基地 6.科室
	private int type;
	//机构id
	private Long zyyOrgId;
	//基地id
	private Long zyyBaseId;
	//科室id
	private Long zyyDeptId;
	//用户id
	private Long zyyUserId;
	//最后更改用户id
	private Long lastUserId;
	//最后修改时间
	private Date lastUpdateDate;
	//用于显示时 判断用字段 数据库中没有该字段
	private int rowSpan;
	private int courseRowSpan;

	//同上
	private int isCurrent;
	//封装自身一个集合用于显示2级分类
	private List<ZyyTrainCourseCategory> courseCategoryList;
	//对应的课程集合
	private List<ZyyTrainCourse> courseList;
	public List<ZyyTrainCourse> getCourseList() {
		return courseList;
	}
	
	public int getCourseRowSpan() {
		return courseRowSpan;
	}

	public void setCourseRowSpan(int courseRowSpan) {
		this.courseRowSpan = courseRowSpan;
	}
	
	public void setCourseList(List<ZyyTrainCourse> courseList) {
		this.courseList = courseList;
	}

	public int getIsCurrent() {
		return isCurrent;
	}

	public void setIsCurrent(int isCurrent) {
		this.isCurrent = isCurrent;
	}

	public List<ZyyTrainCourseCategory> getCourseCategoryList() {
		return courseCategoryList;
	}

	public void setCourseCategoryList(
			List<ZyyTrainCourseCategory> courseCategoryList) {
		this.courseCategoryList = courseCategoryList;
	}
	

	public int getRowSpan() {
		return rowSpan;
	}

	public void setRowSpan(int rowSpan) {
		this.rowSpan = rowSpan;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getCourseCategoryName() {
		return courseCategoryName;
	}

	public void setCourseCategoryName(String courseCategoryName) {
		this.courseCategoryName = courseCategoryName;
	}

	public int getCourseCategoryLevel() {
		return courseCategoryLevel;
	}

	public void setCourseCategoryLevel(int courseCategoryLevel) {
		this.courseCategoryLevel = courseCategoryLevel;
	}

	public Long getParentId() {
		return parentId;
	}

	public void setParentId(Long parentId) {
		this.parentId = parentId;
	}

	public int getStatus() {
		return status;
	}

	public void setStatus(int status) {
		this.status = status;
	}

	public int getType() {
		return type;
	}

	public void setType(int type) {
		this.type = type;
	}

	public Long getZyyOrgId() {
		return zyyOrgId;
	}

	public void setZyyOrgId(Long zyyOrgId) {
		this.zyyOrgId = zyyOrgId;
	}

	public Long getZyyBaseId() {
		return zyyBaseId;
	}

	public void setZyyBaseId(Long zyyBaseId) {
		this.zyyBaseId = zyyBaseId;
	}

	public Long getZyyDeptId() {
		return zyyDeptId;
	}

	public void setZyyDeptId(Long zyyDeptId) {
		this.zyyDeptId = zyyDeptId;
	}

	public Long getZyyUserId() {
		return zyyUserId;
	}

	public void setZyyUserId(Long zyyUserId) {
		this.zyyUserId = zyyUserId;
	}

	public Long getLastUserId() {
		return lastUserId;
	}

	public void setLastUserId(Long lastUserId) {
		this.lastUserId = lastUserId;
	}

	public Date getLastUpdateDate() {
		return lastUpdateDate;
	}

	public void setLastUpdateDate(Date lastUpdateDate) {
		this.lastUpdateDate = lastUpdateDate;
	}
}
