package com.hys.zyy.manage.model;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import com.hys.zyy.manage.util.StringPool;
import com.hys.zyy.manage.util.annotation.Column;
import com.hys.zyy.manage.util.annotation.Id;
import com.hys.zyy.manage.util.annotation.Table;


@Table("ZYY_TRAIN_DISEASE")
public class ZyyTrainDisease extends ZyyBaseObject {
	
	private static final long serialVersionUID = 5454155825314635342L;
	
	//alias
	public static final String TABLE_ALIAS = "ZyyTrainDisease";
	public static final String ALIAS_ID = "id";
	public static final String ALIAS_DISEASE_NAME = "diseaseName";
	public static final String ALIAS_DISEASE_TYPE = "diseaseType";	// 1 -疾病 2 -技能 3 -其它
	public static final String ALIAS_DEPT_STD_ID = "deptStdId";
	
	@Id("ZYY_TRAIN_DISEASE_SEQ.nextval")
	@Column("ID")
	private java.lang.Long id;
	@Column("DISEASE_NAME")
	private java.lang.String diseaseName;
	@Column("DISEASE_TYPE")
	private Integer diseaseType;
	@Column("DEPT_STD_ID")
	private java.lang.Long deptStdId;
	@Column("ZYY_ORG_ID")
	private java.lang.Long zyyOrgId;
	@Column("STATUS")
	private java.lang.Integer status;
	
	private ZyyDeptStd zyyDeptStd;

	private String requireAmount = StringPool.BLANK;
	private Integer realyAmount;
	
	private String requireSymbol = StringPool.BLANK;
	
	public ZyyTrainDisease(){
	}

	public ZyyTrainDisease(
		java.lang.Long deptStdId
	){
		this.deptStdId = deptStdId;
	}

	public void setId(java.lang.Long value) {
		this.id = value;
	}
	
	public java.lang.Long getId() {
		return this.id;
	}
	public void setDiseaseName(java.lang.String value) {
		this.diseaseName = value;
	}
	
	public java.lang.String getDiseaseName() {
		return this.diseaseName;
	}
	public void setDiseaseType(Integer value) {
		this.diseaseType = value;
	}
	
	public Integer getDiseaseType() {
		return this.diseaseType;
	}
	public void setDeptStdId(java.lang.Long value) {
		this.deptStdId = value;
	}
	
	public java.lang.Long getDeptStdId() {
		return this.deptStdId;
	}
	
	public void setZyyDeptStd(ZyyDeptStd zyyDeptStd){
		this.zyyDeptStd = zyyDeptStd;
	}
	
	public ZyyDeptStd getZyyDeptStd() {
		return zyyDeptStd;
	}

	public String toString() {
		return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
			.append("Id",getId())
			.append("DiseaseName",getDiseaseName())
			.append("DiseaseType",getDiseaseType())
			.append("DeptStdId",getDeptStdId())
			.append("requireSymbol",getRequireSymbol())
			.toString();
	}
	
	public int hashCode() {
		return new HashCodeBuilder()
			.append(getId())
			.toHashCode();
	}
	
	public boolean equals(Object obj) {
		if(obj instanceof ZyyTrainDisease == false) return false;
		if(this == obj) return true;
		ZyyTrainDisease other = (ZyyTrainDisease)obj;
		return new EqualsBuilder()
			.append(getId(),other.getId())
			.isEquals();
	}

	public java.lang.Long getZyyOrgId() {
		return zyyOrgId;
	}

	public void setZyyOrgId(java.lang.Long zyyOrgId) {
		this.zyyOrgId = zyyOrgId;
	}

	public String getRequireAmount() {
		return requireAmount;
	}

	public void setRequireAmount(String requireAmount) {
		this.requireAmount = requireAmount;
	}

	public String getRequireSymbol() {
		return requireSymbol;
	}

	public void setRequireSymbol(String requireSymbol) {
		this.requireSymbol = requireSymbol;
	}

	public Integer getRealyAmount() {
		return realyAmount;
	}

	public void setRealyAmount(Integer realyAmount) {
		this.realyAmount = realyAmount;
	}

	public java.lang.Integer getStatus() {
		return status;
	}

	public void setStatus(java.lang.Integer status) {
		this.status = status;
	}




	
}

