package com.hys.zyy.manage.model;

import java.util.Date;
import java.util.List;

/**
 * 师承老师
 */
public class ZyyTutor extends ZyyUserExtendVO{

	private static final long serialVersionUID = 3033609653448388021L;

	private Long id; //主键ID
	
	private Long orgId; //机构ID
	private String orgName;
	
	private Long userId; //老师ID
	
	private Long baseId; //学科ID
	
	private Date createDate; //创建日期
	
	private Integer status; //状态
	
	// 计划招录人数
	private Long recruitingNumber;
	
	// 学员人数
	private Long studentNumber;
	
	private String deptName;
	
	// 临床带教
	private Integer clinicalTeaching;
	
	// 脱产教学
	private Integer fulltimeTeaching;
	
	// 带入取人数
	private Long admittedNumber;
	
	// 工作经历
	private List<ZyyTutorWorkExperience> experience;
	
	// 获得证书
	private List<ZyyTutorCertificate> certificate;
	
	// 师承老师学科
	private List<ZyyBaseUser> zyyBaseUser;
	
	// 专业方向id组成的字符串
	private String baseIdStr;
	
	public String getBaseIdStr() {
		return baseIdStr;
	}

	public void setBaseIdStr(String baseIdStr) {
		this.baseIdStr = baseIdStr;
	}

	public Long getAdmittedNumber() {
		return admittedNumber;
	}

	public void setAdmittedNumber(Long admittedNumber) {
		this.admittedNumber = admittedNumber;
	}
	
	public Integer getClinicalTeaching() {
		return clinicalTeaching;
	}

	public void setClinicalTeaching(Integer clinicalTeaching) {
		this.clinicalTeaching = clinicalTeaching;
	}

	public Integer getFulltimeTeaching() {
		return fulltimeTeaching;
	}

	public void setFulltimeTeaching(Integer fulltimeTeaching) {
		this.fulltimeTeaching = fulltimeTeaching;
	}

	public String getDeptName() {
		return deptName;
	}

	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}
	
	public List<ZyyBaseUser> getZyyBaseUser() {
		return zyyBaseUser;
	}

	public void setZyyBaseUser(List<ZyyBaseUser> zyyBaseUser) {
		this.zyyBaseUser = zyyBaseUser;
	}

	public List<ZyyTutorCertificate> getCertificate() {
		return certificate;
	}

	public void setCertificate(List<ZyyTutorCertificate> certificate) {
		this.certificate = certificate;
	}

	public List<ZyyTutorWorkExperience> getExperience() {
		return experience;
	}

	public void setExperience(List<ZyyTutorWorkExperience> experience) {
		this.experience = experience;
	}

	public Long getRecruitingNumber() {
		return recruitingNumber;
	}

	public void setRecruitingNumber(Long recruitingNumber) {
		this.recruitingNumber = recruitingNumber;
	}

	public Long getStudentNumber() {
		return studentNumber;
	}

	public void setStudentNumber(Long studentNumber) {
		this.studentNumber = studentNumber;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getOrgId() {
		return orgId;
	}

	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName == null ? null : orgName.trim();
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public Long getBaseId() {
		return baseId;
	}

	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}
	
}
