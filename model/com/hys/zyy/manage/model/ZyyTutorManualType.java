package com.hys.zyy.manage.model;

import java.util.Date;

/**
 * 
 * 机构师承手册类别
 *
 */
public class ZyyTutorManualType extends ZyyBaseObject{

	private static final long serialVersionUID = 495965568833870040L;
	
	private Long id; //主键ID
	
	private Long orgId; //机构ID
	
	private String manualName; //手册名称
	
	private Integer manualSeq; //手册排序
	
	private Date createDate; // 创建日期
	
	private Integer status; //状态
	
	private Long manualType;

	public Long getManualType() {
		return manualType;
	}

	public void setManualType(Long manualType) {
		this.manualType = manualType;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getOrgId() {
		return orgId;
	}

	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}

	public String getManualName() {
		return manualName;
	}

	public void setManualName(String manualName) {
		this.manualName = manualName;
	}

	public Integer getManualSeq() {
		return manualSeq;
	}

	public void setManualSeq(Integer manualSeq) {
		this.manualSeq = manualSeq;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

}
