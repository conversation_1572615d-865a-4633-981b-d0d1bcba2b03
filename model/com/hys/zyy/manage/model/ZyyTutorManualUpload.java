package com.hys.zyy.manage.model;

import java.util.Date;

/**
 * 
 * 师承手册上传记录
 *
 */
public class ZyyTutorManualUpload extends ZyyBaseObject{

	private static final long serialVersionUID = 5528981625692399191L;

	private Long id; //主键ID
	
	private Long userId; //用户ID
	
	private Long manualId; //手册ID
	
	private String fileName; //文件名称
	
	private String filePath; //文件路径
	
	private Date commitDate; //提交日期
	
	private Integer checkStatus; // 审核状态
	
	private Date checkDate; //审核日期
	
	private String checkRemark;//审核备注
	
	private Integer status; //状态

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public Long getManualId() {
		return manualId;
	}

	public void setManualId(Long manualId) {
		this.manualId = manualId;
	}

	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	public String getFilePath() {
		return filePath;
	}

	public void setFilePath(String filePath) {
		this.filePath = filePath;
	}

	public Date getCommitDate() {
		return commitDate;
	}

	public void setCommitDate(Date commitDate) {
		this.commitDate = commitDate;
	}

	public Integer getCheckStatus() {
		return checkStatus;
	}

	public void setCheckStatus(Integer checkStatus) {
		this.checkStatus = checkStatus;
	}

	public Date getCheckDate() {
		return checkDate;
	}

	public void setCheckDate(Date checkDate) {
		this.checkDate = checkDate;
	}

	public String getCheckRemark() {
		return checkRemark;
	}

	public void setCheckRemark(String checkRemark) {
		this.checkRemark = checkRemark;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}
		
}
