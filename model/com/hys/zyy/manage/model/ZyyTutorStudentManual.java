package com.hys.zyy.manage.model;

import java.util.Date;

/**
 * 
 * 学员师承手册表
 *
 */
public class ZyyTutorStudentManual extends ZyyBaseObject{

	private static final long serialVersionUID = 6095213195403120254L;

	private Long id; //主键ID
	
	private Long userId; //用户ID
	
	private String manualName; //手册名称
	
	private Long manualType; //手册类别
	
	private Date createDate; //创建日期
	
	private Date updateDate; //更新日期
	
	private Integer status; //状态

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public String getManualName() {
		return manualName;
	}

	public void setManualName(String manualName) {
		this.manualName = manualName;
	}

	public Long getManualType() {
		return manualType;
	}

	public void setManualType(Long manualType) {
		this.manualType = manualType;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Date getUpdateDate() {
		return updateDate;
	}

	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}
	
}
