package com.hys.zyy.manage.model;

import java.util.Date;

public class ZyyTutorStudentManualVO extends ZyyTutorStudentManual {

	private static final long serialVersionUID = -5660539265703779218L;

	private String fileName;
	
	private String filePath;
	
	private Integer checkStatus;
	
	private Date commitDate;
	
	private Long uploadManualId;
	
	private Integer pageOffsetNum;
	
	private String studentName;
	
	private String manualTypeName;
	
	public String getStudentName() {
		return studentName;
	}

	public void setStudentName(String studentName) {
		this.studentName = studentName;
	}

	public Integer getPageOffsetNum() {
		return pageOffsetNum;
	}

	public void setPageOffsetNum(Integer pageOffsetNum) {
		this.pageOffsetNum = pageOffsetNum;
	}

	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	public String getFilePath() {
		return filePath;
	}

	public void setFilePath(String filePath) {
		this.filePath = filePath;
	}

	public Integer getCheckStatus() {
		return checkStatus;
	}

	public void setCheckStatus(Integer checkStatus) {
		this.checkStatus = checkStatus;
	}

	public Date getCommitDate() {
		return commitDate;
	}

	public void setCommitDate(Date commitDate) {
		this.commitDate = commitDate;
	}

	public Long getUploadManualId() {
		return uploadManualId;
	}

	public void setUploadManualId(Long uploadManualId) {
		this.uploadManualId = uploadManualId;
	}

	public String getManualTypeName() {
		return manualTypeName;
	}

	public void setManualTypeName(String manualTypeName) {
		this.manualTypeName = manualTypeName;
	}
	
}
