package com.hys.zyy.manage.model;

public class ZyyTutorStudentVO extends ZyyTutorStudent{

	
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	private String teacherName; //录取老师姓名
	
	private String baseName; //学科
	
	private String degree; //学历
	
	private Integer professionalTitle; //职称
	
	private Integer teacherCheckStatus; //老师审核结果
	
	private Integer orgCheckStatus; //医院审核结果
	
	private Long teacherUserId; //导师用户ID
	
	private Long recruitingNumber; // 计划招录人数
	
	private Long remainAdmittedNumber; //剩余录取人数
	
	private Long rejectedNumber; //已报名未录取人数
	
	public String getTeacherName() {
		return teacherName;
	}

	public void setTeacherName(String teacherName) {
		this.teacherName = teacherName;
	}

	public String getBaseName() {
		return baseName;
	}

	public void setBaseName(String baseName) {
		this.baseName = baseName;
	}

	public String getDegree() {
		return degree;
	}

	public void setDegree(String degree) {
		this.degree = degree;
	}

	public Integer getProfessionalTitle() {
		return professionalTitle;
	}

	public void setProfessionalTitle(Integer professionalTitle) {
		this.professionalTitle = professionalTitle;
	}

	public Integer getTeacherCheckStatus() {
		return teacherCheckStatus;
	}

	public void setTeacherCheckStatus(Integer teacherCheckStatus) {
		this.teacherCheckStatus = teacherCheckStatus;
	}

	public Integer getOrgCheckStatus() {
		return orgCheckStatus;
	}

	public void setOrgCheckStatus(Integer orgCheckStatus) {
		this.orgCheckStatus = orgCheckStatus;
	}

	public Long getTeacherUserId() {
		return teacherUserId;
	}

	public void setTeacherUserId(Long teacherUserId) {
		this.teacherUserId = teacherUserId;
	}

	public Long getRecruitingNumber() {
		return recruitingNumber;
	}

	public void setRecruitingNumber(Long recruitingNumber) {
		this.recruitingNumber = recruitingNumber;
	}

	public Long getRemainAdmittedNumber() {
		return remainAdmittedNumber;
	}

	public void setRemainAdmittedNumber(Long remainAdmittedNumber) {
		this.remainAdmittedNumber = remainAdmittedNumber;
	}

	public Long getRejectedNumber() {
		return rejectedNumber;
	}

	public void setRejectedNumber(Long rejectedNumber) {
		this.rejectedNumber = rejectedNumber;
	}	
}
