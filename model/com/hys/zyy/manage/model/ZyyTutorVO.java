package com.hys.zyy.manage.model;

public class ZyyTutorVO extends ZyyTutor {

	private static final long serialVersionUID = 1L;

	private Long studentID;  //学员的id
	
	private String teacherName; //导师的名称

	private Integer teacherCheckStatus; //老师审核结果
	
	private Integer orgCheckStatus; //医院审核结果

	private Long tutorStudentAspirationId; //学员填报导师志愿表的id
	
	private String joinTeacherRealName; //录取的导师的姓名
	
	private Long joinTeacherId;  //录取导师的id
	
	private String aspirationTeacherName;  //志愿导师的姓名
	
	private Long aspirationTeacherId;  //志愿导师的id
	
	private Integer chooseTutor; //是否选择导师
	
	public Long getJoinTeacherId() {
		return joinTeacherId;
	}

	public void setJoinTeacherId(Long joinTeacherId) {
		this.joinTeacherId = joinTeacherId;
	}

	public String getAspirationTeacherName() {
		return aspirationTeacherName;
	}

	public void setAspirationTeacherName(String aspirationTeacherName) {
		this.aspirationTeacherName = aspirationTeacherName;
	}

	public Long getAspirationTeacherId() {
		return aspirationTeacherId;
	}

	public void setAspirationTeacherId(Long aspirationTeacherId) {
		this.aspirationTeacherId = aspirationTeacherId;
	}

	public String getJoinTeacherRealName() {
		return joinTeacherRealName;
	}

	public void setJoinTeacherRealName(String joinTeacherRealName) {
		this.joinTeacherRealName = joinTeacherRealName;
	}

	public Long getTutorStudentAspirationId() {
		return tutorStudentAspirationId;
	}

	public void setTutorStudentAspirationId(Long tutorStudentAspirationId) {
		this.tutorStudentAspirationId = tutorStudentAspirationId;
	}

	public Long getStudentID() {
		return studentID;
	}

	public void setStudentID(Long studentID) {
		this.studentID = studentID;
	}

	public String getTeacherName() {
		return teacherName;
	}

	public void setTeacherName(String teacherName) {
		this.teacherName = teacherName;
	}

	public Integer getTeacherCheckStatus() {
		return teacherCheckStatus;
	}

	public void setTeacherCheckStatus(Integer teacherCheckStatus) {
		this.teacherCheckStatus = teacherCheckStatus;
	}

	public Integer getOrgCheckStatus() {
		return orgCheckStatus;
	}

	public void setOrgCheckStatus(Integer orgCheckStatus) {
		this.orgCheckStatus = orgCheckStatus;
	}

	public Integer getChooseTutor() {
		return chooseTutor;
	}

	public void setChooseTutor(Integer chooseTutor) {
		this.chooseTutor = chooseTutor;
	}
	
}
