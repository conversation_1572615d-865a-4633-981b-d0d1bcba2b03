package com.hys.zyy.manage.model;

import java.util.Date;

/**
 * 
 * 师承老师工作经历
 *
 */
public class ZyyTutorWorkExperience extends ZyyBaseObject{

	private static final long serialVersionUID = 1618360316869728027L;

	private Long id; //主键ID
	
	private Long userId; //用户ID
	
	private String workUnit; //工作单位
	
	private String workDate; //工作日期
	
	private String workTitle; //职称
	
	private Date createDate; //创建日期
	
	private String baseName;  //科室名称
	
	private String status; // 状态 删除 添加 修改
	
	private Date startDate;  //工作开始时间
	
	private Date endDate;  //工作结束时间
	
	private String duty;  //职务
	
	
	public String getDuty() {
		return duty;
	}

	public void setDuty(String duty) {
		this.duty = duty;
	}

	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public String getWorkUnit() {
		return workUnit;
	}

	public void setWorkUnit(String workUnit) {
		this.workUnit = workUnit;
	}

	public String getWorkDate() {
		return workDate;
	}

	public void setWorkDate(String workDate) {
		this.workDate = workDate;
	}

	public String getWorkTitle() {
		return workTitle;
	}

	public void setWorkTitle(String workTitle) {
		this.workTitle = workTitle;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public String getBaseName() {
		return baseName;
	}

	public void setBaseName(String baseName) {
		this.baseName = baseName;
	}

}
