package com.hys.zyy.manage.model;

import java.util.Date;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：李海龙 May 4, 2012
 * 
 * 描述：
 * 
 * 说明:
 */
public class ZyyUploadAttendSet extends ZyyBaseObject {

	/**
	 * 
	 */
	private static final long serialVersionUID = -43924650593030712L;

	private Long id;// 标识
	private Long zyyOrgId;// 机构ID
	private Integer expiryDate;// 截止日期
	private Integer noticeExpiryDate;// 截止日期通知  1是 0否
	private Integer noticeMonthEnd;// 月末通知  1是 0否
	private Integer noticeEmail;// 邮件通知  1是 0否
	private Integer noticeMsg;// 消息通知  1是 0否
	private Integer noticePhoneMsg;// 短信通知  1是 0否
	private Integer noticePubNo;// 公众号通知  1是 0否
	private Integer status;// 使用状态  1是 0否
	private Date createDate;// 创建时间
	private Date lastUpdateDate;// 更新时间
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getZyyOrgId() {
		return zyyOrgId;
	}
	public void setZyyOrgId(Long zyyOrgId) {
		this.zyyOrgId = zyyOrgId;
	}
	public Integer getExpiryDate() {
		return expiryDate;
	}
	public void setExpiryDate(Integer expiryDate) {
		this.expiryDate = expiryDate;
	}
	public Integer getNoticeExpiryDate() {
		return noticeExpiryDate;
	}
	public void setNoticeExpiryDate(Integer noticeExpiryDate) {
		this.noticeExpiryDate = noticeExpiryDate;
	}
	public Integer getNoticeMonthEnd() {
		return noticeMonthEnd;
	}
	public void setNoticeMonthEnd(Integer noticeMonthEnd) {
		this.noticeMonthEnd = noticeMonthEnd;
	}
	public Integer getNoticeEmail() {
		return noticeEmail;
	}
	public void setNoticeEmail(Integer noticeEmail) {
		this.noticeEmail = noticeEmail;
	}
	public Integer getNoticeMsg() {
		return noticeMsg;
	}
	public void setNoticeMsg(Integer noticeMsg) {
		this.noticeMsg = noticeMsg;
	}
	public Integer getNoticePhoneMsg() {
		return noticePhoneMsg;
	}
	public void setNoticePhoneMsg(Integer noticePhoneMsg) {
		this.noticePhoneMsg = noticePhoneMsg;
	}
	public Integer getNoticePubNo() {
		return noticePubNo;
	}
	public void setNoticePubNo(Integer noticePubNo) {
		this.noticePubNo = noticePubNo;
	}
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	public Date getCreateDate() {
		return createDate;
	}
	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}
	public Date getLastUpdateDate() {
		return lastUpdateDate;
	}
	public void setLastUpdateDate(Date lastUpdateDate) {
		this.lastUpdateDate = lastUpdateDate;
	}
	
}
