package com.hys.zyy.manage.model;

import java.util.Date;

import com.alibaba.fastjson.annotation.JSONField;
import com.hys.security.util.SecurityUtils;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：陈明凯 2012-03-19
 * 
 * 描述：住院医师用户表
 * 
 * 说明:
 */
public class ZyyUser extends ZyyBaseObject {

	private static final long serialVersionUID = -3196086218985753113L;

	/**
	 * 主键ID
	 */
	private Long id;

	/**
	 * 用户类别
	 */
	private Integer zyyUserType;

	/**
	 * 用户组织机构ID
	 */
	private Long zyyUserOrgId;

	/**
	 * 用户组织机构ID(省厅)
	 */
	private Long zyyUserProvinceId;

	/**
	 * 基础用户ID
	 */
	private String baseUserId;

	/**
	 * 真实姓名
	 */
	private String realName;

	/**
	 * 用户状态
	 */
	private Integer zyyUserStatus;

	/**
	 * 上次登录时间
	 */
	private Date lastLoginDate;

	/**
	 * 用户名称
	 */
	private String accountName;
	
	/**
	 * 最后更新状态时间
	 */
	private Date lastUpdateStatusDate ;
	
	//用户所在机构类型
	private Integer hospType;
	
	private String createrTypeStr;
	/*
	 * 学员录取编号
	 */
	@JSONField(serialize = false)
	private String recruitCode;
	@JSONField(serialize = false)
	private Long[] deptIdArr;
	/*
	 * 用户来源（1=系统生成；2=师资系统导入）
	 */
	private Integer zyyUserSource;
	/*
	 * 人员分类
	 */
	private Integer userCategory;
	
	private Integer[] userCategorys;
	/*
	 * 手册标准ID
	 */
	private Long manualStdId;
	
	public ZyyUser() {
		super();
	}

	public ZyyUser(Long zyyUserProvinceId) {
		super();
		this.zyyUserProvinceId = zyyUserProvinceId;
	}

	public ZyyUser(Long id, String recruitCode) {
		super();
		this.id = id;
		this.recruitCode = recruitCode;
	}

	public String getAccountName() {
		return accountName;
	}

	public void setAccountName(String accountName) {
		this.accountName = accountName;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Integer getZyyUserType() {
		return zyyUserType;
	}

	public void setZyyUserType(Integer zyyUserType) {
		this.zyyUserType = zyyUserType;
	}

	public Long getZyyUserOrgId() {
		return zyyUserOrgId;
	}

	public void setZyyUserOrgId(Long zyyUserOrgId) {
		this.zyyUserOrgId = zyyUserOrgId;
	}

	public Long getZyyUserProvinceId() {
		return zyyUserProvinceId;
	}

	public void setZyyUserProvinceId(Long zyyUserProvinceId) {
		this.zyyUserProvinceId = zyyUserProvinceId;
	}

	public String getBaseUserId() {
		return baseUserId;
	}

	public void setBaseUserId(String baseUserId) {
		this.baseUserId = baseUserId;
	}

	public String getRealName() {
		return realName;
	}

	public void setRealName(String realName) {
		this.realName = realName;
	}

	public Integer getZyyUserStatus() {
		return zyyUserStatus;
	}

	public void setZyyUserStatus(Integer zyyUserStatus) {
		this.zyyUserStatus = zyyUserStatus;
	}

	public Date getLastLoginDate() {
		return lastLoginDate;
	}

	public void setLastLoginDate(Date lastLoginDate) {
		this.lastLoginDate = lastLoginDate;
	}

	public Date getLastUpdateStatusDate() {
		return lastUpdateStatusDate;
	}

	public void setLastUpdateStatusDate(Date lastUpdateStatusDate) {
		this.lastUpdateStatusDate = lastUpdateStatusDate;
	}

	public Integer getHospType() {
		return hospType;
	}

	public void setHospType(Integer hospType) {
		this.hospType = hospType;
	}
	
	public String getCreaterTypeStr() {
		return createrTypeStr;
	}

	public void setCreaterTypeStr(String createrTypeStr) {
		this.createrTypeStr = createrTypeStr;
	}

	public String getRecruitCode() {
		return recruitCode;
	}

	public void setRecruitCode(String recruitCode) {
		this.recruitCode = recruitCode == null ? null : recruitCode.trim();
	}

	public Long[] getDeptIdArr() {
		return deptIdArr;
	}

	public void setDeptIdArr(Long[] deptIdArr) {
		this.deptIdArr = deptIdArr;
	}

	public Integer getZyyUserSource() {
		return zyyUserSource;
	}

	public void setZyyUserSource(Integer zyyUserSource) {
		this.zyyUserSource = zyyUserSource;
	}

	public Integer getUserCategory() {
		return userCategory;
	}

	public void setUserCategory(Integer userCategory) {
		this.userCategory = userCategory;
	}

	public Integer[] getUserCategorys() {
		return userCategorys;
	}

	public void setUserCategorys(Integer[] userCategorys) {
		this.userCategorys = userCategorys;
	}

	public Long getManualStdId() {
		return manualStdId;
	}

	public void setManualStdId(Long manualStdId) {
		this.manualStdId = manualStdId;
	}

	/**
	 * 是否省厅,以及省厅管理机构
	 * @return
	 */
	public boolean isProvinceUser() {
		return SecurityUtils.hasProvincePrivilege(this);
	}
	
	/**
	 * 大学级别的用户
	 */
	public boolean isUniversityUser(){
		return SecurityUtils.hasUniversityPrivilege(this);
	}
	
	/**
	 * 市级管理机构（卫生局）用户权限
	 */
	public boolean isBureauUser(){
		return SecurityUtils.hasBureauPrivilege(this);
	}
	
	/**
	 * 是否医院级别用户
	 * @return
	 */
	public boolean isHospitalUser() {
		return SecurityUtils.hasHospitalPrivilege(this);
	}
	
	/**
	 * 是否专业基地级别用户
	 * @return
	 */
	public boolean isBaseUser() {
		return SecurityUtils.hasBasePrivilege(this);
	}
	
	/**
	 * 是否科室级别用户
	 * @return
	 */
	public boolean isDeptUser() {
		return SecurityUtils.hasDepartmentPrivilege(this);
	}
	
	/**
	 * 是否导师级别用户
	 * @return
	 */
	public boolean isTutorUser(){
		return SecurityUtils.hasTutorPrivilege(this);
	}
	
	/**
	 * 是否带教用户
	 * @return
	 */
	public boolean isTeacherUser() {
		return SecurityUtils.hasTeacherPrivilege(this);
	}
	
	/**
	 * 是否住院医师级别用户
	 * @return
	 */
	public boolean isResidencyUser() {
		return SecurityUtils.hasStudentPrivilege(this);
	}
	
}