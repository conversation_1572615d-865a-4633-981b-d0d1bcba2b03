package com.hys.zyy.manage.model;

import java.io.Serializable;

/**
 * 人员分类表
 */
public class ZyyUserCategory implements Serializable {
	private static final long serialVersionUID = 1L;
	/*
	 * 人员ID
	 */
	private Long zyyUserId;
	/*
	 * 分类
	 */
	private Integer category;

	public ZyyUserCategory() {
		super();
	}

	public ZyyUserCategory(Long zyyUserId) {
		super();
		this.zyyUserId = zyyUserId;
	}

	public ZyyUserCategory(Integer category) {
		super();
		this.category = category;
	}

	public ZyyUserCategory(Long zyyUserId, Integer category) {
		super();
		this.zyyUserId = zyyUserId;
		this.category = category;
	}

	public Long getZyyUserId() {
		return zyyUserId;
	}

	public void setZyyUserId(Long zyyUserId) {
		this.zyyUserId = zyyUserId;
	}

	public Integer getCategory() {
		return category;
	}

	public void setCategory(Integer category) {
		this.category = category;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((category == null) ? 0 : category.hashCode());
		result = prime * result + ((zyyUserId == null) ? 0 : zyyUserId.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ZyyUserCategory other = (ZyyUserCategory) obj;
		if (category == null) {
			if (other.category != null)
				return false;
		} else if (!category.equals(other.category))
			return false;
		if (zyyUserId == null) {
			if (other.zyyUserId != null)
				return false;
		} else if (!zyyUserId.equals(other.zyyUserId))
			return false;
		return true;
	}

	@Override
	public String toString() {
		return "ZyyUserCategory [zyyUserId=" + zyyUserId + ", category=" + category + "]";
	}

}