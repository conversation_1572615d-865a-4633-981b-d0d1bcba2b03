package com.hys.zyy.manage.model;

import java.util.Date;

import org.springframework.web.multipart.MultipartFile;

/**
 * 用户的个人证件pojo类
 * <AUTHOR>
 *
 */
public class ZyyUserCertificate  extends ZyyBaseObject {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	private Long id;
	
	private Long userId;  //用户的id
	
	private Date gainDate;  //证书获得的时间
	
	private String certificateName;   //证书的名称
	
	private String filePath;    //证书附件保存的路径
	
	private String fileName;   //证书附件的名称

	private Integer status;    //状态  1正常  0删除 (数据库的数据只有 1 和 -1 lzq )

	private Date updateTime;   //更新的时间
	
	private MultipartFile certificateFile; //证件照片的类
	
	//证书编号
	private String certNumber;
	//证书类型  1 本单位 2 省级 3 国家级 4 其它
	private Integer certType;
	
	public String getFileName() {
		return fileName;
	}
	
	public void setFileName(String fileName) {
		this.fileName = fileName;
	}
	
	
	public MultipartFile getCertificateFile() {
		return certificateFile;
	}

	public void setCertificateFile(MultipartFile certificateFile) {
		this.certificateFile = certificateFile;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public Date getGainDate() {
		return gainDate;
	}

	public void setGainDate(Date gainDate) {
		this.gainDate = gainDate;
	}

	public String getCertificateName() {
		return certificateName;
	}

	public void setCertificateName(String certificateName) {
		this.certificateName = certificateName;
	}

	public String getFilePath() {
		return filePath;
	}

	public void setFilePath(String filePath) {
		this.filePath = filePath;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

	public String getCertNumber() {
		return certNumber;
	}

	public void setCertNumber(String certNumber) {
		this.certNumber = certNumber;
	}

	public Integer getCertType() {
		return certType;
	}

	public void setCertType(Integer certType) {
		this.certType = certType;
	}
}
