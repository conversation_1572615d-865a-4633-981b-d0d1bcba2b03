package com.hys.zyy.manage.model;

import java.util.Date;

public class ZyyUserCheckManualData extends ZyyBaseObject {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1407842682235122575L;

	/**
	 * id
	 */
	private Long id;
	
	/**
	 * 用户Id
	 */
	private Long userId;
	
	/**
	 * 学科ID
	 */
	private Long baseId;
	
	/**
	 * 科室ID
	 */
	private Long deptId;
	
	/**
	 * 学科ID
	 */
	private Long deptStdId;
	
	/**
	 * 类型
	 */
	private Long type;
	
	/**
	 * 表单ID
	 */
	private Long formId;
	
	/**
	 * 数据键名称
	 */
	private String keyName;
	
	/**
	 * 创建日期
	 */
	private Date createDate;
	
	/**
	 * 创建人
	 */
	private Long createBy;
	
	/**
	 * 最后修改日期
	 */
	private Date lastUpdateDate;
	
	/*
	 * 最后修改人
	 */
	private Long lastUpdateBy;
	 
	/**
	 * 数据值
	 */
	private String keyValue;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public Long getBaseId() {
		return baseId;
	}

	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}

	public Long getDeptId() {
		return deptId;
	}

	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}

	public Long getDeptStdId() {
		return deptStdId;
	}

	public void setDeptStdId(Long deptStdId) {
		this.deptStdId = deptStdId;
	}

	public String getKeyName() {
		return keyName;
	}

	public void setKeyName(String keyName) {
		this.keyName = keyName;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Long getCreateBy() {
		return createBy;
	}

	public void setCreateBy(Long createBy) {
		this.createBy = createBy;
	}

	public Date getLastUpdateDate() {
		return lastUpdateDate;
	}

	public void setLastUpdateDate(Date lastUpdateDate) {
		this.lastUpdateDate = lastUpdateDate;
	}

	public Long getLastUpdateBy() {
		return lastUpdateBy;
	}

	public void setLastUpdateBy(Long lastUpdateBy) {
		this.lastUpdateBy = lastUpdateBy;
	}

	public String getKeyValue() {
		return keyValue;
	}

	public void setKeyValue(String keyValue) {
		this.keyValue = keyValue;
	}

	public Long getType() {
		return type;
	}

	public void setType(Long type) {
		this.type = type;
	}

	public Long getFormId() {
		return formId;
	}

	public void setFormId(Long formId) {
		this.formId = formId;
	}

}
