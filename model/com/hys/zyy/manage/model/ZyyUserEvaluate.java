package com.hys.zyy.manage.model;

import java.util.Date;


/**
 * 用户评价表实体类
 * 
 */

public class ZyyUserEvaluate extends ZyyBaseObject {

	private static final long serialVersionUID = 1L;
	/**
	 * 主键
	 */
	private Long id;
	/**
	 * 关联医院创建的评价表的id
	 */
	private Long tableId;
	/**
	 * 评价者的id
	 */
	private Long userId;
	/**
	 * 科室的id
	 */
	private Long deptId;
	/**
	 * 被评价用户的id
	 */
	private Long evaluatedUserId;
	/**
	 * 用户评价表创建的日期
	 */
	private Date evaluateDate;
	/**
	 * 其它意见和建议
	 */
	private String otherTips;
	
	private String evaluateDateString;
	
	public String getEvaluateDateString() {
		return evaluateDateString;
	}
	public void setEvaluateDateString(String evaluateDateString) {
		this.evaluateDateString = evaluateDateString;
	}
	public String getOtherTips() {
		return otherTips;
	}
	public void setOtherTips(String otherTips) {
		this.otherTips = otherTips;
	}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getTableId() {
		return tableId;
	}
	public void setTableId(Long tableId) {
		this.tableId = tableId;
	}
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	public Long getDeptId() {
		return deptId;
	}
	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}
	public Long getEvaluatedUserId() {
		return evaluatedUserId;
	}
	public void setEvaluatedUserId(Long evaluatedUserId) {
		this.evaluatedUserId = evaluatedUserId;
	}
	public Date getEvaluateDate() {
		return evaluateDate;
	}
	public void setEvaluateDate(Date evaluateDate) {
		this.evaluateDate = evaluateDate;
	}
	
}
