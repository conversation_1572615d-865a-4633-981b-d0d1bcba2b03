package com.hys.zyy.manage.model;

import java.util.Date;

/**
 * @desc 360评价
 * <AUTHOR>
 *
 */
public class ZyyUserEvaluateNew extends ZyyBaseObject {
	/**
	 * 
	 */
	private static final long serialVersionUID = 5082432974207364741L;
	/**
	 * 主键
	 */
	private Long id;
	/**
	 * 关联医院创建的评价表配置的id （zyy_evaluate_table_config的id）
	 */
	private Long tableId;
	/**
	 * 评价者的id
	 */
	private Long userId;
	/**
	 * 评价者的科室id
	 */
	private Long deptId;
	//评价者的专业基地id
	private Long baseId;
	//评价者的培训基地id
	private Long hospitalId;
	/**
	 * 被评价用户的id
	 */
	private Long evaluatedUserId;
	/**
	 * 用户评价表创建的日期
	 */
	private Date evaluateDate;
	/**
	 * 其它意见和建议
	 */
	private String otherTips;
	
	private String evaluateDateString;
	
	//被评科室id
	private Long evaluatedDepartId;
	//被评专业基地id
	private Long evaluatedBaseId;
	//被评培训基地id
	private Long evaluatedHospitalId;
	
	//入科时间
	private  String enterTime;
	//出科时间
	private String leaveTime;
	
	//组合key值 - 唯一值 = C+配置表id+E+U/B/D/H+被评论者id+U/B/D/H+评论者id+T+轮转结束时间+EY+评论年 UNIQUE_KEY
	private String uniqueKey;
	
	//护士或者患者的姓名
	private String patNurName;
	
	//评论开始时间
	private String startTime;
	
	//评论结束时间
	private String endTime;
	
	//评论年度 例如：2018
	private String evaluateYear;
	
	public Long getBaseId() {
		return baseId;
	}
	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}
	public Long getHospitalId() {
		return hospitalId;
	}
	public void setHospitalId(Long hospitalId) {
		this.hospitalId = hospitalId;
	}
	public String getEvaluateDateString() {
		return evaluateDateString;
	}
	public void setEvaluateDateString(String evaluateDateString) {
		this.evaluateDateString = evaluateDateString;
	}
	public String getOtherTips() {
		return otherTips;
	}
	public void setOtherTips(String otherTips) {
		this.otherTips = otherTips;
	}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getTableId() {
		return tableId;
	}
	public void setTableId(Long tableId) {
		this.tableId = tableId;
	}
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	public Long getDeptId() {
		return deptId;
	}
	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}
	public Long getEvaluatedUserId() {
		return evaluatedUserId;
	}
	public void setEvaluatedUserId(Long evaluatedUserId) {
		this.evaluatedUserId = evaluatedUserId;
	}
	public Date getEvaluateDate() {
		return evaluateDate;
	}
	public void setEvaluateDate(Date evaluateDate) {
		this.evaluateDate = evaluateDate;
	}
	public Long getEvaluatedDepartId() {
		return evaluatedDepartId;
	}
	public void setEvaluatedDepartId(Long evaluatedDepartId) {
		this.evaluatedDepartId = evaluatedDepartId;
	}
	public Long getEvaluatedBaseId() {
		return evaluatedBaseId;
	}
	public void setEvaluatedBaseId(Long evaluatedBaseId) {
		this.evaluatedBaseId = evaluatedBaseId;
	}
	public Long getEvaluatedHospitalId() {
		return evaluatedHospitalId;
	}
	public void setEvaluatedHospitalId(Long evaluatedHospitalId) {
		this.evaluatedHospitalId = evaluatedHospitalId;
	}
	public String getEnterTime() {
		return enterTime;
	}
	public void setEnterTime(String enterTime) {
		this.enterTime = enterTime;
	}
	public String getLeaveTime() {
		return leaveTime;
	}
	public void setLeaveTime(String leaveTime) {
		this.leaveTime = leaveTime;
	}
	public String getUniqueKey() {
		return uniqueKey;
	}
	public void setUniqueKey(String uniqueKey) {
		this.uniqueKey = uniqueKey;
	}
	public String getPatNurName() {
		return patNurName;
	}
	public void setPatNurName(String patNurName) {
		this.patNurName = patNurName;
	}
	public String getStartTime() {
		return startTime;
	}
	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}
	public String getEndTime() {
		return endTime;
	}
	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}
	public String getEvaluateYear() {
		return evaluateYear;
	}
	public void setEvaluateYear(String evaluateYear) {
		this.evaluateYear = evaluateYear;
	}
}
