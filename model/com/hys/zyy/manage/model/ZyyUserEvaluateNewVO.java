package com.hys.zyy.manage.model;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @desc 360评价
 * <AUTHOR>
 *
 */
public class ZyyUserEvaluateNewVO extends ZyyUserEvaluateNew {
	/**
	 * 
	 */
	private static final long serialVersionUID = 185088065317351490L;
	/**
	 * 用户的评价表对应的医院创建的评价表
	 */
	private ZyyEvaluateTableVO evaluateTable;
	/**
	 * 用户评价表对应的每一列的填写的值
	 */
	private List<ZyyCustomizeTableData> columnsData;
	
	/**
	 * 轮转时间段
	 */
	private String cycleDate;
	/**
	 * 轮转科室
	 */
	private String cycleDeptName;
	/**
	 * 被评价者（学员评价科室时，为科室名称;护士/病人评价学员时，为学员姓名）
	 */
	private String evaluatedName;
	/**
	 * 被评价者类型  21->培训学员  11->带教老师  9->轮转科室
	 */
	private Integer evaluatedType;
	/**
	 * 评价表类型
	 */
	private Integer tableType;
	
	private Long sUserId;//评价查询使用，评价者ID，医院的时候，为医院ID，基地的时候为基地ID 科室的时候，为科室ID
	private Long sEvalUserId;//评价查询使用，被评价者ID同上
	private Integer qFlag = 1;//查询or评价  1查询  2 评价
	private Date qStartDate;//查询评价表，开始时间
	private Date qEndDate;//查询评价表，结束时间
	private String realName; //评价人真实姓名
	private Integer totalScore; //评价表总分
	private Integer realScore;	//学员真实评价分数
	private String realNameBack; //评价人真实姓名备份
	
	
	
	private Integer evaluateType;//评价者类型
	
	//用户
	private ZyyUserExtendVO user;
	//评论配置表
	private ZyyEvaluateTableConfig config;
	
	//评价查询列表需要展示的字段
	//评价者所在单位
	private String unitName;
	//被评价者所在单位
	private String evaluatedUnitName;
	//被评价者姓名
	private String evaluatedRealName;
	//是否匿名  1 是  2 否
	private Integer anonymousType;
	
	//用户分数明细map
		Map<String,Integer> mapResultDetail;
		
		
		
		
		
	
	public String getRealNameBack() {
			return realNameBack;
		}
		public void setRealNameBack(String realNameBack) {
			this.realNameBack = realNameBack;
		}
	public Map<String, Integer> getMapResultDetail() {
			return mapResultDetail;
		}
		public void setMapResultDetail(Map<String, Integer> mapResultDetail) {
			this.mapResultDetail = mapResultDetail;
		}
	public Integer getTableType() {
		return tableType;
	}
	public void setTableType(Integer tableType) {
		this.tableType = tableType;
	}
	public String getCycleDate() {
		return cycleDate;
	}
	public void setCycleDate(String cycleDate) {
		this.cycleDate = cycleDate;
	}
	public String getCycleDeptName() {
		return cycleDeptName;
	}
	public void setCycleDeptName(String cycleDeptName) {
		this.cycleDeptName = cycleDeptName;
	}
	public String getEvaluatedName() {
		return evaluatedName;
	}
	public void setEvaluatedName(String evaluatedName) {
		this.evaluatedName = evaluatedName;
	}
	public Integer getEvaluatedType() {
		return evaluatedType;
	}
	public void setEvaluatedType(Integer evaluatedType) {
		this.evaluatedType = evaluatedType;
	}
	public List<ZyyCustomizeTableData> getColumnsData() {
		return columnsData;
	}
	public void setColumnsData(List<ZyyCustomizeTableData> columnsData) {
		this.columnsData = columnsData;
	}
	public ZyyEvaluateTableVO getEvaluateTable() {
		return evaluateTable;
	}
	public void setEvaluateTable(ZyyEvaluateTableVO evaluateTable) {
		this.evaluateTable = evaluateTable;
	}
	public Date getqStartDate() {
		return qStartDate;
	}
	public void setqStartDate(Date qStartDate) {
		this.qStartDate = qStartDate;
	}
	public Date getqEndDate() {
		return qEndDate;
	}
	public void setqEndDate(Date qEndDate) {
		this.qEndDate = qEndDate;
	}
	public String getRealName() {
		return realName;
	}
	public void setRealName(String realName) {
		this.realName = realName;
	}
	public Integer getTotalScore() {
		return totalScore;
	}
	public void setTotalScore(Integer totalScore) {
		this.totalScore = totalScore;
	}
	public Integer getRealScore() {
		return realScore;
	}
	public void setRealScore(Integer realScore) {
		this.realScore = realScore;
	}
	public Integer getqFlag() {
		return qFlag;
	}
	public void setqFlag(Integer qFlag) {
		this.qFlag = qFlag;
	}
	public ZyyUserExtendVO getUser() {
		return user;
	}
	public void setUser(ZyyUserExtendVO user) {
		this.user = user;
	}
	public ZyyEvaluateTableConfig getConfig() {
		return config;
	}
	public void setConfig(ZyyEvaluateTableConfig config) {
		this.config = config;
	}
	public String getUnitName() {
		return unitName;
	}
	public void setUnitName(String unitName) {
		this.unitName = unitName;
	}
	public String getEvaluatedUnitName() {
		return evaluatedUnitName;
	}
	public void setEvaluatedUnitName(String evaluatedUnitName) {
		this.evaluatedUnitName = evaluatedUnitName;
	}
	public String getEvaluatedRealName() {
		return evaluatedRealName;
	}
	public void setEvaluatedRealName(String evaluatedRealName) {
		this.evaluatedRealName = evaluatedRealName;
	}
	public Integer getAnonymousType() {
		return anonymousType;
	}
	public void setAnonymousType(Integer anonymousType) {
		this.anonymousType = anonymousType;
	}
	public Integer getEvaluateType() {
		return evaluateType;
	}
	public void setEvaluateType(Integer evaluateType) {
		this.evaluateType = evaluateType;
	}
	public Long getsUserId() {
		return sUserId;
	}
	public void setsUserId(Long sUserId) {
		this.sUserId = sUserId;
	}
	public Long getsEvalUserId() {
		return sEvalUserId;
	}
	public void setsEvalUserId(Long sEvalUserId) {
		this.sEvalUserId = sEvalUserId;
	}
	
}
