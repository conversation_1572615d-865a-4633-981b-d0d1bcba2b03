package com.hys.zyy.manage.model;

import java.util.Date;
import java.util.List;


/**
 * 
 */

public class ZyyUserEvaluateVO extends ZyyUserEvaluate {

	private static final long serialVersionUID = 1L;
	/**
	 * 用户的评价表对应的医院创建的评价表
	 */
	private ZyyEvaluateTableVO evaluateTable;
	/**
	 * 用户评价表对应的每一列的填写的值
	 */
	private List<ZyyCustomizeTableData> columnsData;
	
	/**
	 * 轮转时间段
	 */
	private String cycleDate;
	/**
	 * 轮转科室
	 */
	private String cycleDeptName;
	/**
	 * 被评价者（学员评价科室时，为科室名称;护士/病人评价学员时，为学员姓名）
	 */
	private String evaluatedName;
	/**
	 * 被评价者类型  21->培训学员  11->带教老师  9->轮转科室
	 */
	private Integer evaluatedType;
	/**
	 * 评价表类型
	 */
	private Integer tableType;
	
	private Date qStartDate;//查询评价表，开始时间
	private Date qEndDate;//查询评价表，结束时间
	private String realName; //评价人真实姓名
	private Integer totalScore; //评价表总分
	private Integer realScore;	//学员真实评价分数
	
	
	public Integer getTableType() {
		return tableType;
	}
	public void setTableType(Integer tableType) {
		this.tableType = tableType;
	}
	public String getCycleDate() {
		return cycleDate;
	}
	public void setCycleDate(String cycleDate) {
		this.cycleDate = cycleDate;
	}
	public String getCycleDeptName() {
		return cycleDeptName;
	}
	public void setCycleDeptName(String cycleDeptName) {
		this.cycleDeptName = cycleDeptName;
	}
	public String getEvaluatedName() {
		return evaluatedName;
	}
	public void setEvaluatedName(String evaluatedName) {
		this.evaluatedName = evaluatedName;
	}
	public Integer getEvaluatedType() {
		return evaluatedType;
	}
	public void setEvaluatedType(Integer evaluatedType) {
		this.evaluatedType = evaluatedType;
	}
	public List<ZyyCustomizeTableData> getColumnsData() {
		return columnsData;
	}
	public void setColumnsData(List<ZyyCustomizeTableData> columnsData) {
		this.columnsData = columnsData;
	}
	public ZyyEvaluateTableVO getEvaluateTable() {
		return evaluateTable;
	}
	public void setEvaluateTable(ZyyEvaluateTableVO evaluateTable) {
		this.evaluateTable = evaluateTable;
	}
	public Date getqStartDate() {
		return qStartDate;
	}
	public void setqStartDate(Date qStartDate) {
		this.qStartDate = qStartDate;
	}
	public Date getqEndDate() {
		return qEndDate;
	}
	public void setqEndDate(Date qEndDate) {
		this.qEndDate = qEndDate;
	}
	public String getRealName() {
		return realName;
	}
	public void setRealName(String realName) {
		this.realName = realName;
	}
	public Integer getTotalScore() {
		return totalScore;
	}
	public void setTotalScore(Integer totalScore) {
		this.totalScore = totalScore;
	}
	public Integer getRealScore() {
		return realScore;
	}
	public void setRealScore(Integer realScore) {
		this.realScore = realScore;
	}
}
