package com.hys.zyy.manage.model;

import java.util.Date;
import java.util.List;

import com.alibaba.fastjson.annotation.JSONField;

/**
 *
 * 标题：住院医师
 *
 * 作者：张伟清 2012-03-19
 *
 * 描述：学员用户扩展
 *
 * 说明:
 */
public class ZyyUserExtend extends ZyyUser {

	private static final long serialVersionUID = -1686054175376575636L;

	/**
	 * 性别 1.男 2.女 0.未知
	 */
	private Integer sex ;

	/**
	 * 出生日期
	 */
	private Date birthday ;

	/**
	 * 曾用名
	 */
	private String formerName;

	/**
	 * 民族
	 */
	private String nation;

	/**
	 * 婚姻
	 */
	private Integer isMarried;

	/**
	 * 籍贯
	 */
	private String familyRegister;

	/**
	 * 政治面貌
	 */
	private String politicalLandscape;

	/**
	 * 入党(团) 时间
	 */
	private Date joinPartyDate;

	/**
	 * 出生地
	 */
	private String birthplace;

	/**
	 * 生源地
	 */
	private Integer homePlace;

	/**
	 * 证件类型 1.身份证 2.军官证 0.其他 3.护照
	 */
	private Integer certificateType ;

	/**
	 * 证件号码
	 */
	private String certificateNo ;

	/**
	 * 最高学历 1 -大学专科 2 -大学本科 3 -硕士研究生 4 -博士研究生
	 */
	private Integer highestRecordSchool;

	/**
	 * 最高学历专业
	 */
	private String highestRecordProf;

	/**
	 * 最高学历毕业学校
	 */
	private String highestGraduateSchool;

	/**
	 * 最高学位
	 */
	private Integer highestDegree;

	/**
	 * 学制 1.三年 2.五年 3.七年 4.八年
	 */
	private Integer highestSchoolSystem;

	/**
	 * 本科生类型 1.其他 2.全国统考 3.推免
	 */
	private Integer undergraduateType ;

	/**
	 * 1 -科研型研究生 2 -临床型研究生
	 */
	private Integer graduateType ;

	/**
	 * 是否应届 是否应届 0.否 1.是
	 */
	private Integer isFresh;

	/**
	 * 毕业时间
	 */
	private Date graduationDate;

	/**
	 * 毕业学校地点 0 -其他 1 -上海市
	 */
	private Integer schoolPlace ;

	/**
	 * 学习期间担任职务
	 */
	private String postDuringStudy;

	/**
	 * 基地名称
	 */
	private String aliasName;

	/**
	 * 是否具有研究生学籍 1.是 0.否
	 */
	private Integer graduateStudentStatus ;

	/**
	 * 爱好特长
	 */
	private String hobbies;

	/**
	 * 有无医师执业证书 1.有 0.无
	 */
	private Integer physiciansPracticingCert ;

	/**
	 * 医师执业证书取得时间
	 */
	private Date getDate;

	/**
	 * 医师执业证书编码
	 */
	private String practiceCertificateNumber;

	/**
	 * 医师证书执业范围
	 */
	private String scopeCertificate ;

	/**
	 * 医师证书执业类别
	 */
	private String categoryCertificate ;
	/*
	 * 医师执业地点
	 */
	@JSONField(serialize = false)
	private String doctorPractisePlace;

	/**
	 * 医师执业证书取得时间 GET_PRACTICE_DATE
	 */
	private Date getPracticeDate;

	/**
	 * 有无医师资格证书
	 */
	private Integer hasCertificate;

	/**
	 * 医师资格证书编码
	 */
	private String certificateNumber;

	/**
	 * 参加工作时间
	 */
	private Date workDate ;

	/**
	 * 工作单位
	 */
	private String workUnit;

	/**
	 * 技术职称
	 */
	private String getTitle ;

	/**
	 * 技术职称取得时间
	 */
	private Date getTitleDate;

	/**
	 * 通讯地址
	 */
	private String address;

	/**
	 * 通讯地址邮政编码
	 */
	private String addressPostCode;

	/**
	 * 户口地址
	 */
	private String accountAddress;

	/**
	 * 户口地址邮政编码 -- 2012-12-10 xushiqing 新版意思为 工作地址邮编
	 */
	private String accountAddressPostCode;

	/**
	 * 家庭地址
	 */
	private String homeAddress;

	/**
	 * 家庭地址邮政编码
	 */
	private String homeAddressPostCode;

	/**
	 * 移动电话
	 */
	private String mobilNumber ;

	/**
	 * 固定电话
	 */
	private String telphone;

	/**
	 * 邮箱
	 */
	private String email ;

	/**
	 * 紧急联系人及关系
	 */
	private String emergencyContacter;

	/**
	 * 紧急联系人联系电话
	 */
	private String emergencyContacterPhone;

	/**
	 * 紧急联系人邮编
	 */
	private String emergencyContacterPostCode ;

	/**
	 * 健康状况 (既往病史)
	 */
	private String healthState;

	/**
	 * 身高
	 */
	private String height;

	/**
	 * 体重
	 */
	private String weight;

	/**
	 * 鞋码
	 */
	private String shoeSize;

	/**
	 * 外语水平语种及级别
	 */
	private String foreignLanguage;

	/**
	 * 计算机应用能力
	 */
	private String computerSkills;

	/**
	 * 其他
	 */
	private String otherSkills;

	/**
	 * 获奖情况
	 */
	private String awards;

	/**
	 * 科研及论文发表情况
	 */
	private String researchPapers;

	/**
	 * 何时何地何原因受奖励和处分
	 */
	private String rewardPunishment;

	/**
	 * 国籍
	 */
	private String userPost ;

	/**
	 * 照片路径
	 */
	private String photoPath ;

	/**
	 * 毁约次数
	 */
	private Integer repudiations;

	/**
	 * 考生类别 1-常规 2-委培 3-军人 0-其他
	 */
	private Integer candidatesType ;

	/**
	 * 最后修改时间
	 */
	private Date lastUpdateDate ;

	/**
	 * 学员用户补救次数
	 */
	private Integer remedyNumber ;

	/**
	 * 学员用户修改状态 0.用户未提交志愿 1.用户已提交志愿
	 */
	private Integer zyyUserWillStatus ;

	/**
	 * 入学前专业
	 */
	private String preSpecialty;

	/**
	 * 培训学届(老学员注册)
	 */
	private Integer recruitYearId;

	/**
	 * 培训医院(老学员注册)
	 */
	private Long hospitalId;

	/**
	 * 医院类型 1 中医 2 西医 3 中西医
	 */
	private Integer hospType;

	/**
	 * 培训学科(老学员注册)
	 */
	protected Long baseId;

	protected Long baseStdId;

	/**
	 * 是否是老用户
	 */
	private Integer isOldReg;

	/**
	 * 人员类型
	 */
	private Integer residencySource;

	/**
	 * JOB_NUMBER工号
	 */
	private String jobNumber;

	/**
	 * 姓名
	 */
	private String realName;

	/**
	 * 本院他院委托
	 */
	private String entrustType;

	/**
	 *  签约单位
	 */
	private String entrustUnit;

	/**
	 * 第一学历
	 */
	private Integer firstRecordSchool;

	/**
	 * 第一学历专业
	 */
	private String firstRecordProf;

	/**
	 * 第一学历毕业学校
	 */
	private String firstGraduateSchool;

	/**
	 * 工作年限
	 */
	private Integer workYears;

	/**
	 * 第一学位
	 */
	private Integer firstDegree;

	/**
	 *
	 * 第一学位学制
	 *
	 */
	private Integer firstSchoolSystem;

	/**
	 * 拟从事专科
	 */
	private Long wantBaseStdId ;

	/**
	 * 第一学位研究生类别
	 */
	private Integer firstGraduateType ;
	/**
	 * 院校类型  0:无 1:211工程院校	2:985工程院校	3:以上两者兼是	4:其他
	 */
	private Integer schoolType;

	/**
	 * 毕业证书编号 2013-2-28 添加
	 */
	private String graduationCode;

	/**
	 * 考生类别
	 */
	private Integer studentType ;

	   /**
	    * 以往结业考核记录（年月）
	    */
    private Date beforeForCheckRecode;

	private Integer isfreestu;


	private Integer degree_convergence;

	private Date trainingPeriod;
	//实习生的分组
	private String userGroup;
	//实习生的专业（备注：该专业与系统的专业不是一个概念，该专业只做文字存储）
	private String major;
	//实习生的分班
	private String userGrade;
	@JSONField(serialize = false)
	private List<Long> deptIdList;

	/**
	 * 学员属性
	 *  云南：1国家计划 2国家计划外 3省属计划 4省属计划外 5学位衔接 新彊 1.培训基地自培2.行业内社会人3.委托培养4.学位衔接
	 */
	private Integer recruitProperty;

	private String orgName;

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	// 报名吧添加字段 2016、4、1


	/**
	 * 第一学历是否获得证书  0否，1是
	 */
	private Integer hasFirstRecordCertificate;
	/**
	 * 第一学历毕业证书编号
	 */
	private String firstRecordCertifiNum;
	/**
	 * 第一学位是否获得证书  0否，1是
	 */
	private Integer hasFirstDegreeCertificate;
	/**
	 * 第一学位证书编号
	 */
	private String firstDegreeCertifiNum;
	/**
	 * 第一学位类型  1 专业型  2 科学型
	 */
	private Integer firstDegreeType;
	/**
	 * 最高学位是否获得证书     0否，1是
	 */
	private Integer hasHighestDegreeCertificate;
	/**
	 * 最高学位类型     1 专业型  2 科学型
	 */
	private Integer highestDegreeType;
	/**
	 * 最高学历是否获得证书    0否，1是
	 */
	private Integer hasHighestRecordCertificate;
	/**
	 * 最高学位证书编号
	 */
	private String highestDegreeCertifiNum;
	/**
	 * 医师资格级别     2执业医师  3助理执业医师
	 */
	private Integer physiciansQualificationLevel;
	/**
	 * 医师资格类别    1临床  2口腔3  公共卫生  4中医
	 */
	private Integer physiciansQualificationType;
	/**
	 * 计算机能力  1一级  2二级  3三级  4四级  5其他  6未参加考试
	 */
	private Integer computerLevel;
	/**
	 * 外语等级考试类型   1全国英语等级考试  2大学英语四六级考试  3英语专业等级考试 4未参加前三项等级考试
	 */
	private Integer foreignLanguageTestType;
	/**
	 * 单位级别  1三级甲等  2其他三级  3二级甲等  4其他
	 */
	private Integer workUnitLevel;
	/**
	 * 单位性质  1地市级以上  2县级  3社区或乡镇卫生院
	 */
	private Integer workUnitNature;
	/**
	 * QQ
	 */
	private String qqNumber;
	/**
	 * 培训学校
	 */
	private String trainSchool;
	/**
	 * 是否是订单定向生  1-是  0-否
	 */
	private Integer directStu;

	private Integer userPostTitle;
	/**
	 * 自我评价信息
	 */
	private String selfEvaluation;

	//职务
	private  String duty;

	private Date graduateDate;

	private String deptName;

	private int deptLevel;

	private String workNumber;
	/*
	 * 微信号
	 */
	private String wechatNumber;
	/*
	 * 教师资格证书号
	 */
	private String teacQualCerNum;
	/*
	 * 教学职称
	 */
	private String teachingTitle;

	/**
	 * null 老学员、1 专硕生 、2 订单定向生
	 */
	private Integer graduateTargetType;

	//生源地 - 省
	private Long homeProvinceId;
	//生源地 - 市
	private Long homeCityId;
	//定向地
	private String targetPlace;
	//“普通学员”单位人注册时添加“所在市”、“所在县”
	//所在市 LIVE_PROVINCE_ID
	private Long liveProvinceId;
	@JSONField(serialize = false)
	private String liveProvinceStr;
	//所在县  LIVE_CITY_ID
	private Long liveCityId;
	@JSONField(serialize = false)
	private String liveCityStr;
	@JSONField(serialize = false)
	private Long liveCountyId;
	@JSONField(serialize = false)
	private String liveCountyStr;

	//户口所在地省行政区划
	private Long hukouAddress;
	//外语等级考试证书编号
	private String engCetCode;
	//外语等级考试证书取得时间
	private Date engCetTime;
	//备注
	private String remark;
	/*
	 * 户口所在地省ID
	 */
	@JSONField(serialize = false)
	private Long regLocProvinceId;
	/*
	 * 户口所在地市ID
	 */
	@JSONField(serialize = false)
	private Long regLocCityId;
	/*
	 * 户口所在地县ID
	 */
	@JSONField(serialize = false)
	private Long regLocCountyId;
	/*
	 * 工作单位统一信用代码
	 */
	@JSONField(serialize = false)
	private String workUnitCreditCode;
	/*
	 * 是否通过（助理）医师资格考试（1：是，0：否）
	 */
	private Integer isPassQualificationExam;
	/*
	 * 通过（助理）医师资格考试时间
	 */
	private Date passQualificationExamTime;
	/*
	 * 是否为农村订单定向免费医学毕业生（1：是，0：否）
	 */
	private Integer isRuralTargetFreeStu;
	//退培时间
	private Date quitTrainDate;
	//国际及地区
	private String country;
	//医师证书上传路径
	private String doctorCertificate;

	//身份证正面国徽面上传路径
	private String cardFront;
	//身份证反面人像面上传路径
	private String cardBehind;
	private Integer isPassPhysicianQuaExam;
	//医师资格考试通过时间
	private Date physicianQuaExamTime;

	//培养类型  1 国家计划培养 2 省内自主培养
	@JSONField(serialize = false)
	private Integer educationalType ;
	//1 全日制硕士专业学位研究生 2 外单位委托培养住院医师 3 本单位住院医师 4 面向社会招收住院医师
	@JSONField(serialize = false)
	private Integer personDetail;
	/*
	 * 报名承诺书
	 */
	@JSONField(serialize = false)
	private String bmcnsPath;

	private String weixinOpenid;


	/*
	 * 英语能力
	 * 1：未通过
	 * 2：CET-4
	 * 3：CET-6
	 * 4：TEM-4
	 * 5：TEM-8
	 * 6：PETS-1
	 * 7：PETS-2
	 * 8：PETS-3
	 * 9：PETS-4
	 * 10：PETS-5
	 */
	@JSONField(serialize = false)
	private Integer englishLevel;
	/*
	 * 第一学历毕业院校类型（0=本地高校；1=非本地高校）
	 */
	@JSONField(serialize = false)
	private Integer dyxlbyyxlx;
	/*
	 * 第一学历毕业院校分类（1=211工程院校；2=985工程院校；3=以上两者兼是；4=其他）
	 */
	@JSONField(serialize = false)
	private Integer dyxlbyyxfl;
	/*
	 * 科室账号角色类型（1=教学主任；2=教学秘书；3=科室负责人；4=其他）
	 */
	@JSONField(serialize = false)
	private Integer roleType;
	/*
	 * 学习类型（1=其他；2=西学中；3=全科转岗；4=中医师承）
	 */
	@JSONField(serialize = false)
	private Integer studyType;
	@JSONField(serialize = false)
	private String accountRemark;
	private String bdpUserId;

	private Integer trainPhase;
	/*
	 * 导师姓名
	 */
	@JSONField(serialize = false)
	private String mentorName;
	/*
	 * 导师所在单位
	 */
	@JSONField(serialize = false)
	private String mentorUnit;

	public ZyyUserExtend() {
		super();
	}

	public ZyyUserExtend(String mobilNumber) {
		super();
		this.mobilNumber = mobilNumber;
	}

	public Integer getDyxlbyyxlx() {
		return dyxlbyyxlx;
	}

	public void setDyxlbyyxlx(Integer dyxlbyyxlx) {
		this.dyxlbyyxlx = dyxlbyyxlx;
	}

	public Integer getDyxlbyyxfl() {
		return dyxlbyyxfl;
	}

	public void setDyxlbyyxfl(Integer dyxlbyyxfl) {
		this.dyxlbyyxfl = dyxlbyyxfl;
	}

	public Integer getRoleType() {
		return roleType;
	}

	public void setRoleType(Integer roleType) {
		this.roleType = roleType;
	}

	public Integer getStudyType() {
		return studyType;
	}

	public void setStudyType(Integer studyType) {
		this.studyType = studyType;
	}

	public String getAccountRemark() {
		return accountRemark;
	}

	public void setAccountRemark(String accountRemark) {
		this.accountRemark = accountRemark == null ? null : accountRemark.trim();
	}

	public String getBdpUserId() {
		return bdpUserId;
	}

	public void setBdpUserId(String bdpUserId) {
		this.bdpUserId = bdpUserId == null ? null : bdpUserId.trim();
	}

	public Integer getGraduateTargetType() {
		return graduateTargetType;
	}

	public void setGraduateTargetType(Integer graduateTargetType) {
		this.graduateTargetType = graduateTargetType;
	}

	public String getWorkNumber() {
		return workNumber;
	}

	public void setWorkNumber(String workNumber) {
		this.workNumber = workNumber;
	}

	public int getDeptLevel() {
		return deptLevel;
	}

	public void setDeptLevel(int deptLevel) {
		this.deptLevel = deptLevel;
	}

	public String getDeptName() {
		return deptName;
	}

	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}

	public Date getGraduateDate() {
		return graduateDate;
	}

	public void setGraduateDate(Date graduateDate) {
		this.graduateDate = graduateDate;
	}

	public String getDuty() {
		return duty;
	}

	public void setDuty(String duty) {
		this.duty = duty;
	}

	public String getSelfEvaluation() {
		return selfEvaluation;
	}

	public void setSelfEvaluation(String selfEvaluation) {
		this.selfEvaluation = selfEvaluation;
	}

	public Integer getUserPostTitle() {
		return userPostTitle;
	}

	public void setUserPostTitle(Integer userPostTitle) {
		this.userPostTitle = userPostTitle;
	}

	public Integer getDirectStu() {
		return directStu;
	}

	public void setDirectStu(Integer directStu) {
		this.directStu = directStu;
	}

	public String getTrainSchool() {
		return trainSchool;
	}

	public void setTrainSchool(String trainSchool) {
		this.trainSchool = trainSchool;
	}

	public Integer getHasFirstRecordCertificate() {
		return hasFirstRecordCertificate;
	}

	public void setHasFirstRecordCertificate(Integer hasFirstRecordCertificate) {
		this.hasFirstRecordCertificate = hasFirstRecordCertificate;
	}

	public String getFirstRecordCertifiNum() {
		return firstRecordCertifiNum;
	}

	public void setFirstRecordCertifiNum(String firstRecordCertifiNum) {
		this.firstRecordCertifiNum = firstRecordCertifiNum;
	}

	public Integer getHasFirstDegreeCertificate() {
		return hasFirstDegreeCertificate;
	}

	public void setHasFirstDegreeCertificate(Integer hasFirstDegreeCertificate) {
		this.hasFirstDegreeCertificate = hasFirstDegreeCertificate;
	}

	public String getFirstDegreeCertifiNum() {
		return firstDegreeCertifiNum;
	}

	public void setFirstDegreeCertifiNum(String firstDegreeCertifiNum) {
		this.firstDegreeCertifiNum = firstDegreeCertifiNum;
	}

	public Integer getFirstDegreeType() {
		return firstDegreeType;
	}

	public void setFirstDegreeType(Integer firstDegreeType) {
		this.firstDegreeType = firstDegreeType;
	}

	public Integer getHasHighestDegreeCertificate() {
		return hasHighestDegreeCertificate;
	}

	public void setHasHighestDegreeCertificate(Integer hasHighestDegreeCertificate) {
		this.hasHighestDegreeCertificate = hasHighestDegreeCertificate;
	}

	public Integer getHighestDegreeType() {
		return highestDegreeType;
	}

	public void setHighestDegreeType(Integer highestDegreeType) {
		this.highestDegreeType = highestDegreeType;
	}

	public Integer getHasHighestRecordCertificate() {
		return hasHighestRecordCertificate;
	}

	public void setHasHighestRecordCertificate(Integer hasHighestRecordCertificate) {
		this.hasHighestRecordCertificate = hasHighestRecordCertificate;
	}

	public String getHighestDegreeCertifiNum() {
		return highestDegreeCertifiNum;
	}

	public void setHighestDegreeCertifiNum(String highestDegreeCertifiNum) {
		this.highestDegreeCertifiNum = highestDegreeCertifiNum;
	}

	public Integer getPhysiciansQualificationLevel() {
		return physiciansQualificationLevel;
	}

	public void setPhysiciansQualificationLevel(Integer physiciansQualificationLevel) {
		this.physiciansQualificationLevel = physiciansQualificationLevel;
	}

	public Integer getPhysiciansQualificationType() {
		return physiciansQualificationType;
	}

	public void setPhysiciansQualificationType(Integer physiciansQualificationType) {
		this.physiciansQualificationType = physiciansQualificationType;
	}

	public Integer getComputerLevel() {
		return computerLevel;
	}

	public void setComputerLevel(Integer computerLevel) {
		this.computerLevel = computerLevel;
	}

	public Integer getForeignLanguageTestType() {
		return foreignLanguageTestType;
	}

	public void setForeignLanguageTestType(Integer foreignLanguageTestType) {
		this.foreignLanguageTestType = foreignLanguageTestType;
	}

	public Integer getWorkUnitLevel() {
		return workUnitLevel;
	}

	public void setWorkUnitLevel(Integer workUnitLevel) {
		this.workUnitLevel = workUnitLevel;
	}

	public Integer getWorkUnitNature() {
		return workUnitNature;
	}

	public void setWorkUnitNature(Integer workUnitNature) {
		this.workUnitNature = workUnitNature;
	}

	public String getQqNumber() {
		return qqNumber;
	}

	public void setQqNumber(String qqNumber) {
		this.qqNumber = qqNumber;
	}

	public Integer getRecruitProperty() {
		return recruitProperty;
	}

	public void setRecruitProperty(Integer recruitProperty) {
		this.recruitProperty = recruitProperty;
	}

	public Integer getDegree_convergence() {
		return degree_convergence;
	}

	public void setDegree_convergence(Integer degree_convergence) {
		this.degree_convergence = degree_convergence;
	}

	public Integer getIsfreestu() {
		return isfreestu;
	}

	public Integer getTrainPhase() {
		return trainPhase;
	}

	public void setTrainPhase(Integer trainPhase) {
		this.trainPhase = trainPhase;
	}

	public void setIsfreestu(Integer isfreestu) {
		this.isfreestu = isfreestu;
	}

	public Integer getFirstGraduateType() {
		return firstGraduateType;
	}

	public void setFirstGraduateType(Integer firstGraduateType) {
		this.firstGraduateType = firstGraduateType;
	}

	public Integer getFirstSchoolSystem() {
		return firstSchoolSystem;
	}

	public void setFirstSchoolSystem(Integer firstSchoolSystem) {
		this.firstSchoolSystem = firstSchoolSystem;
	}

	public Integer getFirstDegree() {
		return firstDegree;
	}

	public void setFirstDegree(Integer firstDegree) {
		this.firstDegree = firstDegree;
	}

	public Integer getWorkYears() {
		return workYears;
	}

	public void setWorkYears(Integer workYears) {
		this.workYears = workYears;
	}

	public String getRealName() {
		return realName;
	}

	public void setRealName(String realName) {
		this.realName = realName == null ? null : realName.trim();
	}

	public String getJobNumber() {
		return jobNumber;
	}

	public void setJobNumber(String jobNumber) {
		this.jobNumber = jobNumber;
	}

	public Integer getIsOldReg() {
		return isOldReg;
	}

	public void setIsOldReg(Integer isOldReg) {
		this.isOldReg = isOldReg;
	}

	public Integer getSex() {
		return sex;
	}

	public void setSex(Integer sex) {
		this.sex = sex;
	}

	public Date getBirthday() {
		return birthday;
	}

	public void setBirthday(Date birthday) {
		this.birthday = birthday;
	}

	public String getFormerName() {
		return formerName;
	}

	public void setFormerName(String formerName) {
		this.formerName = formerName;
	}

	public String getNation() {
		return nation;
	}

	public void setNation(String nation) {
		this.nation = nation == null ? null : nation.trim();
	}

	public Integer getIsMarried() {
		return isMarried;
	}

	public void setIsMarried(Integer isMarried) {
		this.isMarried = isMarried;
	}

	public String getFamilyRegister() {
		return familyRegister;
	}

	public void setFamilyRegister(String familyRegister) {
		this.familyRegister = familyRegister;
	}

	public String getPoliticalLandscape() {
		return politicalLandscape;
	}

	public void setPoliticalLandscape(String politicalLandscape) {
		this.politicalLandscape = politicalLandscape;
	}

	public Date getJoinPartyDate() {
		return joinPartyDate;
	}

	public void setJoinPartyDate(Date joinPartyDate) {
		this.joinPartyDate = joinPartyDate;
	}

	public String getBirthplace() {
		return birthplace;
	}

	public void setBirthplace(String birthplace) {
		this.birthplace = birthplace;
	}

	public Integer getHomePlace() {
		return homePlace;
	}

	public void setHomePlace(Integer homePlace) {
		this.homePlace = homePlace;
	}

	public Integer getCertificateType() {
		return certificateType;
	}

	public void setCertificateType(Integer certificateType) {
		this.certificateType = certificateType;
	}

	public String getCertificateNo() {
		return certificateNo;
	}

	public void setCertificateNo(String certificateNo) {
		this.certificateNo = certificateNo == null ? null : certificateNo.trim();
	}

	public Integer getHighestRecordSchool() {
		return highestRecordSchool;
	}

	public void setHighestRecordSchool(Integer highestRecordSchool) {
		this.highestRecordSchool = highestRecordSchool;
	}

	public String getHighestRecordProf() {
		return highestRecordProf;
	}

	public void setHighestRecordProf(String highestRecordProf) {
		this.highestRecordProf = highestRecordProf;
	}

	public String getHighestGraduateSchool() {
		return highestGraduateSchool;
	}

	public void setHighestGraduateSchool(String highestGraduateSchool) {
		this.highestGraduateSchool = highestGraduateSchool;
	}

	public Integer getHighestDegree() {
		return highestDegree;
	}

	public void setHighestDegree(Integer highestDegree) {
		this.highestDegree = highestDegree;
	}

	public Integer getHighestSchoolSystem() {
		return highestSchoolSystem;
	}

	public void setHighestSchoolSystem(Integer highestSchoolSystem) {
		this.highestSchoolSystem = highestSchoolSystem;
	}

	public Integer getUndergraduateType() {
		return undergraduateType;
	}

	public void setUndergraduateType(Integer undergraduateType) {
		this.undergraduateType = undergraduateType;
	}

	public Integer getGraduateType() {
		return graduateType;
	}

	public void setGraduateType(Integer graduateType) {
		this.graduateType = graduateType;
	}

	public Integer getIsFresh() {
		return isFresh;
	}

	public void setIsFresh(Integer isFresh) {
		this.isFresh = isFresh;
	}

	public Date getGraduationDate() {
		return graduationDate;
	}

	public void setGraduationDate(Date graduationDate) {
		this.graduationDate = graduationDate;
	}

	public Integer getSchoolPlace() {
		return schoolPlace;
	}

	public void setSchoolPlace(Integer schoolPlace) {
		this.schoolPlace = schoolPlace;
	}

	public String getPostDuringStudy() {
		return postDuringStudy;
	}

	public void setPostDuringStudy(String postDuringStudy) {
		this.postDuringStudy = postDuringStudy;
	}

	public Integer getGraduateStudentStatus() {
		return graduateStudentStatus;
	}

	public void setGraduateStudentStatus(Integer graduateStudentStatus) {
		this.graduateStudentStatus = graduateStudentStatus;
	}

	public String getHobbies() {
		return hobbies;
	}

	public void setHobbies(String hobbies) {
		this.hobbies = hobbies;
	}

	public Integer getPhysiciansPracticingCert() {
		return physiciansPracticingCert;
	}

	public void setPhysiciansPracticingCert(Integer physiciansPracticingCert) {
		this.physiciansPracticingCert = physiciansPracticingCert;
	}

	public Date getGetDate() {
		return getDate;
	}

	public void setGetDate(Date getDate) {
		this.getDate = getDate;
	}

	public String getPracticeCertificateNumber() {
		return practiceCertificateNumber;
	}

	public void setPracticeCertificateNumber(String practiceCertificateNumber) {
		this.practiceCertificateNumber = practiceCertificateNumber;
	}

	public String getScopeCertificate() {
		return scopeCertificate;
	}

	public void setScopeCertificate(String scopeCertificate) {
		this.scopeCertificate = scopeCertificate;
	}

	public String getCategoryCertificate() {
		return categoryCertificate;
	}

	public void setCategoryCertificate(String categoryCertificate) {
		this.categoryCertificate = categoryCertificate;
	}

	public Integer getHasCertificate() {
		return hasCertificate;
	}

	public void setHasCertificate(Integer hasCertificate) {
		this.hasCertificate = hasCertificate;
	}

	public String getCertificateNumber() {
		return certificateNumber;
	}

	public void setCertificateNumber(String certificateNumber) {
		this.certificateNumber = certificateNumber;
	}

	public Date getWorkDate() {
		return workDate;
	}

	public void setWorkDate(Date workDate) {
		this.workDate = workDate;
	}

	public String getWorkUnit() {
		return workUnit;
	}

	public void setWorkUnit(String workUnit) {
		this.workUnit = workUnit;
	}

	public String getGetTitle() {
		return getTitle;
	}

	public void setGetTitle(String getTitle) {
		this.getTitle = getTitle;
	}

	public Date getGetTitleDate() {
		return getTitleDate;
	}

	public void setGetTitleDate(Date getTitleDate) {
		this.getTitleDate = getTitleDate;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getAddressPostCode() {
		return addressPostCode;
	}

	public void setAddressPostCode(String addressPostCode) {
		this.addressPostCode = addressPostCode;
	}

	public String getAccountAddress() {
		return accountAddress;
	}

	public void setAccountAddress(String accountAddress) {
		this.accountAddress = accountAddress;
	}

	public String getAccountAddressPostCode() {
		return accountAddressPostCode;
	}

	public void setAccountAddressPostCode(String accountAddressPostCode) {
		this.accountAddressPostCode = accountAddressPostCode;
	}

	public String getHomeAddress() {
		return homeAddress;
	}

	public void setHomeAddress(String homeAddress) {
		this.homeAddress = homeAddress;
	}

	public String getHomeAddressPostCode() {
		return homeAddressPostCode;
	}

	public void setHomeAddressPostCode(String homeAddressPostCode) {
		this.homeAddressPostCode = homeAddressPostCode;
	}

	public String getMobilNumber() {
		return mobilNumber;
	}

	public void setMobilNumber(String mobilNumber) {
		this.mobilNumber = mobilNumber;
	}

	public String getTelphone() {
		return telphone;
	}

	public void setTelphone(String telphone) {
		this.telphone = telphone;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getEmergencyContacter() {
		return emergencyContacter;
	}

	public void setEmergencyContacter(String emergencyContacter) {
		this.emergencyContacter = emergencyContacter;
	}

	public String getEmergencyContacterPhone() {
		return emergencyContacterPhone;
	}

	public void setEmergencyContacterPhone(String emergencyContacterPhone) {
		this.emergencyContacterPhone = emergencyContacterPhone;
	}

	public String getEmergencyContacterPostCode() {
		return emergencyContacterPostCode;
	}

	public void setEmergencyContacterPostCode(String emergencyContacterPostCode) {
		this.emergencyContacterPostCode = emergencyContacterPostCode;
	}

	public String getHealthState() {
		return healthState;
	}

	public void setHealthState(String healthState) {
		this.healthState = healthState;
	}

	public String getHeight() {
		return height;
	}

	public void setHeight(String height) {
		this.height = height;
	}

	public String getWeight() {
		return weight;
	}

	public void setWeight(String weight) {
		this.weight = weight;
	}

	public String getShoeSize() {
		return shoeSize;
	}

	public void setShoeSize(String shoeSize) {
		this.shoeSize = shoeSize;
	}

	public String getForeignLanguage() {
		return foreignLanguage;
	}

	public void setForeignLanguage(String foreignLanguage) {
		this.foreignLanguage = foreignLanguage;
	}

	public String getComputerSkills() {
		return computerSkills;
	}

	public void setComputerSkills(String computerSkills) {
		this.computerSkills = computerSkills;
	}

	public String getOtherSkills() {
		return otherSkills;
	}

	public void setOtherSkills(String otherSkills) {
		this.otherSkills = otherSkills;
	}

	public String getAwards() {
		return awards;
	}

	public void setAwards(String awards) {
		this.awards = awards;
	}

	public String getResearchPapers() {
		return researchPapers;
	}

	public void setResearchPapers(String researchPapers) {
		this.researchPapers = researchPapers;
	}

	public String getRewardPunishment() {
		return rewardPunishment;
	}

	public void setRewardPunishment(String rewardPunishment) {
		this.rewardPunishment = rewardPunishment;
	}

	public String getUserPost() {
		return userPost;
	}

	public void setUserPost(String userPost) {
		this.userPost = userPost;
	}

	public String getPhotoPath() {
		return photoPath;
	}

	public void setPhotoPath(String photoPath) {
		this.photoPath = photoPath;
	}

	public Integer getRepudiations() {
		return repudiations;
	}

	public void setRepudiations(Integer repudiations) {
		this.repudiations = repudiations;
	}

	public Integer getCandidatesType() {
		return candidatesType;
	}

	public void setCandidatesType(Integer candidatesType) {
		this.candidatesType = candidatesType;
	}

	public Date getLastUpdateDate() {
		return lastUpdateDate;
	}

	public void setLastUpdateDate(Date lastUpdateDate) {
		this.lastUpdateDate = lastUpdateDate;
	}

	public Integer getRemedyNumber() {
		return remedyNumber;
	}

	public void setRemedyNumber(Integer remedyNumber) {
		this.remedyNumber = remedyNumber;
	}

	public Integer getZyyUserWillStatus() {
		return zyyUserWillStatus;
	}

	public void setZyyUserWillStatus(Integer zyyUserWillStatus) {
		this.zyyUserWillStatus = zyyUserWillStatus;
	}

	public String getAliasName() {
		return aliasName;
	}

	public void setAliasName(String aliasName) {
		this.aliasName = aliasName;
	}

	public String getPreSpecialty() {
		return preSpecialty;
	}

	public void setPreSpecialty(String preSpecialty) {
		this.preSpecialty = preSpecialty;
	}

	public Integer getRecruitYearId() {
		return recruitYearId;
	}

	public void setRecruitYearId(Integer recruitYearId) {
		this.recruitYearId = recruitYearId;
	}

	public Long getHospitalId() {
		return hospitalId;
	}

	public void setHospitalId(Long hospitalId) {
		this.hospitalId = hospitalId;
	}

	public Long getBaseId() {
		return baseId;
	}

	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}

	public Integer getResidencySource() {
		return residencySource;
	}

	public void setResidencySource(Integer residencySource) {
		this.residencySource = residencySource;
	}

	public String getEntrustType() {
		return entrustType;
	}

	public void setEntrustType(String entrustType) {
		this.entrustType = entrustType;
	}

	public String getEntrustUnit() {
		return entrustUnit;
	}

	public void setEntrustUnit(String entrustUnit) {
		this.entrustUnit = entrustUnit;
	}

	public Integer getFirstRecordSchool() {
		return firstRecordSchool;
	}

	public void setFirstRecordSchool(Integer firstRecordSchool) {
		this.firstRecordSchool = firstRecordSchool;
	}

	public String getFirstRecordProf() {
		return firstRecordProf;
	}

	public void setFirstRecordProf(String firstRecordProf) {
		this.firstRecordProf = firstRecordProf;
	}

	public String getFirstGraduateSchool() {
		return firstGraduateSchool;
	}

	public void setFirstGraduateSchool(String firstGraduateSchool) {
		this.firstGraduateSchool = firstGraduateSchool;
	}

	public Long getWantBaseStdId() {
		return wantBaseStdId;
	}

	public void setWantBaseStdId(Long wantBaseStdId) {
		this.wantBaseStdId = wantBaseStdId;
	}

	public Integer getHospType() {
		return hospType;
	}

	public void setHospType(Integer hospType) {
		this.hospType = hospType;
	}

	public Date getGetPracticeDate() {
		return getPracticeDate;
	}

	public void setGetPracticeDate(Date getPracticeDate) {
		this.getPracticeDate = getPracticeDate;
	}

	public Integer getSchoolType() {
		return schoolType;
	}

	public void setSchoolType(Integer schoolType) {
		this.schoolType = schoolType;
	}

	public String getGraduationCode() {
		return graduationCode;
	}

	public void setGraduationCode(String graduationCode) {
		this.graduationCode = graduationCode;
	}

	public Integer getStudentType() {
		return studentType;
	}

	public void setStudentType(Integer studentType) {
		this.studentType = studentType;
	}

	public Date getBeforeForCheckRecode() {
		return beforeForCheckRecode;
	}

	public void setBeforeForCheckRecode(Date beforeForCheckRecode) {
		this.beforeForCheckRecode = beforeForCheckRecode;
	}

	public Date getTrainingPeriod() {
		return trainingPeriod;
	}

	public void setTrainingPeriod(Date trainingPeriod) {
		this.trainingPeriod = trainingPeriod;
	}

	public Long getBaseStdId() {
		return baseStdId;
	}

	public void setBaseStdId(Long baseStdId) {
		this.baseStdId = baseStdId;
	}

	public String getWechatNumber() {
		return wechatNumber;
	}

	public void setWechatNumber(String wechatNumber) {
		this.wechatNumber = wechatNumber == null ? null : wechatNumber.trim();
	}

	public String getTeacQualCerNum() {
		return teacQualCerNum;
	}

	public void setTeacQualCerNum(String teacQualCerNum) {
		this.teacQualCerNum = teacQualCerNum == null ? null : teacQualCerNum.trim();
	}

	public String getTeachingTitle() {
		return teachingTitle;
	}

	public void setTeachingTitle(String teachingTitle) {
		this.teachingTitle = teachingTitle == null ? null : teachingTitle.trim();
	}

	public Long getHomeProvinceId() {
		return homeProvinceId;
	}

	public void setHomeProvinceId(Long homeProvinceId) {
		this.homeProvinceId = homeProvinceId;
	}

	public Long getHomeCityId() {
		return homeCityId;
	}

	public void setHomeCityId(Long homeCityId) {
		this.homeCityId = homeCityId;
	}

	public String getTargetPlace() {
		return targetPlace;
	}

	public void setTargetPlace(String targetPlace) {
		this.targetPlace = targetPlace;
	}

	public Long getLiveProvinceId() {
		return liveProvinceId;
	}

	public void setLiveProvinceId(Long liveProvinceId) {
		this.liveProvinceId = liveProvinceId;
	}

	public Long getLiveCityId() {
		return liveCityId;
	}

	public void setLiveCityId(Long liveCityId) {
		this.liveCityId = liveCityId;
	}




	public String getUserGroup() {
		return userGroup;
	}

	public void setUserGroup(String userGroup) {
		this.userGroup = userGroup;
	}

	public String getUserGrade() {
		return userGrade;
	}

	public void setUserGrade(String userGrade) {
		this.userGrade = userGrade;
	}

	public String getMajor() {
		return major;
	}

	public void setMajor(String major) {
		this.major = major;
	}

	public Long getHukouAddress() {
		return hukouAddress;
	}

	public void setHukouAddress(Long hukouAddress) {
		this.hukouAddress = hukouAddress;
	}

	public String getEngCetCode() {
		return engCetCode;
	}

	public void setEngCetCode(String engCetCode) {
		this.engCetCode = engCetCode;
	}

	public Date getEngCetTime() {
		return engCetTime;
	}

	public void setEngCetTime(Date engCetTime) {
		this.engCetTime = engCetTime;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public Integer getIsPassQualificationExam() {
		return isPassQualificationExam;
	}

	public void setIsPassQualificationExam(Integer isPassQualificationExam) {
		this.isPassQualificationExam = isPassQualificationExam;
	}

	public Date getPassQualificationExamTime() {
		return passQualificationExamTime;
	}

	public void setPassQualificationExamTime(Date passQualificationExamTime) {
		this.passQualificationExamTime = passQualificationExamTime;
	}

	public Integer getIsRuralTargetFreeStu() {
		return isRuralTargetFreeStu;
	}

	public void setIsRuralTargetFreeStu(Integer isRuralTargetFreeStu) {
		this.isRuralTargetFreeStu = isRuralTargetFreeStu;
	}

	public Date getQuitTrainDate() {
		return quitTrainDate;
	}

	public void setQuitTrainDate(Date quitTrainDate) {
		this.quitTrainDate = quitTrainDate;
	}


	public String getCountry() {
		return country;
	}

	public void setCountry(String country) {
		this.country = country;
	}

	public String getDoctorCertificate() {
		return doctorCertificate;
	}

	public void setDoctorCertificate(String doctorCertificate) {
		this.doctorCertificate = doctorCertificate;
	}

	public String getCardFront() {
		return cardFront;
	}

	public void setCardFront(String cardFront) {
		this.cardFront = cardFront;
	}

	public String getCardBehind() {
		return cardBehind;
	}

	public void setCardBehind(String cardBehind) {
		this.cardBehind = cardBehind;
	}

	public Integer getIsPassPhysicianQuaExam() {
		return isPassPhysicianQuaExam;
	}

	public void setIsPassPhysicianQuaExam(Integer isPassPhysicianQuaExam) {
		this.isPassPhysicianQuaExam = isPassPhysicianQuaExam;
	}

	public Date getPhysicianQuaExamTime() {
		return physicianQuaExamTime;
	}

	public void setPhysicianQuaExamTime(Date physicianQuaExamTime) {
		this.physicianQuaExamTime = physicianQuaExamTime;
	}

	public Integer getEducationalType() {
		return educationalType;
	}

	public void setEducationalType(Integer educationalType) {
		this.educationalType = educationalType;
	}

	public Integer getPersonDetail() {
		return personDetail;
	}

	public void setPersonDetail(Integer personDetail) {
		this.personDetail = personDetail;
	}

	public String getBmcnsPath() {
		return bmcnsPath;
	}

	public void setBmcnsPath(String bmcnsPath) {
		this.bmcnsPath = bmcnsPath == null ? null : bmcnsPath.trim();
	}

	public String getWeixinOpenid() {
		return weixinOpenid;
	}

	public void setWeixinOpenid(String weixinOpenid) {
		this.weixinOpenid = weixinOpenid;
	}

	public Integer getEnglishLevel() {
		return englishLevel;
	}

	public void setEnglishLevel(Integer englishLevel) {
		this.englishLevel = englishLevel;
	}

	public String getDoctorPractisePlace() {
		return doctorPractisePlace;
	}

	public void setDoctorPractisePlace(String doctorPractisePlace) {
		this.doctorPractisePlace = doctorPractisePlace == null ? null : doctorPractisePlace.trim();
	}

	public Long getRegLocProvinceId() {
		return regLocProvinceId;
	}

	public void setRegLocProvinceId(Long regLocProvinceId) {
		this.regLocProvinceId = regLocProvinceId;
	}

	public Long getRegLocCityId() {
		return regLocCityId;
	}

	public void setRegLocCityId(Long regLocCityId) {
		this.regLocCityId = regLocCityId;
	}

	public Long getRegLocCountyId() {
		return regLocCountyId;
	}

	public void setRegLocCountyId(Long regLocCountyId) {
		this.regLocCountyId = regLocCountyId;
	}

	public String getWorkUnitCreditCode() {
		return workUnitCreditCode;
	}

	public void setWorkUnitCreditCode(String workUnitCreditCode) {
		this.workUnitCreditCode = workUnitCreditCode;
	}

	public Long getLiveCountyId() {
		return liveCountyId;
	}

	public void setLiveCountyId(Long liveCountyId) {
		this.liveCountyId = liveCountyId;
	}

	public String getLiveProvinceStr() {
		return liveProvinceStr;
	}

	public void setLiveProvinceStr(String liveProvinceStr) {
		this.liveProvinceStr = liveProvinceStr == null ? null : liveProvinceStr.trim();
	}

	public String getLiveCityStr() {
		return liveCityStr;
	}

	public void setLiveCityStr(String liveCityStr) {
		this.liveCityStr = liveCityStr == null ? null : liveCityStr.trim();
	}

	public String getLiveCountyStr() {
		return liveCountyStr;
	}

	public void setLiveCountyStr(String liveCountyStr) {
		this.liveCountyStr = liveCountyStr == null ? null : liveCountyStr.trim();
	}

	public List<Long> getDeptIdList() {
		return deptIdList;
	}

	public void setDeptIdList(List<Long> deptIdList) {
		this.deptIdList = deptIdList;
	}

	public String getMentorName() {
		return mentorName;
	}

	public void setMentorName(String mentorName) {
		this.mentorName = mentorName == null ? null : mentorName.trim();
	}

	public String getMentorUnit() {
		return mentorUnit;
	}

	public void setMentorUnit(String mentorUnit) {
		this.mentorUnit = mentorUnit == null ? null : mentorUnit.trim();
	}
	
}