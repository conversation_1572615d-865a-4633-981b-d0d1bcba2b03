package com.hys.zyy.manage.model;

import java.util.Date;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：张伟清 2012-03-19
 * 
 * 描述：住院医师四大经历 - 实习及临床轮转经历
 * 
 * 说明:
 */
public class ZyyUserExtendCycle extends ZyyBaseObject {

	private static final long serialVersionUID = -7924647291285784248L;

	/**
	 * 主键ID
	 */
	private Long id ;
	
	/**
	 * 用户ID
	 */
	private Long zyyUserId ;
	
	/**
	 * 医院名称
	 */
	private String hspName ;
	
	/**
	 * 医院等级
	 */
	private String hspLevel ;
	
	/**
	 * 科室信息
	 */
	private String division ;
	
	/**
	 * 开始时间
	 */
	private Date startDate ;
	
	/**
	 * 结束时间
	 */
	private Date endDate ;
	
	/**
	 * 证明人
	 */
	private String witness ;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getZyyUserId() {
		return zyyUserId;
	}

	public void setZyyUserId(Long zyyUserId) {
		this.zyyUserId = zyyUserId;
	}

	public String getHspName() {
		return hspName;
	}

	public void setHspName(String hspName) {
		this.hspName = hspName;
	}

	public String getHspLevel() {
		return hspLevel;
	}

	public void setHspLevel(String hspLevel) {
		this.hspLevel = hspLevel;
	}

	public String getDivision() {
		return division;
	}

	public void setDivision(String division) {
		this.division = division;
	}

	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	public String getWitness() {
		return witness;
	}

	public void setWitness(String witness) {
		this.witness = witness;
	}
}