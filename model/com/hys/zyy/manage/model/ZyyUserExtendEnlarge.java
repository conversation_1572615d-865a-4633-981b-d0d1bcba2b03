package com.hys.zyy.manage.model;

/**
 * 用户信息扩展表延伸
 * 
 * <AUTHOR> 2018-4-26 上午10:40:03
 */
public class ZyyUserExtendEnlarge extends ZyyBaseObject {

	/**
	 * 
	 */
	private static final long serialVersionUID = -4387878821126704868L;

	// 用户id
	private Long zyyUserId;
	// 医院级别
	private Integer hospitalGrade;
	private String hospitalGradeStr;
	// 医院等次
	private Integer hospitalLevel;
	private String hospitalLevelStr;
	// 医疗卫生机构类别
	private Long hospitalOrgCategory;
	// 医疗卫生机构隶属关系
	private Integer hospitalBelong;
	// 是否为对口支援计划住院医师
	private Integer supportPlan;
	// 对口支援计划住院医师送出单位
	private String supportPlanOutput;

	// 老学员审核拒绝原因
	private String failReason;
	// FIRST_APPLY_LICENSED_DOCTOR 首次报名执业医师考试时间
	// 固定值： 2016年度、2017年度、2018年度、2019年度、尚未报名
	private Integer firstApplyLicensedDoctor;

	public String getFailReason() {
		return failReason;
	}

	public void setFailReason(String failReason) {
		this.failReason = failReason;
	}

	public Long getZyyUserId() {
		return zyyUserId;
	}

	public void setZyyUserId(Long zyyUserId) {
		this.zyyUserId = zyyUserId;
	}

	public Integer getHospitalGrade() {
		return hospitalGrade;
	}

	public void setHospitalGrade(Integer hospitalGrade) {
		this.hospitalGrade = hospitalGrade;
	}

	public Integer getHospitalLevel() {
		return hospitalLevel;
	}

	public void setHospitalLevel(Integer hospitalLevel) {
		this.hospitalLevel = hospitalLevel;
	}

	public Long getHospitalOrgCategory() {
		return hospitalOrgCategory;
	}

	public void setHospitalOrgCategory(Long hospitalOrgCategory) {
		this.hospitalOrgCategory = hospitalOrgCategory;
	}

	public Integer getHospitalBelong() {
		return hospitalBelong;
	}

	public void setHospitalBelong(Integer hospitalBelong) {
		this.hospitalBelong = hospitalBelong;
	}

	public Integer getSupportPlan() {
		return supportPlan;
	}

	public void setSupportPlan(Integer supportPlan) {
		this.supportPlan = supportPlan;
	}

	public String getSupportPlanOutput() {
		return supportPlanOutput;
	}

	public void setSupportPlanOutput(String supportPlanOutput) {
		this.supportPlanOutput = supportPlanOutput;
	}

	public Integer getFirstApplyLicensedDoctor() {
		return firstApplyLicensedDoctor;
	}

	public void setFirstApplyLicensedDoctor(Integer firstApplyLicensedDoctor) {
		this.firstApplyLicensedDoctor = firstApplyLicensedDoctor;
	}

	public String getHospitalGradeStr() {
		return hospitalGradeStr;
	}

	public void setHospitalGradeStr(String hospitalGradeStr) {
		this.hospitalGradeStr = hospitalGradeStr;
	}

	public String getHospitalLevelStr() {
		return hospitalLevelStr;
	}

	public void setHospitalLevelStr(String hospitalLevelStr) {
		this.hospitalLevelStr = hospitalLevelStr;
	}

}
