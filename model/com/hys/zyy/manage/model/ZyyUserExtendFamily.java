package com.hys.zyy.manage.model;

import java.util.Date;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：张伟清 2012-03-19
 * 
 * 描述：住院医师四大经历 - 家庭成员
 * 
 * 说明:
 */
public class ZyyUserExtendFamily extends ZyyBaseObject {

	private static final long serialVersionUID = -4783058965779720889L;

	/**
	 * 主键ID
	 */
	private Long id ;
	
	/**
	 * 用户ID
	 */
	private Long zyyUserId ;
	
	/**
	 * 类别 1.配偶 2.其他 
	 */
	private Integer type ;
	
	/**
	 * 姓名
	 */
	private String name ;
	
	/**
	 * 民族
	 */
	private String nation ;
	
	/**
	 * 身份证号码
	 */
	private String idCardNumber ;
	
	/**
	 * 学历
	 */
	private String degree ;
	
	/**
	 * 政治面貌
	 */
	private String politicalLandscape ;
	
	/**
	 * 户口所在地
	 */
	private String accountAddress ;
	
	/**
	 * 工作单位
	 */
	private String workUnit ;
	
	/**
	 * 职称职务
	 */
	private String title ;
	
	/**
	 * 联系电话
	 */
	private String telphone ;
	
	/**
	 * 关系
	 */
	private String relation ;
	
	/**
	 * 出生日期
	 */
	private Date birthday ;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getZyyUserId() {
		return zyyUserId;
	}

	public void setZyyUserId(Long zyyUserId) {
		this.zyyUserId = zyyUserId;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getNation() {
		return nation;
	}

	public void setNation(String nation) {
		this.nation = nation;
	}

	public String getIdCardNumber() {
		return idCardNumber;
	}

	public void setIdCardNumber(String idCardNumber) {
		this.idCardNumber = idCardNumber;
	}

	public String getDegree() {
		return degree;
	}

	public void setDegree(String degree) {
		this.degree = degree;
	}

	public String getPoliticalLandscape() {
		return politicalLandscape;
	}

	public void setPoliticalLandscape(String politicalLandscape) {
		this.politicalLandscape = politicalLandscape;
	}

	public String getAccountAddress() {
		return accountAddress;
	}

	public void setAccountAddress(String accountAddress) {
		this.accountAddress = accountAddress;
	}

	public String getWorkUnit() {
		return workUnit;
	}

	public void setWorkUnit(String workUnit) {
		this.workUnit = workUnit;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getTelphone() {
		return telphone;
	}

	public void setTelphone(String telphone) {
		this.telphone = telphone;
	}

	public String getRelation() {
		return relation;
	}

	public void setRelation(String relation) {
		this.relation = relation;
	}

	public Date getBirthday() {
		return birthday;
	}

	public void setBirthday(Date birthday) {
		this.birthday = birthday;
	}
}