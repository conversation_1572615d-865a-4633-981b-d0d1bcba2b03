package com.hys.zyy.manage.model;

import java.util.Date;

import com.alibaba.fastjson.serializer.ValueFilter;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：张伟清 2012-03-19
 * 
 * 描述：住院医师四大经历 - 学习经历
 * 
 * 说明:
 */
public class ZyyUserExtendStudy extends ZyyBaseObject {

	private static final long serialVersionUID = 4839201642769709764L;

	/**
	 * 主键ID
	 */
	private Long id ;
	
	/**
	 * 用户ID
	 */
	private Long zyyUserId ;
	
	/**
	 * 学习开始时间
	 */
	private Date startDate;
	private String startDateStr;
	/**
	 * 学习结束时间
	 */
	private Date endDate;
	private String endDateStr;
	/**
	 * 学校名称
	 */
	private String schoolName ;
	/**
	 * 所学专业
	 */
	private String major ;
	
	/**
	 * 学位
	 */
	private String degree ;
	
	/**
	 * 证明人
	 */
	private String witness ;
	
	//学历
	private  String educational;
	/*
	 * 是否获得毕业证书（0：否；1：是）
	 */
	private Integer sfhdbyzs;
	/*
	 * 学历证书编号
	 */
	private String xlzsbh;
	/*
	 * 学历证书取得时间
	 */
	private Date xlzsqdsj;
	private String xlzsqdsjStr;
	/*
	 * 是否获得学位证书（0：否；1：是）
	 */
	private Integer sfhdxwzs;
	/*
	 * 学位类型
	 */
	private String xwlx;
	/*
	 * 学位证书编号
	 */
	private String xwzsbh;
	/*
	 * 学位证书取得时间
	 */
	private Date xwzsqdsj;
	private String xwzsqdsjStr;

	public ZyyUserExtendStudy() {
		super();
	}

	public String getEducational() {
		return educational;
	}

	public void setEducational(String educational) {
		this.educational = educational;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getZyyUserId() {
		return zyyUserId;
	}

	public void setZyyUserId(Long zyyUserId) {
		this.zyyUserId = zyyUserId;
	}

	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	public String getSchoolName() {
		return schoolName;
	}

	public void setSchoolName(String schoolName) {
		this.schoolName = schoolName;
	}

	public String getMajor() {
		return major;
	}

	public void setMajor(String major) {
		this.major = major;
	}

	public String getDegree() {
		return degree;
	}

	public void setDegree(String degree) {
		this.degree = degree;
	}

	public String getWitness() {
		return witness;
	}

	public void setWitness(String witness) {
		this.witness = witness;
	}

	public Integer getSfhdbyzs() {
		return sfhdbyzs;
	}

	public void setSfhdbyzs(Integer sfhdbyzs) {
		this.sfhdbyzs = sfhdbyzs;
	}

	public String getXlzsbh() {
		return xlzsbh;
	}

	public void setXlzsbh(String xlzsbh) {
		this.xlzsbh = xlzsbh;
	}

	public Date getXlzsqdsj() {
		return xlzsqdsj;
	}

	public void setXlzsqdsj(Date xlzsqdsj) {
		this.xlzsqdsj = xlzsqdsj;
	}

	public Integer getSfhdxwzs() {
		return sfhdxwzs;
	}

	public void setSfhdxwzs(Integer sfhdxwzs) {
		this.sfhdxwzs = sfhdxwzs;
	}

	public String getXwlx() {
		return xwlx;
	}

	public void setXwlx(String xwlx) {
		this.xwlx = xwlx;
	}

	public String getXwzsbh() {
		return xwzsbh;
	}

	public void setXwzsbh(String xwzsbh) {
		this.xwzsbh = xwzsbh;
	}

	public Date getXwzsqdsj() {
		return xwzsqdsj;
	}

	public void setXwzsqdsj(Date xwzsqdsj) {
		this.xwzsqdsj = xwzsqdsj;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

	public String getStartDateStr() {
		return startDateStr;
	}

	public void setStartDateStr(String startDateStr) {
		this.startDateStr = startDateStr;
	}

	public String getEndDateStr() {
		return endDateStr;
	}

	public void setEndDateStr(String endDateStr) {
		this.endDateStr = endDateStr;
	}

	public String getXlzsqdsjStr() {
		return xlzsqdsjStr;
	}

	public void setXlzsqdsjStr(String xlzsqdsjStr) {
		this.xlzsqdsjStr = xlzsqdsjStr;
	}

	public String getXwzsqdsjStr() {
		return xwzsqdsjStr;
	}

	public void setXwzsqdsjStr(String xwzsqdsjStr) {
		this.xwzsqdsjStr = xwzsqdsjStr;
	}
	
}