package com.hys.zyy.manage.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.hys.security.model.Resource;
import com.hys.zyy.manage.model.vo.ZyyAccountRelVO;

/**
 *
 * 标题：住院医师
 *
 * 作者：张伟清 2012-03-19
 *
 * 描述：学员用户扩展 VO
 *
 * 说明:
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class ZyyUserExtendVO extends ZyyUserExtend implements Comparable<ZyyUserExtendVO> {

	private static final long serialVersionUID = 6134423570362398356L;
	@JSONField(serialize = false)
	private Integer cycleRecords;
	/*
	 * 人员类型
	 */
	private String residencySourceStr;

	/**
	 * 组织机构ID编号
	 */
	private Long orgId;

	/**
	 * 组织机构名称
	 */
	private String orgName;

	private String orgCode;

	private String baseCode;

	private String university;

	private String lunZhuanSJStr; // 轮转时间字符串

	private Integer zyyUserStatus;
	@JSONField(serialize = false)
	private String zyyUserStatusStr;
	@JSONField(serialize = false)
	private String roleTypeStr;
	/**
	 * @desc 省名称
	 */
	private String provinceName;

	/**
	 * null 老学员、1 专硕生 、2 订单定向生
	 */
	private Integer graduateTargetType;

	//专硕生，订单定向 查询 报到情况使用
	private Integer zyyUserType2;

	//1：未学习 2：学习中 3：已完成
	private Integer studyState;

	private String residencyIds;

	private Long zyyDeptId;

	//zyy_base_residency的residencySource的值 1 -招录系统  2 -导入 3 -其他 4 -老学员注册 5 专硕 6订单定向
	private Integer residencySource2;

	private String sexStr;

	private String highestGraduateSchool;
	private String highestRecordSchoolStr;
	/**
	 * 审核用户类型：1为带教，2为科室，3为医院, 4导师 5 专业基地
	 */
	private Integer auditUserType;
	//分班上限	   大于等于1，上限10个班
	private Integer gradeMax;
	//分组上限	  大于等于1，上限20个组
	private Integer groupMax;
	//培养类型 str
	@JSONField(serialize = false)
	private String educationalTypeStr;
	//人员细分
	@JSONField(serialize = false)
	private String personDetailStr;
	// FIRST_APPLY_LICENSED_DOCTOR  首次报名执业医师考试时间
	// 固定值： 2016年度、2017年度、2018年度、2019年度、尚未报名
	@JSONField(serialize = false)
	private Integer firstApplyLicensedDoctor;
	@JSONField(serialize = false)
	private Integer residencyHospType;
	@JSONField(serialize = false)
	private String filePath;
	@JSONField(serialize = false)
	private String birthdayStr;
	@JSONField(serialize = false)
	private String firstRecordSchoolStr;
	@JSONField(serialize = false)
	private String trainingPeriodStr;
	@JSONField(serialize = false)
	private String dyxlbyyxlxStr;
	@JSONField(serialize = false)
	private String dyxlbyyxflStr;
	@JSONField(serialize = false)
	private String firstDegreeStr;
	@JSONField(serialize = false)
	private String firstSchoolSystemStr;
	@JSONField(serialize = false)
	private String isFreshStr;
	@JSONField(serialize = false)
	private String hospTypeStr;
	@JSONField(serialize = false)
	private String hasCertificateStr;
	@JSONField(serialize = false)
	private String getDateStr;
	@JSONField(serialize = false)
	private String supportPlanStr;
	@JSONField(serialize = false)
	private String supportPlanOutput;
	@JSONField(serialize = false)
	private String checkStatusStr;
	/**
	 * 第一学历是否是全日制
	 */
	private Integer fullTimeFrecord;
	/**
	 * 第一学历是否是在读
	 */
	private Integer isReadingFirstRecord;
	/**
	 * 第一学历证书获取时间
	 */
	private Date firstRecordCertificateTime;

	/**
	 * 第一学位证书获取时间
	 */
	private Date firstDegreeCertificateTime;

	/**
	 * 最高学历是否是全日制
	 */
	private Integer fullTimeGraduation;
	/**
	 * 最高学历是否是在读
	 */
	private Integer isReadingGraduation;
	/**
	 * 最高学历证书获取时间
	 */
	private Date graduationCertificateTime;
	/**
	 * 最高学位证书获取时间
	 */
	private Date hdegreeCertificateTime;
	/**
	 * 是否为西部支援行动住院医师
	 */
	private Integer isSupport;

	/**
	 * 西部支援行动住院医师送出省份
	 */
	private Long sendAddress;

	/**
	 * 西部支援行动住院医师接受省份
	 */
	private Long receiveAddress;

	/**
	 * 西部支援行动住院医师送出单位统一社会信用代码
	 */
	private String sendCreditCode;

	/**
	 * 西部支援行动住院医师接受单位统一社会信用代码
	 */
	private String receiveCreditCode;

	/**
	 * 证件号码
	 */
	private List<String> certificateNos;


	/**
	 * 培训阶段
	 */
	private String trainPhaseStr;

	/*
	 * 报名时间截止后，学员是否可以修改报名表的[是否服从调剂]
	 */
	@JSONField(serialize = false)
	private boolean canUpdateIsAdjust;

	public boolean isCanUpdateIsAdjust() {
		return canUpdateIsAdjust;
	}

	public void setCanUpdateIsAdjust(boolean canUpdateIsAdjust) {
		this.canUpdateIsAdjust = canUpdateIsAdjust;
	}

	public List<String> getCertificateNos() {
		return certificateNos;
	}

	public void setCertificateNos(List<String> certificateNos) {
		this.certificateNos = certificateNos;
	}

	public ZyyUserExtendVO() {
		super();
	}

	public ZyyUserExtendVO(String mobilNumber) {
		super(mobilNumber);
	}

	public String getHighestGraduateSchool() {
		return highestGraduateSchool;
	}

	public void setHighestGraduateSchool(String highestGraduateSchool) {
		this.highestGraduateSchool = highestGraduateSchool == null ? null : highestGraduateSchool.trim();
	}

	public String getHighestRecordSchoolStr() {
		return highestRecordSchoolStr;
	}

	public void setHighestRecordSchoolStr(String highestRecordSchoolStr) {
		this.highestRecordSchoolStr = highestRecordSchoolStr == null ? null : highestRecordSchoolStr.trim();
	}

	public String getSexStr() {
		return sexStr;
	}

	public void setSexStr(String sexStr) {
		this.sexStr = sexStr;
	}

	public Long getZyyDeptId() {
		return zyyDeptId;
	}

	public void setZyyDeptId(Long zyyDeptId) {
		this.zyyDeptId = zyyDeptId;
	}

	public String getResidencyIds() {
		return residencyIds;
	}

	public void setResidencyIds(String residencyIds) {
		this.residencyIds = residencyIds;
	}

	public Integer getStudyState() {
		return studyState;
	}

	public void setStudyState(Integer studyState) {
		this.studyState = studyState;
	}

	public Integer getZyyUserType2() {
		return zyyUserType2;
	}

	public void setZyyUserType2(Integer zyyUserType2) {
		this.zyyUserType2 = zyyUserType2;
	}

	public Integer getGraduateTargetType() {
		return graduateTargetType;
	}

	public void setGraduateTargetType(Integer graduateTargetType) {
		this.graduateTargetType = graduateTargetType;
	}

	//拒绝理由
	private String failMsg;

	public String getFailMsg() {
		return failMsg;
	}

	public void setFailMsg(String failMsg) {
		this.failMsg = failMsg;
	}

	public Integer getZyyUserStatus() {
		return zyyUserStatus;
	}

	public void setZyyUserStatus(Integer zyyUserStatus) {
		this.zyyUserStatus = zyyUserStatus;
	}

	public String getUniversity() {
		return university;
	}

	public void setUniversity(String university) {
		this.university = university;
	}

	/**
	 * 组织机构别名
	 */
	private String orgAliasName;

	//通过userId得到其所有轮转表下所有科室
	private List<ZyyDeptVO> allCycleDeptList;

	//一个学员会跨越多个轮转表,取出最新轮转表Id
	private Long lastCycleTableId;

	//用户类型名称
	private String userTypeName;

	/**
	 * 组织机构标识
	 */
	private Integer orgTypeFlag;

	/**
	 * 行跨度
	 */
	private Integer rowspan;

	/**
	 * 志愿ID
	 */
	private Long stageId;

	/**
	 * 志愿名称
	 */
	private String stageName;

	/**
	 * 招录志愿ID
	 */
	private Long recruitStageId;

	/**
	 * 招录志愿状态
	 */
	private Integer recruitWillStatus;

	/**
	 * 招录志愿ID
	 */
	private Long recruitWillId ;

	/**
	 * 志愿信息
	 */
	private Integer will;

	/**
	 * 页面显示志愿长度
	 */
	private Integer willLength ;

	/**
	 * 科室名称
	 */
	private String deptName;


	/**
	 * 基地ID
	 */
	private Long zyyBaseId ;

	/**
	 * 基地名称
	 */
	private String baseName ;

	/**
	 * 基地别名
	 */
	private String baseAliasName ;

	/**
	 * 科室ID
	 */
	private Long deptId ;

	/**
	 * 邮件发送时间
	 */
	private Date userSendDate;

	/**
	 * 年度ID
	 */
	private Long yearId;

	/**
	 * 用户id
	 */
	private Long accountId;

	/**
	 * 用户名称
	 */
	private String accountName;

	/**
	 * 密码
	 */
	private String accountPassword;

	/**
	 * 角色id
	 */
	private Long roleId;

	/**
	 * 角色姓名
	 */
	private String roleName;

	/**
	 * 学制(住院医师需要轮转时间) 1.一年制 2.二年制 3.三年制
	 */
	private Integer schoolSystem ;

	/**
	 * 入院考试 是否合格
	 */
	private Integer isExamPassed ;

	/**
	 * 阶段 1.一阶段 2.二阶段
	 */
	private Integer stage ;

	/**
	 * 延长毕业年数
	 */
	private Integer graduationYear ;

	/**
	 * 学员信息是否提交状态 0.保存 1.提交
	 */
	private Integer extendStatus ;

	/**
	 * 考生类别
	 */
	private Integer studentType ;
	/**
	 * 用户基地id
	 *
	 */
//	/**
//	 * 住院医师信息扩展 - 有无医师执业证书 1.有 0.无
//	 */
//	private Integer hasCert ;
//
//	/**
//	 * 住院医师信息扩展 - 医师执业证书编码
//	 */
//	private String certificateNumber ;
//
//	/**
//	 * 住院医师信息扩展 - 取得时间
//	 */
//	private Date certificateDate ;
//
//	/**
//	 * 住院医师信息扩展 - 证书执业范围
//	 */
//	private String certificateScope ;
	/**
	 * 住院医师信息扩展 - 是否修改 用于判断页面数据
	 */
	private Integer isUpdate ;

	/**
	 * 轮转开始时间
	 */
	private Date startDate ;
	private String startDateStr;

	/**
	 * 轮转结束时间
	 */
	private Date endDate ;
	private String endDateStr;

	/**
	 * 轮转类型
	 */
	private Integer cycleType ;

	/**
	 * 住院医师 - 学习经历
	 */
	private List<ZyyUserExtendStudy> studyList;

	/**
	 * 住院医师 - 轮转经历
	 */
	private List<ZyyUserExtendCycle> cycleList;

	/**
	 * 住院医师 - 家庭成员
	 */
	private List<ZyyUserExtendFamily> familyList;

	/**
	 * 住院医师 - 工作经历
	 */
	private List<ZyyUserExtendWork> workList;

	/**
	 * 住院医师 - 志愿信息
	 */
	private List<ZyyRecruitResidencyWillVO> willList;

	/**
	 * 住院医师 - 志愿信息 map
	 */
	private Map<Object, List<ZyyRecruitResidencyWillVO>> willMap;

	/**
	 * 住院医师 - 志愿信息 map
	 */
	private Map<Long, List<ZyyRecruitResidencyWillVO>> resiWillMap;

	/**
	 * 阶段列表
	 */
	private List<ZyyRecruitStageVO> stageList;

	/**
	 * 轮转表住院医师轮转时间
	 */
	private Map<Long, ZyyCycleTableResiTime> timeMap;

	/**
	 * 实际轮转时间列表
	 */
	private List<ZyyCycleTableResiTime> resiList;

	/**
	 * 学员轮转表
	 */
	private List<ZyyCycleTableResiCycleVO> resiCycleList;

	/**
	 * 学员轮转表 map
	 */
	private Map<Object, ZyyCycleTableResiCycleVO> cycleMap;

	/**
	 * 学员当前时间下的轮转信息
	 */
	private Map<Object, List<ZyyCycleTableResiCycleVO>> resiCycleMap;

	/**
	 * 科室轮转信息-轮转历史记录 map
	 */
	private Map<Object, List<ZyyResidencyCycle>> historyCycleMap;

	/**
	 * 轮转科室时间列表 map
	 */
	private Map<Long, List<ZyyCycleTableResiTime>> resiTimeMap;

	/**
	 * 住院医师报名录取资格记录 map
	 */
	private Map<Object, ZyyResiQualificationLog> qualLogMap;

	/**
	 * 住院医师轮转科室时间
	 */
	private Map<Long, ZyyDeptCycleTimeDetail> detailMap ;

	/**
	 * 住院医师报名录取资格记录 list
	 */
	private List<ZyyResiQualificationLog> logList ;

	/**
	 * 资源列表
	 */
	private List<Resource> resourceList;

	/**
	 * 用户列表
	 */
	private List<ZyyUserExtendVO> extendList;

	private List<ZyyUserExtendVO> records;

	/**
	 * 机构列表
	 */
	private List<ZyyOrgVO> orgList ;

	/**
	 * 学界
	 */
	private String yearName;

	/**
	 * 基地住院医师ID
	 */
	private Long zyyBaseResidency;

	/**
	 * 学员轮转的科室
	 */
	private List<ZyyDept> deptList;

	/**
	 * 住院医师报名录取资格ID
	 */
	private Long residentQualificationId ;

	/**
	 * 审核类别 1.报名资格审核 2.录取资格审核
	 */
	private Integer checkType ;


	private Integer cycleDeptNum = 0;

	/**
	 * 分页
	 */
	private Integer pageOffsetNum;

	/**
	 * 科室轮转信息
	 */
	private List<ZyyCycleTableResiCycle> deptcylelist = new ArrayList<ZyyCycleTableResiCycle>();

	/**
	 * 住院医师报名录取资格
	 */
	private ZyyResidentQualification qualificat ;

	/**
	 * 各省市地区信息
	 */
	private ZyyRegion region ;

	/**
	 * 流程对象 - 报名审核使用
	 */
	private ZyyProcessDetail detail ;

	/**
	 * 志愿信息
	 */
	private ZyyRecruitResidencyWillVO recWill ;

	/**
	 * 最终报名资格 1.-通过  0.不通过
	 */
	private Integer finalSignupQualification ;

	/**
	 * 最终录取资格 1.-通过  0.不通过
	 */
	private Integer finalAdmissionQualification ;

	//培训学届
	private Integer recruitYearIdBack;

	//学级id
	private Integer recruitYearId;
	//学级
	private String year;

	private String checkStatus;

	private String willStatus;

	private String certificateTypeDes;
	/**
	 * 培训年限描述
	 */
	private String schoolSystemDes;

	private String highestDegreeDes;
	/**
	 * 教学管理统计：活动类型列表
	 */
	private List<ZyyActivityTypeVO> actibityTypeList;

	/**
	 * 请假审核状态
	 */
	private Integer leaveAuditStatus;

	/**
	 * 请假带教审核状态
	 */
	private Integer teacherAuditStatus;

	/**
	 * 请假科室审核状态
	 */
	private Integer deptAuditStatus;

	/**
	 * 请假医院审核状态
	 */
	private Integer hospitalAuditStatus;
	/**
	 * 请假导师审核状态
	 */
	private Integer tutorAuditStatus;
	/**
	 * 请假基地审核状态
	 */
	private Integer baseAuditStatus;

	/**
	 * 准考证信息
	 */
	private List<ZyyResidencyAdmission> admissionList;

	/**
	 * 疾病病种填写率
	 */
	private Double diseaseFillRate;

	private Integer diseaseRealCount;

	private Integer diseaseRequireCount;

	/**
	 * 累积轮转时间
	 */
	private Integer cycleTimeCount;
	/**
	 * 终审状态：1，通过；2，未通过；3，未完成；4，未审核
	 */
	private Integer finalCheckStatus;
	/**
	 * 手册提交状态：1，已提交；2，未提交；3，部分提交
	 */
	private Integer commitStatus;

	/*
	 * 所在州（市）
	 */
	private String liveProvinceName;
	/*
	 * 所在县（区）
	 */
	private String liveCityName;
	/*
	 * 生源地-市
	 */
	private String homeProvinceName;
	/*
	 * 生源地-县
	 */
	private String homeCityName;
	@JSONField(serialize = false)
	private String regLocProvinceStr;
	@JSONField(serialize = false)
	private String regLocCityStr;
	@JSONField(serialize = false)
	private String regLocCountyStr;
	/*
	 * 用户信息扩展表延伸
	 */
	@JSONField(serialize = false)
	private ZyyUserExtendEnlarge zyyUserExtendEnlarge;
	/**
	 * 小程序openId(系统设置，特殊维护 搜索用到)
	 */
	private String weixinOpenid;
	@JSONField(serialize = false)
	private String timeSection;

	public String getTimeSection() {
		return timeSection;
	}

	public void setTimeSection(String timeSection) {
		this.timeSection = timeSection == null ? null : timeSection.trim();
	}

	public String getLiveProvinceName() {
		return liveProvinceName;
	}

	public void setLiveProvinceName(String liveProvinceName) {
		this.liveProvinceName = liveProvinceName;
	}

	public String getLiveCityName() {
		return liveCityName;
	}

	public void setLiveCityName(String liveCityName) {
		this.liveCityName = liveCityName;
	}

	public Integer getCycleTimeCount() {
		return cycleTimeCount;
	}

	public void setCycleTimeCount(Integer cycleTimeCount) {
		this.cycleTimeCount = cycleTimeCount;
	}

	public Double getDiseaseFillRate() {
		return diseaseFillRate;
	}

	public void setDiseaseFillRate(Double diseaseFillRate) {
		this.diseaseFillRate = diseaseFillRate;
	}

	public Integer getDiseaseRealCount() {
		return diseaseRealCount;
	}

	public void setDiseaseRealCount(Integer diseaseRealCount) {
		this.diseaseRealCount = diseaseRealCount;
	}

	public Integer getDiseaseRequireCount() {
		return diseaseRequireCount;
	}

	public void setDiseaseRequireCount(Integer diseaseRequireCount) {
		this.diseaseRequireCount = diseaseRequireCount;
	}

	public List<ZyyResidencyAdmission> getAdmissionList() {
		return admissionList;
	}

	public void setAdmissionList(List<ZyyResidencyAdmission> admissionList) {
		this.admissionList = admissionList;
	}

	public Integer getLeaveAuditStatus() {
		return leaveAuditStatus;
	}

	public void setLeaveAuditStatus(Integer leaveAuditStatus) {
		this.leaveAuditStatus = leaveAuditStatus;
	}


	public Integer getTeacherAuditStatus() {
		return teacherAuditStatus;
	}

	public void setTeacherAuditStatus(Integer teacherAuditStatus) {
		this.teacherAuditStatus = teacherAuditStatus;
	}

	public Integer getDeptAuditStatus() {
		return deptAuditStatus;
	}

	public void setDeptAuditStatus(Integer deptAuditStatus) {
		this.deptAuditStatus = deptAuditStatus;
	}

	public Integer getHospitalAuditStatus() {
		return hospitalAuditStatus;
	}

	public void setHospitalAuditStatus(Integer hospitalAuditStatus) {
		this.hospitalAuditStatus = hospitalAuditStatus;
	}

	public List<ZyyActivityTypeVO> getActibityTypeList() {
		return actibityTypeList;
	}

	public void setActibityTypeList(List<ZyyActivityTypeVO> actibityTypeList) {
		this.actibityTypeList = actibityTypeList;
	}

	public String getHighestDegreeDes() {
		return highestDegreeDes;
	}

	public void setHighestDegreeDes(String highestDegreeDes) {
		this.highestDegreeDes = highestDegreeDes;
	}

	public String getSchoolSystemDes() {
		return schoolSystemDes;
	}

	public void setSchoolSystemDes(String schoolSystemDes) {
		this.schoolSystemDes = schoolSystemDes;
	}

	public String getCertificateTypeDes() {
		return certificateTypeDes;
	}

	public void setCertificateTypeDes(String certificateTypeDes) {
		this.certificateTypeDes = certificateTypeDes;
	}

	public String getWillStatus() {
		return willStatus;
	}

	public void setWillStatus(String willStatus) {
		this.willStatus = willStatus;
	}

	public String getCheckStatus() {
		return checkStatus;
	}

	public void setCheckStatus(String checkStatus) {
		this.checkStatus = checkStatus;
	}

	public String getYear() {
		return year;
	}

	public void setYear(String year) {
		this.year = year;
	}

	public Integer getRecruitYearId() {
		return recruitYearId;
	}

	public void setRecruitYearId(Integer recruitYearId) {
		this.recruitYearId = recruitYearId;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

	/**
	 * 轮转科室时间表 主键ID
	 */
	private Long cycleTimeId ;

	/**
	 * 优先轮转科室表
	 */
	private Long cyclePriorityId ;

	/**
	 * 联排科室ID
	 */
	private String baseContinuityId ;

	private String userYear;

	private String userDeptName;


   /**
    * 以往结业考核记录（年月）
    */
    private Date beforeForCheckRecode;

	/**
	 * 志愿是否可以被操作
	 */
	private Integer willIsCanBeOperat;

	/**
	 * 学员被录取志愿
	 */
	private ZyyRecruitResidencyWillVO admitedBaseWill;

	/**
	 * 学员调剂志愿集合
	 */
	private Map<Long, ZyyRecruitResidencyWillVO> adjustedWillMap ;

	/**
	 * 学员录取志愿集合
	 */
	private Map<Long, ZyyRecruitResidencyWillVO> admitedWillMap ;

	/**
	 * 2013-4-3 添加学员轮转状态 -  -1 轮转结束 0 正在轮转 1 即将轮转
	 */
	private Integer zyyCycleStatus;

	/**
	 * 科室带教
	 * 医师轮转列表
	 */
	private Map<String, ZyyCycleTableResiCycleVO> deptCycleMap;

	/**
	 * 科室带教 学员用户
	 * 解决可能出现的医师轮转时间有间隔中断的问题
	 */
	private List<ZyyCycleTeacherVO> userCycleList;

	/**
	 * 科室带教
	 * 医师的带教老师列表
	 */
	private Map<String, List<ZyyCycleTeacher>> userTeacherMap;

	/**
	 * 学员终审记录
	 */
	private List<ZyyResidencyHandbookHosp> finalCheckResult;


	private List<ZyyUserCheckManual> zyyUserCheckList;

	private Integer isfreestu;

	/**
	 * 志愿是否可以被操作  2不可操作
	 */
	private Integer willIsUpdate;

	private Integer directStu;

	private int deptLevel;

	public int getDeptLevel() {
		return deptLevel;
	}

	public void setDeptLevel(int deptLevel) {
		this.deptLevel = deptLevel;
	}

	//医师电子档案职务
	private String duty;

	private String mobilNumber;

	private  Date graduateDate;

	//带教状态 带教：1  非带教：2
	private int deptStatus;
	//带教老师 工号
	private String workNumber;
	/*
	 * 带教职称
	 */
	@JSONField(serialize = false)
	private String teacherTitleName;

	public int getDeptStatus() {
		return deptStatus;
	}

	public void setDeptStatus(int deptStatus) {
		this.deptStatus = deptStatus;
	}

	public String getWorkNumber() {
		return workNumber;
	}

	public void setWorkNumber(String workNumber) {
		this.workNumber = workNumber;
	}

	/**
	 * 带教老师累计带教年限
	 */
	private Integer teacherYears;

	/**
	 * 带教老师累计带教人数
	 */
	private Integer residencyCount;

	/**
	 * 同步用户ID
	 */
	private String syncId;

	/**
	 * 用户同步状态
	 */
	private Integer syncStatus;
	/**
	 * 用户同步时间
	 */
	private Date syncUpdateDate;

	/**
	 * 同步用户教育信息ID
	 */
	private String eduSyncId;

	/**
	 * 同步用户教育信息学历类型
	 */
	private Integer eduType;
	/***********招录接口需要***************/
	/**
	 * 同步用户招录ID
	 */
	private String synRecruitcId;
	/**
	 * 用户同步招录状态
	 */
	private Integer syncRecruitStatus;

	/**
	 * 同步用户结业考核报考信息信息ID
	 */
	private String syncExamineeId;

	/**
	 * 同步用户结业考核报考考核类型
	 */
	private Integer examType;

	/**
	 * 同步用户结业考核报考考试ID
	 */
	private Long examId;

	/**
	 * 同步用户结业考核报考理论考试报考次数
	 */
	private Integer theoryExamineeNum;

	/**
	 * 同步用户结业考核报考技能考试报考次数
	 */
	private Integer skillExamineeNum;

	/**
	 * 同步用户结业考核报考理论考核年度
	 */
	private String theoryExamineeYear;

	/**
	 * 同步用户结业考核报考技能考核年度
	 */
	private String skillExamineeYear;

	/**
	 * 人员属性  1地方人员 2军队文职人员
	 */
	private Integer residencyAttribute;
	/**
	 * 人员类型其他
	 */
	private String sourceAdditional;
	/**
	 * 实际培训开始时间
	 */
	private Date trainStartDate;
	/**
	 * 实际培训结束时间
	 */
	private Date trainEndDate;

	private Integer degreeConvergence;
	private Long provinceCheckStatus;
	private Long baseCheckStatus;

	private String applyDate;

	private ZyyPlayRecordVo zyyPlayRecordVo;

	/**APP审核登记手册**/
	private Long deptStdId;//标准科室ID
	//手册审核记录ID
	private Long chkId;

	private String cycleStartDateStr;
	private String cycleEndDateStr;
	@JSONField(serialize = false)
	private Integer isDestroy;

	public Integer getIsDestroy() {
		return isDestroy;
	}

	public void setIsDestroy(Integer isDestroy) {
		this.isDestroy = isDestroy;
	}

	public String getCycleStartDateStr() {
		return cycleStartDateStr;
	}

	public void setCycleStartDateStr(String cycleStartDateStr) {
		this.cycleStartDateStr = cycleStartDateStr;
	}

	public String getCycleEndDateStr() {
		return cycleEndDateStr;
	}

	public void setCycleEndDateStr(String cycleEndDateStr) {
		this.cycleEndDateStr = cycleEndDateStr;
	}

	/**
	 * @desc 切换用户显示的角色
	 */
	private List<ZyyAccountRelVO> relVoList = new ArrayList<ZyyAccountRelVO>();


	public String getApplyDate() {
		return applyDate;
	}

	public void setApplyDate(String applyDate) {
		this.applyDate = applyDate;
	}

	public Long getBaseCheckStatus() {
		return baseCheckStatus;
	}

	public void setBaseCheckStatus(Long baseCheckStatus) {
		this.baseCheckStatus = baseCheckStatus;
	}

	public Integer getDegreeConvergence() {
		return degreeConvergence;
	}

	public void setDegreeConvergence(Integer degreeConvergence) {
		this.degreeConvergence = degreeConvergence;
	}

	public String getSynRecruitcId() {
		return synRecruitcId;
	}

	public void setSynRecruitcId(String synRecruitcId) {
		this.synRecruitcId = synRecruitcId;
	}

	public Integer getSyncRecruitStatus() {
		return syncRecruitStatus;
	}

	public void setSyncRecruitStatus(Integer syncRecruitStatus) {
		this.syncRecruitStatus = syncRecruitStatus;
	}

	public Integer getResidencyAttribute() {
		return residencyAttribute;
	}

	public void setResidencyAttribute(Integer residencyAttribute) {
		this.residencyAttribute = residencyAttribute;
	}

	public String getSourceAdditional() {
		return sourceAdditional;
	}

	public void setSourceAdditional(String sourceAdditional) {
		this.sourceAdditional = sourceAdditional;
	}

	public Date getTrainStartDate() {
		return trainStartDate;
	}

	public void setTrainStartDate(Date trainStartDate) {
		this.trainStartDate = trainStartDate;
	}

	public Date getTrainEndDate() {
		return trainEndDate;
	}

	public void setTrainEndDate(Date trainEndDate) {
		this.trainEndDate = trainEndDate;
	}

	public Integer getTeacherYears() {
		return teacherYears;
	}

	public void setTeacherYears(Integer teacherYears) {
		this.teacherYears = teacherYears;
	}

	public Integer getResidencyCount() {
		return residencyCount;
	}

	public void setResidencyCount(Integer residencyCount) {
		this.residencyCount = residencyCount;
	}

	public Date getGraduateDate() {
		return graduateDate;
	}

	public void setGraduateDate(Date graduateDate) {
		this.graduateDate = graduateDate;
	}

	public String getDuty() {
		return duty;
	}

	public String getMobilNumber() {
		return mobilNumber;
	}

	public void setMobilNumber(String mobilNumber) {
		this.mobilNumber = mobilNumber == null ? null : mobilNumber.trim();
	}

	public void setDuty(String duty) {
		this.duty = duty;
	}

	public Integer getDirectStu() {
		return directStu;
	}

	public void setDirectStu(Integer directStu) {
		this.directStu = directStu;
	}

	public Integer getWillIsUpdate() {
		return willIsUpdate;
	}

	public void setWillIsUpdate(Integer willIsUpdate) {
		this.willIsUpdate = willIsUpdate;
	}

	public Integer getIsfreestu() {
		return isfreestu;
	}

	public void setIsfreestu(Integer isfreestu) {
		this.isfreestu = isfreestu;
	}

	public ZyyRecruitResidencyWillVO getAdmitedBaseWill() {
		return admitedBaseWill;
	}

	public void setAdmitedBaseWill(ZyyRecruitResidencyWillVO admitedBaseWill) {
		this.admitedBaseWill = admitedBaseWill;
	}

	public Map<Long, ZyyRecruitResidencyWillVO> getAdmitedWillMap() {
		return admitedWillMap;
	}

	public void setAdmitedWillMap(
			Map<Long, ZyyRecruitResidencyWillVO> admitedWillMap) {
		this.admitedWillMap = admitedWillMap;
	}

	public Map<Long, ZyyRecruitResidencyWillVO> getAdjustedWillMap() {
		return adjustedWillMap;
	}

	public void setAdjustedWillMap(
			Map<Long, ZyyRecruitResidencyWillVO> adjustedWillMap) {
		this.adjustedWillMap = adjustedWillMap;
	}

	public Integer getWillIsCanBeOperat() {
		return willIsCanBeOperat;
	}

	public void setWillIsCanBeOperat(Integer willIsCanBeOperat) {
		this.willIsCanBeOperat = willIsCanBeOperat;
	}

	public Integer getRecruitYearIdBack() {
		return recruitYearIdBack;
	}

	public void setRecruitYearIdBack(Integer recruitYearIdBack) {
		this.recruitYearIdBack = recruitYearIdBack;
	}

	public Integer getFinalAdmissionQualification() {
		return finalAdmissionQualification;
	}

	public Integer getFinalSignupQualification() {
		return finalSignupQualification;
	}

	public void setFinalAdmissionQualification(Integer finalAdmissionQualification) {
		this.finalAdmissionQualification = finalAdmissionQualification;
	}

	public void setFinalSignupQualification(Integer finalSignupQualification) {
		this.finalSignupQualification = finalSignupQualification;
	}

	public ZyyRegion getRegion() {
		return region;
	}

	public void setRegion(ZyyRegion region) {
		this.region = region;
	}

	public ZyyResidentQualification getQualificat() {
		return qualificat;
	}

	public void setQualificat(ZyyResidentQualification qualificat) {
		this.qualificat = qualificat;
	}

	public List<ZyyDept> getDeptList() {
		return deptList;
	}

	public void setDeptList(List<ZyyDept> deptList) {
		this.deptList = deptList;
	}

	public String getYearName() {
		return yearName;
	}

	public void setYearName(String yearName) {
		this.yearName = yearName;
	}

	public Long getZyyBaseResidency() {
		return zyyBaseResidency;
	}

	public void setZyyBaseResidency(Long zyyBaseResidency) {
		this.zyyBaseResidency = zyyBaseResidency;
	}

	public List<Resource> getResourceList() {
		return resourceList;
	}

	public void setResourceList(List<Resource> resourceList) {
		this.resourceList = resourceList;
	}

	public List<ZyyUserExtendStudy> getStudyList() {
		return studyList;
	}

	public void setStudyList(List<ZyyUserExtendStudy> studyList) {
		this.studyList = studyList;
	}

	public List<ZyyUserExtendCycle> getCycleList() {
		return cycleList;
	}

	public void setCycleList(List<ZyyUserExtendCycle> cycleList) {
		this.cycleList = cycleList;
	}

	public List<ZyyUserExtendFamily> getFamilyList() {
		return familyList;
	}

	public void setFamilyList(List<ZyyUserExtendFamily> familyList) {
		this.familyList = familyList;
	}

	public List<ZyyUserExtendWork> getWorkList() {
		return workList;
	}

	public void setWorkList(List<ZyyUserExtendWork> workList) {
		this.workList = workList;
	}

	public List<ZyyRecruitResidencyWillVO> getWillList() {
		return willList;
	}

	public void setWillList(List<ZyyRecruitResidencyWillVO> willList) {
		this.willList = willList;
	}

	public List<ZyyRecruitStageVO> getStageList() {
		return stageList;
	}

	public void setStageList(List<ZyyRecruitStageVO> stageList) {
		this.stageList = stageList;
	}

	public Integer getRowspan() {
		return rowspan;
	}

	public void setRowspan(Integer rowspan) {
		this.rowspan = rowspan;
	}

	public Long getStageId() {
		return stageId;
	}

	public void setStageId(Long stageId) {
		this.stageId = stageId;
	}

	public String getStageName() {
		return stageName;
	}

	public void setStageName(String stageName) {
		this.stageName = stageName;
	}

	public Long getRecruitStageId() {
		return recruitStageId;
	}

	public void setRecruitStageId(Long recruitStageId) {
		this.recruitStageId = recruitStageId;
	}

	public Integer getWill() {
		return will;
	}

	public void setWill(Integer will) {
		this.will = will;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public Long getYearId() {
		return yearId;
	}

	public void setYearId(Long yearId) {
		this.yearId = yearId;
	}

	public Integer getRecruitWillStatus() {
		return recruitWillStatus;
	}

	public void setRecruitWillStatus(Integer recruitWillStatus) {
		this.recruitWillStatus = recruitWillStatus;
	}

	public Map<Long, ZyyCycleTableResiTime> getTimeMap() {
		return timeMap;
	}

	public void setTimeMap(Map<Long, ZyyCycleTableResiTime> timeMap) {
		this.timeMap = timeMap;
	}

	public String getOrgAliasName() {
		return orgAliasName;
	}

	public void setOrgAliasName(String orgAliasName) {
		this.orgAliasName = orgAliasName;
	}

	public List<ZyyCycleTableResiTime> getResiList() {
		return resiList;
	}

	public void setResiList(List<ZyyCycleTableResiTime> resiList) {
		this.resiList = resiList;
	}

	public List<ZyyCycleTableResiCycleVO> getResiCycleList() {
		return resiCycleList;
	}

	public void setResiCycleList(List<ZyyCycleTableResiCycleVO> resiCycleList) {
		this.resiCycleList = resiCycleList;
	}

	public Map<Object, ZyyCycleTableResiCycleVO> getCycleMap() {
		return cycleMap;
	}

	public void setCycleMap(Map<Object, ZyyCycleTableResiCycleVO> cycleMap) {
		this.cycleMap = cycleMap;
	}

	public String getAccountPassword() {
		return accountPassword;
	}

	public void setAccountPassword(String accountPassword) {
		this.accountPassword = accountPassword;
	}

	public Integer getOrgTypeFlag() {
		return orgTypeFlag;
	}

	public void setOrgTypeFlag(Integer orgTypeFlag) {
		this.orgTypeFlag = orgTypeFlag;
	}

	public Date getUserSendDate() {
		return userSendDate;
	}

	public void setUserSendDate(Date userSendDate) {
		this.userSendDate = userSendDate;
	}

	public String getAccountName() {
		return accountName;
	}

	public void setAccountName(String accountName) {
		this.accountName = accountName;
	}

	public Long getOrgId() {
		return orgId;
	}

	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}

	public String getRoleName() {
		return roleName;
	}

	public void setRoleName(String roleName) {
		this.roleName = roleName;
	}

	public Integer getCycleRecords() {
		return cycleRecords;
	}

	public void setCycleRecords(Integer cycleRecords) {
		this.cycleRecords = cycleRecords;
	}

	public Long getAccountId() {
		return accountId;
	}

	public void setAccountId(Long accountId) {
		this.accountId = accountId;
	}

	public Long getRoleId() {
		return roleId;
	}

	public void setRoleId(Long roleId) {
		this.roleId = roleId;
	}

	public String  getDeptName() {
		return deptName;
	}

	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}

	public Integer getSchoolSystem() {
		return schoolSystem;
	}

	public void setSchoolSystem(Integer schoolSystem) {
		this.schoolSystem = schoolSystem;
	}

	public Integer getStage() {
		return stage;
	}

	public void setStage(Integer stage) {
		this.stage = stage;
	}

	public Integer getGraduationYear() {
		return graduationYear;
	}

	public void setGraduationYear(Integer graduationYear) {
		this.graduationYear = graduationYear;
	}

	public String getBaseName() {
		return baseName;
	}

	public void setBaseName(String baseName) {
		this.baseName = baseName;
	}

	public String getBaseAliasName() {
		return baseAliasName;
	}

	public void setBaseAliasName(String baseAliasName) {
		this.baseAliasName = baseAliasName;
	}

	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	public Long getDeptId() {
		return deptId;
	}

	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}

	public Integer getExtendStatus() {
		return extendStatus;
	}

	public void setExtendStatus(Integer extendStatus) {
		this.extendStatus = extendStatus;
	}

	public List<ZyyUserExtendVO> getExtendList() {
		return extendList;
	}

	public void setExtendList(List<ZyyUserExtendVO> extendList) {
		this.extendList = extendList;
	}

	public List<ZyyUserExtendVO> getRecords() {
		return records;
	}

	public void setRecords(List<ZyyUserExtendVO> records) {
		this.records = records;
	}

	public Integer getIsExamPassed() {
		return isExamPassed;
	}

	public void setIsExamPassed(Integer isExamPassed) {
		this.isExamPassed = isExamPassed;
	}

	public Map<Long, List<ZyyCycleTableResiTime>> getResiTimeMap() {
		return resiTimeMap;
	}

	public void setResiTimeMap(Map<Long, List<ZyyCycleTableResiTime>> resiTimeMap) {
		this.resiTimeMap = resiTimeMap;
	}

	public Map<Object, List<ZyyCycleTableResiCycleVO>> getResiCycleMap() {
		return resiCycleMap;
	}

	public void setResiCycleMap(
			Map<Object, List<ZyyCycleTableResiCycleVO>> resiCycleMap) {
		this.resiCycleMap = resiCycleMap;
	}

	public Long getResidentQualificationId() {
		return residentQualificationId;
	}

	public void setResidentQualificationId(Long residentQualificationId) {
		this.residentQualificationId = residentQualificationId;
	}

	public Integer getCheckType() {
		return checkType;
	}

	public void setCheckType(Integer checkType) {
		this.checkType = checkType;
	}

	public Map<Object, ZyyResiQualificationLog> getQualLogMap() {
		return qualLogMap;
	}

	public void setQualLogMap(Map<Object, ZyyResiQualificationLog> qualLogMap) {
		this.qualLogMap = qualLogMap;
	}

	public List<ZyyCycleTableResiCycle> getDeptcylelist() {
		return deptcylelist;
	}

	public void setDeptcylelist(List<ZyyCycleTableResiCycle> deptcylelist) {
		this.deptcylelist = deptcylelist;
	}

	public Integer getCycleDeptNum() {
		return cycleDeptNum;
	}

	public void setCycleDeptNum(Integer cycleDeptNum) {
		this.cycleDeptNum = cycleDeptNum;
	}

	public Map<Object, List<ZyyRecruitResidencyWillVO>> getWillMap() {
		return willMap;
	}

	public void setWillMap(Map<Object, List<ZyyRecruitResidencyWillVO>> willMap) {
		this.willMap = willMap;
	}

	public Map<Long, List<ZyyRecruitResidencyWillVO>> getResiWillMap() {
		return resiWillMap;
	}

	public void setResiWillMap(Map<Long, List<ZyyRecruitResidencyWillVO>> resiWillMap) {
		this.resiWillMap = resiWillMap;
	}

	public Integer getPageOffsetNum() {
		return pageOffsetNum;
	}

	public void setPageOffsetNum(Integer pageOffsetNum) {
		this.pageOffsetNum = pageOffsetNum;
	}

	public Integer getIsUpdate() {
		return isUpdate;
	}

	public void setIsUpdate(Integer isUpdate) {
		this.isUpdate = isUpdate;
	}

	public Integer getWillLength() {
		return willLength;
	}

	public void setWillLength(Integer willLength) {
		this.willLength = willLength;
	}

	public Long getRecruitWillId() {
		return recruitWillId;
	}

	public void setRecruitWillId(Long recruitWillId) {
		this.recruitWillId = recruitWillId;
	}

	public List<ZyyResiQualificationLog> getLogList() {
		return logList;
	}

	public void setLogList(List<ZyyResiQualificationLog> logList) {
		this.logList = logList;
	}

	public ZyyProcessDetail getDetail() {
		return detail;
	}

	public void setDetail(ZyyProcessDetail detail) {
		this.detail = detail;
	}

	public List<ZyyOrgVO> getOrgList() {
		return orgList;
	}

	public void setOrgList(List<ZyyOrgVO> orgList) {
		this.orgList = orgList;
	}

	public ZyyRecruitResidencyWillVO getRecWill() {
		return recWill;
	}

	public void setRecWill(ZyyRecruitResidencyWillVO recWill) {
		this.recWill = recWill;
	}

	public Map<Long, ZyyDeptCycleTimeDetail> getDetailMap() {
		return detailMap;
	}

	public void setDetailMap(Map<Long, ZyyDeptCycleTimeDetail> detailMap) {
		this.detailMap = detailMap;
	}

	public Long getCycleTimeId() {
		return cycleTimeId;
	}

	public void setCycleTimeId(Long cycleTimeId) {
		this.cycleTimeId = cycleTimeId;
	}

	public Long getCyclePriorityId() {
		return cyclePriorityId;
	}

	public void setCyclePriorityId(Long cyclePriorityId) {
		this.cyclePriorityId = cyclePriorityId;
	}

	public String getUserYear() {
		return userYear;
	}

	public void setUserYear(String userYear) {
		this.userYear = userYear;
	}

	public String getUserDeptName() {
		return userDeptName;
	}

	public void setUserDeptName(String userDeptName) {
		this.userDeptName = userDeptName;
	}

	public List<ZyyDeptVO> getAllCycleDeptList() {
		return allCycleDeptList;
	}

	public void setAllCycleDeptList(List<ZyyDeptVO> allCycleDeptList) {
		this.allCycleDeptList = allCycleDeptList;
	}

	public Integer getCycleType() {
		return cycleType;
	}

	public void setCycleType(Integer cycleType) {
		this.cycleType = cycleType;
	}

	public String getBaseContinuityId() {
		return baseContinuityId;
	}

	public void setBaseContinuityId(String baseContinuityId) {
		this.baseContinuityId = baseContinuityId;
	}

	public Map<Object, List<ZyyResidencyCycle>> getHistoryCycleMap() {
		return historyCycleMap;
	}

	public void setHistoryCycleMap(
			Map<Object, List<ZyyResidencyCycle>> historyCycleMap) {
		this.historyCycleMap = historyCycleMap;
	}



	public Integer getZyyCycleStatus() {
		return zyyCycleStatus;
	}

	public void setZyyCycleStatus(Integer zyyCycleStatus) {
		this.zyyCycleStatus = zyyCycleStatus;
	}

	public Map<String, ZyyCycleTableResiCycleVO> getDeptCycleMap() {
		return deptCycleMap;
	}

	public void setDeptCycleMap(Map<String, ZyyCycleTableResiCycleVO> deptCycleMap) {
		this.deptCycleMap = deptCycleMap;
	}

	public Map<String, List<ZyyCycleTeacher>> getUserTeacherMap() {
		return userTeacherMap;
	}

	public void setUserTeacherMap(Map<String, List<ZyyCycleTeacher>> userTeacherMap) {
		this.userTeacherMap = userTeacherMap;
	}

	public List<ZyyCycleTeacherVO> getUserCycleList() {
		return userCycleList;
	}

	public void setUserCycleList(List<ZyyCycleTeacherVO> userCycleList) {
		this.userCycleList = userCycleList;
	}

	public Long getLastCycleTableId() {
		return lastCycleTableId;
	}

	public void setLastCycleTableId(Long lastCycleTableId) {
		this.lastCycleTableId = lastCycleTableId;
	}

	public void setFinalCheckResult(List<ZyyResidencyHandbookHosp> results) {
		this.finalCheckResult = results;
	}

	public List<ZyyResidencyHandbookHosp> getFinalCheckResult() {
		return finalCheckResult;
	}

	public String getUserTypeName() {
		return userTypeName;
	}

	public void setUserTypeName(String userTypeName) {
		this.userTypeName = userTypeName;
	}

	public List<ZyyUserCheckManual> getZyyUserCheckList() {
		return zyyUserCheckList;
	}

	public void setZyyUserCheckList(List<ZyyUserCheckManual> zyyUserCheckList) {
		this.zyyUserCheckList = zyyUserCheckList;
	}

	public Integer getStudentType() {
		return studentType;
	}

	public void setStudentType(Integer studentType) {
		this.studentType = studentType;
	}

	public Date getBeforeForCheckRecode() {
		return beforeForCheckRecode;
	}

	public void setBeforeForCheckRecode(Date beforeForCheckRecode) {
		this.beforeForCheckRecode = beforeForCheckRecode;
	}

	public Long getZyyBaseId() {
		return zyyBaseId;
	}

	public String getLunZhuanSJStr() {
		return lunZhuanSJStr;
	}

	public void setLunZhuanSJStr(String lunZhuanSJStr) {
		this.lunZhuanSJStr = lunZhuanSJStr;
	}

	public void setZyyBaseId(Long zyyBaseId) {
		this.zyyBaseId = zyyBaseId;
	}

	public String getOrgCode() {
		return orgCode;
	}

	public void setOrgCode(String orgCode) {
		this.orgCode = orgCode;
	}

	public String getBaseCode() {
		return baseCode;
	}

	public void setBaseCode(String baseCode) {
		this.baseCode = baseCode;
	}

	public Integer getFinalCheckStatus() {
		return finalCheckStatus;
	}

	public void setFinalCheckStatus(Integer finalCheckStatus) {
		this.finalCheckStatus = finalCheckStatus;
	}

	public Integer getCommitStatus() {
		return commitStatus;
	}

	public void setCommitStatus(Integer commitStatus) {
		this.commitStatus = commitStatus;
	}

	@Override
	public int compareTo(ZyyUserExtendVO o) {
		return (int)(this.getId()-o.getId());
	}

	public String getSyncId() {
		return syncId;
	}

	public void setSyncId(String syncId) {
		this.syncId = syncId;
	}

	public Integer getSyncStatus() {
		return syncStatus;
	}

	public void setSyncStatus(Integer syncStatus) {
		this.syncStatus = syncStatus;
	}

	public String getEduSyncId() {
		return eduSyncId;
	}

	public void setEduSyncId(String eduSyncId) {
		this.eduSyncId = eduSyncId;
	}

	public Integer getEduType() {
		return eduType;
	}

	public void setEduType(Integer eduType) {
		this.eduType = eduType;
	}

	public String getSyncExamineeId() {
		return syncExamineeId;
	}

	public void setSyncExamineeId(String syncExamineeId) {
		this.syncExamineeId = syncExamineeId;
	}

	public Integer getExamType() {
		return examType;
	}

	public void setExamType(Integer examType) {
		this.examType = examType;
	}

	public Integer getTheoryExamineeNum() {
		return theoryExamineeNum;
	}

	public void setTheoryExamineeNum(Integer theoryExamineeNum) {
		this.theoryExamineeNum = theoryExamineeNum;
	}

	public Integer getSkillExamineeNum() {
		return skillExamineeNum;
	}

	public void setSkillExamineeNum(Integer skillExamineeNum) {
		this.skillExamineeNum = skillExamineeNum;
	}

	public Long getExamId() {
		return examId;
	}

	public void setExamId(Long examId) {
		this.examId = examId;
	}

	public String getTheoryExamineeYear() {
		return theoryExamineeYear;
	}

	public void setTheoryExamineeYear(String theoryExamineeYear) {
		this.theoryExamineeYear = theoryExamineeYear;
	}

	public String getSkillExamineeYear() {
		return skillExamineeYear;
	}

	public void setSkillExamineeYear(String skillExamineeYear) {
		this.skillExamineeYear = skillExamineeYear;
	}

	public String getProvinceName() {
		return provinceName;
	}

	public void setProvinceName(String provinceName) {
		this.provinceName = provinceName;
	}

	public String getHomeProvinceName() {
		return homeProvinceName;
	}

	public void setHomeProvinceName(String homeProvinceName) {
		this.homeProvinceName = homeProvinceName;
	}

	public String getHomeCityName() {
		return homeCityName;
	}

	public void setHomeCityName(String homeCityName) {
		this.homeCityName = homeCityName;
	}

	public Date getSyncUpdateDate() {
		return syncUpdateDate;
	}

	public void setSyncUpdateDate(Date syncUpdateDate) {
		this.syncUpdateDate = syncUpdateDate;
	}

	public String getResidencySourceStr() {
		return residencySourceStr;
	}

	public void setResidencySourceStr(String residencySourceStr) {
		this.residencySourceStr = residencySourceStr;
	}

	public String getStartDateStr() {
		return startDateStr;
	}

	public void setStartDateStr(String startDateStr) {
		this.startDateStr = startDateStr;
	}

	public String getEndDateStr() {
		return endDateStr;
	}

	public void setEndDateStr(String endDateStr) {
		this.endDateStr = endDateStr;
	}
	public Long getDeptStdId() {
		return deptStdId;
	}

	public void setDeptStdId(Long deptStdId) {
		this.deptStdId = deptStdId;
	}

	public Long getChkId() {
		return chkId;
	}

	public void setChkId(Long chkId) {
		this.chkId = chkId;
	}

	public List<ZyyAccountRelVO> getRelVoList() {
		return relVoList;
	}

	public void setRelVoList(List<ZyyAccountRelVO> relVoList) {
		this.relVoList = relVoList;
	}

	public ZyyPlayRecordVo getZyyPlayRecordVo() {
		return zyyPlayRecordVo;
	}

	public void setZyyPlayRecordVo(ZyyPlayRecordVo zyyPlayRecordVo) {
		this.zyyPlayRecordVo = zyyPlayRecordVo;
	}

	public Integer getTutorAuditStatus() {
		return tutorAuditStatus;
	}

	public void setTutorAuditStatus(Integer tutorAuditStatus) {
		this.tutorAuditStatus = tutorAuditStatus;
	}

	public Integer getBaseAuditStatus() {
		return baseAuditStatus;
	}

	public void setBaseAuditStatus(Integer baseAuditStatus) {
		this.baseAuditStatus = baseAuditStatus;
	}

	public Integer getResidencySource2() {
		return residencySource2;
	}

	public void setResidencySource2(Integer residencySource2) {
		this.residencySource2 = residencySource2;
	}

	public Integer getAuditUserType() {
		return auditUserType;
	}

	public void setAuditUserType(Integer auditUserType) {
		this.auditUserType = auditUserType;
	}

	public Integer getGradeMax() {
		return gradeMax;
	}

	public void setGradeMax(Integer gradeMax) {
		this.gradeMax = gradeMax;
	}

	public Integer getGroupMax() {
		return groupMax;
	}

	public void setGroupMax(Integer groupMax) {
		this.groupMax = groupMax;
	}

	public String getEducationalTypeStr() {
		return educationalTypeStr;
	}

	public void setEducationalTypeStr(String educationalTypeStr) {
		this.educationalTypeStr = educationalTypeStr;
	}

	public String getPersonDetailStr() {
		return personDetailStr;
	}

	public void setPersonDetailStr(String personDetailStr) {
		this.personDetailStr = personDetailStr;
	}

	public String getTeacherTitleName() {
		return teacherTitleName;
	}

	public void setTeacherTitleName(String teacherTitleName) {
		this.teacherTitleName = teacherTitleName;
	}

	public Integer getFirstApplyLicensedDoctor() {
		return firstApplyLicensedDoctor;
	}

	public void setFirstApplyLicensedDoctor(Integer firstApplyLicensedDoctor) {
		this.firstApplyLicensedDoctor = firstApplyLicensedDoctor;
	}

	public String getZyyUserStatusStr() {
		return zyyUserStatusStr;
	}

	public void setZyyUserStatusStr(String zyyUserStatusStr) {
		this.zyyUserStatusStr = zyyUserStatusStr == null ? null : zyyUserStatusStr.trim();
	}

	public String getRoleTypeStr() {
		return roleTypeStr;
	}

	public void setRoleTypeStr(String roleTypeStr) {
		this.roleTypeStr = roleTypeStr;
	}

	public String getRegLocProvinceStr() {
		return regLocProvinceStr;
	}

	public void setRegLocProvinceStr(String regLocProvinceStr) {
		this.regLocProvinceStr = regLocProvinceStr;
	}

	public String getRegLocCityStr() {
		return regLocCityStr;
	}

	public void setRegLocCityStr(String regLocCityStr) {
		this.regLocCityStr = regLocCityStr;
	}

	public String getRegLocCountyStr() {
		return regLocCountyStr;
	}

	public void setRegLocCountyStr(String regLocCountyStr) {
		this.regLocCountyStr = regLocCountyStr;
	}

	public ZyyUserExtendEnlarge getZyyUserExtendEnlarge() {
		return zyyUserExtendEnlarge;
	}

	public void setZyyUserExtendEnlarge(ZyyUserExtendEnlarge zyyUserExtendEnlarge) {
		this.zyyUserExtendEnlarge = zyyUserExtendEnlarge;
	}

	public Integer getResidencyHospType() {
		return residencyHospType;
	}

	public void setResidencyHospType(Integer residencyHospType) {
		this.residencyHospType = residencyHospType;
	}

	public String getFilePath() {
		return filePath;
	}

	public void setFilePath(String filePath) {
		this.filePath = filePath == null ? null : filePath.trim();
	}
	@Override
	public String getWeixinOpenid() {
		return weixinOpenid;
	}
	@Override
	public void setWeixinOpenid(String weixinOpenid) {
		this.weixinOpenid = weixinOpenid;
	}

	public String getBirthdayStr() {
		return birthdayStr;
	}

	public void setBirthdayStr(String birthdayStr) {
		this.birthdayStr = birthdayStr == null ? null : birthdayStr.trim();
	}

	public String getFirstRecordSchoolStr() {
		return firstRecordSchoolStr;
	}

	public void setFirstRecordSchoolStr(String firstRecordSchoolStr) {
		this.firstRecordSchoolStr = firstRecordSchoolStr == null ? null : firstRecordSchoolStr.trim();
	}

	public String getTrainingPeriodStr() {
		return trainingPeriodStr;
	}

	public void setTrainingPeriodStr(String trainingPeriodStr) {
		this.trainingPeriodStr = trainingPeriodStr == null ? null : trainingPeriodStr.trim();
	}

	public String getDyxlbyyxlxStr() {
		return dyxlbyyxlxStr;
	}

	public void setDyxlbyyxlxStr(String dyxlbyyxlxStr) {
		this.dyxlbyyxlxStr = dyxlbyyxlxStr == null ? null : dyxlbyyxlxStr.trim();
	}

	public String getFirstDegreeStr() {
		return firstDegreeStr;
	}

	public void setFirstDegreeStr(String firstDegreeStr) {
		this.firstDegreeStr = firstDegreeStr == null ? null : firstDegreeStr.trim();
	}

	public String getFirstSchoolSystemStr() {
		return firstSchoolSystemStr;
	}

	public void setFirstSchoolSystemStr(String firstSchoolSystemStr) {
		this.firstSchoolSystemStr = firstSchoolSystemStr == null ? null : firstSchoolSystemStr.trim();
	}

	public String getIsFreshStr() {
		return isFreshStr;
	}

	public void setIsFreshStr(String isFreshStr) {
		this.isFreshStr = isFreshStr == null ? null : isFreshStr.trim();
	}

	public String getHospTypeStr() {
		return hospTypeStr;
	}

	public void setHospTypeStr(String hospTypeStr) {
		this.hospTypeStr = hospTypeStr == null ? null : hospTypeStr.trim();
	}

	public String getHasCertificateStr() {
		return hasCertificateStr;
	}

	public void setHasCertificateStr(String hasCertificateStr) {
		this.hasCertificateStr = hasCertificateStr == null ? null : hasCertificateStr.trim();
	}

	public String getGetDateStr() {
		return getDateStr;
	}

	public void setGetDateStr(String getDateStr) {
		this.getDateStr = getDateStr == null ? null : getDateStr.trim();
	}

	public String getSupportPlanStr() {
		return supportPlanStr;
	}

	public void setSupportPlanStr(String supportPlanStr) {
		this.supportPlanStr = supportPlanStr == null ? null : supportPlanStr.trim();
	}

	public String getSupportPlanOutput() {
		return supportPlanOutput;
	}

	public void setSupportPlanOutput(String supportPlanOutput) {
		this.supportPlanOutput = supportPlanOutput == null ? null : supportPlanOutput.trim();
	}

	public String getCheckStatusStr() {
		return checkStatusStr;
	}

	public void setCheckStatusStr(String checkStatusStr) {
		this.checkStatusStr = checkStatusStr == null ? null : checkStatusStr.trim();
	}

	public String getDyxlbyyxflStr() {
		return dyxlbyyxflStr;
	}

	public void setDyxlbyyxflStr(String dyxlbyyxflStr) {
		this.dyxlbyyxflStr = dyxlbyyxflStr == null ? null : dyxlbyyxflStr.trim();
	}

	public Integer getFullTimeFrecord() {
		return fullTimeFrecord;
	}

	public void setFullTimeFrecord(Integer fullTimeFrecord) {
		this.fullTimeFrecord = fullTimeFrecord;
	}

	public Integer getFullTimeGraduation() {
		return fullTimeGraduation;
	}

	public void setFullTimeGraduation(Integer fullTimeGraduation) {
		this.fullTimeGraduation = fullTimeGraduation;
	}

	public Integer getIsReadingFirstRecord() {
		return isReadingFirstRecord;
	}

	public void setIsReadingFirstRecord(Integer isReadingFirstRecord) {
		this.isReadingFirstRecord = isReadingFirstRecord;
	}


	public Integer getIsReadingGraduation() {
		return isReadingGraduation;
	}

	public void setIsReadingGraduation(Integer isReadingGraduation) {
		this.isReadingGraduation = isReadingGraduation;
	}

	public Integer getIsSupport() {
		return isSupport;
	}

	public void setIsSupport(Integer isSupport) {
		this.isSupport = isSupport;
	}

	public Long getSendAddress() {
		return sendAddress;
	}

	public void setSendAddress(Long sendAddress) {
		this.sendAddress = sendAddress;
	}

	public Long getReceiveAddress() {
		return receiveAddress;
	}

	public void setReceiveAddress(Long receiveAddress) {
		this.receiveAddress = receiveAddress;
	}

	public String getSendCreditCode() {
		return sendCreditCode;
	}

	public void setSendCreditCode(String sendCreditCode) {
		this.sendCreditCode = sendCreditCode;
	}

	public String getReceiveCreditCode() {
		return receiveCreditCode;
	}

	public void setReceiveCreditCode(String receiveCreditCode) {
		this.receiveCreditCode = receiveCreditCode;
	}

	public Date getFirstRecordCertificateTime() {
		return firstRecordCertificateTime;
	}

	public void setFirstRecordCertificateTime(Date firstRecordCertificateTime) {
		this.firstRecordCertificateTime = firstRecordCertificateTime;
	}

	public Date getFirstDegreeCertificateTime() {
		return firstDegreeCertificateTime;
	}

	public void setFirstDegreeCertificateTime(Date firstDegreeCertificateTime) {
		this.firstDegreeCertificateTime = firstDegreeCertificateTime;
	}

	public Date getGraduationCertificateTime() {
		return graduationCertificateTime;
	}

	public void setGraduationCertificateTime(Date graduationCertificateTime) {
		this.graduationCertificateTime = graduationCertificateTime;
	}

	public Date getHdegreeCertificateTime() {
		return hdegreeCertificateTime;
	}

	public void setHdegreeCertificateTime(Date hdegreeCertificateTime) {
		this.hdegreeCertificateTime = hdegreeCertificateTime;
	}

	public Long getProvinceCheckStatus() {
		return provinceCheckStatus;
	}

	public void setProvinceCheckStatus(Long provinceCheckStatus) {
		this.provinceCheckStatus = provinceCheckStatus;
	}

	public String getTrainPhaseStr() {
		return trainPhaseStr;
	}

	public void setTrainPhaseStr(String trainPhaseStr) {
		this.trainPhaseStr = trainPhaseStr;
	}
}
