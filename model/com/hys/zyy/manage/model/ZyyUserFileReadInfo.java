package com.hys.zyy.manage.model;

import java.io.Serializable;
import java.util.Date;

import com.hys.zyy.manage.util.BaseModel;

/**
 * 用户文件阅读情况
 */
public class ZyyUserFileReadInfo extends BaseModel implements Serializable {
	private static final long serialVersionUID = 1L;
	/*
	 * ID
	 */
	private Long id;
	/*
	 * 用户ID
	 */
	private Long zyyUserId;
	/*
	 * 文件ID
	 */
	private Long zyyFileManageId;
	/*
	 * 阅读时间
	 */
	private Date readTime;

	public ZyyUserFileReadInfo() {
		super();
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getZyyUserId() {
		return zyyUserId;
	}

	public void setZyyUserId(Long zyyUserId) {
		this.zyyUserId = zyyUserId;
	}

	public Long getZyyFileManageId() {
		return zyyFileManageId;
	}

	public void setZyyFileManageId(Long zyyFileManageId) {
		this.zyyFileManageId = zyyFileManageId;
	}

	public Date getReadTime() {
		return readTime;
	}

	public void setReadTime(Date readTime) {
		this.readTime = readTime;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ZyyUserFileReadInfo other = (ZyyUserFileReadInfo) obj;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		return true;
	}

	@Override
	public String toString() {
		return "ZyyUserFileReadInfo [id=" + id + ", zyyUserId=" + zyyUserId
				+ ", zyyFileManageId=" + zyyFileManageId + ", readTime="
				+ readTime + "]";
	}

}
