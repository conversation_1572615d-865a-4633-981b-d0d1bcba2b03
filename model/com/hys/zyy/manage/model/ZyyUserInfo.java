package com.hys.zyy.manage.model;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;

/**
 * 师资人员推送至住院医模型
 * <AUTHOR>
 */
public class ZyyUserInfo implements Serializable {
	private static final long serialVersionUID = 1L;
	/*
	 * 住院医用户ID
	 */
	private Long zyyUserId;
	/*
	 * 省厅ID
	 */
	private Long zyyUserProvinceId;
	/*
	 * 机构ID
	 */
	private Long zyyUserOrgId;
	/*
	 * 用户类别[11=带教老师；17=师承老师]
	 */
	private Integer zyyUserType;
	/*
	 * 用户状态[1=(带教/导师)；2=(非带教/关闭)]
	 */
	private Integer zyyUserState;
	/*
	 * 姓名
	 */
	private String realName;
	/*
	 * 证件类型[0=其他；1=居民身份证；2=军官证；3=护照]
	 */
	private Integer certificateType;
	/*
	 * 证件号码
	 */
	private String certificateNo;
	/*
	 * 职称[0=医士；1=主任医师；2=副主任医师；3=主治医师；4=医师；5=技师；6=见习医师；9=主管技师；10=副主任技师；11=主任技师；12=药师；13=主管药师；14=副主任药师；15=主任药师 ...]
	 */
	private Integer userPostTitle;
	/*
	 * 联系电话
	 */
	private String mobileNumber;
	/*
	 * 所属科室
	 */
	private Long[] deptIdArr;
	/*
	 * 专业基地
	 */
	private String[] baseNameArr;
	private List<Long> baseIds;
	/*
	 * 数据推送是否成功
	 */
	private Boolean success;
	/*
	 * 数据推送提示信息
	 */
	private String msg;

	public ZyyUserInfo() {
		super();
	}

	public Long getZyyUserId() {
		return zyyUserId;
	}

	public void setZyyUserId(Long zyyUserId) {
		this.zyyUserId = zyyUserId;
	}

	public Long getZyyUserProvinceId() {
		return zyyUserProvinceId;
	}

	public void setZyyUserProvinceId(Long zyyUserProvinceId) {
		this.zyyUserProvinceId = zyyUserProvinceId;
	}

	public Long getZyyUserOrgId() {
		return zyyUserOrgId;
	}

	public void setZyyUserOrgId(Long zyyUserOrgId) {
		this.zyyUserOrgId = zyyUserOrgId;
	}

	public Integer getZyyUserType() {
		return zyyUserType;
	}

	public void setZyyUserType(Integer zyyUserType) {
		this.zyyUserType = zyyUserType;
	}

	public Integer getZyyUserState() {
		return zyyUserState;
	}

	public void setZyyUserState(Integer zyyUserState) {
		this.zyyUserState = zyyUserState;
	}

	public String getRealName() {
		return realName;
	}

	public void setRealName(String realName) {
		this.realName = realName == null ? null : realName.trim();
	}

	public Integer getCertificateType() {
		return certificateType;
	}

	public void setCertificateType(Integer certificateType) {
		this.certificateType = certificateType;
	}

	public String getCertificateNo() {
		return certificateNo;
	}

	public void setCertificateNo(String certificateNo) {
		this.certificateNo = certificateNo == null ? null : certificateNo.trim();
	}

	public Integer getUserPostTitle() {
		return userPostTitle;
	}

	public void setUserPostTitle(Integer userPostTitle) {
		this.userPostTitle = userPostTitle;
	}

	public String getMobileNumber() {
		return mobileNumber;
	}

	public void setMobileNumber(String mobileNumber) {
		this.mobileNumber = mobileNumber == null ? null : mobileNumber.trim();
	}

	public Long[] getDeptIdArr() {
		return deptIdArr;
	}

	public void setDeptIdArr(Long[] deptIdArr) {
		this.deptIdArr = deptIdArr;
	}

	public String[] getBaseNameArr() {
		return baseNameArr;
	}

	public void setBaseNameArr(String[] baseNameArr) {
		this.baseNameArr = baseNameArr;
	}

	public List<Long> getBaseIds() {
		return baseIds;
	}

	public void setBaseIds(List<Long> baseIds) {
		this.baseIds = baseIds;
	}

	public Boolean isSuccess() {
		return success;
	}

	public void setSuccess(Boolean success) {
		this.success = success;
	}

	public String getMsg() {
		return msg;
	}

	public void setMsg(String msg) {
		this.msg = msg == null ? null : msg.trim();
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((zyyUserId == null) ? 0 : zyyUserId.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ZyyUserInfo other = (ZyyUserInfo) obj;
		if (zyyUserId == null) {
			if (other.zyyUserId != null)
				return false;
		} else if (!zyyUserId.equals(other.zyyUserId))
			return false;
		return true;
	}

	@Override
	public String toString() {
		return "ZyyUserInfo [zyyUserId=" + zyyUserId + ", zyyUserProvinceId="
				+ zyyUserProvinceId + ", zyyUserOrgId=" + zyyUserOrgId
				+ ", zyyUserType=" + zyyUserType + ", zyyUserState="
				+ zyyUserState + ", realName=" + realName
				+ ", certificateType=" + certificateType + ", certificateNo="
				+ certificateNo + ", userPostTitle=" + userPostTitle
				+ ", mobileNumber=" + mobileNumber + ", deptIdArr="
				+ Arrays.toString(deptIdArr) + ", baseNameArr="
				+ Arrays.toString(baseNameArr) + ", baseIds=" + baseIds
				+ ", success=" + success + ", msg=" + msg + "]";
	}
	
}