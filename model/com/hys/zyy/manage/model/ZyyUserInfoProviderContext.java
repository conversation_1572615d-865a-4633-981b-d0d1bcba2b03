package com.hys.zyy.manage.model;

import javax.servlet.http.HttpServletRequest;

public class ZyyUserInfoProviderContext extends ZyyBaseObject {
	
	private static final long serialVersionUID = 1L;
	private HttpServletRequest request;
	private Long userId;
	/*
	 * 人员分类
	 */
	private Integer userCategory;

	public ZyyUserInfoProviderContext(HttpServletRequest request, Long userId) {
		this.request = request;
		this.userId = userId;
	}

	public HttpServletRequest getRequest() {
		return request;
	}

	public void setRequest(HttpServletRequest request) {
		this.request = request;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public Integer getUserCategory() {
		return userCategory;
	}

	public void setUserCategory(Integer userCategory) {
		this.userCategory = userCategory;
	}
	
}