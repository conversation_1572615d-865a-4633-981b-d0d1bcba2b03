/**
 *
 * <p>Title: ZyyUserSpecialHistory.java</p>
 * <p>Description: 描述 用户特殊情况处理历史</p>
 * <p>Copyright: Copyright (c) 2006</p>
 * <p>Company: ha<PERSON><PERSON><PERSON> </p>
 * <AUTHOR>
 * @version 1.0 5:32:06 PMApr 15, 2013
 * 修改日期  修改人  修改目的
 */

package com.hys.zyy.manage.model;

import java.util.Date;

public class ZyyUserSpecialHistory extends ZyyBaseObject{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	private Long id;
	private Long userId;
	private String userName;
	private String userAccount;
	private String userPass;
	private Long userStatus;
	private Long userType;
	private String proName;		//属性名称
	private String unitName; 	//单位名称
	private Long operateType;	//操作类型:1,添加;2,修改;3,删除
	private Long updateUserId;	
	private Date updateDate;
	private Long relationId;		//修改前和修改后关联
	
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	public String getUserName() {
		return userName;
	}
	public void setUserName(String userName) {
		this.userName = userName;
	}
	public String getUserPass() {
		return userPass;
	}
	public void setUserPass(String userPass) {
		this.userPass = userPass;
	}
	public Long getUserStatus() {
		return userStatus;
	}
	public void setUserStatus(Long userStatus) {
		this.userStatus = userStatus;
	}
	public Long getUserType() {
		return userType;
	}
	public void setUserType(Long userType) {
		this.userType = userType;
	}
	public String getProName() {
		return proName;
	}
	public void setProName(String proName) {
		this.proName = proName;
	}
	public String getUnitName() {
		return unitName;
	}
	public void setUnitName(String unitName) {
		this.unitName = unitName;
	}
	public Long getOperateType() {
		return operateType;
	}
	public void setOperateType(Long operateType) {
		this.operateType = operateType;
	}
	public Long getUpdateUserId() {
		return updateUserId;
	}
	public void setUpdateUserId(Long updateUserId) {
		this.updateUserId = updateUserId;
	}
	public Date getUpdateDate() {
		return updateDate;
	}
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}
	public Long getRelationId() {
		return relationId;
	}
	public void setRelationId(Long relationId) {
		this.relationId = relationId;
	}
	public String getUserAccount() {
		return userAccount;
	}
	public void setUserAccount(String userAccount) {
		this.userAccount = userAccount;
	}

	
}


