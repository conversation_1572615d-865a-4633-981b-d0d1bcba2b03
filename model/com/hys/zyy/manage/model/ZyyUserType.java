package com.hys.zyy.manage.model;

import java.util.Date;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：陈明凯 2012-05-3
 * 
 * 描述：用户类型
 * 
 * 说明:
 */
public class ZyyUserType extends ZyyBaseObject {

	private static final long serialVersionUID = -3196086218985753113L;
	
	/**
	 * 类型编号
	 */
	private Integer id;
	
	/**
	 * 类型名称
	 */
	private String userTypeName;
	
	/**
	 * 类型缩写
	 */
	private String userTypeAbbr;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getUserTypeName() {
		return userTypeName;
	}

	public void setUserTypeName(String userTypeName) {
		this.userTypeName = userTypeName;
	}

	public String getUserTypeAbbr() {
		return userTypeAbbr;
	}

	public void setUserTypeAbbr(String userTypeAbbr) {
		this.userTypeAbbr = userTypeAbbr;
	}
	
	
}