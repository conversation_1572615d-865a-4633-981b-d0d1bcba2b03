package com.hys.zyy.manage.model;

import java.io.Serializable;
import java.util.Date;

/**
 * 微信小程序绑定
 * <AUTHOR>
 * @date 2020-4-29上午11:29:17
 */
public class ZyyWeixinMiniUser implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
//	用户id	
	private Long userId;
//	微信小程序id	
	private String wxMiniOpenid;
//	绑定时间	
	private Date bindDate;
	
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	public String getWxMiniOpenid() {
		return wxMiniOpenid;
	}
	public void setWxMiniOpenid(String wxMiniOpenid) {
		this.wxMiniOpenid = wxMiniOpenid;
	}
	public Date getBindDate() {
		return bindDate;
	}
	public void setBindDate(Date bindDate) {
		this.bindDate = bindDate;
	}
}
