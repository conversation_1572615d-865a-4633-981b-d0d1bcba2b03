package com.hys.zyy.manage.model;

import java.io.Serializable;
import java.util.Date;

/**
 * 工作质量月报
 */
public class ZyyWorkQualityMonthReport extends ZyyUserExtendVO implements Serializable {
	private static final long serialVersionUID = 1L;
	/*
	 * ID
	 */
	private Long id;
	/*
	 * 学员ID
	 */
	private Long residencyId;
	/*
	 * 迟到次数
	 */
	private Integer lateNumber;
	/*
	 * 早退次数
	 */
	private Integer leaveEarlyNumber;
	/*
	 * 旷工（天）
	 */
	private Integer absenteeismNumber;
	/*
	 * 请假天数
	 */
	private Double leaveNumber;
	/*
	 * 请假信息
	 */
	private String leaveInfo;
	/*
	 * 实际出勤（天）
	 */
	private Double dutyDays;
	/*
	 * 工作质量（1：优；2：良；3：中；4：差）
	 */
	private Integer workQuality;
	/*
	 * 带教信息
	 */
	private String teacherInfo;
	/*
	 * 填报人
	 */
	private String creater;
	/*
	 * 上报科室
	 */
	private Long createDeptId;
	/*
	 * 轮转开始时间
	 */
	private Date cycleStartDate;
	/*
	 * 轮转结束时间
	 */
	private Date cycleEndDate;
	/*
	 * 上报月份
	 */
	private Date reportMonth;
	/*
	 * 上报时间
	 */
	private Date reportTime;
	/*
	 * 备注
	 */
	private String remark;
	/*
	 * 状态（1：保存；2：上报）
	 */
	private Integer state;
	/*
	 * 创建者ID
	 */
	private Long createUserId;
	/*
	 * 创建时间
	 */
	private Date createTime;
	/*
	 * 修改时间
	 */
	private Date updateTime;

	public ZyyWorkQualityMonthReport() {
		super();
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getResidencyId() {
		return residencyId;
	}

	public void setResidencyId(Long residencyId) {
		this.residencyId = residencyId;
	}

	public Integer getLateNumber() {
		return lateNumber;
	}

	public void setLateNumber(Integer lateNumber) {
		this.lateNumber = lateNumber;
	}

	public Integer getLeaveEarlyNumber() {
		return leaveEarlyNumber;
	}

	public void setLeaveEarlyNumber(Integer leaveEarlyNumber) {
		this.leaveEarlyNumber = leaveEarlyNumber;
	}

	public Integer getAbsenteeismNumber() {
		return absenteeismNumber;
	}

	public void setAbsenteeismNumber(Integer absenteeismNumber) {
		this.absenteeismNumber = absenteeismNumber;
	}
	
	public Double getLeaveNumber() {
		return leaveNumber;
	}

	public void setLeaveNumber(Double leaveNumber) {
		this.leaveNumber = leaveNumber;
	}

	public String getLeaveInfo() {
		return leaveInfo;
	}

	public void setLeaveInfo(String leaveInfo) {
		this.leaveInfo = leaveInfo;
	}
	
	public Double getDutyDays() {
		return dutyDays;
	}

	public void setDutyDays(Double dutyDays) {
		this.dutyDays = dutyDays;
	}

	public Integer getWorkQuality() {
		return workQuality;
	}

	public void setWorkQuality(Integer workQuality) {
		this.workQuality = workQuality;
	}

	public String getTeacherInfo() {
		return teacherInfo;
	}

	public void setTeacherInfo(String teacherInfo) {
		this.teacherInfo = teacherInfo;
	}

	public String getCreater() {
		return creater;
	}

	public void setCreater(String creater) {
		this.creater = creater;
	}

	public Long getCreateDeptId() {
		return createDeptId;
	}

	public void setCreateDeptId(Long createDeptId) {
		this.createDeptId = createDeptId;
	}

	public Date getCycleStartDate() {
		return cycleStartDate;
	}

	public void setCycleStartDate(Date cycleStartDate) {
		this.cycleStartDate = cycleStartDate;
	}

	public Date getCycleEndDate() {
		return cycleEndDate;
	}

	public void setCycleEndDate(Date cycleEndDate) {
		this.cycleEndDate = cycleEndDate;
	}

	public Date getReportMonth() {
		return reportMonth;
	}

	public void setReportMonth(Date reportMonth) {
		this.reportMonth = reportMonth;
	}

	public Date getReportTime() {
		return reportTime;
	}

	public void setReportTime(Date reportTime) {
		this.reportTime = reportTime;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	public Long getCreateUserId() {
		return createUserId;
	}

	public void setCreateUserId(Long createUserId) {
		this.createUserId = createUserId;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ZyyWorkQualityMonthReport other = (ZyyWorkQualityMonthReport) obj;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		return true;
	}

	@Override
	public String toString() {
		return "ZyyWorkQualityMonthReport [id=" + id + ", residencyId="
				+ residencyId + ", lateNumber=" + lateNumber
				+ ", leaveEarlyNumber=" + leaveEarlyNumber
				+ ", absenteeismNumber=" + absenteeismNumber + ", leaveNumber="
				+ leaveNumber + ", leaveInfo=" + leaveInfo + ", dutyDays="
				+ dutyDays + ", workQuality=" + workQuality + ", teacherInfo="
				+ teacherInfo + ", creater=" + creater + ", createDeptId="
				+ createDeptId + ", cycleStartDate=" + cycleStartDate
				+ ", cycleEndDate=" + cycleEndDate + ", reportMonth="
				+ reportMonth + ", reportTime=" + reportTime + ", remark="
				+ remark + ", state=" + state + ", createUserId="
				+ createUserId + ", createTime=" + createTime + ", updateTime="
				+ updateTime + "]";
	}

}