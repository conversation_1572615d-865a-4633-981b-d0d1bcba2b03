package com.hys.zyy.manage.model;

import java.util.List;

import com.hys.zyy.manage.util.DateUtil;

public class ZyyWorkQualityMonthReportVO extends ZyyWorkQualityMonthReport {
	private String ids;
	
	private String workQualityStr;

	private String reportTimeStr;

	private String stateStr;

	private String teacherNames;

	private String reportMonthStr;
	
	private String cycleStartDateStr, cycleEndDateStr;
	
	private String keyword;
	
	private List<ZyyWorkQualityMonthReportVO> list;
	/*
	 * 上报后是否允许科室修改（-1：不允许；1：允许）
	 */
	private Integer allowModify;
	
	public String getIds() {
		return ids;
	}

	public void setIds(String ids) {
		this.ids = ids == null ? null : ids.trim();
	}

	public String getWorkQualityStr() {
		return workQualityStr;
	}

	public void setWorkQualityStr(String workQualityStr) {
		this.workQualityStr = workQualityStr;
	}

	public String getReportTimeStr() {
		return reportTimeStr;
	}

	public void setReportTimeStr(String reportTimeStr) {
		this.reportTimeStr = reportTimeStr;
	}

	public String getStateStr() {
		return stateStr;
	}

	public void setStateStr(String stateStr) {
		this.stateStr = stateStr;
	}

	public String getTeacherNames() {
		return teacherNames;
	}

	public void setTeacherNames(String teacherNames) {
		this.teacherNames = teacherNames;
	}

	public String getReportMonthStr() {
		return reportMonthStr;
	}

	public String getCycleStartDateStr() {
		return cycleStartDateStr;
	}

	public void setCycleStartDateStr(String cycleStartDateStr) {
		this.cycleStartDateStr = cycleStartDateStr == null ? null : cycleStartDateStr.trim();
	}

	public String getCycleEndDateStr() {
		return cycleEndDateStr;
	}

	public void setCycleEndDateStr(String cycleEndDateStr) {
		this.cycleEndDateStr = cycleEndDateStr == null ? null : cycleEndDateStr.trim();
	}

	public void setReportMonthStr(String reportMonthStr) {
		this.reportMonthStr = reportMonthStr;
		if (this.reportMonthStr != null) {
			setReportMonth(DateUtil.parse(reportMonthStr, DateUtil.FORMAT_SHORTER));
		}
	}

	public String getKeyword() {
		return keyword;
	}

	public void setKeyword(String keyword) {
		this.keyword = keyword == null ? null : keyword.trim();
	}

	public List<ZyyWorkQualityMonthReportVO> getList() {
		return list;
	}

	public void setList(List<ZyyWorkQualityMonthReportVO> list) {
		this.list = list;
	}

	public Integer getAllowModify() {
		return allowModify;
	}

	public void setAllowModify(Integer allowModify) {
		this.allowModify = allowModify;
	}
	
}
