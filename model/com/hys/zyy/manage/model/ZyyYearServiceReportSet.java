package com.hys.zyy.manage.model;

import java.io.Serializable;
import java.util.Date;

import com.hys.zyy.manage.util.BaseModel;

/**
 * 年度服务报告设置
 */
public class ZyyYearServiceReportSet extends BaseModel implements Serializable {
	private static final long serialVersionUID = 1L;
	/*
	 * ID
	 */
	private String id;
	/*
	 * 域名
	 */
	private String domainName;
	/*
	 * 省厅名称
	 */
	private String provinceName;
	/*
	 * 省厅ID
	 */
	private Long provinceId;
	/*
	 * 是否显示弹窗（-1：否；1：是）
	 */
	private Integer isShow;
	/*
	 * 备注
	 */
	private String remark;
	/*
	 * 创建时间
	 */
	private Date createTime;
	/*
	 * 修改时间
	 */
	private Date updateTime;

	public ZyyYearServiceReportSet() {
		super();
	}

	public ZyyYearServiceReportSet(Long provinceId) {
		super();
		this.provinceId = provinceId;
	}

	public ZyyYearServiceReportSet(Long provinceId, Integer isShow) {
		super();
		this.provinceId = provinceId;
		this.isShow = isShow;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getDomainName() {
		return domainName;
	}

	public void setDomainName(String domainName) {
		this.domainName = domainName == null ? null : domainName.trim();
	}

	public String getProvinceName() {
		return provinceName;
	}

	public void setProvinceName(String provinceName) {
		this.provinceName = provinceName == null ? null : provinceName.trim();
	}

	public Long getProvinceId() {
		return provinceId;
	}

	public void setProvinceId(Long provinceId) {
		this.provinceId = provinceId;
	}

	public Integer getIsShow() {
		return isShow;
	}

	public void setIsShow(Integer isShow) {
		this.isShow = isShow;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ZyyYearServiceReportSet other = (ZyyYearServiceReportSet) obj;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		return true;
	}

	@Override
	public String toString() {
		return "ZyyYearServiceReportSet [id=" + id + ", domainName="
				+ domainName + ", provinceName=" + provinceName
				+ ", provinceId=" + provinceId + ", isShow=" + isShow
				+ ", remark=" + remark + ", createTime=" + createTime
				+ ", updateTime=" + updateTime + "]";
	}

}
