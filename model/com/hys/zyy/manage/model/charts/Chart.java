package com.hys.zyy.manage.model.charts;


import java.util.List;
import java.util.Map;

/**
 * 图表配置
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/7/13.
 */
public class Chart {


    /**图例*/
    private ChartOptionLegend legend = new ChartOptionLegend();

    private ChartOptionTitle title ;
    /**系列*/
    private List<ChartOptionSeries> series;
    /**x轴*/
    private ChartOptionAxis xAxis;
    /**y轴*/
    private ChartOptionAxis yAxis;
    
    private ChartOptionGrid grid = new ChartOptionGrid();
    /**气泡提示数据*/
    private ChartOptionTooltip tooltip = new ChartOptionTooltip();
    /**报表工具栏*/
    private ChartOptionToolbox toolbox = new ChartOptionToolbox();
    //滚动条，区域缩放
    private ChartOptionDataZoom dataZoom;
    

    

    public ChartOptionLegend getLegend() {
        return legend;
    }

    public void setLegend(ChartOptionLegend legend) {
        this.legend = legend;
    }

    public List<ChartOptionSeries> getSeries() {
        return series;
    }

    public void setSeries(List<ChartOptionSeries> series) {
        this.series = series;
    }

    public ChartOptionAxis getxAxis() {
        return xAxis;
    }

    public void setxAxis(ChartOptionAxis xAxis) {
        this.xAxis = xAxis;
    }

    public ChartOptionAxis getyAxis() {
        return yAxis;
    }

    public void setyAxis(ChartOptionAxis yAxis) {
        this.yAxis = yAxis;
    }

    public ChartOptionTooltip getTooltip() {
        return tooltip;
    }

    public void setTooltip(ChartOptionTooltip tooltip) {
        this.tooltip = tooltip;
    }

    public ChartOptionTitle getTitle() {
        return title;
    }

    public void setTitle(ChartOptionTitle title) {
        this.title = title;
    }

	public ChartOptionToolbox getToolbox() {
		return toolbox;
	}

	public void setToolbox(ChartOptionToolbox toolbox) {
		this.toolbox = toolbox;
	}

	public ChartOptionGrid getGrid() {
		return grid;
	}

	public void setGrid(ChartOptionGrid grid) {
		this.grid = grid;
	}

	public ChartOptionDataZoom getDataZoom() {
		return dataZoom;
	}

	public void setDataZoom(ChartOptionDataZoom dataZoom) {
		this.dataZoom = dataZoom;
	}

    
}
