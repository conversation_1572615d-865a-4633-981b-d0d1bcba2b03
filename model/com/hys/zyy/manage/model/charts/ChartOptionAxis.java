package com.hys.zyy.manage.model.charts;

import java.util.Map;
import java.util.Objects;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/7/13.
 */
public class ChartOptionAxis {

    private String type;
    private Boolean show = true;

    /**是否从坐标轴作为起点*/
    private boolean boundaryGap;

    /**数据*/
    private String[] data;
    /**坐标间隔显示*/
    private Map<String,Integer> splitNumber;
    /**强制间隔距离*/
    private Map<String,Integer> interval;
    /**坐标显示旋转角度*/
    private Map<String,Integer> nameRotate;

    private Map<String,String> splitLine;
    /** 数据格式化 */
    private Map<String,Object>  axisLabel;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public boolean isBoundaryGap() {
        return boundaryGap;
    }

    public void setBoundaryGap(boolean boundaryGap) {
        this.boundaryGap = boundaryGap;
    }

    public String[] getData() {
        return data;
    }

    public void setData(String[] data) {
        this.data = data;
    }

    public Map<String, Object> getAxisLabel() {
        return axisLabel;
    }

    public void setAxisLabel(Map<String, Object> axisLabel) {
        this.axisLabel = axisLabel;
    }

	public Map<String,Integer> getSplitNumber() {
		return splitNumber;
	}

	public void setSplitNumber(Map<String,Integer> splitNumber) {
		this.splitNumber = splitNumber;
	}

	public Map<String,Integer> getNameRotate() {
		return nameRotate;
	}

	public void setNameRotate(Map<String,Integer> nameRotate) {
		this.nameRotate = nameRotate;
	}

	public Map<String,Integer> getInterval() {
		return interval;
	}

	public void setInterval(Map<String,Integer> interval) {
		this.interval = interval;
	}

	public Map<String, String> getSplitLine() {
		return splitLine;
	}

	public void setSplitLine(Map<String, String> splitLine) {
		this.splitLine = splitLine;
	}

	public Boolean getShow() {
		return show;
	}

	public void setShow(Boolean show) {
		this.show = show;
	}
	
    
}
