package com.hys.zyy.manage.model.charts;

public class ChartOptionDataView {

	//数据视图功能是否开启
	private boolean show = true;
	//数据视图标题
	private String title;
	//数据视图是否可编辑
    private boolean readOnly = true;
    //数据视图自定义样式
    private String optionToContent;
	public boolean isShow() {
		return show;
	}
	public void setShow(boolean show) {
		this.show = show;
	}
	public String getTitle() {
		return title;
	}
	public void setTitle(String title) {
		this.title = title;
	}
	public boolean isReadOnly() {
		return readOnly;
	}
	public void setReadOnly(boolean readOnly) {
		this.readOnly = readOnly;
	}
	public String getOptionToContent() {
		return optionToContent;
	}
	public void setOptionToContent(String optionToContent) {
		this.optionToContent = "function(opt) { alert(111) ;var table; table = $('#"+ optionToContent +" ')return table;}";
	}
    
    
}
