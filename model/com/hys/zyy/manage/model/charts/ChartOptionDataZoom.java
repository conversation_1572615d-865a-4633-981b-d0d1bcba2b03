package com.hys.zyy.manage.model.charts;

public class ChartOptionDataZoom {
	//滚动条，区域组件类型：inside\
	private String type;
	//设置控制的x轴
	private Integer[] xAxisIndex = {0};
	//起始控制的区域的起点
	private Integer start = 0;
	//起始控制区域的终点
	private Integer end = 30;
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public Integer[] getxAxisIndex() {
		return xAxisIndex;
	}
	public void setxAxisIndex(Integer[] xAxisIndex) {
		this.xAxisIndex = xAxisIndex;
	}
	public Integer getStart() {
		return start;
	}
	public void setStart(Integer start) {
		this.start = start;
	}
	public Integer getEnd() {
		return end;
	}
	public void setEnd(Integer end) {
		this.end = end;
	}

}
