package com.hys.zyy.manage.model.charts;

/**
 * 图形绘制设置
 * <AUTHOR>
 *
 */
public class ChartOptionGrid {

	//是否显示
	private boolean show;
	//图形与容器左边距"20%"或者30px
	private String left = "10%";
	//图形与容器又边距如"30px",或者20%
    private String right = "10%";
    //图形与底部边距可以是百分比也可以是px
    private String bottom = "25%";
	public boolean isShow() {
		return show;
	}
	public void setShow(boolean show) {
		this.show = show;
	}
	public String getLeft() {
		return left;
	}
	public void setLeft(String left) {
		this.left = left;
	}
	public String getRight() {
		return right;
	}
	public void setRight(String right) {
		this.right = right;
	}
	public String getBottom() {
		return bottom;
	}
	public void setBottom(String bottom) {
		this.bottom = bottom;
	}
    
    
}
