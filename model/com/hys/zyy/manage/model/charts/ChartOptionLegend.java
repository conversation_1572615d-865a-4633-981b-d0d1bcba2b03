package com.hys.zyy.manage.model.charts;

/**
 * 报表的图例设置
 * <AUTHOR>
 *
 */
public class ChartOptionLegend {
	//是否显示图例
    private boolean show = true;
    //图例的布局朝向：横向：horizontal，纵向：vertical
    private String orient= "horizontal";
    //图例
    private String[] data;
    
    private String bottom="20px";
    
    private String top;
    
    private String left;
    
    private String right;
    
    private String type;
    
	public boolean isShow() {
		return show;
	}
	public void setShow(boolean show) {
		this.show = show;
	}
	public String getOrient() {
		return orient;
	}
	public void setOrient(String orient) {
		this.orient = orient;
	}
	public String[] getData() {
		return data;
	}
	public void setData(String[] data) {
		this.data = data;
	}
	public String getBottom() {
		return bottom;
	}
	public void setBottom(String bottom) {
		this.bottom = bottom;
	}
	
	public String getTop() {
		return top;
	}
	public void setTop(String top) {
		this.top = top;
	}
	
	public String getLeft() {
		return left;
	}
	public void setLeft(String left) {
		this.left = left;
	}
	public String getRight() {
		return right;
	}
	public void setRight(String right) {
		this.right = right;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
    
}
