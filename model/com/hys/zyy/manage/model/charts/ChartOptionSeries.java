package com.hys.zyy.manage.model.charts;
import java.util.Map;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/7/13.
 */
public class ChartOptionSeries {

    /**系列的名称*/
    private String name;
    /** 显示的样式*/
    private String type;
    /** 数据*/
    private Object[] data;
    /** 特殊标记点 */
    private Map<String,ChartOptionMeta> markPoint;
    /** 特殊标记线 */
    private Map<String,ChartOptionMeta> markLine;
    /** 占容器的大小*/
    private String radius;
    private Object label;
    private String[] center = {"50%","50%"};
    /**堆叠柱状图标记分组*/
    private String stack;
    /**柱图每个柱的最大宽度*/
    private String barMaxWidth = "60";

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
        if(null != type && type =="pie"){
        	this.label = JSONObject.parseObject("{\"show\":true,\"position\":\"outside\", " +
        			"\"fontWeight\":300 ,\"fontSize\":16,\"formatter\":\"{b}\\n{d}%\"}");
        }
    }

    public Object[] getData() {
        return data;
    }

    public void setData(Object[] data) {
        this.data = data;
    }

    public Map<String, ChartOptionMeta> getMarkPoint() {
        return markPoint;
    }

    public void setMarkPoint(Map<String, ChartOptionMeta> markPoint) {
        this.markPoint = markPoint;
    }

    public Map<String, ChartOptionMeta> getMarkLine() {
        return markLine;
    }

    public void setMarkLine(Map<String, ChartOptionMeta> markLine) {
        this.markLine = markLine;
    }

	public String getStack() {
		return stack;
	}

	public void setStack(String stack) {
		this.stack = stack;
	}

	public String getRadius() {
		return radius;
	}

	public void setRadius(String radius) {
		this.radius = radius;
	}

	public Object getLabel() {
		return label;
	}

	public void setLable(Object label) {
		this.label = label;
	}

	public String getBarMaxWidth() {
		return barMaxWidth;
	}

	public void setBarMaxWidth(String barMaxWidth) {
		this.barMaxWidth = barMaxWidth;
	}

	public void setLabel(Object label) {
		this.label = label;
	}

	public String[] getCenter() {
		return center;
	}

	public void setCenter(String[] center) {
		this.center = center;
	}
    
    
}
