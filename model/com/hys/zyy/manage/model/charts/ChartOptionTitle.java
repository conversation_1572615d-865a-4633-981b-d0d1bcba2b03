package com.hys.zyy.manage.model.charts;

import java.util.HashMap;
import java.util.Map;

/**
 * 报表的标题
 * <AUTHOR>
 * @date 2018年8月14日
 */
public class ChartOptionTitle {
    //标题
	private String text;
	//副标题
    private String subtext;
    //标题位置center、left、right
    private String x = "left";
    //对齐方式right\left\
    private String align = "right";
    //标题距离容器上方的距离
    private String top ="2%";
    //标题文字样式
    private Map<String,Object> textStyle;
    
    public ChartOptionTitle(){
    	textStyle = new HashMap<String,Object>();
    	textStyle.put("fontSize", 14);
    }
    
	public String getText() {
		return text;
	}
	public void setText(String text) {
		this.text = text;
	}
	public String getSubtext() {
		return subtext;
	}
	public void setSubtext(String subtext) {
		this.subtext = subtext;
	}
	public String getX() {
		return x;
	}
	public void setX(String x) {
		this.x = x;
	}
	public String getAlign() {
		return align;
	}
	public void setAlign(String align) {
		this.align = align;
	}

	public Map<String, Object> getTextStyle() {
		return textStyle;
	}

	public void setTextStyle(Map<String, Object> textStyle) {
		this.textStyle = textStyle;
	}

	public String getTop() {
		return top;
	}

	public void setTop(String top) {
		this.top = top;
	}
    
    
}
