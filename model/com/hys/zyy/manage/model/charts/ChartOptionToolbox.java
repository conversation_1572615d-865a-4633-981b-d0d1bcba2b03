package com.hys.zyy.manage.model.charts;

/**
 * 报表的工具栏配置
 * <AUTHOR>
 *
 */
public class ChartOptionToolbox {

	//是否显示工具栏
	private Boolean show =true;
	//工具栏功能配置
	private ToolboxFeature feature = new ToolboxFeature();
	public Boolean getShow() {
		return show;
	}
	public void setShow(Boolean show) {
		this.show = show;
	}
	public ToolboxFeature getFeature() {
		return feature;
	}
	public void setFeature(ToolboxFeature feature) {
		this.feature = feature;
	}
	
	
}
