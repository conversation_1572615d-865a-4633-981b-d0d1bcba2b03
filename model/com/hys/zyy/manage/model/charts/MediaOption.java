package com.hys.zyy.manage.model.charts;

import java.util.HashMap;
import java.util.Map;

/**
 * H5适应设置
 * <AUTHOR>
 *
 */
public class MediaOption {
	
	//规则，根据容器的长宽设置图表调整规则，key可选为：1.maxAspectRatio长宽比例，2.maxWidth宽度
	private Map<String,String> query = new HashMap<String,String>();
	//图例位置设置
	private MediaOptionLegend legend = new MediaOptionLegend();
	//多个系列作为多个图存在时，图的位置设置
	private MediaOptionSeries[] series;
	
	public MediaOption(){
		query.put("maxWidth", "300");
		legend = new MediaOptionLegend();
	}
	public Map<String, String> getQuery() {
		return query;
	}
	public void setQuery(Map<String, String> query) {
		this.query = query;
	}
	public MediaOptionLegend getLegend() {
		return legend;
	}
	public void setLegend(MediaOptionLegend legend) {
		this.legend = legend;
	}
	public MediaOptionSeries[] getSeries() {
		return series;
	}
	public void setSeries(MediaOptionSeries[] series) {
		this.series = series;
	}
	
	

}
