package com.hys.zyy.manage.model.charts;

/**
 * 适应H5时，对图例的位置设置
 * <AUTHOR>
 *
 */
public class MediaOptionLegend {
	//图例距离容器的右边距
	private String right ="center";
	//图例距离容器的头部使用百分比：15%
    private String top = "15%";
    //图例是纵向还是横向：1.横向:horizontal 2.横向：vertical
    private String orient = "vertical";
    //图例距离容器的底部，使用百分比：15%
    private String bottom = "0%";


	public String getRight() {
		return right;
	}


	public void setRight(String right) {
		this.right = right;
	}


	public String getTop() {
		return top;
	}


	public void setTop(String top) {
		this.top = top;
	}


	public String getOrient() {
		return orient;
	}


	public void setOrient(String orient) {
		this.orient = orient;
	}


	public String getBottom() {
		return bottom;
	}


	public void setBottom(String bottom) {
		this.bottom = bottom;
	}
    
	
    
}
