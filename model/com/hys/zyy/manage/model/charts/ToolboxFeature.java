package com.hys.zyy.manage.model.charts;

import java.util.HashMap;
import java.util.Map;

/**
 * 报表的工具栏
 * <AUTHOR>
 *
 */
public class ToolboxFeature {
	 
     //图表切换
     private Map<String,String[]> magicType;
     //
     private Object restore;
     //保存为图片
     private Object saveAsImage = new Object();
     
     private ChartOptionDataView dataView;
     
     private String right = "30";
     
     private String top="10";
     
     private String orient="horizontal";
     
     //自定义还原方法
/*     private Object myReset;*/
            
     public ToolboxFeature(){
    	 magicType = new HashMap<String,String[]>();
    	 String[] types = {"line","bar"};
    	 magicType.put("type", types);
     }


	public Map<String, String[]> getMagicType() {
		return magicType;
	}

	public void setMagicType(Map<String, String[]> magicType) {
		this.magicType = magicType;
	}

	public Object getRestore() {
		return restore;
	}

	public void setRestore(Object restore) {
		this.restore = restore;
	}

	public Object getSaveAsImage() {
		return saveAsImage;
	}

	public void setSaveAsImage(Object saveAsImage) {
		this.saveAsImage = saveAsImage;
	}


	public ChartOptionDataView getDataView() {
		return dataView;
	}


	public void setDataView(ChartOptionDataView dataView) {
		this.dataView = dataView;
	}


	public String getRight() {
		return right;
	}


	public void setRight(String right) {
		this.right = right;
	}


	public String getTop() {
		return top;
	}


	public void setTop(String top) {
		this.top = top;
	}


	public String getOrient() {
		return orient;
	}


	public void setOrient(String orient) {
		this.orient = orient;
	}
 
     
}
