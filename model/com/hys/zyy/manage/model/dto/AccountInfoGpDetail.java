package com.hys.zyy.manage.model.dto;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * @author: HanLiBin
 * @desc: 上报用户数据到云课堂
 * @version: V1.0.0
 */
public class AccountInfoGpDetail {

    @JSONField(serialize = false)
    private Long userId;

    /**
     * 姓名
     */
    private String realName;
    /**
     * 用户名
     */
    private String userName;
    /**
     * 老用户名 -- 这个有值，表示仅修改用户
     */
    private String oldUserName;
    /**
     * 密码
     */
    private String password;
    /**
     * 手机号
     */
    private String mobile;

    /**
     * 用户类型 1学生  2教师
     */
    private Integer userType;
    /**
     * 年度
     */
    private String year;
    /**
     * 科室
     */
    private String dept;
    /**
     * 二级科室
     */
    private String dept1;
    /**
     * 带教的全部科室
     */
    private String deptNames;
    /**
     * 专业
     */
    private String major;

    /**
     * 身份证
     */
    private String idCard;

    private String appId;

    private Long siteId;


    private Integer flag;

    private Integer status=1;
    /*
     * 云课堂带教机构码
     */
    private Long teacherId;
    /*
     * 云课堂学生机构码
     */
    private Long stuId;
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getOldUserName() {
		return oldUserName;
	}

	public void setOldUserName(String oldUserName) {
		this.oldUserName = oldUserName;
	}

	public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public Integer getUserType() {
        return userType;
    }

    public void setUserType(Integer userType) {
        this.userType = userType;
    }

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public String getDept() {
        return dept;
    }

    public void setDept(String dept) {
        this.dept = dept;
    }

    public String getMajor() {
        return major;
    }

    public void setMajor(String major) {
        this.major = major;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public Long getSiteId() {
        return siteId;
    }

    public void setSiteId(Long siteId) {
        this.siteId = siteId;
    }

    public Integer getFlag() {
        return flag;
    }

    public void setFlag(Integer flag) {
        this.flag = flag;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getDept1() {
        return dept1;
    }

    public void setDept1(String dept1) {
        this.dept1 = dept1;
    }
    
	public String getDeptNames() {
		return deptNames;
	}

	public void setDeptNames(String deptNames) {
		this.deptNames = deptNames == null ? null : deptNames.trim();
	}

	public Long getTeacherId() {
		return teacherId;
	}

	public void setTeacherId(Long teacherId) {
		this.teacherId = teacherId;
	}

	public Long getStuId() {
		return stuId;
	}

	public void setStuId(Long stuId) {
		this.stuId = stuId;
	}
    
}
