package com.hys.zyy.manage.model.dto;


import java.util.List;

public class AccountInfoGpParam {
//    专业，账号，姓名，角色类别，手机号，证件号码
//    师资： 年级，专业，科室，账号，姓名，角色类别 ，手机号，证件号码

   //站点地址
   private String domainName;

   //加密
   private String sign;

   private Long timeSpan;

   private List<AccountInfoGpDetail> list;

   private Long siteId;

   public String getDomainName() {
      return domainName;
   }

   public void setDomainName(String domainName) {
      this.domainName = domainName;
   }

   public String getSign() {
      return sign;
   }

   public void setSign(String sign) {
      this.sign = sign;
   }

   public Long getTimeSpan() {
      return timeSpan;
   }

   public void setTimeSpan(Long timeSpan) {
      this.timeSpan = timeSpan;
   }

   public List<AccountInfoGpDetail> getList() {
      return list;
   }

   public void setList(List<AccountInfoGpDetail> list) {
      this.list = list;
   }

   public Long getSiteId() {
      return siteId;
   }

   public void setSiteId(Long siteId) {
      this.siteId = siteId;
   }
}
