package com.hys.zyy.manage.model.dto;

import java.io.Serializable;

/**
 * bdp修改用户和字典数据同步
 * <AUTHOR>
 * @date 2024/3/21
 */
public class BdpUserDictDTO implements Serializable {
	private static final long serialVersionUID = 1L;
	//通知类型 1:字典同步 2:用户同步 3:用户合并 4 组织机构
    private Integer dataType;
    //操作类型 1:新增 2:更改 3:删除 4:合并
    private Integer type;
    //查询参数 字典dicCode或人员bdpUserld
    private String id;
    //加密串
    private String sign;
    // 被合并的bdpUserId，"oldUserId":"1,2,3"
    private String oldUserId;
    // 合并后最新的bdpUserId
    private String newUserId;
    // 合并后最新的bdpUserId对应的用户名
    private String newUserAccount;
    
    public Integer getDataType() {
		return dataType;
	}

	public void setDataType(Integer dataType) {
		this.dataType = dataType;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

	public String getOldUserId() {
		return oldUserId;
	}

	public void setOldUserId(String oldUserId) {
		this.oldUserId = oldUserId == null ? null : oldUserId.trim();
	}

	public String getNewUserId() {
		return newUserId;
	}

	public void setNewUserId(String newUserId) {
		this.newUserId = newUserId == null ? null : newUserId.trim();
	}

	public String getNewUserAccount() {
		return newUserAccount;
	}

	public void setNewUserAccount(String newUserAccount) {
		this.newUserAccount = newUserAccount == null ? null : newUserAccount.trim();
	}
    
}