package com.hys.zyy.manage.model.dto;

import java.io.Serializable;
import java.util.Collections;
import java.util.Comparator;
import java.util.Iterator;
import java.util.List;

import com.alibaba.fastjson.annotation.JSONField;
import com.hys.zyy.manage.util.CollectionUtils;

public class InitGpDictValueParam implements Serializable {
	private static final long serialVersionUID = 1L;
	/*
	 * 科室ID
	 */
	private Long deptId;
	/*
	 * 科室父级ID
	 */
	@JSONField(serialize = false)
	private Long parentDeptId;
	/*
	 * 一级科室
	 */
	private String dept;
	/*
	 * 二级科室
	 */
	private List<String> dept1;
	@JSONField(serialize = false)
	private String dept1Str;
	/*
	 * 科室等级
	 */
	private Integer level;
	/*
	 * 子级科室
	 */
	private List<InitGpDictValueParam> deptChildList;

	public InitGpDictValueParam() {
		super();
	}

	public InitGpDictValueParam(Long deptId, Long parentDeptId, String dept) {
		super();
		this.deptId = deptId;
		this.parentDeptId = parentDeptId;
		this.dept = dept;
	}

	public Long getDeptId() {
		return deptId;
	}

	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}

	public Long getParentDeptId() {
		return parentDeptId;
	}

	public void setParentDeptId(Long parentDeptId) {
		this.parentDeptId = parentDeptId;
	}

	public String getDept() {
		return dept;
	}

	public void setDept(String dept) {
		this.dept = dept == null ? null : dept.trim();
	}

	public List<String> getDept1() {
		return dept1;
	}

	public void setDept1(List<String> dept1) {
		this.dept1 = dept1;
	}

	public String getDept1Str() {
		return dept1Str;
	}

	public void setDept1Str(String dept1Str) {
		this.dept1Str = dept1Str == null ? null : dept1Str.trim();
	}

	public List<InitGpDictValueParam> getDeptChildList() {
		return deptChildList;
	}

	public void setDeptChildList(List<InitGpDictValueParam> deptChildList) {
		this.deptChildList = deptChildList;
	}

	public Integer getLevel() {
		return level;
	}

	public void setLevel(Integer level) {
		this.level = level;
	}
	
	//节点横向排序
    public void sortDeptChildList(Comparator<InitGpDictValueParam> comparator) {
    	if (CollectionUtils.isNotEmpty(deptChildList)) {
    		// 对本层节点进行排序（可根据不同的排序属性，传入不同的比较器）
    		Collections.sort(deptChildList, comparator);
    		// 对每个节点的下一层节点进行排序
    		for (Iterator<InitGpDictValueParam> it = deptChildList.iterator(); it.hasNext();) {
    			it.next().sortDeptChildList(comparator);
    		}
    	}
    }
	
}