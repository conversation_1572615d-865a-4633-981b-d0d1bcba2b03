package com.hys.zyy.manage.model.dto;

import java.io.Serializable;
import java.util.List;

import com.alibaba.fastjson.annotation.JSONField;

public class SyncGpDictParam implements Serializable {
	private static final long serialVersionUID = 1L;
	@JSONField(serialize = false)
	private Long hospitalId;
	/*
	 * 站点地址
	 */
	private String domainName;
	/*
	 * 加密
	 */
	private String sign;

	private Long timeSpan;

	private Long siteId;

	private String appId;

	private Long teacherOrgId;

	//1。年度 YEAR  2，专业 MAJOR  3。  科室一级  DEPT 4 科室二级  DEPT1  【5 科室三级  DEPT2  6 科室四级 DEPT3】
	private Integer dictType;

	// 修改多传以下参数
	// 年度
	private String oldYear;

	// 科室
	private String oldDept;

	// 科室一级
	private String oldDept1;
	
	// 科室 - 三级
	private String oldDept2;
	
	// 科室 -- 四级
	private String oldDept3;

	// zhuanye
	private String oldMajor;

	// 新增--------------
	// 年度
	private String year;

	// 一级科室
	private String dept;

	// 二级科室
	private String dept1;
	
	// 三级科室
	private String dept2;
	
	// 四级科室
	private String dept3;

	// 专业
	private String major;

	// 初始化
	private List<String> yearList;
	private List<String> majorList;
	private List<InitGpDictValueParam> paramList;
	
	// 2级科室升级为1级科室--------------
	// 科室升级涉及用户
	private List<String> userNames;
	// 2级科室下的3级科室，没有不传
	private List<String> dept1List;

	public SyncGpDictParam() {
		super();
	}

	public SyncGpDictParam(Long hospitalId, List<InitGpDictValueParam> paramList) {
		super();
		this.hospitalId = hospitalId;
		this.paramList = paramList;
	}

	public SyncGpDictParam(Long hospitalId, List<String> yearList, List<InitGpDictValueParam> paramList, List<String> majorList) {
		super();
		this.hospitalId = hospitalId;
		this.yearList = yearList;
		this.paramList = paramList;
		this.majorList = majorList;
	}

	public Long getHospitalId() {
		return hospitalId;
	}

	public void setHospitalId(Long hospitalId) {
		this.hospitalId = hospitalId;
	}

	public String getDomainName() {
		return domainName;
	}

	public void setDomainName(String domainName) {
		this.domainName = domainName == null ? null : domainName.trim();
	}

	public String getSign() {
		return sign;
	}

	public void setSign(String sign) {
		this.sign = sign == null ? null : sign.trim();
	}

	public Long getTimeSpan() {
		return timeSpan;
	}

	public void setTimeSpan(Long timeSpan) {
		this.timeSpan = timeSpan;
	}

	public Long getSiteId() {
		return siteId;
	}

	public void setSiteId(Long siteId) {
		this.siteId = siteId;
	}

	public String getAppId() {
		return appId;
	}

	public void setAppId(String appId) {
		this.appId = appId == null ? null : appId.trim();
	}

	public Long getTeacherOrgId() {
		return teacherOrgId;
	}

	public void setTeacherOrgId(Long teacherOrgId) {
		this.teacherOrgId = teacherOrgId;
	}

	public Integer getDictType() {
		return dictType;
	}

	public void setDictType(Integer dictType) {
		this.dictType = dictType;
	}

	public String getOldYear() {
		return oldYear;
	}

	public void setOldYear(String oldYear) {
		this.oldYear = oldYear == null ? null : oldYear.trim();
	}

	public String getOldDept() {
		return oldDept;
	}

	public void setOldDept(String oldDept) {
		this.oldDept = oldDept == null ? null : oldDept.trim();
	}

	public String getOldDept1() {
		return oldDept1;
	}

	public void setOldDept1(String oldDept1) {
		this.oldDept1 = oldDept1 == null ? null : oldDept1.trim();
	}

	public String getOldMajor() {
		return oldMajor;
	}

	public void setOldMajor(String oldMajor) {
		this.oldMajor = oldMajor == null ? null : oldMajor.trim();
	}

	public String getYear() {
		return year;
	}

	public void setYear(String year) {
		this.year = year == null ? null : year.trim();
	}

	public String getDept() {
		return dept;
	}

	public void setDept(String dept) {
		this.dept = dept == null ? null : dept.trim();
	}

	public String getDept1() {
		return dept1;
	}

	public void setDept1(String dept1) {
		this.dept1 = dept1 == null ? null : dept1.trim();
	}

	public String getMajor() {
		return major;
	}

	public void setMajor(String major) {
		this.major = major == null ? null : major.trim();
	}

	public List<String> getYearList() {
		return yearList;
	}

	public void setYearList(List<String> yearList) {
		this.yearList = yearList;
	}

	public List<String> getMajorList() {
		return majorList;
	}

	public void setMajorList(List<String> majorList) {
		this.majorList = majorList;
	}

	public List<InitGpDictValueParam> getParamList() {
		return paramList;
	}

	public void setParamList(List<InitGpDictValueParam> paramList) {
		this.paramList = paramList;
	}

	public List<String> getUserNames() {
		return userNames;
	}

	public void setUserNames(List<String> userNames) {
		this.userNames = userNames;
	}

	public List<String> getDept1List() {
		return dept1List;
	}

	public void setDept1List(List<String> dept1List) {
		this.dept1List = dept1List;
	}

	public String getOldDept2() {
		return oldDept2;
	}

	public void setOldDept2(String oldDept2) {
		this.oldDept2 = oldDept2 == null ? null : oldDept2.trim();
	}

	public String getOldDept3() {
		return oldDept3;
	}

	public void setOldDept3(String oldDept3) {
		this.oldDept3 = oldDept3 == null ? null : oldDept3.trim();
	}

	public String getDept2() {
		return dept2;
	}

	public void setDept2(String dept2) {
		this.dept2 = dept2 == null ? null : dept2.trim();
	}

	public String getDept3() {
		return dept3;
	}

	public void setDept3(String dept3) {
		this.dept3 = dept3 == null ? null : dept3.trim();
	}
	
}