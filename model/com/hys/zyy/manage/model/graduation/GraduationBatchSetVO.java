package com.hys.zyy.manage.model.graduation;

import java.util.List;

/**
 * @author: HLB
 * @desc: 结业批次管理类
 * @version: V1.0.0
 */
public class GraduationBatchSetVO extends GraduationBatchSet{

    /**
     * 批次要添加的学员id集合。
     */
    private List<Long> studentIds;

    /**
     * 批次修改删除的学员id集合。
     */
    private List<Long> deleteStudentIds;
    /**
     * 是否全选（如果是全选 则需要保存所有的学员。0 不是，1 是）
     */
    private Integer isAll;

    public List<Long> getStudentIds() {
        return studentIds;
    }


    public void setStudentIds(List<Long> studentIds) {
        this.studentIds = studentIds;
    }

    public Integer getIsAll() {
        return isAll;
    }

    public void setIsAll(Integer isAll) {
        this.isAll = isAll;
    }

    public List<Long> getDeleteStudentIds() {
        return deleteStudentIds;
    }

    public void setDeleteStudentIds(List<Long> deleteStudentIds) {
        this.deleteStudentIds = deleteStudentIds;
    }
}
