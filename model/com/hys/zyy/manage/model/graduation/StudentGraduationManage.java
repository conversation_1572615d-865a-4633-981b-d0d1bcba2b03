package com.hys.zyy.manage.model.graduation;

/**
 * @author: HLB
 * @desc: 学员结业管理类
 * @version: V1.0.0
 */
public class StudentGraduationManage {
    private String id;
    /**
     * 学员id
     */
    private Long studentId;
    /**
     * 批次Id
     */
    private Long batchId;
    /**
     * 执业医师考试成绩
     */
    private String certifiedExamScore;
    /**
     * 执业医师考试时间
     */
    private String certifiedExamDate;
    /**
     * 年度业务测试时间
     */
    private String annualExamDate;
    /**
     * 年度业务测试水平占位比(单位%)
     */
    private String annualExamScore;
    /**
     * 结业过程考核考核时间
     */
    private String graduationExamDate;
    /**
     * 结业过程考核考核等级
     */
    private String graduationExamLevel;
     /**
     * 结业过程考核考核结果
     */
    private String graduationExamResult;
     /**
     * 理论考核理论考核时间
     */
    private String theoreticalExamDate;
     /**
     * 理论考核理论考核组织考核机构
     */
    private String theoreticalExamBase;
     /**
     * 理论考核理论考核场次编号
     */
    private String theoreticalExamNo;
     /**
     * 理论考核理论考核成绩
     */
    private String theoreticalExamScore;
     /**
     * 理论考核理论考核结果
     */
    private String theoreticalExamResult;
     /**
     * 技能考核时间
     */
    private String skillExamDate;
     /**
     * 技能考核组织考核机构
     */
    private String skillExamBase;
     /**
     * 技能考核场次编号
     */
    private String skillExamNo;
     /**
     * 技能考核成绩
     */
    private String skillExamScore;
     /**
     * 技能考核结果
     */
    private String skillExamResult;
     /**
     * 结业证书编码
     */
    private String graduationCertificateNo;

     /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private String createDate;

    /**
     * 更新时间
     */
    private String updateDate;

    /**
     * 状态：1 正常，-1删除
     */
    private String status;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Long getStudentId() {
        return studentId;
    }

    public void setStudentId(Long studentId) {
        this.studentId = studentId;
    }

    public String getCertifiedExamScore() {
        return certifiedExamScore;
    }

    public void setCertifiedExamScore(String certifiedExamScore) {
        this.certifiedExamScore = certifiedExamScore;
    }

    public String getCertifiedExamDate() {
        return certifiedExamDate;
    }

    public void setCertifiedExamDate(String certifiedExamDate) {
        this.certifiedExamDate = certifiedExamDate;
    }

    public String getAnnualExamDate() {
        return annualExamDate;
    }

    public void setAnnualExamDate(String annualExamDate) {
        this.annualExamDate = annualExamDate;
    }

    public String getAnnualExamScore() {
        return annualExamScore;
    }

    public void setAnnualExamScore(String annualExamScore) {
        this.annualExamScore = annualExamScore;
    }

    public String getGraduationExamDate() {
        return graduationExamDate;
    }

    public void setGraduationExamDate(String graduationExamDate) {
        this.graduationExamDate = graduationExamDate;
    }

    public String getGraduationExamLevel() {
        return graduationExamLevel;
    }

    public void setGraduationExamLevel(String graduationExamLevel) {
        this.graduationExamLevel = graduationExamLevel;
    }

    public String getGraduationExamResult() {
        return graduationExamResult;
    }

    public void setGraduationExamResult(String graduationExamResult) {
        this.graduationExamResult = graduationExamResult;
    }

    public String getTheoreticalExamDate() {
        return theoreticalExamDate;
    }

    public void setTheoreticalExamDate(String theoreticalExamDate) {
        this.theoreticalExamDate = theoreticalExamDate;
    }

    public String getTheoreticalExamBase() {
        return theoreticalExamBase;
    }

    public void setTheoreticalExamBase(String theoreticalExamBase) {
        this.theoreticalExamBase = theoreticalExamBase;
    }

    public String getTheoreticalExamNo() {
        return theoreticalExamNo;
    }

    public void setTheoreticalExamNo(String theoreticalExamNo) {
        this.theoreticalExamNo = theoreticalExamNo;
    }

    public String getTheoreticalExamScore() {
        return theoreticalExamScore;
    }

    public void setTheoreticalExamScore(String theoreticalExamScore) {
        this.theoreticalExamScore = theoreticalExamScore;
    }

    public String getTheoreticalExamResult() {
        return theoreticalExamResult;
    }

    public void setTheoreticalExamResult(String theoreticalExamResult) {
        this.theoreticalExamResult = theoreticalExamResult;
    }

    public String getSkillExamDate() {
        return skillExamDate;
    }

    public void setSkillExamDate(String skillExamDate) {
        this.skillExamDate = skillExamDate;
    }

    public String getSkillExamBase() {
        return skillExamBase;
    }

    public void setSkillExamBase(String skillExamBase) {
        this.skillExamBase = skillExamBase;
    }

    public String getSkillExamNo() {
        return skillExamNo;
    }

    public void setSkillExamNo(String skillExamNo) {
        this.skillExamNo = skillExamNo;
    }

    public String getSkillExamScore() {
        return skillExamScore;
    }

    public void setSkillExamScore(String skillExamScore) {
        this.skillExamScore = skillExamScore;
    }

    public String getSkillExamResult() {
        return skillExamResult;
    }

    public void setSkillExamResult(String skillExamResult) {
        this.skillExamResult = skillExamResult;
    }

    public String getGraduationCertificateNo() {
        return graduationCertificateNo;
    }

    public void setGraduationCertificateNo(String graduationCertificateNo) {
        this.graduationCertificateNo = graduationCertificateNo;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public String getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(String updateDate) {
        this.updateDate = updateDate;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Long getBatchId() {
        return batchId;
    }

    public void setBatchId(Long batchId) {
        this.batchId = batchId;
    }
}
