package com.hys.zyy.manage.model.graduation;

import com.hys.zyy.manage.interfaces.ExcelField;

/**
 * @author: HLB
 * @desc: 学员结业管理Vo类
 * @version: V1.0.0
 */
public class StudentGraduationManageImportVO{
    /**
     * 培训基地(医院ID)
     */
    private Long hospitalId;

    /**
     * 培训基地
     */
    @ExcelField(sort=1)
    private String trainingBase;
    /**
     * 年级
     */
    @ExcelField(sort=2)
    private Integer year;
    /**
     * 专业
     */
    @ExcelField(sort=3)
    private String specialty;
    /**
     * 学员姓名
     */
    @ExcelField(sort=4)
    private String studentName;
    /**
     * 身份证号
     */
    @ExcelField(sort=5)
    private String idCard;


    @ExcelField(sort=6)
    private String batchName;

    /**
     * 执业医师考试成绩
     */
    @ExcelField(sort=8)
    private String certifiedExamScore;
    /**
     * 执业医师考试时间
     */
    @ExcelField(sort=7)
    private String certifiedExamDate;
    /**
     * 年度业务测试时间
     */
    @ExcelField(sort=9)
    private String annualExamDate;
    /**
     * 年度业务测试水平占位比(单位%)
     */
    @ExcelField(sort=10)
    private String annualExamScore;
    /**
     * 结业过程考核考核时间
     */
    @ExcelField(sort=11)
    private String graduationExamDate;
    /**
     * 结业过程考核考核等级
     */
    @ExcelField(sort=12)
    private String graduationExamLevel;
    /**
     * 结业过程考核考核结果
     */
    @ExcelField(sort=13)
    private String graduationExamResult;
    /**
     * 理论考核理论考核时间
     */
    @ExcelField(sort=14)
    private String theoreticalExamDate;
    /**
     * 理论考核理论考核组织考核机构
     */
    @ExcelField(sort=15)
    private String theoreticalExamBase;
    /**
     * 理论考核理论考核场次编号
     */
    @ExcelField(sort=16)
    private String theoreticalExamNo;
    /**
     * 理论考核理论考核成绩
     */
    @ExcelField(sort=17)
    private String theoreticalExamScore;
    /**
     * 理论考核理论考核结果
     */
    @ExcelField(sort=18)
    private String theoreticalExamResult;
    /**
     * 技能考核时间
     */
    @ExcelField(sort=19)
    private String skillExamDate;
    /**
     * 技能考核组织考核机构
     */
    @ExcelField(sort=20)
    private String skillExamBase;
    /**
     * 技能考核场次编号
     */
    @ExcelField(sort=21)
    private String skillExamNo;
    /**
     * 技能考核成绩
     */
    @ExcelField(sort=22)
    private String skillExamScore;
    /**
     * 技能考核结果
     */
    @ExcelField(sort=23)
    private String skillExamResult;
    /**
     * 结业证书编码
     */
    @ExcelField(sort=24)
    private String graduationCertificateNo;



    /**
     * 状态：1 正常，-1删除
     */
    @ExcelField(sort=25)
    private Integer trainingStatus;
    /**
     * 备注
     */
    @ExcelField(sort=26)
    private String remark;

    public Long getHospitalId() {
        return hospitalId;
    }

    public void setHospitalId(Long hospitalId) {
        this.hospitalId = hospitalId;
    }

    public String getTrainingBase() {
        return trainingBase;
    }

    public void setTrainingBase(String trainingBase) {
        this.trainingBase = trainingBase;
    }

    public Integer getYear() {
        return year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public String getSpecialty() {
        return specialty;
    }

    public void setSpecialty(String specialty) {
        this.specialty = specialty;
    }

    public String getStudentName() {
        return studentName;
    }

    public void setStudentName(String studentName) {
        this.studentName = studentName;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public Integer getTrainingStatus() {
        return trainingStatus;
    }

    public void setTrainingStatus(Integer trainingStatus) {
        this.trainingStatus = trainingStatus;
    }

    public String getBatchName() {
        return batchName;
    }

    public void setBatchName(String batchName) {
        this.batchName = batchName;
    }


    public String getCertifiedExamScore() {
        return certifiedExamScore;
    }


    public void setCertifiedExamScore(String certifiedExamScore) {
        this.certifiedExamScore = certifiedExamScore;
    }


    public String getCertifiedExamDate() {
        return certifiedExamDate;
    }


    public void setCertifiedExamDate(String certifiedExamDate) {
        this.certifiedExamDate = certifiedExamDate;
    }


    public String getAnnualExamDate() {
        return annualExamDate;
    }


    public void setAnnualExamDate(String annualExamDate) {
        this.annualExamDate = annualExamDate;
    }


    public String getAnnualExamScore() {
        return annualExamScore;
    }


    public void setAnnualExamScore(String annualExamScore) {
        this.annualExamScore = annualExamScore;
    }


    public String getGraduationExamDate() {
        return graduationExamDate;
    }


    public void setGraduationExamDate(String graduationExamDate) {
        this.graduationExamDate = graduationExamDate;
    }


    public String getGraduationExamLevel() {
        return graduationExamLevel;
    }


    public void setGraduationExamLevel(String graduationExamLevel) {
        this.graduationExamLevel = graduationExamLevel;
    }


    public String getGraduationExamResult() {
        return graduationExamResult;
    }


    public void setGraduationExamResult(String graduationExamResult) {
        this.graduationExamResult = graduationExamResult;
    }


    public String getTheoreticalExamDate() {
        return theoreticalExamDate;
    }


    public void setTheoreticalExamDate(String theoreticalExamDate) {
        this.theoreticalExamDate = theoreticalExamDate;
    }


    public String getTheoreticalExamBase() {
        return theoreticalExamBase;
    }


    public void setTheoreticalExamBase(String theoreticalExamBase) {
        this.theoreticalExamBase = theoreticalExamBase;
    }


    public String getTheoreticalExamNo() {
        return theoreticalExamNo;
    }


    public void setTheoreticalExamNo(String theoreticalExamNo) {
        this.theoreticalExamNo = theoreticalExamNo;
    }


    public String getTheoreticalExamScore() {
        return theoreticalExamScore;
    }


    public void setTheoreticalExamScore(String theoreticalExamScore) {
        this.theoreticalExamScore = theoreticalExamScore;
    }


    public String getTheoreticalExamResult() {
        return theoreticalExamResult;
    }


    public void setTheoreticalExamResult(String theoreticalExamResult) {
        this.theoreticalExamResult = theoreticalExamResult;
    }


    public String getSkillExamDate() {
        return skillExamDate;
    }


    public void setSkillExamDate(String skillExamDate) {
        this.skillExamDate = skillExamDate;
    }


    public String getSkillExamBase() {
        return skillExamBase;
    }


    public void setSkillExamBase(String skillExamBase) {
        this.skillExamBase = skillExamBase;
    }


    public String getSkillExamNo() {
        return skillExamNo;
    }


    public void setSkillExamNo(String skillExamNo) {
        this.skillExamNo = skillExamNo;
    }


    public String getSkillExamScore() {
        return skillExamScore;
    }


    public void setSkillExamScore(String skillExamScore) {
        this.skillExamScore = skillExamScore;
    }


    public String getSkillExamResult() {
        return skillExamResult;
    }


    public void setSkillExamResult(String skillExamResult) {
        this.skillExamResult = skillExamResult;
    }


    public String getGraduationCertificateNo() {
        return graduationCertificateNo;
    }


    public void setGraduationCertificateNo(String graduationCertificateNo) {
        this.graduationCertificateNo = graduationCertificateNo;
    }


    public String getRemark() {
        return remark;
    }


    public void setRemark(String remark) {
        this.remark = remark;
    }


}
