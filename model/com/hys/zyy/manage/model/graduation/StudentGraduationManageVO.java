package com.hys.zyy.manage.model.graduation;

import com.hys.zyy.manage.interfaces.ExcelField;

/**
 * @author: HLB
 * @desc: 学员结业管理Vo类
 * @version: V1.0.0
 */
public class StudentGraduationManageVO extends StudentGraduationManage {
    /**
     * 培训基地(医院ID)
     */
    private Long hospitalId;

    /**
     * 培训基地
     */
    private String trainingBase;
    /**
     * 年级
     */
    private Integer year;
    /**
     * 专业
     */
    private String specialty;
    /**
     * 学员姓名
     */
    private String studentName;
    /**
     * 身份证号
     */
    private String idCard;
    /**
     * 培训状态
     */
    private Integer trainingStatus;
    /**
     * 是否是订单定向生  1-是  0-否
     */
    private Integer directStu;

    /**
     * 省级ID
     */
    private Long provinceId;
    /**
     * 批次名称
     */
    private String batchName;
    /**
     * 带教老师的id（带教老师查询结业成绩）
     */
    private Long teacherId;
    /**
     * 科室用户查询
     */
    private Long deptId;
    /**
     * 基地用户查询
     */
    private Long baseId;

    public String getTrainingBase() {
        return trainingBase;
    }

    public void setTrainingBase(String trainingBase) {
        this.trainingBase = trainingBase;
    }

    public Integer getYear() {
        return year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public String getSpecialty() {
        return specialty;
    }

    public void setSpecialty(String specialty) {
        this.specialty = specialty;
    }

    public String getStudentName() {
        return studentName;
    }

    public void setStudentName(String studentName) {
        this.studentName = studentName;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public Integer getTrainingStatus() {
        return trainingStatus;
    }

    public void setTrainingStatus(Integer trainingStatus) {
        this.trainingStatus = trainingStatus;
    }

    public Integer getDirectStu() {
        return directStu;
    }

    public void setDirectStu(Integer directStu) {
        this.directStu = directStu;
    }

    public Long getProvinceId() {
        return provinceId;
    }

    public void setProvinceId(Long provinceId) {
        this.provinceId = provinceId;
    }

    public Long getHospitalId() {
        return hospitalId;
    }

    public void setHospitalId(Long hospitalId) {
        this.hospitalId = hospitalId;
    }

    public String getBatchName() {
        return batchName;
    }

    public void setBatchName(String batchName) {
        this.batchName = batchName;
    }

    public Long getTeacherId() {
        return teacherId;
    }

    public void setTeacherId(Long teacherId) {
        this.teacherId = teacherId;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public Long getBaseId() {
        return baseId;
    }

    public void setBaseId(Long baseId) {
        this.baseId = baseId;
    }
}
