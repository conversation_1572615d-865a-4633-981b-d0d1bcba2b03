package com.hys.zyy.manage.model.newpkg;

import java.util.Date;
import java.util.List;

public class Supervisor {
	
	private Long id;
	private String title;
	private Date supervisorDate;//督导时间
	
	private Long createUserId;
	private Long createOrgId;
	private Date createDate;
	
	private int supervisorUserCount;//督导人数
	
	private String userIds;
	
	private List<SupervisorUser> supervisorUsers;
	private List<SupervisorFile> supervisorFiles;
	
	
	private String queryUserTypes[];//查询的用户类型，逗号分隔
	
	private String queryUserTypesStr;
	
	private Integer ckAll ;//1全选2全不选
	private String ckUserIds ;//逗号分隔的用户ID
	
	private String realName;
	
	
	
	
	public String getQueryUserTypesStr() {
		return queryUserTypesStr;
	}
	public void setQueryUserTypesStr(String queryUserTypesStr) {
		this.queryUserTypesStr = queryUserTypesStr;
	}
	public String getRealName() {
		return realName;
	}
	public void setRealName(String realName) {
		this.realName = realName;
	}
	public Integer getCkAll() {
		return ckAll;
	}
	public void setCkAll(Integer ckAll) {
		this.ckAll = ckAll;
	}
	public String getCkUserIds() {
		return ckUserIds;
	}
	public void setCkUserIds(String ckUserIds) {
		this.ckUserIds = ckUserIds;
	}
	
	public String[] getQueryUserTypes() {
		return queryUserTypes;
	}
	public void setQueryUserTypes(String[] queryUserTypes) {
		this.queryUserTypes = queryUserTypes;
	}
	public String getUserIds() {
		return userIds;
	}
	public void setUserIds(String userIds) {
		this.userIds = userIds;
	}
	public int getSupervisorUserCount() {
		return supervisorUserCount;
	}
	public void setSupervisorUserCount(int supervisorUserCount) {
		this.supervisorUserCount = supervisorUserCount;
	}
	public List<SupervisorUser> getSupervisorUsers() {
		return supervisorUsers;
	}
	public void setSupervisorUsers(List<SupervisorUser> supervisorUsers) {
		this.supervisorUsers = supervisorUsers;
	}
	public List<SupervisorFile> getSupervisorFiles() {
		return supervisorFiles;
	}
	public void setSupervisorFiles(List<SupervisorFile> supervisorFiles) {
		this.supervisorFiles = supervisorFiles;
	}
	
	
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String gettitle() {
		return title;
	}
	public void settitle(String title) {
		this.title = title;
	}
	public String getTitle() {
		return title;
	}
	public void setTitle(String title) {
		this.title = title;
	}
	public Date getSupervisorDate() {
		return supervisorDate;
	}
	public void setSupervisorDate(Date supervisorDate) {
		this.supervisorDate = supervisorDate;
	}
	public Long getCreateUserId() {
		return createUserId;
	}
	public void setCreateUserId(Long createUserId) {
		this.createUserId = createUserId;
	}
	public Long getCreateOrgId() {
		return createOrgId;
	}
	public void setCreateOrgId(Long createOrgId) {
		this.createOrgId = createOrgId;
	}
	public Date getCreateDate() {
		return createDate;
	}
	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}
	
}
