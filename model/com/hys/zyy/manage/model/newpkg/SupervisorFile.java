package com.hys.zyy.manage.model.newpkg;

import java.util.Date;

/**
 * 
 * @Description: 督导的文件
 * <AUTHOR>
 * @date 2020-9-2 下午5:11:06
 */
public class SupervisorFile {
	
	private Long id;
	private Long supervisorId;
	private String attchFileName;
	private String attchFilePath;
	private Date createDate;
	
	
	
	
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Date getCreateDate() {
		return createDate;
	}
	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}
	public Long getSupervisorId() {
		return supervisorId;
	}
	public void setSupervisorId(Long supervisorId) {
		this.supervisorId = supervisorId;
	}
	public String getAttchFileName() {
		return attchFileName;
	}
	public void setAttchFileName(String attchFileName) {
		this.attchFileName = attchFileName;
	}
	public String getAttchFilePath() {
		return attchFilePath;
	}
	public void setAttchFilePath(String attchFilePath) {
		this.attchFilePath = attchFilePath;
	}
	
	
	
}
