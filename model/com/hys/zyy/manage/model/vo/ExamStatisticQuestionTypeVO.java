package com.hys.zyy.manage.model.vo;

import com.hys.zyy.manage.model.ZyyBaseObject;
/**
 * 按题型统计
 * <AUTHOR>
 * @date 2020-2-17下午1:18:19
 */
public class ExamStatisticQuestionTypeVO extends ZyyBaseObject{

	/**
	 * 
	 */
	private static final long serialVersionUID = 4167523706193278889L;
	//题型
	private String typeName;
	//答对分数
	private String trueScore;
	//答题数量
	private Integer answerNum;
	//总分数
	private String totalScore;
	//答错分数
	private String errorScore;
	//平均分数
	private String avgScore;
	//正确率
	private String correctRate;
	//试题总数
	private Integer totalQuestionNum;
	//正确数量
	private Integer trueNum;
	//错误数量
	private Integer errorNum;
	//未答题数量
	private Integer noAnswerNum;
	
	public String getTypeName() {
		return typeName;
	}
	public void setTypeName(String typeName) {
		this.typeName = typeName;
	}
	public String getTrueScore() {
		return trueScore;
	}
	public void setTrueScore(String trueScore) {
		this.trueScore = trueScore;
	}
	public Integer getAnswerNum() {
		return answerNum;
	}
	public void setAnswerNum(Integer answerNum) {
		this.answerNum = answerNum;
	}
	public String getTotalScore() {
		return totalScore;
	}
	public void setTotalScore(String totalScore) {
		this.totalScore = totalScore;
	}
	public String getErrorScore() {
		return errorScore;
	}
	public void setErrorScore(String errorScore) {
		this.errorScore = errorScore;
	}
	public String getAvgScore() {
		return avgScore;
	}
	public void setAvgScore(String avgScore) {
		this.avgScore = avgScore;
	}
	public String getCorrectRate() {
		return correctRate;
	}
	public void setCorrectRate(String correctRate) {
		this.correctRate = correctRate;
	}
	public Integer getTotalQuestionNum() {
		return totalQuestionNum;
	}
	public void setTotalQuestionNum(Integer totalQuestionNum) {
		this.totalQuestionNum = totalQuestionNum;
	}
	public Integer getTrueNum() {
		return trueNum;
	}
	public void setTrueNum(Integer trueNum) {
		this.trueNum = trueNum;
	}
	public Integer getErrorNum() {
		return errorNum;
	}
	public void setErrorNum(Integer errorNum) {
		this.errorNum = errorNum;
	}
	public Integer getNoAnswerNum() {
		return noAnswerNum;
	}
	public void setNoAnswerNum(Integer noAnswerNum) {
		this.noAnswerNum = noAnswerNum;
	}
}
