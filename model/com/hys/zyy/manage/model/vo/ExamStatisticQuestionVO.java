package com.hys.zyy.manage.model.vo;

import com.hys.zyy.manage.model.ZyyBaseObject;

/**
 * 按试题分析 列表数据封装类
 * <AUTHOR>
 *
 */
public class ExamStatisticQuestionVO extends ZyyBaseObject {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	//题型
	private String typeName;
//	题干内容
	private String content;
//	难度系数
	private String grade;
//	答题人数
	private Integer answerNum;
//	答对人数
	private Integer trueNum;
//	正确率
	private String correctRate;
//	正确答案
	private String rightKey;
//	选项统计
	private String optionStatistic;
	public String getTypeName() {
		return typeName;
	}
	public void setTypeName(String typeName) {
		this.typeName = typeName;
	}
	public String getContent() {
		return content;
	}
	public void setContent(String content) {
		this.content = content;
	}
	public String getGrade() {
		return grade;
	}
	public void setGrade(String grade) {
		this.grade = grade;
	}
	public Integer getAnswerNum() {
		return answerNum;
	}
	public void setAnswerNum(Integer answerNum) {
		this.answerNum = answerNum;
	}
	public Integer getTrueNum() {
		return trueNum;
	}
	public void setTrueNum(Integer trueNum) {
		this.trueNum = trueNum;
	}
	public String getCorrectRate() {
		return correctRate;
	}
	public void setCorrectRate(String correctRate) {
		this.correctRate = correctRate;
	}
	public String getRightKey() {
		return rightKey;
	}
	public void setRightKey(String rightKey) {
		this.rightKey = rightKey;
	}
	public String getOptionStatistic() {
		return optionStatistic;
	}
	public void setOptionStatistic(String optionStatistic) {
		this.optionStatistic = optionStatistic;
	}
	
}
