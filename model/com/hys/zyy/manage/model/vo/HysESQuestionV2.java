package com.hys.zyy.manage.model.vo;

import java.io.Serializable;

public class HysESQuestionV2  implements Serializable{

    private static final long serialVersionUID = -8918381986007951543L;

    private Long id;//唯一标识
    private String labelCode;//试题类型Code
    private String labelName;//试题类型名称
    private Long parentId;//父级ID
    private Integer orderSeq;//试题序号（父子题，子题序号）
    private Integer subQuestionCount;//子题数量
    private String content;//题干，试题内容
    private Double difficultyGrade;//难度系数
    private String analyse;//试题分析
    private String source;//试题来源
    private Integer isnotMultimedia;//是否是多媒体试题 １:是; 0:否
    private String remarks;//备注信息
    private Integer status;//试题状态 0.删除 1.正常 2.禁用 3.重复
    private Long orgId;//机构ID
    private Long sourceOrgId;//来源机构ID
    private String storageIds;//所属题库
    private String propValId1s;//一级
    private String propValId2s;//二级
    private String propValId3s;//三级
    private String practOrgIds;//设置练习题库机构IDS
    private Long[] practiseOrgIds;//设置过练习题库的机构IDS

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getLabelCode() {
        return labelCode;
    }

    public void setLabelCode(String labelCode) {
        this.labelCode = labelCode;
    }

    public String getLabelName() {
        return labelName;
    }

    public void setLabelName(String labelName) {
        this.labelName = labelName;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public Integer getOrderSeq() {
        return orderSeq;
    }

    public void setOrderSeq(Integer orderSeq) {
        this.orderSeq = orderSeq;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Double getDifficultyGrade() {
        return difficultyGrade;
    }

    public void setDifficultyGrade(Double difficultyGrade) {
        this.difficultyGrade = difficultyGrade;
    }

    public String getAnalyse() {
        return analyse;
    }

    public void setAnalyse(String analyse) {
        this.analyse = analyse;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public Integer getIsnotMultimedia() {
        return isnotMultimedia;
    }

    public void setIsnotMultimedia(Integer isnotMultimedia) {
        this.isnotMultimedia = isnotMultimedia;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getPropValId1s() {
        return propValId1s;
    }

    public void setPropValId1s(String propValId1s) {
        this.propValId1s = propValId1s;
    }

    public String getPropValId2s() {
        return propValId2s;
    }

    public void setPropValId2s(String propValId2s) {
        this.propValId2s = propValId2s;
    }

    public String getPropValId3s() {
        return propValId3s;
    }

    public void setPropValId3s(String propValId3s) {
        this.propValId3s = propValId3s;
    }

    public Integer getSubQuestionCount() {
        return subQuestionCount;
    }

    public void setSubQuestionCount(Integer subQuestionCount) {
        this.subQuestionCount = subQuestionCount;
    }

    public String getStorageIds() {
        return storageIds;
    }

    public void setStorageIds(String storageIds) {
        this.storageIds = storageIds;
    }

    public Long[] getPractiseOrgIds() {
        return practiseOrgIds;
    }

    public void setPractiseOrgIds(Long[] practiseOrgIds) {
        this.practiseOrgIds = practiseOrgIds;
    }

    public Long getOrgId() {
        return orgId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    public Long getSourceOrgId() {
        return sourceOrgId;
    }

    public String getPractOrgIds() {
        return practOrgIds;
    }

    public void setPractOrgIds(String practOrgIds) {
        this.practOrgIds = practOrgIds;
    }

    public void setSourceOrgId(Long sourceOrgId) {
        this.sourceOrgId = sourceOrgId;
    }

    public HysESQuestionV2() {
    }

    public HysESQuestionV2(Long id, String labelCode, String labelName, Long parentId, Integer orderSeq, Integer subQuestionCount,
                         String content, Double difficultyGrade, String analyse, String source, Integer isnotMultimedia,
                         Long orgId, Long sourceOrgId, String remarks, Integer status) {
        this.id = id;
        this.labelCode = labelCode;
        this.labelName = labelName;
        this.parentId = parentId;
        this.orderSeq = orderSeq;
        this.subQuestionCount = subQuestionCount;
        this.content = content;
        this.difficultyGrade = difficultyGrade;
        this.analyse = analyse;
        this.source = source;
        this.isnotMultimedia = isnotMultimedia;
        this.orgId = orgId;
        this.sourceOrgId = sourceOrgId;
        this.remarks = remarks;
        this.status = status;
    }

}
