package com.hys.zyy.manage.model.vo;

import java.util.List;

import com.hys.zyy.manage.model.ZyyActivityTypeVO;
import com.hys.zyy.manage.model.ZyyBaseObject;

/**
 * @Desc 绩效统计-学员统计查询
 * <AUTHOR>
 */
public class StatistStuQuery extends ZyyBaseObject{

	/**
	 * 
	 */
	private static final long serialVersionUID = -7460216336291842465L;
	
	private Integer baseInfoTag = 7;//基本信息
	private Integer cycleInfoTag = 2;//轮转部分
	private Integer outDeptInfoTag = 0;//出入科
	private Integer activityTag = 0;//教学活动
	private Integer attendInfoTag = 0;//考勤
	private Integer hdBookInfoTag = 0;//登记手册
	private Integer evalInfoTag = 0;//评价
	private Integer examInfoTag = 0;//考试

	private Integer cycledDeptNumFlag=0;//已轮转科室标识
	private Integer cycledMonthNumFlag=0;//已轮转时间标识
	private Integer enterTeachFlag=0;//入科教育标识
	private Integer enterTeachRateFlag=0;//入科教育出勤率
	private Integer outDeptNumFlag=0;//合格出科
	private Integer outDeptRate=0;//合格出科率
	private Integer attendedActivityFlag=0;//参加教学活动
	private Integer activityAttendRateFlag=0;//教学活动出勤率
	private Integer cdFlag=0;// 迟到次数
	private Integer ztFlag=0;// 早退次数
	private Integer kgFlag=0;// 旷工次数
	private Integer qjFlag=0;// 事假次数
	private Integer hbFlag=0;//手册总数
	private Integer hbSubFlag=0;//手册提交本数
	private Integer hbPassRateFlag=0;//手册通过率
	private Integer caseSubFlag=0;//病例提交数
	private Integer caseAvgScoreFlag=0;//病例提交平均分
	private Integer evalPatientFlag=0;//病人评价平均分
	private Integer evalNurseFlag=0;//护士评价平均分
	private Integer evalTeachFlag=0;//带教评价平均分
	private Integer evalDeptFlag=0;//科室评价平均分
	private Integer evalBaseFlag=0;//专业基地评价平均分
	private Integer evalHospFlag=0;//医院评价平均分
	private Integer needDoneEval=0; //我应该评价/已评价
	private Integer examAttendRateFlag=0;//考试出勤率
	private Integer examAvgScoreFlag=0;//平均分
	private Integer examRankingFlag=0;//排名
	
	private Long zyyUserProvinceId;//省份ID
	private Long orgId;//医院ID
	private Long baseId;//专业基地ID(专业ID)
	private Long yearId;//年份ID
	private Integer residencySource;//人员类型
	private Integer userCategory;
	private String userCategoryStr;
	private String queryKey;//关键词
	private String qStartDate;//查询开始时间
	private String qEndDate;//查询结束时间
	private Integer stuStatus;//培训状态   1 正常  结业  延长毕业  退陪
	private Integer hbAuditLevel;// 11-带教,	9-科室,	7-基地,	5-医院
	
	//系统活动类型
	private List<ZyyActivityTypeVO> activityTypeList;
	//选择的活动类型
	private String activityType;
	//查询类型 1 ： 点击查询  null ： 点击菜单第一次进入
	private Integer queryType;
	
	
	public Integer getBaseInfoTag() {
		return baseInfoTag;
	}
	public void setBaseInfoTag(Integer baseInfoTag) {
		this.baseInfoTag = baseInfoTag;
	}
	public Integer getCycleInfoTag() {
		return cycleInfoTag;
	}
	public void setCycleInfoTag(Integer cycleInfoTag) {
		this.cycleInfoTag = cycleInfoTag;
	}
	public Integer getOutDeptInfoTag() {
		return outDeptInfoTag;
	}
	public void setOutDeptInfoTag(Integer outDeptInfoTag) {
		this.outDeptInfoTag = outDeptInfoTag;
	}
	public Integer getActivityTag() {
		return activityTag;
	}
	public void setActivityTag(Integer activityTag) {
		this.activityTag = activityTag;
	}
	public Integer getAttendInfoTag() {
		return attendInfoTag;
	}
	public void setAttendInfoTag(Integer attendInfoTag) {
		this.attendInfoTag = attendInfoTag;
	}
	public Integer getHdBookInfoTag() {
		return hdBookInfoTag;
	}
	public void setHdBookInfoTag(Integer hdBookInfoTag) {
		this.hdBookInfoTag = hdBookInfoTag;
	}
	public Integer getEvalInfoTag() {
		return evalInfoTag;
	}
	public void setEvalInfoTag(Integer evalInfoTag) {
		this.evalInfoTag = evalInfoTag;
	}
	public Integer getExamInfoTag() {
		return examInfoTag;
	}
	public void setExamInfoTag(Integer examInfoTag) {
		this.examInfoTag = examInfoTag;
	}
	public Integer getCycledDeptNumFlag() {
		return cycledDeptNumFlag;
	}
	public void setCycledDeptNumFlag(Integer cycledDeptNumFlag) {
		this.cycledDeptNumFlag = cycledDeptNumFlag;
	}
	public Integer getCycledMonthNumFlag() {
		return cycledMonthNumFlag;
	}
	public void setCycledMonthNumFlag(Integer cycledMonthNumFlag) {
		this.cycledMonthNumFlag = cycledMonthNumFlag;
	}
	public Integer getEnterTeachFlag() {
		return enterTeachFlag;
	}
	public void setEnterTeachFlag(Integer enterTeachFlag) {
		this.enterTeachFlag = enterTeachFlag;
	}
	public Integer getEnterTeachRateFlag() {
		return enterTeachRateFlag;
	}
	public void setEnterTeachRateFlag(Integer enterTeachRateFlag) {
		this.enterTeachRateFlag = enterTeachRateFlag;
	}
	public Integer getOutDeptNumFlag() {
		return outDeptNumFlag;
	}
	public void setOutDeptNumFlag(Integer outDeptNumFlag) {
		this.outDeptNumFlag = outDeptNumFlag;
	}
	public Integer getOutDeptRate() {
		return outDeptRate;
	}
	public void setOutDeptRate(Integer outDeptRate) {
		this.outDeptRate = outDeptRate;
	}
	public Integer getAttendedActivityFlag() {
		return attendedActivityFlag;
	}
	public void setAttendedActivityFlag(Integer attendedActivityFlag) {
		this.attendedActivityFlag = attendedActivityFlag;
	}
	public Integer getActivityAttendRateFlag() {
		return activityAttendRateFlag;
	}
	public void setActivityAttendRateFlag(Integer activityAttendRateFlag) {
		this.activityAttendRateFlag = activityAttendRateFlag;
	}
	public Integer getCdFlag() {
		return cdFlag;
	}
	public void setCdFlag(Integer cdFlag) {
		this.cdFlag = cdFlag;
	}
	public Integer getZtFlag() {
		return ztFlag;
	}
	public void setZtFlag(Integer ztFlag) {
		this.ztFlag = ztFlag;
	}
	public Integer getKgFlag() {
		return kgFlag;
	}
	public void setKgFlag(Integer kgFlag) {
		this.kgFlag = kgFlag;
	}
	public Integer getQjFlag() {
		return qjFlag;
	}
	public void setQjFlag(Integer qjFlag) {
		this.qjFlag = qjFlag;
	}
	public Integer getHbFlag() {
		return hbFlag;
	}
	public void setHbFlag(Integer hbFlag) {
		this.hbFlag = hbFlag;
	}
	public Integer getHbSubFlag() {
		return hbSubFlag;
	}
	public void setHbSubFlag(Integer hbSubFlag) {
		this.hbSubFlag = hbSubFlag;
	}
	public Integer getHbPassRateFlag() {
		return hbPassRateFlag;
	}
	public void setHbPassRateFlag(Integer hbPassRateFlag) {
		this.hbPassRateFlag = hbPassRateFlag;
	}
	public Integer getCaseSubFlag() {
		return caseSubFlag;
	}
	public void setCaseSubFlag(Integer caseSubFlag) {
		this.caseSubFlag = caseSubFlag;
	}
	public Integer getCaseAvgScoreFlag() {
		return caseAvgScoreFlag;
	}
	public void setCaseAvgScoreFlag(Integer caseAvgScoreFlag) {
		this.caseAvgScoreFlag = caseAvgScoreFlag;
	}
	public Integer getEvalPatientFlag() {
		return evalPatientFlag;
	}
	public void setEvalPatientFlag(Integer evalPatientFlag) {
		this.evalPatientFlag = evalPatientFlag;
	}
	public Integer getEvalNurseFlag() {
		return evalNurseFlag;
	}
	public void setEvalNurseFlag(Integer evalNurseFlag) {
		this.evalNurseFlag = evalNurseFlag;
	}
	public Integer getEvalTeachFlag() {
		return evalTeachFlag;
	}
	public void setEvalTeachFlag(Integer evalTeachFlag) {
		this.evalTeachFlag = evalTeachFlag;
	}
	public Integer getEvalDeptFlag() {
		return evalDeptFlag;
	}
	public void setEvalDeptFlag(Integer evalDeptFlag) {
		this.evalDeptFlag = evalDeptFlag;
	}
	public Integer getEvalBaseFlag() {
		return evalBaseFlag;
	}
	public void setEvalBaseFlag(Integer evalBaseFlag) {
		this.evalBaseFlag = evalBaseFlag;
	}
	public Integer getEvalHospFlag() {
		return evalHospFlag;
	}
	public void setEvalHospFlag(Integer evalHospFlag) {
		this.evalHospFlag = evalHospFlag;
	}
	public Integer getExamAttendRateFlag() {
		return examAttendRateFlag;
	}
	public void setExamAttendRateFlag(Integer examAttendRateFlag) {
		this.examAttendRateFlag = examAttendRateFlag;
	}
	public Integer getExamAvgScoreFlag() {
		return examAvgScoreFlag;
	}
	public void setExamAvgScoreFlag(Integer examAvgScoreFlag) {
		this.examAvgScoreFlag = examAvgScoreFlag;
	}
	public Integer getExamRankingFlag() {
		return examRankingFlag;
	}
	public void setExamRankingFlag(Integer examRankingFlag) {
		this.examRankingFlag = examRankingFlag;
	}
	public Long getBaseId() {
		return baseId;
	}
	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}
	public Long getYearId() {
		return yearId;
	}
	public void setYearId(Long yearId) {
		this.yearId = yearId;
	}
	public Integer getResidencySource() {
		return residencySource;
	}
	public void setResidencySource(Integer residencySource) {
		this.residencySource = residencySource;
	}
	
	public Integer getUserCategory() {
		return userCategory;
	}
	public void setUserCategory(Integer userCategory) {
		this.userCategory = userCategory;
	}
	public String getUserCategoryStr() {
		return userCategoryStr;
	}
	public void setUserCategoryStr(String userCategoryStr) {
		this.userCategoryStr = userCategoryStr == null ? null : userCategoryStr.trim();
	}
	public String getQueryKey() {
		return queryKey;
	}
	public void setQueryKey(String queryKey) {
		this.queryKey = queryKey;
	}
	public String getqStartDate() {
		return qStartDate;
	}
	public void setqStartDate(String qStartDate) {
		this.qStartDate = qStartDate;
	}
	public String getqEndDate() {
		return qEndDate;
	}
	public void setqEndDate(String qEndDate) {
		this.qEndDate = qEndDate;
	}
	public Integer getStuStatus() {
		return stuStatus;
	}
	public void setStuStatus(Integer stuStatus) {
		this.stuStatus = stuStatus;
	}
	public Long getZyyUserProvinceId() {
		return zyyUserProvinceId;
	}
	public void setZyyUserProvinceId(Long zyyUserProvinceId) {
		this.zyyUserProvinceId = zyyUserProvinceId;
	}
	public Long getOrgId() {
		return orgId;
	}
	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}
	public Integer getHbAuditLevel() {
		return hbAuditLevel;
	}
	public void setHbAuditLevel(Integer hbAuditLevel) {
		this.hbAuditLevel = hbAuditLevel;
	}
	public Integer getNeedDoneEval() {
		return needDoneEval;
	}
	public void setNeedDoneEval(Integer needDoneEval) {
		this.needDoneEval = needDoneEval;
	}
	public List<ZyyActivityTypeVO> getActivityTypeList() {
		return activityTypeList;
	}
	public void setActivityTypeList(List<ZyyActivityTypeVO> activityTypeList) {
		this.activityTypeList = activityTypeList;
	}
	public String getActivityType() {
		return activityType;
	}
	public void setActivityType(String activityType) {
		this.activityType = activityType;
	}
	public Integer getQueryType() {
		return queryType;
	}
	public void setQueryType(Integer queryType) {
		this.queryType = queryType;
	}
	/**
	 * @desc 确定要显示的列
	 */
	public void formatTag(){
		outDeptInfoTag = enterTeachFlag+enterTeachRateFlag+outDeptNumFlag+outDeptRate;
		activityTag = attendedActivityFlag+activityAttendRateFlag;
		attendInfoTag = cdFlag+ztFlag+kgFlag+qjFlag;
		hdBookInfoTag = hbFlag+hbSubFlag+hbPassRateFlag+caseSubFlag+caseAvgScoreFlag;
		evalInfoTag = evalPatientFlag+evalNurseFlag+evalTeachFlag+evalDeptFlag+evalBaseFlag+evalHospFlag+needDoneEval;
		examInfoTag = examAttendRateFlag+examAvgScoreFlag+examRankingFlag;
	}
}
