package com.hys.zyy.manage.model.vo;

import java.util.List;

import com.hys.zyy.manage.constants.CommonConstants;
import com.hys.zyy.manage.model.ZyyActivityTypeVO;
import com.hys.zyy.manage.model.ZyyBaseObject;

/**
 * @Desc 绩效统计-学员统计
 * <AUTHOR>
 */
public class StatistStuVO extends ZyyBaseObject{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1013532722540450155L;
	
	private Long userId;//用户ID
	private String realName;//真实姓名
	private Integer sex;//性别
	private String certificateNo;//身份证号
	private String mobilNumber;
	private Integer residencySource;//身份类型
	private String year;//年级
	private Long yearId;//年纪ID
	private String baseName;//专业基地名称
	private Long baseId;//专业基地ID
	private Integer schoolSystem;//培训年限
	private String deptName;//当前轮转科室
	private Integer cycledDeptNum = 0;//已轮转科室
	private Integer cycleDeptNum = 0;//轮转科室总数
	private Integer cycleType;//轮转类型 2  周  3 月
	private Integer cycledMonthNum;//已轮转月份
	private Integer cycleMonthNum;//轮转总时间
	private Integer attendedEnterTeachNum = 0;//已参加轮转入科教育
	private Integer attendEnterTeachNum = 0;//需要参加轮转入科教育
	private String enterTeachAttendRate = CommonConstants.PERSENT_ZERO;//入科教育出勤率
	private Integer outDeptNum = 0;//出科次数
	private Integer outDeptOkNum = 0;//合格合格出科
	private String outDeptRate = CommonConstants.PERSENT_ZERO;//合格出科率
	private Integer attendedActivityNum = 0;//参加教学活动次数
	private Integer attendActivityNum = 0;//需要参加教学活动次数
	private String activityAttendRate = CommonConstants.PERSENT_ZERO;//教学活动出勤率
	private Integer cd=0;// 迟到次数
	private Integer zt=0;// 早退次数
	private Integer kg=0;// 旷工次数
	private Integer qj=0;// 事假次数
	private Integer handBookNum = 0;//登记手册总数
	private Integer handBookSubNum = 0;//登记手册提交次数
	private Integer handBookTotalAudit = 0;//审核过的手册数量
	private Integer handBookPassAudit = 0;//审核通过的手册数量
	private String handBookPassRate = CommonConstants.PERSENT_ZERO;//手册通过率
	private Integer caseSubNum = 0;//病例提交数
	private String caseAvgScore = "0";//病例平均分
	private String avgScoreEvalPatient = "0";//病人评价平均分
	private String avgScoreEvalNurse = "0";//护士评价平均分
	private String avgScoreEvalTeach = "0";//带教评价平均分
	private String avgScoreEvalDept = "0";//科室评价平均分
	private String avgScoreEvalBase = "0";//专业基地评价平均分
	private String avgScoreEvalHosp = "0";//医院评价平均分
	private String needDoneEval = "0/0";//我应该评价/已评价
	private String examAttendRate = CommonConstants.PERSENT_ZERO;//考试出勤率
	private String examAvgScore;//平均分
	private Double examScoreRate;//得分比 
	private Integer examRanking;//排名
	//学员的教学活动 不同的活动类型
	private List<ZyyActivityTypeVO> activityTypeList;
	
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	public String getRealName() {
		return realName;
	}
	public void setRealName(String realName) {
		this.realName = realName;
	}
	public Integer getSex() {
		return sex;
	}
	public void setSex(Integer sex) {
		this.sex = sex;
	}
	public String getCertificateNo() {
		return certificateNo;
	}
	public void setCertificateNo(String certificateNo) {
		this.certificateNo = certificateNo;
	}
	public Integer getResidencySource() {
		return residencySource;
	}
	public void setResidencySource(Integer residencySource) {
		this.residencySource = residencySource;
	}
	public String getYear() {
		return year;
	}
	public void setYear(String year) {
		this.year = year;
	}
	public Long getYearId() {
		return yearId;
	}
	public void setYearId(Long yearId) {
		this.yearId = yearId;
	}
	public String getBaseName() {
		return baseName;
	}
	public void setBaseName(String baseName) {
		this.baseName = baseName;
	}
	public Long getBaseId() {
		return baseId;
	}
	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}
	public Integer getSchoolSystem() {
		return schoolSystem;
	}
	public void setSchoolSystem(Integer schoolSystem) {
		this.schoolSystem = schoolSystem;
	}
	public Integer getCycledDeptNum() {
		return cycledDeptNum;
	}
	public void setCycledDeptNum(Integer cycledDeptNum) {
		this.cycledDeptNum = cycledDeptNum;
	}
	public Integer getCycleDeptNum() {
		return cycleDeptNum;
	}
	public void setCycleDeptNum(Integer cycleDeptNum) {
		this.cycleDeptNum = cycleDeptNum;
	}
	public Integer getCycledMonthNum() {
		return cycledMonthNum;
	}
	public void setCycledMonthNum(Integer cycledMonthNum) {
		this.cycledMonthNum = cycledMonthNum;
	}
	public Integer getCycleMonthNum() {
		return cycleMonthNum;
	}
	public void setCycleMonthNum(Integer cycleMonthNum) {
		this.cycleMonthNum = cycleMonthNum;
	}
	public Integer getAttendedEnterTeachNum() {
		return attendedEnterTeachNum;
	}
	public void setAttendedEnterTeachNum(Integer attendedEnterTeachNum) {
		this.attendedEnterTeachNum = attendedEnterTeachNum;
	}
	public Integer getAttendEnterTeachNum() {
		return attendEnterTeachNum;
	}
	public void setAttendEnterTeachNum(Integer attendEnterTeachNum) {
		this.attendEnterTeachNum = attendEnterTeachNum;
	}
	public String getEnterTeachAttendRate() {
		return enterTeachAttendRate;
	}
	public void setEnterTeachAttendRate(String enterTeachAttendRate) {
		this.enterTeachAttendRate = enterTeachAttendRate;
	}
	public Integer getOutDeptNum() {
		return outDeptNum;
	}
	public void setOutDeptNum(Integer outDeptNum) {
		this.outDeptNum = outDeptNum;
	}
	public Integer getOutDeptOkNum() {
		return outDeptOkNum;
	}
	public void setOutDeptOkNum(Integer outDeptOkNum) {
		this.outDeptOkNum = outDeptOkNum;
	}
	public String getOutDeptRate() {
		return outDeptRate;
	}
	public void setOutDeptRate(String outDeptRate) {
		this.outDeptRate = outDeptRate;
	}
	public Integer getAttendedActivityNum() {
		return attendedActivityNum;
	}
	public void setAttendedActivityNum(Integer attendedActivityNum) {
		this.attendedActivityNum = attendedActivityNum;
	}
	public Integer getAttendActivityNum() {
		return attendActivityNum;
	}
	public void setAttendActivityNum(Integer attendActivityNum) {
		this.attendActivityNum = attendActivityNum;
	}
	public String getActivityAttendRate() {
		return activityAttendRate;
	}
	public void setActivityAttendRate(String activityAttendRate) {
		this.activityAttendRate = activityAttendRate;
	}
	public Integer getCd() {
		return cd;
	}
	public void setCd(Integer cd) {
		this.cd = cd;
	}
	public Integer getZt() {
		return zt;
	}
	public void setZt(Integer zt) {
		this.zt = zt;
	}
	public Integer getKg() {
		return kg;
	}
	public void setKg(Integer kg) {
		this.kg = kg;
	}
	public Integer getQj() {
		return qj;
	}
	public void setQj(Integer qj) {
		this.qj = qj;
	}
	public Integer getHandBookNum() {
		return handBookNum;
	}
	public void setHandBookNum(Integer handBookNum) {
		this.handBookNum = handBookNum;
	}
	public Integer getHandBookSubNum() {
		return handBookSubNum;
	}
	public void setHandBookSubNum(Integer handBookSubNum) {
		this.handBookSubNum = handBookSubNum;
	}
	public Integer getCaseSubNum() {
		return caseSubNum;
	}
	public void setCaseSubNum(Integer caseSubNum) {
		this.caseSubNum = caseSubNum;
	}
	public String getCaseAvgScore() {
		return caseAvgScore;
	}
	public void setCaseAvgScore(String caseAvgScore) {
		this.caseAvgScore = caseAvgScore;
	}
	public String getHandBookPassRate() {
		return handBookPassRate;
	}
	public String getAvgScoreEvalPatient() {
		return avgScoreEvalPatient;
	}
	public void setAvgScoreEvalPatient(String avgScoreEvalPatient) {
		this.avgScoreEvalPatient = avgScoreEvalPatient;
	}
	public String getAvgScoreEvalNurse() {
		return avgScoreEvalNurse;
	}
	public void setAvgScoreEvalNurse(String avgScoreEvalNurse) {
		this.avgScoreEvalNurse = avgScoreEvalNurse;
	}
	public String getAvgScoreEvalTeach() {
		return avgScoreEvalTeach;
	}
	public void setAvgScoreEvalTeach(String avgScoreEvalTeach) {
		this.avgScoreEvalTeach = avgScoreEvalTeach;
	}
	public String getAvgScoreEvalDept() {
		return avgScoreEvalDept;
	}
	public void setAvgScoreEvalDept(String avgScoreEvalDept) {
		this.avgScoreEvalDept = avgScoreEvalDept;
	}
	public String getAvgScoreEvalBase() {
		return avgScoreEvalBase;
	}
	public void setAvgScoreEvalBase(String avgScoreEvalBase) {
		this.avgScoreEvalBase = avgScoreEvalBase;
	}
	public String getAvgScoreEvalHosp() {
		return avgScoreEvalHosp;
	}
	public void setAvgScoreEvalHosp(String avgScoreEvalHosp) {
		this.avgScoreEvalHosp = avgScoreEvalHosp;
	}
	public String getExamAttendRate() {
		return examAttendRate;
	}
	public void setExamAttendRate(String examAttendRate) {
		this.examAttendRate = examAttendRate;
	}
	public String getExamAvgScore() {
		return examAvgScore;
	}
	public void setExamAvgScore(String examAvgScore) {
		this.examAvgScore = examAvgScore;
	}
	public Integer getExamRanking() {
		return examRanking;
	}
	public void setExamRanking(Integer examRanking) {
		this.examRanking = examRanking;
	}
	public String getDeptName() {
		return deptName;
	}
	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}
	public Integer getCycleType() {
		return cycleType;
	}
	public void setCycleType(Integer cycleType) {
		this.cycleType = cycleType;
	}
	public Integer getHandBookTotalAudit() {
		return handBookTotalAudit;
	}
	public void setHandBookTotalAudit(Integer handBookTotalAudit) {
		this.handBookTotalAudit = handBookTotalAudit;
	}
	public Integer getHandBookPassAudit() {
		return handBookPassAudit;
	}
	public void setHandBookPassAudit(Integer handBookPassAudit) {
		this.handBookPassAudit = handBookPassAudit;
	}
	public void setHandBookPassRate(String handBookPassRate) {
		this.handBookPassRate = handBookPassRate;
	}
	public Double getExamScoreRate() {
		return examScoreRate;
	}
	public void setExamScoreRate(Double examScoreRate) {
		this.examScoreRate = examScoreRate;
	}
	public String getMobilNumber() {
		return mobilNumber;
	}
	public void setMobilNumber(String mobilNumber) {
		this.mobilNumber = mobilNumber;
	}
	public String getNeedDoneEval() {
		return needDoneEval;
	}
	public void setNeedDoneEval(String needDoneEval) {
		this.needDoneEval = needDoneEval;
	}
	public List<ZyyActivityTypeVO> getActivityTypeList() {
		return activityTypeList;
	}
	public void setActivityTypeList(List<ZyyActivityTypeVO> activityTypeList) {
		this.activityTypeList = activityTypeList;
	}
}
