package com.hys.zyy.manage.model.vo;

import java.io.Serializable;

public class StatisticsVO implements Serializable {

	private static final long serialVersionUID = 1L;
	private Long id; // ID
	private String name; // 名称
	private Long stdNum; // 人员数量
	private String residencyIdsStr; // 人员ID，以“,”分割
	private Double completePercent; // 完成百分比

	public StatisticsVO() {
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Long getStdNum() {
		return stdNum;
	}

	public void setStdNum(Long stdNum) {
		this.stdNum = stdNum;
	}

	public String getResidencyIdsStr() {
		return residencyIdsStr;
	}

	public void setResidencyIdsStr(String residencyIdsStr) {
		this.residencyIdsStr = residencyIdsStr;
	}

	public Double getCompletePercent() {
		return completePercent;
	}

	public void setCompletePercent(Double completePercent) {
		this.completePercent = completePercent;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		StatisticsVO other = (StatisticsVO) obj;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		return true;
	}

	@Override
	public String toString() {
		return "StatisticsVO [id=" + id + ", name=" + name + ", stdNum="
				+ stdNum + ", residencyIdsStr=" + residencyIdsStr
				+ ", completePercent=" + completePercent + "]";
	}

}
