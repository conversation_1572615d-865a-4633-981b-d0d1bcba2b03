package com.hys.zyy.manage.model.vo;

import java.util.List;

import com.hys.zyy.manage.model.ZyyBaseUser;
import com.hys.zyy.manage.model.ZyyTeacherExtendVO;
import com.hys.zyy.manage.model.ZyyUserExtendVO;

public class TeacherVO extends ZyyUserExtendVO {

	private static final long serialVersionUID = 1L;
	/*
	 * 带教老师专业
	 */
	private List<ZyyBaseUser> zyyBaseUser;
	/*
	 * 带教老师专业字符串
	 */
	private String zyyBaseUserStr;
	
	private String baseAliasName;
	
	//带教状态  1：带教 2：非带教
	private Integer status;
	
	private String oldCertificateNo;
	/*
	 * 带教扩展信息
	 */
	private ZyyTeacherExtendVO teacherExtend;
	private String yszyfws;
	
	public String getOldCertificateNo() {
		return oldCertificateNo;
	}

	public void setOldCertificateNo(String oldCertificateNo) {
		this.oldCertificateNo = oldCertificateNo;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public List<ZyyBaseUser> getZyyBaseUser() {
		return zyyBaseUser;
	}

	public void setZyyBaseUser(List<ZyyBaseUser> zyyBaseUser) {
		this.zyyBaseUser = zyyBaseUser;
	}

	public String getZyyBaseUserStr() {
		return zyyBaseUserStr;
	}

	public void setZyyBaseUserStr(String zyyBaseUserStr) {
		this.zyyBaseUserStr = zyyBaseUserStr;
	}

	public String getBaseAliasName() {
		return baseAliasName;
	}

	public void setBaseAliasName(String baseAliasName) {
		this.baseAliasName = baseAliasName;
	}

	public ZyyTeacherExtendVO getTeacherExtend() {
		return teacherExtend;
	}

	public void setTeacherExtend(ZyyTeacherExtendVO teacherExtend) {
		this.teacherExtend = teacherExtend;
	}

	public String getYszyfws() {
		return yszyfws;
	}

	public void setYszyfws(String yszyfws) {
		this.yszyfws = yszyfws;
	}
	
}