package com.hys.zyy.manage.model.vo;

import com.hys.zyy.manage.model.ZyyAccountRel;

public class ZyyAccountRelVO extends ZyyAccountRel {

	/**
	 * 
	 */
	private static final long serialVersionUID = 9130990572786029609L;
	
	private String accountName; //账户名称
	private String realName;//用户姓名
	private Integer zyyUserType;//用户身份类型
	private Long deptId;//科室ID
	private String deptName;//科室名称
	private Long baseId;//专业基地ID
	private String baseName;//专业基地名称
	private String showUnitName;//最终展示的身份单位字段
	private Integer subAccount;//子账户
	public String getAccountName() {
		return accountName;
	}
	public void setAccountName(String accountName) {
		this.accountName = accountName;
	}
	public String getRealName() {
		return realName;
	}
	public void setRealName(String realName) {
		this.realName = realName;
	}
	public Integer getZyyUserType() {
		return zyyUserType;
	}
	public void setZyyUserType(Integer zyyUserType) {
		this.zyyUserType = zyyUserType;
	}
	public String getDeptName() {
		return deptName;
	}
	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}
	public String getBaseName() {
		return baseName;
	}
	public void setBaseName(String baseName) {
		this.baseName = baseName;
	}
	public Integer getSubAccount() {
		return subAccount;
	}
	public void setSubAccount(Integer subAccount) {
		this.subAccount = subAccount;
	}
	public String getShowUnitName() {
		return showUnitName;
	}
	public void setShowUnitName(String showUnitName) {
		this.showUnitName = showUnitName == null ? null : showUnitName.trim();
	}
	public Long getDeptId() {
		return deptId;
	}
	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}
	public Long getBaseId() {
		return baseId;
	}
	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}
}
