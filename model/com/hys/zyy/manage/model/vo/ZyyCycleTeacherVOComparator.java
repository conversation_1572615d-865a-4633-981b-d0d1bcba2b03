package com.hys.zyy.manage.model.vo;

import java.util.Comparator;
import java.util.Date;

import com.hys.zyy.manage.model.ZyyCycleTeacherVO;

/**
 * 特殊排序
 * <AUTHOR>
 * @date 2018-12-24上午11:21:48
 */
public class ZyyCycleTeacherVOComparator implements Comparator<ZyyCycleTeacherVO> {

	@Override
	public int compare(ZyyCycleTeacherVO o1, ZyyCycleTeacherVO o2) {
		Date start = o1.getCycleStartDate();
		Date end = o2.getCycleEndDate();
		if (start == null || end == null){
			return 0;
		}
		return end.compareTo(start);
	}

}
