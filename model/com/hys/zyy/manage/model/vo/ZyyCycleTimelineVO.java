package com.hys.zyy.manage.model.vo;

import java.io.Serializable;
import java.util.Date;

/**
 * zyy_cycle_timeline表的vo类
 * <AUTHOR>
 * @date 2018-12-4上午10:52:26
 */
public class ZyyCycleTimelineVO implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	//主键
	private Long id;	 	
	//学员ID
	private Long residencyId; 
	//科室ID
	private Long deptId;	 
	private String deptName;
	//入科时间
	private Date startDate;
	private String startDateStr;
	//出科时间
	private Date endDate;	 
	private String endDateStr;
	//时间段，段与段中间用，分割
	private String TIME_SECTION; 
	//轮转状态
	private String cycleStatus;
    //病历数量
	private Integer medicalCount;
	
	//轮转表id
	private Long cycleTableId;     
	//最终审核状态
	private Integer finalCheckStatus; 
	//入科时间
	private Date enterDate;
	 //出科时间
	private Date leaveDate;
	//最终审核时间
	private Date finalCheckDate;  
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getResidencyId() {
		return residencyId;
	}
	public void setResidencyId(Long residencyId) {
		this.residencyId = residencyId;
	}
	public Long getDeptId() {
		return deptId;
	}
	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}
	public String getDeptName() {
		return deptName;
	}
	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}
	public Date getStartDate() {
		return startDate;
	}
	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}
	public String getStartDateStr() {
		return startDateStr;
	}
	public void setStartDateStr(String startDateStr) {
		this.startDateStr = startDateStr;
	}
	public Date getEndDate() {
		return endDate;
	}
	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}
	public String getEndDateStr() {
		return endDateStr;
	}
	public void setEndDateStr(String endDateStr) {
		this.endDateStr = endDateStr;
	}
	public String getTIME_SECTION() {
		return TIME_SECTION;
	}
	public void setTIME_SECTION(String tIME_SECTION) {
		TIME_SECTION = tIME_SECTION;
	}
	public String getCycleStatus() {
		return cycleStatus;
	}
	public void setCycleStatus(String cycleStatus) {
		this.cycleStatus = cycleStatus;
	}
	public Integer getMedicalCount() {
		return medicalCount;
	}
	public void setMedicalCount(Integer medicalCount) {
		this.medicalCount = medicalCount;
	}
	public Long getCycleTableId() {
		return cycleTableId;
	}
	public void setCycleTableId(Long cycleTableId) {
		this.cycleTableId = cycleTableId;
	}
	public Integer getFinalCheckStatus() {
		return finalCheckStatus;
	}
	public void setFinalCheckStatus(Integer finalCheckStatus) {
		this.finalCheckStatus = finalCheckStatus;
	}
	public Date getEnterDate() {
		return enterDate;
	}
	public void setEnterDate(Date enterDate) {
		this.enterDate = enterDate;
	}
	public Date getLeaveDate() {
		return leaveDate;
	}
	public void setLeaveDate(Date leaveDate) {
		this.leaveDate = leaveDate;
	}
	public Date getFinalCheckDate() {
		return finalCheckDate;
	}
	public void setFinalCheckDate(Date finalCheckDate) {
		this.finalCheckDate = finalCheckDate;
	}
}
