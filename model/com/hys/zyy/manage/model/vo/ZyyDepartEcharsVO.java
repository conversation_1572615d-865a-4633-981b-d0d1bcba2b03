package com.hys.zyy.manage.model.vo;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Set;

import com.hys.zyy.manage.model.ZyyJoinDeptRecordSkill;

/**
 * 科室的首页数据
 * <AUTHOR>
 * @date 2018-8-17上午11:42:20
 */
public class ZyyDepartEcharsVO implements Serializable {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	//主键
	private Long id;
	//入科
	//姓名
	private String realName;
	//年度
	private String year;
	//人员类型
	private Integer residencySource;
	//开始时间
	private String startDate;
	private Date startTime;
	//专业名称
	private String aliasName;
	//学员状态：1，已经入科。0，未入科。
	private Integer status;
	// 审核状态
	private Integer auditState;
	//带教名称
	private String teacherName;
	//带教名称的集合
	private Set<String> teacherNameSet;
     
    //过程
	//已经参加的活动数量
    private Integer activityAttendCount;
    //总的活动数量
    private Integer  activityCount;
    //请假天数
    private Integer  leaveCount;
    //实际出勤天数 = 应该出勤天数 - 请假天数
    private Integer workingday;
    //总的轮转天数
    private Integer cycleTotal;
    //手册审核状态
    private Integer checkStatus;
    
    //出科
  	//结束时间
  	private String endDate;
  	private Date endTime;
  	//理论成绩
  	private Float theoryGrade;
  	//出科审核的评论  || 不合格原因 lzq
  	private	String auditReamrk;
  	//出入科的id
    private Long jdrId;
    //出科成绩：心肺复苏：70；大病历：70； 子项
    private List<ZyyJoinDeptRecordSkill> recordSkill;
    //评价表的id
    private Long userEvaluateId;
    
    //zyy_dept_std的id
    private Long dstdId;
    private Long deptId;//科室ID
    private Long residencyId;//人员ID
    private Integer enterTeachStatus;//是否参加入科教育  0 未参加   1 已参加
    
    private Long ctId;
    
	public Long getCtId() {
		return ctId;
	}
	public void setCtId(Long ctId) {
		this.ctId = ctId;
	}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Set<String> getTeacherNameSet() {
		return teacherNameSet;
	}
	public void setTeacherNameSet(Set<String> teacherNameSet) {
		this.teacherNameSet = teacherNameSet;
	}
	public String getRealName() {
		return realName;
	}
	public void setRealName(String realName) {
		this.realName = realName;
	}
	public String getYear() {
		return year;
	}
	public void setYear(String year) {
		this.year = year;
	}
	public Integer getResidencySource() {
		return residencySource;
	}
	public void setResidencySource(Integer residencySource) {
		this.residencySource = residencySource;
	}
	public String getStartDate() {
		return startDate;
	}
	public void setStartDate(String startDate) {
		this.startDate = startDate;
	}
	public Date getStartTime() {
		return startTime;
	}
	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}
	public String getAliasName() {
		return aliasName;
	}
	public void setAliasName(String aliasName) {
		this.aliasName = aliasName;
	}
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	public Integer getAuditState() {
		return auditState;
	}
	public void setAuditState(Integer auditState) {
		this.auditState = auditState;
	}
	public String getTeacherName() {
		return teacherName;
	}
	public void setTeacherName(String teacherName) {
		this.teacherName = teacherName;
	}
	
	public String getEndDate() {
		return endDate;
	}
	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}
	public Date getEndTime() {
		return endTime;
	}
	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}
	public Float getTheoryGrade() {
		return theoryGrade;
	}
	public void setTheoryGrade(Float theoryGrade) {
		this.theoryGrade = theoryGrade;
	}
	public String getAuditReamrk() {
		return auditReamrk;
	}
	public void setAuditReamrk(String auditReamrk) {
		this.auditReamrk = auditReamrk;
	}
	public Long getJdrId() {
		return jdrId;
	}
	public void setJdrId(Long jdrId) {
		this.jdrId = jdrId;
	}
	public List<ZyyJoinDeptRecordSkill> getRecordSkill() {
		return recordSkill;
	}
	public void setRecordSkill(List<ZyyJoinDeptRecordSkill> recordSkill) {
		this.recordSkill = recordSkill;
	}
	public Integer getActivityAttendCount() {
		return activityAttendCount;
	}
	public void setActivityAttendCount(Integer activityAttendCount) {
		this.activityAttendCount = activityAttendCount;
	}
	public Integer getActivityCount() {
		return activityCount;
	}
	public void setActivityCount(Integer activityCount) {
		this.activityCount = activityCount;
	}
	public Integer getLeaveCount() {
		return leaveCount;
	}
	public void setLeaveCount(Integer leaveCount) {
		this.leaveCount = leaveCount;
	}
	public Integer getWorkingday() {
		return workingday;
	}
	public void setWorkingday(Integer workingday) {
		this.workingday = workingday;
	}
	public Integer getCycleTotal() {
		return cycleTotal;
	}
	public void setCycleTotal(Integer cycleTotal) {
		this.cycleTotal = cycleTotal;
	}
	public Integer getCheckStatus() {
		return checkStatus;
	}
	public void setCheckStatus(Integer checkStatus) {
		this.checkStatus = checkStatus;
	}
	public Long getUserEvaluateId() {
		return userEvaluateId;
	}
	public void setUserEvaluateId(Long userEvaluateId) {
		this.userEvaluateId = userEvaluateId;
	}
	public Long getDstdId() {
		return dstdId;
	}
	public void setDstdId(Long dstdId) {
		this.dstdId = dstdId;
	}
	public Long getDeptId() {
		return deptId;
	}
	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}
	public Long getResidencyId() {
		return residencyId;
	}
	public void setResidencyId(Long residencyId) {
		this.residencyId = residencyId;
	}
	public Integer getEnterTeachStatus() {
		return enterTeachStatus;
	}
	public void setEnterTeachStatus(Integer enterTeachStatus) {
		this.enterTeachStatus = enterTeachStatus;
	}
}
