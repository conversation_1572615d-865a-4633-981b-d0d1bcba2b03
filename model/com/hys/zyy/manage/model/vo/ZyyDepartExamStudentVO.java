package com.hys.zyy.manage.model.vo;

import java.util.Date;
import java.util.List;

import com.hys.zyy.manage.model.ZyyBaseObject;
/**
 * 出科考试 学生 封装类
 * <AUTHOR>
 *
 */
public class ZyyDepartExamStudentVO extends ZyyBaseObject {

	/**
	 * 
	 */
	private static final long serialVersionUID = 2342407638303695554L;
	private Long jdrId;
	//用户id
	private Long userId;
	//用户名称 
	private String realName;
	
	//性别
	private Integer sex ;
	//工号 JOB_NUMBER
	private String jobNumber;
	/**
	 * 第一学位 
	 */
	private Integer firstDegree;
	/**
	 * 最高学位
	 */
	private Integer highestDegree;
	/**
	 * 学制(住院医师需要轮转时间) 1.一年制 2.二年制 3.三年制
	 */
	private Integer schoolSystem ;
	
	//证件号码
	private String certificateNo;
	//培训年级
	private String year;
	//专业基地
	private String baseName;
	//轮转科室
	private String deptName;
	//轮转开始时间
	private Date enterDate;
	//轮转结束时间
	private Date leaveDate;
	//轮转时间
	private String cycleTime;
	//轮转状态
	private Integer cycleStatus;
	//出科状态
	private Integer leaveDeptStatus;
	
	//查询条件
	//本月出科学员
	private Integer thisMonth;
	//上月出科学员
	private Integer lastMonth;
	//轮转结束学员
	private Integer cycleEnd;
	//正在轮转学员
	private Integer cycleIn;
	//培训年级
	private Long yearId;
	//专业基地
	private Long baseId;
	//科室
	private Long deptId;
	//出科考试的id  用户查询已经选择的学生   不能用这个字段查询学生，应该用考试科目id查询
	private Long departExamId;
	//考试科目id  用户查询已经选择的学生
	private Long zyyExamCourseId;
	//该学生已经选过  1： 选择过 
	private Integer studentChooseStatus;
	
	//是否为技能考核选择 1=是
	private Long byExamStudentSel;
	
	//是否查看已选择 1=是 0\null=不限制
	private Long bySelUserId;
	
	//带教用户ID
	private Long byTeacherUserId;
	
	//选择的学员
	private List<Long> selUserIds;
	
	public Long getByExamStudentSel() {
		return byExamStudentSel;
	}
	public void setByExamStudentSel(Long byExamStudentSel) {
		this.byExamStudentSel = byExamStudentSel;
	}
	public Long getByTeacherUserId() {
		return byTeacherUserId;
	}
	public void setByTeacherUserId(Long byTeacherUserId) {
		this.byTeacherUserId = byTeacherUserId;
	}
	public Integer getSchoolSystem() {
		return schoolSystem;
	}
	public void setSchoolSystem(Integer schoolSystem) {
		this.schoolSystem = schoolSystem;
	}
	public Integer getSex() {
		return sex;
	}
	public void setSex(Integer sex) {
		this.sex = sex;
	}
	public String getJobNumber() {
		return jobNumber;
	}
	public void setJobNumber(String jobNumber) {
		this.jobNumber = jobNumber;
	}
	public Integer getFirstDegree() {
		return firstDegree;
	}
	public void setFirstDegree(Integer firstDegree) {
		this.firstDegree = firstDegree;
	}
	public Integer getHighestDegree() {
		return highestDegree;
	}
	public void setHighestDegree(Integer highestDegree) {
		this.highestDegree = highestDegree;
	}
	public Long getBySelUserId() {
		return bySelUserId;
	}
	public void setBySelUserId(Long bySelUserId) {
		this.bySelUserId = bySelUserId;
	}
	public List<Long> getSelUserIds() {
		return selUserIds;
	}
	public void setSelUserIds(List<Long> selUserIds) {
		this.selUserIds = selUserIds;
	}
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public Long getJdrId() {
		return jdrId;
	}

	public void setJdrId(Long jdrId) {
		this.jdrId = jdrId;
	}

	public String getRealName() {
		return realName;
	}
	public void setRealName(String realName) {
		this.realName = realName;
	}
	public String getCertificateNo() {
		return certificateNo;
	}
	public void setCertificateNo(String certificateNo) {
		this.certificateNo = certificateNo;
	}
	public String getYear() {
		return year;
	}
	public void setYear(String year) {
		this.year = year;
	}
	public String getBaseName() {
		return baseName;
	}
	public void setBaseName(String baseName) {
		this.baseName = baseName;
	}
	public String getDeptName() {
		return deptName;
	}
	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}
	public Date getEnterDate() {
		return enterDate;
	}
	public void setEnterDate(Date enterDate) {
		this.enterDate = enterDate;
	}
	public Date getLeaveDate() {
		return leaveDate;
	}
	public void setLeaveDate(Date leaveDate) {
		this.leaveDate = leaveDate;
	}
	public String getCycleTime() {
		return cycleTime;
	}
	public void setCycleTime(String cycleTime) {
		this.cycleTime = cycleTime;
	}
	public Integer getCycleStatus() {
		return cycleStatus;
	}
	public void setCycleStatus(Integer cycleStatus) {
		this.cycleStatus = cycleStatus;
	}
	public Integer getLeaveDeptStatus() {
		return leaveDeptStatus;
	}
	public void setLeaveDeptStatus(Integer leaveDeptStatus) {
		this.leaveDeptStatus = leaveDeptStatus;
	}
	public Integer getThisMonth() {
		return thisMonth;
	}
	public void setThisMonth(Integer thisMonth) {
		this.thisMonth = thisMonth;
	}
	public Integer getLastMonth() {
		return lastMonth;
	}
	public void setLastMonth(Integer lastMonth) {
		this.lastMonth = lastMonth;
	}
	public Integer getCycleEnd() {
		return cycleEnd;
	}
	public void setCycleEnd(Integer cycleEnd) {
		this.cycleEnd = cycleEnd;
	}
	public Integer getCycleIn() {
		return cycleIn;
	}
	public void setCycleIn(Integer cycleIn) {
		this.cycleIn = cycleIn;
	}
	public Long getYearId() {
		return yearId;
	}
	public void setYearId(Long yearId) {
		this.yearId = yearId;
	}
	public Long getBaseId() {
		return baseId;
	}
	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}
	public Long getDeptId() {
		return deptId;
	}
	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}
	public Long getDepartExamId() {
		return departExamId;
	}
	public void setDepartExamId(Long departExamId) {
		this.departExamId = departExamId;
	}
	public Long getZyyExamCourseId() {
		return zyyExamCourseId;
	}
	public void setZyyExamCourseId(Long zyyExamCourseId) {
		this.zyyExamCourseId = zyyExamCourseId;
	}
	public Integer getStudentChooseStatus() {
		return studentChooseStatus;
	}
	public void setStudentChooseStatus(Integer studentChooseStatus) {
		this.studentChooseStatus = studentChooseStatus;
	}
}
