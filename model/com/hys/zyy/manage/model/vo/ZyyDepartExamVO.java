package com.hys.zyy.manage.model.vo;

import java.util.Date;

import com.hys.zyy.manage.model.ZyyBaseObject;

/**
 * 出科考试
 * <AUTHOR>
 *
 */
public class ZyyDepartExamVO extends ZyyBaseObject {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1360442953405817097L;
	
//	ID	zyy_exam的id
	private Long id;	
//	机构ID	
	private Long zyyUserOrgId;	
//	考试ID	考试系统的考试ID
	private Long examId;	
//	考试科目ID		考试系统的考试科目ID
	private Long examCourseId;
//	试卷ID		考试系统的试卷ID
	private Long paperId;
//	考试名称	
	private String name; 
//	考试类型		
	private String typeCode;
//	考试开始日期		 
	private String startDate;
//	考试结束日期	
	private String endDate;
//	科目名称
	private String courseName;
//	考试科目开始时间	
	private String courseStartTime;
//	考试科目结束时间	
	private String courseEndTime;
//	考试时长	
	private Integer examDuration;
	//组卷方式    1  自动组卷   2 选择试卷
	private Integer paperType;
//	提交状态		0 未提交   1 已提交
	private Integer submitStatus;
//	审核状态		0 未审核   1 审核通过  2 审核不通过
	private Integer checkStatus;
//	更新时间	
	private Date updateDate;
//	创建时间	
	private Date createDate;	
//	创建人ID	
	private Long createUserId;
	//科室id
	private Long deptId;
	//提交日期
	private Date submitDate;
	private Long jdrId;
	
	//不存库字段
	//学生人数
	private Integer studentTotal;
	//增加的学员的id
	private String addUserIdStrs;
	private String userJdrIdStr;
	//删除的学员的id
	private String delUserIdStrs;
	//排序规则  1  创建出科考试    2  审核出科考试
	private Integer sortFlag;
	//科室名称
	private String deptName;
	//出科考试科目ID  ZYY_EXAM_COURSE 的id
	private Long zyyExamCourseId;
	
	//考试系统的机构id
	private String emSysOrgId;
	//试卷名称
	private String paperName;
	//考试科目成绩发布状态
	private Integer scorePublishStatus;
	//题库属性ids
	private String qAttrIds;
	//提交试卷后是否可以重复考试
	private Integer repeatSubmit;
	//提交试卷后是否可以重复考试 数量
	private Integer repeatSubmitNum;
	//是否限制答题过程中重复进入考试（离开考试页面）
	private Integer repeatExam;
	//是否限制答题过程中重复进入考试（离开考试页面） 数量
	private Integer examIn;
	// 出科考试是否需要审核（0=否；1=是）
	private Integer requiredAudit;
	// 出科考试审核用户类型
	private String auditUserTypeStr;
	private Date startTime;
	private boolean nowBeforeStartTime;

	public ZyyDepartExamVO() {
		super();
	}

	public ZyyDepartExamVO(Long id, Long zyyExamCourseId, Integer checkStatus) {
		super();
		this.id = id;
		this.checkStatus = checkStatus;
		this.zyyExamCourseId = zyyExamCourseId;
	}

	public Integer getRepeatExam() {
		return repeatExam;
	}

	public void setRepeatExam(Integer repeatExam) {
		this.repeatExam = repeatExam;
	}

	public Integer getExamIn() {
		return examIn;
	}

	public void setExamIn(Integer examIn) {
		this.examIn = examIn;
	}

	public Integer getRepeatSubmit() {
		return repeatSubmit;
	}

	public void setRepeatSubmit(Integer repeatSubmit) {
		this.repeatSubmit = repeatSubmit;
	}

	public Integer getRepeatSubmitNum() {
		return repeatSubmitNum;
	}

	public void setRepeatSubmitNum(Integer repeatSubmitNum) {
		this.repeatSubmitNum = repeatSubmitNum;
	}

	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getZyyUserOrgId() {
		return zyyUserOrgId;
	}
	public void setZyyUserOrgId(Long zyyUserOrgId) {
		this.zyyUserOrgId = zyyUserOrgId;
	}
	public Long getExamId() {
		return examId;
	}
	public void setExamId(Long examId) {
		this.examId = examId;
	}
	public Long getExamCourseId() {
		return examCourseId;
	}
	public void setExamCourseId(Long examCourseId) {
		this.examCourseId = examCourseId;
	}
	public Long getPaperId() {
		return paperId;
	}
	public void setPaperId(Long paperId) {
		this.paperId = paperId;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getTypeCode() {
		return typeCode;
	}
	public void setTypeCode(String typeCode) {
		this.typeCode = typeCode;
	}
	public String getStartDate() {
		return startDate;
	}
	public void setStartDate(String startDate) {
		this.startDate = startDate;
	}
	public String getEndDate() {
		return endDate;
	}
	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}
	public String getCourseName() {
		return courseName;
	}
	public void setCourseName(String courseName) {
		this.courseName = courseName;
	}
	public String getCourseStartTime() {
		return courseStartTime;
	}
	public void setCourseStartTime(String courseStartTime) {
		this.courseStartTime = courseStartTime;
	}
	public String getCourseEndTime() {
		return courseEndTime;
	}
	public void setCourseEndTime(String courseEndTime) {
		this.courseEndTime = courseEndTime;
	}
	public Integer getExamDuration() {
		return examDuration;
	}
	public void setExamDuration(Integer examDuration) {
		this.examDuration = examDuration;
	}
	public Integer getPaperType() {
		return paperType;
	}
	public void setPaperType(Integer paperType) {
		this.paperType = paperType;
	}
	public Integer getSubmitStatus() {
		return submitStatus;
	}
	public void setSubmitStatus(Integer submitStatus) {
		this.submitStatus = submitStatus;
	}
	public Integer getCheckStatus() {
		return checkStatus;
	}
	public void setCheckStatus(Integer checkStatus) {
		this.checkStatus = checkStatus;
	}
	public Date getUpdateDate() {
		return updateDate;
	}
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}
	public Date getCreateDate() {
		return createDate;
	}
	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}
	public Long getCreateUserId() {
		return createUserId;
	}
	public void setCreateUserId(Long createUserId) {
		this.createUserId = createUserId;
	}
	public Long getDeptId() {
		return deptId;
	}
	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}
	public Integer getStudentTotal() {
		return studentTotal;
	}
	public void setStudentTotal(Integer studentTotal) {
		this.studentTotal = studentTotal;
	}
	public String getAddUserIdStrs() {
		return addUserIdStrs;
	}
	public void setAddUserIdStrs(String addUserIdStrs) {
		this.addUserIdStrs = addUserIdStrs;
	}

	public String getUserJdrIdStr() {
		return userJdrIdStr;
	}

	public void setUserJdrIdStr(String userJdrIdStr) {
		this.userJdrIdStr = userJdrIdStr == null ? null : userJdrIdStr.trim();
	}

	public String getDelUserIdStrs() {
		return delUserIdStrs;
	}
	public void setDelUserIdStrs(String delUserIdStrs) {
		this.delUserIdStrs = delUserIdStrs;
	}
	public Integer getSortFlag() {
		return sortFlag;
	}
	public void setSortFlag(Integer sortFlag) {
		this.sortFlag = sortFlag;
	}
	public Date getSubmitDate() {
		return submitDate;
	}
	public void setSubmitDate(Date submitDate) {
		this.submitDate = submitDate;
	}
	public String getDeptName() {
		return deptName;
	}
	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}
	public Long getZyyExamCourseId() {
		return zyyExamCourseId;
	}
	public void setZyyExamCourseId(Long zyyExamCourseId) {
		this.zyyExamCourseId = zyyExamCourseId;
	}
	public String getEmSysOrgId() {
		return emSysOrgId;
	}
	public void setEmSysOrgId(String emSysOrgId) {
		this.emSysOrgId = emSysOrgId;
	}
	public String getPaperName() {
		return paperName;
	}
	public void setPaperName(String paperName) {
		this.paperName = paperName;
	}
	public Integer getScorePublishStatus() {
		return scorePublishStatus;
	}
	public void setScorePublishStatus(Integer scorePublishStatus) {
		this.scorePublishStatus = scorePublishStatus;
	}
	public String getqAttrIds() {
		return qAttrIds;
	}
	public void setqAttrIds(String qAttrIds) {
		this.qAttrIds = qAttrIds;
	}

	public Integer getRequiredAudit() {
		return requiredAudit;
	}

	public void setRequiredAudit(Integer requiredAudit) {
		this.requiredAudit = requiredAudit;
	}

	public String getAuditUserTypeStr() {
		return auditUserTypeStr;
	}

	public void setAuditUserTypeStr(String auditUserTypeStr) {
		this.auditUserTypeStr = auditUserTypeStr == null ? null : auditUserTypeStr.trim();
	}

	public Date getStartTime() {
		return startTime;
	}

	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}

	public boolean isNowBeforeStartTime() {
		return nowBeforeStartTime;
	}

	public void setNowBeforeStartTime(boolean nowBeforeStartTime) {
		this.nowBeforeStartTime = nowBeforeStartTime;
	}

	public Long getJdrId() {
		return jdrId;
	}

	public void setJdrId(Long jdrId) {
		this.jdrId = jdrId;
	}
}
