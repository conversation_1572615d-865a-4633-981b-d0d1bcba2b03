package com.hys.zyy.manage.model.vo;

import java.util.List;

import com.hys.zyy.manage.model.ZyyBaseObject;
import com.hys.zyy.manage.model.ZyyDeptQuestionAttr;

public class ZyyDeptQuestionAttrVO extends ZyyBaseObject {

	/**
	 * 
	 */
	private static final long serialVersionUID = -5771963575355777340L;
	
	//科室id
	private Long deptId;
	//科室名称
	private String deptName;
	//试题数量
	private Integer questionTotal;
	//题库属性
	private List<ZyyDeptQuestionAttr> questAttrList;
	//子集
	private List<ZyyDeptQuestionAttrVO> childList;
	
	public Long getDeptId() {
		return deptId;
	}
	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}
	public String getDeptName() {
		return deptName;
	}
	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}
	public Integer getQuestionTotal() {
		return questionTotal;
	}
	public void setQuestionTotal(Integer questionTotal) {
		this.questionTotal = questionTotal;
	}
	public List<ZyyDeptQuestionAttr> getQuestAttrList() {
		return questAttrList;
	}
	public void setQuestAttrList(List<ZyyDeptQuestionAttr> questAttrList) {
		this.questAttrList = questAttrList;
	}
	public List<ZyyDeptQuestionAttrVO> getChildList() {
		return childList;
	}
	public void setChildList(List<ZyyDeptQuestionAttrVO> childList) {
		this.childList = childList;
	}
}
