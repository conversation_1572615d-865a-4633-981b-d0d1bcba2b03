package com.hys.zyy.manage.model.vo;

import com.hys.zyy.manage.model.ZyyEnterTeachUser;

public class ZyyEnterTeachUserVO extends ZyyEnterTeachUser {

	/**
	 * 
	 */
	private static final long serialVersionUID = -6302041377742887138L;
	
	private String realName;//姓名
	private Long teachUserId;//入科教育学员ID
	private Long deptId;//入科教育的科室ID
	private String certificateNo;//身份证号
	private Integer sex;//性别
	private String baseAliasName;//学科名称
	private String year;//年级
	private String enterDateStr;//入科时间
	private String cycleTimes;//轮转次数
	private Integer attendFlag = 0; //是否出勤该科室入科教育  0 未参加 1 已参加   （3时，非本次出勤记录中标识已经出勤过）
	private String userIds ;
	private Integer existFlag;//0.不存在 1.存在
	
	public String getRealName() {
		return realName;
	}
	public void setRealName(String realName) {
		this.realName = realName;
	}
	public String getCertificateNo() {
		return certificateNo;
	}
	public void setCertificateNo(String certificateNo) {
		this.certificateNo = certificateNo;
	}
	public Integer getSex() {
		return sex;
	}
	public void setSex(Integer sex) {
		this.sex = sex;
	}
	public String getYear() {
		return year;
	}
	public void setYear(String year) {
		this.year = year;
	}
	public String getCycleTimes() {
		return cycleTimes;
	}
	public void setCycleTimes(String cycleTimes) {
		this.cycleTimes = cycleTimes;
	}
	public String getBaseAliasName() {
		return baseAliasName;
	}
	public void setBaseAliasName(String baseAliasName) {
		this.baseAliasName = baseAliasName;
	}
	public Long getTeachUserId() {
		return teachUserId;
	}
	public void setTeachUserId(Long teachUserId) {
		this.teachUserId = teachUserId;
	}
	public Integer getAttendFlag() {
		return attendFlag;
	}
	public void setAttendFlag(Integer attendFlag) {
		this.attendFlag = attendFlag;
	}
	public Long getDeptId() {
		return deptId;
	}
	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}
	public String getEnterDateStr() {
		return enterDateStr;
	}
	public void setEnterDateStr(String enterDateStr) {
		this.enterDateStr = enterDateStr;
	}
	public String getUserIds() {
		return userIds;
	}
	public void setUserIds(String userIds) {
		this.userIds = userIds;
	}
	public Integer getExistFlag() {
		return existFlag;
	}
	public void setExistFlag(Integer existFlag) {
		this.existFlag = existFlag;
	}
}
