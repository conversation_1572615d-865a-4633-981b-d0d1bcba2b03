package com.hys.zyy.manage.model.vo;

import com.hys.zyy.manage.model.ZyyEnterTeach;
import com.hys.zyy.manage.model.ZyyUserExtendVO;

public class ZyyEnterTeachVO extends ZyyEnterTeach {

	/**
	 *
	 */
	private static final long serialVersionUID = 5500778973899272741L;

	/**
	 * @desc 登录人ID
	 */
	private ZyyUserExtendVO zyyUser;

	/**
	 * 查询的时间 具体到某年某月
	 * @return
	 */
	private String yearAndMonth;
	private String residencyIds;//参与人ID
	private String qDeptIds;
	private Integer resiCount;//参与人员数量
	private String attendRate;//出勤率
	private String deptName;//科室名称
	private String enterDateStr;//入科时间
	private String startDateStr;//教育活动开始
	private String endDateStr;//教育活动截至
	private String speakerName;//演讲人ID
	private String realName;//学员真实姓名
	private Integer teachStatus;//入科教育活动状态 0-未创建  1 未开始 2已开始 3已结束
	private Integer attaCount;//附件数量
	private Long baseId; // 专业ID
	private Integer attendStatus;// 是否出勤 0 未上报 1 出勤 2未出勤
	private Integer activitySignTwo;// 是否活动中签到 0 未上报 1 出勤 2未出勤
	private boolean pagination = true;
	private String createTimeStr;

	private Integer viewFlag; //1查看  2 出勤登记

	public String getCreateTimeStr() {
		return createTimeStr;
	}

	public void setCreateTimeStr(String createTimeStr) {
		this.createTimeStr = createTimeStr == null ? null : createTimeStr.trim();
	}

	public String getYearAndMonth() {
		return yearAndMonth;
	}
	public void setYearAndMonth(String yearAndMonth) {
		this.yearAndMonth = yearAndMonth;
	}
	public ZyyUserExtendVO getZyyUser() {
		return zyyUser;
	}
	public void setZyyUser(ZyyUserExtendVO zyyUser) {
		this.zyyUser = zyyUser;
	}
	public String getEnterDateStr() {
		return enterDateStr;
	}
	public void setEnterDateStr(String enterDateStr) {
		this.enterDateStr = enterDateStr;
	}
	public String getStartDateStr() {
		return startDateStr;
	}
	public void setStartDateStr(String startDateStr) {
		this.startDateStr = startDateStr;
	}
	public String getEndDateStr() {
		return endDateStr;
	}
	public void setEndDateStr(String endDateStr) {
		this.endDateStr = endDateStr;
	}
	public String getDeptName() {
		return deptName;
	}
	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}
	public String getSpeakerName() {
		return speakerName;
	}
	public void setSpeakerName(String speakerName) {
		this.speakerName = speakerName;
	}
	public Integer getTeachStatus() {
		return teachStatus;
	}
	public void setTeachStatus(Integer teachStatus) {
		this.teachStatus = teachStatus;
	}
	public String getResidencyIds() {
		return residencyIds;
	}
	public void setResidencyIds(String residencyIds) {
		this.residencyIds = residencyIds;
	}
	public Integer getResiCount() {
		return resiCount;
	}
	public void setResiCount(Integer resiCount) {
		this.resiCount = resiCount;
	}
	public String getRealName() {
		return realName;
	}
	public void setRealName(String realName) {
		this.realName = realName;
	}
	public Integer getViewFlag() {
		return viewFlag;
	}
	public void setViewFlag(Integer viewFlag) {
		this.viewFlag = viewFlag;
	}
	public String getAttendRate() {
		return attendRate;
	}
	public void setAttendRate(String attendRate) {
		this.attendRate = attendRate;
	}
	public Integer getAttaCount() {
		return attaCount;
	}
	public void setAttaCount(Integer attaCount) {
		this.attaCount = attaCount;
	}
	public String getqDeptIds() {
		return qDeptIds;
	}
	public void setqDeptIds(String qDeptIds) {
		this.qDeptIds = qDeptIds;
	}
	public Integer getAttendStatus() {
		return attendStatus;
	}
	public void setAttendStatus(Integer attendStatus) {
		this.attendStatus = attendStatus;
	}
	public Long getBaseId() {
		return baseId;
	}
	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}

	public Integer getActivitySignTwo() {
		return activitySignTwo;
	}

	public void setActivitySignTwo(Integer activitySignTwo) {
		this.activitySignTwo = activitySignTwo;
	}
	
	public boolean isPagination() {
		return pagination;
	}

	public void setPagination(boolean pagination) {
		this.pagination = pagination;
	}
}
