package com.hys.zyy.manage.model.vo;

import java.io.Serializable;
import java.util.Date;

import com.hys.zyy.manage.model.ZyyEvaluateTableConfig;
import com.hys.zyy.manage.model.ZyyUserExtendVO;

/**
 * 评价学员封装类
 * <AUTHOR>
 * @date 2018-8-22下午3:55:21
 */
public class ZyyEvaluateUserVO implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private Long id;
	
	//用户id
	private Long userId;
	//	年级
	private Long yearId;
	private String year;
	//	专业基地
	private Long baseId;
	private String aliasName;
	//	被评价者
	private String realName;
	//	证件号码
	private String certificateNo;
	//	当前轮转科室
	private String departName;
	//	状态
	private Integer status;
	//	评价结果
	private String evaluateResult;
	
	//查询条件
	// 开始时间
	private String startDate;
	// 结束时间
	private String endDate;
	//	评价表类型	 1 对评价学员 2 对带教评价 3 对责任导师评价 4 对科室评价 5 对专业基地评价
	private Integer tableType ;	
	//	评价者类型	1带教老师 2 科室 3责任导师 4专业基地 5培训基地 6患者 7护士 8 住院医师
	private Integer evaluateType ;
	
	//填写评论表 需要的字段
	//评论表配置的id
	private Long tableConfigId;
	//评论开始时间
	private String evaluateStartTime;
	//评论结束时间
	private String evaluateEndTime;
	
	//用户
	private ZyyUserExtendVO user;
	//评论配置表
	private ZyyEvaluateTableConfig config;
	
	//评价表的id
	private Long userEvaluateNewId;
	
	//定时任务需要的字段
	private Long teacherId;
	private Long deptIdId;
	private Long hospitalId;
	
	//现在的时间
	private Date nowTime;
	//yyyy-MM-dd
	private String nowTimeStr;
	//时间段
	private String timeSection;
	
	//是否仅查询总数 1 - 是 ， 空 - 否  app需要优化速度
	private Integer onlyTotalCount;
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	public Long getYearId() {
		return yearId;
	}
	public void setYearId(Long yearId) {
		this.yearId = yearId;
	}
	public String getYear() {
		return year;
	}
	public void setYear(String year) {
		this.year = year;
	}
	public Long getBaseId() {
		return baseId;
	}
	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}
	public String getAliasName() {
		return aliasName;
	}
	public void setAliasName(String aliasName) {
		this.aliasName = aliasName;
	}
	public String getRealName() {
		return realName;
	}
	public void setRealName(String realName) {
		this.realName = realName;
	}
	public String getCertificateNo() {
		return certificateNo;
	}
	public void setCertificateNo(String certificateNo) {
		this.certificateNo = certificateNo;
	}
	public String getDepartName() {
		return departName;
	}
	public void setDepartName(String departName) {
		this.departName = departName;
	}
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	public String getEvaluateResult() {
		return evaluateResult;
	}
	public void setEvaluateResult(String evaluateResult) {
		this.evaluateResult = evaluateResult;
	}
	public String getStartDate() {
		return startDate;
	}
	public void setStartDate(String startDate) {
		this.startDate = startDate;
	}
	public String getEndDate() {
		return endDate;
	}
	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}
	public Integer getTableType() {
		return tableType;
	}
	public void setTableType(Integer tableType) {
		this.tableType = tableType;
	}
	public Integer getEvaluateType() {
		return evaluateType;
	}
	public void setEvaluateType(Integer evaluateType) {
		this.evaluateType = evaluateType;
	}
	public Long getTableConfigId() {
		return tableConfigId;
	}
	public void setTableConfigId(Long tableConfigId) {
		this.tableConfigId = tableConfigId;
	}
	public String getEvaluateStartTime() {
		return evaluateStartTime;
	}
	public void setEvaluateStartTime(String evaluateStartTime) {
		this.evaluateStartTime = evaluateStartTime;
	}
	public String getEvaluateEndTime() {
		return evaluateEndTime;
	}
	public void setEvaluateEndTime(String evaluateEndTime) {
		this.evaluateEndTime = evaluateEndTime;
	}
	public ZyyUserExtendVO getUser() {
		return user;
	}
	public void setUser(ZyyUserExtendVO user) {
		this.user = user;
	}
	public ZyyEvaluateTableConfig getConfig() {
		return config;
	}
	public void setConfig(ZyyEvaluateTableConfig config) {
		this.config = config;
	}
	public Long getUserEvaluateNewId() {
		return userEvaluateNewId;
	}
	public void setUserEvaluateNewId(Long userEvaluateNewId) {
		this.userEvaluateNewId = userEvaluateNewId;
	}
	public Long getTeacherId() {
		return teacherId;
	}
	public void setTeacherId(Long teacherId) {
		this.teacherId = teacherId;
	}
	public Long getDeptIdId() {
		return deptIdId;
	}
	public void setDeptIdId(Long deptIdId) {
		this.deptIdId = deptIdId;
	}
	public Long getHospitalId() {
		return hospitalId;
	}
	public void setHospitalId(Long hospitalId) {
		this.hospitalId = hospitalId;
	}
	public Date getNowTime() {
		return nowTime;
	}
	public void setNowTime(Date nowTime) {
		this.nowTime = nowTime;
	}
	public String getNowTimeStr() {
		return nowTimeStr;
	}
	public void setNowTimeStr(String nowTimeStr) {
		this.nowTimeStr = nowTimeStr;
	}
	public String getTimeSection() {
		return timeSection;
	}
	public void setTimeSection(String timeSection) {
		this.timeSection = timeSection;
	}
	public Integer getOnlyTotalCount() {
		return onlyTotalCount;
	}
	public void setOnlyTotalCount(Integer onlyTotalCount) {
		this.onlyTotalCount = onlyTotalCount;
	}
}
