package com.hys.zyy.manage.model.vo;

import java.util.Date;
import java.util.List;

import com.hys.zyy.manage.model.ZyyBaseObject;
import com.hys.zyy.manage.model.ZyyOrgRctVO;

/**
 * 准考证模板
 * <AUTHOR>
 * @date 2020-2-24上午11:48:16
 */
public class ZyyExamApplyFormVO extends ZyyBaseObject {
     /**
	 * 
	 */
	private static final long serialVersionUID = -4894258864085785978L;
	
	//	ID	ID	
	private Long id;
//	名称	
	private String name;	
//	路径	
	private String path;	
//	状态		0: 删除 1:正常
	private Integer status;
//	创建时间	
	private Date createDate;	
//	更新时间	
	private Date updateDate;
	//创建人
	private Long createUserId;
	
	//不存库
	//创建人名称
	private String realName;
	//开始时间
	private String startTime;
	//结束时间
	private String endTime;
	//选择的机构
	private List<ZyyOrgRctVO> orgList;
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getPath() {
		return path;
	}
	public void setPath(String path) {
		this.path = path;
	}
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	public Date getCreateDate() {
		return createDate;
	}
	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}
	public Date getUpdateDate() {
		return updateDate;
	}
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}
	public Long getCreateUserId() {
		return createUserId;
	}
	public void setCreateUserId(Long createUserId) {
		this.createUserId = createUserId;
	}
	public String getRealName() {
		return realName;
	}
	public void setRealName(String realName) {
		this.realName = realName;
	}
	public String getStartTime() {
		return startTime;
	}
	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}
	public String getEndTime() {
		return endTime;
	}
	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}
	public List<ZyyOrgRctVO> getOrgList() {
		return orgList;
	}
	public void setOrgList(List<ZyyOrgRctVO> orgList) {
		this.orgList = orgList;
	}
}
