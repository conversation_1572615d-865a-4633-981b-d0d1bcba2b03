package com.hys.zyy.manage.model.vo;

import java.util.List;

import com.hys.zyy.manage.model.ZyyBaseObject;
import com.hys.zyy.manage.model.ZyyStudentCheckRecord;
/**
 * 考试科目管理选择学生
 * <AUTHOR>
 *
 */
public class ZyyExamCourseStudentVO extends ZyyBaseObject {

	/**
	 * 
	 */
	private static final long serialVersionUID = 7272531867423940417L;
	//用户id
	private Long userId;
	//姓名
	private String realName;
	//性别
	private Integer sex;
	//手机号
	private String mobile;
	//身份证号
	private String certificateNo;
	//是否已经选择  1 : 已经选过了
	private Integer status;
	//培训年级
	private String year;
	//专业
	private String baseAliasName;
	//专业代码
	private String professionalSubjectId;
	//人员类型
	private Integer residencySource;
	//机构名称
	private String orgName;
	//培训年限
	private Integer schoolSystem;
	//科目名称
	private String courseName;
	//报名表id
	private Long studentExamApplyId;
	
	//查询条件
	//考试id
	private Long examId;
	//科目id
	private Long examCourseId;
	//学员
	private String queryCons;
	//培训年级
	private Long yearId;
	//专业
	private Long baseId;
	//住院医的考试科目id  或者 考试id
	private Long zyyExamCourseId;
	//数据类型   1  考试      2  考试科目
	private Integer type;
	//住院医的考试id
	private Long zyyExamId;
	//本级审核流程id
	private Long applyCheckFlowId;
	//上级审核流程id
	private Long beforeCheckFlowId;
	//审核状态   0 - 未审核   1 - 通过 2 - 未通过  3 - 退回
	private Integer checkStatus;
	//医院
	private Long orgId;
	//多个审核层级的ids
	private String flowIds;
	//多层级的状态
	private String flowCheckStatus;
	//报名状态 1 报名   2 未报名
	private Integer applyStatus;
	//审核记录
	private List<ZyyStudentCheckRecord> checkRecordList;
	//是否正式学员：    1 是   2 否
	private Integer studentType;
	
	//选择考生保存需要的字段
	//增加的学生
	private String addCourseUserIdStrs;
	//删除的学生
	private String delCourseUserIdStrs;
	//考试那边的机构id
	private String emSysOrgId;
	
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	public String getRealName() {
		return realName;
	}
	public void setRealName(String realName) {
		this.realName = realName;
	}
	public Integer getSex() {
		return sex;
	}
	public void setSex(Integer sex) {
		this.sex = sex;
	}
	public String getMobile() {
		return mobile;
	}
	public void setMobile(String mobile) {
		this.mobile = mobile;
	}
	public String getCertificateNo() {
		return certificateNo;
	}
	public void setCertificateNo(String certificateNo) {
		this.certificateNo = certificateNo;
	}
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	public String getYear() {
		return year;
	}
	public void setYear(String year) {
		this.year = year;
	}
	public String getBaseAliasName() {
		return baseAliasName;
	}
	public void setBaseAliasName(String baseAliasName) {
		this.baseAliasName = baseAliasName;
	}
	public String getProfessionalSubjectId() {
		return professionalSubjectId;
	}
	public void setProfessionalSubjectId(String professionalSubjectId) {
		this.professionalSubjectId = professionalSubjectId;
	}
	public Integer getResidencySource() {
		return residencySource;
	}
	public void setResidencySource(Integer residencySource) {
		this.residencySource = residencySource;
	}
	public String getOrgName() {
		return orgName;
	}
	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}
	public Integer getSchoolSystem() {
		return schoolSystem;
	}
	public void setSchoolSystem(Integer schoolSystem) {
		this.schoolSystem = schoolSystem;
	}
	public String getCourseName() {
		return courseName;
	}
	public void setCourseName(String courseName) {
		this.courseName = courseName;
	}
	public Long getStudentExamApplyId() {
		return studentExamApplyId;
	}
	public void setStudentExamApplyId(Long studentExamApplyId) {
		this.studentExamApplyId = studentExamApplyId;
	}
	public Long getExamId() {
		return examId;
	}
	public void setExamId(Long examId) {
		this.examId = examId;
	}
	public Long getExamCourseId() {
		return examCourseId;
	}
	public void setExamCourseId(Long examCourseId) {
		this.examCourseId = examCourseId;
	}
	public String getQueryCons() {
		return queryCons;
	}
	public void setQueryCons(String queryCons) {
		this.queryCons = queryCons;
	}
	public Long getYearId() {
		return yearId;
	}
	public void setYearId(Long yearId) {
		this.yearId = yearId;
	}
	public Long getBaseId() {
		return baseId;
	}
	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}
	public Long getZyyExamCourseId() {
		return zyyExamCourseId;
	}
	public void setZyyExamCourseId(Long zyyExamCourseId) {
		this.zyyExamCourseId = zyyExamCourseId;
	}
	public Integer getType() {
		return type;
	}
	public void setType(Integer type) {
		this.type = type;
	}
	public Long getZyyExamId() {
		return zyyExamId;
	}
	public void setZyyExamId(Long zyyExamId) {
		this.zyyExamId = zyyExamId;
	}
	public Long getApplyCheckFlowId() {
		return applyCheckFlowId;
	}
	public void setApplyCheckFlowId(Long applyCheckFlowId) {
		this.applyCheckFlowId = applyCheckFlowId;
	}
	public Long getBeforeCheckFlowId() {
		return beforeCheckFlowId;
	}
	public void setBeforeCheckFlowId(Long beforeCheckFlowId) {
		this.beforeCheckFlowId = beforeCheckFlowId;
	}
	public Integer getCheckStatus() {
		return checkStatus;
	}
	public void setCheckStatus(Integer checkStatus) {
		this.checkStatus = checkStatus;
	}
	public Long getOrgId() {
		return orgId;
	}
	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}
	public String getFlowIds() {
		return flowIds;
	}
	public void setFlowIds(String flowIds) {
		this.flowIds = flowIds;
	}
	public String getFlowCheckStatus() {
		return flowCheckStatus;
	}
	public void setFlowCheckStatus(String flowCheckStatus) {
		this.flowCheckStatus = flowCheckStatus;
	}
	public Integer getApplyStatus() {
		return applyStatus;
	}
	public void setApplyStatus(Integer applyStatus) {
		this.applyStatus = applyStatus;
	}
	public List<ZyyStudentCheckRecord> getCheckRecordList() {
		return checkRecordList;
	}
	public void setCheckRecordList(List<ZyyStudentCheckRecord> checkRecordList) {
		this.checkRecordList = checkRecordList;
	}
	public Integer getStudentType() {
		return studentType;
	}
	public void setStudentType(Integer studentType) {
		this.studentType = studentType;
	}
	public String getAddCourseUserIdStrs() {
		return addCourseUserIdStrs;
	}
	public void setAddCourseUserIdStrs(String addCourseUserIdStrs) {
		this.addCourseUserIdStrs = addCourseUserIdStrs;
	}
	public String getDelCourseUserIdStrs() {
		return delCourseUserIdStrs;
	}
	public void setDelCourseUserIdStrs(String delCourseUserIdStrs) {
		this.delCourseUserIdStrs = delCourseUserIdStrs;
	}
	public String getEmSysOrgId() {
		return emSysOrgId;
	}
	public void setEmSysOrgId(String emSysOrgId) {
		this.emSysOrgId = emSysOrgId;
	}
	
}
