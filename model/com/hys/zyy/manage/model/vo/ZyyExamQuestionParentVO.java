package com.hys.zyy.manage.model.vo;

import java.util.List;

import com.hys.zyy.manage.model.ZyyBaseObject;
/**
 * 试题主干的封装类
 * <AUTHOR>
 * @date 2020-3-10上午9:43:40
 */
public class ZyyExamQuestionParentVO extends ZyyBaseObject {

	/**
	 * 
	 */
	private static final long serialVersionUID = 7398720385516105221L;
	
	//题号,试题主干内容,分数
	private String numContentScore;
	//选项列表
	private List<ZyyExamQuestionChildVO> questionKeyList;
	//正确答案
	private String rightAnswer;
	//回答是否正确    1  正确   2  不正确
	private Integer answerCurrect;
	//回答记录
	private String answer;
	//解析内容
	private String analyse;
	//是否有子试题 1  有  2  没有
	private Integer hasChild;
	//子试题内容
	private List<ZyyExamQuestionParentVO> childQuestionList;
	//试题难度 1 简单  2  中等  3 较难
	private String qDifficultyGradeStr;
	
	public String getNumContentScore() {
		return numContentScore;
	}
	public void setNumContentScore(String numContentScore) {
		this.numContentScore = numContentScore;
	}
	public List<ZyyExamQuestionChildVO> getQuestionKeyList() {
		return questionKeyList;
	}
	public void setQuestionKeyList(List<ZyyExamQuestionChildVO> questionKeyList) {
		this.questionKeyList = questionKeyList;
	}
	public String getRightAnswer() {
		return rightAnswer;
	}
	public void setRightAnswer(String rightAnswer) {
		this.rightAnswer = rightAnswer;
	}
	public Integer getAnswerCurrect() {
		return answerCurrect;
	}
	
	public void setAnswerCurrect(Integer answerCurrect) {
		this.answerCurrect = answerCurrect;
	}
	public String getAnswer() {
		return answer;
	}
	public void setAnswer(String answer) {
		this.answer = answer;
	}
	public String getAnalyse() {
		return analyse;
	}
	public void setAnalyse(String analyse) {
		this.analyse = analyse;
	}
	public Integer getHasChild() {
		return hasChild;
	}
	public void setHasChild(Integer hasChild) {
		this.hasChild = hasChild;
	}
	public List<ZyyExamQuestionParentVO> getChildQuestionList() {
		return childQuestionList;
	}
	public void setChildQuestionList(List<ZyyExamQuestionParentVO> childQuestionList) {
		this.childQuestionList = childQuestionList;
	}
	public String getqDifficultyGradeStr() {
		return qDifficultyGradeStr;
	}
	public void setqDifficultyGradeStr(String qDifficultyGradeStr) {
		this.qDifficultyGradeStr = qDifficultyGradeStr;
	}
}
