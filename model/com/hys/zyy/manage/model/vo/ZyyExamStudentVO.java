package com.hys.zyy.manage.model.vo;

import java.util.List;

import com.hys.zyy.manage.model.ZyyBaseObject;
import com.hys.zyy.manage.model.ZyyStudentTicketInfo;

/**
 * 导入并维护准考证信息  封装用户信息
 * <AUTHOR>
 * @date 2020-4-8下午2:40:07
 */
public class ZyyExamStudentVO extends ZyyBaseObject {
	/**
	 * 
	 */
	private static final long serialVersionUID = 7993656406892097400L;
	
	//用户id
	private Long userId;
	//考试id
	private Long zyyExamId;
	//姓名
	private String realName;
	//性别
	private Integer sex;
	//所属机构
	private String orgName;
	//处室名称
	private String baseName;
	//准考证号码
	private String ticketNumber;
	//证件类型
	private Integer certType;
	//身份证号
	private String certificateNo;
	//多个科目的数据
	private List<ZyyStudentTicketInfo> childList;
	//身份号
	private List<ZyyExamStudentVO> studentList;
	
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	public Long getZyyExamId() {
		return zyyExamId;
	}
	public void setZyyExamId(Long zyyExamId) {
		this.zyyExamId = zyyExamId;
	}
	public String getRealName() {
		return realName;
	}
	public void setRealName(String realName) {
		this.realName = realName;
	}
	public Integer getSex() {
		return sex;
	}
	public void setSex(Integer sex) {
		this.sex = sex;
	}
	public String getOrgName() {
		return orgName;
	}
	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}
	 
	public String getBaseName() {
		return baseName;
	}
	public void setBaseName(String baseName) {
		this.baseName = baseName;
	}
	public String getTicketNumber() {
		return ticketNumber;
	}
	public void setTicketNumber(String ticketNumber) {
		this.ticketNumber = ticketNumber;
	}
	public Integer getCertType() {
		return certType;
	}
	public void setCertType(Integer certType) {
		this.certType = certType;
	}
	public String getCertificateNo() {
		return certificateNo;
	}
	public void setCertificateNo(String certificateNo) {
		this.certificateNo = certificateNo;
	}
	public List<ZyyStudentTicketInfo> getChildList() {
		return childList;
	}
	public void setChildList(List<ZyyStudentTicketInfo> childList) {
		this.childList = childList;
	}
	public List<ZyyExamStudentVO> getStudentList() {
		return studentList;
	}
	public void setStudentList(List<ZyyExamStudentVO> studentList) {
		this.studentList = studentList;
	}
}
