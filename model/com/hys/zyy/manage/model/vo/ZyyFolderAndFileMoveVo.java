package com.hys.zyy.manage.model.vo;

import java.util.List;

/**
 * @author: HLB
 * @desc: 文件管理文件夹和文件移动的vo 类
 * @version: V1.0.0
 */
public class ZyyFolderAndFileMoveVo {
    // 要移动的文件夹Id集合
    private List<Long> folderIds;
    // 要移动的文件Id集合
    private List<Long> fileIds;
    // 要移动那个文件夹Id
    private Long parentId;

    public List<Long> getFolderIds() {
        return folderIds;
    }

    public void setFolderIds(List<Long> folderIds) {
        this.folderIds = folderIds;
    }

    public List<Long> getFileIds() {
        return fileIds;
    }

    public void setFileIds(List<Long> fileIds) {
        this.fileIds = fileIds;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }
}
