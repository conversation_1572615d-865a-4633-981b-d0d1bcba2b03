package com.hys.zyy.manage.model.vo;
/**
 * 我的考试
 * <AUTHOR>
 * @date 2020-3-4上午9:37:49
 */
public class ZyyMyExamCourseVO  {
	//考试科目的id
	private Long examCourseId;
	//考试id
	private Long examId;
	//身份证号
	private String certificateNo;
	//发布状态
	private Integer publishStatus;
	//考试名称
	private String name;
	//考试分类
	private String typeName;
	//	考试科目名称
	private String courseName;
	//	考试开始日期
	private String startDate;
	//考试结束日期
	private String endDate;
	//考试开始时间
	private String courseStartTime;
	//考试结束时间
	private String courseEndTime;
	//	考试时长
	private String examDuration;
	//	考试状态
	private Integer examStatus;
	//	成绩发布状态
	private Integer scorePublishStatus;
	//试卷id
	private Long paperId;
	
	//查询条件
	private Long userId;
	//考试结束后是否允许学员查看试卷 0 否  1 是
	private Integer viewPaper;

	//是否可以重复考试（0：否 1：是）
	private Integer repeatSubmit;
	//可以重复考试的次数
	private Integer repeatSubmitNum;

	public Integer getRepeatSubmit() {
		return repeatSubmit;
	}

	public void setRepeatSubmit(Integer repeatSubmit) {
		this.repeatSubmit = repeatSubmit;
	}

	public Integer getRepeatSubmitNum() {
		return repeatSubmitNum;
	}

	public void setRepeatSubmitNum(Integer repeatSubmitNum) {
		this.repeatSubmitNum = repeatSubmitNum;
	}

	public Long getExamCourseId() {
		return examCourseId;
	}
	public void setExamCourseId(Long examCourseId) {
		this.examCourseId = examCourseId;
	}
	public Long getExamId() {
		return examId;
	}
	public void setExamId(Long examId) {
		this.examId = examId;
	}
	public String getCertificateNo() {
		return certificateNo;
	}
	public void setCertificateNo(String certificateNo) {
		this.certificateNo = certificateNo;
	}
	public Integer getPublishStatus() {
		return publishStatus;
	}
	public void setPublishStatus(Integer publishStatus) {
		this.publishStatus = publishStatus;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getTypeName() {
		return typeName;
	}
	public void setTypeName(String typeName) {
		this.typeName = typeName;
	}
	public String getCourseName() {
		return courseName;
	}
	public void setCourseName(String courseName) {
		this.courseName = courseName;
	}
	public String getStartDate() {
		return startDate;
	}
	public void setStartDate(String startDate) {
		this.startDate = startDate;
	}
	public String getEndDate() {
		return endDate;
	}
	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}
	public String getCourseStartTime() {
		return courseStartTime;
	}
	public void setCourseStartTime(String courseStartTime) {
		this.courseStartTime = courseStartTime;
	}
	public String getCourseEndTime() {
		return courseEndTime;
	}
	public void setCourseEndTime(String courseEndTime) {
		this.courseEndTime = courseEndTime;
	}
	public String getExamDuration() {
		return examDuration;
	}
	public void setExamDuration(String examDuration) {
		this.examDuration = examDuration;
	}
	public Integer getExamStatus() {
		return examStatus;
	}
	public void setExamStatus(Integer examStatus) {
		this.examStatus = examStatus;
	}
	public Integer getScorePublishStatus() {
		return scorePublishStatus;
	}
	public void setScorePublishStatus(Integer scorePublishStatus) {
		this.scorePublishStatus = scorePublishStatus;
	}
	public Long getPaperId() {
		return paperId;
	}
	public void setPaperId(Long paperId) {
		this.paperId = paperId;
	}
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	public Integer getViewPaper() {
		return viewPaper;
	}
	public void setViewPaper(Integer viewPaper) {
		this.viewPaper = viewPaper;
	}
}
