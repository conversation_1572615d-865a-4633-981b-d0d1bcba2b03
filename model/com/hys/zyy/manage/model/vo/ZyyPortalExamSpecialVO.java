package com.hys.zyy.manage.model.vo;

import java.util.Date;

import com.hys.zyy.manage.model.ZyyBaseObject;

/**
 * 专门处理住院医rct和portal考试的
 * <AUTHOR>
 * @date 2020-7-1上午11:17:11
 */
public class ZyyPortalExamSpecialVO extends ZyyBaseObject {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	private Long examId; 
	private Long examCourseId;
	private String studentId; 
	private Date answerStartTime ;
	private Date answerEndTime ;
    private Integer totalScore;
    private Long examineeId;
    private Long examCourseScoreId;
    private String examDuration;
	public Long getExamId() {
		return examId;
	}
	public void setExamId(Long examId) {
		this.examId = examId;
	}
	public Long getExamCourseId() {
		return examCourseId;
	}
	public void setExamCourseId(Long examCourseId) {
		this.examCourseId = examCourseId;
	}
	public String getStudentId() {
		return studentId;
	}
	public void setStudentId(String studentId) {
		this.studentId = studentId;
	}
	public Date getAnswerStartTime() {
		return answerStartTime;
	}
	public void setAnswerStartTime(Date answerStartTime) {
		this.answerStartTime = answerStartTime;
	}
	public Date getAnswerEndTime() {
		return answerEndTime;
	}
	public void setAnswerEndTime(Date answerEndTime) {
		this.answerEndTime = answerEndTime;
	}
	public Integer getTotalScore() {
		return totalScore;
	}
	public void setTotalScore(Integer totalScore) {
		this.totalScore = totalScore;
	}
	public Long getExamineeId() {
		return examineeId;
	}
	public void setExamineeId(Long examineeId) {
		this.examineeId = examineeId;
	}
	public Long getExamCourseScoreId() {
		return examCourseScoreId;
	}
	public void setExamCourseScoreId(Long examCourseScoreId) {
		this.examCourseScoreId = examCourseScoreId;
	}
	public String getExamDuration() {
		return examDuration;
	}
	public void setExamDuration(String examDuration) {
		this.examDuration = examDuration;
	}
}
