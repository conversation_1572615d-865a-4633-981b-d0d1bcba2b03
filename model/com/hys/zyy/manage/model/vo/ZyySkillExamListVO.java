package com.hys.zyy.manage.model.vo;

import java.io.Serializable;
import java.util.List;

/**
 * 列表
 * <AUTHOR>
 * @date 2021-09-09
 */
public class ZyySkillExamListVO extends ZyySkillExamVO implements Serializable {

	private static final long serialVersionUID = -9171903751050653674L;

	private List<Long> queryExamIds;

	//是否发布考生考核满意度评价状态 1 是 2 否
	private Long studentFeedbackId;
	
	//发布考官考核满意度评价状态 1 是 2 否
    private Long techerFeedbackId;

	/**
	 * 评分记录数量
	 */
	private Long scoreRecordCount;

    //取消发布是否显示
    private Integer publishStatusShowLogc;

	public Integer getPublishStatusShowLogc() {

		//考试已经开始
		if(this.getExamStartDate()!=null&&System.currentTimeMillis()>=this.getExamStartDate().getTime()){
			if(scoreRecordCount!=null&&scoreRecordCount>0){
				return 2;//有评分记录不可以显示
			}
			if(this.getExamEndDate()!=null&&System.currentTimeMillis()>=this.getExamEndDate().getTime()){
				return 2;//考试结束不可以显示
			}
		}

		return 1;
	}

	public void setPublishStatusShowLogc(Integer publishStatusShowLogc) {
	}

	public Long getScoreRecordCount() {
		return scoreRecordCount;
	}

	public void setScoreRecordCount(Long scoreRecordCount) {
		this.scoreRecordCount = scoreRecordCount;
	}

	public List<Long> getQueryExamIds() {
		return queryExamIds;
	}

	public void setQueryExamIds(List<Long> queryExamIds) {
		this.queryExamIds = queryExamIds;
	}

	/**
	 * 获取考试是否开始
	 * @return 1=开始考试
	 */
	public Integer getExamIsStartStatus(){
		if(this.getExamStartDate()!=null){
			if(System.currentTimeMillis()>=this.getExamStartDate().getTime()){
				return 1;
			}
		}
		return 0;
	}
	
	//在列表上加一个字段  避免每次查询  考试结束点，严格按照结束时间来算
    @Override
	public Integer getExamEndStatus() {
		
		//需要临时进行计算，数据库暂时不做计算
		if(super.getExamEndFinalDate()!=null){
			if(super.getTecherLimitStatus().intValue()==1){
				if((super.getExamEndFinalDate().getTime()-System.currentTimeMillis())>0){
					return 2;//未结束
				}
			}
		}
		return 1;//结束
	}

    @Override
	public void setExamEndStatus(Integer examEndStatus) {
	}

	//持续分钟数
	public Long getDurationMinute() {
		Long durationMinute=0L;
		if(super.getExamStartDate()!=null&&super.getExamEndDate()!=null){
			if(super.getExamEndDate().getTime()>super.getExamStartDate().getTime()){
				return (super.getExamEndDate().getTime()-super.getExamStartDate().getTime())/1000/60;
			}
		}
		return durationMinute;
	}

	public void setDurationMinute(Long durationMinute) {
	}

	public Long getStudentFeedbackId() {
		return studentFeedbackId;
	}

	public void setStudentFeedbackId(Long studentFeedbackId) {
		this.studentFeedbackId = studentFeedbackId;
	}

	public Long getTecherFeedbackId() {
		return techerFeedbackId;
	}

	public void setTecherFeedbackId(Long techerFeedbackId) {
		this.techerFeedbackId = techerFeedbackId;
	}
	
}
