package com.hys.zyy.manage.model.vo;

import java.io.Serializable;

public class ZyySkillExamScoreListVO extends ZyySkillExamListVO implements Serializable {

	private static final long serialVersionUID = 4754268627946274969L;

	/**
	 * 总得分
	 */
	private Double score;
	
	private Long pass;
	
	/*
	 * 学员用户ID
	 */
    private String studentUserId;
    
	private String studentIdCard;
	
	//是否过了考试结束时间
	private Integer gtExamEndStatus;

    /**
     * 学员身份证号码
     */
    private String studentName;
    
	public Integer getGtExamEndStatus() {
		//需要临时进行计算，数据库暂时不做计算
		if(super.getExamEndDate()!=null){
			if((super.getExamEndDate().getTime()-System.currentTimeMillis())<0){
				return 1;//结束
			}
		}
		return 2;//未结束
	}

	public void setGtExamEndStatus(Integer gtExamEndStatus) {
	}

	public String getStudentUserId() {
		return studentUserId;
	}

	public void setStudentUserId(String studentUserId) {
		this.studentUserId = studentUserId;
	}

	public String getStudentName() {
		return studentName;
	}

	public void setStudentName(String studentName) {
		this.studentName = studentName;
	}

	public Double getScore() {
		return score;
	}

	public void setScore(Double score) {
		this.score = score;
	}

	public Long getPass() {
		return pass;
	}

	public void setPass(Long pass) {
		this.pass = pass;
	}

	public String getStudentIdCard() {
		return studentIdCard;
	}

	public void setStudentIdCard(String studentIdCard) {
		this.studentIdCard = studentIdCard;
	}

	
}
