package com.hys.zyy.manage.model.vo;

import java.io.Serializable;
import com.hys.zyy.manage.model.ZyySkillExamStudent;

public class ZyySkillExamStudentListVO extends ZyySkillExamStudent implements Serializable {

	private static final long serialVersionUID = 874086492299168515L;

	/**
	 * 评分表ID
	 */
	private Long scoreId;
	
	/**
	 * 得分
	 */
	private Double score;
	
	/**
	 * 总分
	 */
	private Double passScore;
	
	/**
	 * 老师选择的通过不通过
	 */
	private Long pass;
	
	private String userName;
	
	private String idCard;
	
	/**
	 * 测试是否通过 1=通过 2=不通过
	 */
	private Long testPass;

	public Double getScore() {
		return score;
	}

	public void setScore(Double score) {
		this.score = score;
	}

	public Double getPassScore() {
		return passScore;
	}

	public void setPassScore(Double passScore) {
		this.passScore = passScore;
	}

	public Long getTestPass() {
		
		if(score!=null&&passScore!=null&&passScore>0){
			if(score>=passScore){
				return 1L;
			}
		}else{
			if(pass!=null){
				return pass;
			}
		}
		return 2L;
	}

	public void setTestPass(Long testPass) {
		this.testPass = testPass;
	}

	public Long getPass() {
		return pass;
	}

	public void setPass(Long pass) {
		this.pass = pass;
	}

	public Long getScoreId() {
		return scoreId;
	}

	public void setScoreId(Long scoreId) {
		this.scoreId = scoreId;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getIdCard() {
		return idCard;
	}

	public void setIdCard(String idCard) {
		this.idCard = idCard;
	}
	
}
