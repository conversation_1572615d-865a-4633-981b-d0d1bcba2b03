package com.hys.zyy.manage.model.vo;

import java.io.Serializable;
import java.util.List;

import com.hys.zyy.manage.model.ZyySkillExam;
import com.hys.zyy.manage.model.ZyyUserExtendVO;

public class ZyySkillExamVO extends ZyySkillExam implements Serializable {

	private static final long serialVersionUID = -242558611240560907L;
	
	private Long activeExamId;
	
	/**
	 * 查询参数
	 * 是否限制开始时间  limitStartDateVo =1 限制开始后 =0/null不限制
	 */
	private Long limitStartDateVo;
	
	/**
	 * 强行请求
	 */
	private Long forceRequest;
	
	/**
	 * 量表名称
	 */
    private String tableName;
    
    /**
     * 考官名称
     */
    private String techerUserId;
    
    /**
     * 考官名称
     */
    private String techerName;
    
    private String techerIdCard;
    
    /**
     * 考试结束状态
     * 	考试结束点，严格按照结束时间来算
     * @return
     */
    private Integer examEndStatus;
    
    /**
     * 考试开始状态
     */
    private Integer examStartStatus;
    
    /**
	 * 考试时长 按分钟单位计
	 * @return
	 */
    private Long durationMinute;
    
    private String createUserName;
    
    private String deptName;
    
    private String userTypeName;
        
    /**
     * 机构名称
     */
    private String orgName;
    
    /**
     * 专业名称
     */
    private String stdName;
    
    private List<ZyySkillExamStudentListVO> examStudentVos;
    
    private ZyyUserExtendVO zyyUser;
    
    private Integer studentCount, skillExamState;
    
    private String examStartDateStr, examEndDateStr, techerLimitTimeStr;
    
    private Double totalScore;
    
	public Integer getExamEndStatus() {
		
		//需要临时进行计算，数据库暂时不做计算
		if(super.getExamEndFinalDate()!=null){
			if(super.getTecherLimitStatus().intValue()==1){
				if((super.getExamEndFinalDate().getTime()-System.currentTimeMillis())>0){
					return 2;//未结束
				}
			}else if(super.getTecherLimitStatus().intValue()==2){
				return 2;//未结束
			}
		}
		return 1;//结束
	}
	public void setExamEndStatus(Integer examEndStatus) {
	}
	
	public Integer getExamStartStatus() {
		//需要临时进行计算，数据库暂时不做计算
		if(super.getExamStartDate()!=null){
			
			if((System.currentTimeMillis()-super.getExamStartDate().getTime())>0){
				return 1;//开始
			}
		}
		return 2;//未开始
	}
	public void setExamStartStatus(Integer examStartStatus) {
	}
	
	public Long getDurationMinute() {
		Long durationMinute=0L;
		if(super.getExamStartDate()!=null&&super.getExamEndDate()!=null){
			if(super.getExamEndDate().getTime()>super.getExamStartDate().getTime()){
				return (super.getExamEndDate().getTime()-super.getExamStartDate().getTime())/1000/60;
			}
		}
		return durationMinute;
	}
	public void setDurationMinute(Long durationMinute) {
	}
	public String getTableName() {
		return tableName;
	}
	public void setTableName(String tableName) {
		this.tableName = tableName;
	}
	public String getTecherName() {
		return techerName;
	}
	public void setTecherName(String techerName) {
		this.techerName = techerName;
	}
	public String getTecherUserId() {
		return techerUserId;
	}
	public void setTecherUserId(String techerUserId) {
		this.techerUserId = techerUserId;
	}
	public List<ZyySkillExamStudentListVO> getExamStudentVos() {
		return examStudentVos;
	}
	public void setExamStudentVos(List<ZyySkillExamStudentListVO> examStudentVos) {
		this.examStudentVos = examStudentVos;
	}
	public ZyyUserExtendVO getZyyUser() {
		return zyyUser;
	}
	public void setZyyUser(ZyyUserExtendVO zyyUser) {
		this.zyyUser = zyyUser;
	}
	public Integer getStudentCount() {
		return studentCount;
	}
	public void setStudentCount(Integer studentCount) {
		this.studentCount = studentCount;
	}
	public Integer getSkillExamState() {
		return skillExamState;
	}
	public void setSkillExamState(Integer skillExamState) {
		this.skillExamState = skillExamState;
	}
	public String getExamStartDateStr() {
		return examStartDateStr;
	}
	public void setExamStartDateStr(String examStartDateStr) {
		this.examStartDateStr = examStartDateStr == null ? null : examStartDateStr.trim();
	}
	public String getExamEndDateStr() {
		return examEndDateStr;
	}
	public void setExamEndDateStr(String examEndDateStr) {
		this.examEndDateStr = examEndDateStr == null ? null : examEndDateStr.trim();
	}
	public String getTecherLimitTimeStr() {
		return techerLimitTimeStr;
	}
	public void setTecherLimitTimeStr(String techerLimitTimeStr) {
		this.techerLimitTimeStr = techerLimitTimeStr == null ? null : techerLimitTimeStr.trim();
	}
	public Long getForceRequest() {
		return forceRequest;
	}
	public void setForceRequest(Long forceRequest) {
		this.forceRequest = forceRequest;
	}
	public Long getActiveExamId() {
		return activeExamId;
	}
	public void setActiveExamId(Long activeExamId) {
		this.activeExamId = activeExamId;
	}
	public Long getLimitStartDateVo() {
		return limitStartDateVo;
	}
	public void setLimitStartDateVo(Long limitStartDateVo) {
		this.limitStartDateVo = limitStartDateVo;
	}
	public String getCreateUserName() {
		return createUserName;
	}
	public void setCreateUserName(String createUserName) {
		this.createUserName = createUserName;
	}
	public String getDeptName() {
		return deptName;
	}
	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}
	public String getUserTypeName() {
		return userTypeName;
	}
	public void setUserTypeName(String userTypeName) {
		this.userTypeName = userTypeName;
	}
	public String getOrgName() {
		return orgName;
	}
	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}
	public String getTecherIdCard() {
		return techerIdCard;
	}
	public void setTecherIdCard(String techerIdCard) {
		this.techerIdCard = techerIdCard;
	}
	public String getStdName() {
		return stdName;
	}
	public void setStdName(String stdName) {
		this.stdName = stdName;
	}
	public Double getTotalScore() {
		return totalScore;
	}
	public void setTotalScore(Double totalScore) {
		this.totalScore = totalScore;
	}
	
}
