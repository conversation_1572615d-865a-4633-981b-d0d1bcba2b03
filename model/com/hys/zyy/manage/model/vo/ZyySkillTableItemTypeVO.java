package com.hys.zyy.manage.model.vo;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import com.hys.zyy.manage.model.ZyySkillTableItemType;
/**
 * 量表下类别数据 VO
 * <AUTHOR>
 * @date 2021-08-30
 */
public class ZyySkillTableItemTypeVO extends ZyySkillTableItemType implements Serializable{

	private static final long serialVersionUID = 7201630313123585359L;

	public ZyySkillTableItemTypeVO() {
		super();
	}
	
	public ZyySkillTableItemTypeVO(Long id, String name, Double totalScore) {
		super(id, name, totalScore);
	}

	public ZyySkillTableItemTypeVO(Long id, Long skillTypeId, String name,
			Long flag, Date createDate, Double totalScore) {
		super(id, skillTypeId, name, flag, createDate, totalScore);
	}

	public ZyySkillTableItemTypeVO(List<ZyySkillTableItemVO> tableItems) {
		super();
		this.tableItems = tableItems;
	}
	
	public ZyySkillTableItemTypeVO(ZyySkillTableItemType itemType,List<ZyySkillTableItemVO> tableItems){
		super(itemType.getId(), itemType.getSkillTableId(), itemType.getName(), itemType.getFlag(), itemType.getCreateDate(), itemType.getTotalScore());
		this.tableItems = tableItems;
	}

	private Double inputTotalScore;
	
	//类别集
	private List<ZyySkillTableItemVO> tableItems;

	public List<ZyySkillTableItemVO> getTableItems() {
		return tableItems;
	}

	public void setTableItems(List<ZyySkillTableItemVO> tableItems) {
		this.tableItems = tableItems;
	}

	public Double getInputTotalScore() {
		Double inputTotalScore=0D;
		if(tableItems!=null&&tableItems.size()>0){
			for (ZyySkillTableItemVO itemVo : tableItems) {
				if(itemVo.getExamScoreItem()!=null&&itemVo.getExamScoreItem().getTotalScore()!=null){
					inputTotalScore+=itemVo.getExamScoreItem().getTotalScore();
				}
			}
		}
		return inputTotalScore;
	}

	public void setInputTotalScore(Double inputTotalScore) {
		this.inputTotalScore = inputTotalScore;
	}
	
}
