package com.hys.zyy.manage.model.vo;

import java.io.Serializable;

import com.hys.zyy.manage.model.ZyySkillExamScoreItem;
import com.hys.zyy.manage.model.ZyySkillTableItem;

public class ZyySkillTableItemVO extends ZyySkillTableItem implements Serializable {

	private static final long serialVersionUID = 5697273177253027443L;
	/*
	 * 病例是否显示
	 */
	private Long caseShow;
	/*
	 * 评分量表名称
	 */
	private String skillTableName;
	/*
	 * 评分量表注意事项是否显示
	 */
	private Long skillTableNoteShow;
	/*
	 * 评分量表注意事项
	 */
	private String skillTableNote;
	/*
	 * 评分量表总分数
	 */
	private Double skillTabletotalScore;
	/*
	 * 评分量表及格分数
	 */
	private Double passScore;
	/*
	 * 考核项类型
	 */
	private String itemTypeName;
	/*
	 * 考核项类型总分
	 */
	private Double itemTypeTotalScore;
	/*
	 * 考核评语是否显示
	 */
	private Long testResultShow;

	public Long getCaseShow() {
		return caseShow;
	}

	public void setCaseShow(Long caseShow) {
		this.caseShow = caseShow;
	}

	public String getSkillTableName() {
		return skillTableName;
	}

	public void setSkillTableName(String skillTableName) {
		this.skillTableName = skillTableName == null ? null : skillTableName
				.trim();
	}

	public Long getSkillTableNoteShow() {
		return skillTableNoteShow;
	}

	public void setSkillTableNoteShow(Long skillTableNoteShow) {
		this.skillTableNoteShow = skillTableNoteShow;
	}

	public String getSkillTableNote() {
		return skillTableNote;
	}

	public void setSkillTableNote(String skillTableNote) {
		this.skillTableNote = skillTableNote == null ? null : skillTableNote
				.trim();
	}

	public Double getSkillTabletotalScore() {
		return skillTabletotalScore;
	}

	public void setSkillTabletotalScore(Double skillTabletotalScore) {
		this.skillTabletotalScore = skillTabletotalScore;
	}

	public Double getPassScore() {
		return passScore;
	}

	public void setPassScore(Double passScore) {
		this.passScore = passScore;
	}

	public String getItemTypeName() {
		return itemTypeName;
	}

	public void setItemTypeName(String itemTypeName) {
		this.itemTypeName = itemTypeName == null ? null : itemTypeName.trim();
	}

	public Double getItemTypeTotalScore() {
		return itemTypeTotalScore;
	}

	public void setItemTypeTotalScore(Double itemTypeTotalScore) {
		this.itemTypeTotalScore = itemTypeTotalScore;
	}

	public Long getTestResultShow() {
		return testResultShow;
	}

	public void setTestResultShow(Long testResultShow) {
		this.testResultShow = testResultShow;
	}

	/**
	 * 用户打分
	 */
	private ZyySkillExamScoreItem examScoreItem;

	public ZyySkillExamScoreItem getExamScoreItem() {
		return examScoreItem;
	}

	public void setExamScoreItem(ZyySkillExamScoreItem examScoreItem) {
		this.examScoreItem = examScoreItem;
	}

}