package com.hys.zyy.manage.model.vo;

import java.io.Serializable;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;

import com.hys.zyy.manage.model.ZyySkillTable;
import com.hys.zyy.manage.util.CollectionUtils;

/**
 * 量表数据 VO
 * <AUTHOR>
 * @date 2021-08-30
 */
public class ZyySkillTableVO extends ZyySkillTable implements Serializable {
	
	public ZyySkillTableVO(){}

	public ZyySkillTableVO(Long id, Long skillTypeId, Long flag, Long enabled,
			Date updateDate, Date createDate, String name, Double totalScore,
			Double passScore, Long noteShow, Long testResultShow, Long caseShow,String note) {
		super(id, skillTypeId, flag, enabled, updateDate, createDate, name, totalScore, passScore, noteShow, testResultShow, caseShow,note);
	}
	
	public ZyySkillTableVO(Long id, Long skillTypeId, Long flag, Long enabled,
			Date updateDate, Date createDate, String name,
			List<ZyySkillTableItemTypeVO> itemTypes, Double totalScore,
			Double passScore, Long noteShow, Long testResultShow, Long caseShow,String note) {
		this(id, skillTypeId, flag, enabled, updateDate, createDate, name, totalScore, passScore, noteShow, testResultShow, caseShow,note);
		this.itemTypes = itemTypes;
	}

	public ZyySkillTableVO(ZyySkillTable table) {
		this(table.getId(), table.getSkillTypeId(), table.getFlag(), table.getEnabled(), table.getUpdateDate(), table.getCreateDate(), table.getName(),
				table.getTotalScore(), table.getPassScore(), table.getNoteShow(), table.getTestResultShow(), table.getCaseShow(),table.getNote());
	}
	
	public ZyySkillTableVO(ZyySkillTable table, List<ZyySkillTableItemTypeVO> itemTypes) {
		this(table);
		this.itemTypes = itemTypes;
	}

	private static final long serialVersionUID = -7970704015058465704L;
	
	//量表-类别列表
	private List<ZyySkillTableItemTypeVO> itemTypes;

	public List<ZyySkillTableItemTypeVO> getItemTypes() {
		return itemTypes;
	}

	public void setItemTypes(List<ZyySkillTableItemTypeVO> itemTypes) {
		this.itemTypes = itemTypes;
	}
	
	public void read(ZyySkillTableItemVO temp) {
		if (temp != null) {
			// 病例是否显示
			this.setCaseShow(temp.getCaseShow());
			// 评分量表名称
			this.setName(temp.getSkillTableName());
			// 评分量表总分数
			this.setTotalScore(temp.getSkillTabletotalScore());
			// 及格分
			this.setPassScore(temp.getPassScore());
			// 评分量表注意事项是否显示
			this.setNoteShow(temp.getSkillTableNoteShow());
			// 评分量表注意事项
			this.setNote(temp.getSkillTableNote());
			// 考核评语是否显示
			this.setTestResultShow(temp.getTestResultShow());
		}
	}
	
	public void sort() {
		if (CollectionUtils.isNotEmpty(itemTypes)) {
			Collections.sort(itemTypes, new Comparator<ZyySkillTableItemTypeVO>() {
				@Override
				public int compare(ZyySkillTableItemTypeVO o1, ZyySkillTableItemTypeVO o2) {
					Long result = o1.getId() - o2.getId();
					return result < 0 ? -1 : 1;
				}
			});
			List<ZyySkillTableItemVO> tableItems = null;
			for (ZyySkillTableItemTypeVO itemType : itemTypes) {
				if (itemType == null)
					continue;
				tableItems = itemType.getTableItems();
				if (CollectionUtils.isNotEmpty(tableItems))
					Collections.sort(tableItems, new Comparator<ZyySkillTableItemVO>() {
						@Override
						public int compare(ZyySkillTableItemVO o1, ZyySkillTableItemVO o2) {
							Long result = o1.getId() - o2.getId();
							return result < 0 ? -1 : 1;
						}
					});
			}
		}
	}
}
