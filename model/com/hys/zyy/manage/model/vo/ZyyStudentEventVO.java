package com.hys.zyy.manage.model.vo;

import com.hys.zyy.manage.model.ZyyBaseObject;
import java.util.List;

/**
 * 学生日程安排
 */
public class ZyyStudentEventVO extends ZyyBaseObject {


	private static final long serialVersionUID = -3944040888208368711L;

	/**
	 * 数据来源类型
	 * 1 出科考试-开始结束时间
	 * 2 技能考核-开始结束时间
	 * 3 科目考核--科目开始结束时间
	 * 4 教学活动-开始结束时间
	 * 5 入科教育-开始结束时间
	 */
	private Integer saveSourceType;

	/**
	 * 当前数据来源实体的主键ID
	 */
	private Long saveId;

	/**
	 * 事件开始时间
	 */
	private String startDate;

	/**
	 * 事件结束时间
	 */
	private String endDate;

	private String startTime;

	private String endTime;

	/**
	 * 事件参与的学生
	 */
	private List<Long> studentUserIds;

	public Integer getSaveSourceType() {
		return saveSourceType;
	}

	public void setSaveSourceType(Integer saveSourceType) {
		this.saveSourceType = saveSourceType;
	}

	public Long getSaveId() {
		return saveId;
	}

	public void setSaveId(Long saveId) {
		this.saveId = saveId;
	}

	public String getStartDate() {
		return startDate;
	}

	public void setStartDate(String startDate) {
		this.startDate = startDate;
	}

	public String getEndDate() {
		return endDate;
	}

	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}

	public List<Long> getStudentUserIds() {
		return studentUserIds;
	}

	public void setStudentUserIds(List<Long> studentUserIds) {
		this.studentUserIds = studentUserIds;
	}

	public String getStartTime() {
		return startTime;
	}

	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}

	public String getEndTime() {
		return endTime;
	}

	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}
}
