package com.hys.zyy.manage.model.vo;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.hys.zyy.manage.model.ZyyStudentCheckRecord;
/**
 * 导出学生考试报名表信息的封装类
 * <AUTHOR>
 * @date 2020-4-26下午3:35:23
 */
public class ZyyStudentExamApplyUserVO implements Serializable {
    /**
     * ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;
    
//    考试ID	
    private Long zyyExamId;
    
//    人员类型		1 本单位住院医师 2 外单位委托培养住院医师 3 面向社会招收住院医师 4全日制硕士专业研究生
    private Integer doctorUserType;

    /**
     * 是否为军队人员  0 否  1 是
     */
    private Integer armyType;

    /**
     * 本次考试是否为初次报考 0 否  1 是
     */
    private Integer firstApplyExam;

    /**
     * 毕业专业备注
     */
    private String graduateProfRemark;

    /**
     * 是否为西部支援住院医师  0 否  1 是
     */
    private Integer westSupportDoctor;

    /**
     * 工作年限
     */
    private String workYear;

    /**
     * 进入培训基地时间
     */
    private Date workStartDate;

    /**
     * 培训专业备注
     */
    private String baseRemark;

    /**
     * 是否在协同单位培训 0 否  1 是
     */
    private Integer cooperatUnit;

    /**
     * 所在协同单位
     */
    private String cooperatUnitName;

    /**
     * 执业地点
     */
    private String practicePlace;

    /**
     * 是否为补考学员 0 否  1 是
     */
    private Integer makeupExam;

    /**
     * 报名表状态 2 未报名 1 已报名
     */
    private Integer applyStatus;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;
    
    private static final long serialVersionUID = 1L;
    
    // 不存库字段
    //科目id
    private String courseIds;
    
    //zyy_user_extend 的信息
    //姓名
    private String realName;
    //性别
    private Integer sex;
    //证件类型
    private Integer certificateType;
    //证件号码 
    private String certificateNo;
    //出生日期
    private Date birthday;
    //民族
    private String nation;
    //联系地址
    private String address;
    //邮政编码
    private String addressPostCode;
    //培训基地
    private String  orgName ;
    //医师资格取得时间
    private Date  getPracticeDate ;
    //医师资格证书编号
    private String  practiceCertificateNumber ;
    //证书类别
    private Integer  physiciansQualificationType ;
    //最高学历
    private String  highestRecordSchool ;
    //最高学位
    private String  highestDegree ;
    //学位类型
    private Integer  highestDegreeType ;
    //毕业学校
    private String  highestGraduateSchool ;
    //毕业时间
    private Date  graduationDate ;
    //毕业专业 
    private String  highestRecordProf ;
    //毕业证书编号
    private String  graduationCode ;
    //参加工作时间
    private String  workDate ;
    //执业范围
    private String  scopeCertificate ;
    //培训专业
    private String baseName;
  	//手机号
  	private String mobile;
  	//培训年级
  	private String year;
  	//人员类型
  	private Integer residencySource;
  	//培训年限
  	private Integer schoolSystem;
  	
  	//查询条件
    //多个审核层级的ids
  	private String flowIds;
  	//多层级的状态
  	private String flowCheckStatus;
  	//审核记录
  	private List<ZyyStudentCheckRecord> checkRecordList;
  	//机构id
  	private Long orgId;
  	//专业id
  	private Long baseId;
  	//报名的科目
  	private String courseName;
  	//年级id
  	private Long yearId;
  	
  	//导出的字段
  	private String exportExtendCol;
  	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	public Long getZyyExamId() {
		return zyyExamId;
	}
	public void setZyyExamId(Long zyyExamId) {
		this.zyyExamId = zyyExamId;
	}
	public Integer getDoctorUserType() {
		return doctorUserType;
	}
	public void setDoctorUserType(Integer doctorUserType) {
		this.doctorUserType = doctorUserType;
	}
	public Integer getArmyType() {
		return armyType;
	}
	public void setArmyType(Integer armyType) {
		this.armyType = armyType;
	}
	public Integer getFirstApplyExam() {
		return firstApplyExam;
	}
	public void setFirstApplyExam(Integer firstApplyExam) {
		this.firstApplyExam = firstApplyExam;
	}
	public String getGraduateProfRemark() {
		return graduateProfRemark;
	}
	public void setGraduateProfRemark(String graduateProfRemark) {
		this.graduateProfRemark = graduateProfRemark;
	}
	public Integer getWestSupportDoctor() {
		return westSupportDoctor;
	}
	public void setWestSupportDoctor(Integer westSupportDoctor) {
		this.westSupportDoctor = westSupportDoctor;
	}
	public String getWorkYear() {
		return workYear;
	}
	public void setWorkYear(String workYear) {
		this.workYear = workYear;
	}
	public Date getWorkStartDate() {
		return workStartDate;
	}
	public void setWorkStartDate(Date workStartDate) {
		this.workStartDate = workStartDate;
	}
	public String getBaseRemark() {
		return baseRemark;
	}
	public void setBaseRemark(String baseRemark) {
		this.baseRemark = baseRemark;
	}
	public Integer getCooperatUnit() {
		return cooperatUnit;
	}
	public void setCooperatUnit(Integer cooperatUnit) {
		this.cooperatUnit = cooperatUnit;
	}
	public String getCooperatUnitName() {
		return cooperatUnitName;
	}
	public void setCooperatUnitName(String cooperatUnitName) {
		this.cooperatUnitName = cooperatUnitName;
	}
	public String getPracticePlace() {
		return practicePlace;
	}
	public void setPracticePlace(String practicePlace) {
		this.practicePlace = practicePlace;
	}
	public Integer getMakeupExam() {
		return makeupExam;
	}
	public void setMakeupExam(Integer makeupExam) {
		this.makeupExam = makeupExam;
	}
	public Integer getApplyStatus() {
		return applyStatus;
	}
	public void setApplyStatus(Integer applyStatus) {
		this.applyStatus = applyStatus;
	}
	public Date getCreateDate() {
		return createDate;
	}
	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}
	public Date getUpdateDate() {
		return updateDate;
	}
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}
	public String getCourseIds() {
		return courseIds;
	}
	public void setCourseIds(String courseIds) {
		this.courseIds = courseIds;
	}
	public String getRealName() {
		return realName;
	}
	public void setRealName(String realName) {
		this.realName = realName;
	}
	public Integer getSex() {
		return sex;
	}
	public void setSex(Integer sex) {
		this.sex = sex;
	}
	public Integer getCertificateType() {
		return certificateType;
	}
	public void setCertificateType(Integer certificateType) {
		this.certificateType = certificateType;
	}
	public String getCertificateNo() {
		return certificateNo;
	}
	public void setCertificateNo(String certificateNo) {
		this.certificateNo = certificateNo;
	}
	public Date getBirthday() {
		return birthday;
	}
	public void setBirthday(Date birthday) {
		this.birthday = birthday;
	}
	public String getNation() {
		return nation;
	}
	public void setNation(String nation) {
		this.nation = nation;
	}
	public String getAddress() {
		return address;
	}
	public void setAddress(String address) {
		this.address = address;
	}
	public String getAddressPostCode() {
		return addressPostCode;
	}
	public void setAddressPostCode(String addressPostCode) {
		this.addressPostCode = addressPostCode;
	}
	public String getOrgName() {
		return orgName;
	}
	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}
	public Date getGetPracticeDate() {
		return getPracticeDate;
	}
	public void setGetPracticeDate(Date getPracticeDate) {
		this.getPracticeDate = getPracticeDate;
	}
	public String getPracticeCertificateNumber() {
		return practiceCertificateNumber;
	}
	public void setPracticeCertificateNumber(String practiceCertificateNumber) {
		this.practiceCertificateNumber = practiceCertificateNumber;
	}
	public Integer getPhysiciansQualificationType() {
		return physiciansQualificationType;
	}
	public void setPhysiciansQualificationType(Integer physiciansQualificationType) {
		this.physiciansQualificationType = physiciansQualificationType;
	}
	public String getHighestRecordSchool() {
		return highestRecordSchool;
	}
	public void setHighestRecordSchool(String highestRecordSchool) {
		this.highestRecordSchool = highestRecordSchool;
	}
	public String getHighestDegree() {
		return highestDegree;
	}
	public void setHighestDegree(String highestDegree) {
		this.highestDegree = highestDegree;
	}
	public Integer getHighestDegreeType() {
		return highestDegreeType;
	}
	public void setHighestDegreeType(Integer highestDegreeType) {
		this.highestDegreeType = highestDegreeType;
	}
	public String getHighestGraduateSchool() {
		return highestGraduateSchool;
	}
	public void setHighestGraduateSchool(String highestGraduateSchool) {
		this.highestGraduateSchool = highestGraduateSchool;
	}
	public Date getGraduationDate() {
		return graduationDate;
	}
	public void setGraduationDate(Date graduationDate) {
		this.graduationDate = graduationDate;
	}
	public String getHighestRecordProf() {
		return highestRecordProf;
	}
	public void setHighestRecordProf(String highestRecordProf) {
		this.highestRecordProf = highestRecordProf;
	}
	public String getGraduationCode() {
		return graduationCode;
	}
	public void setGraduationCode(String graduationCode) {
		this.graduationCode = graduationCode;
	}
	public String getWorkDate() {
		return workDate;
	}
	public void setWorkDate(String workDate) {
		this.workDate = workDate;
	}
	public String getScopeCertificate() {
		return scopeCertificate;
	}
	public void setScopeCertificate(String scopeCertificate) {
		this.scopeCertificate = scopeCertificate;
	}
	public String getBaseName() {
		return baseName;
	}
	public void setBaseName(String baseName) {
		this.baseName = baseName;
	}
	public String getMobile() {
		return mobile;
	}
	public void setMobile(String mobile) {
		this.mobile = mobile;
	}
	public String getYear() {
		return year;
	}
	public void setYear(String year) {
		this.year = year;
	}
	public Integer getResidencySource() {
		return residencySource;
	}
	public void setResidencySource(Integer residencySource) {
		this.residencySource = residencySource;
	}
	public Integer getSchoolSystem() {
		return schoolSystem;
	}
	public void setSchoolSystem(Integer schoolSystem) {
		this.schoolSystem = schoolSystem;
	}
	public String getFlowIds() {
		return flowIds;
	}
	public void setFlowIds(String flowIds) {
		this.flowIds = flowIds;
	}
	public String getFlowCheckStatus() {
		return flowCheckStatus;
	}
	public void setFlowCheckStatus(String flowCheckStatus) {
		this.flowCheckStatus = flowCheckStatus;
	}
	public List<ZyyStudentCheckRecord> getCheckRecordList() {
		return checkRecordList;
	}
	public void setCheckRecordList(List<ZyyStudentCheckRecord> checkRecordList) {
		this.checkRecordList = checkRecordList;
	}
	public Long getOrgId() {
		return orgId;
	}
	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}
	public Long getBaseId() {
		return baseId;
	}
	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}
	public String getCourseName() {
		return courseName;
	}
	public void setCourseName(String courseName) {
		this.courseName = courseName;
	}
	public Long getYearId() {
		return yearId;
	}
	public void setYearId(Long yearId) {
		this.yearId = yearId;
	}
	public String getExportExtendCol() {
		return exportExtendCol;
	}
	public void setExportExtendCol(String exportExtendCol) {
		this.exportExtendCol = exportExtendCol;
	}
	
     
}