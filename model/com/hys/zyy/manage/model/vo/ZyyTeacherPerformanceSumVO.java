package com.hys.zyy.manage.model.vo;

import java.io.Serializable;
/**
 * 带教绩效统计  总和
 * <AUTHOR>
 * @date 2019-1-14下午6:50:58
 */
public class ZyyTeacherPerformanceSumVO implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	//学员总人数 
	private String studentTotal;
	//平均带教人数  ＝ 当前医院学员总人数／当前医院的带教总人数
	private String teacherAvg ;
	// 大于平均值和小于平均值人数总和＝带教人数     
	//大于平均的人数
	private String teacherMore;
	//小于平均的人数
	private String teacherLess;
	//合格出科人数
	private String qualifyLeave;
	//不合格出科人数
	private String failLeave;
	//合格出科比例
	private String qualifyLeaveRate;
	//出科平均分
	private String leaveAvg;
	//出科大于平均分的人数
	private String leaveMore;
	//出科小于平均分的人数
	private String leaveLess;
	//教学活动类型  格式：['大于平均人数:55','小于平均人数:66','小堂客:99']
	private String activityType;
	//教学活动组织平均次数  : 教学活动出勤率 
	private String activityAvg;
	//教学活动类型组织的次数 格式：json [{value:35, name:'大于平均人数:55'},  {value:30,name:'小于平均人数:66'}, {value:30, name:'小堂客:99'}]
	private String activityTypeHold;
	//手册审核通过
	private String handbookPass;
	//手册审核未通过
	private String handbookFail;
	//手册审核通过比例
	//带教统计的【登记手册审核率】，带教站好登录，查看【手册审核】节点的数据,分子为一级审核的【审核通过】+【审核未通过】/分子为一级审核的【审核通过】+【审核未通过】+【未审核】（不是全部状态）
	private String handbookPassRate;
	//病例审核通过
	private String casePass;
	//病例审核未通过
	private String caseFail;
	//病例审核通过比例
	private String casePassRate;
	//评价分数 格式： [20.0, 40.9, 70.0, 13.2]
	private String evaluateScore;
	
	//和科室公用一个类， 以下是科室的字段
	//入科教育未参加人数
	private String notAttendEdu;
	//入科教育参加人数
	private String attendEdu;
	//入科教育出勤率
	private String attendEduRate;
	//指定带教人数
	private String hasTeacher;
	//未指定带教人数
	private String notHasTeacher;
	//指定带教率  
	//科室统计的【指定带教完成率】，科室账号登录，查看【指定带教】节点的数据，分子为已经指定的人次/分母为所有的人次；日期查询条件，查询的是学员的轮转时间的【开始时间】
	private String hasTeacherRate;
	//带教人数
	private String teacherTotal;
	//入科总人数
	private String enterDepartTotal;
	
	public String getStudentTotal() {
		return studentTotal;
	}
	public void setStudentTotal(String studentTotal) {
		this.studentTotal = studentTotal;
	}
	public String getTeacherAvg() {
		return teacherAvg;
	}
	public void setTeacherAvg(String teacherAvg) {
		this.teacherAvg = teacherAvg;
	}
	public String getTeacherMore() {
		return teacherMore;
	}
	public void setTeacherMore(String teacherMore) {
		this.teacherMore = teacherMore;
	}
	public String getTeacherLess() {
		return teacherLess;
	}
	public void setTeacherLess(String teacherLess) {
		this.teacherLess = teacherLess;
	}
	public String getQualifyLeave() {
		return qualifyLeave;
	}
	public void setQualifyLeave(String qualifyLeave) {
		this.qualifyLeave = qualifyLeave;
	}
	public String getFailLeave() {
		return failLeave;
	}
	public void setFailLeave(String failLeave) {
		this.failLeave = failLeave;
	}
	public String getLeaveAvg() {
		return leaveAvg;
	}
	public void setLeaveAvg(String leaveAvg) {
		this.leaveAvg = leaveAvg;
	}
	public String getLeaveMore() {
		return leaveMore;
	}
	public void setLeaveMore(String leaveMore) {
		this.leaveMore = leaveMore;
	}
	public String getLeaveLess() {
		return leaveLess;
	}
	public void setLeaveLess(String leaveLess) {
		this.leaveLess = leaveLess;
	}
	public String getActivityType() {
		return activityType;
	}
	public void setActivityType(String activityType) {
		this.activityType = activityType;
	}
	public String getActivityAvg() {
		return activityAvg;
	}
	public void setActivityAvg(String activityAvg) {
		this.activityAvg = activityAvg;
	}
	public String getActivityTypeHold() {
		return activityTypeHold;
	}
	public void setActivityTypeHold(String activityTypeHold) {
		this.activityTypeHold = activityTypeHold;
	}
	public String getHandbookPass() {
		return handbookPass;
	}
	public void setHandbookPass(String handbookPass) {
		this.handbookPass = handbookPass;
	}
	public String getHandbookFail() {
		return handbookFail;
	}
	public void setHandbookFail(String handbookFail) {
		this.handbookFail = handbookFail;
	}
	public String getCasePass() {
		return casePass;
	}
	public void setCasePass(String casePass) {
		this.casePass = casePass;
	}
	public String getCaseFail() {
		return caseFail;
	}
	public void setCaseFail(String caseFail) {
		this.caseFail = caseFail;
	}
	public String getEvaluateScore() {
		return evaluateScore;
	}
	public void setEvaluateScore(String evaluateScore) {
		this.evaluateScore = evaluateScore;
	}
	public String getQualifyLeaveRate() {
		return qualifyLeaveRate;
	}
	public void setQualifyLeaveRate(String qualifyLeaveRate) {
		this.qualifyLeaveRate = qualifyLeaveRate;
	}
	public String getHandbookPassRate() {
		return handbookPassRate;
	}
	public void setHandbookPassRate(String handbookPassRate) {
		this.handbookPassRate = handbookPassRate;
	}
	public String getCasePassRate() {
		return casePassRate;
	}
	public void setCasePassRate(String casePassRate) {
		this.casePassRate = casePassRate;
	}
	public String getNotAttendEdu() {
		return notAttendEdu;
	}
	public void setNotAttendEdu(String notAttendEdu) {
		this.notAttendEdu = notAttendEdu;
	}
	public String getAttendEdu() {
		return attendEdu;
	}
	public void setAttendEdu(String attendEdu) {
		this.attendEdu = attendEdu;
	}
	public String getAttendEduRate() {
		return attendEduRate;
	}
	public void setAttendEduRate(String attendEduRate) {
		this.attendEduRate = attendEduRate;
	}
	public String getHasTeacher() {
		return hasTeacher;
	}
	public void setHasTeacher(String hasTeacher) {
		this.hasTeacher = hasTeacher;
	}
	public String getNotHasTeacher() {
		return notHasTeacher;
	}
	public void setNotHasTeacher(String notHasTeacher) {
		this.notHasTeacher = notHasTeacher;
	}
	public String getHasTeacherRate() {
		return hasTeacherRate;
	}
	public void setHasTeacherRate(String hasTeacherRate) {
		this.hasTeacherRate = hasTeacherRate;
	}
	public String getTeacherTotal() {
		return teacherTotal;
	}
	public void setTeacherTotal(String teacherTotal) {
		this.teacherTotal = teacherTotal;
	}
	public String getEnterDepartTotal() {
		return enterDepartTotal;
	}
	public void setEnterDepartTotal(String enterDepartTotal) {
		this.enterDepartTotal = enterDepartTotal;
	}
}
