package com.hys.zyy.manage.model.vo;

import java.io.Serializable;
import java.util.List;

import com.hys.zyy.manage.model.ZyyActivityTypeVO;

/**
 * 带教绩效统计 
 * <AUTHOR>
 * @date 2019-1-10下午4:25:25
 */
public class ZyyTeacherPerformanceVO implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	//带教老师的id
	private Long teacherId;
	//姓名
	private String teacherName;
	// 工号
	private String jobNumber;
	//职称
	private String title;
	//证件号
	private String certificateNo;
	//科室
	private String deptName;
	//已带学员人数 -- 人员 已轮转人数
	private String studentTotal;
	//轮转结束的人数
	private String studentTotalEnd;
	//入科教育主讲数量 : 主持数量 -- 实际组织次数
	private String holdTotal;
	//合格出科人数 --  出科人数
	private String qualifyTotal;
	//出科总人数  
	private String leaveDepartTotal;
	//出科考试平均分 = 出科考试成绩总分／出勤考试人数
	private String leaveAvgScore;
	//出科合格率 -- 合格出科率
	private String leaveRate;
	//出科考试合格率 =  出科考试合格人数／出科考试总人数       出科考试合格人数：考试成绩占试卷总分60%的学员
	private String leaveExamRate;
	//教学活动统计 : 组织次数  
	private String activityTotal;
	//教学活动统计 : 主持教学活动次数
	private String speakerActivityTotal;
	//学员平均分
	private String studentEvalScore;
	//科室平均分
	private String departEvalScore;
	//专业基地平均分
	private String baseEvalScore;
	//培训基地平均分
	private String hospitalEvalScore;
	//我应该评价/已评价
	private String needDoneEval = "0/0";
	
	//登记手册审核率
	private String handbookRate;
	//大病历审核率
	private String caseRate;
	
	//科室和带教公用一个类，下面是科室需要的字段
	private Long deptId;
	//带教总数
	private String teacherTotal;
	//入科教育次数 == 入科教育总数：查询时间范围内需要组织的入科教育
	private String needHoldTotal;
	//入科教育出勤率：入科教育出勤学员数／需要参加入科教育的总人数
	private String attendEduRate;
	//入科人数 ：已接收入科的学员
	private String enterDepartNum;
	//指定带教率
	private String hasTeacherRate;
	//出勤率：出勤学员／应该参加总数
	private String attendActivityRate;
	//带教平均分
	private String teacherEvalScore;
	//出科考试数量：创建出科考试数量
	private String leaveExamNum;
	//出科考试出勤率：出勤考试人数／考试总人数
	private String leaveExamAttendRate;
	//学员的教学活动 不同的活动类型
	private List<ZyyActivityTypeVO> activityTypeList;
	
	public Long getTeacherId() {
		return teacherId;
	}
	public void setTeacherId(Long teacherId) {
		this.teacherId = teacherId;
	}
	public String getTeacherName() {
		return teacherName;
	}
	public void setTeacherName(String teacherName) {
		this.teacherName = teacherName;
	}
	public String getJobNumber() {
		return jobNumber;
	}
	public void setJobNumber(String jobNumber) {
		this.jobNumber = jobNumber == null ? null : jobNumber.trim();
	}
	public String getTitle() {
		return title;
	}
	public void setTitle(String title) {
		this.title = title;
	}
	public String getDeptName() {
		return deptName;
	}
	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}
	public String getStudentTotal() {
		return studentTotal;
	}
	public void setStudentTotal(String studentTotal) {
		this.studentTotal = studentTotal;
	}
	public String getStudentTotalEnd() {
		return studentTotalEnd;
	}
	public void setStudentTotalEnd(String studentTotalEnd) {
		this.studentTotalEnd = studentTotalEnd;
	}
	public String getHoldTotal() {
		return holdTotal;
	}
	public void setHoldTotal(String holdTotal) {
		this.holdTotal = holdTotal;
	}
	public String getQualifyTotal() {
		return qualifyTotal;
	}
	public void setQualifyTotal(String qualifyTotal) {
		this.qualifyTotal = qualifyTotal;
	}
	public String getLeaveDepartTotal() {
		return leaveDepartTotal;
	}
	public void setLeaveDepartTotal(String leaveDepartTotal) {
		this.leaveDepartTotal = leaveDepartTotal;
	}
	public String getLeaveAvgScore() {
		return leaveAvgScore;
	}
	public void setLeaveAvgScore(String leaveAvgScore) {
		this.leaveAvgScore = leaveAvgScore;
	}
	public String getLeaveRate() {
		return leaveRate;
	}
	public void setLeaveRate(String leaveRate) {
		this.leaveRate = leaveRate;
	}
	public String getLeaveExamRate() {
		return leaveExamRate;
	}
	public void setLeaveExamRate(String leaveExamRate) {
		this.leaveExamRate = leaveExamRate;
	}
	public String getActivityTotal() {
		return activityTotal;
	}
	public void setActivityTotal(String activityTotal) {
		this.activityTotal = activityTotal;
	}
	public String getSpeakerActivityTotal() {
		return speakerActivityTotal;
	}
	public void setSpeakerActivityTotal(String speakerActivityTotal) {
		this.speakerActivityTotal = speakerActivityTotal;
	}
	public String getStudentEvalScore() {
		return studentEvalScore;
	}
	public void setStudentEvalScore(String studentEvalScore) {
		this.studentEvalScore = studentEvalScore;
	}
	public String getDepartEvalScore() {
		return departEvalScore;
	}
	public void setDepartEvalScore(String departEvalScore) {
		this.departEvalScore = departEvalScore;
	}
	public String getBaseEvalScore() {
		return baseEvalScore;
	}
	public void setBaseEvalScore(String baseEvalScore) {
		this.baseEvalScore = baseEvalScore;
	}
	public String getHospitalEvalScore() {
		return hospitalEvalScore;
	}
	public void setHospitalEvalScore(String hospitalEvalScore) {
		this.hospitalEvalScore = hospitalEvalScore;
	}
	public String getHandbookRate() {
		return handbookRate;
	}
	public void setHandbookRate(String handbookRate) {
		this.handbookRate = handbookRate;
	}
	public String getCaseRate() {
		return caseRate;
	}
	public void setCaseRate(String caseRate) {
		this.caseRate = caseRate;
	}
	public Long getDeptId() {
		return deptId;
	}
	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}
	public String getTeacherTotal() {
		return teacherTotal;
	}
	public void setTeacherTotal(String teacherTotal) {
		this.teacherTotal = teacherTotal;
	}
	public String getNeedHoldTotal() {
		return needHoldTotal;
	}
	public void setNeedHoldTotal(String needHoldTotal) {
		this.needHoldTotal = needHoldTotal;
	}
	public String getAttendEduRate() {
		return attendEduRate;
	}
	public void setAttendEduRate(String attendEduRate) {
		this.attendEduRate = attendEduRate;
	}
	public String getEnterDepartNum() {
		return enterDepartNum;
	}
	public void setEnterDepartNum(String enterDepartNum) {
		this.enterDepartNum = enterDepartNum;
	}
	public String getHasTeacherRate() {
		return hasTeacherRate;
	}
	public void setHasTeacherRate(String hasTeacherRate) {
		this.hasTeacherRate = hasTeacherRate;
	}
	public String getAttendActivityRate() {
		return attendActivityRate;
	}
	public void setAttendActivityRate(String attendActivityRate) {
		this.attendActivityRate = attendActivityRate;
	}
	public String getTeacherEvalScore() {
		return teacherEvalScore;
	}
	public void setTeacherEvalScore(String teacherEvalScore) {
		this.teacherEvalScore = teacherEvalScore;
	}
	public String getLeaveExamNum() {
		return leaveExamNum;
	}
	public void setLeaveExamNum(String leaveExamNum) {
		this.leaveExamNum = leaveExamNum;
	}
	public String getLeaveExamAttendRate() {
		return leaveExamAttendRate;
	}
	public void setLeaveExamAttendRate(String leaveExamAttendRate) {
		this.leaveExamAttendRate = leaveExamAttendRate;
	}
	public String getCertificateNo() {
		return certificateNo;
	}
	public void setCertificateNo(String certificateNo) {
		this.certificateNo = certificateNo;
	}
	public String getNeedDoneEval() {
		return needDoneEval;
	}
	public void setNeedDoneEval(String needDoneEval) {
		this.needDoneEval = needDoneEval;
	}
	public List<ZyyActivityTypeVO> getActivityTypeList() {
		return activityTypeList;
	}
	public void setActivityTypeList(List<ZyyActivityTypeVO> activityTypeList) {
		this.activityTypeList = activityTypeList;
	}
}
