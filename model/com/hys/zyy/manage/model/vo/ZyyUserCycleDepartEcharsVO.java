package com.hys.zyy.manage.model.vo;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.hys.zyy.manage.model.ZyyJoinDeptRecordSkill;

/**
 * 用户轮转信息
 * <AUTHOR>
 * @date 2018-8-16上午10:31:55
 */
public class ZyyUserCycleDepartEcharsVO implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	//科室id
	private Long deptId;
	//科室名称
    private String deptName;
    //手册名称
    private String handbookName;
    //手册审核状态
    private Integer checkStatus;
    //轮转开始时间
    private Date startDate;
    private String cycleStartDate;
    //轮转结束时间
    private Date endDate;
    private String cycleEndDate;
    //带教id
    private Long teacherId;
    //带教姓名
    private String teacherName;
    //请假天数 
    private Integer leaveCount;
    // 1 -迟到 2 -早退 3 -旷工
    private Integer lateCount;
    private Integer earlyCount;
    private Integer notWorkCount;
    //活动的名称和审核状态
    private String activityName;
    
    /*
     * 入科
     * a. audit_state 为空的时候，status== 1 则表示已入科，否则就是未入科
     * b. audit_state 不为空的时候，已出科
     * 出科
     * a. audit_state为空或者0，则表示 未出科
     * b. auditState== 1 ， 则表示合格出科
     * c. auditState== 2 ，则表示不合格出科
     */
    private Integer status;
    private Integer auditState;
    //出入科的id
    private Long jdrId;
    //理论成绩
    private Float theoryGrade;
    //出科评语
    private String auditReamrk;
    //出科成绩：心肺复苏：70；大病历：70； 子项
    private List<ZyyJoinDeptRecordSkill> recordSkill;
    //是否是当前轮转时间内  1 :表示是
    private Integer cycleTime;
    // 手册状态 和带教老师  : 同一个开始时间的
    private List<ZyyUserCycleDepartEcharsVO> sameStartDateList;
    
    //评价状态：1 未评价  2 已评价  3 无需评价
    private Integer teacherStatus;
    private Integer departStatus;
    //带教评价时间段 
    private String teacherTime;
    //科室评价时间段
    private String departTime;
    private Integer enterTeachStatus;//是否参加入科教育  0 未参加   1 已参加

	public Long getDeptId() {
		return deptId;
	}

	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}

	public String getDeptName() {
		return deptName;
	}

	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}

	public String getHandbookName() {
		return handbookName;
	}

	public void setHandbookName(String handbookName) {
		this.handbookName = handbookName;
	}

	public Integer getCheckStatus() {
		return checkStatus;
	}

	public void setCheckStatus(Integer checkStatus) {
		this.checkStatus = checkStatus;
	}
	
	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	public String getCycleStartDate() {
		return cycleStartDate;
	}

	public void setCycleStartDate(String cycleStartDate) {
		this.cycleStartDate = cycleStartDate;
	}

	public String getCycleEndDate() {
		return cycleEndDate;
	}

	public void setCycleEndDate(String cycleEndDate) {
		this.cycleEndDate = cycleEndDate;
	}
	
	public Long getTeacherId() {
		return teacherId;
	}

	public void setTeacherId(Long teacherId) {
		this.teacherId = teacherId;
	}

	public String getTeacherName() {
		return teacherName;
	}

	public void setTeacherName(String teacherName) {
		this.teacherName = teacherName;
	}

	public Integer getLeaveCount() {
		return leaveCount;
	}

	public void setLeaveCount(Integer leaveCount) {
		this.leaveCount = leaveCount;
	}

	public Integer getLateCount() {
		return lateCount;
	}

	public void setLateCount(Integer lateCount) {
		this.lateCount = lateCount;
	}

	public Integer getEarlyCount() {
		return earlyCount;
	}

	public void setEarlyCount(Integer earlyCount) {
		this.earlyCount = earlyCount;
	}

	public Integer getNotWorkCount() {
		return notWorkCount;
	}

	public void setNotWorkCount(Integer notWorkCount) {
		this.notWorkCount = notWorkCount;
	}

	public String getActivityName() {
		return activityName;
	}

	public void setActivityName(String activityName) {
		this.activityName = activityName;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Integer getAuditState() {
		return auditState;
	}

	public void setAuditState(Integer auditState) {
		this.auditState = auditState;
	}

	public Long getJdrId() {
		return jdrId;
	}

	public void setJdrId(Long jdrId) {
		this.jdrId = jdrId;
	}

	public Float getTheoryGrade() {
		return theoryGrade;
	}

	public void setTheoryGrade(Float theoryGrade) {
		this.theoryGrade = theoryGrade;
	}

	public String getAuditReamrk() {
		return auditReamrk;
	}

	public void setAuditReamrk(String auditReamrk) {
		this.auditReamrk = auditReamrk;
	}

	public List<ZyyJoinDeptRecordSkill> getRecordSkill() {
		return recordSkill;
	}

	public void setRecordSkill(List<ZyyJoinDeptRecordSkill> recordSkill) {
		this.recordSkill = recordSkill;
	}

	public Integer getCycleTime() {
		return cycleTime;
	}

	public void setCycleTime(Integer cycleTime) {
		this.cycleTime = cycleTime;
	}

	public List<ZyyUserCycleDepartEcharsVO> getSameStartDateList() {
		return sameStartDateList;
	}

	public void setSameStartDateList(
			List<ZyyUserCycleDepartEcharsVO> sameStartDateList) {
		this.sameStartDateList = sameStartDateList;
	}

	public Integer getTeacherStatus() {
		return teacherStatus;
	}

	public void setTeacherStatus(Integer teacherStatus) {
		this.teacherStatus = teacherStatus;
	}

	public Integer getDepartStatus() {
		return departStatus;
	}

	public void setDepartStatus(Integer departStatus) {
		this.departStatus = departStatus;
	}

	public String getTeacherTime() {
		return teacherTime;
	}

	public void setTeacherTime(String teacherTime) {
		this.teacherTime = teacherTime;
	}

	public String getDepartTime() {
		return departTime;
	}

	public void setDepartTime(String departTime) {
		this.departTime = departTime;
	}

	public Integer getEnterTeachStatus() {
		return enterTeachStatus;
	}

	public void setEnterTeachStatus(Integer enterTeachStatus) {
		this.enterTeachStatus = enterTeachStatus;
	}
}
