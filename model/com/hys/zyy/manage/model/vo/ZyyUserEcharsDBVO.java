package com.hys.zyy.manage.model.vo;

import java.io.Serializable;

/**
 * 用户图表数据 DBVO
 * <AUTHOR>
 * @date 2018-8-13下午6:50:11
 */
public class ZyyUserEcharsDBVO implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	//名称
	private String name;
	//年度id
	private Long yearId;
	//年度
	private String year;
	//统计的数量
	private Integer countNum;
	
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public Long getYearId() {
		return yearId;
	}
	public void setYearId(Long yearId) {
		this.yearId = yearId;
	}
	public String getYear() {
		return year;
	}
	public void setYear(String year) {
		this.year = year;
	}
	public Integer getCountNum() {
		return countNum;
	}
	public void setCountNum(Integer countNum) {
		this.countNum = countNum;
	}
	
}
