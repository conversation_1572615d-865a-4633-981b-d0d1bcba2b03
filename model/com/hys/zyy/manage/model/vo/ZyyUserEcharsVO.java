package com.hys.zyy.manage.model.vo;

import java.io.Serializable;
/**
 * 用户图表数据 VO
 * <AUTHOR>
 * @date 2018-8-14上午9:31:53
 */
public class ZyyUserEcharsVO implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	//年度id
	private Long yearId;
	//年度
	private String year;
	//单位人数量
	private Integer unitCount;
	/*
	 * 本单位住院医师
	 */
	private Integer unitLocalCount;
	/*
	 * 外单位委托培养住院医师
	 */
	private Integer unitForeignCount;
	//社会人数量
	private Integer socialCount;
	/*
	 * 公卫医师数量
	 */
	private Integer gwysCount;
	//学位衔接数量
	private Integer degreeCount;
	//单位、学校名称
	private String unitName;
	//小计
	private Integer total;
	
	public Long getYearId() {
		return yearId;
	}
	public void setYearId(Long yearId) {
		this.yearId = yearId;
	}
	public String getYear() {
		return year;
	}
	public void setYear(String year) {
		this.year = year;
	}
	public Integer getUnitCount() {
		if(unitCount != null)
			return unitCount;
		else
			return (getUnitLocalCount() == null ? 0 : getUnitLocalCount()) + (getUnitForeignCount() == null ? 0 : getUnitForeignCount());
	}
	public void setUnitCount(Integer unitCount) {
		this.unitCount = unitCount;
	}
	
	public Integer getUnitLocalCount() {
		return unitLocalCount;
	}
	public void setUnitLocalCount(Integer unitLocalCount) {
		this.unitLocalCount = unitLocalCount;
	}
	public Integer getUnitForeignCount() {
		return unitForeignCount;
	}
	public void setUnitForeignCount(Integer unitForeignCount) {
		this.unitForeignCount = unitForeignCount;
	}
	public Integer getSocialCount() {
		return socialCount;
	}
	public void setSocialCount(Integer socialCount) {
		this.socialCount = socialCount;
	}
	
	public Integer getGwysCount() {
		return gwysCount;
	}
	public void setGwysCount(Integer gwysCount) {
		this.gwysCount = gwysCount;
	}
	public Integer getDegreeCount() {
		return degreeCount;
	}
	public void setDegreeCount(Integer degreeCount) {
		this.degreeCount = degreeCount;
	}
	public Integer getTotal() {
		return total;
	}
	public void setTotal(Integer total) {
		this.total = total;
	}
	public String getUnitName() {
		return unitName;
	}
	public void setUnitName(String unitName) {
		this.unitName = unitName;
	}
}
