package com.hys.zyy.manage.model.vo;

import java.io.Serializable;

/**
 * 年度带教学生人数
 * <AUTHOR>
 * @date 2020-5-13下午4:56:16
 */
public class ZyyYearTeacherStudentVO implements Serializable {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	//	身份证号	
	private String idCard;
//	年度	
	private Integer year;
//	已带学员人数	
	private Integer teachStudentNum = 0;
//	审核登记手册数	
	private Integer auditRegisterNum = 0;
	//主持人入科教育次数
	private Integer hostEducationNum = 0;
//	1月带教人数	
	private Integer teachStudentNum1 = 0;
//	2月带教人数	
	private Integer teachStudentNum2 = 0;
//	3月带教人数	
	private Integer teachStudentNum3 = 0;
//	4月带教人数	
	private Integer teachStudentNum4 = 0;
//	5月带教人数	
	private Integer teachStudentNum5 = 0;
//	6月带教人数	
	private Integer teachStudentNum6 = 0;
//	7月带教人数	
	private Integer teachStudentNum7 = 0;
//	8月带教人数	
	private Integer teachStudentNum8 = 0;
//	9月带教人数	
	private Integer teachStudentNum9 = 0;
//	10月带教人数	
	private Integer teachStudentNum10 = 0;
//	11月带教人数	
	private Integer teachStudentNum11 = 0;
//	12月带教人数	
	private Integer teachStudentNum12 = 0;
	
	public String getIdCard() {
		return idCard;
	}
	public void setIdCard(String idCard) {
		this.idCard = idCard;
	}
	public Integer getYear() {
		return year;
	}
	public void setYear(Integer year) {
		this.year = year;
	}
	public Integer getTeachStudentNum() {
		return teachStudentNum;
	}
	public void setTeachStudentNum(Integer teachStudentNum) {
		this.teachStudentNum = teachStudentNum;
	}
	public Integer getAuditRegisterNum() {
		return auditRegisterNum;
	}
	public void setAuditRegisterNum(Integer auditRegisterNum) {
		this.auditRegisterNum = auditRegisterNum;
	}
	
	public Integer getHostEducationNum() {
		return hostEducationNum;
	}
	public void setHostEducationNum(Integer hostEducationNum) {
		this.hostEducationNum = hostEducationNum;
	}
	public Integer getTeachStudentNum1() {
		return teachStudentNum1;
	}
	public void setTeachStudentNum1(Integer teachStudentNum1) {
		this.teachStudentNum1 = teachStudentNum1;
	}
	public Integer getTeachStudentNum2() {
		return teachStudentNum2;
	}
	public void setTeachStudentNum2(Integer teachStudentNum2) {
		this.teachStudentNum2 = teachStudentNum2;
	}
	public Integer getTeachStudentNum3() {
		return teachStudentNum3;
	}
	public void setTeachStudentNum3(Integer teachStudentNum3) {
		this.teachStudentNum3 = teachStudentNum3;
	}
	public Integer getTeachStudentNum4() {
		return teachStudentNum4;
	}
	public void setTeachStudentNum4(Integer teachStudentNum4) {
		this.teachStudentNum4 = teachStudentNum4;
	}
	public Integer getTeachStudentNum5() {
		return teachStudentNum5;
	}
	public void setTeachStudentNum5(Integer teachStudentNum5) {
		this.teachStudentNum5 = teachStudentNum5;
	}
	public Integer getTeachStudentNum6() {
		return teachStudentNum6;
	}
	public void setTeachStudentNum6(Integer teachStudentNum6) {
		this.teachStudentNum6 = teachStudentNum6;
	}
	public Integer getTeachStudentNum7() {
		return teachStudentNum7;
	}
	public void setTeachStudentNum7(Integer teachStudentNum7) {
		this.teachStudentNum7 = teachStudentNum7;
	}
	public Integer getTeachStudentNum8() {
		return teachStudentNum8;
	}
	public void setTeachStudentNum8(Integer teachStudentNum8) {
		this.teachStudentNum8 = teachStudentNum8;
	}
	public Integer getTeachStudentNum9() {
		return teachStudentNum9;
	}
	public void setTeachStudentNum9(Integer teachStudentNum9) {
		this.teachStudentNum9 = teachStudentNum9;
	}
	public Integer getTeachStudentNum10() {
		return teachStudentNum10;
	}
	public void setTeachStudentNum10(Integer teachStudentNum10) {
		this.teachStudentNum10 = teachStudentNum10;
	}
	public Integer getTeachStudentNum11() {
		return teachStudentNum11;
	}
	public void setTeachStudentNum11(Integer teachStudentNum11) {
		this.teachStudentNum11 = teachStudentNum11;
	}
	public Integer getTeachStudentNum12() {
		return teachStudentNum12;
	}
	public void setTeachStudentNum12(Integer teachStudentNum12) {
		this.teachStudentNum12 = teachStudentNum12;
	}
}
