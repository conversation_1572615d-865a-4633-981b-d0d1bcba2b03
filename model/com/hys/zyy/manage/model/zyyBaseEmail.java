package com.hys.zyy.manage.model;

/**
 * 邮件配置类的pojo
 */
public class zyyBaseEmail extends ZyyBaseObject {

	
	/**
	 * 
	 */
	private static final long serialVersionUID = 6562205547409528127L;

	private Long id;
	
	private String mailserverhost;//
	
	private String mailserverport;
	
	private String username;
	
	private String password;
	
	private String fromaddress;
	
	private String emailtitle;
	
	private String emailcontent;
	
	private String namefailure;
	
	private String emailfailure;
	
	private String sendsuccess;
	
	private String exceptfailure;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getMailserverhost() {
		return mailserverhost;
	}

	public void setMailserverhost(String mailserverhost) {
		this.mailserverhost = mailserverhost;
	}

	public String getMailserverport() {
		return mailserverport;
	}

	public void setMailserverport(String mailserverport) {
		this.mailserverport = mailserverport;
	}

	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public String getFromaddress() {
		return fromaddress;
	}

	public void setFromaddress(String fromaddress) {
		this.fromaddress = fromaddress;
	}

	public String getEmailtitle() {
		return emailtitle;
	}

	public void setEmailtitle(String emailtitle) {
		this.emailtitle = emailtitle;
	}

	public String getEmailcontent() {
		return emailcontent;
	}

	public void setEmailcontent(String emailcontent) {
		this.emailcontent = emailcontent;
	}

	public String getNamefailure() {
		return namefailure;
	}

	public void setNamefailure(String namefailure) {
		this.namefailure = namefailure;
	}

	public String getEmailfailure() {
		return emailfailure;
	}

	public void setEmailfailure(String emailfailure) {
		this.emailfailure = emailfailure;
	}

	public String getSendsuccess() {
		return sendsuccess;
	}

	public void setSendsuccess(String sendsuccess) {
		this.sendsuccess = sendsuccess;
	}

	public String getExceptfailure() {
		return exceptfailure;
	}

	public void setExceptfailure(String exceptfailure) {
		this.exceptfailure = exceptfailure;
	}
	
	
}