package com.hys.zyy.manage.model;

import java.util.Date;

public class zyyResidencyLeaveAttachment {

	private Long id;
	
	private Long leaveId;
	
	private String attachmentName;
	
	private String attachmentUrl;
	
	private String attachmentStatus;
	
	private Date createDate;
	
	private Boolean canEdit = true;
	/*
	 * 文件存储在本地服务器
	 */
	private Boolean isLocalFile;
	/*
	 * 文件存储在七牛云
	 */
	private Boolean isQiNiuFile;
	private Boolean isImg;
	private Boolean isPdf;
	private String thumbnail;
	
	public Boolean getCanEdit() {
		return canEdit;
	}

	public void setCanEdit(Boolean canEdit) {
		this.canEdit = canEdit;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getLeaveId() {
		return leaveId;
	}

	public void setLeaveId(Long leaveId) {
		this.leaveId = leaveId;
	}

	public String getAttachmentName() {
		return attachmentName;
	}

	public void setAttachmentName(String attachmentName) {
		this.attachmentName = attachmentName;
	}

	public String getAttachmentUrl() {
		return attachmentUrl;
	}

	public void setAttachmentUrl(String attachmentUrl) {
		this.attachmentUrl = attachmentUrl;
	}

	public String getAttachmentStatus() {
		return attachmentStatus;
	}

	public void setAttachmentStatus(String attachmentStatus) {
		this.attachmentStatus = attachmentStatus;
	}

	public Boolean getIsLocalFile() {
		return isLocalFile;
	}

	public void setIsLocalFile(Boolean isLocalFile) {
		this.isLocalFile = isLocalFile;
	}

	public Boolean getIsQiNiuFile() {
		return isQiNiuFile;
	}

	public void setIsQiNiuFile(Boolean isQiNiuFile) {
		this.isQiNiuFile = isQiNiuFile;
	}

	public Boolean getIsImg() {
		return isImg;
	}

	public void setIsImg(Boolean isImg) {
		this.isImg = isImg;
	}

	public Boolean getIsPdf() {
		return isPdf;
	}

	public void setIsPdf(Boolean isPdf) {
		this.isPdf = isPdf;
	}

	public String getThumbnail() {
		return thumbnail;
	}

	public void setThumbnail(String thumbnail) {
		this.thumbnail = thumbnail == null ? null : thumbnail.trim();
	}
	
}