package com.hys.zyy.manage.query;

import java.util.List;

public class CourseQuery {

	//课件标题或者教师名称
	private String nameQuery;
	//收费类型 1收费 2免费
	private Integer chargeTypeQuery;
	//视频状态 1正常 2下架
	private Integer stateQuery;
    //一级分类
    private String catagoryCode;
    //二级分类
    private String subCatagoryCode;
    //课程进度 0：未开始 1：进行中 2：已结束
    private Integer playState;
    
    private Integer pageNo;
    
    private Integer pageSize;
    
    private Long zyyUserId;
    
    private List<Long> courseIds;

    public List<Long> getCourseIds() {
        return courseIds;
    }

    public void setCourseIds(List<Long> courseIds) {
        this.courseIds = courseIds;
    }
    
	public Long getZyyUserId() {
		return zyyUserId;
	}
	public void setZyyUserId(Long zyyUserId) {
		this.zyyUserId = zyyUserId;
	}
	public Integer getPageNo() {
		return pageNo;
	}
	public void setPageNo(Integer pageNo) {
		this.pageNo = pageNo;
	}
	public Integer getPageSize() {
		return pageSize;
	}
	public void setPageSize(Integer pageSize) {
		this.pageSize = pageSize;
	}
	public Integer getPlayState() {
		return playState;
	}
	public void setPlayState(Integer playState) {
		this.playState = playState;
	}
	public String getNameQuery() {
		return nameQuery;
	}
	public void setNameQuery(String nameQuery) {
		this.nameQuery = nameQuery;
	}
	public Integer getChargeTypeQuery() {
		return chargeTypeQuery;
	}
	public void setChargeTypeQuery(Integer chargeTypeQuery) {
		this.chargeTypeQuery = chargeTypeQuery;
	}
	public Integer getStateQuery() {
		return stateQuery;
	}
	public void setStateQuery(Integer stateQuery) {
		this.stateQuery = stateQuery;
	}
	public String getCatagoryCode() {
		return catagoryCode;
	}
	public void setCatagoryCode(String catagoryCode) {
		this.catagoryCode = catagoryCode;
	}
	public String getSubCatagoryCode() {
		return subCatagoryCode;
	}
	public void setSubCatagoryCode(String subCatagoryCode) {
		this.subCatagoryCode = subCatagoryCode;
	}
}
