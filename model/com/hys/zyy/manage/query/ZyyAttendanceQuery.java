package com.hys.zyy.manage.query;

import java.util.Date;

import com.hys.zyy.manage.model.ZyyBaseObject;

/**
 * 日常考勤查询对象
 * <AUTHOR>
 *
 */
public class ZyyAttendanceQuery extends ZyyBaseObject  {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 5963849453131181804L;

	/**
	 * 学届
	 */
	private Long yearId;
	
	/**
	 * 月份
	 */
	private int month = new Date().getMonth() + 1;
	
	/**
	 * 年份
	 */
	private int year;
	
	/**
	 * 学制
	 */
	private int xz;

	/**
	 * 基地ID
	 */
	private Long baseId;
	
	/**
	 * 科室ID
	 */
	private Long dptId;
	
	/**
	 * 学员ID
	 */
	private Long stuId;
	
	/**
	 * 用户类型
	 */
	private Integer userType;
	
	/**
	 * 医院ID
	 */
	private Long hospitalId;
	
	/**
	 * 当前查询的天 
	 */
	private Date currentDay;

	public Long getYearId() {
		return yearId;
	}

	public void setYearId(Long yearId) {
		this.yearId = yearId;
	}

	public int getXz() {
		return xz;
	}

	public void setXz(int xz) {
		this.xz = xz;
	}

	public Long getBaseId() {
		return baseId;
	}

	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}

	public Long getDptId() {
		return dptId;
	}

	public void setDptId(Long dptId) {
		this.dptId = dptId;
	}

	public Long getStuId() {
		return stuId;
	}

	public void setStuId(Long stuId) {
		this.stuId = stuId;
	}

	public Integer getUserType() {
		return userType;
	}

	public void setUserType(Integer userType) {
		this.userType = userType;
	}

	public int getMonth() {
		return month;
	}

	public void setMonth(int month) {
		this.month = month;
	}

	public Long getHospitalId() {
		return hospitalId;
	}

	public void setHospitalId(Long hospitalId) {
		this.hospitalId = hospitalId;
	}

	public Date getCurrentDay() {
		return currentDay;
	}

	public void setCurrentDay(Date currentDay) {
		this.currentDay = currentDay;
	}

	public int getYear() {
		return year;
	}

	public void setYear(int year) {
		this.year = year;
	}
}
