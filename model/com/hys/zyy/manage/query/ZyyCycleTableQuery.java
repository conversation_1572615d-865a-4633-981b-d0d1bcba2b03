package com.hys.zyy.manage.query;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.FactoryUtils;
import org.apache.commons.collections.ListUtils;

import com.hys.zyy.manage.model.ZyyBaseObject;
import com.hys.zyy.manage.model.ZyyBaseVO;
import com.hys.zyy.manage.model.ZyyCycleTableDept;
import com.hys.zyy.manage.model.ZyyCycleTableResiCycleVO;
import com.hys.zyy.manage.model.ZyyCycleTableResiTime;
import com.hys.zyy.manage.model.ZyyCycleTableResidency;
import com.hys.zyy.manage.model.ZyyUser;
import com.hys.zyy.manage.model.ZyyUserExtendVO;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：张伟清 2012-04-26
 * 
 * 描述：轮转查询对象
 * 
 * 说明:
 */
@SuppressWarnings("unchecked")
public class ZyyCycleTableQuery  extends ZyyBaseObject  {
	/**
	 * 
	 */
	private static final long serialVersionUID = -4818768622869930297L;

	/*@SuppressWarnings("unchecked")
	public ZyyCycleTableQuery(List<ZyyCycleTableDept> deptList){
		this.deptList = ListUtils.lazyList(
				new ArrayList<ZyyCycleTableDept>(),FactoryUtils.instantiateFactory(ZyyCycleTableDept.class));
	}*/
	
	/**
	 * 基地ID
	 */
	private Long baseId ;
	
	/**
	 * 基地ID 数组
	 */
	private Long[] base_id ;
	
	/**
	 * 年度ID
	 */
	private Long yearId ;
	
	/**
	 * 状态 1.保存 2.提交
	 */
	private Integer status ;
	
	/**
	 * 轮转表ID
	 */
	private Long cycTableId ;
	
	/**
	 * 用户基本信息
	 */
	private ZyyUser zyyUser ;
	
	/**
	 * 学员轮转实际时间 list
	 */
	private List<ZyyCycleTableResiTime> timeList ;
	
	/**
	 * 学员轮转实际时间 map
	 */
	private Map<Integer, ZyyCycleTableResiTime> timeMap ;
	
	/**
	 * 轮转表基地科室
	 * 20130226 chenlaibin edit
	 * if 不这样,form提交过来的数据少了是正常,多了就会报错：Index of out of bounds in property path 'deptList512]';
	 * 这是因为Spring在绑定对象的时候,先从Cache中拿出原来的对象,并且根据现在提供的数据,一次拿出原数据,如果过多,就数组越界异常.
	 */
	private List<ZyyCycleTableDept> deptList = ListUtils.lazyList(
			new ArrayList<ZyyCycleTableDept>(),FactoryUtils.instantiateFactory(ZyyCycleTableDept.class));
	
	/**
	 * 轮转表住院医师
	 */
	private List<ZyyCycleTableResidency> resiList ;
	
	/**
	 * 轮转表住院医师轮转 list
	 */
	private List<ZyyCycleTableResiCycleVO> cycleList ;
	
	/**
	 * 轮转表住院医师轮转 map
	 */
	private Map<Integer, ZyyCycleTableResiCycleVO> cycleMap ;
	
	/**
	 * 住院医师map
	 */
	private Map<Object, List<ZyyUserExtendVO>> extMap ;
	
	/**
	 * 基地列表
	 */
	private List<ZyyBaseVO> baseList ;

	/**
	 * 用户页面传递 轮转信息
	 */
	private String user[] ;
	
	/**
	 * 查询数据
	 */
	private List<Long> list ;
	
	public ZyyCycleTableQuery() {
		super();
	}
	
	public ZyyCycleTableQuery(Long cycTableId, ZyyUser zyyUser) {
		super();
		this.cycTableId = cycTableId;
		this.zyyUser = zyyUser;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public List<ZyyCycleTableResiTime> getTimeList() {
		return timeList;
	}

	public void setTimeList(List<ZyyCycleTableResiTime> timeList) {
		this.timeList = timeList;
	}

	public ZyyUser getZyyUser() {
		return zyyUser;
	}

	public void setZyyUser(ZyyUser zyyUser) {
		this.zyyUser = zyyUser;
	}

	public List<ZyyCycleTableDept> getDeptList() {
		return deptList;
	}

	public void setDeptList(List<ZyyCycleTableDept> deptList) {
		this.deptList = deptList;
	}

	public List<ZyyCycleTableResidency> getResiList() {
		return resiList;
	}

	public void setResiList(List<ZyyCycleTableResidency> resiList) {
		this.resiList = resiList;
	}

	public Long getYearId() {
		return yearId;
	}

	public void setYearId(Long yearId) {
		this.yearId = yearId;
	}

	public Long getBaseId() {
		return baseId;
	}

	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}

	public String[] getUser() {
		return user;
	}

	public void setUser(String[] user) {
		this.user = user;
	}

	public List<ZyyCycleTableResiCycleVO> getCycleList() {
		return cycleList;
	}

	public void setCycleList(List<ZyyCycleTableResiCycleVO> cycleList) {
		this.cycleList = cycleList;
	}

	public Long getCycTableId() {
		return cycTableId;
	}

	public void setCycTableId(Long cycTableId) {
		this.cycTableId = cycTableId;
	}

	public Long[] getBase_id() {
		return base_id;
	}

	public void setBase_id(Long[] base_id) {
		this.base_id = base_id;
	}

	public Map<Integer, ZyyCycleTableResiTime> getTimeMap() {
		return timeMap;
	}

	public void setTimeMap(Map<Integer, ZyyCycleTableResiTime> timeMap) {
		this.timeMap = timeMap;
	}

	public Map<Integer, ZyyCycleTableResiCycleVO> getCycleMap() {
		return cycleMap;
	}

	public void setCycleMap(Map<Integer, ZyyCycleTableResiCycleVO> cycleMap) {
		this.cycleMap = cycleMap;
	}

	public List<Long> getList() {
		return list;
	}

	public void setList(List<Long> list) {
		this.list = list;
	}

	public List<ZyyBaseVO> getBaseList() {
		return baseList;
	}

	public void setBaseList(List<ZyyBaseVO> baseList) {
		this.baseList = baseList;
	}

	public Map<Object, List<ZyyUserExtendVO>> getExtMap() {
		return extMap;
	}

	public void setExtMap(Map<Object, List<ZyyUserExtendVO>> extMap) {
		this.extMap = extMap;
	}
}