package com.hys.zyy.manage.query;

import com.hys.zyy.manage.model.ZyyBaseObject;

/**
 * 科室审核手册的查询封装类
 * <AUTHOR>
 * @date 2019-5-6下午4:16:30
 */
public class ZyyDeptCheckHandbookQuery extends ZyyBaseObject  {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	//用户id
	private Long residencyId;
	//科室id
	private Long deptId;
	//标准科室id
	private Long deptStdId;
	//科室手册的id
	private Long cycleId;
	//开始日期
	private String startDate;
	//结束日期
	private String endDate;
	
	public Long getResidencyId() {
		return residencyId;
	}
	public void setResidencyId(Long residencyId) {
		this.residencyId = residencyId;
	}
	public Long getDeptId() {
		return deptId;
	}
	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}
	public Long getDeptStdId() {
		return deptStdId;
	}
	public void setDeptStdId(Long deptStdId) {
		this.deptStdId = deptStdId;
	}
	public Long getCycleId() {
		return cycleId;
	}
	public void setCycleId(Long cycleId) {
		this.cycleId = cycleId;
	}
	public String getStartDate() {
		return startDate;
	}
	public void setStartDate(String startDate) {
		this.startDate = startDate;
	}
	public String getEndDate() {
		return endDate;
	}
	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}
}
