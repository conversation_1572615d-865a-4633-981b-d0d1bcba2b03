package com.hys.zyy.manage.query;

import com.hys.zyy.manage.model.ZyyBaseObject;

//优先轮转科室表
public class ZyyDeptCyclePriorityQuery extends ZyyBaseObject  {

	/**
	 * 
	 */
	private static final long serialVersionUID = -5189360083706246020L;

	/**
	 * 医院ID
	 */
	private Long hospitalId; 
	
	/**
	 * 学制
	 */
	private Integer educationSystem;
	
	/**
	 * baseId 
	 */
	private Long zyyBaseId;
	
	/**
	 * 2013-6-20 xusq 
	 * 区分是优先还是滞后 1 优先 2 滞后
	 */
	private int type;

	public Long getHospitalId() {
		return hospitalId;
	}

	public void setHospitalId(Long hospitalId) {
		this.hospitalId = hospitalId;
	}

	public Integer getEducationSystem() {
		return educationSystem;
	}

	public void setEducationSystem(Integer educationSystem) {
		this.educationSystem = educationSystem;
	}
	
	public Long getZyyBaseId() {
		return zyyBaseId;
	}

	public void setZyyBaseId(Long zyyBaseId) {
		this.zyyBaseId = zyyBaseId;
	}

	public int getType() {
		return type;
	}

	public void setType(int type) {
		this.type = type;
	}
}
