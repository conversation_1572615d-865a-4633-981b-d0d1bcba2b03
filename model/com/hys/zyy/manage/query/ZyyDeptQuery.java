package com.hys.zyy.manage.query;

import java.util.List;

import com.hys.zyy.manage.model.ZyyBaseObject;
import com.hys.zyy.manage.model.ZyyDeptCycleTime;
import com.hys.zyy.manage.model.ZyyDeptExt;
import com.hys.zyy.manage.model.ZyyDeptStdDept;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：张伟清 2012-04-13
 * 
 * 描述：科室查询对象
 * 
 * 说明:
 */
public class ZyyDeptQuery extends ZyyBaseObject  {

	/**
	 * 
	 */
	private static final long serialVersionUID = -3265991073128065714L;

	/**
	 * 基地ID
	 */
	private Long baseId;

	/**
	 * 学制
	 */
	private Integer educationSystem ;
	
	/**
	 * 提交状态
	 */
	private Integer status ;
	
	/**
	 * 基地轮转科室
	 */
	private Long[] deptIds;
	
	/**
	 * 科室扩展
	 */
	private List<ZyyDeptExt> extList ;
	
	/**
	 * 科室关联列表
	 */
	private List<ZyyDeptStdDept> deptList ;
	
	/**
	 * 轮转科室时间表
	 */
	private List<ZyyDeptCycleTime> timeList ;

	public Long getBaseId() {
		return baseId;
	}

	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}

	public Long[] getDeptIds() {
		return deptIds;
	}

	public void setDeptIds(Long[] deptIds) {
		this.deptIds = deptIds;
	}

	public List<ZyyDeptExt> getExtList() {
		return extList;
	}

	public void setExtList(List<ZyyDeptExt> extList) {
		this.extList = extList;
	}

	public List<ZyyDeptStdDept> getDeptList() {
		return deptList;
	}

	public void setDeptList(List<ZyyDeptStdDept> deptList) {
		this.deptList = deptList;
	}

	public Integer getEducationSystem() {
		return educationSystem;
	}

	public void setEducationSystem(Integer educationSystem) {
		this.educationSystem = educationSystem;
	}

	public List<ZyyDeptCycleTime> getTimeList() {
		return timeList;
	}

	public void setTimeList(List<ZyyDeptCycleTime> timeList) {
		this.timeList = timeList;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}
}
