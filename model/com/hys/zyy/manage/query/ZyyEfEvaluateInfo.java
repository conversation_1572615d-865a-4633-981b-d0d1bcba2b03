package com.hys.zyy.manage.query;

import java.util.Date;

import com.hys.zyy.manage.model.ZyyBaseObject;


/**
 * 评价信息
 * <AUTHOR>
 *
 */
public class ZyyEfEvaluateInfo extends ZyyBaseObject  {
	 
	/**
	 * 
	 */
	private static final long serialVersionUID = 3718544377972611035L;

	private Long instanceId;
	
	private String realName;
	
	private String userName;
	
	private Long userId;
	
	private Long zyyUserType;
	
	private Long formId;
	
	private String formName;
	
	private String formCreateUser;
	
	private Date createTime;
	
	private Long status;
	
	private Date startDate;
	
	private Date endDate;
	
	private long unfinishCount;
	
	public Long getInstanceId() {
		return instanceId;
	}

	public void setInstanceId(Long instanceId) {
		this.instanceId = instanceId;
	}

	public String getRealName() {
		return realName;
	}

	public void setRealName(String realName) {
		this.realName = realName;
	}

	public Long getZyyUserType() {
		return zyyUserType;
	}

	public void setZyyUserType(Long zyyUserType) {
		this.zyyUserType = zyyUserType;
	}

	public String getFormName() {
		return formName;
	}

	public void setFormName(String formName) {
		this.formName = formName;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Long getStatus() {
		return status;
	}

	public void setStatus(Long status) {
		this.status = status;
	}

	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public Long getFormId() {
		return formId;
	}

	public void setFormId(Long formId) {
		this.formId = formId;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public long getUnfinishCount() {
		return unfinishCount;
	}

	public void setUnfinishCount(long unfinishCount) {
		this.unfinishCount = unfinishCount;
	}

	public String getFormCreateUser() {
		return formCreateUser;
	}

	public void setFormCreateUser(String formCreateUser) {
		this.formCreateUser = formCreateUser;
	}

}
