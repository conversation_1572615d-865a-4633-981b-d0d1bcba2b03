package com.hys.zyy.manage.query;

import java.util.List;
import java.util.Map;

import com.hys.zyy.manage.model.ZyyBaseObject;
import com.hys.zyy.manage.model.ZyyDept;
import com.hys.zyy.manage.model.ZyyEfCatalog;

/**
 * 评价选择成员
 * <AUTHOR>
 *
 */
public class ZyyEfMembers extends ZyyBaseObject {
	 
	/**
	 * 
	 */
	private static final long serialVersionUID = -403822305944984696L;
	private long userid;
	private String realName;
	private String aliasName;
	private String orgName;
	private String deptName;
	private Integer zyyUserStatus;
	private long hospitalId;
	private String baseName;
	private String baseAliasName;
	private long zyyUserType;
	private String userPost;
	private String email;
	private String telphone;
	private Long zyyDeptId;
	private Long deptLevel;
	private String accountName;
	//人员所在的科室 1级科室 2级科室等
	private Map<String ,ZyyDept> zyyDepts;
	
	public long getUserid() {
		return userid;
	}
	public void setUserid(long userid) {
		this.userid = userid;
	}
	public String getRealName() {
		return realName;
	}
	public void setRealName(String realName) {
		this.realName = realName;
	}
	public long getHospitalId() {
		return hospitalId;
	}
	public void setHospitalId(long hospitalId) {
		this.hospitalId = hospitalId;
	}
	public String getBaseName() {
		return baseName;
	}
	public void setBaseName(String baseName) {
		this.baseName = baseName;
	}
	public String getBaseAliasName() {
		return baseAliasName;
	}
	public void setBaseAliasName(String baseAliasName) {
		this.baseAliasName = baseAliasName;
	}
	public long getZyyUserType() {
		return zyyUserType;
	}
	public void setZyyUserType(long zyyUserType) {
		this.zyyUserType = zyyUserType;
	}
	public void setAliasName(String aliasName) {
		this.aliasName = aliasName;
	}
	public String getAliasName() {
		return aliasName;
	}
	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}
	public String getOrgName() {
		return orgName;
	}
	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}
	public String getDeptName() {
		return deptName;
	}
	public void setZyyUserStatus(Integer zyyUserStatus) {
		this.zyyUserStatus = zyyUserStatus;
	}
	public Integer getZyyUserStatus() {
		return zyyUserStatus;
	}
	public void setUserPost(String userPost) {
		this.userPost = userPost;
	}
	public String getUserPost() {
		return userPost;
	}
	public void setEmail(String email) {
		this.email = email;
	}
	public String getEmail() {
		return email;
	}
	public void setTelphone(String telphone) {
		this.telphone = telphone;
	}
	public String getTelphone() {
		return telphone;
	}
	public void setZyyDeptId(Long zyyDeptId) {
		this.zyyDeptId = zyyDeptId;
	}
	public Long getZyyDeptId() {
		return zyyDeptId;
	}
	public void setAccountName(String accountName) {
		this.accountName = accountName;
	}
	public String getAccountName() {
		return accountName;
	}
	public void setZyyDepts(Map<String ,ZyyDept> zyyDepts) {
		this.zyyDepts = zyyDepts;
	}
	public Map<String ,ZyyDept> getZyyDepts() {
		return zyyDepts;
	}
	public void setDeptLevel(Long deptLevel) {
		this.deptLevel = deptLevel;
	}
	public Long getDeptLevel() {
		return deptLevel;
	}
}
