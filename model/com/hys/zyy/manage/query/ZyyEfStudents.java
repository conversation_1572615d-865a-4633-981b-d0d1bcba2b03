package com.hys.zyy.manage.query;

import com.hys.zyy.manage.model.ZyyBaseObject;

/**
 * 被评价的学员
 * <AUTHOR>
 *
 */
public class ZyyEfStudents extends ZyyBaseObject  {
	 
	/**
	 * 
	 */
	private static final long serialVersionUID = 912733476544175836L;
	private long userid;
	private String realName;
	private Integer sex ;
	private String jobNumber;
	private Integer highestDegree;
	private String aliasName;
	private String year;
	private Integer schoolSystem;
	private String orgName;
	private String deptName;
	private Integer zyyUserStatus;
	private long hospitalId;
	private String baseName;
	private String baseAliasName;
	private long zyyUserTYpe;
	private String accountName;
	/**
	 *教学活动计划中页面是否可选 1可选   0不可选
	 */
	private String isSelect;
	
	private Long deptId;
	
	private Integer[] zyyUserStatusArr;
	private boolean edit = false; 
	
	public Long getDeptId() {
		return deptId;
	}
	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}
	public String getIsSelect() {
		return isSelect;
	}
	public void setIsSelect(String isSelect) {
		this.isSelect = isSelect;
	}
	public long getUserid() {
		return userid;
	}
	public void setUserid(long userid) {
		this.userid = userid;
	}
	public String getRealName() {
		return realName;
	}
	public void setRealName(String realName) {
		this.realName = realName == null ? null : realName.trim();
	}
	public String getYear() {
		return year;
	}
	public void setYear(String year) {
		this.year = year;
	}
	public long getHospitalId() {
		return hospitalId;
	}
	public void setHospitalId(long hospitalId) {
		this.hospitalId = hospitalId;
	}
	public String getBaseName() {
		return baseName;
	}
	public void setBaseName(String baseName) {
		this.baseName = baseName;
	}
	public String getBaseAliasName() {
		return baseAliasName;
	}
	public void setBaseAliasName(String baseAliasName) {
		this.baseAliasName = baseAliasName;
	}
	public long getZyyUserTYpe() {
		return zyyUserTYpe;
	}
	public void setZyyUserTYpe(long zyyUserTYpe) {
		this.zyyUserTYpe = zyyUserTYpe;
	}
	public void setSex(Integer sex) {
		this.sex = sex;
	}
	public Integer getSex() {
		return sex;
	}
	public void setJobNumber(String jobNumber) {
		this.jobNumber = jobNumber;
	}
	public String getJobNumber() {
		return jobNumber;
	}
	public void setHighestDegree(Integer highestDegree) {
		this.highestDegree = highestDegree;
	}
	public Integer getHighestDegree() {
		return highestDegree;
	}
	public void setAliasName(String aliasName) {
		this.aliasName = aliasName;
	}
	public String getAliasName() {
		return aliasName;
	}
	public void setSchoolSystem(Integer schoolSystem) {
		this.schoolSystem = schoolSystem;
	}
	public Integer getSchoolSystem() {
		return schoolSystem;
	}
	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}
	public String getOrgName() {
		return orgName;
	}
	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}
	public String getDeptName() {
		return deptName;
	}
	public void setZyyUserStatus(Integer zyyUserStatus) {
		this.zyyUserStatus = zyyUserStatus;
	}
	public Integer getZyyUserStatus() {
		return zyyUserStatus;
	}
	public void setAccountName(String accountName) {
		this.accountName = accountName;
	}
	public String getAccountName() {
		return accountName;
	}
	public Integer[] getZyyUserStatusArr() {
		return zyyUserStatusArr;
	}
	public void setZyyUserStatusArr(Integer[] zyyUserStatusArr) {
		this.zyyUserStatusArr = zyyUserStatusArr;
	}
	public boolean isEdit() {
		return edit;
	}
	public void setEdit(boolean edit) {
		this.edit = edit;
	}
	
}
