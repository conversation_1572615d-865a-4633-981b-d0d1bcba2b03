package com.hys.zyy.manage.query;

import java.util.Date;
import java.util.List;

import com.hys.zyy.manage.model.ZyyBaseObject;
import com.hys.zyy.manage.model.ZyyUserExtendVO;


/**
 * 评价体系用的查询pojo
 * 
 */
public class ZyyEvaluateQuery extends ZyyBaseObject{

	private static final long serialVersionUID = 1L;
	
	/**
	 * 学员的id
	 */
	private Long residencyId;
	/**
	 * 评价状态
	 */
	private Integer evalStatus;
	/**
	 * 学年id
	 */
	private Long yearId;
	/**
	 * 专业
	 */
	private Long baseId;
	/**
	 * 轮转状态
	 */
	private Integer cycleStatus;
	/**
	 * 科室
	 */
	private Long deptId;
	/**
	 * 姓名
	 */
	private String name;
	/**
	 * 工号
	 */
	private String jobNumber;
	/**
	 * 身份证号
	 */
	private String certifiNo;
	/**
	 * 带教的id
	 */
	private Long teacherId;
	/**
	 * 学员评价表中的评价者的id
	 */
	private Long userId;
	
	private Long createOrg;
	/**
	 * 评价表类型
	 */
	private Integer tableType;
	/**
	 * 带教姓名
	 */
	private String teacherName;
	/**
	 * 带教结束时间1  轮转结束时间1
	 */
	private Date endDate1;
	
	private Long orgId;
	
	private Integer schoolSystem;
	
	private Integer residencySource;
	
	private String realName;
	
	private String certificateNo;
	
	private String mobilNumber;
	
	private Long userEvaluateId;
	
	private String startEvaluateDate;
	
	private String endEvaluateDate;
	
	private Integer userType;
	
	private Integer userPostTitle;
	
	private Long tableId;
	
	public Long getTableId() {
		return tableId;
	}
	public void setTableId(Long tableId) {
		this.tableId = tableId;
	}
	public Integer getUserPostTitle() {
		return userPostTitle;
	}
	public void setUserPostTitle(Integer userPostTitle) {
		this.userPostTitle = userPostTitle;
	}
	public Integer getUserType() {
		return userType;
	}
	public void setUserType(Integer userType) {
		this.userType = userType;
	}
	public String getStartEvaluateDate() {
		return startEvaluateDate;
	}
	public void setStartEvaluateDate(String startEvaluateDate) {
		this.startEvaluateDate = startEvaluateDate;
	}
	public String getEndEvaluateDate() {
		return endEvaluateDate;
	}
	public void setEndEvaluateDate(String endEvaluateDate) {
		this.endEvaluateDate = endEvaluateDate;
	}
	public Long getUserEvaluateId() {
		return userEvaluateId;
	}
	public void setUserEvaluateId(Long userEvaluateId) {
		this.userEvaluateId = userEvaluateId;
	}
	public String getCertificateNo() {
		return certificateNo;
	}
	public void setCertificateNo(String certificateNo) {
		this.certificateNo = certificateNo;
	}
	public String getMobilNumber() {
		return mobilNumber;
	}
	public void setMobilNumber(String mobilNumber) {
		this.mobilNumber = mobilNumber;
	}
	public String getRealName() {
		return realName;
	}
	public void setRealName(String realName) {
		this.realName = realName;
	}
	public Integer getResidencySource() {
		return residencySource;
	}
	public void setResidencySource(Integer residencySource) {
		this.residencySource = residencySource;
	}
	public Integer getSchoolSystem() {
		return schoolSystem;
	}
	public void setSchoolSystem(Integer schoolSystem) {
		this.schoolSystem = schoolSystem;
	}
	public Long getOrgId() {
		return orgId;
	}
	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}
	public Date getEndDate1() {
		return endDate1;
	}
	public void setEndDate1(Date endDate1) {
		this.endDate1 = endDate1;
	}
	public Date getEndDate2() {
		return endDate2;
	}
	public void setEndDate2(Date endDate2) {
		this.endDate2 = endDate2;
	}
	public Integer getTeachStatus() {
		return teachStatus;
	}
	public void setTeachStatus(Integer teachStatus) {
		this.teachStatus = teachStatus;
	}
	public String getTableName() {
		return tableName;
	}
	public void setTableName(String tableName) {
		this.tableName = tableName;
	}
	/**
	 * 带教结束时间2  轮转结束时间2
	 */
	private Date endDate2;
	/**
	 * 带教状态  1-正在带教   2-带教结束
	 */
	private Integer teachStatus;
	/**
	 * 评价表名称
	 */
	private String tableName;
	
	public String getTeacherName() {
		return teacherName;
	}
	public void setTeacherName(String teacherName) {
		this.teacherName = teacherName;
	}
	public Integer getTableType() {
		return tableType;
	}
	public void setTableType(Integer tableType) {
		this.tableType = tableType;
	}
	public Long getCreateOrg() {
		return createOrg;
	}
	public void setCreateOrg(Long createOrg) {
		this.createOrg = createOrg;
	}
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	public Long getTeacherId() {
		return teacherId;
	}
	public void setTeacherId(Long teacherId) {
		this.teacherId = teacherId;
	}
	public Long getResidencyId() {
		return residencyId;
	}
	public void setResidencyId(Long residencyId) {
		this.residencyId = residencyId;
	}
	public Integer getEvalStatus() {
		return evalStatus;
	}
	public void setEvalStatus(Integer evalStatus) {
		this.evalStatus = evalStatus;
	}
	public Long getYearId() {
		return yearId;
	}
	public void setYearId(Long yearId) {
		this.yearId = yearId;
	}
	public Long getBaseId() {
		return baseId;
	}
	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}
	public Integer getCycleStatus() {
		return cycleStatus;
	}
	public void setCycleStatus(Integer cycleStatus) {
		this.cycleStatus = cycleStatus;
	}
	public Long getDeptId() {
		return deptId;
	}
	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getJobNumber() {
		return jobNumber;
	}
	public void setJobNumber(String jobNumber) {
		this.jobNumber = jobNumber;
	}
	public String getCertifiNo() {
		return certifiNo;
	}
	public void setCertifiNo(String certifiNo) {
		this.certifiNo = certifiNo;
	}
	
	
	
}