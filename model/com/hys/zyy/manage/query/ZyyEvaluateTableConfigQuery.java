package com.hys.zyy.manage.query;

import com.hys.zyy.manage.model.ZyyBaseObject;
/**
 * 评价表配置的查询类
 * <AUTHOR>
 * @date 2018-8-20下午5:03:23
 */
public class ZyyEvaluateTableConfigQuery extends ZyyBaseObject{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	//id
	private Long id;
	
	//	评价表类型	 1 对评价学员 2 对带教评价 3 对责任导师评价 4 对科室评价 5 对专业基地评价 6 对培训基地评价
	private Integer tableType ;	
	//	评价者类型		1带教老师 2 科室 3责任导师 4专业基地 5培训基地 6患者 7护士 8 住院医师
	private Integer evaluateType ;
	
	//	评价表状态	1 开启 2 关闭
	private Integer status ;
	
	//是否有模板
	private boolean haveTemplet;
	
	//	评价表名称	
	private String tableName	;
	
	

	public boolean isHaveTemplet() {
		return haveTemplet;
	}

	public void setHaveTemplet(boolean haveTemplet) {
		this.haveTemplet = haveTemplet;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Integer getTableType() {
		return tableType;
	}

	public void setTableType(Integer tableType) {
		this.tableType = tableType;
	}

	public Integer getEvaluateType() {
		return evaluateType;
	}

	public void setEvaluateType(Integer evaluateType) {
		this.evaluateType = evaluateType;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public String getTableName() {
		return tableName;
	}

	public void setTableName(String tableName) {
		this.tableName = tableName;
	}
}
