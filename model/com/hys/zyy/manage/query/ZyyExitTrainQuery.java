package com.hys.zyy.manage.query;

import com.hys.zyy.manage.model.ZyyBaseObject;

public class ZyyExitTrainQuery extends ZyyBaseObject{
	
	private Long provinceId;
	
	private Long hospitalId;
	
	private Long baseId;
	
	private Long yearId;
	
	private Integer residencySource;
	
	private String name;
	
	private String certificateNo;
	
	private Integer baseCheckStatus;
	
	private Integer provinceCheckStatus;
	
	private Integer blackStatus;
	
	private Integer directStu;//是否为订单定向生 1是0否
	
	public Integer getBaseCheckStatus() {
		return baseCheckStatus;
	}

	public void setBaseCheckStatus(Integer baseCheckStatus) {
		this.baseCheckStatus = baseCheckStatus;
	}

	public Integer getProvinceCheckStatus() {
		return provinceCheckStatus;
	}

	public void setProvinceCheckStatus(Integer provinceCheckStatus) {
		this.provinceCheckStatus = provinceCheckStatus;
	}

	public Integer getBlackStatus() {
		return blackStatus;
	}

	public void setBlackStatus(Integer blackStatus) {
		this.blackStatus = blackStatus;
	}

	public Long getProvinceId() {
		return provinceId;
	}

	public void setProvinceId(Long provinceId) {
		this.provinceId = provinceId;
	}

	public Long getHospitalId() {
		return hospitalId;
	}

	public void setHospitalId(Long hospitalId) {
		this.hospitalId = hospitalId;
	}

	public Long getBaseId() {
		return baseId;
	}

	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}

	public Long getYearId() {
		return yearId;
	}

	public void setYearId(Long yearId) {
		this.yearId = yearId;
	}

	public Integer getResidencySource() {
		return residencySource;
	}

	public void setResidencySource(Integer residencySource) {
		this.residencySource = residencySource;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getCertificateNo() {
		return certificateNo;
	}

	public void setCertificateNo(String certificateNo) {
		this.certificateNo = certificateNo;
	}

	public Integer getDirectStu() {
		return directStu;
	}

	public void setDirectStu(Integer directStu) {
		this.directStu = directStu;
	}
	
	
}
