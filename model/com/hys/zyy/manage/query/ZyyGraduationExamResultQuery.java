package com.hys.zyy.manage.query;

import com.hys.zyy.manage.model.ZyyBase;

public class ZyyGraduationExamResultQuery extends ZyyBase {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	
	private Long provinceId;
	/**
	 * 考试批次（名称）
	 */
	private Long examId;
	
	/**
	 * 理论考核结果  1:通过;2不通过
	 */
	private Integer theoryExamResult;
	
	/**
	 * 技能考核结果 1:通过;2不通过
	 */
	private Integer skillExamResult;
	
	/**
	 * 结业考试综合结果 1:通过;2不通过
	 */
	private Integer examResult;
	
	/**
	 * 培训过程考核是否合格 1:通过;2不通过
	 */
	private Integer trainProcessResult;
	
	/**
	 * 培训基地
	 */
	private Long orgId;
	
	/**
	 * 年级
	 */
	private Long yearId;
	
	/**
	 * 专业
	 */
	private Long baseId;
	
	/**
	 * 姓名
	 */
	private String realName;
	
	/**
	 * 身份证号
	 */
	private String certificateNo;
	
	/**
	 * 技能考站考核结果  1通过2不通过
	 */
	private Integer skillStationExamResult;
	/**
	 * 技能考站考核内容
	 */
	private String examineContent;
	
	////////
	/**
	 * 结业管理中的导入证书信息用
	 * 是否获得证书1：是；2：否    0 全部
	 */
	private Integer hasCertificate;
	
	public Integer getHasCertificate() {
		return hasCertificate;
	}

	public void setHasCertificate(Integer hasCertificate) {
		this.hasCertificate = hasCertificate;
	}

	public String getExamineContent() {
		return examineContent;
	}

	public void setExamineContent(String examineContent) {
		this.examineContent = examineContent;
	}

	public Integer getSkillStationExamResult() {
		return skillStationExamResult;
	}

	public void setSkillStationExamResult(Integer skillStationExamResult) {
		this.skillStationExamResult = skillStationExamResult;
	}

	public Long getProvinceId() {
		return provinceId;
	}

	public void setProvinceId(Long provinceId) {
		this.provinceId = provinceId;
	}

	public Long getExamId() {
		return examId;
	}

	public void setExamId(Long examId) {
		this.examId = examId;
	}

	public Integer getTheoryExamResult() {
		return theoryExamResult;
	}

	public void setTheoryExamResult(Integer theoryExamResult) {
		this.theoryExamResult = theoryExamResult;
	}

	public Integer getSkillExamResult() {
		return skillExamResult;
	}

	public void setSkillExamResult(Integer skillExamResult) {
		this.skillExamResult = skillExamResult;
	}

	public Integer getExamResult() {
		return examResult;
	}

	public void setExamResult(Integer examResult) {
		this.examResult = examResult;
	}

	public Integer getTrainProcessResult() {
		return trainProcessResult;
	}

	public void setTrainProcessResult(Integer trainProcessResult) {
		this.trainProcessResult = trainProcessResult;
	}

	public Long getOrgId() {
		return orgId;
	}

	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}

	public Long getYearId() {
		return yearId;
	}

	public void setYearId(Long yearId) {
		this.yearId = yearId;
	}

	public Long getBaseId() {
		return baseId;
	}

	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}

	public String getRealName() {
		return realName;
	}

	public void setRealName(String realName) {
		this.realName = realName;
	}

	public String getCertificateNo() {
		return certificateNo;
	}

	public void setCertificateNo(String certificateNo) {
		this.certificateNo = certificateNo;
	}
		
}
