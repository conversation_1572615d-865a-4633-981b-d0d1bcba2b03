package com.hys.zyy.manage.query;

import com.hys.zyy.manage.page.PageQuery;

/**
 * 教员查询
 * <AUTHOR>
 */
public class ZyyInstructorQuery extends PageQuery {
	private static final long serialVersionUID = 4590111871200133075L;

	private Long yearId;			// 年份
	
	private Long hospitalId;		// 医院ID
	
	private String baseId;			// 基地ID			多个用逗号隔开
	
	private String deptId;			// 科室ID			多个用逗号隔开
	
	private String userName;		// 姓名 
	
	private String identityCard;	// 身份证
	
	private Long paramType = 2l;	// 按学科，按科室，按人名身份证
	/*
	 * 微信号
	 */
	private String wechatNumber;
	/*
	 * 教师资格证书号
	 */
	private String teacQualCerNum;
	/*
	 * 教学职称
	 */
	private String teachingTitle;
	
	private String certificateNo;
	
	private String mobilNumber;
	
	/**
	 * 职称
	 * 1 主任医师
	 * 2 副主任医师
	 * 3主治医师
	 * 4医师
	 */
	private Integer title;
	private Integer userCategory;
	
	private Long zyyUserProvinceId;
	
	// 目前状态   1 带教    2  非带教
	private Integer status;
	//开始时间
	private String startDateStr;
	//结束时间
	private String endDateStr;
	
	private String accountName;
	
	private Long selUserId;//已选用户
	
	public Long getSelUserId() {
		return selUserId;
	}

	public void setSelUserId(Long selUserId) {
		this.selUserId = selUserId;
	}

	public Long getZyyUserProvinceId() {
		return zyyUserProvinceId;
	}

	public void setZyyUserProvinceId(Long zyyUserProvinceId) {
		this.zyyUserProvinceId = zyyUserProvinceId;
	}

	public String getMobilNumber() {
		return mobilNumber;
	}

	public void setMobilNumber(String mobilNumber) {
		this.mobilNumber = mobilNumber;
	}

	public Integer getTitle() {
		return title;
	}

	public void setTitle(Integer title) {
		this.title = title;
	}

	public Integer getUserCategory() {
		return userCategory;
	}

	public void setUserCategory(Integer userCategory) {
		this.userCategory = userCategory;
	}

	public String getCertificateNo() {
		return certificateNo;
	}

	public void setCertificateNo(String certificateNo) {
		this.certificateNo = certificateNo;
	}

	public Long getHospitalId() {
		return hospitalId;
	}

	public void setHospitalId(Long hospitalId) {
		this.hospitalId = hospitalId;
	}

	public Long getYearId() {
		return yearId;
	}

	public void setYearId(Long yearId) {
		this.yearId = yearId;
	}

	public String getBaseId() {
		return baseId;
	}

	public void setBaseId(String baseId) {
		this.baseId = baseId;
	}

	public String getDeptId() {
		return deptId;
	}

	public void setDeptId(String deptId) {
		this.deptId = deptId;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName == null ? null : userName.trim();
	}

	public String getIdentityCard() {
		return identityCard;
	}

	public void setIdentityCard(String identityCard) {
		this.identityCard = identityCard;
	}

	public Long getParamType() {
		return paramType;
	}

	public void setParamType(Long paramType) {
		this.paramType = paramType;
	}

	public String getWechatNumber() {
		return wechatNumber;
	}

	public void setWechatNumber(String wechatNumber) {
		this.wechatNumber = wechatNumber == null ? null : wechatNumber.trim();
	}

	public String getTeacQualCerNum() {
		return teacQualCerNum;
	}

	public void setTeacQualCerNum(String teacQualCerNum) {
		this.teacQualCerNum = teacQualCerNum == null ? null : teacQualCerNum.trim();
	}

	public String getTeachingTitle() {
		return teachingTitle;
	}

	public void setTeachingTitle(String teachingTitle) {
		this.teachingTitle = teachingTitle == null ? null : teachingTitle.trim();
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public String getStartDateStr() {
		return startDateStr;
	}

	public void setStartDateStr(String startDateStr) {
		this.startDateStr = startDateStr;
	}

	public String getEndDateStr() {
		return endDateStr;
	}

	public void setEndDateStr(String endDateStr) {
		this.endDateStr = endDateStr;
	}

	public String getAccountName() {
		return accountName;
	}

	public void setAccountName(String accountName) {
		this.accountName = accountName;
	}
}
