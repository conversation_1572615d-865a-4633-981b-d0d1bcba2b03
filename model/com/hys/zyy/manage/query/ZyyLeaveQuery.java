package com.hys.zyy.manage.query;

import java.util.Date;

import com.hys.zyy.manage.model.ZyyBaseObject;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：张伟清 2012-05-04
 * 
 * 描述：
 * 
 * 说明:
 */
public class ZyyLeaveQuery extends ZyyBaseObject  {

	/**
	 * 
	 */
	private static final long serialVersionUID = 3293981326130100590L;

	/**
	 * 年度ID
	 */
	private Long yearId ;
	
	/**
	 * 基地ID
	 */
	private Long baseId ;
	
	/**
	 * 组织机构ID
	 */
	private Long zyyUserOrgId ;
	
	/**
	 * 月份
	 */
	private String month ;
	
	/**
	 * 用户类别
	 */
	private Integer zyyUserType;
	
	/**
	 * 
	 *医院人员ID 
	 * 
	 */
	private Long userId;
	
	/**
	 *科室ID 
	 */
	private Long deptId;
	
	/**
	 * 当前日期 
	 */
	private Date dqrq;
	
	/**
	 *	医院id 
	 */
	private Long hospitalId;

	public Long getHospitalId() {
		return hospitalId;
	}

	public void setHospitalId(Long hospitalId) {
		this.hospitalId = hospitalId;
	}

	public Date getDqrq() {
		return dqrq;
	}

	public void setDqrq(Date dqrq) {
		this.dqrq = dqrq;
	}

	public Long getDeptId() {
		return deptId;
	}

	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public Integer getZyyUserType() {
		return zyyUserType;
	}

	public void setZyyUserType(Integer zyyUserType) {
		this.zyyUserType = zyyUserType;
	}

	public Long getYearId() {
		return yearId;
	}

	public void setYearId(Long yearId) {
		this.yearId = yearId;
	}

	public Long getBaseId() {
		return baseId;
	}

	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}

	public Long getZyyUserOrgId() {
		return zyyUserOrgId;
	}

	public void setZyyUserOrgId(Long zyyUserOrgId) {
		this.zyyUserOrgId = zyyUserOrgId;
	}

	public String getMonth() {
		return month;
	}

	public void setMonth(String month) {
		this.month = month;
	}
}
