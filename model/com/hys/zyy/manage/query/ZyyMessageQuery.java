package com.hys.zyy.manage.query;

import java.util.Date;

import com.hys.zyy.manage.model.ZyyBaseObject;

/**
 * 
 * 标题：zyy
 * 
 * 作者：Tony Apr 5, 2012
 * 
 * 描述：
 * 
 * 说明:
 */
public class ZyyMessageQuery extends ZyyBaseObject  {
	
	
	/**
	 * 
	 */
	private static final long serialVersionUID = -1909808600206158010L;

	private Long id;
	
	/**
	 * 通知时间
	 */
	private Date messageDate;
	
	/**
	 * 通知创建时间
	 */
	private Date messageCreateDate;
	
	/**
	 * 收信人id
	 */
	private Long addresseeId;
	
	/**
	 * 通知标题
	 */
	private String messageTitle;
	
	/**
	 * 通知内容
	 */
	private String messageContent;	
	
	/**
	 * 通知类别ID
	 */
	private Long messageTypeId;
	
	
	/**
	 * 是否置顶
	 * 1：是
	 * 0：否
	 */
	private Integer isOntop = 0;
	
	/**
	 * 是否加新
	 * 1：是
	 * 0：否
	 */
	private Integer isNew = 0;
	
	/**
	 * 通知级别
	 * 1 -重要 2  -一般
	 */
	private Integer messageLevel = 1;
	
	
	/**
	 * 通知对象
	 * 20：报名学员
	 * 21：正式学员
	 * 11：带教老师
	 * 9：科室负责人
	 * 7：基地管理者
	 * 5：医院管理者
	 */
	private Integer[] messageObj;
	
	
	/**
	 * 通知地址 首页
	 * 1：发送 
	 * 0：不发送
	 */
	private Integer messageToIndex;
	
	/**
	 * 通知地址 通知栏
	 * 1：发送 
	 * 0：不发送
	 */
	private Integer messageToInner;
	
	/**
	 * 通知地址 邮箱
	 * 1：发送 
	 * 0：不发送
	 */
	private Integer messageToEmail;
	
	/**
	 * 通知地址 短信
	 * 1：发送 
	 * 0：不发送
	 */
	private Integer messageToSms;
	
	
	/**
	 * 发送者
	 */
	private Long messageSender;
	
	/**
	 * 通知类别
	 * 1 -通知 2 -草稿
	 */
	private Integer messageType;
	
	/**
	 * 置顶天数
	 */
	private Integer ontopDays = 0;
	
	
	/**
	 * 组织机构ID
	 */
	private Long zyyOrgId;
	
	/**
	 * 通知栏目ID(网站首页)
	 */
	private Long messageCategoryId;
	
	/**
	 * 通知栏目名称（网站首页）
	 */
	private String messageCategoryName;
	
	/**
	 * 是否全员通知
	 * 1：是
	 * 0：否
	 */
	private Integer isFull = 0;
	
	
	private String startTime;
	private String endTime;
	private String findDate;
	private String users;
	
	private Integer[] willStatus;
	/**
	 * files
	 */
	private Integer[] files;
	
	//科室的首页新版  lzq
	//分页的大小
	private Integer pageSize;
	//第几页
	private Integer pageOffset;
	//1 入科  2 过程 3 出科 科室跳转页面的类型
	private Integer departType;
	//当前年
	private Integer currentYear;
	//当前月
	private Integer currentMonth;

	public Integer getIsFull() {
		return isFull;
	}

	public void setIsFull(Integer isFull) {
		this.isFull = isFull;
	}

	public Long getMessageCategoryId() {
		return messageCategoryId;
	}

	public void setMessageCategoryId(Long messageCategoryId) {
		this.messageCategoryId = messageCategoryId;
	}

	public Integer getMessageType() {
		return messageType;
	}

	public void setMessageType(Integer messageType) {
		this.messageType = messageType;
	}

	public Integer getOntopDays() {
		return ontopDays;
	}

	public void setOntopDays(Integer ontopDays) {
		this.ontopDays = ontopDays;
	}

	public Long getZyyOrgId() {
		return zyyOrgId;
	}

	public void setZyyOrgId(Long zyyOrgId) {
		this.zyyOrgId = zyyOrgId;
	}

	public Date getMessageDate() {
		return messageDate;
	}

	public void setMessageDate(Date messageDate) {
		this.messageDate = messageDate;
	}

	public String getMessageTitle() {
		return messageTitle;
	}

	public void setMessageTitle(String messageTitle) {
		this.messageTitle = messageTitle;
	}

	public String getMessageContent() {
		return messageContent;
	}

	public void setMessageContent(String messageContent) {
		this.messageContent = messageContent;
	}

	public Long getMessageTypeId() {
		return messageTypeId;
	}

	public void setMessageTypeId(Long messageTypeId) {
		this.messageTypeId = messageTypeId;
	}


	public Integer getIsOntop() {
		return isOntop;
	}

	public void setIsOntop(Integer isOntop) {
		this.isOntop = isOntop;
	}

	public Integer getIsNew() {
		return isNew;
	}

	public void setIsNew(Integer isNew) {
		this.isNew = isNew;
	}

	public Integer getMessageLevel() {
		return messageLevel;
	}

	public void setMessageLevel(Integer messageLevel) {
		this.messageLevel = messageLevel;
	}

	public Integer[] getMessageObj() {
		return messageObj;
	}

	public void setMessageObj(Integer[] messageObj) {
		this.messageObj = messageObj;
	}



	public Integer getMessageToIndex() {
		return messageToIndex;
	}

	public void setMessageToIndex(Integer messageToIndex) {
		this.messageToIndex = messageToIndex;
	}

	public Integer getMessageToInner() {
		return messageToInner;
	}

	public void setMessageToInner(Integer messageToInner) {
		this.messageToInner = messageToInner;
	}

	public Integer getMessageToEmail() {
		return messageToEmail;
	}

	public void setMessageToEmail(Integer messageToEmail) {
		this.messageToEmail = messageToEmail;
	}

	public Integer getMessageToSms() {
		return messageToSms;
	}

	public void setMessageToSms(Integer messageToSms) {
		this.messageToSms = messageToSms;
	}

	public Long getMessageSender() {
		return messageSender;
	}

	public void setMessageSender(Long messageSender) {
		this.messageSender = messageSender;
	}

	public Date getMessageCreateDate() {
		return messageCreateDate;
	}

	public void setMessageCreateDate(Date messageCreateDate) {
		this.messageCreateDate = messageCreateDate;
	}

	public Long getAddresseeId() {
		return addresseeId;
	}

	public void setAddresseeId(Long addresseeId) {
		this.addresseeId = addresseeId;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Integer[] getFiles() {
		return files;
	}

	public void setFiles(Integer[] files) {
		this.files = files;
	}

	public String getStartTime() {
		return startTime;
	}

	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}

	public String getEndTime() {
		return endTime;
	}

	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}

	public String getFindDate() {
		return findDate;
	}

	public void setFindDate(String findDate) {
		this.findDate = findDate;
	}

	public String getUsers() {
		return users;
	}

	public void setUsers(String users) {
		this.users = users;
	}

	public Integer[] getWillStatus() {
		return willStatus;
	}

	public void setWillStatus(Integer[] willStatus) {
		this.willStatus = willStatus;
	}

	public String getMessageCategoryName() {
		return messageCategoryName;
	}

	public void setMessageCategoryName(String messageCategoryName) {
		this.messageCategoryName = messageCategoryName;
	}

	public Integer getPageSize() {
		return pageSize;
	}

	public void setPageSize(Integer pageSize) {
		this.pageSize = pageSize;
	}

	public Integer getPageOffset() {
		return pageOffset;
	}

	public void setPageOffset(Integer pageOffset) {
		this.pageOffset = pageOffset;
	}

	public Integer getDepartType() {
		return departType;
	}

	public void setDepartType(Integer departType) {
		this.departType = departType;
	}

	public Integer getCurrentYear() {
		return currentYear;
	}

	public void setCurrentYear(Integer currentYear) {
		this.currentYear = currentYear;
	}

	public Integer getCurrentMonth() {
		return currentMonth;
	}

	public void setCurrentMonth(Integer currentMonth) {
		this.currentMonth = currentMonth;
	}
	
}
