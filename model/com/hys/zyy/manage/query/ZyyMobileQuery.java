package com.hys.zyy.manage.query;

import com.hys.zyy.manage.model.ZyyBaseObject;

public class ZyyMobileQuery  extends ZyyBaseObject {
	/**
	 * 
	 */
	private static final long serialVersionUID = -8103106058426202513L;
	private Long id;
	private String name;
	private String cardNO;
	private Long grade;
	private Long yearCourse;
	private Long cycleType;
	// 判断是初始化数据还是点击查询的数据
	private Integer isQuery;

	public Integer getIsQuery() {
		return isQuery;
	}

	public void setIsQuery(Integer isQuery) {
		this.isQuery = isQuery;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getCardNO() {
		return cardNO;
	}

	public void setCardNO(String cardNO) {
		this.cardNO = cardNO;
	}

	public Long getGrade() {
		return grade;
	}

	public void setGrade(Long grade) {
		this.grade = grade;
	}

	public Long getYearCourse() {
		return yearCourse;
	}

	public void setYearCourse(Long yearCourse) {
		this.yearCourse = yearCourse;
	}

	public Long getCycleType() {
		return cycleType;
	}

	public void setCycleType(Long cycleType) {
		this.cycleType = cycleType;
	}
}
