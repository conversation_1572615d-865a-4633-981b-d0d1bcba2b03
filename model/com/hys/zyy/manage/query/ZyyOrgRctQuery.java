package com.hys.zyy.manage.query;

import com.hys.zyy.manage.model.ZyyBaseObject;
/**
 * 机构封装的查询类
 * <AUTHOR>
 * @date 2019-11-8下午1:38:06
 */
public class ZyyOrgRctQuery extends ZyyBaseObject  {

	/**
	 * 
	 */
	private static final long serialVersionUID = -7423540847364674601L;
	
	private String orgId;
	//机构级别
	private Integer orgType;
	//机构编码
	private String orgCode;
	//机构名称
	private String orgName;
	//处室
	private Integer officesType;
	
	//准考证模板id
	private Long modelId;
	
	public String getOrgId() {
		return orgId;
	}
	public void setOrgId(String orgId) {
		this.orgId = orgId;
	}
	public Integer getOrgType() {
		return orgType;
	}
	public void setOrgType(Integer orgType) {
		this.orgType = orgType;
	}
	public String getOrgCode() {
		return orgCode;
	}
	public void setOrgCode(String orgCode) {
		this.orgCode = orgCode;
	}
	public String getOrgName() {
		return orgName;
	}
	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}
	public Integer getOfficesType() {
		return officesType;
	}
	public void setOfficesType(Integer officesType) {
		this.officesType = officesType;
	}
	public Long getModelId() {
		return modelId;
	}
	public void setModelId(Long modelId) {
		this.modelId = modelId;
	}
}
