package com.hys.zyy.manage.query;

import java.util.List;
import java.util.Map;

import com.hys.zyy.manage.model.ZyyBaseObject;
import com.hys.zyy.manage.model.ZyyRecruitBaseExtend;
import com.hys.zyy.manage.model.ZyyRecruitModify;
import com.hys.zyy.manage.model.ZyyRecruitModifyCheck;
import com.hys.zyy.manage.model.ZyyRecruitModifyDetail;
import com.hys.zyy.manage.model.ZyyUser;

/**
 * 
 * 标题：zyy
 * 
 * 作者：Tony Mar 23, 2012
 * 
 * 描述：
 * 
 * 说明:
 */
public class ZyyRecruitBaseExtendQuery extends ZyyBaseObject  {
	/**
	 * 
	 */
	private static final long serialVersionUID = -1062279571913273785L;

	/**
	 * 年度id
	 */
	private Long yearId;
	
	/**
	 * 阶段
	 */
	private Integer stage;
	
	/**
	 * 医院id
	 */
	private Long orgId;
	
	/**
	 * 申请原因
	 */
	private String applyReason;
	
	/**
	 * @desc 多个医院时，医院IDS
	 */
	private String hospIds;
	/**
	 * @desc 多个专业时，专业IDS
	 */
	private String baseIds;
	
	/**
	 * 计划人数ID
	 */
	private Long recruitModifyId ;
	
	/**
	 * 提交状态 0.申请提交 1.申请通过 2.申请不通过 3.保存状态 -1.已删除
	 */
	private Integer status ;
	
	/**
	 * 提交计划类别 1.社会人计划 2.单位人计划 3.全部
	 */
	private Integer recruitModifyType ;
	
	/**
	 * 委托培训-单位人-是否有审核者 0.无 1.有
	 */
	private Integer consignPlan;

	/**
	 * 自主培训-社会人-是否有审核者 0.无 1.有
	 */
	private Integer libertyPlan;
		
	/**
	 * 单位人社会人 1.单位人 2.社会人 3.包含（单位人社会人）
	 */
	private Integer residencySource ;
	
	/**
	 * 用户信息
	 */
	private ZyyUser zyyUser ;
	
	/**
	 * 是否(社会、单位)制定者
	 */
	private Integer planPerson ;
	
	/**
	 * 医院修改计划招生人数
	 */
	private ZyyRecruitModify modify ;
	
	/**
	 * 基地扩展信息
	 */
	private List<ZyyRecruitBaseExtend> baseExtendList;
	
	/**
	 * 基地扩展信息
	 */
	private Map<Integer,ZyyRecruitBaseExtend> baseExtendMap;
	
	/**
	 * 医院修改计划招生人数详细
	 */
	private List<ZyyRecruitModifyDetail> detailList ;
	
	/**
	 * 计划招生人数详细 map
	 */
	private Map<Integer, ZyyRecruitModifyDetailQuery> detailMap;
	
	/**
	 * 计划招生审核记录
	 */
	private List<ZyyRecruitModifyCheck> checkList ;

	public Long getYearId() {
		return yearId;
	}

	public void setYearId(Long yearId) {
		this.yearId = yearId;
	}

	public Long getOrgId() {
		return orgId;
	}

	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}

	public String getApplyReason() {
		return applyReason;
	}

	public void setApplyReason(String applyReason) {
		this.applyReason = applyReason;
	}

	public Long getRecruitModifyId() {
		return recruitModifyId;
	}

	public void setRecruitModifyId(Long recruitModifyId) {
		this.recruitModifyId = recruitModifyId;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Integer getRecruitModifyType() {
		return recruitModifyType;
	}

	public void setRecruitModifyType(Integer recruitModifyType) {
		this.recruitModifyType = recruitModifyType;
	}

	public Integer getConsignPlan() {
		return consignPlan;
	}

	public void setConsignPlan(Integer consignPlan) {
		this.consignPlan = consignPlan;
	}

	public Integer getLibertyPlan() {
		return libertyPlan;
	}

	public void setLibertyPlan(Integer libertyPlan) {
		this.libertyPlan = libertyPlan;
	}

	public Integer getResidencySource() {
		return residencySource;
	}

	public void setResidencySource(Integer residencySource) {
		this.residencySource = residencySource;
	}

	public ZyyUser getZyyUser() {
		return zyyUser;
	}

	public void setZyyUser(ZyyUser zyyUser) {
		this.zyyUser = zyyUser;
	}

	public Integer getPlanPerson() {
		return planPerson;
	}

	public void setPlanPerson(Integer planPerson) {
		this.planPerson = planPerson;
	}

	public ZyyRecruitModify getModify() {
		return modify;
	}

	public void setModify(ZyyRecruitModify modify) {
		this.modify = modify;
	}

	public List<ZyyRecruitBaseExtend> getBaseExtendList() {
		return baseExtendList;
	}

	public void setBaseExtendList(List<ZyyRecruitBaseExtend> baseExtendList) {
		this.baseExtendList = baseExtendList;
	}

	public Map<Integer, ZyyRecruitBaseExtend> getBaseExtendMap() {
		return baseExtendMap;
	}

	public void setBaseExtendMap(Map<Integer, ZyyRecruitBaseExtend> baseExtendMap) {
		this.baseExtendMap = baseExtendMap;
	}

	public List<ZyyRecruitModifyDetail> getDetailList() {
		return detailList;
	}

	public void setDetailList(List<ZyyRecruitModifyDetail> detailList) {
		this.detailList = detailList;
	}

	public Map<Integer, ZyyRecruitModifyDetailQuery> getDetailMap() {
		return detailMap;
	}

	public void setDetailMap(Map<Integer, ZyyRecruitModifyDetailQuery> detailMap) {
		this.detailMap = detailMap;
	}

	public List<ZyyRecruitModifyCheck> getCheckList() {
		return checkList;
	}

	public void setCheckList(List<ZyyRecruitModifyCheck> checkList) {
		this.checkList = checkList;
	}

	public Integer getStage() {
		return stage;
	}

	public void setStage(Integer stage) {
		this.stage = stage;
	}

	public String getHospIds() {
		return hospIds;
	}

	public void setHospIds(String hospIds) {
		this.hospIds = hospIds;
	}

	public String getBaseIds() {
		return baseIds;
	}

	public void setBaseIds(String baseIds) {
		this.baseIds = baseIds;
	}
}