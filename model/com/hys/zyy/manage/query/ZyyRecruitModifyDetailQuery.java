package com.hys.zyy.manage.query;

import com.hys.zyy.manage.model.ZyyBaseObject;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：张伟清 2012-07-18
 * 
 * 描述：医院修改计划招生人数详细
 * 
 * 说明:
 */
public class ZyyRecruitModifyDetailQuery extends ZyyBaseObject {

	private static final long serialVersionUID = -7436348589993017110L;
	
	/**
	 * 主键ID
	 */
	private Long id;
	
	/**
	 * 计划人数ID
	 */
	private Long recruitModifyId;
	
	/**
	 * 扩展基地ID
	 */
	private Long recruitBaseId;
	
	/**
	 * 医院ID
	 */
	private Long zyyOrgId;
	
	/**
	 * 本科招收人数
	 */
	private Integer undergraduateNum;
	
	/**
	 *硕士招收人数 
	 */
	private Integer masterNum;
	
	/**
	 * 博士招收人数
	 */
	private Integer doctorNum;
	
	/**
	 * 本科招收人数(专业学位研究生结合项目)
	 */
	private Integer undergraduateNumPro;
	
	/**
	 * 本科招收人数(其他)
	 */
	private Integer undergraduateNumOther;

	/**
	 * 本院委托本科招收人数
	 */
	private Integer insideUndergraduateNum ;
	
	/**
	 * 本院委托硕士招收人数
	 */
	private Integer insideMasterNum ;
	
	/**
	 * 本院委托博士招收人数
	 */
	private Integer insideDoctorNum ;
	
	/**
	 * 本院委托本科招收人数(专业学位研究生结合项目)
	 */
	private Integer insideUndergraduateNumPro ;
	
	/**
	 * 本院委托本科招收人数(其他)
	 */
	private Integer insideUndergraduateNumOther ;
	
	/**
	 * 外院委托本科招收人数
	 */
	private Integer outsideUndergraduateNum ;
	
	/**
	 * 外院委托硕士招收人数
	 */
	private Integer outsideMasterNum ;
	
	/**
	 * 外院委托博士招收人数
	 */
	private Integer outsideDoctorNum ;
	
	/**
	 * 外院委托本科招收人数(专业学位研究生结合项目)
	 */
	private Integer outsideUndergraduateNumPro ;
	
	/**
	 * 外院委托本科招收人数(其他)
	 */
	private Integer outsideUndergraduateNumOth ;
	
	/**
	 * 基地名称
	 */
	private String baseName;

	/**
	 * 基地别名
	 */
	private String baseAliasName;
	
	/**
	 * 医院ID 
	 */
	private Long orgId ;
	
	/**
	 * 医院名称
	 */
	private String orgName ;
	
	/**
	 * 医院别名
	 */
	private String orgAliasName ;
	
	/**
	 * 年度信息
	 */
	private String recruitYear ;
	
	/**
	 * 委托培训小计
	 */
	private Integer consignTotal;

	/**
	 * 自主培训小计
	 */
	private Integer libertyTotal;
	
	/**
	 * 自主招生
	 */
	private Integer planTotalNum;
	
	/**
	 * 组织机构序列
	 */
	private Integer orgSeq2 ;
	
	private String entrustTotal;
	private String autonomousTotal;

	

	public String getEntrustTotal() {
		return entrustTotal;
	}

	public void setEntrustTotal(String entrustTotal) {
		this.entrustTotal = entrustTotal;
	}

	public String getAutonomousTotal() {
		return autonomousTotal;
	}

	public void setAutonomousTotal(String autonomousTotal) {
		this.autonomousTotal = autonomousTotal;
	}

	public Integer getOrgSeq2() {
		return orgSeq2;
	}

	public void setOrgSeq2(Integer orgSeq2) {
		this.orgSeq2 = orgSeq2;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getRecruitModifyId() {
		return recruitModifyId;
	}

	public void setRecruitModifyId(Long recruitModifyId) {
		this.recruitModifyId = recruitModifyId;
	}

	public Long getRecruitBaseId() {
		return recruitBaseId;
	}

	public void setRecruitBaseId(Long recruitBaseId) {
		this.recruitBaseId = recruitBaseId;
	}

	public Long getZyyOrgId() {
		return zyyOrgId;
	}

	public void setZyyOrgId(Long zyyOrgId) {
		this.zyyOrgId = zyyOrgId;
	}

	public Integer getUndergraduateNum() {
		return undergraduateNum;
	}

	public void setUndergraduateNum(Integer undergraduateNum) {
		this.undergraduateNum = undergraduateNum;
	}

	public Integer getMasterNum() {
		return masterNum;
	}

	public void setMasterNum(Integer masterNum) {
		this.masterNum = masterNum;
	}

	public Integer getDoctorNum() {
		return doctorNum;
	}

	public void setDoctorNum(Integer doctorNum) {
		this.doctorNum = doctorNum;
	}

	public Integer getUndergraduateNumPro() {
		return undergraduateNumPro;
	}

	public void setUndergraduateNumPro(Integer undergraduateNumPro) {
		this.undergraduateNumPro = undergraduateNumPro;
	}

	public Integer getUndergraduateNumOther() {
		return undergraduateNumOther;
	}

	public void setUndergraduateNumOther(Integer undergraduateNumOther) {
		this.undergraduateNumOther = undergraduateNumOther;
	}

	public Integer getInsideUndergraduateNum() {
		return insideUndergraduateNum;
	}

	public void setInsideUndergraduateNum(Integer insideUndergraduateNum) {
		this.insideUndergraduateNum = insideUndergraduateNum;
	}

	public Integer getInsideMasterNum() {
		return insideMasterNum;
	}

	public void setInsideMasterNum(Integer insideMasterNum) {
		this.insideMasterNum = insideMasterNum;
	}

	public Integer getInsideDoctorNum() {
		return insideDoctorNum;
	}

	public void setInsideDoctorNum(Integer insideDoctorNum) {
		this.insideDoctorNum = insideDoctorNum;
	}

	public Integer getInsideUndergraduateNumPro() {
		return insideUndergraduateNumPro;
	}

	public void setInsideUndergraduateNumPro(Integer insideUndergraduateNumPro) {
		this.insideUndergraduateNumPro = insideUndergraduateNumPro;
	}

	public Integer getInsideUndergraduateNumOther() {
		return insideUndergraduateNumOther;
	}

	public void setInsideUndergraduateNumOther(Integer insideUndergraduateNumOther) {
		this.insideUndergraduateNumOther = insideUndergraduateNumOther;
	}

	public Integer getOutsideUndergraduateNum() {
		return outsideUndergraduateNum;
	}

	public void setOutsideUndergraduateNum(Integer outsideUndergraduateNum) {
		this.outsideUndergraduateNum = outsideUndergraduateNum;
	}

	public Integer getOutsideMasterNum() {
		return outsideMasterNum;
	}

	public void setOutsideMasterNum(Integer outsideMasterNum) {
		this.outsideMasterNum = outsideMasterNum;
	}

	public Integer getOutsideDoctorNum() {
		return outsideDoctorNum;
	}

	public void setOutsideDoctorNum(Integer outsideDoctorNum) {
		this.outsideDoctorNum = outsideDoctorNum;
	}

	public Integer getOutsideUndergraduateNumPro() {
		return outsideUndergraduateNumPro;
	}

	public void setOutsideUndergraduateNumPro(Integer outsideUndergraduateNumPro) {
		this.outsideUndergraduateNumPro = outsideUndergraduateNumPro;
	}

	public Integer getOutsideUndergraduateNumOth() {
		return outsideUndergraduateNumOth;
	}

	public void setOutsideUndergraduateNumOth(Integer outsideUndergraduateNumOth) {
		this.outsideUndergraduateNumOth = outsideUndergraduateNumOth;
	}

	public String getBaseName() {
		return baseName;
	}

	public void setBaseName(String baseName) {
		this.baseName = baseName;
	}

	public String getBaseAliasName() {
		return baseAliasName;
	}

	public void setBaseAliasName(String baseAliasName) {
		this.baseAliasName = baseAliasName;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public String getOrgAliasName() {
		return orgAliasName;
	}

	public void setOrgAliasName(String orgAliasName) {
		this.orgAliasName = orgAliasName;
	}

	public String getRecruitYear() {
		return recruitYear;
	}

	public void setRecruitYear(String recruitYear) {
		this.recruitYear = recruitYear;
	}

	public Integer getConsignTotal() {
		return consignTotal;
	}

	public void setConsignTotal(Integer consignTotal) {
		this.consignTotal = consignTotal;
	}

	public Integer getLibertyTotal() {
		return libertyTotal;
	}

	public void setLibertyTotal(Integer libertyTotal) {
		this.libertyTotal = libertyTotal;
	}

	public Integer getPlanTotalNum() {
		return planTotalNum;
	}

	public void setPlanTotalNum(Integer planTotalNum) {
		this.planTotalNum = planTotalNum;
	}

	public Long getOrgId() {
		return orgId;
	}

	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}
}