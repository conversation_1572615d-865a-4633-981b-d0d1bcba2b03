package com.hys.zyy.manage.query;

import java.util.List;
import java.util.Map;

import org.springframework.web.bind.annotation.PathVariable;

import com.hys.zyy.manage.model.ZyyBaseObject;

/**
 * 招录操作 查询参数封装
 * 
 * <AUTHOR> 2018-4-19 下午3:58:57
 */
public class ZyyRecruitOperateQuery extends ZyyBaseObject{
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	//年度id
	private Integer yearId;
	//阶段id
	private Long stageId;
	//基地id
	private Long baseId;
	//
	private Long orgid;
	//订单定向生
	private Integer directStu;
	//签约单位属性
	private String unitProperty;
	//单位
	private String unit;
	//名称
	private String name;
	//用户id
	private String userId;
	//
	private Long willHospId;
	//
	private Long willBaseId;
	//志愿
	private Integer willWill;
	//是否允许调剂
	private Integer isAdjust;
	//人员类型   1 单位人  2 社会人  3 学位链接
	private Integer residencySource2;
	//证件号码
	private String certificateNo;
	
	//url中的参数
	private Integer category;
	private Integer residencySource;
	//阶段的ids
	private List<Long> stageIds;
	
	private Map<String,String> verifys;
	//导出的扩展列
	private List<String> exportExtendCol;
	
	private Integer degree_convergence;
	
	private Integer hid;
	
	// 1 网页注册   2 excel导入注册
	private Integer flower;
	
	public Integer getYearId() {
		return yearId;
	}
	public void setYearId(Integer yearId) {
		this.yearId = yearId;
	}
	public Long getStageId() {
		return stageId;
	}
	public void setStageId(Long stageId) {
		this.stageId = stageId;
	}
	public Long getBaseId() {
		return baseId;
	}
	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}
	public Integer getDirectStu() {
		return directStu;
	}
	public void setDirectStu(Integer directStu) {
		this.directStu = directStu;
	}
	public String getUnitProperty() {
		return unitProperty;
	}
	public void setUnitProperty(String unitProperty) {
		this.unitProperty = unitProperty;
	}
	public String getUnit() {
		return unit;
	}
	public void setUnit(String unit) {
		this.unit = unit;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getUserId() {
		return userId;
	}
	public void setUserId(String userId) {
		this.userId = userId;
	}
	public Long getWillHospId() {
		return willHospId;
	}
	public void setWillHospId(Long willHospId) {
		this.willHospId = willHospId;
	}
	public Long getWillBaseId() {
		return willBaseId;
	}
	public void setWillBaseId(Long willBaseId) {
		this.willBaseId = willBaseId;
	}
	public Integer getWillWill() {
		return willWill;
	}
	public void setWillWill(Integer willWill) {
		this.willWill = willWill;
	}
	public Integer getIsAdjust() {
		return isAdjust;
	}
	public void setIsAdjust(Integer isAdjust) {
		this.isAdjust = isAdjust;
	}
	public Integer getResidencySource2() {
		return residencySource2;
	}
	public void setResidencySource2(Integer residencySource2) {
		this.residencySource2 = residencySource2;
	}
	public String getCertificateNo() {
		return certificateNo;
	}
	public void setCertificateNo(String certificateNo) {
		this.certificateNo = certificateNo;
	}
	public Integer getCategory() {
		return category;
	}
	public void setCategory(Integer category) {
		this.category = category;
	}
	public Integer getResidencySource() {
		return residencySource;
	}
	public void setResidencySource(Integer residencySource) {
		this.residencySource = residencySource;
	}
	public List<Long> getStageIds() {
		return stageIds;
	}
	public void setStageIds(List<Long> stageIds) {
		this.stageIds = stageIds;
	}
	public Map<String, String> getVerifys() {
		return verifys;
	}
	public void setVerifys(Map<String, String> verifys) {
		this.verifys = verifys;
	}
	public List<String> getExportExtendCol() {
		return exportExtendCol;
	}
	public void setExportExtendCol(List<String> exportExtendCol) {
		this.exportExtendCol = exportExtendCol;
	}
	public Long getOrgid() {
		return orgid;
	}
	public void setOrgid(Long orgid) {
		this.orgid = orgid;
	}
	public Integer getDegree_convergence() {
		return degree_convergence;
	}
	public void setDegree_convergence(Integer degree_convergence) {
		this.degree_convergence = degree_convergence;
	}
	public Integer getHid() {
		return hid;
	}
	public void setHid(Integer hid) {
		this.hid = hid;
	}
	public Integer getFlower() {
		return flower;
	}
	public void setFlower(Integer flower) {
		this.flower = flower;
	}
}
