package com.hys.zyy.manage.query;

import java.util.List;

import com.hys.zyy.manage.model.ZyyBaseObject;
import com.hys.zyy.manage.model.ZyyRecruitStageTimeConfigVO;
import com.hys.zyy.manage.model.ZyyRecruitStageVO;
import com.hys.zyy.manage.model.ZyyRegion;
import com.hys.zyy.manage.model.ZyyResiQualificationLog;
import com.hys.zyy.manage.model.ZyyUser;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：张伟清 2012-03-21
 * 
 * 描述：志愿查询对象
 * 
 * 说明:
 */
public class ZyyRecruitQuery extends ZyyBaseObject{
	
	private static final long serialVersionUID = 3176225267097927387L;

	/**
	 * 真实姓名
	 */
	private String realName ;
	
	/**
	 * 基地ID
	 */
	private Long baseId ;
	
	/**
	 * 标准基地ID
	 */
	private Long baseStdId ;
	
	/**
	 * 医院ID
	 */
	private Long hospId ;
	
	/**
	 * 组织机构ID
	 */
	private Long orgId ;
	
	/**
	 * 年度ID
	 */
	private Long yearId ;
	
	/**
	 * 用户ID
	 */
	private Long userId ;
	
	/**
	 * 志愿信息
	 */
	private Integer will ;
	
	/**
	 * 志愿序列 1.第一志愿 2.第二志愿 3.第三志愿
	 */
	private Integer willSeq ;
	
	/**
	 * 招录志愿ID
	 */
	private Long recruitStageId ;
	
	/**
	 * 阶段ID
	 */
	private Long stageId ;
	
	/**
	 * 排序类别
	 */
	private Integer orderType ;

	/**
	 * 流程ID
	 */
	private Long processId ;
	
	/**
	 * 状态
	 */
	private int[] params ;
	
	/**
	 * 人员类型 1.单位人 2.社会人
	 */
	private Integer residencySource ;
	
	/**
	 * 医师类别  1.中医  2.西医
	 */
	private Integer hospType;

	/**
	 * 是否服从调剂 1.服从 0.不服从 2.全部
	 */
	private Integer isAdjust ;
	
	// “是否服从调剂培训基地” 是、否；“是否服从调剂专业”  是、否；
	private Integer isAdjustHospital ;
	
	/**
	 * 用户信息
	 */
	private ZyyUser zyyUser ;
	
	/**
	 * 各地方区域信息
	 */
	private ZyyRegion region ;
	
	/**
	 * 是否最终极
	 */
	private Integer isFinal ;
	
	/**
	 * 志愿信息
	 */
	private Integer recruitWill ;
	
	/**
	 * 签约单位属性
	 */
	private String propName;

	/**
	 * 签约单位
	 */
	private String entrustUnit ;
	
	/**
	 * 最终报名资格
	 */
	private Integer finalSignupQualification ;
	
	/**
	 * 最终录取资格
	 */
	private Integer finalAdmissionQualification ;
	
	/**
	 * 阶段列表
	 */
	private List<ZyyRecruitStageVO> stageList ;
	
	/**
	 * 阶段列表
	 */
	private List<ZyyRecruitStageTimeConfigVO> recruitStageList ;

	/**
	 * 住院医师报名录取资格记录 列表
	 */
	private List<ZyyResiQualificationLog> logList ;
	
	/**
	 * 页面属性 
	 */
	private Integer pageNum;
	
	/**
	 * 分页页码
	 */
	private int pageOffset ;
	
	/**
	 * 志愿医院
	 */
	private Long willHospId;
	
	/**
	 * 录取医院
	 */
	private Long admitHospId;
	
	/**
	 * 志愿医院培训专科
	 */
	private Long willBaseId;
	
	/**
	 * 录取医院培训专科
	 */
	private Long admitBaseId;
	
	/**
	 * 志愿医院的志愿
	 */
	private Integer willWill;
	
	/**
	 * 录取医院的志愿
	 */
	private Integer admitWill;

	/**
	 * 是否被录取    1:录取   0:未录取   10:非调剂  11:调剂
	 */
	private Integer admited;
	
	/**
	 * 是否被调剂   1:调剂  2:非调剂
	 */
	private Integer adjusted;
	
	/**
	 * 录取资格审核
	 */
	private Integer admitVerifySetting;
	
	/**
	 * 报名资格审核配置
	 */
	private Integer signUpVerifySetting;
	
	/**
	 * 需要导出的列
	 */
	private List<String> exportCol;
	
	/**
	 * 需要导出的扩展信息列
	 */
	private List<String> exportExtendCol;
	
	/**
	 * 医师培训专科类别  1.中医  2.西医
	 */
	private Integer userHospType;
	
	/**
	 * 定向考单生
	 */
	private Integer directStu;
	/**
	 * 人员类型 ： 1 单位人，2 社会人，3 学位链接
	 */
	private Integer residencySource2;
	//证件号码
	private String certificateNo;
	
	public Integer getDirectStu() {
		return directStu;
	}

	public void setDirectStu(Integer directStu) {
		this.directStu = directStu;
	}

	public String getDegree_convergence() {
		return degree_convergence;
	}

	public void setDegree_convergence(String degree_convergence) {
		this.degree_convergence = degree_convergence;
	}

	/**
	 * 学位衔接
	 */
	private String degree_convergence;

	public Integer getUserHospType() {
		return userHospType;
	}

	public void setUserHospType(Integer userHospType) {
		this.userHospType = userHospType;
	}

	public List<String> getExportExtendCol() {
		return exportExtendCol;
	}

	public void setExportExtendCol(List<String> exportExtendCol) {
		this.exportExtendCol = exportExtendCol;
	}

	public List<String> getExportCol() {
		return exportCol;
	}

	public void setExportCol(List<String> exportCol) {
		this.exportCol = exportCol;
	}

	public Integer getAdmitVerifySetting() {
		return admitVerifySetting;
	}

	public void setAdmitVerifySetting(Integer admitVerifySetting) {
		this.admitVerifySetting = admitVerifySetting;
	}

	public Integer getSignUpVerifySetting() {
		return signUpVerifySetting;
	}

	public void setSignUpVerifySetting(Integer signUpVerifySetting) {
		this.signUpVerifySetting = signUpVerifySetting;
	}

	public Integer getAdmited() {
		return admited;
	}

	public void setAdmited(Integer admited) {
		this.admited = admited;
	}

	public Integer getAdjusted() {
		return adjusted;
	}

	public void setAdjusted(Integer adjusted) {
		this.adjusted = adjusted;
	}
	
	public List<ZyyRecruitStageTimeConfigVO> getRecruitStageList() {
		return recruitStageList;
	}

	public void setRecruitStageList(
			List<ZyyRecruitStageTimeConfigVO> recruitStageList) {
		this.recruitStageList = recruitStageList;
	}

	public Long getWillBaseId() {
		return willBaseId;
	}

	public void setWillBaseId(Long willBaseId) {
		this.willBaseId = willBaseId;
	}

	public Long getAdmitBaseId() {
		return admitBaseId;
	}

	public void setAdmitBaseId(Long admitBaseId) {
		this.admitBaseId = admitBaseId;
	}

	public Long getWillHospId() {
		return willHospId;
	}

	public void setWillHospId(Long willHospId) {
		this.willHospId = willHospId;
	}

	public Long getAdmitHospId() {
		return admitHospId;
	}

	public void setAdmitHospId(Long admitHospId) {
		this.admitHospId = admitHospId;
	}

	public Integer getWillWill() {
		return willWill;
	}

	public void setWillWill(Integer willWill) {
		this.willWill = willWill;
	}

	public Integer getAdmitWill() {
		return admitWill;
	}

	public void setAdmitWill(Integer admitWill) {
		this.admitWill = admitWill;
	}
	
	public Integer getPageNum() {
		return pageNum;
	}

	public void setPageNum(Integer pageNum) {
		this.pageNum = pageNum;
	}

	public String getRealName() {
		return realName;
	}

	public void setRealName(String realName) {
		this.realName = realName;
	}

	public Long getBaseId() {
		return baseId;
	}

	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}

	public Long getBaseStdId() {
		return baseStdId;
	}

	public void setBaseStdId(Long baseStdId) {
		this.baseStdId = baseStdId;
	}

	public Long getHospId() {
		return hospId;
	}

	public void setHospId(Long hospId) {
		this.hospId = hospId;
	}

	public Long getOrgId() {
		return orgId;
	}

	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}

	public Long getYearId() {
		return yearId;
	}

	public void setYearId(Long yearId) {
		this.yearId = yearId;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public Integer getWill() {
		return will;
	}

	public void setWill(Integer will) {
		this.will = will;
	}

	public Long getRecruitStageId() {
		return recruitStageId;
	}

	public void setRecruitStageId(Long recruitStageId) {
		this.recruitStageId = recruitStageId;
	}

	public Integer getOrderType() {
		return orderType;
	}

	public void setOrderType(Integer orderType) {
		this.orderType = orderType;
	}

	public Long getProcessId() {
		return processId;
	}

	public void setProcessId(Long processId) {
		this.processId = processId;
	}

	public int[] getParams() {
		return params;
	}

	public void setParams(int[] params) {
		this.params = params;
	}

	public Integer getResidencySource() {
		return residencySource;
	}

	public void setResidencySource(Integer residencySource) {
		this.residencySource = residencySource;
	}

	public ZyyUser getZyyUser() {
		return zyyUser;
	}

	public void setZyyUser(ZyyUser zyyUser) {
		this.zyyUser = zyyUser;
	}

	public Integer getFinalSignupQualification() {
		return finalSignupQualification;
	}

	public void setFinalSignupQualification(Integer finalSignupQualification) {
		this.finalSignupQualification = finalSignupQualification;
	}

	public List<ZyyRecruitStageVO> getStageList() {
		return stageList;
	}

	public void setStageList(List<ZyyRecruitStageVO> stageList) {
		this.stageList = stageList;
	}

	public List<ZyyResiQualificationLog> getLogList() {
		return logList;
	}

	public void setLogList(List<ZyyResiQualificationLog> logList) {
		this.logList = logList;
	}

	public ZyyRegion getRegion() {
		return region;
	}

	public void setRegion(ZyyRegion region) {
		this.region = region;
	}

	public Integer getWillSeq() {
		return willSeq;
	}

	public void setWillSeq(Integer willSeq) {
		this.willSeq = willSeq;
	}

	public Long getStageId() {
		return stageId;
	}

	public void setStageId(Long stageId) {
		this.stageId = stageId;
	}

	public Integer getIsFinal() {
		return isFinal;
	}

	public void setIsFinal(Integer isFinal) {
		this.isFinal = isFinal;
	}

	public Integer getRecruitWill() {
		return recruitWill;
	}

	public void setRecruitWill(Integer recruitWill) {
		this.recruitWill = recruitWill;
	}

	public String getEntrustUnit() {
		return entrustUnit;
	}

	public void setEntrustUnit(String entrustUnit) {
		this.entrustUnit = entrustUnit;
	}

	public Integer getFinalAdmissionQualification() {
		return finalAdmissionQualification;
	}

	public void setFinalAdmissionQualification(Integer finalAdmissionQualification) {
		this.finalAdmissionQualification = finalAdmissionQualification;
	}

	public Integer getIsAdjust() {
		return isAdjust;
	}

	public void setIsAdjust(Integer isAdjust) {
		this.isAdjust = isAdjust;
	}
	
	public Integer getIsAdjustHospital() {
		return isAdjustHospital;
	}

	public void setIsAdjustHospital(Integer isAdjustHospital) {
		this.isAdjustHospital = isAdjustHospital;
	}

	public String getPropName() {
		return propName;
	}

	public void setPropName(String propName) {
		this.propName = propName;
	}

	public int getPageOffset() {
		return pageOffset;
	}

	public void setPageOffset(int pageOffset) {
		this.pageOffset = pageOffset;
	}
	
	public Integer getHospType() {
		return hospType;
	}

	public void setHospType(Integer hospType) {
		this.hospType = hospType;
	}

	public Integer getResidencySource2() {
		return residencySource2;
	}

	public void setResidencySource2(Integer residencySource2) {
		this.residencySource2 = residencySource2;
	}

	public String getCertificateNo() {
		return certificateNo;
	}

	public void setCertificateNo(String certificateNo) {
		this.certificateNo = certificateNo;
	}
	
}