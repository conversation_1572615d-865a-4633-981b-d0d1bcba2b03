package com.hys.zyy.manage.query;

import java.util.List;

import com.hys.zyy.manage.model.ZyyBaseObject;
import com.hys.zyy.manage.model.ZyyRecruitStage;

/**
 * 
 * 标题：zyy
 * 
 * 作者：Tony Mar 23, 2012
 * 
 * 描述：
 * 
 * 说明:
 */
public class ZyyRecruitStageQuery extends ZyyBaseObject{
	
	private static final long serialVersionUID = 3445828668679938615L;

	/**
	 * 年度id
	 */
	private Long yearId;
	
	/**
	 * 组织机构ID
	 */
	private Long orgId;
	
	/**
	 * 状态
	 * 0 -保存状态 
	 * 1 -提交状态
	 */
	private Integer status;
	
	/**
	 * 类别  1 -单位人 2 -社会人
	 */
	private Integer stageType;

	/**
	 * ZyyRecruitStage
	 */
	private List<ZyyRecruitStage> recruitStageList;
	
	public Long getYearId() {
		return yearId;
	}

	public void setYearId(Long yearId) {
		this.yearId = yearId;
	}

	public List<ZyyRecruitStage> getRecruitStageList() {
		return recruitStageList;
	}

	public void setRecruitStageList(List<ZyyRecruitStage> recruitStageList) {
		this.recruitStageList = recruitStageList;
	}

	public Long getOrgId() {
		return orgId;
	}

	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Integer getStageType() {
		return stageType;
	}

	public void setStageType(Integer stageType) {
		this.stageType = stageType;
	}
}
