package com.hys.zyy.manage.query;

import com.hys.zyy.manage.constants.Constants;
import com.hys.zyy.manage.model.ZyyBaseObject;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：张伟清 2012-11-29
 * 
 * 描述：招录统计 查询统计对象
 * 
 * 说明:
 */
public class ZyyRecruitStatisQuery extends ZyyBaseObject {

	private static final long serialVersionUID = -4605024292402342705L;

	/**
	 * 医院中西医类别：1.中医 2.西医
	 */
	private Integer hospType ;
	
	/**
	 * 医院中西医类别：1.中医 2.西医
	 */
	private Integer __htp__ ;
	
	/**
	 * 医院ID
	 */
	private Long hospId ;
	
	/**
	 * 基地ID
	 */
	private Long baseId ;
	
	/**
	 * 标准基地ID
	 */
	private Long baseStdId ;
	
	/**
	 * 年度ID
	 */
	private Long yearId ;
	
	/**
	 * 人员类别 1.单位人 2.社会人 
	 */
	private Integer resiSource ;
	
	/**
	 * 招录阶段ID
	 */
	private Long recruitStageId ;
	
	/**
	 * 省厅组织机构ID
	 */
	private Long provId ;
	
	/**
	 * 查询类别：1.生源地（本地/非本地） 2.毕业时间（应届/往届） 3.最高学位（本科/硕士/博士） 4.毕业院校（本地/非本地） 
	 */
	private Integer[] viewFeature ;
	
	/**
	 * 录取状态
	 */
	private Integer[] luQuStatus = Constants.MATRICULATED_STATUS ;
	
	/**
	 * 报名状态
	 */
	private Integer[] registStatus = Constants.SIGN_UP_STATUS ;
	
	/**
	 * 报道状态
	 */
	private Integer[] chekInStatus = Constants.CHECK_IN_STATUS ;
	
	/**
	 * 统计类别 1.报名统计 2.录取统计 3.缺额统计 4.报到统计 5.综合分析
	 */
	private Integer statisType ;
	/**
	 * 有无报名资格审核标识
	 */
	private Integer signUpQua = 0;
	/**
	 * 有无录取资格审核标识
	 */
	private Integer checkInQua = 0;
	/**
	 * 志愿
	 */
	private Integer signUpWish;
	/**
	 * 专科
	 */
	private Integer signUpSubject;
	/**
	 * 签约单位属性
	 */
	private String hospPro;
	/**
	 * 签约单位
	 */
	private String hospCon;
	/**
	 * 用户类别
	 */
	private Integer userType;
	
	/**
	 * 报名状态
	 */
	private Integer signUpSatus;
	
	public Integer getUserType() {
		return userType;
	}

	public void setUserType(Integer userType) {
		this.userType = userType;
	}

	public String getHospPro() {
		return hospPro;
	}

	public void setHospPro(String hospPro) {
		this.hospPro = hospPro;
	}

	public String getHospCon() {
		return hospCon;
	}

	public void setHospCon(String hospCon) {
		this.hospCon = hospCon;
	}

	public Integer getSignUpSubject() {
		return signUpSubject;
	}

	public void setSignUpSubject(Integer signUpSubject) {
		this.signUpSubject = signUpSubject;
	}

	public Integer getSignUpWish() {
		return signUpWish;
	}

	public void setSignUpWish(Integer signUpWish) {
		this.signUpWish = signUpWish;
	}

	public Integer getSignUpQua() {
		return signUpQua;
	}

	public void setSignUpQua(Integer signUpQua) {
		this.signUpQua = signUpQua;
	}

	public Integer getCheckInQua() {
		return checkInQua;
	}

	public void setCheckInQua(Integer checkInQua) {
		this.checkInQua = checkInQua;
	}

	public Integer getHospType() {
		return hospType;
	}

	public void setHospType(Integer hospType) {
		this.hospType = hospType;
	}

	public Long getHospId() {
		return hospId;
	}

	public void setHospId(Long hospId) {
		this.hospId = hospId;
	}

	public Long getBaseId() {
		return baseId;
	}

	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}

	public Long getBaseStdId() {
		return baseStdId;
	}

	public void setBaseStdId(Long baseStdId) {
		this.baseStdId = baseStdId;
	}

	public Long getYearId() {
		return yearId;
	}

	public void setYearId(Long yearId) {
		this.yearId = yearId;
	}

	public Integer get__htp__() {
		return __htp__;
	}

	public void set__htp__(Integer __htp__) {
		this.__htp__ = __htp__;
	}

	public Integer[] getViewFeature() {
		return viewFeature;
	}

	public void setViewFeature(Integer[] viewFeature) {
		this.viewFeature = viewFeature;
	}

	public Integer getResiSource() {
		return resiSource;
	}

	public void setResiSource(Integer resiSource) {
		this.resiSource = resiSource;
	}

	public Long getRecruitStageId() {
		return recruitStageId;
	}

	public void setRecruitStageId(Long recruitStageId) {
		this.recruitStageId = recruitStageId;
	}

	public Long getProvId() {
		return provId;
	}

	public void setProvId(Long provId) {
		this.provId = provId;
	}

	public Integer[] getLuQuStatus() {
		return luQuStatus;
	}

	public void setLuQuStatus(Integer[] luQuStatus) {
		this.luQuStatus = luQuStatus;
	}

	public Integer[] getRegistStatus() {
		return registStatus;
	}

	public void setRegistStatus(Integer[] registStatus) {
		this.registStatus = registStatus;
	}

	public Integer getStatisType() {
		return statisType;
	}

	public void setStatisType(Integer statisType) {
		this.statisType = statisType;
	}

	public Integer[] getChekInStatus() {
		return chekInStatus;
	}

	public void setChekInStatus(Integer[] chekInStatus) {
		this.chekInStatus = chekInStatus;
	}

	public Integer getSignUpSatus() {
		return signUpSatus;
	}

	public void setSignUpSatus(Integer signUpSatus) {
		this.signUpSatus = signUpSatus;
	}
}
