package com.hys.zyy.manage.query;

import java.util.List;

import com.hys.zyy.manage.model.ZyyBaseObject;
import com.hys.zyy.manage.model.ZyyResidencyAdmission;


/**
 * 住院医师准考证信息查询对象实体类
 * 
 * <AUTHOR>      
 * @version 1.0    
 * @created 2012-9-10 下午03:49:14
 */
public class ZyyResidencyAdmissionQuery extends ZyyBaseObject{
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1192715539373595408L;

	/**
	 * ID
	 */
	private Long id;

	/**
	 * 年份
	 */
	private Long yearId ;
	
	/**
	 * 阶段 
	 */
	private Long stageId ;
	
	/**
	 * 志愿
	 */
	private Integer will;
	

	/**
	 * 人员类型 1.单位人 2.社会人
	 */
	private Integer residencySource ;
	
	/**
	 * 最高学历 1-大学专科,2-大学本科,3-硕士研究生,4-博士研究生,5-博士后
	 */
	private Integer highestRecordSchool;
	
	/**
	 * 学员姓名
	 */
	private String userName;
	
	/**
	 * 证件号码
	 */
	private String certificateNumber;
	
	/**
	 * @desc 准考证ID
	 */
	private Long admissionId;

	/**
	 * 准考证信息列表  
	 */
	private List<ZyyResidencyAdmission> admissionList;
	
	/**
	 * 操作结果
	 */
	private String result;

	
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}
	public String getResult() {
		return result;
	}

	public void setResult(String result) {
		this.result = result;
	}

	public List<ZyyResidencyAdmission> getAdmissionList() {
		return admissionList;
	}

	public void setAdmissionList(List<ZyyResidencyAdmission> admissionList) {
		this.admissionList = admissionList;
	}

	public Long getYearId() {
		return yearId;
	}

	public void setYearId(Long yearId) {
		this.yearId = yearId;
	}

	public Long getStageId() {
		return stageId;
	}

	public void setStageId(Long stageId) {
		this.stageId = stageId;
	}

	public Integer getResidencySource() {
		return residencySource;
	}

	public void setResidencySource(Integer residencySource) {
		this.residencySource = residencySource;
	}

	public Integer getHighestRecordSchool() {
		return highestRecordSchool;
	}

	public void setHighestRecordSchool(Integer highestRecordSchool) {
		this.highestRecordSchool = highestRecordSchool;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getCertificateNumber() {
		return certificateNumber;
	}

	public void setCertificateNumber(String certificateNumber) {
		this.certificateNumber = certificateNumber;
	}

	public Integer getWill() {
		return will;
	}

	public void setWill(Integer will) {
		this.will = will;
	}

	public Long getAdmissionId() {
		return admissionId;
	}

	public void setAdmissionId(Long admissionId) {
		this.admissionId = admissionId;
	}
	
}