package com.hys.zyy.manage.query;

import java.util.List;

import com.hys.zyy.manage.model.ZyyBaseObject;
import com.hys.zyy.manage.model.ZyyResidencyHbkExt;


/**
 * 
 * 标题：用户填写手册
 * 
 * 作者：ccj  
 * 
 * 描述：
 * 
 * 说明:
 */

public class ZyyResidencyHbkExtQuery extends ZyyBaseObject{

	private static final long serialVersionUID = 1839135588653098898L;

	/**
	 * 主键ID
	 */
	private List<ZyyResidencyHbkExt> resdenHbkList;

	public List<ZyyResidencyHbkExt> getResdenHbkList() {
		return resdenHbkList;
	}

	public void setResdenHbkList(List<ZyyResidencyHbkExt> resdenHbkList) {
		this.resdenHbkList = resdenHbkList;
	}


	
	

}