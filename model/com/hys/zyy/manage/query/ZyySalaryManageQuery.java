package com.hys.zyy.manage.query;

import java.util.Date;

import com.hys.zyy.manage.model.ZyyBaseObject;

public class ZyySalaryManageQuery extends ZyyBaseObject {

	private static final long serialVersionUID = -7729508907658832327L;

	private String realName;
	
	private String certificateNo;
	
	private String payMonth;
	
	private Long zyyOrgId;
	
	private Date startDate;
	
	private Date endDate;
	
	private Long yearId;
	
	private Long zyyBaseId;
	
	private Integer highestRecordSchool;
	
	private Integer residencySource;
	
	private Long provinceId;//新增属性，省厅用户使用，可以查看所管辖的所有医院数据
	private Integer userType;//用户类型，同上
	
	public String getRealName() {
		return realName;
	}

	public void setRealName(String realName) {
		this.realName = realName;
	}

	public String getCertificateNo() {
		return certificateNo;
	}

	public void setCertificateNo(String certificateNo) {
		this.certificateNo = certificateNo;
	}

	public String getPayMonth() {
		return payMonth;
	}

	public void setPayMonth(String payMonth) {
		this.payMonth = payMonth;
	}

	public Long getZyyOrgId() {
		return zyyOrgId;
	}

	public void setZyyOrgId(Long zyyOrgId) {
		this.zyyOrgId = zyyOrgId;
	}

	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	public Long getYearId() {
		return yearId;
	}

	public void setYearId(Long yearId) {
		this.yearId = yearId;
	}

	public Long getZyyBaseId() {
		return zyyBaseId;
	}

	public void setZyyBaseId(Long zyyBaseId) {
		this.zyyBaseId = zyyBaseId;
	}

	public Integer getHighestRecordSchool() {
		return highestRecordSchool;
	}

	public void setHighestRecordSchool(Integer highestRecordSchool) {
		this.highestRecordSchool = highestRecordSchool;
	}

	public Integer getResidencySource() {
		return residencySource;
	}

	public void setResidencySource(Integer residencySource) {
		this.residencySource = residencySource;
	}

	public Long getProvinceId() {
		return provinceId;
	}

	public void setProvinceId(Long provinceId) {
		this.provinceId = provinceId;
	}

	public Integer getUserType() {
		return userType;
	}

	public void setUserType(Integer userType) {
		this.userType = userType;
	}	
	
}
