package com.hys.zyy.manage.query;

import java.util.List;

import com.hys.zyy.manage.model.ZyyBaseObject;
import com.hys.zyy.manage.model.ZyyBaseStd;
import com.hys.zyy.manage.model.ZyyOrgVO;
import com.hys.zyy.manage.model.ZyyRegion;

/**
 * 
 * 标题：zyy
 * 
 * 作者：Tony Apr 16, 2012
 * 
 * 描述：
 * 
 * 说明:
 */
public class ZyyStatisticsQuery extends ZyyBaseObject{
	
	private static final long serialVersionUID = -457420290321251094L;

	/**
	 * 招录年度id
	 */
	private Long yearId;
	
	/**
	 * 招录阶段id
	 */
	private Long stageId;
	
	/**
	 * 招录阶段id
	 */	
	private String stageIdStr;
	
	/**
	 * 省厅id
	 */
	private Long orgId;
	
	/**
	 * 医院id
	 */
	private Long hospitalId;
	
	/**
	 * 组织机构类别
	 */
	private Integer orgTypeFlag;
	
	/**
	 * 标准基地id
	 */
	private Long baseStdId;
	/**
	 * 标准医院id
	 */
	private Long hospId;
	/**
	 * 按医院:1
	 * 按学科:2
	 */
	private Integer stype;
	/**
	 * 培训类别：1 中医
	 * 2 西医
	 * 3 中西医
	 */
	private Integer trainSort;
	/**
	 * 1   -已报名(提交状态)
	 * 10 -已通知笔试
	 * 11 -已笔试
	 * 12 -已通知面试
	 * 13 -已面试
	 * 20 -已淘汰
	 * 30 -已通知签约
	 * 31 -已签约
	 * 32 -已毁约
	 * 40 -已通知报到
	 * 41 -已报到
	 * -1  -已经删除
	 * 志愿状态
	 */
	private Integer[] status;
	
	/**
	 * 0 - 全部
	 * 1 - 本地地区
	 * 2 - 非本地地区
	 * 生源地
	 */
	private Integer homePlace;
	
	/**
	 * 0 -全部
	 * 1 -本科
	 * 2 -硕士 
	 * 3 -博士
	 * 最高学历
	 */
	private Integer highestRecordSchool;
	
	/**
	 * 0 -全部
	 * 1 -一类
	 * 2 -二类
	 * 3 -三类 
	 * 4 -四类
	 * 毕业院校 
	 */
	private Integer graduateSchoolType;
	
	
	/**
	 * 志愿状态
	 * 1－全部
	 */
	private Integer paramStatus1;
	
	/**
	 * 志愿状态
	 * 1－已报名
	 */
	private Integer paramStatus2;
	
	/**
	 * 志愿状态
	 * 1－已面试
	 */
	private Integer paramStatus3;
	
	/**
	 * 志愿状态
	 * 1－已签约
	 */
	private Integer paramStatus4;
	
	/**
	 * 志愿状态
	 * 1－已报到 
	 */
	private Integer paramStatus5;
	
	/**
	 * 志愿状态
	 * 1－已毁约
	 */
	private Integer paramStatus6;
	
	/**
	 * 志愿状态
	 */
	private String paramStatus;
	
	
	/**
	 * 志愿状态
	 *  已报名
	 */
	private String paramStatusStr2;
	
	/**
	 * 志愿状态
	 *  已面试
	 */
	private String paramStatusStr3;
	
	/**
	 * 志愿状态
	 * 1－已签约
	 */
	private String paramStatusStr4;
	
	/**
	 * 志愿状态
	 *  已报到 
	 */
	private String paramStatusStr5;
	
	/**
	 * 志愿状态
	 *  已毁约
	 */
	private String paramStatusStr6;	
	
	
	/**
	 * 是否应届
	 * 9 全部
	 * 1 是 
	 * 0 否
	 */
	private Integer isFresh;
	
	
	/**
	 * 生源地
	 */
	private String param1All;
	
	/**
	 * 毕业时间
	 */
	private String param2All;
	
	/**
	 * 学历
	 */
	private String param3All;
	
	/**
	 * 毕业院校
	 */
	private String param4All;
	
	/**
	 * 医院列表
	 */
	private List<ZyyOrgVO> orgList;
	
	/**
	 * 学科列表
	 */
	private List<ZyyBaseStd> baseStdList;
	/*
	 * 委托 1
	 * 自主 2
	 */
	private int resiSource;
	
	/**
	 * 综合分析查询方式
	 * 1:省厅用户 按医院查询 全部医院 全部学科
	 * 2:省厅用户 按医院查询 全部医院 单一学科
	 * 3:省厅用户 按医院查询 单一医院 全部学科
	 * 4:省厅用户 按医院查询 单一医院 单一学科
	 * 5:省厅用户 按学科查询 全部学科
	 * 6:省厅用户 按学科查询 单一学科
	 * 7:医院用户 按学科查询 全部学科
	 * 8:医院用户 按学科查询 单一学科
	 */
	private int queryType;
	
	/**
	 * 各地区信息
	 */
	private ZyyRegion region ;
	
	/**
	 * 单位人 社会人
	 */
	private Integer residencySource ;
	
	
	public ZyyRegion getRegion() {
		return region;
	}

	public void setRegion(ZyyRegion region) {
		this.region = region;
	}

	public Long getStageId() {
		return stageId;
	}

	public void setStageId(Long stageId) {
		this.stageId = stageId;
	}

	public Long getYearId() {
		return yearId;
	}

	public void setYearId(Long yearId) {
		this.yearId = yearId;
	}

	public Integer getStype() {
		return stype;
	}

	public void setStype(Integer stype) {
		this.stype = stype;
	}

	public Long getOrgId() {
		return orgId;
	}

	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}

	public Long getHospitalId() {
		return hospitalId;
	}

	public void setHospitalId(Long hospitalId) {
		this.hospitalId = hospitalId;
	}

	public Integer getOrgTypeFlag() {
		return orgTypeFlag;
	}

	public void setOrgTypeFlag(Integer orgTypeFlag) {
		this.orgTypeFlag = orgTypeFlag;
	}

	public Long getBaseStdId() {
		return baseStdId;
	}

	public void setBaseStdId(Long baseStdId) {
		this.baseStdId = baseStdId;
	}

	public Integer[] getStatus() {
		return status;
	}

	public void setStatus(Integer[] status) {
		this.status = status;
	}

	public Integer getHomePlace() {
		return homePlace;
	}

	public void setHomePlace(Integer homePlace) {
		this.homePlace = homePlace;
	}

	public Integer getHighestRecordSchool() {
		return highestRecordSchool;
	}

	public void setHighestRecordSchool(Integer highestRecordSchool) {
		this.highestRecordSchool = highestRecordSchool;
	}

	public Integer getGraduateSchoolType() {
		return graduateSchoolType;
	}

	public void setGraduateSchoolType(Integer graduateSchoolType) {
		this.graduateSchoolType = graduateSchoolType;
	}

	public Integer getParamStatus1() {
		return paramStatus1;
	}

	public void setParamStatus1(Integer paramStatus1) {
		this.paramStatus1 = paramStatus1;
	}

	public Integer getParamStatus2() {
		return paramStatus2;
	}

	public void setParamStatus2(Integer paramStatus2) {
		this.paramStatus2 = paramStatus2;
	}

	public Integer getParamStatus3() {
		return paramStatus3;
	}

	public void setParamStatus3(Integer paramStatus3) {
		this.paramStatus3 = paramStatus3;
	}

	public Integer getParamStatus4() {
		return paramStatus4;
	}

	public void setParamStatus4(Integer paramStatus4) {
		this.paramStatus4 = paramStatus4;
	}

	public Integer getParamStatus5() {
		return paramStatus5;
	}

	public void setParamStatus5(Integer paramStatus5) {
		this.paramStatus5 = paramStatus5;
	}

	public Integer getParamStatus6() {
		return paramStatus6;
	}

	public void setParamStatus6(Integer paramStatus6) {
		this.paramStatus6 = paramStatus6;
	}

	public Integer getIsFresh() {
		return isFresh;
	}

	public void setIsFresh(Integer isFresh) {
		this.isFresh = isFresh;
	}

	public String getParam1All() {
		return param1All;
	}

	public void setParam1All(String param1All) {
		this.param1All = param1All;
	}

	public String getParam2All() {
		return param2All;
	}

	public void setParam2All(String param2All) {
		this.param2All = param2All;
	}

	public String getParam3All() {
		return param3All;
	}

	public void setParam3All(String param3All) {
		this.param3All = param3All;
	}

	public String getParam4All() {
		return param4All;
	}

	public void setParam4All(String param4All) {
		this.param4All = param4All;
	}

	public String getParamStatus() {
		return paramStatus;
	}

	public void setParamStatus(String paramStatus) {
		this.paramStatus = paramStatus;
	}

	public String getParamStatusStr2() {
		return paramStatusStr2;
	}

	public void setParamStatusStr2(String paramStatusStr2) {
		this.paramStatusStr2 = paramStatusStr2;
	}

	public String getParamStatusStr3() {
		return paramStatusStr3;
	}

	public void setParamStatusStr3(String paramStatusStr3) {
		this.paramStatusStr3 = paramStatusStr3;
	}

	public String getParamStatusStr4() {
		return paramStatusStr4;
	}

	public void setParamStatusStr4(String paramStatusStr4) {
		this.paramStatusStr4 = paramStatusStr4;
	}

	public String getParamStatusStr5() {
		return paramStatusStr5;
	}

	public void setParamStatusStr5(String paramStatusStr5) {
		this.paramStatusStr5 = paramStatusStr5;
	}

	public String getParamStatusStr6() {
		return paramStatusStr6;
	}

	public void setParamStatusStr6(String paramStatusStr6) {
		this.paramStatusStr6 = paramStatusStr6;
	}

	public List<ZyyOrgVO> getOrgList() {
		return orgList;
	}

	public void setOrgList(List<ZyyOrgVO> orgList) {
		this.orgList = orgList;
	}

	public List<ZyyBaseStd> getBaseStdList() {
		return baseStdList;
	}

	public void setBaseStdList(List<ZyyBaseStd> baseStdList) {
		this.baseStdList = baseStdList;
	}

	public int getQueryType() {
		return queryType;
	}

	public void setQueryType(int queryType) {
		this.queryType = queryType;
	}

	public String getStageIdStr() {
		return stageIdStr;
	}

	public void setStageIdStr(String stageIdStr) {
		this.stageIdStr = stageIdStr;
	}

	public Integer getResidencySource() {
		return residencySource;
	}

	public void setResidencySource(Integer residencySource) {
		this.residencySource = residencySource;
	}

	public Integer getTrainSort() {
		return trainSort;
	}

	public void setTrainSort(Integer trainSort) {
		this.trainSort = trainSort;
	}

	public int getResiSource() {
		return resiSource;
	}

	public void setResiSource(int resiSource) {
		this.resiSource = resiSource;
	}

	public Long getHospId() {
		return hospId;
	}

	public void setHospId(Long hospId) {
		this.hospId = hospId;
	}

	

	
	

}
