package com.hys.zyy.manage.query;

import java.io.Serializable;
import java.util.List;

import com.hys.zyy.manage.model.ZyyActivityTypeVO;

/**
 *  带教绩效统计  查询类
 * <AUTHOR>
 * @date 2019-1-10下午4:56:40
 */
public class ZyyTeacherPerformanceQuery implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	//医院id
	private Long hospitalId;
	//科室id
	private Long deptId;
	//开始时间
	private String startTime;
	//结束时间
	private String endTime;
	//证件号 /姓名
	private String certName;
	//显示项目1,2,3,4...
	private String showItem;
	//查询类型 1 : 页面点击查询  null： 直接跳转过来的  为了checkbox的默认全选 加的这个字段
	private Integer queryType;
	//省份id
	private Long provinceId;
	//系统活动类型
	private List<ZyyActivityTypeVO> activityTypeList;
	//选择的活动类型
	private String activityType;
	//活动类型选择的数量
	private Integer activityTotal = 0;
	/*
	 * 人员类型
	 */
	private Integer residencySource;
	private Integer userCategory;
	public Integer getResidencySource() {
		return residencySource;
	}
	public void setResidencySource(Integer residencySource) {
		this.residencySource = residencySource;
	}
	
	public Integer getUserCategory() {
		return userCategory;
	}
	public void setUserCategory(Integer userCategory) {
		this.userCategory = userCategory;
	}
	public Long getHospitalId() {
		return hospitalId;
	}
	public void setHospitalId(Long hospitalId) {
		this.hospitalId = hospitalId;
	}
	public Long getDeptId() {
		return deptId;
	}
	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}
	public String getStartTime() {
		return startTime;
	}
	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}
	public String getEndTime() {
		return endTime;
	}
	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}
	public String getCertName() {
		return certName;
	}
	public void setCertName(String certName) {
		this.certName = certName;
	}
	public String getShowItem() {
		return showItem;
	}
	public void setShowItem(String showItem) {
		this.showItem = showItem;
	}
	public Integer getQueryType() {
		return queryType;
	}
	public void setQueryType(Integer queryType) {
		this.queryType = queryType;
	}
	public Long getProvinceId() {
		return provinceId;
	}
	public void setProvinceId(Long provinceId) {
		this.provinceId = provinceId;
	}
	public List<ZyyActivityTypeVO> getActivityTypeList() {
		return activityTypeList;
	}
	public void setActivityTypeList(List<ZyyActivityTypeVO> activityTypeList) {
		this.activityTypeList = activityTypeList;
	}
	public String getActivityType() {
		return activityType;
	}
	public void setActivityType(String activityType) {
		this.activityType = activityType;
	}
	public Integer getActivityTotal() {
		return activityTotal;
	}
	public void setActivityTotal(Integer activityTotal) {
		this.activityTotal = activityTotal;
	}
}
