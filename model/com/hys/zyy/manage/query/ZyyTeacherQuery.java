package com.hys.zyy.manage.query;

import java.util.List;

import com.hys.zyy.manage.model.ZyyBaseObject;
import com.hys.zyy.manage.model.ZyyUserExtendVO;


/**
 * 
 * 带教老师查询模型    
 *    
 * <AUTHOR>   
 * @version 1.0    
 * @created 2013-6-4 上午11:00:24
 */
public class ZyyTeacherQuery extends ZyyBaseObject{
	
	private static final long serialVersionUID = -1015684071525509995L;

	/**
	 * 	培训医院
	 */
	private Long hospId;

	/**
	 * 查询基地ID
	 */
	private List<Long> baseIds;
	
	/**
	 * 基地ID
	 */
	private Long baseId;

	/**
	 * 所选年级
	 */
	private List<Long> yearIds;
	
	/**
	 * 查看轮转开始时间  
	 */
	private String startDate;
	
	/**
	 * 查看轮转结束时间
	 */
	private String endDate;
	
	/**
	 * 查询类型 0-非 iframe查询
	 */
	private int type;
	
	/**
	 * 科室ID
	 */
	private Long deptId;
	
	/**
	 * 年度Id
	 */
	private Long yearId;

	/**
	 * 轮转状态    1-正在轮转   2-尚未轮转   3-结束轮转  4-全部
	 */
	private Integer status;
	
	/**
	 * 学员Id
	 */
	private Long userId;
	
	/**
	 * 查询科室ID
	 */
	private List<Long> deptIds;
	
	private String stuName;
	
	private String teacherName;
	
	//专业基地ID
	private Long zyyBaseId;
	
	//证件号码
	private String certificateNo;
	
	//真实姓名
	private String realName;
	
	//人员类型 
	private Integer residencySource;
	private Integer userCategory;
	//登记手册修改，添加字段
	private String sDate;//带教开始时间
	private String eDate;//带教结束时间
	private String teacherIds;//带教老师的id集合
	private String finalAuditingTid;//选中的手册审核者的id,teacher的id
	private String cycleTeacherIds;//zyy_cycle_teacher表的id集合
	private String finalAuditingCid;//zyy_cycle_teacher表中的id,此记录为这个时间段的手册审核者
	
	private String queryTime;//查询轮转月份时间
	private String endQueryTime;
	private Integer queryFlag = 1;//是否查询带教数据 1查询查询带教， 否则不查询
	
	String deptName;
	
	Long ctId;//轮转ID
	
	
	//指定状态  2020-06-05  覃伟刚新题的需求
	private Integer pointStatus;
	
	public Integer getResidencySource() {
		return residencySource;
	}

	public void setResidencySource(Integer residencySource) {
		this.residencySource = residencySource;
	}

	public Integer getUserCategory() {
		return userCategory;
	}

	public void setUserCategory(Integer userCategory) {
		this.userCategory = userCategory;
	}

	public String getRealName() {
		return realName;
	}

	public void setRealName(String realName) {
		this.realName = realName;
	}

	public String getCertificateNo() {
		return certificateNo;
	}

	public void setCertificateNo(String certificateNo) {
		this.certificateNo = certificateNo;
	}

	public Long getZyyBaseId() {
		return zyyBaseId;
	}

	public void setZyyBaseId(Long zyyBaseId) {
		this.zyyBaseId = zyyBaseId;
	}

	public String getFinalAuditingCid() {
		return finalAuditingCid;
	}

	public void setFinalAuditingCid(String finalAuditingCid) {
		this.finalAuditingCid = finalAuditingCid;
	}

	public String getsDate() {
		return sDate;
	}

	public void setsDate(String sDate) {
		this.sDate = sDate;
	}

	public String geteDate() {
		return eDate;
	}

	public void seteDate(String eDate) {
		this.eDate = eDate;
	}

	public String getTeacherIds() {
		return teacherIds;
	}

	public void setTeacherIds(String teacherIds) {
		this.teacherIds = teacherIds;
	}

	public String getFinalAuditingTid() {
		return finalAuditingTid;
	}

	public void setFinalAuditingTid(String finalAuditingTid) {
		this.finalAuditingTid = finalAuditingTid;
	}

	public String getCycleTeacherIds() {
		return cycleTeacherIds;
	}

	public void setCycleTeacherIds(String cycleTeacherIds) {
		this.cycleTeacherIds = cycleTeacherIds;
	}

	public String getStuName() {
		return stuName;
	}

	public void setStuName(String stuName) {
		this.stuName = stuName;
	}

	public String getTeacherName() {
		return teacherName;
	}

	public void setTeacherName(String teacherName) {
		this.teacherName = teacherName;
	}

	public List<Long> getDeptIds() {
		return deptIds;
	}

	public void setDeptIds(List<Long> deptIds) {
		this.deptIds = deptIds;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	private ZyyUserExtendVO zyyUser;

	
	public ZyyUserExtendVO getZyyUser() {
		return zyyUser;
	}

	public void setZyyUser(ZyyUserExtendVO zyyUser) {
		this.zyyUser = zyyUser;
	}

	public Long getYearId() {
		return yearId;
	}

	public void setYearId(Long yearId) {
		this.yearId = yearId;
	}


	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Long getDeptId() {
		return deptId;
	}

	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}

	public int getType() {
		return type;
	}

	public void setType(int type) {
		this.type = type;
	}

	public Long getBaseId() {
		return baseId;
	}

	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}

	public String getStartDate() {
		return startDate;
	}

	public void setStartDate(String startDate) {
		this.startDate = startDate;
	}

	public String getEndDate() {
		return endDate;
	}

	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}

	public Long getHospId() {
		return hospId;
	}

	public void setHospId(Long hospId) {
		this.hospId = hospId;
	}


	
	public List<Long> getBaseIds() {
		return baseIds;
	}

	public void setBaseIds(List<Long> baseIds) {
		this.baseIds = baseIds;
	}
	public List<Long> getYearIds() {
		return yearIds;
	}

	public void setYearIds(List<Long> yearIds) {
		this.yearIds = yearIds;
	}

	public String getDeptName() {
		return deptName;
	}

	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}

	public String getQueryTime() {
		return queryTime;
	}

	public void setQueryTime(String queryTime) {
		this.queryTime = queryTime == null ? null : queryTime.trim();
	}

	public String getEndQueryTime() {
		return endQueryTime;
	}

	public void setEndQueryTime(String endQueryTime) {
		this.endQueryTime = endQueryTime == null ? null : endQueryTime.trim();
	}

	public Integer getQueryFlag() {
		return queryFlag;
	}

	public void setQueryFlag(Integer queryFlag) {
		this.queryFlag = queryFlag;
	}

	public Long getCtId() {
		return ctId;
	}

	public void setCtId(Long ctId) {
		this.ctId = ctId;
	}

	public Integer getPointStatus() {
		return pointStatus;
	}

	public void setPointStatus(Integer pointStatus) {
		this.pointStatus = pointStatus;
	}
}