package com.hys.zyy.manage.query;

import java.util.Date;

import com.hys.zyy.manage.model.ZyyBaseObject;
import com.hys.zyy.manage.util.DateUtil;

public class ZyyTeachingActivityQuery extends ZyyBaseObject {

	private static final long serialVersionUID = 1L;
	/*
	 * 教学活动ID
	 */
	private Long zyyTeachingActivityId;
	/**
	 * 活动类型
	 */
	private Long activityType;
	/**
	 * 活动状态  1正在进行    2已结束     3尚未开始
	 */
	private Integer activityStatus;
	/**
	 * 创建者类型
	 */
	private String createrType;
	/**
	 * 活动名称
	 */
	private String activityName;
	/**
	 * 主讲人
	 */
	private String speaker;
	/**
	 * 学员
	 */
	private String residencyName;
	/**
	 * 开始时间,赋值的同时会对下面的日期类型赋值 注意
	 */
	private String startDateStr;
	private Date startDate;
	/**
	 * 结束时间
	 */
	private String endDateStr;
	private Date endDate;
	/**
	 *
	 */
	private Long orgId;
	/**
	 * 学科的id
	 */
	private Long baseId;
	/**
	 * 科室的id
	 */
	private Long deptId;
	/**
	 * 学员查询时,此处存储学员的Id
	 */
	private Long residencyId;

	/**
	 * 评价状态 1-已评价 2-未评价
	 */
	private Integer evaluateStatus;

	private Long userId;

	private Long evaluateUserId;

	private boolean singleSelect;
	/**
	 * 学员app 根据年度搜索条件
	 */
	private String searchYear;
	
	private Integer attState;

	public boolean isSingleSelect() {
		return singleSelect;
	}
	public void setSingleSelect(boolean singleSelect) {
		this.singleSelect = singleSelect;
	}
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	public Long getEvaluateUserId() {
		return evaluateUserId;
	}
	public void setEvaluateUserId(Long evaluateUserId) {
		this.evaluateUserId = evaluateUserId;
	}
	public Integer getEvaluateStatus() {
		return evaluateStatus;
	}
	public void setEvaluateStatus(Integer evaluateStatus) {
		this.evaluateStatus = evaluateStatus;
	}
	public Date getStartDate() {
		return startDate;
	}
	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}
	public Date getEndDate() {
		return endDate;
	}
	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}
	public Long getResidencyId() {
		return residencyId;
	}
	public void setResidencyId(Long residencyId) {
		this.residencyId = residencyId;
	}
	public Long getOrgId() {
		return orgId;
	}
	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}
	public Long getBaseId() {
		return baseId;
	}
	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}
	public Long getDeptId() {
		return deptId;
	}
	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}
	public Long getActivityType() {
		return activityType;
	}
	public void setActivityType(Long activityType) {
		this.activityType = activityType;
	}
	public Integer getActivityStatus() {
		return activityStatus;
	}
	public void setActivityStatus(Integer activityStatus) {
		this.activityStatus = activityStatus;
	}
	public String getCreaterType() {
		return createrType;
	}
	public void setCreaterType(String createrType) {
		this.createrType = createrType;
	}
	public String getActivityName() {
		return activityName;
	}
	public void setActivityName(String activityName) {
		this.activityName = activityName;
	}
	public String getSpeaker() {
		return speaker;
	}
	public void setSpeaker(String speaker) {
		this.speaker = speaker;
	}
	public String getResidencyName() {
		return residencyName;
	}
	public void setResidencyName(String residencyName) {
		this.residencyName = residencyName;
	}
	public String getStartDateStr() {
		return startDateStr;
	}
	public void setStartDateStr(String startDateStr) {
		Date date = DateUtil.parse(startDateStr, DateUtil.FORMAT_MINUTES);
		this.startDate=date;
		this.startDateStr = startDateStr;
	}
	public String getEndDateStr() {
		return endDateStr;
	}
	public void setEndDateStr(String endDateStr) {
		Date date = DateUtil.parse(endDateStr, DateUtil.FORMAT_MINUTES);
		this.endDate=date;
		this.endDateStr = endDateStr;
	}
	public Long getZyyTeachingActivityId() {
		return zyyTeachingActivityId;
	}
	public void setZyyTeachingActivityId(Long zyyTeachingActivityId) {
		this.zyyTeachingActivityId = zyyTeachingActivityId;
	}

	public String getSearchYear() {
		return searchYear;
	}

	public void setSearchYear(String searchYear) {
		this.searchYear = searchYear;
	}

	public Integer getAttState() {
		return attState;
	}

	public void setAttState(Integer attState) {
		this.attState = attState;
	}

}