package com.hys.zyy.manage.query;

import java.io.Serializable;

import com.hys.zyy.manage.model.ZyyUserExtendVO;

/**
 * 评价查询条件的封装类
 * <AUTHOR>
 * @date 2018-8-29下午1:09:49
 */
public class ZyyUserEvaluateNewQuery implements Serializable {
	private static final long serialVersionUID = 1L;

	// 被评价者 角色类型 1 对评价学员 2 对带教评价 3 对责任导师评价 4 对科室评价 5 对专业基地评价
	private Integer tableType;
	// 评价者类型 角色类型 1带教老师 2 科室 3责任导师 4专业基地 5培训基地 6患者 7护士 8 住院医师
	private Integer evaluateType;
	// 开始时间
	private String startTime;
	// 结束时间
	private String endTime;
	// 姓名
	private String name;
	// 证件号码
	private String certificateNo;

	// 评价者 专业基地id
	private Long baseId;
	// 评价者 科室id
	private Long deptId;
	/*
	 * 被评价者学员ID
	 */
	private Long evaluatedUserId;
	// 被评价者 专业基地id
	private Long evaluatedBaseId;
	// 被评价者 科室id
	private Long evaluatedDeptId;
	// 登录用户
	private ZyyUserExtendVO user;
	// 评价状态 空 全部 1 已评价 2 未评价
	private Integer evaluateStatus;
	
	private String taskStartTime;

	private String taskEndTime;
	
	private Long evaluateId;

	public ZyyUserEvaluateNewQuery() {
		super();
	}

	public ZyyUserEvaluateNewQuery(Long evaluatedUserId) {
		super();
		this.evaluatedUserId = evaluatedUserId;
	}

	public ZyyUserEvaluateNewQuery(Long evaluatedUserId, ZyyUserExtendVO user) {
		super();
		this.evaluatedUserId = evaluatedUserId;
		this.user = user;
	}

	public ZyyUserEvaluateNewQuery(Integer tableType, Long evaluatedUserId, ZyyUserExtendVO user) {
		super();
		this.tableType = tableType;
		this.evaluatedUserId = evaluatedUserId;
		this.user = user;
	}

	public Integer getTableType() {
		return tableType;
	}

	public void setTableType(Integer tableType) {
		this.tableType = tableType;
	}

	public Integer getEvaluateType() {
		return evaluateType;
	}

	public void setEvaluateType(Integer evaluateType) {
		this.evaluateType = evaluateType;
	}

	public String getStartTime() {
		return startTime;
	}

	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}

	public String getEndTime() {
		return endTime;
	}

	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getCertificateNo() {
		return certificateNo;
	}

	public void setCertificateNo(String certificateNo) {
		this.certificateNo = certificateNo;
	}

	public Long getBaseId() {
		return baseId;
	}

	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}

	public Long getDeptId() {
		return deptId;
	}

	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}

	public Long getEvaluatedBaseId() {
		return evaluatedBaseId;
	}

	public void setEvaluatedBaseId(Long evaluatedBaseId) {
		this.evaluatedBaseId = evaluatedBaseId;
	}

	public Long getEvaluatedDeptId() {
		return evaluatedDeptId;
	}

	public void setEvaluatedDeptId(Long evaluatedDeptId) {
		this.evaluatedDeptId = evaluatedDeptId;
	}

	public ZyyUserExtendVO getUser() {
		return user;
	}

	public void setUser(ZyyUserExtendVO user) {
		this.user = user;
	}

	public Integer getEvaluateStatus() {
		return evaluateStatus;
	}

	public void setEvaluateStatus(Integer evaluateStatus) {
		this.evaluateStatus = evaluateStatus;
	}

	public Long getEvaluatedUserId() {
		return evaluatedUserId;
	}

	public void setEvaluatedUserId(Long evaluatedUserId) {
		this.evaluatedUserId = evaluatedUserId;
	}

	public Long getEvaluateId() {
		return evaluateId;
	}

	public void setEvaluateId(Long evaluateId) {
		this.evaluateId = evaluateId;
	}

	public String getTaskStartTime() {
		return taskStartTime;
	}

	public void setTaskStartTime(String taskStartTime) {
		this.taskStartTime = taskStartTime;
	}

	public String getTaskEndTime() {
		return taskEndTime;
	}

	public void setTaskEndTime(String taskEndTime) {
		this.taskEndTime = taskEndTime;
	}
}
