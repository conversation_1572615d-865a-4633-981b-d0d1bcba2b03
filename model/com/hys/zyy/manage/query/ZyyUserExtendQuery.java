package com.hys.zyy.manage.query;

import java.util.List;

import com.hys.zyy.manage.model.ZyyBaseObject;
import com.hys.zyy.manage.model.ZyyUserExtendVO;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：张伟清 2012-04-26
 * 
 * 描述：轮转查询对象
 * 
 * 说明:
 */
public class ZyyUserExtendQuery extends ZyyBaseObject{
	
	private static final long serialVersionUID = 3648094451099134342L;

	private Integer orgTypeFlag;
	
	private List<ZyyUserExtendVO> userExtendList;

	public List<ZyyUserExtendVO> getUserExtendList() {
		return userExtendList;
	}

	public void setUserExtendList(List<ZyyUserExtendVO> userExtendList) {
		this.userExtendList = userExtendList;
	}

	public Integer getOrgTypeFlag() {
		return orgTypeFlag;
	}

	public void setOrgTypeFlag(Integer orgTypeFlag) {
		this.orgTypeFlag = orgTypeFlag;
	}
}