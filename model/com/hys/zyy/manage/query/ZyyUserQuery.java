package com.hys.zyy.manage.query;

import com.hys.zyy.manage.model.ZyyBaseObject;
/**
 * 用户的查询类
 * <AUTHOR>
 * @date 2018-9-4下午4:34:55
 */
public class ZyyUserQuery extends ZyyBaseObject{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	/**
	 * 用户类别  多个用逗号分隔  例如：1，2，3
	 */
	private String zyyUserType;

	/**
	 * 用户组织机构ID
	 */
	private Long zyyUserOrgId;

	public String getZyyUserType() {
		return zyyUserType;
	}

	public void setZyyUserType(String zyyUserType) {
		this.zyyUserType = zyyUserType;
	}

	public Long getZyyUserOrgId() {
		return zyyUserOrgId;
	}

	public void setZyyUserOrgId(Long zyyUserOrgId) {
		this.zyyUserOrgId = zyyUserOrgId;
	}
}
