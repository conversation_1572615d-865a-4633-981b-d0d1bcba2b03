package com.hys.zyy.manage.query;

import com.hys.zyy.manage.model.ZyyBaseObject;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：lky 6.29
 * 
 * 描述：审核用户查询
 * 
 */
public class ZyyVerifyUserQuery extends ZyyBaseObject{
	
	private static final long serialVersionUID = -5190702820057957784L;

	/**
	 * 招录年度ID(学届)
	 */
	private Long recruitYearId ;
	
	/**
	 * 招录年度
	 */
	private String recruitYear ;
	
	/**
	 * 学制 1.1年 2.2年 3.3年
	 */
	private Integer educationSystem ;
	
	/**
	 * 审核状态(轮转状态)	1.未审核 (0.初始化 1.学员提交审核) 2.审核未通过（2.提交后带教老师打回 3.提交后科室主任打回） 4.审核通过
	 */
	private Integer cycleStatus ;
	
	/**
	 * 出科月份
	 */
	private String rotateMonth ;
	
	/**
	 * 医院ID
	 */
	private Long hospId ;
	
	/**
	 * 轮转科室ID
	 */
	private Long deptId ;
	
	/**
	 * 标准科室ID
	 */
	public Long stdDeptId;
	
	/**
	 * 医院ID 
	 */
	private Long hospitalId;
	/**
	 * 基地ID
	 */
	private Long baseId;
	
	private Integer userType;
	
	private Integer selectDeptId;	//用户所选,可能为本科室,也可能为下级科室
	
	private Integer hospType;
	

	public Long getHospitalId() {
		return hospitalId;
	}

	public void setHospitalId(Long hospitalId) {
		this.hospitalId = hospitalId;
	}

	public Long getBaseId() {
		return baseId;
	}

	public void setBaseId(Long baseId) {
		this.baseId = baseId;
	}

	public Long getRecruitYearId() {
		return recruitYearId;
	}

	public void setRecruitYearId(Long recruitYearId) {
		this.recruitYearId = recruitYearId;
	}

	public String getRecruitYear() {
		return recruitYear;
	}

	public void setRecruitYear(String recruitYear) {
		this.recruitYear = recruitYear;
	}

	public Integer getEducationSystem() {
		return educationSystem;
	}

	public void setEducationSystem(Integer educationSystem) {
		this.educationSystem = educationSystem;
	}

	public Integer getCycleStatus() {
		return cycleStatus;
	}

	public void setCycleStatus(Integer cycleStatus) {
		this.cycleStatus = cycleStatus;
	}

	public String getRotateMonth() {
		return rotateMonth;
	}

	public void setRotateMonth(String rotateMonth) {
		this.rotateMonth = rotateMonth;
	}

	public Long getHospId() {
		return hospId;
	}

	public void setHospId(Long hospId) {
		this.hospId = hospId;
	}

	public Long getDeptId() {
		return deptId;
	}

	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}

	public Integer getUserType() {
		return userType;
	}

	public void setUserType(Integer userType) {
		this.userType = userType;
	}

	public Integer getSelectDeptId() {
		return selectDeptId;
	}

	public void setSelectDeptId(Integer selectDeptId) {
		this.selectDeptId = selectDeptId;
	}

	public Integer getHospType() {
		return hospType;
	}

	public void setHospType(Integer hospType) {
		this.hospType = hospType;
	}

	public Long getStdDeptId() {
		return stdDeptId;
	}

	public void setStdDeptId(Long stdDeptId) {
		this.stdDeptId = stdDeptId;
	}

}