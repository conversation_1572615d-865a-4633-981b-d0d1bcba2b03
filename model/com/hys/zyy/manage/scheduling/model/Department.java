package com.hys.zyy.manage.scheduling.model;

import java.util.ArrayList;
import java.util.List;

import com.hys.zyy.manage.model.ZyyBaseObject;

/**
 * 科室类
 * <AUTHOR>
 *
 */
public class Department extends ZyyBaseObject{
	
	private static final long serialVersionUID = -141658485503368114L;

	/**
	 * 科室ID
	 */
	private Long id;
	
	/**
	 * 科室名称
	 */
	private String departmentName;
	
	
	/**
	 * 人员排班次数
	 */
	private int numberOfTime;
	
	/**
	 * 班次周期
	 */
	private List<Period> periods=new ArrayList<Period>();
	
	/**
	 * 最少人数
	 */
	private int minNumber;
	
	/**
	 * 最大人数
	 */
	private int maxNumber;
	
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getDepartmentName() {
		return departmentName;
	}

	public void setDepartmentName(String departmentName) {
		this.departmentName = departmentName;
	}

	public int getNumberOfTime() {
		return numberOfTime;
	}

	public void setNumberOfTime(int numberOfTime) {
		this.numberOfTime = numberOfTime;
	}



	public int getMinNumber() {
		return minNumber;
	}

	public void setMinNumber(int minNumber) {
		this.minNumber = minNumber;
	}

	public int getMaxNumber() {
		return maxNumber;
	}

	public void setMaxNumber(int maxNumber) {
		this.maxNumber = maxNumber;
	}

	public List<Period> getPeriods() {
		return periods;
	}

	public void setPeriods(List<Period> periods) {
		this.periods = periods;
	}
}
