package com.hys.zyy.manage.scheduling.model;

import java.util.ArrayList;
import java.util.List;

import com.hys.zyy.manage.model.ZyyBaseObject;


/**
 * 科室组 
 * <AUTHOR>
 *
 */
public class DepartmentGroup extends ZyyBaseObject{
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 6606723593155645405L;

	//组类型
	private DepartmentGroupType groupType;
	
	//科室集合
	private List<Department> departments=new ArrayList<Department>();
	
	//选修改班次数
	private int electiveNumber;
	
	
	public DepartmentGroup(){
		
	}
	
	public void addDepartment(Department department){
		this.departments.add(department);
		
	}
	
	public DepartmentGroup(DepartmentGroupType groupType){
		this.setGroupType(groupType);
	}

	public DepartmentGroupType getGroupType() {
		return groupType;
	}

	public void setGroupType(DepartmentGroupType groupType) {
		this.groupType = groupType;
	}

	public List<Department> getDepartments() {
		return departments;
	}

	public void setDepartments(List<Department> departments) {
		this.departments = departments;
	}

	public int getElectiveNumber() {
		return electiveNumber;
	}

	public void setElectiveNumber(int electiveNumber) {
		this.electiveNumber = electiveNumber;
	}

	
	
}
