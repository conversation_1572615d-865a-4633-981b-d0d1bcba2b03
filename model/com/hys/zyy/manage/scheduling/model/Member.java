package com.hys.zyy.manage.scheduling.model;

import java.util.ArrayList;
import java.util.List;

import com.hys.zyy.manage.model.ZyyBaseObject;

/**
 * 排班人员
 * <AUTHOR>
 *
 */
public class Member extends ZyyBaseObject{
	
	/**
	 * 
	 */
	private static final long serialVersionUID = -7237141244424752260L;

	/**
	 * 人员ID
	 */
	private Long id;
	
	/**
	 * 年制
	 */
	private int year;
	
	/**
	 * 人员姓名
	 */
	private String name;
	
	/**
	 * 已经排过班次
	 */
	private List<Squad> squads=new ArrayList<Squad>();
	
	/**
	 * 当前所排班次
	 */
	private Squad squad;
	
	/**
	 * 轮转 周期数
	 */
	private int periodNumber;
	
	/**
	 * 当前所排班次所有周期次数
	 */
	private int squadNumberOfTime;
	
	/**
	 * 已经排过班次
	 */
	private List<Squad> scheduledSquads;
	
	/**
	 * 查询是否排过班次
	 * @param squad
	 * @return
	 */
	public boolean isExistSquad(Squad squad){
		boolean b=false;
		for(Squad s:this.squads){
			if(squad.getId().equals(s.getId())){
				b=true;
				break;
			}
		}
		return b;
	}
	public boolean isExistSquad(List<Squad> squads){
		boolean b=false;
		for(Squad squad:squads){
			for(Squad s:this.squads){
				if(squad.getId().equals(s.getId())){
					b=true;
					break;
				}
			}
		}
		return b;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public List<Squad> getSquads() {
		return squads;
	}

	public void setSquads(List<Squad> squads) {
		this.squads = squads;
	}

	public Squad getSquad() {
		return squad;
	}

	public void setSquad(Squad squad) {
		if(squad!=this.squad){
			this.squads.add(squad);
		}
		this.squad = squad;
	}

	public int getSquadNumberOfTime() {
		return squadNumberOfTime;
	}

	public void setSquadNumberOfTime(int squadNumberOfTime) {
		this.squadNumberOfTime = squadNumberOfTime;
	}

	public List<Squad> getScheduledSquads() {
		return scheduledSquads;
	}

	public void setScheduledSquads(List<Squad> scheduledSquads) {
		this.scheduledSquads = scheduledSquads;
	}
	public int getPeriodNumber() {
		return periodNumber;
	}
	public void setPeriodNumber(int periodNumber) {
		this.periodNumber = periodNumber;
	}
	public int getYear() {
		return year;
	}
	public void setYear(int year) {
		this.year = year;
	}
	
	

}
