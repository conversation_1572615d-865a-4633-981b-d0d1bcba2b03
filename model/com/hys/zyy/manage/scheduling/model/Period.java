package com.hys.zyy.manage.scheduling.model;

import java.util.ArrayList;
import java.util.List;

import com.hys.zyy.manage.model.ZyyBaseObject;

/**
 * 时间周期
 * <AUTHOR>
 *
 */
public class Period extends ZyyBaseObject implements java.lang.Cloneable {


	private static final long serialVersionUID = -3524740950766328137L;
	
	/**
	 * 周期ID
	 */
	private Long id;
	/**
	 * 开始时间
	 */
	private String startTime;
	
	/**
	 * 结束时间
	 */
	private String endTime;
	
	/**
	 * 成员
	 */
	private List<Member> members=new ArrayList<Member>();
	
	/**
	 * 添加成员
	 * @param member
	 */
	public void andMember(Member member){
		members.add(member);
	}
	
	/**
	 * 已存在人数
	 */
	private int membersNumber;
	
	
	public Period clone() { 
        try { 
        	Period period= (Period)super.clone(); 
        	period.setMembers(new ArrayList<Member>());
        	return period;
        } catch (CloneNotSupportedException e) { 
            return null; 
        } 
    } 
	
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getStartTime() {
		return startTime;
	}

	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}

	public String getEndTime() {
		return endTime;
	}

	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}

	public List<Member> getMembers() {
		return members;
	}

	public void setMembers(List<Member> members) {
		this.members = members;
	}

	public int getMembersNumber() {
		return membersNumber;
	}

	public void setMembersNumber(int membersNumber) {
		this.membersNumber = membersNumber;
	}

}
