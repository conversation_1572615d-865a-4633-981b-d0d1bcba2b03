package com.hys.zyy.manage.scheduling.model;

import java.util.List;

import com.hys.zyy.manage.model.ZyyBaseObject;

public class Scheduling extends ZyyBaseObject{
	
	private static final long serialVersionUID = -5971900382338800558L;

	//科室集合
	private List<Department> departments;
	
	//周期集合
    private List<Period> periods;
	
	//待排人员
	private List<Member> members;
	
	//联排班组集合
	private List<DepartmentGroup> continuousDepartmentGroups;
	
	//优先班次集合
	private List<Department> priorityDepartments;
	
	//选修班次集合
	private List<DepartmentGroup> electiveDepartmentGroups;

	public List<Department> getDepartments() {
		return departments;
	}

	public void setDepartments(List<Department> departments) {
		this.departments = departments;
	}

	public List<Period> getPeriods() {
		return periods;
	}

	public void setPeriods(List<Period> periods) {
		this.periods = periods;
	}

	public List<Member> getMembers() {
		return members;
	}

	public void setMembers(List<Member> members) {
		this.members = members;
	}

	public List<DepartmentGroup> getContinuousDepartmentGroups() {
		return continuousDepartmentGroups;
	}

	public void setContinuousDepartmentGroups(
			List<DepartmentGroup> continuousDepartmentGroups) {
		this.continuousDepartmentGroups = continuousDepartmentGroups;
	}

	public List<Department> getPriorityDepartments() {
		return priorityDepartments;
	}

	public void setPriorityDepartments(List<Department> priorityDepartments) {
		this.priorityDepartments = priorityDepartments;
	}

	public List<DepartmentGroup> getElectiveDepartmentGroups() {
		return electiveDepartmentGroups;
	}

	public void setElectiveDepartmentGroups(List<DepartmentGroup> electiveDepartmentGroups) {
		this.electiveDepartmentGroups = electiveDepartmentGroups;
	}
	
	
	
	

}
