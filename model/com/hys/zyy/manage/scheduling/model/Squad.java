package com.hys.zyy.manage.scheduling.model;

import java.util.ArrayList;
import java.util.List;

import com.hys.zyy.manage.model.ZyyBaseObject;


/**
 * 班次
 * <AUTHOR>
 *
 */
public class Squad extends ZyyBaseObject{
	
	private static final long serialVersionUID = -3338997029563976501L;

	/**
	 * 班次ID
	 */
	private Long id;
	
	/**
	 * 班次名称
	 */
	private String squadName;
	
	
	/**
	 * 人员排班次数
	 */
	private int numberOfTime;
	
	/**
	 * 班次周期
	 */
	private List<Period> periods=new ArrayList<Period>();
	
	/**
	 * 最少人数
	 */
	private int minNumber;
	
	/**
	 * 最大人数
	 */
	private int maxNumber;
	
	/**
	 * 每个周期平匀人数
	 */
	private float avgNumberOfPeriod;
	
	/**
	 * 平匀周期分组数
	 */
	private float avgPeriodGroup;
	
	
	public Squad(){
		
	}
	
	public Squad(Long id,String squadName,int numberOfTime,int minNumber,int maxNumber){
		this.id=id;
		this.squadName=squadName;
		this.numberOfTime=numberOfTime;
		this.minNumber=minNumber;
		this.maxNumber=maxNumber;
			
	}
	
	public void andPeriod(Period period){
		periods.add(period);
		if(this.numberOfTime>0&&periods!=null&&periods.size()>0){
			this.avgPeriodGroup=((float)periods.size())/numberOfTime;
		}
	}
	
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getSquadName() {
		return squadName;
	}

	public void setSquadName(String squadName) {
		this.squadName = squadName;
	}

	public int getNumberOfTime() {
		return numberOfTime;
	}

	public void setNumberOfTime(int numberOfTime) {
		this.numberOfTime = numberOfTime;
	}

	public List<Period> getPeriods() {
		return periods;
	}

	public void setPeriods(List<Period> periods) {
		this.periods = periods;
		if(this.numberOfTime>0&&periods!=null&&periods.size()>0){
			//this.avgPeriodGroup=periods.size()%numberOfTime==0?periods.size()/numberOfTime:(periods.size()/numberOfTime+1);
			this.avgPeriodGroup=periods.size()/numberOfTime;
		}
	}

	public float getAvgPeriodGroup() {
		return avgPeriodGroup;
	}

	public float getAvgNumberOfPeriod() {
		return avgNumberOfPeriod;
	}

	public void setAvgNumberOfPeriod(float avgNumberOfPeriod) {
		this.avgNumberOfPeriod = avgNumberOfPeriod;
	}

	public int getMinNumber() {
		return minNumber;
	}

	public void setMinNumber(int minNumber) {
		this.minNumber = minNumber;
	}

	public int getMaxNumber() {
		return maxNumber;
	}

	public void setMaxNumber(int maxNumber) {
		this.maxNumber = maxNumber;
	}

	
	
}
