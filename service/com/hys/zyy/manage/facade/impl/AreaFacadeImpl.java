package com.hys.zyy.manage.facade.impl;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.hys.zyy.manage.exception.ErrorCode;
import com.hys.zyy.manage.exception.FrameworkRuntimeException;
import com.hys.zyy.manage.facade.AreaFacade;
import com.hys.zyy.manage.model.Area;
import com.hys.zyy.manage.model.AreaVO;
import com.hys.zyy.manage.model.ZyyUserExtendVO;
import com.hys.zyy.manage.service.AreaManage;

@Service("areaFacade")
public class AreaFacadeImpl implements AreaFacade {
	@Autowired
	private AreaManage areaManage ;

	@Override
	public Map<String, Long> findAllYunnanProvinceMap() {
		return areaManage.findAllYunnanProvinceMap();
	}

	@Override
	public Map<String, Long> findAllYunnanCityMap() {
		return areaManage.findAllYunnanCityMap();
	}

	@Override
	public List<Area> findAllYunnanProvince() {
		return areaManage.findAllYunnanProvince();
	}

	@Override
	public List<Area> findAllCityByParentId(Long parentId) {
		return areaManage.findAllCityByParentId(parentId);
	}

	@Override
	public AreaVO findAllByIds(ZyyUserExtendVO extend) {
		return areaManage.findAllByIds(extend);
	}

	@Override
	public List<Area> find(Area query) {
		try {
			return areaManage.find(query);
		} catch (Exception ex) {
			throw new FrameworkRuntimeException(ErrorCode.E0001, ex);
		}
	}
}
