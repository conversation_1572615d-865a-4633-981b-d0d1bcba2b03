package com.hys.zyy.manage.facade.impl;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.hys.zyy.manage.exception.ErrorCode;
import com.hys.zyy.manage.exception.FrameworkRuntimeException;
import com.hys.zyy.manage.facade.YktStatFacade;
import com.hys.zyy.manage.model.YktDeptAttendStat;
import com.hys.zyy.manage.model.YktStatQuery;
import com.hys.zyy.manage.model.YktStatType;
import com.hys.zyy.manage.service.YktStatManage;

@Service("yktStatFacade")
public class YktStatFacadeImpl implements YktStatFacade {

	@Autowired
	private YktStatManage yktStatManage;

	@Override
	public List<YktStatType> queryStatTypes(YktStatQuery query) {
		try {
			return yktStatManage.queryStatTypes(query);
		} catch (Exception ex) {
			throw new FrameworkRuntimeException(ErrorCode.E0000, ex);
		}
	}

	@Override
	public Map<String, Object> stat(YktStatQuery query) {
		try {
			return yktStatManage.stat(query);
		} catch (Exception ex) {
			throw new FrameworkRuntimeException(ErrorCode.E0000, ex);
		}
	}

	@Override
	public List<YktDeptAttendStat> deptAttendStat(YktStatQuery query) {
		try {
			return yktStatManage.deptAttendStat(query);
		} catch (Exception ex) {
			throw new FrameworkRuntimeException(ErrorCode.E0000, ex);
		}
	}

}
