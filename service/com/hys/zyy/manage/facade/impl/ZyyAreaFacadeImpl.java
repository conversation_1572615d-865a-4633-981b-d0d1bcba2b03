package com.hys.zyy.manage.facade.impl;

import java.util.List;

import org.springframework.stereotype.Service;

import com.hys.zyy.manage.exception.ErrorCode;
import com.hys.zyy.manage.exception.FrameworkRuntimeException;
import com.hys.zyy.manage.facade.ZyyAreaFacade;
import com.hys.zyy.manage.model.ZyyAreaVO;
import com.hys.zyy.manage.service.ZyyAreaManage;

@Service("zyyAreaFacade")
public class ZyyAreaFacadeImpl implements ZyyAreaFacade {

	private ZyyAreaManage zyyAreaManage;

	public ZyyAreaManage getZyyAreaManage() {
		return zyyAreaManage;
	}

	public void setZyyAreaManage(ZyyAreaManage zyyAreaManage) {
		this.zyyAreaManage = zyyAreaManage;
	}

	@Override
	public List<ZyyAreaVO> find(ZyyAreaVO query) {
		try {
			return zyyAreaManage.find(query);
		} catch (Exception ex) {
			throw new FrameworkRuntimeException(ErrorCode.E0000, ex);
		}
	}

}
