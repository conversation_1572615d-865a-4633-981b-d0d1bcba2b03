package com.hys.zyy.manage.facade.impl;

import java.util.List;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.springframework.stereotype.Service;

import com.hys.zyy.manage.exception.ErrorCode;
import com.hys.zyy.manage.exception.FrameworkRuntimeException;
import com.hys.zyy.manage.facade.ZyyBaseFacade;
import com.hys.zyy.manage.model.ZyyBase;
import com.hys.zyy.manage.model.ZyyBaseStd;
import com.hys.zyy.manage.model.ZyyBaseVO;
import com.hys.zyy.manage.model.ZyyOrg;
import com.hys.zyy.manage.model.ZyyOrgVO;
import com.hys.zyy.manage.model.ZyyRecruitYear;
import com.hys.zyy.manage.model.ZyyUser;
import com.hys.zyy.manage.model.ZyyUserExtendVO;
import com.hys.zyy.manage.service.ZyyBaseManage;

/**
 * 
 * 标题：住院医师
 * 
 * 作者：李海龙 Mar 22, 2012
 * 
 * 描述：
 * 
 * 说明:
 */
@Service("zyyBaseFacade")
public class ZyyBaseFacadeImpl implements ZyyBaseFacade {

	private ZyyBaseManage zyyBaseManage;
	
	public ZyyBaseManage getZyyBaseManage() {
		return zyyBaseManage;
	}

	public void setZyyBaseManage(ZyyBaseManage zyyBaseManage) {
		this.zyyBaseManage = zyyBaseManage;
	}

	@Override
	public int addZyyBase(ZyyBase zyyBase) {
		try {
			return zyyBaseManage.addZyyBase(zyyBase);
		} catch (Exception e) {
			throw new FrameworkRuntimeException(ErrorCode.E0001, e);
		}
	}

	@Override
	public List<ZyyBase> findBaseStdIdToZyyBase(Long baseStdId) {
		try {
			return zyyBaseManage.findBaseStdIdToZyyBase(baseStdId);
		} catch (Exception e) {
			throw new FrameworkRuntimeException(ErrorCode.E0001, e);
		}
	}

	@Override
	public ZyyBaseVO findZyyBase(Long id) {
		try {
			return zyyBaseManage.findZyyBase(id);
		} catch (Exception e) {
			throw new FrameworkRuntimeException(ErrorCode.E0001, e);
		}
	}

	@Override
	public void updateZyyBase(ZyyBase zyyBase) {
		try {
			zyyBaseManage.updateZyyBase(zyyBase);
		} catch (Exception e) {
			throw new FrameworkRuntimeException(ErrorCode.E0001, e);
		}
	}
	
	@Override //根据医院ID 查询基地信息
	public List<ZyyBaseVO> getZyyBaseListByHospital(Long hospId, int status) {
		try {
			return zyyBaseManage.getZyyBaseListByHospital(hospId, status);
		} catch (Exception e) {
			throw new FrameworkRuntimeException(ErrorCode.E0026, e);
		}
	}
	
	/**
	 * 根据医院ID,课程Id, 查询基地信息
	 * 
	 * @param hospId
	 * @param courseId
	 * @param status
	 * @return
	 */
	@Override
	public List<ZyyBaseVO> getZyyBaseListByHospitalAndCourseId(Long hospId, Long courseId){
		try{
			return zyyBaseManage.getZyyBaseListByHospitalAndCourseId(hospId, courseId);
		}catch (Exception e){
			throw new FrameworkRuntimeException(ErrorCode.E0026,e);
		}
	}
	
	/**
	 * 获取医院所有培训专科  
	 * @param hospId 医院Id
	 * @param status
	 * @param hospType 类别,中医或西医
	 * @return
	 * <AUTHOR>
	 */
	public String getBasesJSON(Long hospId, int status, int hospType, ZyyUserExtendVO zyyUser){
		
		List<ZyyBaseVO> bases = null; 
		
		if(hospId != null && hospId != 0l){
			bases = zyyBaseManage.getZyyBaseListByHospital(hospId, status, hospType);
		} else {
			bases = zyyBaseManage.findAllZyyBaseStd(zyyUser.getZyyUserProvinceId(), hospType);
		}
		
		JSONArray array = null;
		String str = "";
		if(bases != null && bases.size() > 0){
			array = new JSONArray();
			for(ZyyBaseVO base : bases){
				JSONObject obj = new JSONObject();
				obj.put("id", base.getBaseStdId());
				obj.put("name", base.getAliasName());
				//追加一个base_id lzq 2018-7-4
				obj.put("baseId", base.getId());
				array.add(obj);
			}
			str = array.toString();
		}
		return str;
	}
	
	@Override //查询本年度已经开启的基地信息
	public List<ZyyBaseVO> getZyyBaseListByYear(Long hospId, Long yearId) {
		try {
			return zyyBaseManage.getZyyBaseListByYear(hospId, yearId);
		} catch (Exception e) {
			throw new FrameworkRuntimeException(ErrorCode.E0026, e);
		}
	}
	
	@Override //查询指定基地信息列表
	public List<ZyyBaseVO> getZyyBaseListByBaseIds(List<Long> list, Long hospId) {
		try {
			return zyyBaseManage.getZyyBaseListByBaseIds(list, hospId);
		} catch (Exception e) {
			throw new FrameworkRuntimeException(ErrorCode.E0026, e);
		}
	}
	
	@Override //根据医院ID 查询基地轮转科室信息
	public List<ZyyBaseVO> getZyyBaseCycleDeptByHospital(Long hospId) {
		try {
			return zyyBaseManage.getZyyBaseCycleDeptByHospital(hospId);
		} catch (Exception e) {
			throw new FrameworkRuntimeException(ErrorCode.E0026, e);
		}
	}

	@Override
	public void updateBaseList(Long[] ids, List<ZyyOrgVO> hVoList) {
		try {
			zyyBaseManage.updateBaseList(ids, hVoList);
		} catch (Exception e) {
			throw new FrameworkRuntimeException(ErrorCode.E0001, e);
		}
		
	}

	@Override
	public List<ZyyBaseVO> getZyyBaseRoleListByHospital(Long hospId, int status) {
		try {
			return zyyBaseManage.getZyyBaseRoleListByHospital(hospId, status);
		} catch (Exception e) {
			throw new FrameworkRuntimeException(ErrorCode.E0000, e);
		}
	}

	/**
	 * 批量新增基地用户信息
	 * 
	 * @param baseIdList
	 * @return
	 */
	@Override
	public void addZyyBaseUserList(List<ZyyBaseVO> baseIdList,
			ZyyUser zyyLoginUser) {
		try {
			zyyBaseManage.addZyyBaseUserList(baseIdList, zyyLoginUser);
		} catch (Exception e) {
			throw new FrameworkRuntimeException(ErrorCode.E0000, e);
		}
	}
	
	@Override //根据用户ID 查询基地信息
	public ZyyBase getZyyBaseByUserId(Long userId) {
		try {
			return zyyBaseManage.getZyyBaseByUserId(userId);
		} catch (Exception e) {
			throw new FrameworkRuntimeException(ErrorCode.E0001, e);
		}
	}

	@Override
	public List<ZyyBase> findZyyBase(Long[] baseIds) {
		return zyyBaseManage.findZyyBase(baseIds);
	}

	@Override
	public List<ZyyBaseVO> getZyyBaseListByBaseId(Long hospId, Long[] baseIds) {
		return zyyBaseManage.getZyyBaseListByBaseId(hospId, baseIds);
	}

	@Override
	public List<ZyyBaseVO> getZyyBaseListByDeptId(Long hospId, Long[] deptIds) {
		return zyyBaseManage.getZyyBaseListByDeptId(hospId, deptIds);
	}

	@Override
	public List<ZyyBaseVO> getZyyBaseListByUserNameOrIdCard(Long hospId,
			String userName, String identityCard) {
		return zyyBaseManage.getZyyBaseListByUserNameOrIdCard(hospId, userName, identityCard);
	}
	
	@Override
	public List<ZyyBaseVO> getAllZyyBaseList(Long selectOrgId,Long userOrgId,Long userProvinceId,Integer userType,Integer status){
		return zyyBaseManage.getAllZyyBaseList(selectOrgId,userOrgId, userProvinceId, userType, status);
	}
	
	@Override
	public List<ZyyOrg> getZyyOrg(Long orgId,Integer hospType){
		return zyyBaseManage.getZyyOrg(orgId,hospType);
	}
	
	@Override
	public List<ZyyOrg> getZyyOrg2(Long orgId){
		return zyyBaseManage.getZyyOrg2(orgId);
	}
	
	@Override
	public List<ZyyBaseStd> getZyyBaseStd(Long hospitalId,Long hospType,Long orgId){
		return zyyBaseManage.getZyyBaseStd(hospitalId,hospType,orgId);
	}
	
	@Override
	public List<ZyyBaseStd> getZyyBaseStdNew(Long hospitalId, Long hospType, Long orgId) {
		return zyyBaseManage.getZyyBaseStdNew(hospitalId, hospType, orgId);
	}
	
	@Override
	public List<ZyyRecruitYear> getZyyRecruitYears(Long orgId){
		return zyyBaseManage.getZyyRecruitYears(orgId);
	}
	
	@Override
	public List<ZyyOrg> getUniversity(Long hospitalId){
		return zyyBaseManage.getUniversity(hospitalId);
	}
	
	@Override
	public List<String> getEntrustType(Long orgId){
		return zyyBaseManage.getEntrustType(orgId);
	}
	
	@Override
	public List<String> getEntrustUnit(String entrustType,Long orgId){
		return zyyBaseManage.getEntrustUnit(entrustType,orgId);
	}

	@Override
	public List<ZyyBaseVO> getAllZyyBaseListByHosptialId(Long hospitalId) {
		return zyyBaseManage.getAllZyyBaseListByHosptialId(hospitalId);
	}

	@Override
	public List<ZyyRecruitYear> getZyyRecruitYearsDesc(Long orgId) {
		return zyyBaseManage.getZyyRecruitYearsDesc(orgId);
	}

}
