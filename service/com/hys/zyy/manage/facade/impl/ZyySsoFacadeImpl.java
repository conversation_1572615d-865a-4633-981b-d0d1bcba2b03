package com.hys.zyy.manage.facade.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.hys.zyy.manage.constants.Constants;
import com.hys.zyy.manage.facade.ZyySsoFacade;
import com.hys.zyy.manage.json.BdpUserInfoResult;
import com.hys.zyy.manage.model.BdpUserInfo;
import com.hys.zyy.manage.model.ZyyRegion;
import com.hys.zyy.manage.model.ZyyUserExtend;
import com.hys.zyy.manage.model.ZyyUserExtendEnlarge;
import com.hys.zyy.manage.service.ZyyNewUserManage;
import com.hys.zyy.manage.util.AESUtil256;
import com.hys.zyy.manage.util.BDPUtil;
import com.hys.zyy.manage.util.SSOUtil;
import com.hys.zyy.manage.util.StringUtils;

@Service("zyySsoFacade")
public class ZyySsoFacadeImpl implements ZyySsoFacade {

	@Autowired
	private BDPUtil bdpUtil;
	@Autowired
	private ZyyNewUserManage zyyNewUserManage;

	@Override
	public void autoRegister(ZyyUserExtend userExtend, ZyyRegion region, String accountPassword, ZyyUserExtendEnlarge userExtendEnlarge) {
	 	zyyNewUserManage.saveNewUser(userExtend, region, accountPassword, userExtendEnlarge);
	}

	@Override
	public void autoRegister(ZyyRegion region, ZyyUserExtend userExtend) {
		BdpUserInfoResult br = SSOUtil.getUserInfoByUserId(userExtend.getBdpUserId());
		if (br.isSuccess()) {
			BdpUserInfo userInfo = br.getData();
			// BDP用户ID
			userExtend.setBdpUserId(userInfo.getUserId());
			// 用户账户
			userExtend.setAccountName(userInfo.getUserAccount());
			// 账户备注
			userExtend.setAccountRemark(Constants.AUTO_REGISTER_USER_ACCOUNT_REMARK);
			// 真实姓名
			userExtend.setRealName(userInfo.getRealName());
			// 用户来源为自动注册
			userExtend.setZyyUserSource(3);
			// 手机号码
			String mobileNumber = userInfo.getMobileNumber();
			if (StringUtils.isNotBlank(mobileNumber))
				userExtend.setMobilNumber(AESUtil256.decrypt(mobileNumber, Constants.AES256_KEY));
			// 证件号码
			String certificateNo = userInfo.getCertificateNo();
			if (StringUtils.isNotBlank(certificateNo))
				userExtend.setCertificateNo(AESUtil256.decrypt(certificateNo, Constants.AES256_KEY));
			this.autoRegister(userExtend, region, Constants.DEFAULT_ACCOUNT_PASSWORD, new ZyyUserExtendEnlarge());
			bdpUtil.regAuth(userInfo.getUserId());
		}
	}
	
}
