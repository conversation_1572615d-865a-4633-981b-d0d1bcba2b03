package com.hys.zyy.manage.facade.impl;

import java.util.List;

import org.springframework.stereotype.Service;

import com.hys.zyy.manage.exception.ErrorCode;
import com.hys.zyy.manage.exception.FrameworkRuntimeException;
import com.hys.zyy.manage.facade.ZyyStageFacade;
import com.hys.zyy.manage.model.ZyyOrgStage;
import com.hys.zyy.manage.model.ZyyStage;
import com.hys.zyy.manage.service.ZyyStageManage;

/**
 * 
 * 标题：zyy
 * 
 * 作者：Tony Mar 19, 2012
 * 
 * 描述：
 * 
 * 说明:
 */
@Service("zyyStageFacade")
public class ZyyStageFacadeImpl implements ZyyStageFacade {
	
	private ZyyStageManage zyyStageManage;

	public ZyyStageManage getZyyStageManage() {
		return zyyStageManage;
	}

	public void setZyyStageManage(ZyyStageManage zyyStageManage) {
		this.zyyStageManage = zyyStageManage;
	}

	public List<ZyyStage> getZyyStageListByOrgId(Long orgId,Integer stageType) {
		try {
			return zyyStageManage.getZyyStageListByOrgId(orgId,stageType);
		} catch (Exception e) {
			throw new FrameworkRuntimeException(ErrorCode.E0151, e);
		}
	}

	@Override
	public List<ZyyStage> getZyyStageListByYearId(Long yearId, Integer stageType) {
		try {
			return zyyStageManage.getZyyStageListByOrgId(yearId,stageType);
		} catch (Exception e) {
			throw new FrameworkRuntimeException(ErrorCode.E0151, e);
		}
	}


	@Override
	public void deleteZyyStage(ZyyOrgStage zyyOrgStage) {
		try {
			zyyStageManage.deleteZyyStage(zyyOrgStage);
		} catch (Exception e) {
			throw new FrameworkRuntimeException(ErrorCode.E0001, e);
		}
	}

	@Override
	public List<ZyyStage> getZyyStageList() {
		try {
			return zyyStageManage.getZyyStageList();
		} catch (Exception e) {
			throw new FrameworkRuntimeException(ErrorCode.E0001, e);
		}
	}

	@Override
	public List<ZyyOrgStage> getZyyStageListByFind(ZyyOrgStage zyyOrgStage) {
		try {
			return zyyStageManage.getZyyStageListByFind(zyyOrgStage);
		} catch (Exception e) {
			throw new FrameworkRuntimeException(ErrorCode.E0001, e);
		}
	}

	@Override
	public void saveZyyStage(List<ZyyOrgStage> list, ZyyOrgStage zyyOrgStage) {
		try {
			zyyStageManage.saveZyyStage(list, zyyOrgStage) ;
		} catch (Exception e) {
			throw new FrameworkRuntimeException(ErrorCode.E0001, e);
		}
	}

}
