package com.hys.zyy.manage.action.app;

import com.hys.security.util.SecurityUtils;
import com.hys.zyy.manage.action.AbstractBaseController;
import com.hys.zyy.manage.model.ZyyUserExtendVO;
import com.hys.zyy.manage.model.vo.ZyyDepartExamVO;
import com.hys.zyy.manage.query.Pager;
import com.hys.zyy.manage.service.ZyyDepartExamManage;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * App科室考试相关
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/appDeptExam")
public class AppDeptExamController extends AbstractBaseController {

    private static Logger logger = Logger.getLogger(AppDeptExamController.class);

    @Autowired
    private ZyyDepartExamManage zyyDepartExamManage;

    @RequestMapping(value = "/leaveDeptExamList")
    public String leaveDeptExamList(Model model, Pager<ZyyDepartExamVO> pager, ZyyDepartExamVO query) {
        ZyyUserExtendVO zyyUser = SecurityUtils.getZyyUser();
        query.setSortFlag(1);
        query.setCheckStatus(1);
        pager.setPageSize(Integer.MAX_VALUE);
        query.setCreateUserId(zyyUser.getId());
        query.setZyyUserOrgId(zyyUser.getZyyUserOrgId());
        query.setDeptId(zyyUser.getDeptId());
        zyyDepartExamManage.findZyyDepartExamVOPage(pager, query);
        model.addAttribute("records", pager.getList());
        return "mobile_new/deptExam/leaveDeptExamList";
    }

}


















